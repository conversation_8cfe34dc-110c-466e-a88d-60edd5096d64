{"mW": 1200, "mH": 720, "tW": 24, "tH": 24, "tiles": [["1317", 0, 3, 2], ["1317", 2, 3, 2], ["1317", 1, 3, 2], ["1317", 3, 3, 2], ["3861", 0, 1, 1], ["3862", 0, 1, 1], ["3863", 0, 1, 1], ["3864", 0, 1, 1], ["3865", 0, 1, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["3877", 0, 3, 3], ["3298", 0, 3, 3], ["3874", 0, 3, 2], ["3874", 2, 3, 2], ["3874", 1, 3, 2], ["3874", 3, 3, 2], ["3875", 0, 1, 1], ["1406", 0, 3, 2], ["1406", 2, 3, 2], ["1406", 1, 3, 2], ["1406", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "3103", 104, -87, 110, 152, 0], [2, "1125", 185, 51, 62, 46, 0], [2, "456", 214, 43, 40, 32, 0], [2, "458", 170, 52, 54, 55, 0], [2, "1360", 93, 46, 48, 52, 0], [2, "1360", 117, 55, 48, 52, 0], [2, "1360", 136, 65, 48, 52, 0], [2, "1360", 108, 21, 48, 52, 0], [2, "1360", 108, 21, 48, 52, 0], [2, "1360", 134, 24, 48, 52, 0], [2, "1360", 156, 35, 48, 52, 0], [2, "736", 177, 25, 56, 42, 0], [2, "739", 118, -7, 40, 39, 0], [2, "1126", 149, 8, 44, 41, 2], [2, "455", 151, 46, 50, 74, 0], [2, "455", 86, 14, 50, 74, 0], [2, "1160", -34, 619, 88, 75, 0], [2, "1160", -17, 661, 88, 75, 0], [2, "3196", 385, 127, 76, 55, 0], [2, "3860", 950, 145, 42, 44, 0], [2, "3860", 1059, 21, 42, 44, 0], [2, "3860", 902, 5, 42, 44, 0], [2, "3860", 960, 378, 42, 44, 0], [2, "3860", 818, 485, 42, 44, 0], [2, "3860", 983, 614, 42, 44, 0], [2, "3860", 1116, 506, 42, 44, 0], [2, "3860", 1134, 312, 42, 44, 0], [2, "3859", 1138, 11, 42, 44, 0], [2, "3859", 975, 354, 42, 44, 0], [2, "3859", 740, 644, 42, 44, 0], [2, "3857", 946, 548, 72, 99, 0], [2, "3857", 936, 576, 72, 99, 0], [2, "3857", 1102, 249, 72, 99, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 118, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 115, 119, 118, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 115, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 108, 119, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 97, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 97, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 107, 106, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 107, 106, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100]}, {"type": 4, "obj": [[2, "1159", 378, 11, 36, 37, 0], [2, "1159", 271, 25, 36, 37, 0], [2, "1159", 402, 32, 36, 37, 0], [2, "3087", 280, 40, 52, 42, 0], [2, "1159", 473, 54, 36, 37, 0], [4, 13, 335, 99, 0, 4005], [2, "1161", 499, 48, 54, 52, 0], [2, "1157", 392, 61, 42, 41, 0], [2, "1157", 392, 61, 42, 41, 0], [2, "3099", 349, 61, 58, 42, 0], [4, 5, 915, 106, 0, 4044], [2, "3103", 342, -39, 110, 152, 0], [2, "3087", 416, 83, 52, 42, 0], [2, "1159", 402, 92, 36, 37, 0], [2, "3840", 912, 104, 28, 38, 0], [2, "3855", 998, 120, 38, 56, 0], [4, 4, 1070, 186, 0, 4045], [2, "3837", 945, 147, 76, 42, 2], [2, "3844", 887, 129, 78, 71, 2], [2, "3829", 900, 175, 18, 30, 0], [2, "3845", 922, 155, 58, 62, 0], [2, "679", 395, 188, 36, 32, 0], [2, "125", 416, 154, 18, 70, 0], [2, "3836", 976, 158, 92, 66, 2], [2, "3828", 962, 190, 22, 38, 0], [2, "3833", 1003, 202, 36, 26, 0], [2, "14", 442, 200, 32, 30, 0], [2, "3802", 917, 201, 34, 32, 0], [2, "14", 421, 212, 32, 30, 0], [2, "3087", 88, 211, 52, 42, 0], [2, "1159", 113, 222, 36, 37, 0], [2, "1159", 341, 223, 36, 37, 0], [2, "3832", 1025, 266, 30, 20, 0], [2, "3835", 1114, 229, 32, 77, 0], [2, "3834", 1090, 284, 36, 26, 0], [2, "3838", 1104, 291, 38, 31, 0], [2, "3849", 1124, 311, 34, 25, 0], [2, "3849", 1152, 313, 34, 25, 0], [4, 14, 268, 343, 0, 4024], [2, "3849", 1122, 323, 34, 25, 0], [4, 3, 1018, 349, 0, 4044], [2, "3849", 1162, 324, 34, 25, 0], [2, "3846", 1139, 319, 60, 31, 0], [2, "3849", 1131, 337, 34, 25, 0], [2, "3849", 1100, 338, 34, 25, 0], [2, "1159", 66, 327, 36, 37, 0], [2, "3849", 1166, 344, 34, 25, 0], [2, "3849", 1150, 346, 34, 25, 0], [2, "3849", 1131, 358, 34, 25, 0], [2, "3849", 1131, 358, 34, 25, 0], [2, "3849", 1157, 364, 34, 25, 0], [2, "3849", 1157, 364, 34, 25, 0], [2, "3870", 347, 349, 32, 44, 0], [2, "125", 373, 326, 18, 70, 0], [2, "3854", 1112, 356, 32, 47, 0], [2, "3833", 1007, 380, 36, 26, 0], [2, "3870", 318, 367, 32, 44, 0], [2, "3851", 1073, 385, 44, 32, 0], [2, "52", 192, 407, 46, 22, 0], [2, "125", 306, 370, 18, 70, 0], [2, "3872", 216, 420, 10, 38, 0], [2, "125", 231, 388, 18, 70, 0], [2, "3871", 201, 425, 14, 49, 0], [2, "3103", 110, 335, 110, 152, 0], [2, "125", 184, 417, 18, 70, 0], [2, "3814", 1082, 454, 50, 34, 0], [2, "3099", 112, 460, 58, 42, 0], [4, 2, 1147, 504, 0, 4045], [2, "1161", 146, 464, 54, 52, 0], [2, "3853", 1112, 501, 38, 39, 0], [2, "3781", 1155, 490, 32, 62, 0], [2, "3356", 328, 504, 44, 49, 0], [2, "3359", 370, 513, 32, 43, 0], [4, 1, 865, 561, 0, 4044], [2, "1160", 126, 490, 88, 75, 0], [2, "1159", 30, 540, 36, 37, 0], [2, "3807", 785, 543, 42, 40, 0], [2, "3873", 494, 556, 28, 33, 0], [2, "1162", 148, 546, 36, 46, 0], [2, "1159", 24, 560, 36, 37, 0], [4, 6, 993, 610, 0, 4045], [2, "1161", 175, 568, 54, 52, 0], [2, "3781", 1022, 562, 32, 62, 0], [2, "3103", 165, 486, 110, 152, 0], [2, "1159", 78, 602, 36, 37, 0], [2, "3073", 385, 601, 46, 39, 0], [2, "1159", 103, 604, 36, 37, 0], [2, "3869", 228, 626, 30, 20, 0], [2, "3852", 943, 616, 42, 48, 0], [2, "3868", 257, 670, 68, 35, 0], [2, "3869", 288, 702, 30, 20, 0]]}, {"type": 3, "obj": [[2, "3849", 1129, 544, 34, 25, 2], [2, "3835", 1173, 123, 32, 77, 0], [2, "3779", 1052, 537, 102, 80, 2], [2, "3779", 991, 574, 102, 80, 2], [2, "3099", 1, 5, 58, 42, 0], [2, "3194", 475, 557, 92, 47, 0], [2, "28", 416, 544, 12, 27, 0], [2, "28", 376, 525, 12, 27, 0], [2, "28", 339, 502, 12, 27, 0], [2, "28", 455, 573, 12, 27, 0], [2, "28", 495, 599, 12, 27, 0], [2, "1352", 258, 517, 92, 52, 2], [2, "1352", 273, 525, 92, 52, 2], [2, "1352", 285, 534, 92, 52, 2], [2, "1352", 305, 548, 92, 52, 2], [2, "1352", 326, 561, 92, 52, 2], [2, "1352", 344, 573, 92, 52, 2], [2, "1352", 363, 584, 92, 52, 2], [2, "1352", 377, 593, 92, 52, 2], [2, "1352", 393, 600, 92, 52, 2], [2, "1352", 410, 610, 92, 52, 2], [2, "1352", 277, 565, 92, 52, 0], [2, "1352", 364, 538, 92, 52, 0], [2, "1352", 355, 608, 92, 52, 0], [2, "1352", 329, 551, 92, 52, 0], [2, "28", 253, 553, 12, 27, 0], [2, "28", 287, 577, 12, 27, 0], [2, "28", 327, 599, 12, 27, 0], [2, "28", 373, 627, 12, 27, 0], [2, "28", 413, 648, 12, 27, 0], [2, "3179", 52, 133, 86, 69, 0], [2, "3873", 134, 182, 28, 33, 0], [2, "3194", 460, 618, 92, 47, 0], [2, "3868", 242, 644, 68, 35, 0], [2, "3869", 312, 676, 30, 20, 0], [2, "3868", 540, 476, 68, 35, 0], [2, "3869", 610, 508, 30, 20, 0], [2, "3869", 559, 514, 30, 20, 0], [2, "3869", 653, 335, 30, 20, 0], [2, "1160", -27, 274, 88, 75, 0], [2, "1160", -25, 325, 88, 75, 2], [2, "1156", 2, 346, 58, 85, 0], [2, "1158", -6, 418, 34, 37, 0], [2, "3868", 591, 197, 68, 35, 0], [2, "3868", 582, 309, 68, 35, 2], [2, "3869", 649, 178, 30, 20, 0], [2, "3869", 674, 132, 30, 20, 0], [2, "3869", 659, 144, 30, 20, 2], [2, "3869", 745, 425, 30, 20, 2], [2, "3869", 572, 25, 30, 20, 2], [2, "3869", 572, 60, 30, 20, 2], [2, "3868", 479, 14, 68, 35, 0], [2, "3103", 3, -117, 110, 152, 0], [2, "3103", -26, -99, 110, 152, 0], [2, "3103", -49, -56, 110, 152, 0], [2, "1157", 32, 6, 42, 41, 0], [2, "3099", 3, 42, 58, 42, 0], [2, "3099", -27, 71, 58, 42, 0], [2, "3099", 70, -12, 58, 42, 0], [2, "3099", 111, 0, 58, 42, 0], [2, "3099", 291, -11, 58, 42, 0], [2, "3099", 327, 4, 58, 42, 0], [2, "3099", 203, -31, 58, 42, 0], [2, "1458", 690, 59, 34, 36, 0], [2, "1458", 313, 633, 34, 36, 0], [2, "3185", 256, 538, 66, 36, 0], [2, "3187", 286, 557, 62, 36, 0], [2, "679", 88, 149, 36, 32, 0], [2, "1108", 63, 133, 26, 31, 0], [2, "3375", 169, 143, 24, 32, 0], [2, "11", 48, 165, 32, 29, 0], [2, "3813", 591, 132, 48, 36, 0], [2, "3812", 575, 172, 24, 19, 0], [2, "3811", 489, 438, 36, 26, 0], [2, "3811", 611, 67, 36, 26, 0], [2, "3815", 577, 370, 20, 17, 0], [2, "3816", 310, 458, 22, 17, 0], [2, "3816", 538, 268, 22, 17, 0], [2, "3816", 445, 54, 22, 17, 0], [2, "3810", 567, 286, 20, 10, 0], [2, "3810", 550, 297, 20, 10, 0], [2, "3810", 611, 176, 20, 10, 0], [2, "3810", 555, 99, 20, 10, 0], [2, "3810", 397, 357, 20, 10, 0], [2, "3810", 393, 371, 20, 10, 0], [2, "3810", 191, 657, 20, 10, 0], [2, "3801", 648, 30, 26, 23, 0], [2, "3814", 559, 19, 50, 34, 0], [2, "3869", 689, 326, 30, 20, 0], [2, "3869", 813, 307, 30, 20, 0], [2, "3782", 1146, 535, 48, 72, 0], [2, "3782", 1015, 607, 48, 72, 0], [2, "3786", 1046, 667, 60, 33, 2], [2, "3786", 1093, 691, 60, 33, 2], [2, "3786", 1140, 714, 60, 33, 2], [2, "3786", 1175, 595, 60, 33, 2], [2, "3800", 930, 335, 64, 100, 0], [2, "3779", 866, 409, 102, 80, 2], [2, "3800", 798, 442, 100, 64, 7], [2, "3800", 602, 671, 100, 64, 7], [2, "3800", 759, 468, 64, 100, 0], [2, "3800", 668, 631, 100, 64, 7], [2, "3800", 730, 548, 64, 100, 0], [2, "3844", 794, 459, 78, 71, 0], [2, "3845", 795, 500, 58, 62, 0], [2, "3847", 741, 620, 62, 63, 2], [2, "3837", 686, 662, 76, 42, 0], [2, "3837", 950, 397, 76, 42, 0], [2, "3841", 955, 358, 76, 41, 0], [2, "3843", 940, 336, 50, 28, 0], [2, "3844", 1083, -16, 78, 71, 0], [2, "3844", 1139, -9, 78, 71, 2], [2, "3845", 1118, 28, 58, 62, 0], [2, "3846", 1065, 38, 60, 31, 0], [2, "3845", 1046, -12, 58, 62, 2], [2, "3841", 1047, 58, 76, 41, 0], [2, "3836", 975, 1, 92, 66, 0], [2, "3842", 1106, 107, 58, 31, 0], [2, "3837", 1145, 183, 76, 42, 0], [2, "3838", 999, 39, 38, 31, 0], [2, "3844", 835, -29, 78, 71, 0], [2, "3837", 782, -5, 76, 42, 0], [2, "3835", 837, -26, 32, 77, 0], [2, "3835", 865, 430, 32, 77, 0], [2, "3835", 778, 516, 32, 77, 0], [2, "3835", 732, 594, 32, 77, 0], [2, "3846", 896, -9, 60, 31, 0], [2, "3849", 939, 33, 34, 25, 0], [2, "3813", 888, 60, 48, 36, 0], [2, "3812", 1112, 165, 24, 19, 0], [2, "3811", 1114, 402, 36, 26, 0], [2, "3811", 905, 598, 36, 26, 0], [2, "3813", 866, 624, 48, 36, 0], [2, "3812", 886, 524, 24, 19, 0], [2, "3810", 1049, 429, 20, 10, 0], [2, "3810", 967, 499, 20, 10, 0], [2, "3810", 930, 285, 20, 10, 0], [2, "3810", 975, 110, 20, 10, 0], [2, "3810", 975, 110, 20, 10, 0], [2, "3810", 888, 48, 20, 10, 0], [2, "3810", 1058, 134, 20, 10, 0], [2, "3810", 1077, 422, 20, 10, 0], [2, "3810", 1083, 667, 20, 10, 0], [2, "3810", 960, 679, 20, 10, 0], [2, "3810", 808, 605, 20, 10, 0], [2, "3810", 1097, 487, 20, 10, 0], [2, "3815", 1106, 428, 20, 17, 0], [2, "3823", 1169, 567, 34, 41, 2], [2, "3823", 1038, 639, 34, 41, 2], [2, "3802", 902, 5, 34, 32, 2], [2, "3845", 965, 597, 58, 62, 0], [2, "3849", 1107, 528, 34, 25, 2], [2, "3827", 776, 600, 32, 30, 0], [2, "3780", 1072, 602, 108, 58, 0], [2, "3780", 1139, 636, 108, 58, 0], [2, "28", 271, 125, 12, 27, 0], [2, "28", 240, 146, 12, 27, 0], [2, "28", 202, 165, 12, 27, 0], [2, "28", 136, 166, 12, 27, 0], [2, "28", 103, 146, 12, 27, 0], [2, "257", 140, 104, 14, 66, 0], [2, "3735", 207, 93, 82, 45, 0], [2, "3735", 191, 99, 82, 45, 0], [2, "3735", 180, 109, 82, 45, 0], [2, "3735", 180, 109, 82, 45, 0], [2, "3735", 162, 116, 82, 45, 0], [2, "3735", 152, 125, 82, 45, 0], [2, "3735", 134, 130, 82, 45, 0], [2, "3735", 120, 139, 82, 45, 0], [2, "3735", 107, 147, 82, 45, 0], [2, "3735", 142, 85, 82, 45, 2], [2, "3735", 89, 112, 82, 45, 2], [2, "3735", 128, 75, 82, 45, 2], [2, "3735", 75, 102, 82, 45, 2], [2, "28", 74, 129, 12, 27, 0], [2, "28", 170, 179, 12, 27, 0], [2, "3735", 107, 147, 82, 45, 0], [2, "510", 205, 154, 66, 40, 2], [2, "257", 242, 67, 14, 66, 0], [2, "257", 235, 70, 14, 66, 0], [2, "257", 228, 73, 14, 66, 0], [2, "257", 222, 76, 14, 66, 0], [2, "257", 215, 79, 14, 66, 0], [2, "257", 105, 81, 14, 66, 0], [2, "257", 112, 85, 14, 66, 0], [2, "257", 118, 89, 14, 66, 0], [2, "257", 125, 93, 14, 66, 0], [2, "257", 132, 96, 14, 66, 0], [2, "257", 174, 97, 14, 66, 0], [2, "257", 139, 100, 14, 66, 0], [2, "257", 167, 100, 14, 66, 0], [2, "257", 160, 103, 14, 66, 0], [2, "257", 146, 104, 14, 66, 0], [2, "257", 153, 107, 14, 66, 0], [2, "759", 89, 75, 20, 75, 0], [2, "461", 161, 155, 24, 20, 0], [2, "461", 164, 153, 24, 20, 0], [2, "461", 222, 126, 24, 20, 0], [2, "461", 232, 121, 24, 20, 0], [2, "461", 137, 155, 24, 20, 2], [2, "461", 116, 145, 24, 20, 2], [2, "461", 104, 139, 24, 20, 2], [2, "234", 114, 99, 34, 63, 0], [2, "1200", 117, 111, 24, 32, 2], [2, "759", 150, 106, 20, 75, 0], [2, "759", 243, 63, 20, 75, 0], [2, "3858", 903, 641, 74, 45, 0], [2, "3858", 952, 657, 74, 45, 0], [2, "3858", 741, 679, 74, 45, 0], [2, "3858", 783, 569, 74, 45, 0], [2, "3858", 748, 461, 74, 45, 0], [2, "3858", 933, 435, 74, 45, 0], [2, "3858", 1055, 484, 74, 45, 0], [2, "3858", 1123, 379, 74, 45, 0], [2, "3858", 1072, 332, 74, 45, 0], [2, "3858", 952, 215, 74, 45, 0], [2, "3858", 952, 215, 74, 45, 0], [2, "3858", 899, 247, 74, 45, 0], [2, "3858", 911, 224, 74, 45, 0], [2, "3858", 963, 55, 74, 45, 0], [2, "3858", 909, 21, 74, 45, 0], [2, "3858", 790, 34, 74, 45, 0], [2, "3858", 1105, 200, 74, 45, 0], [2, "3858", 1016, 267, 74, 45, 0], [2, "3858", 1169, 289, 74, 45, 0], [2, "3858", 1169, 289, 74, 45, 0], [2, "3858", 908, 463, 74, 45, 0], [2, "3858", 896, 564, 74, 45, 0], [2, "3858", 1078, 620, 74, 45, 0], [2, "3858", 1106, 587, 74, 45, 0], [2, "3858", 1042, 542, 74, 45, 0], [2, "3858", 952, 323, 74, 45, 0]]}, {"type": 2, "data": [33, 33, 33, 33, -1, -1, 51, 50, -1, -1, -1, 49, 52, 51, 51, 51, 51, 38, -1, -1, 23, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, 116, 119, 118, 118, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 33, 39, 46, 48, 48, 48, 48, 47, -1, -1, -1, -1, 49, 48, 52, 51, 51, 38, -1, -1, 20, 23, 4, 4, 4, 4, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, 116, 115, 119, 118, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 33, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 51, 51, 38, -1, -1, -1, 20, 23, 4, 4, 4, 4, 4, 5, 2, 95, -1, -1, -1, -1, -1, -1, -1, 116, 115, 119, 118, 100, 100, 100, 100, 100, 100, 100, 100, 45, 46, 43, -1, -1, -1, -1, -1, 45, -1, -1, -1, -1, -1, 44, 51, 51, 38, -1, -1, -1, -1, 20, 23, 4, 4, 4, 4, 4, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 115, 115, 119, 100, 100, 100, 100, 100, 100, 42, 43, 37, 36, 45, 45, -1, 45, 45, -1, -1, -1, -1, -1, 49, 48, 48, 43, -1, -1, -1, -1, -1, 20, 23, 4, 4, 4, 4, 4, 9, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 100, 100, 100, 100, -1, 29, 40, 39, 45, 45, 45, 45, 45, 45, 45, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 4, 4, 4, 4, 5, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, -1, 32, 51, -1, 45, 45, 45, 45, 33, 33, 33, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 4, 4, 4, 4, 10, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, -1, 49, 48, 52, 51, 45, 45, 45, 45, 45, 46, 43, -1, -1, -1, -1, 37, 36, 36, 35, -1, -1, -1, -1, -1, 15, 22, 4, 4, 4, 4, 17, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 100, 100, 100, -1, -1, -1, 49, 48, 52, 51, 45, 46, 42, 43, -1, -1, -1, -1, -1, 44, 51, 39, 38, -1, -1, -1, -1, 8, 11, 22, 4, 4, 4, 4, 21, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, -1, -1, -1, -1, -1, 49, 48, 42, 43, -1, -1, -1, -1, -1, -1, -1, 49, 48, -1, -1, -1, -1, -1, -1, 15, 22, 4, 4, 4, 4, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 30, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 107, 100, 100, 100, 33, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, 99, 106, 100, 100, 100, 100, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 6, -1, -1, -1, -1, -1, -1, 104, 107, 100, 100, 100, 100, 100, -1, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, -1, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, -1, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 17, 18, -1, -1, -1, -1, -1, -1, 116, 119, 118, 100, 100, 100, 100, 45, 45, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 10, 4, 4, 4, 4, 4, 4, 4, 4, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, 111, 118, 100, 100, 100, 100, 45, 45, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 7, 7, 7, 6, -1, -1, 8, 7, 11, 10, 4, 4, 4, 4, 4, 4, 17, 13, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 100, 100, 100, 100, 45, 46, 47, -1, -1, 37, 36, 35, -1, -1, -1, 0, 11, 4, 4, 4, 5, 7, 7, 11, 10, 4, 4, 4, 4, 4, 4, 4, 17, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 100, 100, 100, 42, 43, -1, -1, 29, 40, 39, 38, -1, -1, 8, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, -1, -1, -1, -1, 44, 39, -1, -1, -1, -1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, -1, -1, -1, -1, 49, 52, -1, -1, -1, -1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 107, 100, 100, 100, -1, -1, -1, -1, -1, 44, -1, -1, -1, 0, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 103, 107, 100, 100, 100, 100, -1, -1, -1, -1, -1, 44, 45, 45, 50, 15, 4, 4, 4, 4, 4, 4, 4, 4, 16, 17, 19, 19, 23, 22, 4, 4, 4, 4, 4, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 107, 100, 100, 100, 100, 100, 100, 100, -1, -1, -1, -1, -1, 41, 52, 51, 47, 15, 4, 22, 22, 22, 4, 4, 22, 22, 13, 14, -1, -1, 20, 19, 23, 22, 4, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 30, 31, -1, -1, -1, -1, 49, 47, 8, 11, 4, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, 20, 19, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 100, 100, 100, 100, 100, 100, 100, 100, 33, 34, 30, 31, -1, -1, -1, 8, 11, 10, 4, 4, 4, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 107, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, 33, 38, -1, -1, -1, 3, 4, 4, 4, 4, 4, 4, 22, 22, 22, 22, 22, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 107, 106, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, 39, 38, -1, -1, -1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, -1, -1, 39, 34, 31, -1, -1, 20, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 22, 22, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 97, 107, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100]}, {"type": 3, "obj": [[2, "3810", 710, 369, 20, 10, 0], [2, "3810", 763, 335, 20, 10, 0], [2, "3811", 743, 282, 36, 26, 0], [2, "3813", 700, 242, 48, 36, 0], [2, "3833", 635, 482, 36, 26, 0], [2, "3834", 751, 389, 36, 26, 0], [2, "3802", 738, 127, 34, 32, 0]]}, {"type": 2, "data": [62, 63, 64, 66, 66, 66, 66, 66, 66, 66, 66, 62, 63, 64, 62, 63, 64, 55, 55, 53, 54, 55, 86, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 62, 63, 64, 62, 63, 65, 66, 67, 65, 62, 63, 62, 63, 62, 63, 64, 56, 57, 58, 83, 94, 93, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 66, 67, 65, 66, 67, 65, 66, 68, 69, 70, 68, 65, 66, 65, 66, 65, 66, 67, 59, 60, 61, 59, 91, 90, 94, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 68, 69, 70, 68, 69, 70, 68, 69, 70, 62, 63, 64, 68, 69, 68, 62, 68, 69, 70, 55, 53, 54, 55, 53, 54, 91, 90, 94, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 69, 66, 62, 62, 63, 64, 65, 62, 63, 64, 62, 68, 65, 66, 67, 67, 58, 56, 57, 58, 56, 57, 56, 53, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 66, 67, 64, 62, 65, 65, 62, 63, 64, 65, 66, 67, 62, 63, 68, 69, 70, 70, 55, 53, 54, 55, 55, 60, 59, 56, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 68, 69, 70, 62, 63, 64, 62, 65, 66, 67, 68, 62, 63, 65, 66, 62, 63, 64, 57, 58, 56, 57, 58, 53, 54, 55, 59, 91, 94, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 66, 66, 66, 65, 66, 67, 65, 68, 69, 70, 62, 65, 66, 68, 69, 65, 66, 67, 60, 61, 59, 60, 61, 56, 57, 58, 53, 57, 86, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 63, 64, 66, 68, 69, 70, 68, 69, 70, 62, 65, 68, 62, 63, 64, 68, 69, 70, 61, 53, 54, 55, 61, 59, 60, 61, 56, 57, 86, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 66, 67, 65, 65, 66, 67, 66, 66, 66, 62, 63, 64, 65, 66, 67, 55, 53, 54, 55, 56, 57, 58, 55, 53, 54, 55, 59, 71, 82, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 68, 69, 70, 62, 63, 64, 65, 66, 67, 68, 69, 70, 58, 56, 57, 58, 59, 60, 61, 58, 56, 57, 58, 53, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 66, 67, 69, 70, 68, 65, 66, 67, 68, 62, 63, 64, 59, 60, 61, 59, 60, 61, 53, 59, 60, 61, 59, 60, 61, 56, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 62, 63, 64, 68, 69, 62, 63, 65, 66, 67, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 79, 82, 81, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 66, 67, 65, 66, 62, 62, 63, 64, 66, 68, 69, 70, 58, 56, 57, 58, 56, 57, 58, 56, 57, 58, 56, 57, 79, 82, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 62, 63, 64, 65, 66, 67, 69, 70, 59, 60, 61, 59, 60, 61, 59, 60, 61, 59, 60, 61, 59, 60, 86, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 66, 67, 65, 66, 67, 68, 69, 70, 54, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 54, 56, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 68, 69, 70, 68, 69, 70, 69, 70, 56, 57, 58, 56, 57, 58, 56, 57, 58, 56, 57, 58, 56, 57, 58, 57, 59, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 64, 68, 69, 70, 60, 59, 60, 61, 59, 60, 61, 59, 60, 61, 59, 60, 61, 59, 60, 61, 54, 55, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 66, 67, 67, 66, 66, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 56, 79, 78, 82, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 68, 69, 70, 70, 66, 57, 58, 56, 57, 58, 56, 57, 58, 56, 57, 58, 56, 57, 58, 53, 54, 79, 78, 82, 81, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 66, 59, 60, 61, 59, 60, 61, 59, 60, 61, 59, 60, 61, 59, 60, 79, 78, 72, 82, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 63, 64, 67, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 53, 54, 55, 79, 78, 82, 81, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 66, 67, 70, 58, 56, 57, 58, 56, 57, 58, 56, 57, 58, 56, 79, 78, 82, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 63, 64, 61, 59, 60, 61, 59, 60, 61, 59, 60, 79, 78, 82, 81, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 66, 67, 53, 59, 60, 61, 59, 60, 61, 54, 79, 82, 81, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 68, 69, 70, 56, 57, 58, 56, 57, 58, 56, 57, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 62, 62, 63, 64, 60, 61, 59, 60, 61, 59, 60, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 65, 65, 66, 67, 64, 55, 53, 54, 55, 53, 54, 74, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 68, 68, 69, 70, 67, 58, 56, 57, 58, 56, 57, 91, 94, 93, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 66, 65, 66, 67, 62, 63, 64, 60, 61, 59, 60, 61, 91, 90, 94, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}