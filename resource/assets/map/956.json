{"mW": 960, "mH": 840, "tW": 24, "tH": 24, "tiles": [["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["709_2", 0, 2, 1], ["91", 1, 3, 2], ["91", 3, 3, 2], ["995", 0, 3, 2], ["995", 2, 3, 2], ["995", 1, 3, 2], ["995", 3, 3, 2], ["444_2", 0, 2, 2], ["996", 0, 2, 2], ["1234", 0, 3, 2], ["1234", 2, 3, 2], ["1234", 1, 3, 2], ["1234", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "333", 96, 359, 38, 35, 2], [2, "332", 157, 392, 36, 38, 0], [2, "334", 18, 414, 40, 32, 2], [2, "334", 30, 380, 32, 40, 4], [2, "334", 99, 381, 40, 32, 0], [2, "334", 83, 345, 40, 32, 0], [2, "333", 44, 347, 38, 35, 0], [2, "334", 61, 338, 40, 32, 0], [2, "333", 129, 398, 38, 35, 2], [2, "332", 116, 371, 36, 38, 0], [2, "334", 60, 346, 40, 32, 0], [2, "334", 69, 361, 40, 32, 0], [2, "333", 23, 376, 38, 35, 2], [2, "332", 63, 359, 36, 38, 0], [2, "334", 54, 399, 40, 32, 2], [2, "333", -15, 403, 38, 35, 0], [2, "332", 4, 388, 36, 38, 0], [2, "332", 122, 353, 36, 38, 0], [2, "125", 40, 68, 18, 70, 0], [2, "457", 160, 12, 70, 59, 2], [2, "457", 114, 33, 70, 59, 2], [2, "457", 137, -19, 70, 59, 2], [2, "458", 66, 32, 54, 55, 2], [2, "457", 91, 2, 70, 59, 2], [2, "124", -41, 80, 142, 70, 2], [2, "125", 10, 130, 18, 70, 0], [2, "125", 89, 102, 18, 70, 0], [2, "487", 34, 102, 36, 23, 0], [2, "455", 186, -21, 50, 74, 2], [2, "455", 86, 25, 50, 74, 2], [2, "456", 46, 28, 40, 32, 2], [2, "83_4", 132, -30, 64, 38, 2], [2, "83_4", 80, -4, 64, 38, 2], [2, "502", 143, 18, 32, 29, 0], [2, "502", 114, 63, 32, 29, 2], [2, "502", 65, 43, 32, 29, 0]]}, {"type": 4, "obj": [[2, "1389", 783, 108, 122, 125, 2], [2, "429", 667, 222, 64, 63, 0], [2, "1389", 816, 434, 122, 125, 0]]}, {"type": 3, "obj": [[2, "329", -14, 282, 42, 37, 2], [2, "307", 170, 146, 42, 19, 0], [2, "307", 225, 128, 42, 19, 0], [2, "174_3", 129, 500, 68, 33, 0], [2, "174_2", 213, 17, 68, 33, 2], [2, "799", 330, 31, 46, 26, 2], [2, "799", 301, 22, 46, 26, 0], [2, "14_1", 1, 220, 32, 30, 0], [2, "14_1", 305, 65, 32, 30, 0], [2, "1109", 312, 59, 20, 19, 5], [2, "1105", 217, 10, 104, 75, 2], [2, "445", 745, 226, 50, 22, 2], [2, "391", 788, 221, 86, 55, 0], [2, "392", 686, 226, 118, 69, 2], [2, "435", 651, 260, 50, 77, 2], [2, "422", 690, 250, 16, 14, 0], [2, "426", 854, 268, 26, 22, 2], [2, "328", 640, 251, 32, 29, 2], [2, "425", 895, 397, 30, 36, 2], [2, "437", 861, 534, 20, 19, 0], [2, "441", 662, 337, 20, 29, 5], [2, "552", 645, 334, 22, 15, 0], [2, "439", 687, 408, 64, 42, 2], [2, "438", 669, 365, 26, 43, 2], [2, "420", 668, 386, 16, 13, 2], [2, "328", 667, 377, 32, 29, 2], [2, "391", 879, 515, 86, 55, 2], [2, "328", 884, 543, 32, 29, 2], [2, "437", 837, 536, 20, 19, 0], [2, "253_1", 273, 280, 92, 53, 2], [2, "552", 197, 207, 22, 15, 2], [2, "174_2", 301, 106, 68, 33, 0], [2, "420", 71, 568, 16, 13, 0], [2, "553", 104, 549, 14, 8, 0], [2, "548", 67, 536, 30, 24, 0], [2, "552", 84, 559, 22, 15, 0], [2, "420", 95, 542, 16, 13, 0], [2, "422", 74, 547, 16, 14, 2], [2, "326", 106, 552, 18, 14, 0], [2, "1389", 270, -59, 122, 125, 2], [2, "552", 156, 509, 22, 15, 2], [2, "174_2", 426, 537, 68, 33, 0], [2, "174_2", 753, 209, 68, 33, 2], [2, "174_2", 701, 509, 68, 33, 2], [2, "174_2", 827, 575, 68, 33, 2], [2, "420", 891, 649, 16, 13, 0], [2, "553", 924, 630, 14, 8, 0], [2, "548", 887, 617, 30, 24, 0], [2, "552", 904, 640, 22, 15, 0], [2, "420", 915, 623, 16, 13, 0], [2, "422", 894, 628, 16, 14, 2], [2, "326", 926, 633, 18, 14, 0], [2, "438", 851, 204, 26, 43, 2], [2, "439", 801, 164, 64, 42, 2], [2, "438", 786, 130, 26, 43, 2], [2, "420", 660, 337, 16, 13, 0], [2, "438", 740, 446, 26, 43, 2], [2, "441", 755, 483, 28, 21, 2], [2, "439", 772, 504, 64, 42, 2], [2, "263", 914, 543, 34, 34, 0], [2, "308", 51, 158, 52, 22, 0], [2, "306", -4, 173, 46, 25, 0], [2, "307", 41, 175, 42, 19, 0], [2, "308", 13, 164, 52, 22, 0], [2, "459", 155, 82, 20, 33, 0], [2, "459", 135, 92, 20, 33, 0], [2, "464", 51, 48, 18, 82, 0], [2, "464", 214, 42, 18, 82, 0], [2, "460", 182, 101, 20, 37, 0], [2, "460", 201, 91, 20, 37, 0], [2, "460", 156, 111, 20, 37, 0], [2, "460", 135, 121, 20, 37, 0], [2, "459", 203, 60, 20, 33, 0], [2, "459", 183, 70, 20, 33, 0], [2, "461", 200, 84, 24, 20, 0], [2, "461", 178, 93, 24, 20, 0], [2, "461", 157, 102, 24, 20, 0], [2, "461", 134, 113, 24, 20, 0], [2, "462", 176, 72, 6, 33, 0], [2, "460", 63, 102, 20, 37, 2], [2, "460", 83, 114, 20, 37, 2], [2, "460", 99, 122, 20, 37, 2], [2, "460", 63, 84, 20, 37, 2], [2, "460", 82, 94, 20, 37, 2], [2, "460", 98, 102, 20, 37, 2], [2, "460", 66, 66, 20, 37, 2], [2, "460", 82, 76, 20, 37, 2], [2, "460", 98, 84, 20, 37, 2], [2, "464", 115, 78, 18, 82, 0], [2, "822", 150, 67, 22, 20, 2], [2, "822", 200, 43, 22, 20, 2], [2, "822", 214, 36, 22, 20, 2], [2, "822", 186, 50, 22, 20, 2], [2, "822", 172, 57, 22, 20, 2], [2, "822", 159, 63, 22, 20, 2], [2, "459", 183, 65, 20, 33, 0], [2, "459", 202, 55, 20, 33, 0], [2, "822", 126, 79, 22, 20, 2], [2, "822", 148, 69, 22, 20, 2], [2, "822", 135, 75, 22, 20, 2], [2, "459", 155, 77, 20, 33, 0], [2, "459", 134, 87, 20, 33, 0], [2, "129", 227, 52, 14, 9, 0], [2, "129", 42, 65, 14, 9, 2], [2, "459", 77, 78, 20, 33, 2], [2, "459", 96, 87, 20, 33, 2], [2, "459", 78, 68, 20, 33, 2], [2, "459", 96, 76, 20, 33, 2], [2, "34", 167, 71, 30, 53, 0], [2, "34", 167, 95, 30, 53, 0], [2, "824", 167, 72, 30, 62, 2], [2, "459", 63, 72, 20, 33, 2], [2, "459", 63, 61, 20, 33, 2], [2, "461", 63, 92, 24, 20, 2], [2, "461", 84, 102, 24, 20, 2], [2, "461", 93, 107, 24, 20, 2], [2, "129", 114, 95, 14, 9, 2], [2, "98", 145, 117, 20, 38, 0], [2, "98", 132, 122, 20, 38, 0], [2, "98", 201, 93, 20, 38, 0], [2, "98", 197, 95, 20, 38, 0], [2, "771", 164, 70, 36, 26, 2], [2, "462", 85, 110, 6, 33, 2], [2, "770", 33, 118, 28, 25, 0], [2, "116", 49, 109, 46, 39, 0], [2, "328", 378, 519, 32, 29, 0], [2, "502", 189, 97, 32, 29, 0], [2, "1102", 123, 112, 114, 63, 2], [2, "263", 242, 88, 34, 34, 0], [2, "8", 225, 111, 38, 29, 0], [2, "12", 230, 99, 26, 28, 0], [2, "422", 235, 125, 16, 14, 0], [2, "1102", 35, 117, 114, 63, 0], [2, "116", 32, 122, 46, 39, 0], [2, "116", 74, 123, 46, 39, 0], [2, "116", 41, 141, 46, 39, 0], [2, "306", 84, 167, 46, 25, 0], [2, "5_1", 31, 450, 42, 66, 0], [2, "5_1", 73, 451, 42, 66, 2], [2, "263", 25, 484, 34, 34, 0], [2, "331", 44, 386, 104, 108, 0], [2, "1109", 48, 512, 20, 19, 5], [2, "479", 121, 169, 36, 18, 0], [2, "14_1", 784, 198, 32, 30, 0], [2, "14_1", 770, 204, 32, 30, 0], [2, "14_1", 776, 190, 32, 30, 0], [2, "1109", 778, 218, 18, 21, 0], [2, "1103", 274, 40, 44, 41, 0], [2, "125", 309, 50, 18, 70, 0], [2, "47_3", 17, 264, 54, 63, 0], [2, "125", 261, 24, 18, 70, 0], [2, "329", 346, 152, 42, 37, 0], [2, "328", 322, 157, 32, 29, 0], [2, "422", 340, 170, 16, 14, 0], [2, "64", 321, 154, 14, 15, 0], [2, "63_1", 332, 138, 16, 31, 0], [2, "422", 332, 159, 16, 14, 0], [2, "62_1", 305, 157, 16, 27, 0], [2, "422", 303, 172, 16, 14, 0], [2, "326", 315, 175, 18, 14, 0], [2, "325", 62, 211, 50, 37, 0], [2, "21", 110, 218, 28, 24, 0], [2, "65", 173, 244, 12, 6, 0], [2, "64", 145, 237, 14, 15, 2], [2, "63_1", 128, 225, 16, 31, 0], [2, "64", 116, 243, 14, 15, 0], [2, "62_1", 156, 235, 16, 27, 0], [2, "62_1", 102, 240, 16, 27, 0], [2, "327", 121, 245, 30, 22, 2], [2, "422", 104, 256, 16, 14, 0], [2, "328", 156, 243, 32, 29, 2], [2, "422", 63, 304, 16, 14, 0], [2, "62_1", 185, 229, 16, 27, 0], [2, "21", 301, 115, 28, 24, 0], [2, "174_2", 682, 147, 68, 33, 2], [2, "391", 904, 94, 86, 55, 0], [2, "392", 810, 102, 118, 69, 2], [2, "328", 255, 554, 32, 29, 0], [2, "328", 306, 651, 32, 29, 0], [2, "328", 33, 711, 32, 29, 0], [2, "21", 168, 785, 28, 24, 0], [2, "65", 231, 811, 12, 6, 0], [2, "64", 203, 804, 14, 15, 2], [2, "63_1", 186, 792, 16, 31, 0], [2, "64", 174, 810, 14, 15, 0], [2, "62_1", 214, 802, 16, 27, 0], [2, "62_1", 160, 807, 16, 27, 0], [2, "327", 179, 812, 30, 22, 2], [2, "422", 162, 823, 16, 14, 0], [2, "328", 214, 810, 32, 29, 2], [2, "62_1", 243, 796, 16, 27, 0], [2, "329", 321, 781, 42, 37, 0], [2, "328", 297, 786, 32, 29, 0], [2, "422", 315, 799, 16, 14, 0], [2, "64", 296, 783, 14, 15, 0], [2, "63_1", 307, 767, 16, 31, 0], [2, "422", 307, 788, 16, 14, 0], [2, "62_1", 280, 786, 16, 27, 0], [2, "422", 278, 801, 16, 14, 0], [2, "326", 290, 804, 18, 14, 0], [2, "420", 388, 825, 16, 13, 0], [2, "553", 421, 806, 14, 8, 0], [2, "548", 384, 793, 30, 24, 0], [2, "552", 401, 816, 22, 15, 0], [2, "420", 412, 799, 16, 13, 0], [2, "422", 391, 804, 16, 14, 2], [2, "326", 423, 809, 18, 14, 0], [2, "174_3", 15, 791, 68, 33, 0], [2, "174_2", 346, 745, 68, 33, 2], [2, "325", 808, 57, 50, 37, 0], [2, "328", 864, 76, 32, 29, 2], [2, "552", 740, 468, 22, 15, 2], [2, "420", 751, 480, 16, 13, 2], [2, "420", 689, 423, 16, 13, 2], [2, "402", 810, 454, 48, 26, 2], [2, "402", 919, 417, 48, 26, 2], [2, "410", 934, 225, 14, 35, 2], [2, "401", 894, 253, 62, 36, 2], [2, "409", 923, 390, 28, 22, 2], [2, "430", 946, 428, 20, 27, 0], [2, "402", 794, 279, 48, 26, 2], [2, "403", 923, 299, 40, 28, 2], [2, "430", 787, 280, 20, 27, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 22, 28, 27, -1, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, 34, 38, 39, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 21, -1, -1, -1, 24, -1, 24, -1, 10, 11, 12, 20, 21, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, -1, 24, 0, 1, 2, 42, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 38, -1, -1, 24, 24, 0, 1, 2, -1, -1, -1, 20, 21, -1, -1, -1, -1, 86, 85, 85, 85, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 0, 5, 0, 1, 2, -1, -1, -1, -1, 34, 35, -1, -1, -1, 78, 79, 89, 88, 88, 88, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 82, 82, 82, 94, 95, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 93, 94, 82, 100, 99, 92, 96, -1, -1, -1, -1, -1, -1, -1, 20, 21, -1, 38, 39, 27, 26, -1, -1, 41, 40, -1, -1, 20, 21, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 93, 94, 82, 100, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 101, 94, 100, 99, -1, -1, -1, -1, -1, 20, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, -1, -1, -1, 78, 79, 82, 85, 84, -1, -1, -1, -1, -1, -1, -1, -1, 90, 91, 97, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 41, 40, -1, -1, 81, 82, 82, 88, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 82, 82, 100, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 82, -1, 97, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 21, 22, 21, 22, -1, -1, -1, 78, 79, 79, 79, 80, -1, -1, -1, -1, -1, -1, -1, 78, 79, 85, 85, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 45, 44, -1, 44, 43, -1, -1, -1, 81, 82, 82, 82, 85, 85, 84, -1, -1, -1, -1, -1, 81, 82, 88, 88, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 41, 35, 41, 40, 82, -1, -1, 81, 82, 82, 82, 82, 82, 99, -1, -1, -1, -1, -1, 93, 94, 82, 82, 83, 85, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 78, 79, 89, 82, 82, 82, 82, 82, 83, 84, -1, -1, -1, -1, 90, 101, 100, 82, 82, 88, 87, 85, 84, -1, -1, -1, -1, 20, 21, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 82, 100, 82, 82, 82, 82, 82, 82, 87, -1, -1, -1, -1, -1, 98, 97, 82, 82, 82, 82, 88, 87, 79, 80, -1, -1, 34, 35, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, 93, 94, 100, 100, 82, 100, 82, 82, 95, 96, -1, -1, -1, -1, -1, -1, 93, 94, 82, 82, -1, 82, 82, 82, 83, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 97, 97, 97, 101, 100, 100, 100, 99, -1, -1, -1, -1, -1, -1, -1, 90, 91, 82, 82, 82, -1, 82, 88, 82, 87, 84, -1, -1, -1, -1, 79, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 97, 97, 97, 96, -1, -1, -1, -1, -1, -1, -1, -1, 90, 91, 92, -1, 82, 88, 88, 82, 82, 87, -1, 78, 79, 80, 82, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 91, 97, 97, 96, 96, 78, 79, 82, 83, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 82, 82, 82, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 91, 101, 100, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 101, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 78, 79, 82, 82, 79, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 22, 28, 27, 26, 20, 21, 22, 89, 82, 82, 82, 82, 83, 85, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, 42, 41, 40, 42, 41, 40, 82, 82, 82, 82, 82, 82, 88, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [70, 70, 71, 70, 71, 70, 71, 70, 71, 70, 71, 71, 71, 70, 71, 51, 52, 75, 75, 61, 62, 72, 70, 71, 70, 71, 70, 70, 70, 71, 70, 71, 70, 70, 71, 70, 71, 70, 70, 71, 72, 72, 70, 71, 73, 72, 73, 72, 73, 72, 73, 73, 73, 72, 73, 72, 55, 77, 77, 49, 70, 71, 71, 73, 72, 73, 72, 72, 72, 73, 72, 73, 72, 72, 73, 72, 73, 70, 72, 73, 70, 71, 70, 71, 70, 71, 70, 71, 70, 71, 70, 71, 70, 70, 71, 70, 67, 74, 46, 57, 72, 73, 70, 70, 70, 71, 70, 71, 70, 71, 70, 71, 70, 70, 71, 70, 71, 72, 73, 72, 72, 73, 72, 73, 72, 73, 72, 73, 70, 71, 72, 73, 70, 72, 73, 63, 64, 76, 49, 50, 72, 50, 72, 72, 72, 73, 72, 73, 72, 62, 63, 65, 65, 69, 73, 72, 73, 70, 71, 70, 70, 71, 70, 71, 70, 71, 70, 71, 72, 73, 70, 71, 72, 70, 71, 64, 74, 75, 58, 69, 71, 71, 70, 71, 70, 71, 70, 70, 70, 63, 60, 75, 76, 61, 70, 71, 71, 72, 73, 72, 72, 73, 72, 73, 72, 73, 70, 71, 69, 68, 72, 73, 71, 72, 64, 75, 75, 77, 46, 57, 70, 71, 72, 73, 72, 73, 70, 63, 65, 64, 76, 77, 74, 58, 71, 73, 73, -1, -1, -1, 70, 71, 70, 71, 70, 71, 74, 75, 66, 65, 69, 68, 71, 51, 52, 74, 46, 47, 57, 70, 71, 73, 70, 71, 70, 71, 72, 67, 75, 74, 74, 75, 75, 73, 32, 33, 32, 33, 32, 33, 72, 73, 72, 73, 72, 73, 47, 48, 74, 75, 66, 65, 69, 71, 67, 75, 49, 50, 70, 72, 73, 70, 72, 73, 72, 73, 72, 67, 77, 76, 76, 46, 47, 71, 72, 32, 33, 32, 33, 32, 70, 71, 70, 71, 70, 71, 50, 51, 52, 75, 74, 75, 58, 59, 60, 75, 61, 70, 72, 70, 71, 71, 72, 73, 70, 71, 70, 51, 52, 75, 74, 49, 50, 75, 71, -1, 32, 33, 32, 33, 72, 73, 72, 73, 72, 73, 72, 73, 55, 46, 47, 53, 52, 74, 75, 74, 49, 70, 70, 72, 73, 73, 71, 71, 72, 73, 72, 73, 55, 77, 46, 57, 77, 77, 73, 32, 33, 32, 33, 32, 70, 71, 70, 71, 70, 70, 63, 65, 64, 66, 69, 56, 51, 53, 52, 76, 61, 72, 72, 70, 71, 72, 73, 73, 70, 71, 70, 71, 51, 72, 73, 72, 73, -1, -1, -1, 32, 33, 32, 33, 70, 71, 72, 73, 70, 56, 55, 74, 75, 74, 61, 73, 70, 68, 67, 76, 58, 69, 70, 72, 73, 70, 71, 71, 72, 73, 72, 73, 71, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 70, 71, 70, 71, 72, 73, 72, 47, 48, 75, 58, 69, 62, 63, 64, 75, 74, 58, 69, 72, 73, 72, 73, 73, 70, 71, 70, 72, 73, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 72, 73, 72, 73, 73, 70, 71, 68, 67, 74, 74, 58, 65, 64, 75, 77, 74, 75, 61, 70, 70, 72, 70, 71, 72, 73, 72, 73, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 50, 73, 70, 71, 70, 70, 63, 63, 64, 74, 74, 75, 76, 76, 74, 75, 76, 46, 57, 72, 72, 73, 72, 73, 72, 73, 72, 73, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 50, 71, 70, 71, 72, 72, 67, 74, 75, 75, 76, 77, 74, 75, 76, 77, 46, 57, 62, 70, 71, 73, 72, 73, 70, 71, 70, 71, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 50, 71, 72, 70, 71, 70, 51, 76, 77, 75, 74, 75, 76, 77, 74, 75, 58, 65, 69, 62, 73, 71, 70, 71, 72, 73, 72, 70, 71, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 50, 73, 70, 72, 73, 72, 72, 73, 47, 48, 74, 74, 74, 75, 76, 77, 76, 77, 58, 69, 72, 73, 72, 73, 71, 71, 72, 72, 73, 32, 33, 32, 33, 32, 33, 32, 33, 32, 33, 32, 70, 71, 72, 73, 70, 71, 70, 71, 50, 51, 47, 48, 76, 46, 47, 53, 52, 74, 75, 58, 69, 62, 71, 72, 73, 73, 72, 72, 73, 70, 71, 32, 33, 32, 33, 32, 33, 32, 33, 32, 72, 73, 72, 73, 70, 71, 72, 73, 72, 70, 50, 51, 53, 57, 72, 56, 51, 48, 77, 76, 61, 63, 69, 68, 71, 70, 71, 71, 70, 72, 73, 33, 32, 33, 32, 33, 32, 33, 32, 33, 70, 50, 70, 71, 70, 71, 70, 70, 71, 72, 73, 72, 73, 72, 73, 72, 56, 51, 53, 52, 58, 67, 66, 65, 69, 72, 73, 73, 72, 72, 70, 71, 33, 32, 33, 32, 33, 32, 33, 32, 72, 73, 72, 73, 72, 73, 72, 72, 73, 70, 71, 71, 72, 73, 70, 71, 70, 71, 71, 55, 74, 75, 75, 74, 49, 70, 71, 70, 70, 71, 72, 70, 71, 33, 32, 33, 32, 33, 32, 33, 70, 71, 72, 73, 73, 70, 71, 70, 71, 72, 73, 73, 72, 73, 72, 73, 72, 73, 73, 51, 53, 52, 46, 47, 57, 62, 63, 69, 72, 73, 72, 72, 73, 70, 71, 32, 33, 32, 33, 32, 70, 71, 70, 71, 70, 72, 73, 72, 73, 71, 70, 71, 70, 71, 72, 73, 71, 70, 72, 73, 71, 70, 71, 70, 71, 63, 60, 58, 65, 69, 70, 71, 72, 72, 73, 71, 71, 70, 71, 70, 70, 71, 72, 73, 72, 73, 72, 73, 72, 73, 70, 71, 70, 71, 70, 71, 73, 72, 73, 72, 73, 72, 73, 72, 73, 67, 74, 74, 75, 61, 72, 73, 70, 71, 72, 73, 73, 72, 73, 72, 72, 70, 71, 70, 71, 70, 70, 71, 70, 71, 72, 73, 72, 73, 70, 71, 70, 71, 71, 70, 70, 71, 70, 71, 70, 51, 52, 74, 75, 49, 70, 71, 70, 71, 72, 73, 72, 73, 70, 71, 70, 72, 73, 72, 73, 72, 72, 73, 72, 73, 70, 71, 70, 71, 72, 73, 72, 73, 73, 72, 72, 73, 72, 73, 72, 73, 51, 53, 53, 57, 72, 73, 72, 73, 71, 70, 71, 70, 71, 73, 72, 73, 70, 71, 71, 70, 70, 71, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 70, 71, 73, 72, 73, 72, 73, 72, 73, 72, 73, 70, 71, 72, 73, 72, 73, 71, 70, 71, 72, 73, 73, 72, 72, 73, 70, 71, 71, 70, 71, 70, 71, 70, 71, 70, 71, 70, 71, 71, 72, 73, 71, 70, 71, 62, 63, 65, 69, 68, 68, 72, 73, 70, 71, 70, 71, 73, 72, 73, 70, 71, 71, 70, 71, 70, 72, 73, 73, 72, 73, 72, 73, 72, 73, 72, 70, 71, 73, 73, 70, 71, 73, 72, 73, 63, 60, 74, 66, 65, 69, 71, 70, 72, 73, 72, 73, 71, 70, 70, 71, 70, 71, 72, 73, 72, 73, 70, 70, 71, 70, 71, 70, 71, 70, 72, 72, 73, 70, 71, 72, 73, 71, 70, 71, 67, 75, 76, 77, 74, 61, 73, 72, 73, 70, 71, 72, 73, 72, 72, 73, 72, 73, 72, 73, 72, 70, 71, 72, 73, 71, 70, 72, 73, 71, 70, 71, 70, 72, 73, 71, 72, 73, 63, 59, 60, 77, 74, 75, 76, 61, 62, 71, 70, 72, 73, 71, 71, 70, 71, 70, 71, 70, 71, 70, 71, 72, 73, 71, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 72, 68, 67, 76, 77, 54, 53, 52, 75, 66, 65, 65, 69, 68, 72, 73, 73, 70, 71, 70, 71, 70, 70, 71, 70, 71, 70, 71, 70, 71, 70, 71, 70, 71, 72, 73, 72, 70, 71, 73, 70, 63, 64, 75, 46, 57, 56, 51, 48, 76, 74, 74, 66, 69, 70, 71, 71, 72, 73, 72, 73, 72, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 72, 73, 72, 72, 73, 70, 72, 67, 76, 77, 61, 62, 62, 56, 51, 53, 52, 76, 77, 61, 72, 73, 73]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}