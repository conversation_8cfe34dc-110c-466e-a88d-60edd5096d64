{"mW": 1128, "mH": 960, "tW": 24, "tH": 24, "tiles": [["302_5", 0, 2, 2], ["1246", 0, 3, 2], ["1246", 2, 3, 2], ["1246", 1, 3, 2], ["1246", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 0, 4, 2]], "layers": [{"type": 3, "obj": [[2, "1254", 604, 674, 54, 32, 0], [2, "1254", 644, 693, 54, 32, 0], [2, "1244", -23, 11, 30, 67, 2], [2, "1141_1", -7, 31, 54, 67, 2], [2, "1254", -7, 24, 54, 32, 0], [2, "1244", 36, 41, 30, 67, 2], [2, "1141_1", 52, 61, 54, 67, 2], [2, "1254", 53, 53, 54, 32, 0], [2, "1244", 94, 68, 30, 67, 2], [2, "1141_1", 110, 88, 54, 67, 2], [2, "1254", 110, 81, 54, 32, 0], [2, "1244", 153, 98, 30, 67, 2], [2, "1254", 170, 110, 54, 32, 0], [2, "1141_1", 169, 118, 54, 67, 2], [2, "1244", 197, 120, 30, 67, 2], [2, "1253", 146, 121, 22, 35, 2], [2, "1253", 31, 67, 22, 35, 2], [2, "3246", 26, 14, 28, 70, 0], [2, "3246", 141, 66, 28, 70, 0], [2, "1244", 917, 467, 30, 67, 2], [2, "1141_1", 933, 487, 54, 67, 2], [2, "1254", 933, 480, 54, 32, 0], [2, "1244", 976, 497, 30, 67, 2], [2, "1141_1", 992, 517, 54, 67, 2], [2, "1254", 993, 509, 54, 32, 0], [2, "1244", 1034, 524, 30, 67, 2], [2, "1141_1", 1050, 544, 54, 67, 2], [2, "1254", 1050, 537, 54, 32, 0], [2, "1244", 1093, 554, 30, 67, 2], [2, "1254", 1110, 566, 54, 32, 0], [2, "1141_1", 1109, 574, 54, 67, 2], [2, "1253", 1086, 577, 22, 35, 2], [2, "1253", 971, 523, 22, 35, 2], [2, "3246", 966, 470, 28, 70, 0], [2, "3246", 1081, 522, 28, 70, 0], [2, "3242", 796, 202, 32, 34, 0], [2, "3242", 698, 147, 32, 34, 0], [2, "3242", 1015, 556, 32, 34, 0], [2, "1254", 531, 713, 54, 32, 0], [2, "1254", 571, 732, 54, 32, 0], [2, "1254", 647, 711, 54, 32, 2], [2, "1254", 606, 731, 54, 32, 2], [2, "1254", 573, 677, 54, 32, 2], [2, "1254", 532, 697, 54, 32, 2], [2, "10_1", 558, 707, 50, 26, 2], [2, "10_1", 584, 694, 50, 26, 2], [2, "10_1", 593, 689, 50, 26, 2], [2, "10_1", 620, 701, 50, 26, 2], [2, "10_1", 596, 713, 50, 26, 2], [2, "10_1", 583, 720, 50, 26, 2], [2, "10_1", 591, 723, 50, 26, 2], [2, "10_1", 617, 711, 50, 26, 2], [2, "10_1", 629, 704, 50, 26, 2], [2, "980", 999, 415, 58, 41, 0], [2, "980", 1083, 178, 58, 41, 0], [2, "1244", 917, 897, 30, 67, 2], [2, "1141_1", 876, 920, 54, 67, 0], [2, "1254", 877, 909, 54, 32, 2], [2, "3246", 555, 704, 28, 70, 0]]}, {"type": 4, "obj": [[2, "3236", 442, 18, 90, 61, 0], [2, "3237", 502, 42, 44, 43, 0], [2, "3247", 910, 2, 48, 97, 0], [2, "3246", 616, 75, 28, 70, 0], [2, "3246", 1007, 197, 28, 70, 0], [2, "3246", 905, 213, 28, 70, 0], [2, "3233", 1037, 184, 76, 130, 0], [2, "1254", 979, 413, 54, 32, 2], [2, "1244", 1024, 400, 30, 67, 2], [2, "1254", 919, 439, 54, 32, 2], [2, "1141_1", 978, 421, 54, 67, 0], [2, "1244", 958, 430, 30, 67, 2], [2, "3236", 992, 437, 90, 61, 0], [2, "1141_1", 918, 447, 54, 67, 0], [2, "3237", 971, 473, 44, 43, 0], [2, "1244", 898, 456, 30, 67, 2], [2, "1141_1", 646, 717, 54, 67, 0], [2, "1141_1", 531, 722, 54, 67, 2], [2, "1253", 560, 761, 22, 35, 2], [2, "1141_1", 573, 740, 54, 67, 2], [2, "1141_1", 605, 740, 54, 67, 0], [2, "980", 260, 802, 58, 41, 0], [2, "3233", 623, 722, 76, 130, 0], [2, "1508", 224, 842, 112, 80, 2], [2, "980", 1049, 885, 58, 41, 0], [2, "3233", 942, 817, 76, 130, 0], [2, "1508", 139, 878, 112, 80, 2]]}, {"type": 3, "obj": [[2, "930_1", 513, 791, 42, 22, 2], [2, "930_1", 511, 806, 42, 22, 0], [2, "930_1", 546, 823, 42, 22, 0], [2, "930_1", 153, 889, 42, 22, 2], [2, "930_1", 117, 907, 42, 22, 2], [2, "930_1", 85, 924, 42, 22, 2], [2, "930_1", 53, 941, 42, 22, 2], [2, "930_1", 108, 715, 42, 22, 0], [2, "930_1", 543, 574, 42, 22, 0], [2, "930_1", 577, 590, 42, 22, 0], [2, "930_1", 31, 335, 42, 22, 0], [2, "1141_1", 1082, 879, 54, 67, 2], [2, "1141_1", 1038, 878, 54, 67, 0], [2, "1141_1", 1012, 888, 54, 67, 0], [2, "1141_1", 1051, 831, 54, 67, 0], [2, "251_2", 1063, 941, 68, 57, 2], [2, "1141_1", 1012, 850, 54, 67, 0], [2, "1244", 732, 724, 30, 67, 2], [2, "1141_1", 689, 746, 54, 67, 0], [2, "1141_1", 646, 761, 54, 67, 0], [2, "3242", 584, 453, 32, 34, 0], [2, "930_1", 793, 298, 42, 22, 2], [2, "930_1", 829, 280, 42, 22, 2], [2, "930_1", 630, 246, 42, 22, 1], [2, "930_1", 667, 229, 42, 22, 2], [2, "930_1", 703, 211, 42, 22, 2], [2, "1254", 655, 240, 54, 32, 2], [2, "1254", 772, 288, 54, 32, 2], [2, "1254", 816, 266, 54, 32, 2], [2, "1254", 856, 245, 54, 32, 2], [2, "1254", 645, 353, 54, 32, 2], [2, "1254", 689, 331, 54, 32, 2], [2, "1254", 729, 310, 54, 32, 2], [2, "1254", 614, 260, 54, 32, 2], [2, "1254", 574, 281, 54, 32, 2], [2, "1254", 530, 303, 54, 32, 2], [2, "1508", 279, -20, 112, 80, 2], [2, "1141_1", 358, 361, 54, 67, 0], [2, "1141_1", 317, 380, 54, 67, 0], [2, "1141_1", 274, 402, 54, 67, 0], [2, "1141_1", 231, 424, 54, 67, 0], [2, "1141_1", 189, 444, 54, 67, 0], [2, "1141_1", 146, 466, 54, 67, 0], [2, "1254", -33, 144, 54, 32, 0], [2, "930_1", 726, 307, 42, 22, 2], [2, "930_1", 690, 325, 42, 22, 2], [2, "930_1", 658, 342, 42, 22, 2], [2, "930_1", 626, 359, 42, 22, 2], [2, "930_1", 645, 264, 42, 22, 2], [2, "930_1", 604, 168, 42, 22, 2], [2, "930_1", 608, 185, 42, 22, 0], [2, "930_1", 667, 213, 42, 22, 0], [2, "1254", 796, -15, 54, 32, 2], [2, "1254", 752, 7, 54, 32, 2], [2, "1254", 710, 28, 54, 32, 2], [2, "1254", 666, 50, 54, 32, 2], [2, "1254", 622, 71, 54, 32, 2], [2, "1141_1", 1073, 216, 54, 67, 0], [2, "1141_1", 1074, 173, 54, 67, 0], [2, "1141_1", 1032, 237, 54, 67, 0], [2, "1141_1", 1033, 194, 54, 67, 0], [2, "1141_1", 989, 216, 54, 67, 0], [2, "1141_1", 618, 98, 54, 67, 2], [2, "1141_1", 617, 141, 54, 67, 2], [2, "1141_1", 657, 161, 54, 67, 2], [2, "1141_1", 658, 118, 54, 67, 2], [2, "1141_1", 701, 139, 54, 67, 2], [2, "1141_1", 700, 182, 54, 67, 2], [2, "1141_2", 782, 236, 54, 67, 0], [2, "1141_2", 774, 239, 54, 67, 0], [2, "1141_2", 745, 241, 54, 67, 0], [2, "1241", 722, 161, 38, 50, 2], [2, "3248", 518, 575, 110, 63, 0], [2, "1254", 942, -11, 54, 32, 0], [2, "1244", -23, 57, 30, 67, 2], [2, "1141_1", -7, 77, 54, 67, 2], [2, "1254", -7, 70, 54, 32, 0], [2, "1241", 696, 205, 38, 50, 2], [2, "1255", 756, 287, 26, 27, 2], [2, "1240", 750, 168, 84, 50, 2], [2, "11_4", 320, 58, 32, 29, 0], [2, "3249", 467, 567, 110, 63, 0], [2, "1253", 622, 129, 22, 35, 2], [2, "3232", 794, 46, 258, 118, 0], [2, "1255", 680, 249, 26, 27, 2], [2, "1240", 740, 182, 84, 50, 2], [2, "1240", 718, 209, 84, 50, 2], [2, "1240", 728, 195, 84, 50, 2], [2, "1240", 697, 235, 84, 50, 2], [2, "1240", 707, 221, 84, 50, 2], [2, "1240", 684, 248, 84, 50, 2], [2, "1240", 674, 262, 84, 50, 2], [2, "1241", 798, 200, 38, 50, 2], [2, "1241", 772, 243, 38, 50, 2], [2, "1254", 987, 11, 54, 32, 0], [2, "1254", 1031, 33, 54, 32, 0], [2, "1254", 1077, 55, 54, 32, 0], [2, "1254", 1122, 77, 54, 32, 0], [2, "1141_1", 830, 241, 54, 67, 2], [2, "1141_1", 831, 198, 54, 67, 2], [2, "1141_1", 871, 218, 54, 67, 2], [2, "1141_1", 870, 261, 54, 67, 2], [2, "1141_1", 914, 238, 54, 67, 2], [2, "1141_1", 913, 281, 54, 67, 2], [2, "1254", 619, 89, 54, 32, 0], [2, "1254", 664, 112, 54, 32, 0], [2, "1254", 709, 133, 54, 32, 0], [2, "1254", 754, 155, 54, 32, 0], [2, "1254", 799, 178, 54, 32, 0], [2, "1254", 844, 199, 54, 32, 0], [2, "1254", 888, 221, 54, 32, 0], [2, "1254", 1082, 160, 54, 32, 2], [2, "3230", 625, 118, 78, 135, 0], [2, "3230", 810, 206, 78, 135, 0], [2, "1253", 910, 268, 22, 35, 2], [2, "1244", 36, 85, 30, 67, 2], [2, "1141_1", 52, 105, 54, 67, 2], [2, "1254", 52, 98, 54, 32, 0], [2, "1244", 95, 113, 30, 67, 2], [2, "1141_1", 111, 133, 54, 67, 2], [2, "1254", 111, 126, 54, 32, 0], [2, "1244", 154, 141, 30, 67, 2], [2, "1141_1", 170, 161, 54, 67, 2], [2, "1254", 170, 154, 54, 32, 0], [2, "1244", 211, 170, 30, 67, 2], [2, "1141_1", 227, 190, 54, 67, 2], [2, "1254", 227, 183, 54, 32, 0], [2, "1244", 270, 198, 30, 67, 2], [2, "1141_1", 286, 218, 54, 67, 2], [2, "1254", 286, 211, 54, 32, 0], [2, "1244", 329, 226, 30, 67, 2], [2, "1141_1", 345, 246, 54, 67, 2], [2, "1254", 345, 239, 54, 32, 0], [2, "1244", 388, 254, 30, 67, 2], [2, "1141_1", 404, 274, 54, 67, 2], [2, "1254", 404, 267, 54, 32, 0], [2, "1244", 447, 284, 30, 67, 2], [2, "1141_1", 463, 304, 54, 67, 2], [2, "1254", 463, 297, 54, 32, 0], [2, "1244", 505, 314, 30, 67, 2], [2, "1141_1", 521, 334, 54, 67, 2], [2, "1254", 521, 327, 54, 32, 0], [2, "1244", 564, 342, 30, 67, 2], [2, "1141_1", 580, 362, 54, 67, 2], [2, "1254", 580, 355, 54, 32, 0], [2, "1244", 623, 370, 30, 67, 2], [2, "1141_1", 639, 390, 54, 67, 2], [2, "1254", 639, 383, 54, 32, 0], [2, "1244", 682, 398, 30, 67, 2], [2, "1141_1", 698, 418, 54, 67, 2], [2, "1254", 698, 411, 54, 32, 0], [2, "1244", 739, 427, 30, 67, 2], [2, "1141_1", 755, 447, 54, 67, 2], [2, "1254", 755, 440, 54, 32, 0], [2, "1244", 798, 455, 30, 67, 2], [2, "1141_1", 814, 475, 54, 67, 2], [2, "1254", 814, 468, 54, 32, 0], [2, "1244", 857, 483, 30, 67, 2], [2, "1141_1", 873, 503, 54, 67, 2], [2, "1254", 873, 496, 54, 32, 0], [2, "1244", 916, 511, 30, 67, 2], [2, "1141_1", 932, 531, 54, 67, 2], [2, "1254", 932, 524, 54, 32, 0], [2, "1141_1", 991, 560, 54, 67, 2], [2, "1254", 991, 553, 54, 32, 0], [2, "1244", 1034, 568, 30, 67, 2], [2, "1141_1", 1050, 588, 54, 67, 2], [2, "1254", 1050, 581, 54, 32, 0], [2, "1244", 1093, 596, 30, 67, 2], [2, "1141_1", 1109, 616, 54, 67, 2], [2, "1254", 1109, 609, 54, 32, 0], [2, "1244", 975, 540, 30, 67, 2], [2, "1141_2", 1050, 214, 54, 67, 2], [2, "1141_2", 1054, 192, 54, 67, 2], [2, "1254", 1038, 182, 54, 32, 2], [2, "1254", 996, 203, 54, 32, 2], [2, "1254", 952, 226, 54, 32, 2], [2, "1254", 908, 229, 54, 32, 0], [2, "43_9", 1025, 247, 82, 58, 2], [2, "1141_1", 988, 259, 54, 67, 0], [2, "1141_1", 945, 281, 54, 67, 0], [2, "1141_1", 946, 238, 54, 67, 0], [2, "1253", 1009, 253, 22, 35, 0], [2, "1253", 1121, 201, 22, 35, 0], [2, "1254", 546, -11, 54, 32, 2], [2, "1254", 502, 11, 54, 32, 2], [2, "1254", 460, 32, 54, 32, 2], [2, "1254", 416, 54, 54, 32, 2], [2, "1254", 372, 75, 54, 32, 2], [2, "1254", 329, 96, 54, 32, 2], [2, "1254", 285, 118, 54, 32, 2], [2, "1254", 243, 139, 54, 32, 2], [2, "1254", 213, 153, 54, 32, 2], [2, "1254", 1016, 309, 54, 32, 0], [2, "1254", 1059, 330, 54, 32, 0], [2, "1254", 1103, 351, 54, 32, 0], [2, "1254", 1114, 262, 54, 32, 0], [2, "930_1", 1007, 319, 42, 22, 0], [2, "930_1", 1041, 336, 42, 22, 0], [2, "930_1", 1076, 353, 42, 22, 0], [2, "930_1", 1110, 369, 42, 22, 0], [2, "930_1", 1037, 296, 42, 22, 2], [2, "930_1", 1073, 278, 42, 22, 2], [2, "930_1", 1087, 271, 42, 22, 2], [2, "930_1", 974, 322, 42, 22, 2], [2, "930_1", 957, 330, 42, 22, 2], [2, "930_1", 919, 333, 42, 22, 0], [2, "930_1", 885, 316, 42, 22, 0], [2, "930_1", 609, 282, 42, 22, 2], [2, "930_1", 577, 299, 42, 22, 2], [2, "930_1", 545, 316, 42, 22, 2], [2, "3244", 510, 352, 46, 24, 0], [2, "3245", 546, 370, 46, 24, 0], [2, "3244", 519, 388, 46, 24, 0], [2, "3245", 540, 409, 46, 24, 0], [2, "3245", 479, 372, 46, 24, 0], [2, "3245", 481, 403, 46, 24, 0], [2, "3244", 444, 388, 46, 24, 0], [2, "3244", 448, 422, 46, 24, 0], [2, "3244", 504, 428, 46, 24, 0], [2, "3244", 576, 388, 46, 24, 0], [2, "3245", 465, 447, 46, 24, 0], [2, "3245", 411, 408, 46, 24, 0], [2, "3244", 408, 442, 46, 24, 0], [2, "3244", 430, 464, 46, 24, 0], [2, "3245", 397, 482, 46, 24, 0], [2, "3245", 375, 462, 46, 24, 0], [2, "3244", 377, 426, 46, 24, 0], [2, "3244", 339, 483, 46, 24, 0], [2, "3245", 344, 447, 46, 24, 0], [2, "3244", 365, 502, 46, 24, 0], [2, "3245", 333, 521, 46, 24, 0], [2, "3245", 306, 504, 46, 24, 0], [2, "3244", 305, 469, 46, 24, 0], [2, "3245", 270, 488, 46, 24, 0], [2, "3244", 271, 523, 46, 24, 0], [2, "3244", 300, 541, 46, 24, 0], [2, "3244", 238, 508, 46, 24, 0], [2, "1254", 4, 163, 54, 32, 0], [2, "1254", 47, 184, 54, 32, 0], [2, "1254", 91, 205, 54, 32, 0], [2, "1254", 134, 226, 54, 32, 0], [2, "1254", 177, 247, 54, 32, 0], [2, "1254", 221, 268, 54, 32, 0], [2, "1254", 264, 289, 54, 32, 0], [2, "1254", 307, 310, 54, 32, 0], [2, "1254", 351, 331, 54, 32, 0], [2, "1254", 612, 464, 54, 32, 0], [2, "1254", 655, 485, 54, 32, 0], [2, "1254", 699, 506, 54, 32, 0], [2, "1254", 742, 527, 54, 32, 0], [2, "1254", 785, 548, 54, 32, 0], [2, "1254", 829, 569, 54, 32, 0], [2, "1254", 872, 590, 54, 32, 0], [2, "1254", 915, 611, 54, 32, 0], [2, "1254", 959, 632, 54, 32, 0], [2, "1254", 1002, 653, 54, 32, 0], [2, "1254", 1045, 674, 54, 32, 0], [2, "1254", 1089, 695, 54, 32, 0], [2, "1254", 1305, 800, 54, 32, 0], [2, "1254", 1349, 821, 54, 32, 0], [2, "1254", 362, 350, 54, 32, 2], [2, "1254", 320, 371, 54, 32, 2], [2, "1254", 276, 393, 54, 32, 2], [2, "1254", 233, 414, 54, 32, 2], [2, "1254", 191, 435, 54, 32, 2], [2, "1254", 147, 457, 54, 32, 2], [2, "930_1", 345, 353, 42, 22, 2], [2, "930_1", 309, 371, 42, 22, 2], [2, "930_1", 277, 388, 42, 22, 2], [2, "930_1", 245, 403, 42, 22, 2], [2, "930_1", 208, 422, 42, 22, 2], [2, "930_1", 172, 440, 42, 22, 2], [2, "930_1", 140, 457, 42, 22, 2], [2, "1254", 571, 469, 54, 32, 2], [2, "1254", 529, 490, 54, 32, 2], [2, "1254", 485, 512, 54, 32, 2], [2, "1254", 441, 533, 54, 32, 2], [2, "1254", 398, 554, 54, 32, 2], [2, "1254", 361, 571, 54, 32, 2], [2, "1254", 136, 479, 54, 32, 0], [2, "1254", 179, 500, 54, 32, 0], [2, "1254", 223, 521, 54, 32, 0], [2, "1254", 266, 542, 54, 32, 0], [2, "1254", 309, 562, 54, 32, 0], [2, "1254", 323, 569, 54, 32, 0], [2, "1244", 533, -57, 30, 67, 2], [2, "1254", 492, -43, 54, 32, 2], [2, "1141_1", 491, -35, 54, 67, 0], [2, "1244", 474, -27, 30, 67, 2], [2, "1254", 433, -13, 54, 32, 2], [2, "1141_1", 432, -5, 54, 67, 0], [2, "1244", 413, 4, 30, 67, 2], [2, "1254", 372, 18, 54, 32, 2], [2, "1141_1", 371, 26, 54, 67, 0], [2, "1244", 351, 35, 30, 67, 2], [2, "1508", 77, 7, 112, 80, 0], [2, "1508", 3, -26, 112, 80, 0], [2, "3236", 189, -19, 90, 61, 0], [2, "3237", 259, 7, 44, 43, 0], [2, "930_1", 332, 110, 42, 22, 2], [2, "930_1", 296, 128, 42, 22, 2], [2, "930_1", 264, 145, 42, 22, 2], [2, "930_1", 232, 162, 42, 22, 2], [2, "930_1", 467, 41, 42, 22, 2], [2, "930_1", 431, 59, 42, 22, 2], [2, "930_1", 399, 76, 42, 22, 2], [2, "930_1", 367, 93, 42, 22, 2], [2, "930_1", 566, -9, 42, 22, 2], [2, "930_1", 534, 8, 42, 22, 2], [2, "930_1", 502, 25, 42, 22, 2], [2, "930_1", 241, 179, 42, 22, 0], [2, "930_1", 275, 196, 42, 22, 0], [2, "930_1", 310, 213, 42, 22, 0], [2, "930_1", 344, 229, 42, 22, 0], [2, "930_1", 381, 247, 42, 22, 0], [2, "930_1", 415, 264, 42, 22, 0], [2, "930_1", 450, 281, 42, 22, 0], [2, "930_1", 484, 297, 42, 22, 0], [2, "930_1", 496, 302, 42, 22, 0], [2, "930_1", 531, 299, 42, 22, 2], [2, "930_1", 563, 282, 42, 22, 2], [2, "930_1", 595, 265, 42, 22, 2], [2, "3243", 61, 151, 32, 34, 2], [2, "3242", 138, 163, 32, 34, 0], [2, "3242", 343, 258, 32, 34, 0], [2, "3242", 432, 499, 32, 34, 0], [2, "3243", 225, 461, 32, 34, 2], [2, "3243", 369, 293, 32, 34, 2], [2, "3243", 641, 422, 32, 34, 2], [2, "3242", 828, 514, 32, 34, 0], [2, "930_1", 591, 477, 42, 22, 2], [2, "930_1", 555, 495, 42, 22, 2], [2, "930_1", 523, 512, 42, 22, 2], [2, "930_1", 491, 527, 42, 22, 2], [2, "930_1", 454, 546, 42, 22, 2], [2, "930_1", 418, 564, 42, 22, 2], [2, "930_1", 386, 580, 42, 22, 2], [2, "930_1", 116, 484, 42, 22, 0], [2, "930_1", 150, 501, 42, 22, 0], [2, "930_1", 185, 518, 42, 22, 0], [2, "930_1", 219, 534, 42, 22, 0], [2, "930_1", 249, 548, 42, 22, 0], [2, "930_1", 283, 564, 42, 22, 0], [2, "930_1", 318, 582, 42, 22, 0], [2, "930_1", 331, 589, 42, 22, 0], [2, "930_1", 363, 592, 42, 22, 2], [2, "930_1", 118, 467, 42, 22, 2], [2, "930_1", 231, 286, 42, 22, 0], [2, "930_1", 265, 303, 42, 22, 0], [2, "930_1", 300, 320, 42, 22, 0], [2, "930_1", 334, 336, 42, 22, 0], [2, "930_1", 96, 219, 42, 22, 0], [2, "930_1", 130, 236, 42, 22, 0], [2, "930_1", 165, 253, 42, 22, 0], [2, "930_1", 199, 269, 42, 22, 0], [2, "930_1", -11, 169, 42, 22, 0], [2, "930_1", 24, 186, 42, 22, 0], [2, "930_1", 58, 202, 42, 22, 0], [2, "930_1", 624, 482, 42, 22, 0], [2, "930_1", 658, 499, 42, 22, 0], [2, "930_1", 727, 532, 42, 22, 0], [2, "930_1", 693, 516, 42, 22, 0], [2, "930_1", 762, 549, 42, 22, 0], [2, "930_1", 796, 566, 42, 22, 0], [2, "930_1", 865, 599, 42, 22, 0], [2, "930_1", 831, 583, 42, 22, 0], [2, "930_1", 899, 616, 42, 22, 0], [2, "930_1", 933, 633, 42, 22, 0], [2, "930_1", 1002, 666, 42, 22, 0], [2, "930_1", 968, 650, 42, 22, 0], [2, "930_1", 899, 616, 42, 22, 0], [2, "930_1", 933, 633, 42, 22, 0], [2, "930_1", 1002, 666, 42, 22, 0], [2, "930_1", 968, 650, 42, 22, 0], [2, "930_1", 1038, 682, 42, 22, 0], [2, "930_1", 1072, 699, 42, 22, 0], [2, "930_1", 1141, 732, 42, 22, 0], [2, "930_1", 1107, 716, 42, 22, 0], [2, "930_1", 757, 315, 42, 22, 2], [2, "930_1", 721, 333, 42, 22, 2], [2, "930_1", 689, 350, 42, 22, 2], [2, "930_1", 657, 367, 42, 22, 2], [2, "1141_1", 490, -7, 54, 67, 2], [2, "1254", 492, -12, 54, 32, 0], [2, "1244", 533, 1, 30, 67, 2], [2, "11_4", 407, 61, 32, 29, 2], [2, "11_4", 874, 307, 32, 29, 2], [2, "3243", 969, 596, 32, 34, 2], [2, "3243", 1069, 621, 32, 34, 2], [2, "1141_1", 530, 766, 54, 67, 2], [2, "1141_1", 572, 785, 54, 67, 2], [2, "1141_1", 605, 783, 54, 67, 0], [2, "1141_2", 641, 757, 54, 67, 2], [2, "43_9", 621, 782, 82, 58, 2], [2, "1254", 692, 740, 54, 32, 2], [2, "251_2", 1023, 900, 68, 57, 2], [2, "1141_1", 1084, 839, 54, 67, 2], [2, "1240", 949, 913, 84, 50, 0], [2, "1240", 960, 925, 84, 50, 0], [2, "1240", 970, 937, 84, 50, 0], [2, "1240", 983, 946, 84, 50, 0], [2, "1254", 1085, 829, 54, 32, 0], [2, "1254", 1045, 825, 54, 32, 2], [2, "1254", 1014, 841, 54, 32, 2], [2, "1254", 865, 679, 54, 32, 0], [2, "1244", 849, 666, 30, 67, 2], [2, "1254", 926, 710, 54, 32, 0], [2, "1141_1", 865, 686, 54, 67, 2], [2, "1244", 910, 697, 30, 67, 2], [2, "1141_1", 926, 717, 54, 67, 2], [2, "1254", 517, 504, 54, 32, 0], [2, "1244", 501, 491, 30, 67, 2], [2, "1254", 578, 535, 54, 32, 0], [2, "1141_1", 517, 511, 54, 67, 2], [2, "1244", 562, 522, 30, 67, 2], [2, "1141_1", 578, 542, 54, 67, 2], [2, "1254", -2, 261, 54, 32, 0], [2, "1244", -18, 248, 30, 67, 2], [2, "1254", 59, 292, 54, 32, 0], [2, "1141_1", -2, 268, 54, 67, 2], [2, "1244", 43, 279, 30, 67, 2], [2, "1141_1", 59, 299, 54, 67, 2], [2, "1254", 0, 624, 54, 32, 0], [2, "1244", -16, 611, 30, 67, 2], [2, "1254", 61, 655, 54, 32, 0], [2, "1141_1", 0, 631, 54, 67, 2], [2, "1244", 45, 642, 30, 67, 2], [2, "1141_1", 61, 662, 54, 67, 2], [2, "1244", 100, 675, 30, 67, 2], [2, "1141_1", 57, 697, 54, 67, 0], [2, "1254", 60, 691, 54, 32, 2], [2, "1244", 37, 710, 30, 67, 2], [2, "1141_1", -6, 732, 54, 67, 0], [2, "1254", -3, 726, 54, 32, 2], [2, "1254", 281, 791, 54, 32, 0], [2, "1244", 265, 778, 30, 67, 2], [2, "1141_1", 281, 798, 54, 67, 2], [2, "1244", 326, 809, 30, 67, 2], [2, "1141_1", 223, 799, 54, 67, 0], [2, "1254", 226, 793, 54, 32, 2], [2, "1244", 203, 810, 30, 67, 2], [2, "1254", 164, 825, 54, 32, 2], [2, "1141_1", 161, 831, 54, 67, 0], [2, "1244", 143, 839, 30, 67, 2], [2, "1254", 104, 854, 54, 32, 2], [2, "1141_1", 101, 860, 54, 67, 0], [2, "1244", 82, 870, 30, 67, 2], [2, "1254", 43, 885, 54, 32, 2], [2, "1141_1", 40, 891, 54, 67, 0], [2, "1244", 21, 899, 30, 67, 2], [2, "1254", -18, 914, 54, 32, 2], [2, "1141_1", -21, 920, 54, 67, 0], [2, "11_4", 258, 832, 32, 29, 2], [2, "11_4", 73, 923, 32, 29, 2], [2, "3236", 320, 839, 90, 61, 0], [2, "3237", 543, 807, 44, 43, 0], [2, "1244", 967, 725, 30, 67, 2], [2, "3236", 445, 529, 90, 61, 0], [2, "3237", 529, 552, 44, 43, 0], [2, "3236", -32, 310, 90, 61, 0], [2, "3237", 135, 497, 44, 43, 0], [2, "3236", 884, 734, 90, 61, 2], [2, "3237", 851, 718, 44, 43, 0], [2, "930_1", 665, 386, 42, 22, 0], [2, "930_1", 699, 403, 42, 22, 0], [2, "930_1", 734, 420, 42, 22, 0], [2, "930_1", 768, 436, 42, 22, 0], [2, "930_1", 803, 454, 42, 22, 0], [2, "930_1", 837, 471, 42, 22, 0], [2, "930_1", 871, 489, 42, 22, 0], [2, "930_1", 60, 349, 42, 22, 0], [2, "930_1", 645, 92, 42, 22, 0], [2, "930_1", 679, 109, 42, 22, 0], [2, "930_1", 714, 126, 42, 22, 0], [2, "930_1", 748, 142, 42, 22, 0], [2, "930_1", 784, 159, 42, 22, 0], [2, "930_1", 818, 176, 42, 22, 0], [2, "930_1", 853, 193, 42, 22, 0], [2, "930_1", 887, 209, 42, 22, 0], [2, "930_1", 918, 224, 42, 22, 0], [2, "930_1", 658, 74, 42, 22, 2], [2, "930_1", 690, 57, 42, 22, 2], [2, "930_1", 722, 40, 42, 22, 2], [2, "930_1", 758, 22, 42, 22, 2], [2, "930_1", 954, 220, 42, 22, 2], [2, "930_1", 986, 203, 42, 22, 2], [2, "930_1", 1018, 186, 42, 22, 2], [2, "930_1", 1054, 168, 42, 22, 2], [2, "930_1", 1125, 132, 42, 22, 2], [2, "930_1", 1089, 150, 42, 22, 2], [2, "930_1", 793, 6, 42, 22, 2], [2, "930_1", 826, -11, 42, 22, 2], [2, "930_1", 908, -12, 42, 22, 0], [2, "930_1", 943, 5, 42, 22, 0], [2, "930_1", 977, 21, 42, 22, 0], [2, "930_1", 1013, 38, 42, 22, 0], [2, "930_1", 1047, 55, 42, 22, 0], [2, "930_1", 1082, 72, 42, 22, 0], [2, "930_1", 1116, 88, 42, 22, 0], [2, "930_1", 98, 732, 42, 22, 2], [2, "930_1", 62, 750, 42, 22, 2], [2, "930_1", 30, 767, 42, 22, 2], [2, "930_1", -2, 784, 42, 22, 2], [2, "930_1", 189, 873, 42, 22, 2], [2, "930_1", 221, 856, 42, 22, 2], [2, "930_1", 580, 839, 42, 22, 0], [2, "930_1", 618, 835, 42, 22, 2], [2, "930_1", 654, 817, 42, 22, 2], [2, "930_1", 691, 799, 42, 22, 2], [2, "930_1", 727, 781, 42, 22, 2], [2, "3249", 237, 545, 110, 63, 0], [2, "3249", 215, 532, 110, 63, 0], [2, "3249", 186, 516, 110, 63, 0], [2, "3249", 508, 306, 110, 63, 0], [2, "3249", 571, 337, 110, 63, 0], [2, "3248", 539, 318, 110, 63, 0], [2, "3248", 66, 725, 110, 63, 0], [2, "3248", 18, 759, 110, 63, 0], [2, "3248", -33, 786, 110, 63, 0], [2, "3248", 533, 825, 110, 63, 0], [2, "3248", 758, 830, 110, 63, 0], [2, "3249", 749, 281, 110, 63, 0], [2, "3249", 611, 227, 110, 63, 0], [2, "3249", 549, 177, 110, 63, 0], [2, "3249", 851, 312, 110, 63, 0], [2, "3249", 872, 69, 110, 63, 0], [2, "3249", 873, 79, 110, 63, 0], [2, "3249", 873, 79, 110, 63, 0], [2, "3249", 873, 79, 110, 63, 0], [2, "3249", 979, 296, 110, 63, 0], [2, "3248", 1035, 273, 110, 63, 0], [2, "3248", 624, 810, 110, 63, 0], [2, "3248", 1043, 692, 110, 63, 0], [2, "3249", 928, 922, 110, 63, 0], [2, "3249", 956, 909, 110, 63, 0], [2, "3249", 940, 918, 110, 63, 0], [2, "3249", 940, 918, 110, 63, 0], [2, "3249", 358, 856, 110, 63, 0], [2, "3248", 337, 76, 110, 63, 0], [2, "3248", 181, 26, 110, 63, 0], [2, "3248", -4, 184, 110, 63, 0], [2, "3248", 14, 343, 110, 63, 0], [2, "3248", 714, 543, 110, 63, 0], [2, "3248", 993, 461, 110, 63, 0], [2, "3249", 346, 428, 110, 63, 0], [2, "3249", 329, 465, 110, 63, 0], [2, "3249", 439, 404, 110, 63, 0], [2, "3249", 465, 356, 110, 63, 0], [2, "3249", 271, 471, 110, 63, 0], [2, "3248", 267, 506, 110, 63, 0], [2, "3248", 374, 440, 110, 63, 0], [2, "3248", 412, 387, 110, 63, 0], [2, "3248", 476, 390, 110, 63, 0]]}, {"type": 2, "data": [50, 50, 50, 50, 50, 50, 50, 50, 50, -1, 50, 50, 50, 50, -1, 50, 50, 50, 50, 50, -1, -1, -1, 50, 45, 41, 41, 41, 41, 41, 41, 41, 41, 41, 41, 41, 41, 41, 41, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, 50, -1, 50, 50, 50, 50, 50, 50, 50, 50, -1, -1, 50, 50, -1, -1, -1, -1, -1, 50, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, -1, 50, 44, 45, 47, 47, 47, 47, 47, 51, 50, -1, -1, -1, 50, 50, 50, 50, 50, 50, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, 41, 42, -1, -1, -1, -1, -1, 43, 50, -1, -1, 50, 50, 50, 50, 50, 44, 45, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 50, 50, 50, 50, 44, 45, 41, 41, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 41, 41, 41, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 34, -1, -1, -1, -1, -1, -1, -1, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 39, 38, 33, 29, 30, -1, -1, -1, -1, 43, 44, 50, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, 50, 32, 33, 34, -1, -1, -1, 43, 44, 50, 50, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, 50, 50, 38, 37, -1, -1, -1, 51, 50, -1, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, -1, -1, -1, 48, 47, 51, 50, 50, 50, 50, 50, 50, 50, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 51, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, -1, -1, 44, 45, 41, 42, -1, -1, -1, 32, 32, 35, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 47, 47, 51, 50, 50, 50, 50, 50, 50, 50, 44, 45, 41, 42, -1, -1, -1, 32, 32, 32, 32, 32, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 47, 47, 47, 47, 47, 47, 41, 42, -1, -1, -1, -1, -1, 32, 32, 32, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 32, 44, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 35, 35, 29, 30, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 39, 38, 50, 50, 32, 33, -1, 29, 30, -1, -1, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, 50, 50, 50, -1, -1, -1, -1, 32, 33, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, 50, -1, -1, 32, 32, 32, 32, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 32, 44, 45, 42, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 35, 29, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, 33, 29, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 39, 38, 44, 32, 33, 35, 34, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 32, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, 44, 44, 44, 44, 44, 45, 42, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 32, 44, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 44, 44, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 35, 35, 35, 35, 35, -1, -1, -1, -1, -1, 47, 47, 47, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, -1, -1, -1, -1, -1, 36, 35, 35, 39, 38, 44, 44, 44, 44, 44, 44, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 44, 44, 44, 45, 47, 41, 41, 51, 50, 44, 33, 35, 35, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 41, 41, 41, 42, -1, -1, -1, 48, 47, 47, 47, 51, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, -1, -1, 35, 35, 29, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 44, -1, -1, -1, -1, -1, -1, 35, 35, -1, -1, -1, -1, 51, 50, -1, -1, 44, 32, 33, 29, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, 44, 44, -1, -1, -1, -1, 48, 51, 50, -1, -1, -1, 44, 32, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 39, 38, 38, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 51, 50, -1, 50, 44, 44, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 35, 29, 30, -1, -1, 40, 47, 47, 47, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 47, 47, 41, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 32, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 29, -1, 50, 50, 50, 50, 50, -1, -1, -1, -1, -1, 44, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 39, -1, -1, 50, 50, 50, 50, 50, 50, 50, -1, -1, 41, 41, 41, 42, -1, -1, -1, -1, -1, -1, 29, 30, -1, -1, -1, -1, -1, -1, -1, 36, 35, 35, 35, 35, 35, 35, -1, -1, -1, -1, -1, -1, -1, 35, 32, -1, -1, -1, 50, 50, -1, -1, 41, 41, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 38, 32, 33, 30, -1, -1, -1, -1, 36, 35, 39, 38, 44, 44, 44, -1, -1, -1, -1, -1, -1, -1, 44, -1, -1, -1, 50, 50, 50, 50, 50, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 39, 38, 38, 38, 32, 33, -1, -1, -1, 36, 39, 38, 44, 44, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44]}, {"type": 2, "data": [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 57, 57, 57, 57, 57, 57, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 0, 0, 1, 2, 3, 2, 2, 3, 2, 2, 3, 57, 57, 57, 57, 2, 3, 2, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 0, 1, 2, 3, 2, 3, 2, 2, 3, 3, 1, 0, 1, 0, 1, 2, 3, 2, 2, 3, 2, 0, 1, 0, 1, 1, 2, 3, 0, 1, 57, 57, 0, 1, 0, 2, 3, 3, 2, 2, 2, 3, 2, 3, 2, 3, 2, 2, 3, 2, 0, 1, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 3, 0, 1, 0, 0, 0, 2, 3, 3, 2, 3, 2, 3, 2, 3, 0, 3, 2, 3, 3, 2, 3, 0, 2, 3, 3, 0, 1, 0, 1, 0, 1, 0, 2, 3, 0, 1, 3, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 2, 3, 0, 2, 2, 3, 0, 1, 1, -1, 0, 1, 0, 1, 57, 57, 57, 1, 0, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 0, 2, 3, 2, 3, 0, 0, 2, 3, 3, 0, 1, 1, 2, 3, 57, 57, 57, 57, 57, 2, 3, 0, 1, 2, 3, 0, 1, 3, 0, 0, 1, 0, 1, 2, 0, 1, 2, 3, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 0, 2, 3, 2, 2, 3, 3, 2, 0, 1, 0, 1, 1, 0, 57, 57, 57, 57, 57, 57, 1, 0, 1, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 2, 3, 2, 3, 2, 0, 1, 2, 2, 2, 3, 3, 0, 1, 1, 2, 3, 3, 2, 2, 3, 2, 2, 3, 2, 3, 3, 2, 3, 3, 57, 57, 57, 57, 57, 57, 3, 3, 2, 3, 2, 3, 3, 2, 2, 3, 2, 3, 0, 1, 2, 0, 1, 0, 0, 0, 1, 0, 0, 1, 3, 3, 3, 1, 2, 3, 0, 1, 0, 0, 0, 2, 0, 1, 2, 3, 1, 0, 2, 57, 57, 57, 57, 57, 57, 2, 3, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 0, 1, 0, 2, 0, 1, 2, 3, 3, 0, 1, 3, 2, 3, 2, 3, 2, 2, 2, 3, 2, 3, 0, 1, 3, 2, 3, 0, 2, 57, 57, 57, 57, 57, 1, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 0, 1, 3, 2, 3, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 2, 3, 2, 3, 3, 2, 57, 57, 57, 57, 57, 57, 0, 1, 1, 1, 0, 2, 3, 0, 1, 0, 1, 1, 2, 3, 2, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 0, 1, 1, 0, 57, 57, 57, 57, 57, 57, 3, 3, 2, 3, 0, 2, 3, 2, 3, 0, 1, 3, 3, 0, 1, 3, 0, 1, 1, 0, 1, 0, 1, 0, 0, 2, 3, 0, 1, 0, 3, 2, 3, 1, 0, 1, 3, 2, 3, 3, 2, 3, 2, 57, 57, 57, 57, 57, 57, 1, 0, 1, 2, 0, 1, 2, 2, 3, 0, 0, 2, 3, 0, 2, 3, 3, 2, 3, 2, 0, 1, 2, 3, 2, 2, 3, 2, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 57, 57, 57, 57, 57, 57, 1, 2, 2, 3, 2, 2, 3, 1, 0, 1, 1, 2, 2, 3, 0, 1, 3, 2, 2, 3, 1, 2, 0, 1, 3, 3, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, -1, -1, 57, 57, 57, 57, 0, 57, 0, 1, 0, 1, 0, 0, 1, 3, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 3, 2, 2, 3, 0, 0, 3, 2, 3, 0, 1, 0, 0, 1, 3, 2, 3, 0, 1, -1, -1, -1, 0, 57, 57, 57, 57, 57, 57, 57, 0, 0, 3, 2, 2, 3, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 2, 2, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, -1, -1, -1, 0, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 0, 1, 0, 1, 3, 2, 3, 2, 0, 1, 2, 0, 0, 2, 3, 2, 3, 3, 3, 2, 0, 3, 2, 2, 3, 2, 3, 2, 3, 2, -1, -1, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 3, 0, 0, 1, 0, 2, 0, 0, 1, 2, 3, 0, 1, 3, 2, 0, 1, 2, 0, 1, 0, 1, 0, 0, 1, -1, -1, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 2, 3, 2, 0, 1, 2, 3, 1, 1, 2, 3, 0, 1, 2, 3, 0, 0, 1, 1, 3, 2, 2, 3, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 0, 0, 1, 0, 57, 57, 57, 57, 57, 2, 0, 1, 2, 3, 3, 3, 0, 0, 1, 3, 0, 1, 2, 2, 3, 0, 1, 1, 2, 3, 0, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 0, 0, 0, 1, 2, 3, 3, 2, 3, 57, 57, 57, 57, 57, 57, 1, 0, 0, 1, 1, 2, 3, 1, 2, 3, 3, 0, 1, 0, 0, 1, 1, 0, 2, 3, 2, 0, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 0, 1, 2, 3, 2, 3, 1, 0, 1, 2, 3, 57, 57, 57, 57, 57, 2, 2, 3, 3, 1, 1, 3, 1, 0, 1, 2, 3, 2, 2, 3, 3, 2, 3, 3, 1, 0, 2, 3, 57, 57, 57, 57, 57, 0, 0, 1, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 0, 2, 57, 57, 57, 57, 57, -1, 2, 3, 3, 2, 3, 2, 3, 0, 1, 2, 0, 1, 3, 2, 3, 1, 0, 1, 0, 1, 0, 3, 57, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 0, 1, 0, 2, 57, 57, 57, 57, 57, -1, 1, 1, 0, 1, 1, 2, 3, 2, 2, 3, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 2, 3, 3, 0, 57, 57, 57, 57, 57, 57, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 0, 1, 0, 1, 1, 2, 3, 0, 0, 1, 2, 3, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 3, 57, 57, 57, 57, 57, 57, 3, 0, 0, 2, 3, 2, 3, 0, 1, 3, 2, 3, 3, 1, 0, 1, 2, 3, 1, 2, 0, 1, 3, 2, 3, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 2, 3, 57, 57, 57, 57, 57, 57, 57, 57, 1, 2, 2, 2, 3, 0, 1, 0, 2, 3, 2, 3, 3, 0, 1, 3, 2, 3, 0, 1, 1, 0, 1, 2, 3, 0, 1, 0, 0, 1, 0, 1, 0, 1, 3, 0, 1, 3, 2, 3, 3, 57, 57, 57, 57, 57, 57, 57, 0, 0, 0, 1, 2, 3, 1, 3, 2, 3, 2, 3, 2, 3, 0, 2, 3, 2, 3, 3, 0, 1, 2, 0, 1, 3, 0, 1, 3, 2, 3, 2, 3, 3, 2, 3, 3, 2, 3, 0, 1, 3, 57, 57, 57, 57, 3, 2, 2, 2, 3, 0, 1, 3, 0, 2, 0, 1, 0, 1, 1, 0, 1, 1, 2, 2, 3, 0, 1, 0, 2, 3, 1, 0, 1, 2, 3, 2, 3, 1, 0, 0, 1, 1, 0, 1, 2, 3, 2, 3, 57, 57, 0, 1, 0, 1, 2, 3, 2, 3, 3, 2, 3, 2, 3, 0, 1, 0, 2, 3, 3, 2, 3, 3, 2, 3, 1, 2, 2, 3, 2, 3, 3, 2, 0, 1, 0, 1, 2, 3, 0, 1, 3, 3, 3, 2, 3, 0, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 2, 0, 1, 2, 3, 0, 1, 0, 2, 3, 3, 2, 2, 2, 3, 1, 1, 2, 3, 2, 3, 0, 1, 2, 3, 0, 1, 0, 1, 1, 0, 1, 1, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 3, 2, 2, 3, 3, 2, 0, 1, 3, 2, 3, 0, 0, 0, 0, 1, 0, 0, 1, 3, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 3, 2, 3, 3, 2, 3, 3, 0, 1, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 2, 3, 1, 0, 0, 2, 2, 0, 0, 0, 1, 2, 3, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 0, 1, 2, 3, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 1, 2, 3, 2, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 2, 3, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 2, 3, 0, 1, 2, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 0, 1, 2, 3, 2, 0, 1, 2, 3, 1, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 1, 2, 2, 0, 1, 1, 0, 1, 0, 1, 3, 0, 0, 0, 1, 3, 0, 1, 2, 3, 2, 3, 0, 1, 0, 1, 2, 0, 0, 1, 0, 2, 3, 1, 2, 3, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 1, 0, 2, 3, 1, 2, 3, 1, 1, 0, 1, 0, 2, 3, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 0, 1, 0, 1, 3, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1]}