{"mW": 984, "mH": 720, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1233", 0, 3, 2], ["315", 0, 3, 3], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["928", 0, 2, 1], ["154", 0, 2, 1]], "layers": [{"type": 3, "obj": [[2, "3988", 717, 489, 148, 179, 0], [2, "3988", 187, 242, 148, 179, 0], [2, "3988", 301, -4, 148, 179, 0], [2, "3988", 405, 314, 148, 179, 0], [2, "3410", 716, 380, 54, 74, 2], [2, "1501", 936, 544, 50, 26, 0], [2, "1501", 911, 557, 50, 26, 0], [2, "1501", 785, 624, 50, 26, 0], [2, "1501", 811, 611, 50, 26, 0], [2, "1501", 808, 613, 50, 26, 0], [2, "1501", 834, 600, 50, 26, 0], [2, "1501", 783, 626, 50, 26, 0], [2, "1501", 815, 629, 50, 26, 0], [2, "1501", 790, 642, 50, 26, 0], [2, "1501", 829, 639, 50, 26, 0], [2, "1501", 804, 652, 50, 26, 0], [2, "1501", 734, 650, 50, 26, 0], [2, "1501", 760, 637, 50, 26, 0], [2, "1501", 709, 663, 50, 26, 0], [2, "1501", 657, 689, 50, 26, 0], [2, "1501", 683, 676, 50, 26, 0], [2, "1501", 632, 702, 50, 26, 0], [2, "1501", 606, 715, 50, 26, 0], [2, "1501", 676, 705, 50, 26, 0], [2, "1501", 702, 692, 50, 26, 0], [2, "1501", 651, 718, 50, 26, 0], [2, "1501", 810, 662, 50, 26, 0], [2, "1501", 836, 649, 50, 26, 0], [2, "1501", 785, 675, 50, 26, 0], [2, "1501", 708, 690, 50, 26, 0], [2, "1501", 683, 703, 50, 26, 0], [2, "1501", 753, 666, 50, 26, 0], [2, "1501", 779, 653, 50, 26, 0], [2, "1501", 728, 679, 50, 26, 0], [2, "1501", 861, 585, 50, 26, 0], [2, "1501", 836, 598, 50, 26, 0], [2, "1501", 931, 536, 50, 26, 0], [2, "1501", 957, 549, 50, 26, 0], [2, "1501", 983, 562, 50, 26, 0], [2, "1501", 860, 587, 50, 26, 0], [2, "1501", 946, 543, 50, 26, 0], [2, "1501", 904, 562, 50, 26, 0], [2, "1501", 930, 549, 50, 26, 0], [2, "1501", 879, 575, 50, 26, 0], [2, "1501", 908, 575, 50, 26, 0], [2, "1501", 934, 562, 50, 26, 0], [2, "1501", 883, 588, 50, 26, 0], [2, "1501", 908, 597, 50, 26, 0], [2, "1501", 934, 584, 50, 26, 0], [2, "1501", 960, 571, 50, 26, 0], [2, "1501", 899, 606, 50, 26, 0], [2, "1501", 925, 593, 50, 26, 0], [2, "1501", 951, 580, 50, 26, 0], [2, "1501", 977, 593, 50, 26, 0], [2, "1501", 951, 606, 50, 26, 0], [2, "1501", 925, 619, 50, 26, 0], [2, "1501", 977, 619, 50, 26, 0], [2, "1501", 825, 642, 50, 26, 0], [2, "1501", 851, 655, 50, 26, 0], [2, "1501", 744, 711, 50, 26, 0], [2, "1501", 796, 711, 50, 26, 0], [2, "1501", 792, 688, 50, 26, 0], [2, "1501", 818, 675, 50, 26, 0], [2, "1501", 844, 662, 50, 26, 0], [2, "1501", 818, 701, 50, 26, 0], [2, "1501", 844, 714, 50, 26, 0], [2, "1501", 863, 647, 50, 26, 0], [2, "1501", 889, 660, 50, 26, 0], [2, "1501", 825, 693, 50, 26, 0], [2, "1501", 851, 680, 50, 26, 0], [2, "1501", 877, 667, 50, 26, 0], [2, "1501", 851, 706, 50, 26, 0], [2, "1501", 877, 719, 50, 26, 0], [2, "1501", 903, 706, 50, 26, 0], [2, "1501", 970, 616, 50, 26, 0], [2, "1501", 850, 704, 50, 26, 0], [2, "1501", 876, 691, 50, 26, 0], [2, "1501", 902, 678, 50, 26, 0], [2, "1501", 928, 691, 50, 26, 0], [2, "1501", 902, 704, 50, 26, 0], [2, "1501", 876, 717, 50, 26, 0], [2, "1501", 928, 717, 50, 26, 0], [2, "1501", 954, 704, 50, 26, 0], [2, "1501", 899, 657, 50, 26, 0], [2, "1501", 925, 644, 50, 26, 0], [2, "1501", 951, 631, 50, 26, 0], [2, "1501", 977, 644, 50, 26, 0], [2, "1501", 951, 657, 50, 26, 0], [2, "1501", 925, 670, 50, 26, 0], [2, "1501", 951, 683, 50, 26, 0], [2, "1501", 977, 670, 50, 26, 0], [2, "1501", 932, 684, 50, 26, 0], [2, "1501", 958, 671, 50, 26, 0], [2, "1501", 958, 697, 50, 26, 0], [2, "1501", 696, 716, 50, 26, 0], [2, "1501", 848, 713, 50, 26, 0], [2, "1501", -38, 192, 50, 26, 0], [2, "1501", -38, 218, 50, 26, 0], [2, "1501", -12, 205, 50, 26, 0], [2, "1501", -46, 240, 50, 26, 0], [2, "1501", -20, 227, 50, 26, 0], [2, "1501", 6, 214, 50, 26, 0], [2, "1501", 32, 227, 50, 26, 0], [2, "1501", 6, 240, 50, 26, 0], [2, "1501", 6, 266, 50, 26, 0], [2, "1501", 32, 253, 50, 26, 0], [2, "1501", 58, 240, 50, 26, 0], [2, "1501", -37, 227, 50, 26, 0], [2, "1501", -11, 240, 50, 26, 0], [2, "1501", -37, 253, 50, 26, 0], [2, "1501", -37, 279, 50, 26, 0], [2, "1501", -11, 266, 50, 26, 0], [2, "1501", 15, 253, 50, 26, 0], [2, "1501", 702, 705, 50, 26, 0], [2, "1501", 728, 692, 50, 26, 0], [2, "1501", 754, 679, 50, 26, 0], [2, "1501", 780, 692, 50, 26, 0], [2, "1501", 754, 705, 50, 26, 0], [2, "1501", 728, 718, 50, 26, 0], [2, "1501", 780, 718, 50, 26, 0], [2, "1501", 806, 705, 50, 26, 0], [2, "1501", 825, 624, 50, 26, 0], [2, "1501", 851, 611, 50, 26, 0], [2, "1501", 877, 598, 50, 26, 0], [2, "1501", 903, 611, 50, 26, 0], [2, "1501", 877, 624, 50, 26, 0], [2, "1501", 851, 637, 50, 26, 0], [2, "1501", 877, 650, 50, 26, 0], [2, "1501", 903, 637, 50, 26, 0], [2, "1501", 929, 624, 50, 26, 0], [2, "894", 448, 85, 24, 20, 2], [2, "894", 429, 95, 24, 20, 2], [2, "894", 405, 107, 24, 20, 2], [2, "873", 963, 259, 64, 80, 0], [2, "873", 940, 269, 64, 80, 0], [2, "873", 899, 291, 64, 80, 0], [2, "873", 921, 281, 64, 80, 0], [2, "873", 876, 302, 64, 80, 0], [2, "873", 853, 312, 64, 80, 0], [2, "873", 812, 334, 64, 80, 0], [2, "873", 834, 324, 64, 80, 0], [2, "873", 791, 347, 64, 80, 0], [2, "873", 769, 357, 64, 80, 0], [2, "873", 758, 362, 64, 80, 0], [2, "873", 737, 372, 64, 80, 0], [2, "1403", 722, 383, 18, 22, 0], [2, "1403", 714, 397, 18, 22, 0], [2, "1225", 951, 239, 48, 44, 2], [2, "1225", 913, 260, 48, 44, 2], [2, "1225", 875, 282, 48, 44, 2], [2, "1225", 837, 303, 48, 44, 2], [2, "1226", 778, 325, 70, 47, 2], [2, "1226", 726, 347, 70, 47, 2], [2, "1196", 770, 299, 60, 74, 0], [2, "1196", 904, 231, 60, 74, 0], [2, "873", 857, -70, 64, 80, 2], [2, "873", 879, -60, 64, 80, 2], [2, "873", 901, -47, 64, 80, 2], [2, "873", 925, -38, 64, 80, 2], [2, "873", 946, -30, 64, 80, 2], [2, "873", 970, -18, 64, 80, 2], [2, "1196", 950, -42, 60, 74, 2], [2, "1501", -45, 214, 50, 26, 0], [2, "1501", -45, 240, 50, 26, 0], [2, "1501", -19, 227, 50, 26, 0], [2, "1501", 920, 583, 50, 26, 0], [2, "1501", 946, 570, 50, 26, 0], [2, "1501", 972, 557, 50, 26, 0], [2, "1501", 998, 570, 50, 26, 0], [2, "1501", 972, 583, 50, 26, 0], [2, "1501", 946, 596, 50, 26, 0], [2, "1501", 972, 609, 50, 26, 0], [2, "1501", 998, 596, 50, 26, 0], [2, "1501", 1024, 583, 50, 26, 0], [2, "1501", -71, 201, 50, 26, 0], [2, "3984", -24, 696, 24, 24, 0], [2, "3984", -48, 696, 24, 24, 0], [2, "3984", -72, 696, 24, 24, 0], [2, "3984", -72, 672, 24, 24, 0], [2, "3984", -48, 672, 24, 24, 0], [2, "3984", -24, 672, 24, 24, 0], [2, "3984", -72, 624, 24, 24, 0], [2, "3984", -48, 624, 24, 24, 0], [2, "3984", -24, 624, 24, 24, 0], [2, "3984", -24, 648, 24, 24, 0], [2, "3984", -48, 648, 24, 24, 0], [2, "3984", -72, 648, 24, 24, 0], [2, "3984", -72, 576, 24, 24, 0], [2, "3984", -48, 576, 24, 24, 0], [2, "3984", -24, 576, 24, 24, 0], [2, "3984", -24, 600, 24, 24, 0], [2, "3984", -48, 600, 24, 24, 0], [2, "3984", -72, 600, 24, 24, 0], [2, "3984", -72, 432, 24, 24, 0], [2, "3984", -48, 432, 24, 24, 0], [2, "3984", -24, 432, 24, 24, 0], [2, "3984", -24, 456, 24, 24, 0], [2, "3984", -48, 456, 24, 24, 0], [2, "3984", -72, 456, 24, 24, 0], [2, "3984", -72, 480, 24, 24, 0], [2, "3984", -48, 480, 24, 24, 0], [2, "3984", -24, 480, 24, 24, 0], [2, "3984", -24, 504, 24, 24, 0], [2, "3984", -48, 504, 24, 24, 0], [2, "3984", -72, 504, 24, 24, 0], [2, "3984", -72, 528, 24, 24, 0], [2, "3984", -48, 528, 24, 24, 0], [2, "3984", -24, 528, 24, 24, 0], [2, "3984", -24, 552, 24, 24, 0], [2, "3984", -48, 552, 24, 24, 0], [2, "3984", -72, 552, 24, 24, 0], [2, "3984", -72, 336, 24, 24, 0], [2, "3984", -48, 336, 24, 24, 0], [2, "3984", -24, 336, 24, 24, 0], [2, "3984", -24, 360, 24, 24, 0], [2, "3984", -48, 360, 24, 24, 0], [2, "3984", -72, 360, 24, 24, 0], [2, "3984", -72, 384, 24, 24, 0], [2, "3984", -48, 384, 24, 24, 0], [2, "3984", -24, 384, 24, 24, 0], [2, "3984", -24, 408, 24, 24, 0], [2, "3984", -48, 408, 24, 24, 0], [2, "3984", -72, 408, 24, 24, 0], [2, "3984", -72, 144, 24, 24, 0], [2, "3984", -48, 144, 24, 24, 0], [2, "3984", -24, 144, 24, 24, 0], [2, "3984", -24, 168, 24, 24, 0], [2, "3984", -48, 168, 24, 24, 0], [2, "3984", -72, 168, 24, 24, 0], [2, "3984", -72, 240, 24, 24, 0], [2, "3984", -48, 240, 24, 24, 0], [2, "3984", -24, 240, 24, 24, 0], [2, "3984", -24, 264, 24, 24, 0], [2, "3984", -48, 264, 24, 24, 0], [2, "3984", -72, 264, 24, 24, 0], [2, "3984", -72, 0, 24, 24, 0], [2, "3984", -48, 0, 24, 24, 0], [2, "3984", -24, 0, 24, 24, 0], [2, "3984", -24, 24, 24, 24, 0], [2, "3984", -48, 24, 24, 24, 0], [2, "3984", -72, 24, 24, 24, 0], [2, "3984", -72, 48, 24, 24, 0], [2, "3984", -48, 48, 24, 24, 0], [2, "3984", -24, 48, 24, 24, 0], [2, "3984", -24, 72, 24, 24, 0], [2, "3984", -48, 72, 24, 24, 0], [2, "3984", -72, 72, 24, 24, 0], [2, "3984", -72, 96, 24, 24, 0], [2, "3984", -48, 96, 24, 24, 0], [2, "3984", -24, 96, 24, 24, 0], [2, "3984", -24, 120, 24, 24, 0], [2, "3984", -48, 120, 24, 24, 0], [2, "3984", -72, 120, 24, 24, 0], [2, "3984", -144, 576, 24, 24, 0], [2, "3984", -120, 576, 24, 24, 0], [2, "3984", -96, 576, 24, 24, 0], [2, "3984", -96, 600, 24, 24, 0], [2, "3984", -120, 600, 24, 24, 0], [2, "3984", -144, 600, 24, 24, 0], [2, "3984", -144, 528, 24, 24, 0], [2, "3984", -120, 528, 24, 24, 0], [2, "3984", -96, 528, 24, 24, 0], [2, "3984", -96, 552, 24, 24, 0], [2, "3984", -120, 552, 24, 24, 0], [2, "3984", -144, 552, 24, 24, 0], [2, "3984", 985, 477, 24, 24, 0], [2, "3984", 1009, 477, 24, 24, 0], [2, "3984", 1033, 477, 24, 24, 0], [2, "3984", 1033, 501, 24, 24, 0], [2, "3984", 1009, 501, 24, 24, 0], [2, "3984", 985, 501, 24, 24, 0], [2, "3984", 985, 525, 24, 24, 0], [2, "3984", 1009, 525, 24, 24, 0], [2, "3984", 1033, 525, 24, 24, 0], [2, "3984", 1033, 549, 24, 24, 0], [2, "3984", 1009, 549, 24, 24, 0], [2, "3984", 985, 549, 24, 24, 0], [2, "3984", 985, 477, 24, 24, 0], [2, "3984", 1009, 477, 24, 24, 0], [2, "3984", 1033, 477, 24, 24, 0], [2, "3984", 1033, 501, 24, 24, 0], [2, "3984", 1009, 501, 24, 24, 0], [2, "3984", 985, 501, 24, 24, 0], [2, "3984", 985, 525, 24, 24, 0], [2, "3984", 1009, 525, 24, 24, 0], [2, "3984", 1033, 525, 24, 24, 0], [2, "3984", 1033, 549, 24, 24, 0], [2, "3984", 1009, 549, 24, 24, 0], [2, "3984", 985, 549, 24, 24, 0], [2, "3984", 604, 720, 24, 24, 0], [2, "3984", 628, 720, 24, 24, 0], [2, "3984", 652, 720, 24, 24, 0], [2, "3984", 652, 744, 24, 24, 0], [2, "3984", 628, 744, 24, 24, 0], [2, "3984", 604, 744, 24, 24, 0], [2, "3978", -28, 299, 50, 26, 0], [2, "3978", -2, 286, 50, 26, 0], [2, "3978", 50, 260, 50, 26, 0], [2, "3978", 24, 273, 50, 26, 0], [2, "3978", 153, 209, 50, 26, 0], [2, "3978", 127, 222, 50, 26, 0], [2, "3978", 101, 235, 50, 26, 0], [2, "3978", 75, 248, 50, 26, 0], [2, "3978", 257, 157, 50, 26, 0], [2, "3978", 231, 170, 50, 26, 0], [2, "3978", 205, 183, 50, 26, 0], [2, "3978", 179, 196, 50, 26, 0], [2, "3978", 358, 106, 50, 26, 0], [2, "3978", 332, 119, 50, 26, 0], [2, "3978", 306, 132, 50, 26, 0], [2, "3978", 280, 145, 50, 26, 0], [2, "3978", 461, 54, 50, 26, 0], [2, "3978", 435, 67, 50, 26, 0], [2, "3978", 409, 80, 50, 26, 0], [2, "3978", 383, 93, 50, 26, 0], [2, "3978", 487, 39, 50, 26, 0], [2, "3978", 461, 52, 50, 26, 0], [2, "3978", 435, 65, 50, 26, 0], [2, "3978", 409, 79, 50, 26, 0], [2, "3978", -32, 195, 50, 26, 2], [2, "3978", -7, 208, 50, 26, 2], [2, "3978", 18, 221, 50, 26, 2], [2, "3978", 43, 234, 50, 26, 2], [2, "3978", 55, 240, 50, 26, 2], [2, "3984", -72, 192, 24, 24, 0], [2, "3984", -48, 192, 24, 24, 0], [2, "3984", -24, 192, 24, 24, 0], [2, "3984", -24, 216, 24, 24, 0], [2, "3984", -48, 216, 24, 24, 0], [2, "3984", -72, 216, 24, 24, 0], [2, "3984", -72, 288, 24, 24, 0], [2, "3984", -48, 288, 24, 24, 0], [2, "3984", -24, 288, 24, 24, 0], [2, "3984", -24, 312, 24, 24, 0], [2, "3984", -48, 312, 24, 24, 0], [2, "3984", -72, 312, 24, 24, 0], [2, "3978", 241, 191, 50, 26, 2], [2, "3978", 267, 204, 50, 26, 2], [2, "3978", 317, 230, 50, 26, 2], [2, "3978", 291, 217, 50, 26, 2], [2, "3978", 364, 253, 50, 26, 2], [2, "3978", 338, 240, 50, 26, 2], [2, "3978", 413, 277, 50, 26, 2], [2, "3978", 387, 264, 50, 26, 2], [2, "3978", 462, 302, 50, 26, 2], [2, "3978", 436, 289, 50, 26, 2], [2, "3978", 514, 328, 50, 26, 2], [2, "3978", 488, 315, 50, 26, 2], [2, "3978", 558, 350, 50, 26, 2], [2, "3978", 532, 337, 50, 26, 2], [2, "3978", 606, 374, 50, 26, 2], [2, "3978", 580, 361, 50, 26, 2], [2, "3978", 651, 396, 50, 26, 2], [2, "3978", 625, 383, 50, 26, 2], [2, "3978", 695, 418, 50, 26, 2], [2, "3978", 669, 405, 50, 26, 2], [2, "3978", 743, 442, 50, 26, 2], [2, "3978", 717, 429, 50, 26, 2], [2, "3978", 791, 465, 50, 26, 2], [2, "3978", 765, 452, 50, 26, 2], [2, "3978", 841, 491, 50, 26, 2], [2, "3978", 815, 478, 50, 26, 2], [2, "3978", 882, 511, 50, 26, 2], [2, "3978", 856, 498, 50, 26, 2], [2, "3978", 917, 529, 50, 26, 2], [2, "3978", 891, 516, 50, 26, 2], [2, "3978", 939, 539, 50, 26, 0], [2, "3978", 913, 552, 50, 26, 0], [2, "3978", 888, 565, 50, 26, 0], [2, "3978", 862, 578, 50, 26, 0], [2, "3978", 836, 591, 50, 26, 0], [2, "3978", 810, 604, 50, 26, 0], [2, "3978", 785, 617, 50, 26, 0], [2, "3978", 759, 630, 50, 26, 0], [2, "3978", 735, 643, 50, 26, 0], [2, "3978", 709, 656, 50, 26, 0], [2, "3978", 685, 668, 50, 26, 0], [2, "3978", 659, 681, 50, 26, 0], [2, "3978", 633, 694, 50, 26, 0], [2, "3978", 607, 707, 50, 26, 0], [2, "894", 481, 325, 24, 20, 0], [2, "894", 457, 313, 24, 20, 0], [2, "894", 524, 347, 24, 20, 0], [2, "894", 500, 335, 24, 20, 0]]}, {"type": 4, "obj": [[4, 3, 761, 78, 1, 4006], [2, "3069", 576, 11, 98, 74, 0], [2, "3987", 865, 50, 42, 42, 0], [2, "942", 599, 58, 68, 61, 0], [2, "3987", 473, 78, 42, 42, 0], [2, "3987", 926, 79, 42, 42, 0], [2, "3069", 666, 53, 98, 74, 0], [2, "879", 447, 84, 26, 56, 0], [2, "3069", 754, 95, 98, 74, 0], [2, "942", 697, 109, 68, 61, 0], [2, "3987", 371, 130, 42, 42, 0], [2, "3573", 49, 176, 40, 33, 0], [2, "1231", 532, 49, 114, 162, 0], [4, 1, 636, 233, 1, 4022], [4, 2, 643, 324, 1, 4005], [2, "879", 457, 315, 26, 56, 2], [2, "673", 115, 316, 80, 63, 0], [2, "3542", 182, 341, 42, 59, 0], [2, "673", 329, 339, 80, 63, 2], [2, "3558", 360, 368, 46, 38, 2], [2, "1231", 644, 256, 114, 162, 0], [2, "3573", 117, 393, 40, 33, 0], [2, "673", 258, 375, 80, 63, 2], [2, "3558", 287, 405, 46, 38, 2], [2, "3573", 120, 434, 40, 33, 0], [2, "3573", 120, 434, 40, 33, 0], [2, "3573", 89, 454, 40, 33, 0], [2, "3573", 5, 455, 40, 33, 0], [4, 4, 232, 528, 0, 4023], [2, "673", 13, 491, 80, 63, 2], [2, "3558", 44, 526, 46, 38, 2], [2, "3581", 448, 518, 82, 71, 0], [2, "3557", 536, 555, 22, 38, 0], [2, "3581", 337, 528, 82, 71, 2], [2, "3557", 313, 563, 22, 38, 0], [2, "41", 428, 630, 12, 11, 0], [2, "41", 443, 635, 12, 11, 0], [2, "41", 399, 644, 12, 11, 0], [2, "3581", 556, 584, 82, 71, 0], [2, "41", 414, 649, 12, 11, 0], [2, "3581", 230, 591, 82, 71, 2], [2, "3532", 199, 608, 48, 70, 2]]}, {"type": 3, "obj": [[2, "3573", 53, 386, 40, 33, 0], [2, "3573", 15, 403, 40, 33, 0], [2, "1457", 673, 27, 22, 30, 0], [2, "1457", 738, -1, 22, 30, 0], [2, "3069", 911, 172, 98, 74, 0], [2, "3145", 746, 236, 108, 72, 0], [2, "3609", 25, 414, 112, 59, 2], [2, "3609", 370, 633, 112, 59, 2], [2, "3609", 136, 101, 112, 59, 2], [2, "3082", 892, 453, 76, 40, 0], [2, "884", 409, 56, 24, 25, 0], [2, "884", 429, 66, 24, 25, 0], [2, "884", 548, 421, 24, 25, 0], [2, "895", 348, 291, 8, 31, 0], [2, "895", 348, 283, 8, 31, 0], [2, "895", 348, 256, 8, 31, 0], [2, "895", 160, 11, 8, 31, 2], [2, "895", 160, 38, 8, 31, 2], [2, "894", 980, 572, 24, 20, 0], [2, "895", 238, 242, 8, 31, 0], [2, "895", 238, 228, 8, 31, 0], [2, "895", 238, 201, 8, 31, 0], [2, "884", 236, 265, 24, 25, 0], [2, "884", 256, 275, 24, 25, 0], [2, "22", 468, 260, 62, 38, 0], [2, "21", 442, 243, 28, 24, 0], [2, "21", 529, 289, 28, 24, 0], [2, "3554", 379, 129, 20, 31, 2], [2, "895", 53, 91, 8, 31, 2], [2, "895", 53, 64, 8, 31, 2], [2, "1207", 964, 368, 22, 81, 0], [2, "1207", 964, 330, 22, 81, 0], [2, "1207", 846, 416, 22, 81, 0], [2, "1207", 846, 378, 22, 81, 0], [2, "1197", 943, 345, 54, 44, 2], [2, "1197", 938, 344, 54, 44, 2], [2, "1197", 889, 368, 54, 44, 2], [2, "1197", 839, 393, 54, 44, 2], [2, "1197", 790, 417, 54, 44, 2], [2, "884", 276, 285, 24, 25, 0], [2, "884", 296, 295, 24, 25, 0], [2, "884", 317, 305, 24, 25, 0], [2, "884", 337, 315, 24, 25, 0], [2, "884", 357, 325, 24, 25, 0], [2, "884", 377, 335, 24, 25, 0], [2, "884", 435, 365, 24, 25, 0], [2, "884", 395, 345, 24, 25, 0], [2, "884", 415, 355, 24, 25, 0], [2, "884", 516, 405, 24, 25, 0], [2, "884", 536, 415, 24, 25, 0], [2, "884", 118, 317, 24, 25, 2], [2, "884", 138, 307, 24, 25, 2], [2, "884", 78, 337, 24, 25, 2], [2, "884", 98, 327, 24, 25, 2], [2, "884", 38, 357, 24, 25, 2], [2, "884", 58, 347, 24, 25, 2], [2, "884", -2, 377, 24, 25, 2], [2, "884", 18, 367, 24, 25, 2], [2, "884", -22, 387, 24, 25, 2], [2, "884", 760, 527, 24, 25, 0], [2, "884", 781, 538, 24, 25, 0], [2, "884", 771, 533, 24, 25, 0], [2, "884", 791, 543, 24, 25, 0], [2, "884", 852, 572, 24, 25, 0], [2, "884", 872, 582, 24, 25, 0], [2, "895", 230, 204, 8, 31, 2], [2, "895", 138, 286, 8, 31, 2], [2, "895", 138, 276, 8, 31, 2], [2, "895", 138, 249, 8, 31, 2], [2, "895", 43, 333, 8, 31, 2], [2, "895", 43, 323, 8, 31, 2], [2, "895", 43, 296, 8, 31, 2], [2, "895", 453, 336, 8, 31, 0], [2, "895", 453, 309, 8, 31, 0], [2, "895", 453, 344, 8, 31, 0], [2, "895", 753, 487, 8, 31, 0], [2, "895", 753, 460, 8, 31, 0], [2, "895", 753, 495, 8, 31, 0], [2, "884", 811, 554, 24, 25, 0], [2, "884", 831, 564, 24, 25, 0], [2, "895", 398, 142, 8, 31, 2], [2, "895", 398, 132, 8, 31, 2], [2, "895", 398, 105, 8, 31, 2], [2, "895", 471, 103, 8, 31, 2], [2, "895", 471, 93, 8, 31, 2], [2, "895", 471, 66, 8, 31, 2], [2, "1207", 529, 71, 22, 81, 0], [2, "1207", 529, 52, 22, 81, 0], [2, "895", 320, 179, 8, 31, 2], [2, "895", 320, 169, 8, 31, 2], [2, "895", 320, 142, 8, 31, 2], [2, "895", 575, 72, 8, 31, 0], [2, "895", 575, 96, 8, 31, 0], [2, "895", 887, 252, 8, 31, 0], [2, "895", 887, 228, 8, 31, 0], [2, "895", 789, 202, 8, 31, 0], [2, "895", 789, 178, 8, 31, 0], [2, "895", 674, 145, 8, 31, 0], [2, "895", 674, 121, 8, 31, 0], [2, "894", 213, 202, 24, 20, 2], [2, "894", 189, 215, 24, 20, 2], [2, "894", 165, 227, 24, 20, 2], [2, "894", 141, 240, 24, 20, 2], [2, "884", 246, 17, 24, 25, 2], [2, "884", 266, 7, 24, 25, 2], [2, "884", 206, 37, 24, 25, 2], [2, "884", 226, 27, 24, 25, 2], [2, "884", 166, 57, 24, 25, 2], [2, "884", 86, 97, 24, 25, 2], [2, "884", 106, 87, 24, 25, 2], [2, "884", 46, 117, 24, 25, 2], [2, "884", 6, 137, 24, 25, 2], [2, "884", -14, 147, 24, 25, 2], [2, "884", 146, 67, 24, 25, 2], [2, "884", 557, 426, 24, 25, 0], [2, "884", 577, 436, 24, 25, 0], [2, "884", 597, 447, 24, 25, 0], [2, "884", 617, 457, 24, 25, 0], [2, "895", 542, 381, 8, 31, 0], [2, "895", 542, 354, 8, 31, 0], [2, "895", 542, 389, 8, 31, 0], [2, "895", 652, 408, 8, 31, 0], [2, "895", 871, 544, 8, 31, 0], [2, "895", 871, 517, 8, 31, 0], [2, "895", 871, 552, 8, 31, 0], [2, "3554", 857, 418, 20, 31, 2], [2, "3554", 973, 367, 20, 31, 2], [2, "1235", 542, 96, 12, 18, 2], [2, "1236", 515, 113, 42, 43, 0], [2, "1235", 531, 82, 12, 18, 2], [2, "1236", 865, 269, 42, 43, 0], [2, "894", 115, 251, 24, 20, 2], [2, "894", 91, 264, 24, 20, 2], [2, "894", 67, 276, 24, 20, 2], [2, "894", 43, 289, 24, 20, 2], [2, "894", 19, 301, 24, 20, 2], [2, "894", -5, 314, 24, 20, 2], [2, "894", 68, 41, 24, 20, 2], [2, "894", 44, 53, 24, 20, 2], [2, "894", 20, 65, 24, 20, 2], [2, "894", -4, 77, 24, 20, 2], [2, "894", 163, -7, 24, 20, 2], [2, "894", 139, 5, 24, 20, 2], [2, "894", 115, 17, 24, 20, 2], [2, "894", 91, 29, 24, 20, 2], [2, "894", 441, 6, 24, 20, 0], [2, "894", 465, 18, 24, 20, 0], [2, "894", 489, 30, 24, 20, 0], [2, "894", 418, -6, 24, 20, 0], [2, "894", 394, -18, 24, 20, 0], [2, "894", 262, 215, 24, 20, 0], [2, "894", 238, 203, 24, 20, 0], [2, "894", 359, 264, 24, 20, 0], [2, "894", 335, 252, 24, 20, 0], [2, "894", 311, 240, 24, 20, 0], [2, "894", 429, 299, 24, 20, 0], [2, "894", 405, 287, 24, 20, 0], [2, "894", 381, 275, 24, 20, 0], [2, "894", 597, 383, 24, 20, 0], [2, "894", 573, 371, 24, 20, 0], [2, "894", 549, 359, 24, 20, 0], [2, "894", 669, 419, 24, 20, 0], [2, "894", 645, 407, 24, 20, 0], [2, "894", 621, 395, 24, 20, 0], [2, "894", 741, 454, 24, 20, 0], [2, "894", 717, 442, 24, 20, 0], [2, "894", 693, 430, 24, 20, 0], [2, "894", 789, 478, 24, 20, 0], [2, "894", 765, 466, 24, 20, 0], [2, "894", 930, 549, 24, 20, 0], [2, "894", 906, 537, 24, 20, 0], [2, "894", 882, 525, 24, 20, 0], [2, "894", 858, 513, 24, 20, 0], [2, "894", 834, 501, 24, 20, 0], [2, "1398", 618, 85, 28, 66, 0], [2, "1398", 714, 132, 28, 66, 0], [2, "3127", 373, 193, 30, 30, 0], [2, "1457", 507, 128, 22, 30, 0], [2, "1456", 487, 133, 24, 32, 0], [2, "1456", 858, 284, 24, 32, 0], [2, "1457", 658, 184, 22, 30, 0], [2, "1457", 640, 175, 22, 30, 0], [2, "1456", 695, 204, 24, 32, 2], [2, "1457", 332, 211, 22, 30, 0], [2, "1457", 623, 337, 22, 30, 0], [2, "1457", 615, 353, 22, 30, 0], [2, "1457", 634, 354, 22, 30, 0], [2, "3127", 650, 351, 30, 30, 0], [2, "1456", 666, 367, 24, 32, 0], [2, "3518", 337, 282, 42, 37, 0], [2, "3560", 424, 311, 28, 31, 0], [2, "3560", 543, 368, 28, 31, 0], [2, "884", 294, -1, 24, 25, 0], [2, "884", 334, 19, 24, 25, 0], [2, "884", 354, 29, 24, 25, 0], [2, "884", 375, 39, 24, 25, 0], [2, "884", 395, 49, 24, 25, 0], [2, "3560", 29, 79, 28, 31, 2], [2, "891", 891, 405, 54, 75, 0], [2, "890", 874, 393, 94, 105, 0], [2, "3587", 510, 573, 64, 50, 0], [2, "3587", 292, 584, 64, 50, 2], [2, "3589", 383, 624, 90, 57, 2], [2, "3127", 349, 203, 30, 30, 0], [2, "3587", 166, 43, 64, 50, 2], [2, "3595", 118, 61, 94, 82, 2], [2, "3587", 57, 101, 64, 50, 2], [2, "3609", 394, 401, 112, 59, 0], [2, "3557", 186, 21, 22, 38, 0], [2, "3557", 78, 84, 22, 38, 0], [2, "3589", 39, 407, 90, 57, 2], [2, "41", 503, 266, 12, 11, 0], [2, "3560", 1, 328, 28, 31, 2], [2, "1398", 338, 129, 28, 66, 2], [2, "1398", 497, 49, 28, 66, 2], [2, "3554", 482, 77, 20, 31, 2], [2, "41", 484, 263, 12, 11, 2], [2, "3145", 799, 177, 108, 72, 0], [2, "3145", 773, 205, 108, 72, 0], [2, "682", 903, 215, 34, 35, 0], [2, "682", 930, 228, 34, 35, 0], [2, "682", 955, 240, 34, 35, 0], [2, "942", 931, 213, 68, 61, 0], [2, "1197", 799, -33, 54, 44, 0], [2, "1197", 848, -8, 54, 44, 0], [2, "1197", 897, 16, 54, 44, 0], [2, "1197", 946, 41, 54, 44, 0], [2, "3082", 883, 106, 76, 40, 2], [2, "1207", 762, -24, 22, 81, 0], [2, "891", 902, 61, 54, 75, 2], [2, "890", 881, 46, 94, 105, 2], [2, "1207", 852, 23, 22, 81, 0], [2, "1207", 853, -5, 22, 81, 0], [2, "1207", 979, 81, 22, 81, 0], [2, "1207", 980, 53, 22, 81, 0], [2, "1398", 807, -27, 28, 66, 0], [2, "3069", 616, -32, 98, 74, 2], [2, "1457", 715, 215, 22, 30, 0], [2, "1457", 675, 196, 22, 30, 0], [2, "1457", 746, 21, 22, 30, 0], [2, "3127", 767, 33, 30, 30, 0], [2, "1456", 964, 124, 24, 32, 2], [2, "1457", 833, 61, 22, 30, 0], [2, "1231", 639, -105, 114, 162, 0], [2, "3554", 880, 48, 20, 31, 0], [2, "3554", 941, 79, 20, 31, 0], [2, "3533", 54, 295, 36, 57, 2], [2, "884", 279, 0, 24, 25, 2], [2, "3581", 274, 11, 82, 71, 0], [2, "895", 376, 2, 8, 31, 0], [2, "895", 376, -25, 8, 31, 0], [2, "895", 376, 10, 8, 31, 0], [2, "895", 444, 36, 8, 31, 0], [2, "895", 444, 9, 8, 31, 0], [2, "895", 444, 44, 8, 31, 0], [2, "3492", 574, 374, 58, 86, 0], [2, "3492", 798, 485, 58, 86, 0], [2, "3492", 278, 225, 58, 86, 0], [2, "3492", 385, -15, 58, 86, 0], [2, "3483", 194, 216, 60, 106, 2], [2, "3485", 322, 308, 58, 54, 0], [2, "3145", 427, 371, 108, 72, 0], [2, "3145", -56, 558, 108, 72, 2], [2, "3145", -29, 588, 108, 72, 2], [2, "3145", -3, 618, 108, 72, 2], [2, "3145", -84, 528, 108, 72, 2], [2, "3480", 626, 428, 64, 94, 0], [2, "3512", 214, -10, 38, 52, 2], [2, "3599", 118, 30, 22, 34, 2], [2, "3546", 295, 1, 44, 55, 0], [2, "3145", 404, 137, 108, 72, 2], [2, "3480", 673, 451, 64, 94, 0], [2, "3558", 525, 420, 46, 38, 0], [2, "3558", 795, 553, 46, 38, 0], [2, "3532", 722, 493, 48, 70, 0], [2, "3532", 13, 110, 48, 70, 2], [2, "3561", 338, 40, 44, 54, 0], [2, "3483", 146, 236, 60, 106, 2], [2, "3522", 60, 411, 44, 31, 0], [2, "3558", 569, 441, 46, 38, 0], [2, "894", 256, -53, 24, 20, 2], [2, "894", 232, -41, 24, 20, 2], [2, "894", 208, -29, 24, 20, 2], [2, "894", 184, -17, 24, 20, 2], [2, "894", 278, -65, 24, 20, 2], [2, "894", 254, -53, 24, 20, 2], [2, "894", 230, -41, 24, 20, 2], [2, "894", 206, -29, 24, 20, 2], [2, "894", 370, -30, 24, 20, 0], [2, "894", 346, -42, 24, 20, 0], [2, "894", 322, -54, 24, 20, 0], [2, "894", 349, -41, 24, 20, 0], [2, "894", 325, -53, 24, 20, 0], [2, "894", 301, -65, 24, 20, 0], [2, "895", 294, -30, 8, 31, 2], [2, "895", 294, -57, 8, 31, 2], [2, "895", 294, -27, 8, 31, 2]]}, {"type": 3, "obj": [[2, "617", 878, 257, 22, 43, 0], [2, "617", 868, 252, 22, 43, 0], [2, "617", 878, 226, 22, 43, 0], [2, "617", 878, 209, 22, 43, 0], [2, "617", 974, 320, 22, 43, 0], [2, "617", 775, 440, 22, 43, 2], [2, "617", 765, 445, 22, 43, 2], [2, "617", 958, 366, 22, 43, 2], [2, "617", 968, 361, 22, 43, 2], [2, "617", 968, 344, 22, 43, 2], [2, "617", 958, 349, 22, 43, 2], [2, "617", 935, 379, 22, 43, 2], [2, "617", 935, 362, 22, 43, 2], [2, "617", 925, 367, 22, 43, 2], [2, "617", 903, 377, 22, 43, 2], [2, "617", 893, 382, 22, 43, 2], [2, "617", 860, 446, 22, 43, 2], [2, "617", 860, 415, 22, 43, 2], [2, "617", 870, 410, 22, 43, 2], [2, "617", 870, 393, 22, 43, 2], [2, "617", 860, 398, 22, 43, 2], [2, "617", 828, 461, 22, 43, 2], [2, "617", 828, 430, 22, 43, 2], [2, "617", 838, 425, 22, 43, 2], [2, "617", 828, 413, 22, 43, 2], [2, "617", 805, 442, 22, 43, 2], [2, "617", 805, 425, 22, 43, 2], [2, "617", 795, 430, 22, 43, 2], [2, "617", 286, 182, 22, 43, 2], [2, "617", 286, 165, 22, 43, 2], [2, "617", 276, 170, 22, 43, 2], [2, "617", 275, 180, 22, 43, 2], [2, "617", 275, 163, 22, 43, 2], [2, "617", 265, 168, 22, 43, 2], [2, "3401", 478, 24, 44, 81, 0], [2, "3401", -4, 67, 44, 81, 2], [2, "1501", 958, 703, 50, 26, 0], [2, "1501", 932, 716, 50, 26, 0], [2, "3401", -4, 88, 44, 81, 2], [2, "3401", 302, -64, 44, 81, 0], [2, "3401", 302, -43, 44, 81, 0], [2, "3401", 40, 66, 44, 81, 2], [2, "3401", 40, 45, 44, 81, 2], [2, "3401", 82, 45, 44, 81, 2], [2, "3401", 82, 24, 44, 81, 2], [2, "3401", 126, 23, 44, 81, 2], [2, "3401", 126, 2, 44, 81, 2], [2, "3401", 213, -21, 44, 81, 2], [2, "3401", 213, -42, 44, 81, 2], [2, "3401", 169, 1, 44, 81, 2], [2, "3401", 169, -20, 44, 81, 2], [2, "3401", 257, -43, 44, 81, 2], [2, "3401", 257, -64, 44, 81, 2], [2, "3401", 346, -21, 44, 81, 0], [2, "3401", 346, -42, 44, 81, 0], [2, "3401", 390, 1, 44, 81, 0], [2, "3401", 390, -20, 44, 81, 0], [2, "3401", 434, 23, 44, 81, 0], [2, "3401", 434, 2, 44, 81, 0], [2, "617", 513, 102, 22, 43, 2], [2, "617", 503, 107, 22, 43, 2], [2, "617", 503, 76, 22, 43, 2], [2, "617", 513, 71, 22, 43, 2], [2, "617", 513, 54, 22, 43, 2], [2, "617", 536, 102, 22, 43, 0], [2, "617", 546, 76, 22, 43, 0], [2, "617", 546, 59, 22, 43, 0], [2, "617", 536, 54, 22, 43, 0], [2, "617", 569, 87, 22, 43, 0], [2, "617", 569, 70, 22, 43, 0], [2, "617", 471, 91, 22, 43, 2], [2, "617", 481, 86, 22, 43, 2], [2, "617", 481, 69, 22, 43, 2], [2, "617", 471, 74, 22, 43, 2], [2, "617", 383, 166, 22, 43, 2], [2, "617", 373, 171, 22, 43, 2], [2, "617", 373, 140, 22, 43, 2], [2, "617", 383, 135, 22, 43, 2], [2, "617", 383, 118, 22, 43, 2], [2, "617", 373, 123, 22, 43, 2], [2, "617", 351, 181, 22, 43, 2], [2, "617", 341, 186, 22, 43, 2], [2, "617", 341, 155, 22, 43, 2], [2, "617", 351, 150, 22, 43, 2], [2, "617", 351, 133, 22, 43, 2], [2, "617", 318, 198, 22, 43, 2], [2, "617", 308, 172, 22, 43, 2], [2, "617", 318, 167, 22, 43, 2], [2, "617", 318, 150, 22, 43, 2], [2, "617", 308, 155, 22, 43, 2], [2, "617", 602, 135, 22, 43, 0], [2, "617", 602, 104, 22, 43, 0], [2, "617", 612, 109, 22, 43, 0], [2, "617", 602, 87, 22, 43, 0], [2, "617", 635, 120, 22, 43, 0], [2, "617", 645, 125, 22, 43, 0], [2, "617", 645, 108, 22, 43, 0], [2, "617", 635, 103, 22, 43, 0], [2, "617", 667, 136, 22, 43, 0], [2, "617", 677, 141, 22, 43, 0], [2, "617", 677, 124, 22, 43, 0], [2, "617", 667, 119, 22, 43, 0], [2, "617", 710, 188, 22, 43, 0], [2, "617", 700, 183, 22, 43, 0], [2, "617", 700, 152, 22, 43, 0], [2, "617", 710, 157, 22, 43, 0], [2, "617", 700, 135, 22, 43, 0], [2, "617", 733, 169, 22, 43, 0], [2, "617", 743, 174, 22, 43, 0], [2, "617", 743, 157, 22, 43, 0], [2, "617", 733, 152, 22, 43, 0], [2, "617", 766, 216, 22, 43, 0], [2, "617", 766, 185, 22, 43, 0], [2, "617", 776, 190, 22, 43, 0], [2, "617", 776, 173, 22, 43, 0], [2, "617", 766, 168, 22, 43, 0], [2, "617", 799, 185, 22, 43, 0], [2, "617", 898, 251, 22, 43, 0], [2, "617", 908, 256, 22, 43, 0], [2, "617", 898, 234, 22, 43, 0], [2, "1208", 530, 104, 52, 56, 0], [2, "1208", 574, 126, 52, 56, 0], [2, "1208", 622, 149, 52, 56, 0], [2, "1208", 666, 171, 52, 56, 0], [2, "1208", 710, 193, 52, 56, 0], [2, "1208", 754, 215, 52, 56, 0], [2, "1208", 844, 261, 52, 56, 0], [2, "1208", 890, 283, 52, 56, 0], [2, "1208", 491, 104, 52, 56, 2], [2, "1208", 471, 112, 52, 56, 2], [2, "1208", 358, 170, 52, 56, 2], [2, "1208", 319, 189, 52, 56, 2], [2, "1208", 290, 202, 52, 56, 2], [2, "1208", 952, 390, 52, 56, 2], [2, "1208", 952, 392, 52, 56, 2], [2, "1208", 910, 413, 52, 56, 2], [2, "1208", 874, 430, 52, 56, 2], [2, "1208", 832, 451, 52, 56, 2], [2, "3401", 193, 202, 44, 81, 2], [2, "3401", 149, 225, 44, 81, 2], [2, "3401", 105, 267, 44, 81, 2], [2, "3401", 105, 246, 44, 81, 2], [2, "3401", 61, 290, 44, 81, 2], [2, "3401", 61, 269, 44, 81, 2], [2, "3401", 16, 313, 44, 81, 2], [2, "3401", 16, 292, 44, 81, 2], [2, "3401", -28, 336, 44, 81, 2], [2, "3401", -28, 315, 44, 81, 2], [2, "3401", 238, 222, 44, 81, 0], [2, "3401", 238, 201, 44, 81, 0], [2, "3401", 283, 245, 44, 81, 0], [2, "3401", 283, 224, 44, 81, 0], [2, "3401", 372, 290, 44, 81, 0], [2, "3401", 372, 269, 44, 81, 0], [2, "3401", 327, 267, 44, 81, 0], [2, "3401", 327, 246, 44, 81, 0], [2, "3401", 415, 311, 44, 81, 0], [2, "3401", 415, 290, 44, 81, 0], [2, "3401", 542, 371, 44, 81, 0], [2, "3401", 542, 350, 44, 81, 0], [2, "3401", 646, 407, 44, 81, 0], [2, "3401", 689, 428, 44, 81, 0], [2, "3401", 734, 472, 44, 81, 0], [2, "3401", 734, 451, 44, 81, 0], [2, "3401", 778, 494, 44, 81, 0], [2, "3401", 778, 473, 44, 81, 0], [2, "3401", 823, 516, 44, 81, 0], [2, "3401", 823, 495, 44, 81, 0], [2, "3401", 866, 537, 44, 81, 0], [2, "3401", 866, 516, 44, 81, 0], [2, "3401", 911, 539, 44, 81, 0], [2, "3401", 955, 561, 44, 81, 0], [2, "3401", 573, 391, 44, 81, 0], [2, "3401", 573, 370, 44, 81, 0], [2, "3401", 616, 412, 44, 81, 0], [2, "3401", 616, 391, 44, 81, 0], [2, "682", 639, -23, 34, 35, 2], [2, "682", 613, -10, 34, 35, 2], [2, "682", 588, 2, 34, 35, 2], [2, "682", 561, 15, 34, 35, 2], [2, "682", 535, 27, 34, 35, 2], [2, "682", 536, 33, 34, 35, 0], [2, "682", 563, 46, 34, 35, 0], [2, "682", 589, 59, 34, 35, 0], [2, "682", 642, 86, 34, 35, 0], [2, "682", 669, 99, 34, 35, 0], [2, "682", 694, 112, 34, 35, 0], [2, "682", 747, 138, 34, 35, 0], [2, "682", 774, 151, 34, 35, 0], [2, "682", 799, 163, 34, 35, 0], [2, "617", 789, 0, 22, 43, 0], [2, "617", 779, -5, 22, 43, 0], [2, "617", 779, -36, 22, 43, 0], [2, "617", 789, -31, 22, 43, 0], [2, "617", 820, 16, 22, 43, 0], [2, "617", 810, 11, 22, 43, 0], [2, "617", 820, -15, 22, 43, 0], [2, "617", 820, -32, 22, 43, 0], [2, "617", 810, -37, 22, 43, 0], [2, "617", 841, 27, 22, 43, 0], [2, "617", 841, -4, 22, 43, 0], [2, "617", 851, 1, 22, 43, 0], [2, "617", 841, -21, 22, 43, 0], [2, "617", 882, 48, 22, 43, 0], [2, "617", 872, 43, 22, 43, 0], [2, "617", 872, 12, 22, 43, 0], [2, "617", 882, 17, 22, 43, 0], [2, "617", 882, 0, 22, 43, 0], [2, "617", 872, -5, 22, 43, 0], [2, "617", 903, 28, 22, 43, 0], [2, "617", 913, 16, 22, 43, 0], [2, "617", 903, 11, 22, 43, 0], [2, "617", 944, 49, 22, 43, 0], [2, "617", 944, 32, 22, 43, 0], [2, "617", 934, 27, 22, 43, 0], [2, "617", 965, 90, 22, 43, 0], [2, "617", 965, 59, 22, 43, 0], [2, "617", 975, 64, 22, 43, 0], [2, "617", 965, 42, 22, 43, 0], [2, "1208", 770, 14, 52, 56, 0], [2, "1208", 816, 38, 52, 56, 0], [2, "1208", 861, 60, 52, 56, 0], [2, "1208", 948, 104, 52, 56, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, 22, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 24, 25, -1, -1, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, 22, 22, -1, 22, 22, 17, 19, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 24, 25, 26, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, 36, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, 10, 10, 24, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, -1, -1, -1, 31, 30, 29, -1, -1, 10, 10, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, 22, 1, 2, -1, -1, -1, 31, 30, 29, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, 20, 19, 22, 16, 17, -1, -1, -1, -1, -1, -1, 4, 9, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, 20, 19, 13, 19, 29, 22, 22, -1, -1, -1, -1, -1, 22, -1, -1, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 24, 25, 30, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, 16, 16, 16, 13, 24, 25, 26, -1, 31, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, 34, 35, 4, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, 10, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, 24, 25, 26, 31, 30, 29, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 24, 25, 26, -1, -1, -1, -1, 31, 30, 29, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "3025", 419, 170, 92, 53, 2], [2, "1501", -29, 13, 50, 26, 0], [2, "1501", -3, 26, 50, 26, 0], [2, "1501", -29, 39, 50, 26, 0], [2, "1501", -29, 65, 50, 26, 0], [2, "1501", -3, 52, 50, 26, 0], [2, "1501", 23, 39, 50, 26, 0], [2, "1501", 11, -4, 50, 26, 0], [2, "1501", 37, -17, 50, 26, 0], [2, "1501", 37, 35, 50, 26, 0], [2, "1501", 63, 22, 50, 26, 0], [2, "1501", 89, 9, 50, 26, 0], [2, "1501", 483, -10, 50, 26, 0], [2, "1501", 509, -23, 50, 26, 0], [2, "1501", 509, 3, 50, 26, 0], [2, "1501", 483, 16, 50, 26, 0], [2, "1501", 509, 29, 50, 26, 0], [2, "1501", 535, 16, 50, 26, 0], [2, "163", 453, 363, 60, 33, 2], [2, "163", 503, 387, 60, 33, 2], [2, "688", 352, 363, 46, 24, 0], [2, "688", 314, 382, 46, 24, 0], [2, "688", 278, 401, 46, 24, 0], [2, "688", 240, 420, 46, 24, 0], [2, "688", 202, 439, 46, 24, 0], [2, "688", 164, 458, 46, 24, 0], [2, "688", 126, 477, 46, 24, 0], [2, "688", 88, 496, 46, 24, 0], [2, "688", 50, 515, 46, 24, 0], [2, "688", 18, 530, 46, 24, 0], [2, "688", -16, 550, 46, 24, 0], [2, "3025", 498, 338, 92, 53, 0], [2, "1501", -38, -1, 50, 26, 0], [2, "1501", -12, -14, 50, 26, 0], [2, "1501", 40, -14, 50, 26, 0], [2, "1501", 14, -1, 50, 26, 0], [2, "1501", -12, 12, 50, 26, 0], [2, "1501", 14, 25, 50, 26, 0], [2, "1501", 40, 12, 50, 26, 0], [2, "1501", 66, -1, 50, 26, 0], [2, "1501", 35, -19, 50, 26, 0], [2, "1501", 87, -19, 50, 26, 0], [2, "1501", 61, -6, 50, 26, 0], [2, "1501", 87, 7, 50, 26, 0], [2, "1501", 113, -6, 50, 26, 0], [2, "1501", 139, -19, 50, 26, 0], [2, "1501", 417, -16, 50, 26, 0], [2, "1501", 469, -16, 50, 26, 0], [2, "1501", 443, -3, 50, 26, 0], [2, "1501", 469, 10, 50, 26, 0], [2, "1501", 495, -3, 50, 26, 0], [2, "3616", 266, 16, 60, 32, 0], [2, "3616", 229, 34, 60, 32, 0], [2, "3616", 254, 46, 60, 32, 0], [2, "3616", 315, 41, 60, 32, 0], [2, "3616", 340, 53, 60, 32, 0], [2, "3616", 303, 71, 60, 32, 0], [2, "3616", 278, 59, 60, 32, 0], [2, "3616", 363, 65, 60, 32, 0], [2, "3616", 388, 77, 60, 32, 0], [2, "3616", 351, 95, 60, 32, 0], [2, "3616", 326, 83, 60, 32, 0], [2, "3616", 412, 90, 60, 32, 0], [2, "3616", 375, 108, 60, 32, 0], [2, "3616", 429, 99, 60, 32, 0], [2, "3616", 417, 129, 60, 32, 0], [2, "3616", 392, 117, 60, 32, 0], [2, "3616", 191, 53, 60, 32, 0], [2, "3616", 216, 65, 60, 32, 0], [2, "3616", 179, 83, 60, 32, 0], [2, "3616", 154, 71, 60, 32, 0], [2, "3616", 240, 78, 60, 32, 0], [2, "3616", 265, 90, 60, 32, 0], [2, "3616", 228, 108, 60, 32, 0], [2, "3616", 203, 96, 60, 32, 0], [2, "3616", 288, 102, 60, 32, 0], [2, "3616", 313, 114, 60, 32, 0], [2, "3616", 276, 132, 60, 32, 0], [2, "3616", 251, 120, 60, 32, 0], [2, "3616", 362, 139, 60, 32, 0], [2, "3616", 354, 136, 60, 32, 0], [2, "3616", 379, 148, 60, 32, 0], [2, "3616", 116, 90, 60, 32, 0], [2, "3616", 104, 120, 60, 32, 0], [2, "3616", 79, 108, 60, 32, 0], [2, "3616", 190, 127, 60, 32, 0], [2, "3616", 153, 145, 60, 32, 0], [2, "3616", 128, 133, 60, 32, 0], [2, "3616", 213, 139, 60, 32, 0], [2, "3616", 238, 151, 60, 32, 0], [2, "3616", 201, 169, 60, 32, 0], [2, "3616", 176, 157, 60, 32, 0], [2, "3616", 42, 126, 60, 32, 0], [2, "3616", 67, 138, 60, 32, 0], [2, "3616", 30, 156, 60, 32, 0], [2, "3616", 5, 144, 60, 32, 0], [2, "3616", 91, 151, 60, 32, 0], [2, "3616", 116, 163, 60, 32, 0], [2, "3616", 79, 181, 60, 32, 0], [2, "3616", 54, 169, 60, 32, 0], [2, "3616", 139, 175, 60, 32, 0], [2, "3616", 164, 187, 60, 32, 0], [2, "3616", 127, 205, 60, 32, 0], [2, "3616", 102, 193, 60, 32, 0], [2, "3616", -32, 163, 60, 32, 0], [2, "3616", -7, 175, 60, 32, 0], [2, "3616", -44, 193, 60, 32, 0], [2, "3616", 17, 188, 60, 32, 0], [2, "3616", 42, 200, 60, 32, 0], [2, "3616", 5, 218, 60, 32, 0], [2, "3616", -20, 206, 60, 32, 0], [2, "3616", 65, 212, 60, 32, 0], [2, "3616", 90, 224, 60, 32, 0], [2, "3616", 53, 242, 60, 32, 0], [2, "3616", 28, 230, 60, 32, 0], [2, "1501", 497, -8, 50, 26, 0], [2, "1501", 523, -21, 50, 26, 0], [2, "1501", 575, -21, 50, 26, 0], [2, "1501", 549, -8, 50, 26, 0], [2, "1501", 523, 5, 50, 26, 0], [2, "1501", 549, 18, 50, 26, 0], [2, "1501", 575, 5, 50, 26, 0], [2, "1501", 601, -8, 50, 26, 0], [2, "163", 831, 170, 60, 33, 2], [2, "163", 863, 186, 60, 33, 2], [2, "3025", 738, 42, 92, 53, 2], [2, "3025", 809, 70, 92, 53, 2], [2, "3025", 893, 125, 92, 53, 2], [2, "3025", 786, 45, 92, 53, 2], [2, "3615", 208, 280, 64, 33, 0], [2, "3615", 176, 295, 64, 33, 0], [2, "3615", 240, 297, 64, 33, 0], [2, "3615", 208, 312, 64, 33, 0], [2, "3615", 242, 329, 64, 33, 0], [2, "3615", 274, 314, 64, 33, 0], [2, "3615", 306, 331, 64, 33, 0], [2, "3615", 274, 346, 64, 33, 0], [2, "3615", 112, 326, 64, 33, 0], [2, "3615", 144, 311, 64, 33, 0], [2, "3615", 176, 328, 64, 33, 0], [2, "3615", 144, 343, 64, 33, 0], [2, "3615", 178, 360, 64, 33, 0], [2, "3615", 210, 345, 64, 33, 0], [2, "3615", 242, 362, 64, 33, 0], [2, "3615", 210, 377, 64, 33, 0], [2, "3615", 50, 356, 64, 33, 0], [2, "3615", 82, 341, 64, 33, 0], [2, "3615", 114, 358, 64, 33, 0], [2, "3615", 82, 373, 64, 33, 0], [2, "3615", 116, 390, 64, 33, 0], [2, "3615", 148, 375, 64, 33, 0], [2, "3615", 180, 392, 64, 33, 0], [2, "3615", 148, 407, 64, 33, 0], [2, "3615", -14, 386, 64, 33, 0], [2, "3615", 18, 371, 64, 33, 0], [2, "3615", 50, 388, 64, 33, 0], [2, "3615", 18, 403, 64, 33, 0], [2, "3615", 84, 405, 64, 33, 0], [2, "3615", 116, 422, 64, 33, 0], [2, "3615", 84, 437, 64, 33, 0], [2, "3615", -47, 402, 64, 33, 0], [2, "3615", -15, 419, 64, 33, 0], [2, "3615", -47, 434, 64, 33, 0], [2, "3615", -13, 451, 64, 33, 0], [2, "3615", 19, 436, 64, 33, 0], [2, "3615", 51, 453, 64, 33, 0], [2, "3615", 19, 468, 64, 33, 0], [2, "3615", -46, 467, 64, 33, 0], [2, "3615", -14, 484, 64, 33, 0], [2, "3615", -46, 499, 64, 33, 0], [2, "3615", 338, 348, 64, 33, 0], [2, "3615", 305, 363, 64, 33, 0], [2, "3615", 273, 378, 64, 33, 0], [2, "3615", 241, 392, 64, 33, 0], [2, "3615", 210, 409, 64, 33, 0], [2, "3615", 179, 424, 64, 33, 0], [2, "3615", 148, 438, 64, 33, 0], [2, "3615", 116, 454, 64, 33, 0], [2, "3615", 85, 469, 64, 33, 0], [2, "3615", 52, 485, 64, 33, 0], [2, "3615", 18, 499, 64, 33, 0], [2, "3615", -13, 516, 64, 33, 0], [2, "3615", -45, 530, 64, 33, 0], [2, "688", 116, 342, 46, 24, 2], [2, "688", 153, 361, 46, 24, 2], [2, "688", 176, 374, 46, 24, 2], [2, "688", 215, 393, 46, 24, 2], [2, "688", 242, 406, 46, 24, 2], [2, "688", 238, 286, 46, 24, 2], [2, "688", 276, 305, 46, 24, 2], [2, "688", 313, 324, 46, 24, 2], [2, "688", 119, 325, 46, 24, 0], [2, "688", 75, 347, 46, 24, 0], [2, "688", 38, 365, 46, 24, 0], [2, "688", 3, 383, 46, 24, 0], [2, "688", -33, 401, 46, 24, 0], [2, "688", 436, 134, 46, 24, 0], [2, "688", 398, 153, 46, 24, 0], [2, "688", 400, 367, 46, 24, 2], [2, "688", 520, 432, 46, 24, 2], [2, "688", 561, 451, 46, 24, 2], [2, "688", 599, 467, 46, 24, 2], [2, "688", 732, 532, 46, 24, 2], [2, "688", 766, 551, 46, 24, 2], [2, "688", 802, 569, 46, 24, 2], [2, "688", 839, 588, 46, 24, 2]]}, {"type": 2, "data": [63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 48, 48, 48, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 63, 48, 48, 48, 48, 48, 45, 46, 54, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 48, 48, 49, 60, 60, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 63, 48, 48, 48, 48, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 63, 63, 63, 63, 63, 48, 48, 63, 48, 48, 69, 70, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 76, 75, 48, 48, 63, 63, 63, 63, 63, 63, 63, 48, 48, 69, 70, 66, 67, 50, 61, 60, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 54, 50, 73, 76, 75, 48, 48, 48, 63, 63, 63, 63, 63, 63, 63, 66, 67, 50, 61, 60, 64, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 49, 45, 46, 54, 73, 66, 66, 76, 75, 48, 48, 48, 63, 63, 63, 63, 63, 63, 63, 64, 63, 63, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 48, 48, 49, 45, 46, 54, 55, 73, 72, 76, 75, 48, 48, 48, 63, 63, 63, 63, 63, 63, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 75, 48, 48, 48, 48, 49, 45, 46, 51, 50, 73, 72, 76, 69, 70, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 48, 48, 69, 74, 50, 50, 51, 55, 73, 66, 67, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 69, 70, 66, 67, 50, 53, 54, 51, 52, 50, 51, 61, 60, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 69, 70, 66, 67, 50, 50, 61, 60, 60, 45, 46, 53, 54, 68, 63, 72, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 67, 50, 50, 54, 44, 64, 63, 48, 48, 49, 46, 57, 68, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 77, 78, 77, 54, 50, 50, 61, 63, 63, 70, 75, 75, 75, 48, 49, 45, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 78, 77, 78, 77, 78, 77, 63, 63, 50, 64, 63, 63, 63, 63, 48, 48, 75, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 48, 48, 63, 63, 63, 63, 63, 63, 63, 79, 63, 63, 63, 63, 63, 63, 63, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 79, 79, 63, 79, 63, 63, 63, 63, 63, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 63, 63, 63, 63, 63, 63, 63, 63, 63, 79, 80, 79, 79, 79, 63, 63, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 63, 63, 63, 63, 63, 63, 63, 63, 79, 79, 79, 79, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 63, 63, 63, 63, 63, 63, 63, 63, 79, 79, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 78, 78, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}