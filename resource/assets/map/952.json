{"mW": 840, "mH": 720, "tW": 24, "tH": 24, "tiles": [["940_1", 0, 2, 1], ["937_1", 0, 2, 1], ["937_1", 3, 2, 1], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["709_3", 0, 2, 1], ["91", 1, 3, 2], ["91", 3, 3, 2], ["3011", 0, 3, 2], ["3011", 2, 3, 2], ["3011", 1, 3, 2], ["3011", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "268_3", 16, -20, 106, 82, 0], [2, "1222_1", 289, -72, 64, 80, 0], [2, "1197_1", 219, -30, 54, 44, 0], [2, "1222_1", 268, -60, 64, 80, 0], [2, "1222_1", 246, -50, 64, 80, 0], [2, "1230_1", 272, 634, 58, 34, 2], [2, "1228_1", 245, 601, 60, 75, 2], [2, "1228_1", 147, 604, 60, 75, 0], [2, "1227_1", 295, 619, 46, 62, 2], [2, "1228_1", 171, 616, 60, 75, 0], [2, "1197_1", 251, 655, 54, 44, 2], [2, "1228_1", 193, 625, 60, 75, 0], [2, "1228_1", 189, 631, 60, 75, 2], [2, "1228_1", 217, 633, 60, 75, 0], [2, "1197_1", 139, 666, 54, 44, 0], [2, "1228_1", 163, 641, 60, 75, 2], [2, "159_1", 706, 658, 76, 66, 0], [2, "87_2", -36, 12, 72, 57, 0], [2, "1228_1", 761, 58, 60, 75, 2], [2, "1197_1", 771, 136, 54, 44, 2], [2, "1228_1", 740, 68, 60, 75, 2], [2, "1228_1", 643, 66, 60, 75, 0], [2, "1228_1", 669, 80, 60, 75, 0], [2, "1228_1", 691, 89, 60, 75, 0], [2, "1228_1", 687, 95, 60, 75, 2], [2, "1228_1", 711, 104, 60, 75, 0], [2, "1197_1", 639, 127, 54, 44, 0], [2, "1228_1", 662, 104, 60, 75, 2], [2, "1227_1", 606, 93, 46, 62, 2], [2, "1225_2", 740, 43, 48, 44, 2], [2, "1225_2", 667, 52, 48, 44, 0], [2, "1229_2", 700, 68, 48, 39, 2], [2, "1226_2", 647, 84, 70, 47, 2], [2, "1228_1", 736, 115, 60, 75, 0], [2, "1230_1", 784, 116, 58, 34, 2], [2, "1226_2", 740, 87, 70, 47, 0], [2, "1227_1", 696, 128, 46, 62, 2], [2, "1204_1", 763, 272, 38, 51, 0], [2, "1204_1", 797, 273, 38, 51, 2], [2, "1221_1", 752, 200, 50, 88, 0], [2, "1221_1", 800, 200, 50, 88, 2], [2, "1222_1", 432, 677, 64, 80, 2], [2, "1222_1", 451, 688, 64, 80, 2], [2, "1222_1", 471, 698, 64, 80, 2], [2, "1222_1", 490, 708, 64, 80, 2], [2, "1225_3", 463, 660, 48, 44, 0], [2, "1229_3", 504, 684, 48, 39, 0], [2, "1228_1", 129, 601, 60, 75, 0], [2, "1227_1", 216, 647, 46, 62, 2], [2, "1230_1", 112, 651, 58, 34, 0], [2, "1225_2", 232, 587, 48, 44, 2], [2, "1225_2", 161, 578, 48, 44, 0], [2, "1229_2", 202, 602, 48, 39, 0], [2, "1226_2", 233, 618, 70, 47, 0], [2, "1226_2", 148, 625, 70, 47, 2], [2, "238_1", 808, 619, 30, 42, 0], [2, "1197_1", 717, 671, 54, 44, 0], [2, "1204_1", 634, 692, 38, 51, 0], [2, "1204_1", 668, 693, 38, 51, 2], [2, "1208_1", 715, 691, 52, 56, 0], [2, "1208_1", 767, 691, 52, 56, 2], [2, "1224_1", 757, 642, 66, 59, 2], [2, "1224_1", 765, 651, 66, 59, 2], [2, "1230_3", 691, 658, 58, 34, 0], [2, "1221_1", 623, 623, 50, 88, 0], [2, "1221_1", 671, 623, 50, 88, 2], [2, "1224_1", 741, 650, 66, 59, 2], [2, "1224_1", 746, 661, 66, 59, 2], [2, "1223_1", 749, 631, 56, 37, 2], [2, "1223_1", 733, 637, 56, 37, 2], [2, "1222_1", 788, -32, 64, 80, 2], [2, "1222_1", 807, -21, 64, 80, 2], [2, "1222_1", 827, -11, 64, 80, 2], [2, "1225_3", 819, -49, 48, 44, 0], [2, "268_3", 544, 672, 106, 82, 0], [2, "9_1", 487, 630, 20, 16, 0], [2, "1205_1", 213, -3, 42, 62, 0], [2, "1205_1", 238, 33, 42, 62, 0], [2, "1222_1", 202, 22, 64, 80, 0], [2, "1222_1", 181, 34, 64, 80, 0], [2, "1222_1", 160, 45, 64, 80, 0], [2, "1216_1", 65, 5, 42, 121, 2], [2, "1222_1", 139, 57, 64, 80, 0], [2, "1224_1", 182, 83, 66, 59, 0], [2, "1222_1", 119, 69, 64, 80, 0], [2, "1205_1", 123, 89, 42, 62, 0], [2, "1205_1", 136, 98, 42, 62, 0], [2, "1225_3", 177, 5, 48, 44, 2], [2, "1229_3", 117, 38, 48, 39, 2], [2, "1216_1", 108, 5, 42, 121, 0], [2, "1223_1", 203, 72, 56, 37, 0], [2, "1220_1", 227, 83, 48, 39, 0], [2, "1196_1", 139, -9, 60, 74, 0], [2, "9_1", 311, 114, 20, 16, 2], [2, "1133_1", 333, 678, 24, 34, 2], [2, "3009", 333, 664, 32, 36, 0], [2, "3009", 305, 101, 32, 36, 2], [2, "3009", 480, 619, 32, 36, 0]]}, {"type": 4, "obj": [[2, "3009", 339, 3, 32, 36, 0], [2, "3009", 305, 14, 32, 36, 0], [2, "3009", 813, 57, 32, 36, 0], [2, "89_2", 9, 36, 48, 95, 0], [2, "3008", 242, 113, 36, 20, 0], [2, "3009", 282, 99, 32, 36, 0], [2, "1133_1", 282, 113, 24, 34, 2], [2, "3007", 178, 154, 14, 26, 0], [2, "3009", 169, 149, 32, 36, 0], [2, "159_1", 755, 121, 76, 66, 2], [2, "1_1", 307, 77, 40, 111, 2], [2, "3009", 604, 155, 32, 36, 2], [2, "1133_1", 612, 169, 24, 34, 0], [2, "3009", 652, 168, 32, 36, 2], [2, "3009", 777, 172, 32, 36, 0], [2, "3009", 733, 285, 32, 36, 2], [2, "1133_1", 743, 297, 24, 34, 0], [2, "1133_1", 744, 297, 24, 34, 0], [2, "3008", 763, 318, 36, 20, 2], [2, "3008", 804, 318, 36, 20, 0], [2, "3009", 804, 344, 32, 36, 0], [2, "3009", 396, 387, 32, 36, 2], [2, "1435_1", 336, 291, 104, 146, 0], [2, "1_1", 471, 594, 40, 111, 0], [2, "3009", 146, 696, 32, 36, 0]]}, {"type": 3, "obj": [[2, "930_1", 321, 401, 42, 22, 2], [2, "930_1", 274, 423, 42, 22, 2], [2, "939_1", 637, 138, 34, 19, 2], [2, "930_1", 621, 134, 42, 22, 2], [2, "930_1", 584, 152, 42, 22, 2], [2, "939_1", 613, 150, 34, 19, 2], [2, "939_1", 589, 163, 34, 19, 2], [2, "939_1", 567, 173, 34, 19, 2], [2, "930_1", 551, 169, 42, 22, 2], [2, "930_1", 514, 187, 42, 22, 2], [2, "939_1", 543, 185, 34, 19, 2], [2, "939_1", 519, 198, 34, 19, 2], [2, "938_1", 526, 201, 38, 22, 0], [2, "938_1", 554, 215, 38, 22, 0], [2, "938_1", 583, 230, 38, 22, 0], [2, "479_1", 565, 226, 36, 18, 2], [2, "930_1", 517, 208, 42, 22, 0], [2, "930_1", 554, 227, 42, 22, 0], [2, "930_1", 493, 675, 42, 22, 0], [2, "930_1", 528, 693, 42, 22, 0], [2, "938_1", 483, 678, 38, 22, 0], [2, "938_1", 511, 692, 38, 22, 0], [2, "938_1", 540, 707, 38, 22, 0], [2, "930_1", 563, 712, 42, 22, 0], [2, "422_3", 310, 78, 16, 14, 0], [2, "470_4", 296, 13, 18, 62, 2], [2, "1207_1", 206, -30, 22, 81, 0], [2, "470_4", 221, -13, 18, 62, 0], [2, "470_4", 237, -6, 18, 62, 0], [2, "470_4", 255, 3, 18, 62, 0], [2, "1194_2", 224, 3, 46, 60, 2], [2, "470_4", 270, 9, 18, 62, 0], [2, "1207_1", 278, 1, 22, 81, 0], [2, "470_4", 313, 4, 18, 62, 2], [2, "470_4", 330, -5, 18, 62, 2], [2, "470_4", 348, -14, 18, 62, 2], [2, "1207_1", 369, -45, 22, 81, 0], [2, "470_4", 358, -19, 18, 62, 2], [2, "14_6", 305, 54, 32, 30, 0], [2, "18_4", 337, 28, 88, 64, 0], [2, "72_1", 380, 9, 42, 44, 0], [2, "938_1", -19, 170, 38, 22, 0], [2, "930_1", -34, 174, 42, 22, 0], [2, "939_1", 584, -32, 34, 19, 2], [2, "930_1", 580, -25, 42, 22, 2], [2, "930_1", 543, -7, 42, 22, 2], [2, "939_1", 560, -20, 34, 19, 2], [2, "939_1", 536, -7, 34, 19, 2], [2, "930_1", -21, 345, 42, 22, 0], [2, "930_1", 14, 366, 42, 22, 0], [2, "938_1", -18, 356, 38, 22, 0], [2, "938_1", 11, 371, 38, 22, 0], [2, "939_1", 512, 5, 34, 19, 2], [2, "930_1", 508, 12, 42, 22, 2], [2, "930_1", 471, 30, 42, 22, 2], [2, "939_1", 488, 17, 34, 19, 2], [2, "939_1", 464, 30, 34, 19, 2], [2, "939_1", 440, 42, 34, 19, 2], [2, "930_1", 436, 49, 42, 22, 2], [2, "930_1", 399, 67, 42, 22, 2], [2, "939_1", 416, 54, 34, 19, 2], [2, "939_1", 392, 67, 34, 19, 2], [2, "939_1", 368, 78, 34, 19, 2], [2, "930_1", 364, 85, 42, 22, 2], [2, "930_1", 327, 103, 42, 22, 2], [2, "939_1", 344, 90, 34, 19, 2], [2, "939_1", 320, 103, 34, 19, 2], [2, "930_1", 331, 123, 42, 22, 0], [2, "930_1", 368, 142, 42, 22, 0], [2, "939_1", 476, 682, 34, 19, 2], [2, "930_1", 460, 678, 42, 22, 2], [2, "930_1", 424, 696, 42, 22, 2], [2, "939_1", 452, 694, 34, 19, 2], [2, "939_1", 428, 708, 34, 19, 2], [2, "939_1", 404, 720, 34, 19, 2], [2, "938_1", 306, 120, 38, 22, 0], [2, "938_1", 334, 134, 38, 22, 0], [2, "938_1", 363, 149, 38, 22, 0], [2, "938_1", 714, 371, 38, 22, 0], [2, "938_1", 739, 384, 38, 22, 0], [2, "938_1", 768, 399, 38, 22, 0], [2, "938_1", 797, 413, 38, 22, 0], [2, "938_1", 825, 427, 38, 22, 0], [2, "938_1", 387, 394, 38, 22, 0], [2, "938_1", 415, 408, 38, 22, 0], [2, "938_1", 444, 423, 38, 22, 0], [2, "938_1", 473, 437, 38, 22, 0], [2, "938_1", 353, 392, 38, 22, 2], [2, "938_1", 326, 406, 38, 22, 2], [2, "938_1", 297, 421, 38, 22, 2], [2, "938_1", 268, 435, 38, 22, 2], [2, "938_1", 469, 449, 38, 22, 2], [2, "938_1", 442, 463, 38, 22, 2], [2, "938_1", 413, 478, 38, 22, 2], [2, "938_1", 384, 492, 38, 22, 2], [2, "938_1", 259, 450, 38, 22, 0], [2, "938_1", 268, 454, 38, 22, 0], [2, "938_1", 296, 468, 38, 22, 0], [2, "938_1", 325, 483, 38, 22, 0], [2, "938_1", 354, 497, 38, 22, 0], [2, "422_3", 327, 407, 16, 14, 0], [2, "508_2", 436, 438, 60, 37, 0], [2, "508_2", 379, 391, 60, 37, 2], [2, "508_2", 425, 414, 60, 37, 2], [2, "422_3", 332, 170, 16, 14, 0], [2, "479_1", 331, 189, 36, 18, 0], [2, "874_1", 806, 154, 16, 39, 2], [2, "866_2", 630, 233, 42, 26, 2], [2, "238_1", 268, 41, 30, 42, 0], [2, "508_2", 332, 395, 60, 37, 0], [2, "508_2", 289, 417, 60, 37, 0], [2, "479_1", 474, 453, 36, 18, 0], [2, "479_1", 344, 497, 36, 18, 2], [2, "479_1", 295, 473, 36, 18, 2], [2, "930_1", 393, 712, 42, 22, 2], [2, "939_1", 289, 196, 34, 19, 2], [2, "930_1", 285, 203, 42, 22, 2], [2, "930_1", 248, 221, 42, 22, 2], [2, "939_1", 265, 208, 34, 19, 2], [2, "939_1", 241, 221, 34, 19, 2], [2, "939_1", 217, 232, 34, 19, 2], [2, "930_1", 213, 239, 42, 22, 2], [2, "930_1", 176, 257, 42, 22, 2], [2, "939_1", 193, 244, 34, 19, 2], [2, "939_1", 169, 257, 34, 19, 2], [2, "939_1", 361, 159, 34, 19, 2], [2, "930_1", 357, 166, 42, 22, 2], [2, "930_1", 320, 184, 42, 22, 2], [2, "939_1", 337, 171, 34, 19, 2], [2, "939_1", 313, 184, 34, 19, 2], [2, "939_1", 13, 378, 34, 19, 2], [2, "930_1", 9, 385, 42, 22, 2], [2, "930_1", -28, 403, 42, 22, 2], [2, "939_1", -11, 390, 34, 19, 2], [2, "479_1", 711, 375, 36, 18, 2], [2, "479_1", 787, 415, 36, 18, 2], [2, "479_1", 421, 717, 36, 18, 0], [2, "930_1", 707, 378, 42, 22, 0], [2, "930_1", 744, 397, 42, 22, 0], [2, "930_1", 782, 416, 42, 22, 0], [2, "930_1", 819, 435, 42, 22, 0], [2, "470_4", 139, 140, 18, 62, 0], [2, "1207_1", 251, 92, 22, 81, 2], [2, "470_4", 238, 118, 18, 62, 2], [2, "470_4", 172, 150, 18, 62, 2], [2, "470_4", 188, 141, 18, 62, 2], [2, "1194_2", 201, 134, 46, 60, 0], [2, "1207_1", 154, 137, 22, 81, 2], [2, "1192_1", 207, 115, 24, 25, 0], [2, "1192_1", 188, 124, 24, 25, 0], [2, "1192_1", 170, 133, 24, 25, 0], [2, "1217_1", 192, 184, 30, 30, 0], [2, "1213_1", 174, 170, 26, 62, 0], [2, "1192_1", 202, 128, 24, 25, 2], [2, "1217_1", 252, 158, 30, 30, 0], [2, "1218_1", 266, 113, 28, 82, 0], [2, "866_2", 244, 193, 42, 26, 0], [2, "1218_1", 215, 138, 28, 82, 0], [2, "1215_1", 79, 160, 32, 52, 2], [2, "1215_1", 112, 159, 32, 52, 0], [2, "1193_1", 801, 153, 34, 71, 0], [2, "470_4", 763, 165, 18, 62, 2], [2, "470_4", 776, 162, 18, 62, 2], [2, "470_4", 644, 149, 18, 62, 0], [2, "1212_1", 609, 162, 40, 58, 0], [2, "1211_1", 615, 145, 30, 35, 0], [2, "470_4", 794, 154, 18, 62, 2], [2, "470_4", 735, 169, 18, 62, 0], [2, "470_4", 748, 171, 18, 62, 0], [2, "1208_1", 712, 195, 52, 56, 0], [2, "1208_1", 764, 187, 52, 56, 2], [2, "1200_1", 731, 178, 24, 32, 2], [2, "1203_1", 644, 184, 24, 55, 0], [2, "1209_1", 644, 158, 66, 95, 0], [2, "1212_1", 700, 199, 40, 58, 0], [2, "1211_1", 705, 181, 30, 35, 0], [2, "1206_1", 669, 211, 14, 42, 0], [2, "1206_1", 629, 196, 14, 42, 2], [2, "1210_1", 626, 168, 62, 59, 0], [2, "1193_1", 742, 183, 34, 71, 0], [2, "24_2", 33, 168, 28, 38, 2], [2, "24_2", 828, 187, 28, 38, 2], [2, "1193_1", 117, 678, 34, 71, 2], [2, "470_4", 140, 685, 18, 62, 0], [2, "470_4", 151, 693, 18, 62, 0], [2, "470_4", 245, 691, 18, 62, 2], [2, "470_4", 282, 674, 18, 62, 2], [2, "1212_1", 295, 692, 40, 58, 2], [2, "1211_1", 301, 672, 30, 35, 2], [2, "470_4", 167, 697, 18, 62, 0], [2, "470_4", 204, 697, 18, 62, 2], [2, "470_4", 188, 705, 18, 62, 2], [2, "1208_1", 185, 718, 52, 56, 2], [2, "1208_1", 139, 711, 52, 56, 0], [2, "1200_1", 198, 703, 24, 32, 0], [2, "1203_1", 281, 699, 24, 55, 2], [2, "1209_1", 246, 680, 66, 95, 2], [2, "1212_1", 214, 719, 40, 58, 2], [2, "1211_1", 220, 701, 30, 35, 2], [2, "1206_1", 267, 734, 14, 42, 2], [2, "1206_1", 305, 718, 14, 42, 0], [2, "1210_1", 260, 690, 62, 59, 2], [2, "1193_1", 175, 708, 34, 71, 0], [2, "1219_1", 222, 102, 78, 46, 0], [2, "1215_1", 111, 121, 32, 52, 0], [2, "1214_1", 164, 139, 34, 55, 0], [2, "1215_1", 78, 119, 32, 52, 2], [2, "938_1", 598, 238, 38, 22, 0], [2, "938_1", 626, 252, 38, 22, 0], [2, "938_1", 655, 267, 38, 22, 0], [2, "479_1", 637, 263, 36, 18, 2], [2, "479_1", 588, 240, 36, 18, 2], [2, "930_1", 583, 241, 42, 22, 0], [2, "930_1", 620, 260, 42, 22, 0], [2, "930_1", 657, 278, 42, 22, 0], [2, "938_1", 684, 281, 38, 22, 0], [2, "938_1", 709, 294, 38, 22, 0], [2, "479_1", 681, 285, 36, 18, 2], [2, "930_1", 677, 288, 42, 22, 0], [2, "930_1", 714, 307, 42, 22, 0], [2, "938_1", 739, 308, 38, 22, 0], [2, "938_1", 764, 321, 38, 22, 0], [2, "479_1", 736, 312, 36, 18, 2], [2, "930_1", 732, 315, 42, 22, 0], [2, "938_1", 8, 184, 38, 22, 0], [2, "938_1", 33, 197, 38, 22, 0], [2, "938_1", 62, 212, 38, 22, 0], [2, "938_1", 91, 226, 38, 22, 0], [2, "938_1", 119, 240, 38, 22, 0], [2, "938_1", 148, 254, 38, 22, 0], [2, "479_1", 130, 251, 36, 18, 2], [2, "479_1", 5, 188, 36, 18, 2], [2, "479_1", 81, 228, 36, 18, 2], [2, "930_1", 1, 191, 42, 22, 0], [2, "930_1", 38, 210, 42, 22, 0], [2, "930_1", 76, 229, 42, 22, 0], [2, "930_1", 113, 248, 42, 22, 0], [2, "930_1", 139, 261, 42, 22, 0], [2, "939_1", 761, 337, 34, 19, 2], [2, "930_1", 745, 333, 42, 22, 2], [2, "930_1", 708, 351, 42, 22, 2], [2, "939_1", 737, 349, 34, 19, 2], [2, "939_1", 713, 362, 34, 19, 2], [2, "939_1", 832, 42, 34, 19, 2], [2, "930_1", 816, 38, 42, 22, 2], [2, "930_1", 779, 56, 42, 22, 2], [2, "939_1", 808, 54, 34, 19, 2], [2, "939_1", 784, 67, 34, 19, 2], [2, "422_3", 702, 367, 16, 14, 0], [2, "470_4", 761, 326, 18, 62, 0], [2, "470_4", 783, 336, 18, 62, 0], [2, "470_4", 767, 329, 18, 62, 0], [2, "470_4", 801, 336, 18, 62, 2], [2, "470_4", 818, 328, 18, 62, 2], [2, "863_1", 833, 353, 16, 33, 0], [2, "863_1", 833, 326, 16, 33, 0], [2, "898_1", 784, 323, 16, 25, 0], [2, "899_1", 757, 300, 44, 22, 0], [2, "898_1", 771, 317, 16, 25, 0], [2, "898_1", 799, 323, 16, 25, 2], [2, "899_1", 776, 309, 44, 22, 0], [2, "898_1", 813, 316, 16, 25, 2], [2, "898_1", 828, 309, 16, 25, 2], [2, "899_1", 778, 290, 44, 22, 0], [2, "899_1", 796, 299, 44, 22, 0], [2, "898_1", 758, 311, 16, 25, 0], [2, "24_2", 445, 13, 28, 38, 0], [2, "24_2", 485, -7, 28, 38, 0], [2, "24_2", 825, 32, 28, 38, 0], [2, "24_2", 787, 50, 28, 38, 0], [2, "24_2", -4, 149, 28, 38, 2], [2, "24_2", -2, 346, 28, 38, 2], [2, "24_2", -4, 362, 28, 38, 0], [2, "1207_1", 796, 14, 22, 81, 0], [2, "470_4", 812, 29, 18, 62, 0], [2, "470_4", 827, 39, 18, 62, 0], [2, "470_4", 813, 41, 18, 62, 0], [2, "470_4", 826, 50, 18, 62, 0], [2, "470_4", 845, 46, 18, 62, 0], [2, "470_4", 844, 58, 18, 62, 0], [2, "90_3", 817, 86, 28, 36, 0], [2, "3010", 808, 337, 22, 44, 0], [2, "3010", 308, 12, 22, 44, 0], [2, "3010", 343, -2, 22, 44, 0], [2, "72_1", 348, 18, 42, 44, 0], [2, "3010", 150, 693, 22, 44, 2], [2, "3010", 780, 167, 22, 44, 0], [2, "3010", 820, 50, 22, 44, 2], [2, "174_4", 128, 239, 68, 33, 0], [2, "174_4", 524, 630, 68, 33, 0], [2, "174_4", 19, 677, 68, 33, 0], [2, "174_4", 526, 204, 68, 33, 2], [2, "174_4", 0, 390, 68, 33, 0], [2, "174_4", 783, 397, 68, 33, 2], [2, "422_3", 417, 707, 16, 14, 0], [2, "422_3", 373, 81, 16, 14, 0], [2, "422_3", 63, 201, 16, 14, 2], [2, "422_3", 31, 369, 16, 14, 2], [2, "898_1", 453, 432, 16, 25, 2], [2, "938_1", 312, 445, 38, 22, 0], [2, "898_1", 332, 447, 16, 25, 0], [2, "899_1", 306, 425, 44, 22, 0], [2, "898_1", 319, 441, 16, 25, 0], [2, "898_1", 387, 467, 16, 25, 2], [2, "899_1", 324, 433, 44, 22, 0], [2, "898_1", 402, 459, 16, 25, 2], [2, "898_1", 417, 451, 16, 25, 2], [2, "899_1", 326, 415, 44, 22, 0], [2, "899_1", 344, 423, 44, 22, 0], [2, "898_1", 306, 435, 16, 25, 0], [2, "898_1", 346, 454, 16, 25, 0], [2, "898_1", 360, 461, 16, 25, 0], [2, "898_1", 373, 467, 16, 25, 0], [2, "898_1", 432, 443, 16, 25, 2], [2, "898_1", 447, 435, 16, 25, 2], [2, "899_1", 346, 444, 44, 22, 0], [2, "899_1", 367, 434, 44, 22, 0], [2, "899_1", 385, 443, 44, 22, 0], [2, "899_1", 365, 453, 44, 22, 0], [2, "899_1", 345, 405, 44, 22, 0], [2, "899_1", 366, 395, 44, 22, 0], [2, "899_1", 384, 404, 44, 22, 0], [2, "899_1", 364, 414, 44, 22, 0], [2, "899_1", 385, 424, 44, 22, 0], [2, "899_1", 406, 414, 44, 22, 0], [2, "899_1", 424, 423, 44, 22, 0], [2, "899_1", 404, 433, 44, 22, 0], [2, "508_2", 423, 445, 60, 37, 0], [2, "508_2", 378, 467, 60, 37, 0], [2, "508_2", 277, 441, 60, 37, 2], [2, "508_2", 324, 465, 60, 37, 2], [2, "508_2", 335, 470, 60, 37, 2], [2, "422_3", 380, 492, 16, 14, 0], [2, "3008", 308, 444, 36, 20, 2], [2, "3008", 345, 462, 36, 20, 2], [2, "3008", 432, 441, 36, 20, 0], [2, "3008", 391, 461, 36, 20, 0], [2, "3009", 447, 414, 32, 36, 0], [2, "174_4", 313, 419, 68, 33, 2], [2, "174_4", 496, 453, 68, 33, 2], [2, "3009", 298, 415, 32, 36, 0], [2, "3009", 373, 448, 32, 36, 0], [2, "866_2", 373, 424, 42, 26, 0], [2, "866_2", 357, 425, 42, 26, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, 33, 33, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 12, 7, 8, 12, 11, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 33, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, 21, -1, -1, -1, -1, -1, -1, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 12, 11, -1, -1, -1, -1, 30, 30, 6, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 12, 11, 30, 11, 30, 7, 8, -1, -1, -1, -1, -1, -1, -1, 52, 53, 54, -1, -1, -1, -1, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 12, 7, 8, -1, 67, 68, 69, -1, -1, -1, -1, -1, 55, 56, 57, 58, -1, -1, -1, 40, 41, 44, 43, 44, 50, 49, 50, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 65, 66, -1, -1, -1, 52, 53, 63, 62, 74, 73, -1, -1, -1, -1, 40, 41, 40, 41, 51, 50, 47, 50, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 68, 68, 68, 69, 70, -1, -1, -1, 75, 74, 73, -1, -1, 48, 47, -1, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 65, 65, 65, 66, -1, -1, -1, -1, 72, 71, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 41, 48, 47, -1, 52, 53, 53, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 56, 56, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 33, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 63, 62, 62, 62, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 62, 62, 62, 62, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 53, 54, -1, -1, -1, -1, -1, -1, -1, 62, 62, 62, 62, 62, 57, 59, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 56, 57, 59, 59, 58, -1, -1, -1, -1, 62, 62, 62, 62, 68, 74, 73, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 65, 75, 74, 62, 61, -1, -1, -1, -1, 68, 69, 65, 75, 69, 71, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 53, 63, 62, 62, 57, 59, 59, 58, -1, 65, 66, -1, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 56, 62, 62, 62, 68, 68, 62, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 65, 71, 75, 74, 62, 69, 71, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 71, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 27, -1, -1, -1, -1, -1, -1, -1, 33, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [1, 0, 1, 0, 1, 0, 1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 1, 0, 1, 0, 1, 0, 1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 0, 1, 0, 1, 0, 1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, -1, -1, -1, 0, 1, 0, 1, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, -1, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, -1, -1, 0, 1, 0, 1, 0, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, -1, -1, -1, -1, 0, 5, 4, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 4, 5, 4, 5, 4, 5, 4, 5, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 1, 0, 1, 0, 1, 0, 1, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 5, 4, 5, 4, 5, 0, 1, 0, 1, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, -1, -1, -1, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, -1, -1, -1, 1, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 39, 38, 39, 38, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 38, 39, 38, 39, 38, 39, 38, 39, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 38, 39, 38, 39, 38, 39, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 39, 38, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 5, 4, 5, 4, 5, 4, 4, 5, 4, 0, 1, 0, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}