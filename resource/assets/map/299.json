{"mW": 960, "mH": 672, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2]], "layers": [{"type": 2, "data": [47, 47, 47, 47, 39, 47, 39, 40, 37, 47, 40, 41, 49, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 41, 42, 44, 41, 49, 48, 39, 31, 31, 47, 47, 31, 47, 47, 47, 47, 47, 47, 47, 47, 35, 43, 35, 36, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 49, 37, 39, 40, 43, 49, 43, 48, 47, 47, 47, 47, 31, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, 44, 43, 37, 47, 45, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 47, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 32, 23, 29, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 47, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 35, 43, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 39, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, 29, 20, 28, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 32, 31, 30, 28, 47, 24, 32, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 39, 47, 24, 32, 47, 47, 47, 47, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 48, 47, 47, 47, 47, 47, 47, 47, 47, 47, 24, 19, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 48, 47, 47, 47, 47, 47, 47, 47, 47, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 39, 40, 41, 41, 41, 48, 47, 29, 26, 28, 27, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 36, -1, -1, -1, 44, 43, 41, 28, 32, 39, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, -1, -1, -1, -1, 44, 43, 48, 31, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 35, 35, 36, -1, 28, 27, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 27, 27, 20, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 19, 27, 33, 33, 32, 47, 23, 47, 24, 27, 26, 28, 47, 29, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 23, 31, 31, 31, 31, 47, 47, 23, 47, 47, 29, 32, 47, 47, 23, 29, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 39, 40, 37, 47, 23, 47, 47, 47, 47, 23, 23, 23, 23, 47, 47, 23, 24, 20, -1, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, 34, 35, 36, 38, 47, 47, 47, 47, 47, 47, 47, 47, 23, 47, 23, 23, 23, 23, 24, 27, 32, 31, 30, -1, -1, 28, 27, 27, 27, 33, 33, 27, 27, 32, 31, 30, -1, -1, -1, -1, -1, 28, 33, 21, 47, 47, 47, 23, 23, 23, 23, 23, 23]}, {"type": 4, "obj": [[2, "1310_2", 60, 76, 18, 29, 2], [2, "1310_2", 180, 357, 18, 29, 0], [2, "1310_2", 80, 375, 18, 29, 0], [2, "1310_2", 58, 378, 18, 29, 2], [2, "1308_2", 111, 374, 22, 37, 0], [2, "1308_2", 90, 379, 22, 37, 0], [2, "1310_2", 32, 393, 18, 29, 2], [2, "1310_2", 157, 399, 18, 29, 0], [2, "1310_2", 178, 399, 18, 29, 0], [2, "1308_2", 132, 400, 22, 37, 0], [2, "1309_2", 12, 406, 20, 32, 2], [2, "1310_2", 223, 428, 18, 29, 2], [2, "1310_2", 239, 433, 18, 29, 0], [2, "1308_2", 203, 426, 22, 37, 0], [2, "1310_2", 306, 457, 18, 29, 2], [2, "1308_2", 290, 452, 22, 37, 0]]}, {"type": 3, "obj": [[2, "208_3", 895, 438, 78, 40, 3], [2, "214_3", 910, 551, 54, 40, 2], [2, "1150", 793, 10, 48, 85, 0], [2, "1149", 813, 369, 40, 82, 0], [2, "208_3", 30, 554, 78, 40, 1], [2, "216", 124, 148, 46, 46, 2], [2, "216", 485, 243, 46, 46, 0], [2, "1150", 37, 115, 48, 85, 0], [2, "1150", 889, 22, 48, 85, 2], [2, "1148", 478, 315, 46, 108, 0], [2, "1148", 519, 332, 46, 108, 0], [2, "1150", 556, 357, 48, 85, 2], [2, "1149", 320, -12, 40, 82, 0], [2, "1150", 286, -6, 48, 85, 0], [2, "1149", 611, 150, 40, 82, 0], [2, "1149", 581, 149, 40, 82, 2], [2, "1149", 736, 40, 40, 82, 2], [2, "1149", 921, 54, 40, 82, 2], [2, "1149", 371, 501, 40, 82, 2], [2, "1149", 401, 500, 40, 82, 0], [2, "1150", 376, 393, 48, 85, 0], [2, "1150", 10, 174, 48, 85, 0], [2, "214_3", 486, 231, 54, 40, 2], [2, "214_3", 896, 235, 54, 40, 2], [2, "214_3", 449, 356, 54, 40, 2], [2, "216", 527, 343, 46, 46, 0], [2, "216", 415, 381, 46, 46, 0], [2, "216", 536, 117, 46, 46, 0], [2, "213_3", 489, 124, 64, 45, 2], [2, "214_3", 297, 152, 54, 40, 2], [2, "213_3", 348, 131, 64, 45, 0], [2, "213_3", 120, 136, 64, 45, 0], [2, "213_3", 64, 120, 64, 45, 0], [2, "213_3", 858, 319, 64, 45, 2], [2, "1149", 589, 380, 40, 82, 2], [2, "1150", 763, 39, 48, 85, 0], [2, "216", 630, 144, 46, 46, 2], [2, "214_3", 643, 173, 54, 40, 0], [2, "214_3", 671, 48, 54, 40, 2], [2, "214_3", 395, 23, 54, 40, 0], [2, "213_3", 182, 140, 64, 45, 0], [2, "213_3", 235, 160, 64, 45, 0], [2, "214_3", 255, 171, 54, 40, 2], [2, "214_3", 349, 163, 54, 40, 0], [2, "216", 71, 142, 46, 46, 0], [2, "216", 181, 153, 46, 46, 0], [2, "1310_2", 168, 149, 18, 29, 0], [2, "1310_2", 225, 160, 18, 29, 0], [2, "1309_2", 393, 153, 20, 32, 0], [2, "1310_2", 48, 168, 18, 29, 0], [2, "1150", 160, 3, 48, 85, 0], [2, "1149", 0, 221, 40, 82, 0], [2, "1150", 253, -5, 48, 85, 2], [2, "1150", 234, -5, 48, 85, 0], [2, "1150", 192, 0, 48, 85, 2], [2, "1308_2", 188, 6, 22, 37, 0], [2, "1310_2", 361, -12, 18, 29, 0], [2, "1154", 233, 25, 28, 51, 0], [2, "1153", 897, 45, 34, 54, 2], [2, "214_3", 31, 448, 54, 40, 0], [2, "216", 821, 334, 46, 46, 2], [2, "214_3", 781, 420, 54, 40, 2], [2, "214_3", 599, 262, 54, 40, 0], [2, "216", 479, 296, 46, 46, 2], [2, "213_3", 365, 358, 64, 45, 2], [2, "214_3", 371, 402, 54, 40, 2], [2, "214_3", 868, 544, 54, 40, 2], [2, "1147", 500, 444, 50, 42, 0], [2, "1149", 609, 377, 40, 82, 0], [2, "214_3", 635, 436, 54, 40, 0], [2, "214_3", 297, 467, 54, 40, 2], [2, "216", 338, 467, 46, 46, 0], [2, "1152", 608, 465, 38, 26, 0], [2, "1152", 565, 444, 38, 26, 0], [2, "214_3", 31, 504, 54, 40, 0], [2, "213_3", 72, 523, 64, 45, 0], [2, "1149", -17, 433, 40, 82, 0], [2, "1150", 855, 5, 48, 85, 2], [2, "1147", 831, 62, 50, 42, 0], [2, "214_3", 590, 379, 54, 40, 0], [2, "1150", 31, 55, 48, 85, 0], [2, "1147", 540, 236, 50, 42, 0], [2, "1147", 76, 211, 50, 42, 0], [2, "216", 21, 374, 46, 46, 0], [2, "214_3", 676, 615, 54, 40, 2], [2, "213_3", 622, 617, 64, 45, 2], [2, "1152", 322, 533, 38, 26, 0], [2, "425_2", 465, 407, 30, 36, 0], [2, "425_2", 807, 92, 30, 36, 0], [2, "426_2", 131, 208, 26, 22, 0], [2, "1149", 906, 242, 40, 82, 0], [2, "1154", 613, 185, 28, 51, 0], [2, "1147", 871, 378, 50, 42, 0], [2, "426_2", 896, 417, 26, 22, 0], [2, "214_3", 579, 635, 54, 40, 2], [2, "213_3", 533, 638, 64, 45, 2], [2, "313_2", 830, 148, 70, 44, 0], [2, "313_2", 687, 285, 70, 44, 2], [2, "313_2", 171, 559, 70, 44, 2], [2, "207_2", 394, 165, 38, 27, 2], [2, "208_3", 140, 45, 78, 40, 2], [2, "181_3", 72, 18, 104, 100, 2], [2, "208_3", 121, 116, 78, 40, 0], [2, "207_2", 194, 140, 38, 27, 0], [2, "152_3", 232, 142, 76, 40, 0], [2, "208_3", 348, 126, 78, 40, 3], [2, "208_3", 208, 66, 78, 40, 3], [2, "207_2", 281, 73, 38, 27, 0], [2, "152_3", 478, 110, 76, 40, 2], [2, "208_3", 305, 54, 78, 40, 0], [2, "205_3", 258, 212, 54, 40, 2], [2, "208_3", 192, 210, 78, 40, 1], [2, "208_3", 193, 456, 78, 40, 2], [2, "208_3", 130, 304, 78, 40, 0], [2, "208_3", 31, 426, 78, 40, 1], [2, "208_3", 786, 314, 78, 40, 2], [2, "208_3", 600, 490, 78, 40, 0], [2, "208_3", 460, 217, 78, 40, 2], [2, "208_3", 430, 325, 78, 40, 2], [2, "208_3", 734, 106, 78, 40, 0], [2, "208_3", 815, 104, 78, 40, 2], [2, "208_3", 645, 32, 78, 40, 3], [2, "208_3", 874, 206, 78, 40, 2], [2, "208_3", 525, 327, 78, 40, 0], [2, "208_3", 621, 225, 78, 40, 2], [2, "152_3", 643, 152, 76, 40, 0], [2, "208_3", 880, 117, 78, 40, 0], [2, "207_2", 801, 121, 38, 27, 0], [2, "208_3", 890, 143, 78, 40, 3], [2, "205_3", 913, 177, 54, 40, 2], [2, "207_2", 654, 49, 38, 27, 2], [2, "208_3", 594, 11, 78, 40, 0], [2, "207_2", 667, 65, 38, 27, 0], [2, "208_3", 555, 11, 78, 40, 2], [2, "207_2", 542, 22, 38, 27, 0], [2, "208_3", 550, 103, 78, 40, 0], [2, "1144", 624, 87, 114, 70, 0], [2, "208_3", 385, 44, 78, 40, 2], [2, "207_2", 359, 65, 38, 27, 2], [2, "208_3", 453, 10, 78, 40, 0], [2, "205_3", 514, 13, 54, 40, 0], [2, "152_3", 391, 3, 76, 40, 0], [2, "206", 287, 126, 66, 40, 0], [2, "206", 62, 104, 66, 40, 0], [2, "206", 663, 188, 66, 40, 0], [2, "208_3", 759, 391, 78, 40, 2], [2, "208_3", 757, 433, 78, 40, 3], [2, "208_3", 599, 245, 78, 40, 1], [2, "205_3", 158, 208, 54, 40, 0], [2, "207_2", 157, 330, 38, 27, 0], [2, "208_3", 131, 236, 78, 40, 1], [2, "152_3", 102, 270, 76, 40, 2], [2, "164_1", 90, 272, 60, 30, 0], [2, "165_1", 117, 229, 42, 37, 0], [2, "166_2", 163, 196, 30, 35, 0], [2, "207_2", 491, 170, 38, 27, 2], [2, "205_3", 497, 186, 54, 40, 2], [2, "166_2", 524, 168, 30, 35, 2], [2, "165_1", 509, 205, 42, 37, 2], [2, "207_2", 483, 147, 38, 27, 2], [2, "208_3", 349, 189, 78, 40, 2], [2, "208_3", 327, 205, 78, 40, 0], [2, "207_2", 305, 217, 38, 27, 0], [2, "208_3", 448, 285, 78, 40, 2], [2, "205_3", 470, 253, 54, 40, 0], [2, "165_1", 840, 93, 42, 37, 0], [2, "166_2", 894, 108, 30, 35, 2], [2, "166_2", 593, 230, 30, 35, 0], [2, "208_3", 347, 375, 78, 40, 0], [2, "205_3", 413, 357, 54, 40, 0], [2, "152_3", 353, 344, 76, 40, 2], [2, "208_3", 315, 408, 78, 40, 2], [2, "208_3", 288, 443, 78, 40, 0], [2, "205_3", 340, 438, 54, 40, 2], [2, "313_2", 186, 259, 70, 44, 0], [2, "313_2", 207, 271, 70, 44, 2], [2, "1149", 270, 460, 40, 82, 0], [2, "1149", 241, 460, 40, 82, 2], [2, "208_3", 241, 539, 78, 40, 0], [2, "206", 771, 353, 66, 40, 0], [2, "1151", 884, 161, 38, 33, 0], [2, "1151", 257, 254, 38, 33, 0], [2, "208_3", 585, 280, 78, 40, 2], [2, "205_3", 544, 308, 54, 40, 2], [2, "206", 587, 354, 66, 40, 2], [2, "208_3", 829, 428, 78, 40, 2], [2, "208_3", 765, 526, 78, 40, 2], [2, "1151", 662, 64, 38, 33, 2], [2, "208_3", 630, 381, 78, 40, 0], [2, "208_3", 634, 415, 78, 40, 1], [2, "208_3", 667, 438, 78, 40, 0], [2, "426_2", 571, 469, 26, 22, 0], [2, "208_3", 636, 472, 78, 40, 2], [2, "214_3", 207, 504, 54, 40, 2], [2, "206", 196, 484, 66, 40, 2], [2, "1144", 128, 518, 114, 70, 0], [2, "208_3", 93, 401, 78, 40, 2], [2, "205_3", 60, 456, 54, 40, 2], [2, "208_3", 31, 488, 78, 40, 1], [2, "206", 74, 513, 66, 40, 2], [2, "1151", 115, 552, 38, 33, 0], [2, "208_3", 542, 516, 78, 40, 1], [2, "208_3", 415, 561, 78, 40, 1], [2, "208_3", 652, 594, 78, 40, 2], [2, "208_3", 524, 624, 78, 40, 2], [2, "206", 618, 602, 66, 40, 0], [2, "1151", 632, 255, 38, 33, 0], [2, "208_3", 311, 544, 78, 40, 2], [2, "206", 367, 555, 66, 40, 2], [2, "207_2", 516, 536, 38, 27, 0], [2, "313_2", 473, 550, 70, 44, 2], [2, "1310_2", 427, 502, 18, 29, 2], [2, "1310_2", 717, 19, 18, 29, 0], [2, "1310_2", 839, 23, 18, 29, 0], [2, "1310_2", 641, -2, 18, 29, 0], [2, "1308_2", 728, 23, 22, 37, 0], [2, "1308_2", 891, 334, 22, 37, 0], [2, "1310_2", 874, 333, 18, 29, 0], [2, "1310_2", 925, 356, 18, 29, 0], [2, "1308_2", 938, 346, 22, 37, 0], [2, "207_2", 590, 625, 38, 27, 2], [2, "313_2", 656, 261, 70, 44, 2], [2, "313_2", 649, 296, 70, 44, 2], [2, "313_2", 176, 294, 70, 44, 2], [2, "313_2", 644, 338, 70, 44, 2], [2, "313_2", 627, 567, 70, 44, 2], [2, "166_2", 643, 409, 30, 35, 2], [2, "166_2", 28, 484, 30, 35, 0], [2, "165_1", 225, 478, 42, 37, 2], [2, "1305_2", 723, 228, 20, 14, 0], [2, "1302_3", 693, 207, 40, 29, 0], [2, "1303_2", 693, 238, 34, 20, 0], [2, "1302_3", 78, 562, 40, 29, 0], [2, "1303_2", 103, 584, 34, 20, 0], [2, "1302_3", 230, 227, 40, 29, 0], [2, "1303_2", 220, 254, 34, 20, 0], [2, "1305_2", 125, 587, 20, 14, 0], [2, "955_4", 820, 269, 20, 18, 0], [2, "955_4", 668, 524, 20, 18, 0], [2, "955_4", 336, 587, 20, 18, 0], [2, "955_4", 191, 465, 20, 18, 0], [2, "955_4", 474, 217, 20, 18, 0], [2, "955_4", 353, 94, 20, 18, 0], [2, "1303_2", 772, 455, 34, 20, 0], [2, "1303_2", 460, 584, 34, 20, 2], [2, "1303_2", 308, 379, 34, 20, 2], [2, "955_4", 99, 449, 20, 18, 0], [2, "1303_2", 321, 94, 34, 20, 2], [2, "1303_2", 621, 51, 34, 20, 2], [2, "1303_2", 622, 318, 34, 20, 2], [2, "955_4", 588, 340, 20, 18, 0], [2, "426_2", 574, 211, 26, 22, 0], [2, "1147", 437, 475, 50, 42, 2], [2, "1301_2", 454, 10, 24, 49, 0], [2, "1151", 491, 169, 38, 33, 2], [2, "1151", 228, 643, 38, 33, 0], [2, "1303_2", 467, 42, 34, 20, 2], [2, "1153", 19, 198, 34, 54, 0], [2, "1153", 331, 1, 34, 54, 0], [2, "1153", -6, 447, 34, 54, 0], [2, "208_3", 863, 522, 78, 40, 0], [2, "207_2", 932, 532, 38, 27, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, 66, 66, 66, 66, 73, 72, 71, 70, 69, 68, 70, 69, 73, 72, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 67, 73, 72, -1, -1, -1, 70, 69, 68, 57, 56, 68, 57, 56, 70, 69, 68, 66, 66, -1, 66, 72, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, 70, 69, -1, -1, 74, 75, 75, 75, 81, 80, -1, -1, -1, -1, -1, 70, 69, -1, -1, 63, 69, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 75, 76, -1, -1, -1, -1, -1, -1, -1, 74, 75, 85, 78, 78, 91, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 78, 79, 81, 81, 80, -1, -1, -1, 74, 85, 78, 90, 91, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 57, -1, -1, -1, -1, -1, -1, -1, 86, 93, 87, 87, 87, 88, -1, -1, -1, 86, 97, 90, 91, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 61, 60, -1, -1, -1, -1, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 97, 95, -1, -1, 72, 72, 66, 67, -1, -1, -1, -1, 74, 75, 75, 81, 75, 76, -1, -1, -1, 66, -1, 66, 66, 67, 73, 72, 66, 66, 66, 72, -1, -1, 66, 67, 73, 72, -1, -1, 86, 92, -1, 72, 66, 67, 73, 64, -1, -1, -1, -1, 86, 97, 78, 78, 78, 79, 80, -1, -1, 66, -1, 66, 63, 64, 70, 69, 66, 66, 69, 73, 66, 67, 63, 64, 70, 69, -1, -1, -1, -1, -1, -1, 63, 64, 65, 72, 60, 60, -1, -1, -1, 89, 78, 78, 78, 84, 95, -1, -1, 66, -1, 63, 64, -1, -1, 65, 66, 66, -1, 70, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 73, 72, 71, -1, -1, 89, 78, 78, 90, 91, 92, -1, -1, 66, 66, 71, -1, -1, -1, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 81, 80, -1, -1, 57, 56, -1, -1, -1, 70, 69, 68, -1, -1, 89, 96, 91, 87, 88, -1, -1, 66, 66, 63, 68, 58, 57, 57, 61, -1, -1, -1, -1, 74, 75, 76, 74, 75, 75, 85, 84, 83, -1, -1, 66, 67, -1, 50, 51, 66, -1, -1, -1, 82, 85, 78, 95, -1, -1, -1, 66, 66, 66, -1, 66, 66, -1, -1, -1, -1, -1, -1, 74, 77, 78, 79, 85, 78, 78, 78, 96, 95, -1, -1, 63, 64, 50, 61, 54, -1, -1, 74, 75, 85, 78, 90, 95, -1, -1, 92, 66, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 78, 78, 78, 78, 90, 91, 87, 93, 92, -1, -1, -1, -1, 53, 54, -1, -1, -1, 77, 96, 96, 96, 91, 92, -1, -1, -1, -1, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 90, 78, 78, 91, 87, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 96, 96, 96, 95, -1, -1, -1, 66, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 97, 78, 96, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 97, 90, 91, 92, -1, -1, 66, 66, 73, 72, 66, 54, 66, 66, 66, 66, 66, -1, -1, -1, -1, 86, 78, 91, 92, -1, -1, -1, 66, 72, 71, 71, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 92, -1, -1, 66, 67, 63, 70, 73, 72, 54, 66, 66, 66, 66, 66, 72, 71, -1, -1, 78, 78, 88, -1, -1, -1, -1, 66, 69, 68, -1, -1, -1, -1, -1, -1, 66, -1, -1, -1, -1, -1, -1, -1, 63, 64, -1, -1, 73, 72, 66, 67, 64, -1, -1, 62, 69, 68, 50, 51, 52, -1, -1, -1, -1, -1, 66, 63, 64, -1, -1, -1, -1, 50, 51, 66, 66, -1, -1, -1, -1, -1, -1, -1, 60, 59, -1, -1, 70, 69, 63, 64, 66, -1, -1, -1, -1, -1, 53, 54, 55, -1, -1, -1, -1, -1, 66, 55, 57, 56, -1, -1, -1, 53, 54, 66, 66, 54, 54, -1, 74, 75, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, 66, -1, -1, 82, 81, 80, -1, -1, -1, -1, 66, 66, 66, 66, 66, 66, 69, 68, -1, -1, -1, 62, 73, 66, 66, 54, 54, -1, 77, 78, 96, 95, -1, -1, 74, 75, 81, 80, 58, 57, 66, -1, -1, 77, 84, 83, -1, -1, -1, -1, 66, 67, 69, 69, 68, 68, -1, -1, -1, -1, 58, 57, 61, -1, -1, -1, 74, 75, 85, 91, 93, 92, -1, -1, 86, 87, 93, 92, 61, 54, 54, -1, -1, 78, 78, -1, -1, 54, 66, 66, 66, 71, -1, -1, -1, -1, 57, 56, -1, 50, 51, 66, -1, -1, 74, 75, 85, 78, 91, 92, -1, -1, -1, -1, 55, 61, 60, -1, 66, 54, 54, -1, -1, -1, -1, 74, 75, 76, -1, -1, 66, 55, 57, 56, 66, -1, 66, 55, 57, 56, -1, -1, -1, -1, 86, 97, 78, 93, 92, -1, -1, -1, -1, -1, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 78, 79, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 81, 85, 96, 78, 78, 79, 75, 80, 82, 81, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, 84, 91, 96, 87, 87, 97, 78, 79, 85, 78, 84, 79, 81, 80, -1, -1, -1, 53, -1, -1, -1, 0, 1, 73, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 88, -1, -1, -1, 86, 87, 93, 93, 93, 93, 93, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, 4, 70, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, 0, 1, 2, 0, 1, 2, 2, -1, 0, 1, 2, 0, 1, 0, 1, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, 3, 4, 5, 3, 4, 5, 5, 0, 3, 4, 5, 3, 4, 3, 4, 13, 14, 8, 6, 7, 8, 6, 7, 8, 6, 3, 4, 5, 2, 0, 0, 1, 0, 1, 2, -1, -1, -1, -1, -1, 6, 7, 8, 6, 7, 8, 8, 3, 6, 7, 8, 6, 7, 6, 7, 16, 17, 17, 10, 9, 10, 11, 9, 10, 11, 6, 7, 0, 1, 2, 3, 4, 3, 4, 5, 0, 1, -1, -1, -1, 9, 10, 11, 9, 9, 10, 10, 11, 7, 8, 10, 11, 11, 9, 10, 9, 9, 12, 13, 12, 13, 14, 12, 13, 14, 14, 6, 3, 4, 0, 1, 2, 6, 7, 8, 3, 4, -1, -1, -1, 12, 13, 14, 12, 12, 13, 9, 10, 9, 12, 13, 14, 14, 12, 13, 9, 10, 11, 16, 15, 16, 17, 15, 16, 17, 17, -1, 6, 7, 3, 4, 5, 2, 0, 1, 6, 3, 0, 1, 2, 15, 16, 17, 15, 15, 16, 12, 13, 12, 15, 16, 17, 17, 15, 16, 9, 10, 11, 0, 1, 2, 0, 1, 2, -1, 9, 10, 11, 9, 6, 7, 8, 10, 11, 11, 5, 6, 3, 0, 1, 2, 0, 1, 2, 0, 1, 10, 11, 15, 16, 0, 1, 1, 2, 9, 12, 13, 14, 0, 1, 2, 3, 0, 1, 2, 12, 13, 9, 12, 13, 14, 15, 16, 15, 16, 17, 1, 6, 3, 4, 5, 3, 4, 5, 3, 4, 5, 5, 0, 0, 1, 2, 2, 5, 12, 15, 16, 17, 3, 4, 5, 6, 3, 4, 5, 15, 16, 12, 15, 16, 17, 12, 9, 10, 11, 11, 1, 3, 6, 7, 8, 6, 7, 8, 6, 7, 8, 8, 3, 3, 4, 5, 5, 9, 10, 11, 17, 14, 6, 0, 1, 2, 2, 7, 0, 9, 10, 11, 11, 11, 12, 15, 9, 10, 11, 11, 4, 6, 7, 8, 0, 6, 7, 8, 9, 10, 11, 10, 6, 6, 7, 8, 8, 12, 13, 14, 10, 11, 14, 3, 4, 5, 0, 1, 3, 12, 13, 14, 14, 14, 9, 10, 11, 13, 14, 14, 7, 0, 1, 2, 3, 4, 5, 9, 9, 10, 11, 11, 14, 16, 17, 10, 11, 15, 16, 17, 13, 14, 0, 0, 1, 2, 0, 1, 2, 15, 16, 17, 17, 11, 12, 13, 9, 10, 11, 1, 2, 3, 4, 5, 6, 7, 8, 12, 12, 9, 10, 11, 17, 11, 12, 13, 14, 12, 13, 14, 16, 17, 0, 1, 2, 5, 3, 4, 9, 15, 16, 17, 17, 9, 9, 10, 11, 13, 14, 4, 5, 6, 7, 8, 0, 1, 2, 16, 17, 12, 9, 10, 11, 14, 9, 10, 11, 10, 11, 10, 11, 17, 3, 4, 5, 8, 6, 9, 10, 11, 14, 12, 9, 10, 11, 13, 13, 14, 17, 1, 2, -1, -1, -1, 3, 4, 5, 16, 17, 17, 12, 13, 9, 10, 12, 13, 14, 13, 14, 10, 11, 2, 6, 7, 8, 10, 11, 12, 13, 14, 17, 9, 10, 11, 14, 16, 16, 17, 3, 4, 5, -1, -1, -1, 6, 7, 8, 4, 16, 17, 15, 16, 12, 13, 15, 16, 17, 13, 14, 13, 14, 5, 6, 7, 8, 13, 14, 15, 16, 17, 11, 12, 13, 14, 17, 3, 4, 6, 6, 7, 8, -1, -1, 0, 1, 0, 3, 3, 13, 14, 17, 17, 15, 16, 17, 12, 15, 16, 17, 6, 0, 0, 1, 2, 2, 16, 17, 9, 10, 11, 14, 15, 16, 17, 1, 2, 7, 0, 1, 0, 1, 1, 2, 3, 0, 3, 6, 6, 16, 17, 11, 16, 17, 16, 17, 15, 16, 17, 0, 1, 2, 3, 4, 5, 5, 0, 1, 2, 13, 14, 9, 10, 16, 10, 11, 5, 1, 2, 4, 3, 4, 4, 5, 6, 3, 6, 7, 7, 13, 13, 14, 17, 9, 10, 11, 12, 13, 14, 0, 1, 0, 1, 2, 8, 8, 3, 4, 5, 9, 10, 11, 11, 16, 13, 14, 8, 4, 5, 7, 6, 7, 7, 8, 13, 14, 14, 13, 14, 16, 17, 13, 14, 12, 13, 10, 11, 1, 2, 3, 4, 3, 4, 5, 0, 1, 2, 0, 1, 2, 13, 14, 14, 10, 10, 11, 6, 7, 8, 0, 1, 2, 3, 4, 16, 17, 17, 16, 17, 14, 13, 13, 14, 15, 16, 13, 14, 4, 5, 6, 7, 6, 7, 8, 3, 4, 5, 3, 4, 5, 2, 17, 9, 13, 13, 14, 10, 9, 10, 11, 9, 10, 6, 7, 6, 7, 16, 17, 9, 10, 13, 14, 17, 2, 0, 1, 2, 2, 0, 1, 2, 0, 1, 2, 6, 7, 8, 6, 7, 8, 2, 11, 12, 15, 16, 12, 9, 9, 10, 11, 9, 10, 0, 1, 2, 9, 10, 13, 14, 13, 16, 17, 4, 5, 3, 4, 5, 5, 3, 4, 5, 3, 4, 5, 5, 7, 8, 9, 10, 11, 9, 10, 11, 11, 17, 15, 12, 12, 13, 14, 12, 13, 3, 4, 5, 12, 13, 16, 17, 16, 17, 6, 7, 8, 6, 7, 8, 8, 6, 7, 8, 6, 7, 8, 8, 10, 11, 12, 13, 14, 12, 13, 14, 14, 16, 17, 15, 15, 16, 17, 15, 16, 6, 7, 8, 15, 13, 14, 16, 17, 9, 10, 11, 8, 8, 6, 7, 8, 6, 7, 8, 7, 8, 9, 12, 9, 10, 15, 16, 17, 15, 16, 17, 16, 17, -1, 3, 4, 5, 7, 8, -1, -1, 9, 10, 11, 16, 17, 11, 9, 12, 13, 14, 14, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 10, 12, 13, 12, 13, 14, 17, 0, 1, 2, -1, -1, 6, 7, 8, -1, -1, -1, -1, 12, 13, 14, 9, 10, 11, 12, 9, 10, 11, 11, 14, 12, 9, 10, 11, 9, 10, 11, 13, 14, 13, 15, 16, 15, 16, 17, 2, 3, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, 17, 12, 13, 14, 15, 9, 10, 11, 14, 17, 15, 12, 13, 14, 12, 13, 14, 16, 17, 16, 17, 16, 16, 17, 4, 5, 6, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, 9, 10, 12, 13, 14, 17, 15, 16, 15, 16, 17, 15, 16, 17, 17, -1, 0, 1, 2, 7, 8, 7, 8, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}