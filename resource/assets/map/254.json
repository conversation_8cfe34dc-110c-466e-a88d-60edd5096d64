{"mW": 792, "mH": 720, "tW": 24, "tH": 24, "tiles": [["106_3", 0, 3, 3], ["255", 0, 1, 1], ["304_1", 0, 3, 2], ["304_1", 2, 3, 2], ["304_1", 1, 3, 2], ["304_1", 3, 3, 2], ["490", 0, 3, 2], ["490", 2, 3, 2], ["490", 1, 3, 2], ["490", 3, 3, 2], ["491", 0, 3, 2], ["491", 2, 3, 2], ["491", 1, 3, 2], ["491", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "88_1", 728, 98, 88, 61, 0], [2, "88_1", 658, 89, 88, 61, 0], [2, "268_1", 673, 51, 106, 82, 0], [2, "88_1", 89, -43, 88, 61, 2], [2, "263", 96, 37, 34, 34, 0], [2, "88_1", 431, 690, 88, 61, 0], [2, "88_1", 348, 687, 88, 61, 0], [2, "268_1", 380, 641, 106, 82, 0], [2, "88_1", 216, 202, 88, 61, 0], [2, "88_1", 124, 189, 88, 61, 0], [2, "268_1", 168, 145, 106, 82, 0], [2, "88_1", 86, 146, 88, 61, 0], [2, "88_1", 36, 182, 88, 61, 0], [2, "88_1", -33, 190, 88, 61, 0], [2, "88_1", -1, 149, 88, 61, 0], [2, "88_1", 663, 485, 88, 61, 0], [2, "88_1", 744, 349, 88, 61, 2], [2, "88_1", 740, 395, 88, 61, 0], [2, "268_1", 708, 446, 106, 82, 2], [2, "88_1", 344, -34, 88, 61, 0], [2, "268_1", 396, 425, 106, 82, 0], [2, "88_1", 401, 394, 88, 61, 0], [2, "88_1", 370, 468, 88, 61, 0], [2, "88_1", 454, 454, 88, 61, 0], [2, "88_1", 19, 535, 88, 61, 0], [2, "88_1", 56, 465, 88, 61, 0], [2, "268_1", 76, 500, 106, 82, 2], [2, "88_1", 45, 501, 88, 61, 0], [2, "268_1", -27, 626, 106, 82, 2], [2, "268_1", 8, 667, 106, 82, 2], [2, "88_1", -31, 686, 88, 61, 0], [2, "88_1", 193, 690, 88, 61, 0], [2, "268_1", 716, 630, 106, 82, 0], [2, "268_1", 380, 641, 106, 82, 0], [2, "268_1", 687, 672, 106, 82, 0]]}, {"type": 4, "obj": [[2, "89", 150, -44, 48, 95, 0], [2, "4_2", 409, -56, 122, 119, 2], [2, "244", 152, 26, 20, 38, 0], [2, "245", 139, 44, 22, 22, 2], [2, "88_1", 414, 139, 88, 61, 0], [2, "89", 715, 118, 48, 95, 2], [2, "88_1", 387, 178, 88, 61, 0], [2, "88_1", 448, 190, 88, 61, 0], [2, "89", 82, 188, 48, 95, 2], [2, "89", 84, 191, 48, 95, 0], [2, "89", -12, 215, 48, 95, 2], [2, "331", 166, 204, 104, 108, 0], [2, "89", 432, 489, 48, 95, 2], [2, "89", 724, 518, 48, 95, 2], [2, "89", 81, 555, 48, 95, 0]]}, {"type": 3, "obj": [[2, "214_1", 206, 525, 54, 40, 2], [2, "214_1", 170, 514, 54, 40, 2], [2, "214_1", 444, 139, 54, 40, 2], [2, "214_1", 439, 76, 54, 40, 2], [2, "597", -11, 370, 34, 26, 2], [2, "305", 244, 295, 30, 24, 0], [2, "253", 490, 42, 92, 53, 0], [2, "214_1", 232, 287, 54, 40, 2], [2, "214_1", 214, 315, 54, 40, 2], [2, "245", 62, 250, 22, 22, 2], [2, "245", 43, 251, 22, 22, 0], [2, "422", 465, 408, 16, 14, 0], [2, "326", 774, 244, 18, 14, 0], [2, "21", 132, 268, 28, 24, 2], [2, "24", 768, 166, 28, 38, 2], [2, "208_1", 423, -13, 78, 40, 3], [2, "244", 91, 24, 20, 38, 0], [2, "245", 104, 42, 22, 22, 0], [2, "219_1", 98, 43, 36, 30, 2], [2, "174", 108, 162, 68, 33, 0], [2, "219_1", 353, 445, 36, 30, 2], [2, "244", 17, 246, 38, 20, 5], [2, "245", 18, 258, 22, 22, 2], [2, "219_1", 352, 207, 36, 30, 2], [2, "24", 651, 492, 28, 38, 0], [2, "328", 471, 403, 32, 29, 0], [2, "329", 617, 492, 42, 37, 2], [2, "90", 756, 171, 28, 36, 0], [2, "205_1", 436, 2, 54, 40, 2], [2, "208_1", 205, 272, 78, 40, 3], [2, "205_1", 212, 296, 54, 40, 2], [2, "326", 549, 417, 18, 14, 0], [2, "327", 560, 407, 30, 22, 0], [2, "326", 148, 449, 18, 14, 0], [2, "219_1", 65, 241, 36, 30, 0], [2, "219_1", 514, 401, 36, 30, 0], [2, "219_1", 590, 390, 36, 30, 2], [2, "244", 120, 43, 20, 38, 0], [2, "208_1", 203, 325, 78, 40, 0], [2, "14_1", 61, 271, 32, 30, 0], [2, "14_1", 34, 273, 32, 30, 0], [2, "14_1", 46, 255, 32, 30, 0], [2, "597", 11, 360, 34, 26, 0], [2, "596", -3, 353, 14, 15, 0], [2, "598", 21, 348, 18, 22, 0], [2, "263", 247, 336, 34, 34, 2], [2, "21", 557, 272, 28, 24, 2], [2, "263", 531, 256, 34, 34, 0], [2, "90", 521, 258, 28, 36, 0], [2, "90", 494, 277, 36, 28, 5], [2, "420", 537, 289, 16, 13, 0], [2, "208_1", 168, 500, 78, 40, 0], [2, "664", 689, 161, 52, 51, 0], [2, "679", 696, 168, 36, 32, 0], [2, "665", 708, 178, 30, 34, 0], [2, "24", 759, -15, 28, 38, 2], [2, "21", 732, -4, 28, 24, 2], [2, "205_1", 438, 54, 54, 40, 2], [2, "208_1", 413, 83, 78, 40, 0], [2, "208_1", 423, 109, 78, 40, 2], [2, "205_1", 436, 144, 54, 40, 2], [2, "89", 445, 205, 48, 95, 2], [2, "244", 468, 94, 20, 38, 0], [2, "245", 459, 111, 22, 22, 2], [2, "244", 448, 118, 20, 38, 0], [2, "244", 440, 67, 20, 38, 0], [2, "245", 452, 82, 22, 22, 0], [2, "208_1", 718, 498, 78, 40, 2], [2, "208_1", 654, 528, 78, 40, 1], [2, "205_1", 206, 508, 54, 40, 2], [2, "205_1", 217, 542, 54, 40, 0]]}, {"type": 2, "data": [81, 80, 80, 81, 80, 79, -1, -1, 70, 71, 71, 71, 81, 80, 79, 94, 90, 89, 89, 89, 88, -1, -1, -1, -1, 58, 80, 80, 80, 79, -1, -1, -1, 78, 77, 76, 78, 77, 80, 80, -1, -1, -1, -1, -1, 78, 77, 76, -1, 97, 104, 104, 104, 91, -1, -1, -1, -1, 61, 51, 57, 67, 82, 83, 89, 88, -1, -1, -1, -1, 82, 83, 84, 82, 83, 89, 88, -1, -1, -1, -1, -1, 102, 101, 101, 101, 96, -1, -1, 58, 65, 51, 48, 49, 67, 102, 94, 101, 100, -1, -1, -1, -1, 94, 95, 96, 94, 95, 101, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 58, 51, 53, 47, 48, 34, 45, 79, -1, -1, -1, -1, -1, -1, -1, -1, 66, 65, 65, 65, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 51, 48, 42, 41, 41, 45, 77, 76, -1, -1, -1, -1, -1, -1, 58, 59, 69, 51, 53, 57, 63, 64, -1, -1, 66, 65, 65, 64, -1, -1, -1, -1, 61, 39, 40, 49, 77, 76, -1, -1, -1, -1, -1, -1, -1, 58, 58, 69, 51, 47, 52, 9, 54, 57, 67, -1, -1, 73, 51, 57, 67, -1, -1, -1, -1, -1, 73, 39, 45, -1, -1, -1, -1, -1, -1, -1, 90, 89, 61, 51, 53, 52, 42, 41, 35, 36, 49, 67, -1, 58, 69, 39, 45, 63, -1, 82, -1, -1, -1, 70, 71, 76, -1, -1, -1, -1, -1, -1, 89, 93, 92, 61, 43, 9, 42, 45, 50, 80, 39, 45, -1, -1, 61, 80, 80, 80, 79, -1, 85, 82, 83, 84, -1, -1, -1, 90, 89, 88, -1, -1, 82, 83, 89, 98, 70, 39, 35, 45, 75, 75, 77, 77, 76, -1, 58, 51, 81, 80, 80, 79, -1, -1, 94, 95, 96, -1, -1, -1, 102, 101, 100, -1, -1, 94, 95, 101, 95, 89, 78, 77, 90, 89, 88, -1, 82, 83, 84, 58, 43, 46, 57, 67, 74, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 83, 89, 88, -1, 92, 98, 99, 89, 98, 91, -1, 97, 86, 87, 61, 39, 40, 49, 50, 74, 67, 82, 89, 89, 88, -1, 89, 88, -1, -1, -1, -1, 94, 95, 101, 100, -1, 98, 99, 95, 95, 95, 96, -1, 102, 101, 95, 96, 61, 43, 46, 47, 57, 79, 94, 95, 101, 101, 101, 101, 100, 66, 65, 64, 66, 65, -1, -1, -1, -1, 95, 96, -1, -1, -1, -1, -1, -1, 66, 65, 64, 70, 39, 41, 40, 54, 57, 64, -1, -1, -1, -1, -1, -1, 70, 81, -1, 69, 51, 47, 66, 65, 64, -1, -1, -1, -1, -1, -1, -1, -1, 78, 77, 76, 76, 70, 77, 43, 9, 49, 67, 90, 89, 88, -1, -1, -1, -1, 82, 83, 73, 39, 40, 46, 57, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 39, 36, 49, 90, 93, 98, 87, 88, -1, -1, 90, 93, 99, 100, 61, 39, 40, 37, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 88, 73, 78, 39, 45, 79, 94, 95, 100, 100, 82, 83, 98, 98, 100, -1, 66, 51, 52, 49, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 100, 78, 77, 78, 77, 76, -1, -1, -1, -1, -1, 101, 101, 100, -1, 66, 51, 52, 34, 45, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 59, 59, 74, 67, 64, 58, -1, -1, -1, -1, -1, -1, -1, -1, 58, 59, 51, 47, 48, 42, 45, 101, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 51, 57, 74, 74, 64, 61, 51, 57, 64, -1, -1, -1, -1, -1, 51, 47, 48, 9, 34, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 43, 46, 47, 57, 74, 51, 48, 49, 79, -1, -1, -1, -1, 58, 39, 40, 34, 35, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 39, 35, 36, 49, 80, 39, 41, 45, 79, -1, -1, -1, -1, 70, 74, 39, 45, 76, -1, 82, 88, 101, 90, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 78, 77, 71, 71, 77, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 89, 93, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 83, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, 104, 104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, 104, 104, -1, -1, 90, 89, 89, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 102, 101, 105, -1, -1, 93, 92, 92, 91, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, -1, -1, -1, 92, 92, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, 86, 87, 83, 84, -1, -1, -1, -1, -1, -1, 90, 89, 93, 92, -1, -1, 83, 84, -1, -1, -1, -1, -1, -1, 90, 89, -1, -1, -1, -1, -1, 86, 86, 86, 86, 86, 87, 88, -1, -1, -1, -1, 82, 93, 92, -1, -1, -1, -1, 86, 87, 84, -1, -1, -1, -1, 90, 93, 92, -1, -1, -1]}, {"type": 3, "obj": [[2, "309", 593, 197, 46, 33, 0], [2, "253", 287, 382, 92, 53, 0], [2, "253", 38, 484, 92, 53, 2], [2, "253", 137, 32, 92, 53, 0], [2, "253", 381, 219, 92, 53, 2], [2, "253", 503, 388, 92, 53, 2], [2, "253", 426, 360, 92, 53, 2], [2, "308", 242, 69, 52, 22, 2], [2, "308", 279, 45, 52, 22, 0], [2, "305", 285, 64, 30, 24, 0], [2, "307", 225, 55, 42, 19, 0], [2, "308", 422, 354, 52, 22, 0], [2, "308", 490, 375, 52, 22, 0], [2, "306", 370, 250, 46, 25, 0], [2, "309", 362, 273, 46, 33, 0], [2, "307", 260, 60, 42, 19, 0], [2, "253", 347, 193, 92, 53, 2], [2, "325", 237, 385, 50, 37, 0], [2, "325", 145, 488, 50, 37, 0], [2, "174", 716, 329, 68, 33, 0], [2, "174", 256, 43, 68, 33, 0], [2, "174", 727, 211, 68, 33, 0], [2, "174", 498, 95, 68, 33, 0], [2, "174", 77, 461, 68, 33, 0], [2, "219_1", 155, 484, 36, 30, 2], [2, "21", 9, 501, 28, 24, 2], [2, "420", 751, 46, 16, 13, 2], [2, "420", 166, 510, 16, 13, 0], [2, "420", 58, 492, 16, 13, 2], [2, "420", 115, 514, 16, 13, 2], [2, "219_1", 322, 38, 36, 30, 2], [2, "219_1", 117, 493, 36, 30, 0], [2, "219_1", 10, 471, 36, 30, 0], [2, "219_1", 552, 517, 36, 30, 0], [2, "219_1", 424, 375, 36, 30, 0], [2, "219_1", 619, 119, 36, 30, 2], [2, "253", 295, 31, 92, 53, 2], [2, "309", 679, 195, 46, 33, 0], [2, "307", 627, 305, 42, 19, 0], [2, "308", 585, 317, 52, 22, 0], [2, "263", 546, 491, 34, 34, 0], [2, "219_1", 460, 20, 36, 30, 2], [2, "420", 586, 305, 16, 13, 0], [2, "219_1", 545, 366, 36, 30, 0], [2, "327", 156, 436, 30, 22, 0], [2, "219_1", 300, 89, 36, 30, 0], [2, "219_1", 611, 361, 36, 30, 2], [2, "219_1", 570, 384, 36, 30, 2], [2, "219_1", 393, 354, 36, 30, 0], [2, "253", 670, 271, 92, 53, 2], [2, "253", 589, 302, 92, 53, 2], [2, "219_1", 765, 208, 36, 30, 2], [2, "219_1", 639, 335, 36, 30, 2], [2, "219_1", 589, 192, 36, 30, 2], [2, "219_1", 443, 186, 36, 30, 0], [2, "219_1", 234, 30, 36, 30, 0], [2, "219_1", 695, 30, 36, 30, 0], [2, "219_1", 724, 183, 36, 30, 0], [2, "219_1", 737, 286, 36, 30, 2], [2, "219_1", 764, 295, 36, 30, 0], [2, "219_1", 293, 404, 36, 30, 2], [2, "214_1", 441, -4, 54, 40, 2], [2, "219_1", 480, 161, 36, 30, 0], [2, "174", 451, 366, 68, 33, 0], [2, "308", 574, 377, 52, 22, 2], [2, "325", 619, 376, 50, 37, 0], [2, "309", 513, 503, 46, 33, 2], [2, "219_1", 184, 23, 36, 30, 2], [2, "219_1", 473, 257, 36, 30, 0], [2, "308", 281, 355, 52, 22, 2], [2, "308", 318, 331, 52, 22, 0], [2, "305", 324, 350, 30, 24, 0], [2, "307", 264, 341, 42, 19, 0], [2, "307", 299, 346, 42, 19, 0], [2, "174", 295, 329, 68, 33, 0], [2, "219_1", 251, 360, 36, 30, 0], [2, "208_1", 88, 247, 78, 40, 1], [2, "208_1", 140, 246, 78, 40, 3], [2, "253", -11, 341, 92, 53, 0], [2, "325", -3, 400, 50, 37, 0], [2, "253", 103, -9, 92, 53, 2], [2, "219_1", 710, -17, 36, 30, 0], [2, "309", 275, 577, 46, 33, 0], [2, "309", 262, 603, 46, 33, 0], [2, "309", 295, 591, 46, 33, 2], [2, "309", 567, 601, 46, 33, 2], [2, "308", 331, 651, 52, 22, 2], [2, "308", 160, 552, 52, 22, 2], [2, "308", 148, 317, 52, 22, 2], [2, "308", 480, 647, 52, 22, 2], [2, "174", 385, 44, 68, 33, 0], [2, "174", 646, 466, 68, 33, 0], [2, "219_1", 311, 563, 36, 30, 0], [2, "219_1", 324, 650, 36, 30, 0], [2, "219_1", 105, 606, 36, 30, 0], [2, "219_1", 22, 609, 36, 30, 0], [2, "219_1", 111, 629, 36, 30, 2], [2, "219_1", 566, 559, 36, 30, 2], [2, "219_1", 462, 567, 36, 30, 2], [2, "219_1", 655, 687, 36, 30, 2], [2, "219_1", 624, 689, 36, 30, 2], [2, "219_1", 643, 703, 36, 30, 2], [2, "219_1", 762, 607, 36, 30, 2], [2, "219_1", 711, 559, 36, 30, 2], [2, "219_1", 513, 655, 36, 30, 2], [2, "219_1", 193, 647, 36, 30, 2], [2, "219_1", 152, 354, 36, 30, 2], [2, "219_1", 15, 101, 36, 30, 2], [2, "219_1", 627, 225, 36, 30, 2]]}, {"type": 2, "data": [1, 10, 11, 12, 2, 10, 11, 21, 26, 15, 17, 16, 2, 10, 11, 21, 32, 14, 14, 20, 20, 26, 26, 26, 26, 20, 31, 1, 10, 11, 21, 14, 26, 17, 21, 26, 15, 16, 30, 33, 26, 26, 26, 20, 19, 5, 22, 23, 23, 33, 32, 14, 14, 26, 26, 20, 26, 20, 27, 28, 5, 25, 26, 26, 20, 26, 26, 32, 32, 32, 15, 17, 21, 32, 32, 32, 32, 31, 1, 2, 8, 4, 30, 33, 27, 29, 26, 26, 21, 27, 29, 28, 7, 8, 22, 23, 33, 20, 26, 26, 26, 27, 29, 33, 20, 27, 33, 32, 27, 29, 10, 17, 12, 7, 7, 8, 30, 28, 5, 17, 27, 28, 4, 7, 4, 5, 7, 8, 4, 22, 23, 26, 20, 32, 31, 6, 30, 29, 28, 30, 29, 28, 4, 22, 33, 15, 17, 16, 10, 21, 4, 5, 20, 19, 8, 7, 8, 10, 11, 12, 18, 17, 17, 16, 26, 27, 29, 28, 3, 6, 7, 8, 3, 4, 10, 11, 12, 30, 29, 23, 24, 25, 26, 26, 26, 32, 31, 10, 17, 11, 21, 20, 15, 21, 20, 20, 15, -1, 15, 16, 8, 3, 4, 5, 0, 8, 10, 21, 32, 31, 0, 0, 7, 8, 22, 33, 32, 32, 29, 28, 25, 20, 20, 26, 20, 32, 20, 32, 32, 32, 20, 29, 28, 2, 6, 7, 8, 3, 10, 21, 26, 20, 19, 1, 3, 4, 10, 11, 21, 21, 26, 15, 16, 13, 20, 20, 32, 26, 20, 26, 32, 26, 20, 20, 4, 5, 5, 6, 7, 8, 6, 30, 33, 27, 23, 24, 0, 7, 8, 25, 20, 20, 20, 20, 20, 31, 22, 33, 26, 20, 20, 32, 32, 32, 20, 32, 20, 7, 8, 8, 6, 18, 17, 17, 16, 30, 28, 0, 1, 2, 4, 18, 21, 20, 26, 20, 20, 20, 19, 8, 30, 33, 20, 20, 26, 32, 32, 32, 20, 26, 11, 11, 11, 11, 21, 20, 14, 15, 16, 4, 5, 4, 5, 1, 25, 20, 20, 26, 27, 33, 32, 15, 17, 16, 22, 33, 20, 20, 20, 26, 20, 20, 26, 20, 32, 32, 20, 26, 26, 26, 27, 28, 7, 8, 7, 8, 1, 22, 33, 32, 20, 19, 30, 29, 29, 33, 15, 16, 30, 23, 33, 20, 20, 20, 20, 26, 26, 27, 29, 29, 33, 26, 20, 15, 17, 17, 11, 12, 3, 4, 5, 30, 33, 20, 15, 16, 2, 2, 22, 23, 24, 8, 3, 30, 33, 20, 27, 33, 32, 26, 15, 16, 0, 22, 33, 20, 26, 26, 32, 26, 15, 16, 3, 3, 3, 30, 29, 33, 15, 11, 12, 6, 0, 1, 10, 17, 16, 30, 29, 28, 30, 33, 26, 14, 19, 4, 5, 25, 26, 26, 26, 26, 26, 26, 15, 17, 16, 5, 3, 8, 22, 33, 14, 15, 16, 3, 0, 13, 32, 15, 16, 1, 0, 1, 13, 26, 26, 31, 4, 5, 22, 33, 26, 26, 26, 26, 26, 26, 20, 19, 5, 10, 11, 11, 21, 20, 32, 15, 17, 17, 21, 32, 32, 15, 16, 3, 4, 13, 27, 23, 24, 7, 8, 2, 30, 29, 23, 33, 26, 14, 14, 14, 15, 8, 13, 14, 20, 20, 32, 32, 32, 20, 20, 20, 32, 32, 27, 24, 6, 7, 13, 15, 16, 5, 5, 18, 17, 16, 3, 3, 22, 23, 33, 14, 14, 32, 15, 21, 14, 14, 20, 32, 15, 17, 20, 26, 32, 32, 27, 24, 0, 10, 11, 21, 26, 15, 11, 11, 21, 20, 19, 18, 17, 16, 0, 30, 23, 33, 26, 32, 27, 33, 20, 26, 27, 33, 32, 26, 20, 27, 29, 24, 4, 3, 13, 14, 20, 20, 26, 26, 20, 27, 23, 24, 25, 26, 31, 3, 22, 28, 22, 33, 27, 28, 22, 33, 27, 28, 30, 29, 33, 27, 28, 6, 6, 7, 18, 21, 26, 20, 20, 20, 20, 20, 15, 17, 17, 21, 20, 15, 11, 11, 12, 3, 22, 24, 7, 8, 25, 15, 17, 17, 16, 22, 28, 8, 7, 8, 10, 21, 27, 23, 20, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 20, 14, 15, 11, 12, 10, 11, 11, 21, 20, 26, 20, 15, 17, 17, 16, 10, 21, 20, 32, 15, 21, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 27, 24, 25, 20, 20, 20, 20, 20, 20, 20, 20, 20, 27, 23, 33, 20, 20, 20, 20, 20, 20, 20, 20, 27, 29, 33, 20, 20, 20, 20, 26, 27, 23, 28, 1, 25, 27, 23, 33, 20, 20, 20, 20, 20, 26, 31, 1, 25, 20, 20, 20, 20, 20, 20, 20, 20, 19, 4, 13, 20, 20, 26, 27, 23, 24, 2, 1, 4, 30, 28, 1, 25, 20, 20, 20, 20, 27, 23, 24, 18, 21, 20, 20, 20, 20, 20, 20, 20, 20, 15, 17, 21, 20, 26, 26, 31, 3, 4, 5, 4, 5, 4, 4, 4, 25, 20, 20, 20, 20, 19, 4, 5, 25, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 26, 26, 31, 6, 7, 0, 1, 2, 4, 18, 17, 21, 20, 20, 20, 20, 15, 16, 8, 30, 33, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 14, 15, 11, 12, 3, 4, 18, 17, 21, 20, 20, 20, 20, 20, 20, 20, 31, 4, 5, 25, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 14, 15, 17, 17, 21, 20, 20, 20, 20, 20, 20, 20, 20, 20, 15, 17, 17, 21, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}