{"mW": 720, "mH": 1200, "tW": 24, "tH": 24, "tiles": [["315", 0, 3, 3], ["335", 0, 1, 1], ["336", 0, 1, 1], ["335", 2, 1, 1], ["336", 2, 1, 1], ["369", 0, 3, 3], ["369", 2, 3, 3], ["369", 1, 3, 3], ["369", 3, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["373", 0, 1, 1], ["203_10", 0, 2, 1], ["203_11", 0, 2, 1], ["203_12", 0, 2, 1], ["203_11", 1, 2, 1], ["203_12", 1, 2, 1], ["203_10", 1, 2, 1], ["203_9", 0, 2, 1], ["203_9", 1, 2, 1], ["380", 0, 3, 2], ["380", 2, 3, 2], ["380", 1, 3, 2], ["380", 3, 3, 2], ["381", 0, 3, 2], ["382", 0, 1, 3], ["383", 0, 2, 3], ["383", 2, 2, 3], ["382", 2, 1, 3], ["381", 2, 3, 2], ["383", 1, 2, 3], ["384", 0, 1, 3], ["384", 2, 1, 3], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1]], "layers": [{"type": 3, "obj": [[2, "334", 133, 395, 40, 32, 2], [2, "334", 145, 361, 32, 40, 4], [2, "334", 20, 337, 40, 32, 0], [2, "333", 48, 336, 38, 35, 3], [2, "334", 106, 301, 40, 32, 0], [2, "333", 74, 306, 38, 35, 0], [2, "334", 8, 354, 40, 32, 0], [2, "333", 50, 354, 38, 35, 2], [2, "332", -9, 319, 36, 38, 0], [2, "332", 37, 327, 36, 38, 0], [2, "334", 92, 316, 40, 32, 0], [2, "334", 100, 333, 40, 32, 0], [2, "334", 122, 344, 40, 32, 1], [2, "333", 128, 322, 38, 35, 2], [2, "332", 94, 331, 36, 38, 0], [2, "334", 169, 380, 40, 32, 2], [2, "333", 98, 369, 38, 35, 0], [2, "332", 119, 369, 36, 38, 0], [2, "2_1", 300, 859, 90, 66, 2], [2, "2_1", 343, 909, 90, 66, 2], [2, "2_1", 243, 906, 90, 66, 0], [2, "350", 1, 161, 58, 24, 0], [2, "353", 40, 145, 40, 29, 0], [2, "355", 169, 64, 32, 47, 0], [2, "356", 148, 92, 30, 30, 0], [2, "352", 82, 145, 44, 54, 0], [2, "356", 126, 103, 30, 30, 0], [2, "356", 103, 114, 30, 30, 0], [2, "356", 81, 125, 30, 30, 0], [2, "354", 71, 112, 30, 44, 0], [2, "2_1", 491, 1160, 90, 66, 0], [2, "2_1", 680, 1070, 90, 66, 2], [2, "2_1", -9, 457, 90, 66, 2], [2, "385", 642, 411, 72, 48, 0], [2, "375", 148, 951, 18, 45, 0], [2, "385", 676, 646, 72, 48, 0], [2, "385", 521, 709, 72, 48, 0], [2, "2_1", 479, 271, 90, 66, 0], [2, "150", 493, 452, 36, 37, 0], [2, "151", 527, 424, 30, 25, 0], [2, "471", 574, 421, 52, 47, 0], [2, "472", 472, 446, 38, 27, 0], [2, "475", 554, 392, 30, 42, 0], [2, "149", 546, 426, 58, 51, 0], [2, "149", 519, 439, 58, 51, 0], [2, "471", 511, 448, 52, 47, 0], [2, "473", 505, 418, 28, 38, 2], [2, "150", 175, 796, 36, 37, 2], [2, "151", 152, 767, 30, 25, 2], [2, "471", 83, 764, 52, 47, 2], [2, "472", 196, 789, 38, 27, 2], [2, "475", 126, 735, 30, 42, 2], [2, "149", 104, 768, 58, 51, 2], [2, "149", 132, 781, 58, 51, 2], [2, "471", 144, 794, 52, 47, 2], [2, "473", 172, 760, 28, 38, 0], [2, "351", 208, 133, 54, 36, 0], [2, "352", 181, 96, 44, 54, 0], [2, "2_1", 534, 311, 90, 66, 2], [2, "2_1", 429, 324, 90, 66, 2]]}, {"type": 4, "obj": [[2, "327", 624, 58, 30, 22, 0], [2, "346", 152, 98, 72, 71, 0], [2, "364", 661, 121, 44, 64, 0], [2, "346", 113, 116, 72, 71, 0], [2, "347", 187, 145, 60, 45, 0], [2, "346", 74, 134, 72, 71, 0], [2, "348", 44, 146, 70, 61, 0], [2, "347", 148, 163, 60, 45, 0], [2, "329", 649, 175, 42, 37, 0], [2, "347", 14, 170, 60, 45, 2], [2, "347", 22, 172, 60, 45, 2], [2, "366", 627, 171, 32, 48, 0], [2, "347", 109, 181, 60, 45, 0], [2, "347", 89, 190, 60, 45, 0], [2, "347", 59, 191, 60, 45, 2], [2, "349", 98, 177, 22, 62, 0], [4, 3, 339, 295, 0, 4006], [2, "367", 100, 318, 14, 42, 0], [2, "327", 213, 361, 30, 22, 0], [2, "331", 472, 314, 104, 108, 2], [2, "328", 514, 394, 32, 29, 2], [2, "339", 184, 426, 22, 22, 0], [2, "331", 47, 352, 104, 108, 0], [2, "330", 140, 422, 60, 49, 0], [2, "339", 134, 454, 22, 22, 0], [2, "339", 186, 455, 22, 22, 0], [2, "85_2", 605, 480, 48, 53, 0], [2, "357", 568, 447, 48, 91, 0], [2, "357", 494, 472, 48, 91, 0], [2, "329", 516, 541, 42, 37, 0], [2, "263_2", 474, 553, 34, 34, 0], [2, "328", 453, 563, 32, 29, 0], [2, "329", 151, 559, 42, 37, 0], [2, "331", -4, 497, 104, 108, 0], [2, "328", 65, 577, 32, 29, 0], [2, "386", 345, 559, 54, 155, 0], [4, 1, 352, 718, 0, 4002], [4, 2, 387, 720, 0, 4003], [2, "329", 460, 738, 42, 37, 0], [2, "366", 452, 734, 32, 48, 0], [2, "328", -10, 756, 32, 29, 0], [2, "329", 651, 768, 42, 37, 0], [2, "366", 406, 759, 32, 48, 0], [2, "385", 422, 759, 72, 48, 0], [2, "364", 323, 744, 44, 64, 0], [2, "329", 354, 771, 42, 37, 0], [2, "385", 289, 783, 72, 48, 0], [2, "385", 394, 783, 72, 48, 0], [2, "328", 79, 825, 32, 29, 2], [2, "85_2", 205, 824, 48, 53, 0], [2, "357", 100, 787, 48, 91, 2], [2, "329", -6, 853, 42, 37, 0], [2, "357", 165, 815, 48, 91, 2], [4, 5, 228, 911, 0, 4001], [2, "263_2", 468, 907, 34, 34, 0], [2, "361", 502, 837, 58, 104, 4], [2, "361", 530, 890, 58, 104, 6], [2, "366", 598, 948, 32, 48, 2], [2, "328", 604, 978, 32, 29, 0], [2, "365", 568, 926, 48, 94, 2], [2, "363", 97, 957, 54, 75, 0], [2, "320_1", 132, 938, 62, 103, 0], [2, "263_2", 120, 1010, 34, 34, 0], [2, "364", 162, 986, 44, 64, 0], [2, "329", 185, 1015, 42, 37, 0], [2, "366", 158, 1007, 32, 48, 2], [2, "329", 542, 1018, 42, 37, 2], [4, 6, 564, 1055, 0, 4005], [2, "365", 689, 968, 48, 94, 0], [2, "328", 161, 1034, 32, 29, 0], [2, "366", 707, 1039, 32, 48, 0], [2, "85_2", 646, 1052, 48, 53, 0], [4, 9, 204, 1175, 0, 4022]]}, {"type": 3, "obj": [[2, "318", 351, 1099, 18, 54, 2], [2, "381", 450, 1160, 72, 48, 2], [2, "381", 379, 1160, 72, 48, 0], [2, "318", 423, 1087, 18, 54, 0], [2, "325", 246, 547, 50, 37, 0], [2, "2_1", 223, 109, 90, 66, 0], [2, "324", 281, 164, 70, 35, 2], [2, "324", 288, 168, 70, 35, 2], [2, "331", 151, 58, 104, 108, 0], [2, "331", -28, 73, 104, 108, 2], [2, "340", 220, 182, 34, 18, 2], [2, "340", 264, 204, 34, 18, 2], [2, "174_2", 80, 557, 68, 33, 2], [2, "174_2", 251, 529, 68, 33, 2], [2, "174_1", 221, 414, 68, 33, 2], [2, "174_1", 132, 462, 68, 33, 2], [2, "174_1", 208, 371, 68, 33, 2], [2, "174_1", 24, 378, 68, 33, 1], [2, "174_1", -8, 412, 68, 33, 0], [2, "324", 540, 809, 70, 35, 0], [2, "366", 599, 776, 32, 48, 0], [2, "371", 440, 611, 26, 48, 2], [2, "371", 232, 668, 26, 48, 2], [2, "371", 286, 619, 26, 48, 2], [2, "385", 438, 620, 72, 48, 0], [2, "381", 393, 137, 72, 48, 0], [2, "382", 413, 135, 72, 24, 4], [2, "382", 610, 671, 72, 24, 6], [2, "383", 596, 624, 72, 48, 4], [2, "382", 461, 63, 24, 72, 0], [2, "381", 392, 86, 72, 48, 0], [2, "318", 639, 380, 18, 54, 0], [2, "319", 669, 418, 62, 65, 2], [2, "317", 644, 405, 22, 42, 0], [2, "317", 656, 414, 22, 42, 0], [2, "317", 666, 413, 22, 42, 0], [2, "253", -8, 561, 92, 53, 0], [2, "325", 245, 417, 50, 37, 0], [2, "318", 454, 952, 18, 54, 0], [2, "362", 654, 861, 64, 42, 0], [2, "325", 677, 1058, 50, 37, 0], [2, "325", 533, 846, 50, 37, 2], [2, "253", 260, 1097, 92, 53, 0], [2, "324", 5, 741, 70, 35, 2], [2, "371", 509, 880, 26, 48, 0], [2, "325", 153, 1164, 50, 37, 0], [2, "253", 251, 1011, 92, 53, 2], [2, "323", 290, 918, 46, 70, 2], [2, "328", 326, 166, 32, 29, 0], [2, "325", 471, 160, 50, 37, 0], [2, "361", 461, 264, 104, 58, 2], [2, "253", 552, 1058, 92, 53, 2], [2, "324", 385, 211, 70, 35, 2], [2, "371", 281, 596, 26, 48, 2], [2, "320", 577, 195, 62, 103, 0], [2, "371", 460, 143, 26, 48, 0], [2, "319", 345, 801, 62, 65, 2], [2, "319", 661, 169, 62, 65, 0], [2, "319", 482, 183, 62, 65, 2], [2, "319", 312, 572, 62, 65, 0], [2, "319", 582, 562, 62, 65, 0], [2, "340", 308, 226, 34, 18, 2], [2, "344", 191, 181, 30, 74, 2], [2, "344", 118, 216, 30, 74, 2], [2, "344", 43, 202, 30, 74, 0], [2, "344", 73, 217, 30, 74, 0], [2, "342", 26, 194, 20, 74, 0], [2, "345", 161, 197, 24, 73, 0], [2, "342", 100, 226, 20, 74, 0], [2, "343", 145, 212, 20, 70, 0], [2, "343", 182, 195, 20, 70, 0], [2, "337", 98, 353, 24, 26, 0], [2, "338", 263, 265, 34, 31, 0], [2, "338", 241, 276, 34, 31, 0], [2, "338", 197, 298, 34, 31, 0], [2, "338", 153, 320, 34, 31, 0], [2, "338", -1, 303, 34, 31, 2], [2, "338", 43, 325, 34, 31, 2], [2, "340", 9, 177, 34, 18, 3], [2, "319", 595, 519, 62, 65, 0], [2, "319", 260, 767, 62, 65, 2], [2, "322", 425, 984, 52, 101, 0], [2, "322", 605, 429, 52, 101, 2], [2, "319", 114, 627, 62, 65, 0], [2, "318", 498, 704, 18, 54, 2], [2, "319", 78, 649, 62, 65, 2], [2, "318", 302, 586, 18, 54, 0], [2, "323", 573, 388, 46, 70, 2], [2, "324", 505, 363, 70, 35, 2], [2, "319", 439, 763, 62, 65, 0], [2, "320", 344, 802, 62, 103, 0], [2, "318", 436, 792, 18, 54, 2], [2, "326", 267, 570, 18, 14, 0], [2, "341", 206, 155, 26, 30, 0], [2, "2_1", -65, 95, 90, 66, 0], [2, "2_1", -13, 34, 90, 66, 0], [2, "2_1", 74, 57, 90, 66, 2], [2, "2_1", 149, 34, 90, 66, 2], [2, "253", 246, 335, 92, 53, 2], [2, "253", 396, 294, 92, 53, 0], [2, "319", 547, 202, 62, 65, 2], [2, "318", 471, 179, 18, 54, 0], [2, "318", 479, 180, 18, 54, 0], [2, "318", 499, 203, 18, 54, 0], [2, "319", 612, 202, 62, 65, 0], [2, "318", 699, 104, 18, 54, 2], [2, "318", 691, 136, 18, 54, 2], [2, "318", 706, 129, 18, 54, 2], [2, "317", 490, 217, 22, 42, 0], [2, "318", 469, 142, 18, 54, 0], [2, "328", 479, 167, 32, 29, 0], [2, "325", 22, 753, 50, 37, 2], [2, "318", 601, 218, 18, 54, 2], [2, "318", 663, 198, 18, 54, 2], [2, "319", 444, 1137, 62, 65, 2], [2, "371", 452, 795, 26, 48, 2], [2, "372", 625, 532, 44, 50, 0], [2, "318", 644, 494, 18, 54, 2], [2, "319", 309, 797, 62, 65, 2], [2, "319", 380, 794, 62, 65, 2], [2, "318", 490, 766, 18, 54, 2], [2, "318", 243, 738, 18, 54, 0], [2, "318", 256, 761, 18, 54, 0], [2, "371", 331, 829, 26, 48, 0], [2, "371", 431, 825, 26, 48, 2], [2, "318", 248, 737, 18, 54, 2], [2, "371", 661, 206, 26, 48, 2], [2, "371", 636, 230, 26, 48, 2], [2, "318", 434, 1116, 18, 54, 0], [2, "318", 425, 791, 18, 54, 2], [2, "322", 318, 968, 52, 101, 2], [2, "319", 479, 1166, 62, 65, 2], [2, "371", 459, 1154, 26, 48, 0], [2, "319", 269, 1170, 62, 65, 0], [2, "371", 325, 1153, 26, 48, 2], [2, "318", 348, 1133, 18, 54, 2], [2, "319", 514, 563, 62, 65, 0], [2, "371", 579, 610, 26, 48, 2], [2, "319", 534, 597, 62, 65, 0], [2, "319", 340, 586, 62, 65, 2], [2, "319", 183, 653, 62, 65, 2], [2, "319", 235, 617, 62, 65, 0], [2, "319", 369, 568, 62, 65, 0], [2, "319", 454, 580, 62, 65, 0], [2, "319", 412, 570, 62, 65, 2], [2, "319", 491, 600, 62, 65, 2], [2, "318", 302, 801, 18, 54, 0], [2, "318", 399, 826, 18, 54, 0], [2, "371", 359, 802, 26, 48, 2], [2, "318", 684, 204, 18, 54, 2], [2, "371", 653, 236, 26, 48, 2], [2, "319", 389, 592, 62, 65, 0], [2, "371", 547, 628, 26, 48, 2], [2, "318", 355, 1050, 18, 54, 0], [2, "325", 625, 49, 50, 37, 0], [2, "319", 27, 621, 62, 65, 2], [2, "319", 121, 583, 62, 65, 0], [2, "320", 32, 632, 62, 103, 0], [2, "318", 118, 612, 18, 54, 2], [2, "371", 134, 615, 26, 48, 2], [2, "319", -9, 617, 62, 65, 2], [2, "319", 62, 614, 62, 65, 2], [2, "371", 13, 649, 26, 48, 0], [2, "371", 113, 645, 26, 48, 2], [2, "318", 107, 611, 18, 54, 2], [2, "318", -16, 621, 18, 54, 0], [2, "318", 81, 646, 18, 54, 0], [2, "371", 45, 622, 26, 48, 2], [2, "320", 162, 593, 62, 103, 0], [2, "319", 197, 600, 62, 65, 0], [2, "318", 248, 596, 18, 54, 2], [2, "371", 246, 604, 26, 48, 2], [2, "371", 221, 628, 26, 48, 2], [2, "318", 269, 602, 18, 54, 2], [2, "371", 238, 634, 26, 48, 2], [2, "253", 511, 507, 92, 53, 2], [2, "325", 454, 702, 50, 37, 2], [2, "325", 251, 754, 50, 37, 0], [2, "366", 415, 203, 32, 48, 0], [2, "328", 444, 236, 32, 29, 0], [2, "329", 457, 234, 42, 37, 0], [2, "327", 369, 202, 30, 22, 0], [2, "253", 386, 337, 92, 53, 0], [2, "178_1", 582, 109, 70, 37, 0], [2, "178_1", 521, 109, 70, 37, 2], [2, "178_1", 583, 143, 70, 37, 1], [2, "178_1", 521, 142, 70, 37, 3], [2, "178_1", 366, 676, 70, 37, 0], [2, "178_1", 305, 676, 70, 37, 2], [2, "178_1", 367, 710, 70, 37, 1], [2, "178_1", 305, 709, 70, 37, 3], [2, "253", 123, 830, 92, 53, 0], [2, "5", 310, 973, 42, 66, 2], [2, "5", 267, 973, 42, 66, 0], [2, "331", 282, 909, 104, 108, 0], [2, "323", 218, 840, 46, 70, 2], [2, "324", 257, 905, 70, 35, 2], [2, "328", 230, 866, 32, 29, 0], [2, "325", 113, 1030, 50, 37, 0], [2, "253", 59, 888, 92, 53, 0], [2, "325", 179, 944, 50, 37, 0], [2, "21", 226, 1164, 28, 24, 2], [2, "22", 119, 1153, 62, 38, 0], [2, "365", 587, -20, 48, 94, 0], [2, "364", 525, 17, 44, 64, 0], [2, "363", 478, 30, 54, 75, 0], [2, "366", 566, 25, 32, 48, 0], [2, "364", 464, 88, 44, 64, 0], [2, "364", 668, 48, 44, 64, 2], [2, "366", 648, 34, 32, 48, 0], [2, "253", 430, 1042, 92, 53, 0], [2, "324", 74, 765, 70, 35, 2], [2, "367", 231, 156, 14, 42, 0], [2, "342", 219, 177, 20, 74, 0], [2, "361", 502, 201, 104, 58, 2], [2, "362", 502, 242, 64, 42, 2], [2, "362", 530, 690, 64, 42, 0], [2, "361", 463, 717, 104, 58, 2], [2, "365", 367, 568, 48, 94, 0], [2, "364", 299, 601, 44, 64, 0], [2, "364", 412, 610, 44, 64, 2], [2, "363", 446, 628, 54, 75, 2], [2, "366", 334, 614, 32, 48, 0], [2, "362", 199, 809, 64, 42, 0], [2, "361", 225, 768, 104, 58, 2], [2, "366", 248, 877, 32, 48, 2], [2, "362", 391, 1034, 64, 42, 0], [2, "362", 421, 1058, 64, 42, 0], [2, "361", 337, 1063, 104, 58, 2], [2, "365", 558, 754, 48, 94, 0], [2, "366", 538, 816, 32, 48, 0], [2, "329", 687, 100, 42, 37, 0], [2, "323", 456, 910, 46, 70, 0], [2, "36_1", 578, 984, 140, 103, 0], [2, "325", 550, 996, 50, 37, 0], [2, "325", 487, 927, 50, 37, 0], [2, "21", 466, 942, 28, 24, 0], [2, "328", 54, 750, 32, 29, 0], [2, "329", 14, 732, 42, 37, 0], [2, "253", 633, 1160, 92, 53, 2], [2, "21", 266, 418, 28, 24, 0], [2, "364", 682, 744, 44, 64, 2], [2, "366", 628, 759, 32, 48, 2], [2, "253", 329, 370, 92, 53, 0], [2, "253", 379, 397, 92, 53, 2], [2, "253", 293, 428, 92, 53, 0], [2, "326", 628, 804, 18, 14, 0], [2, "325", -18, 1026, 50, 37, 2], [2, "326", 3, 1037, 18, 14, 0], [2, "329", 592, 405, 42, 37, 0], [2, "327", 270, 541, 30, 22, 0], [2, "324", 658, 332, 70, 35, 0], [2, "366", 646, 340, 32, 48, 0], [2, "328", 419, 238, 32, 29, 0], [2, "253", 49, 469, 92, 53, 2], [2, "329", 659, 326, 42, 37, 2], [2, "364", -23, 715, 44, 64, 0], [2, "361", 596, 595, 104, 58, 0], [2, "362", 647, 637, 64, 42, 0], [2, "361", 595, 674, 104, 58, 0], [2, "361", 546, 554, 104, 58, 0], [2, "363", 246, 619, 54, 75, 0], [2, "364", 237, 680, 44, 64, 0], [2, "327", 244, 730, 30, 22, 0], [2, "381", 626, 241, 72, 48, 0], [2, "381", 410, 864, 72, 48, 2], [2, "381", 674, 174, 72, 48, 0], [2, "327", 94, 584, 30, 22, 0], [2, "327", 206, 972, 30, 22, 0], [2, "327", 5, 1173, 30, 22, 0], [2, "327", 531, 1001, 30, 22, 0], [2, "329", 449, 127, 42, 37, 2], [2, "385", 428, 171, 72, 48, 0], [2, "385", 567, -6, 72, 48, 0], [2, "385", 219, 639, 72, 48, 0], [2, "385", 50, 674, 72, 48, 0], [2, "385", 334, 1141, 72, 48, 0], [2, "385", 243, 814, 72, 48, 0], [2, "385", 356, 852, 72, 48, 0], [2, "385", 607, 204, 72, 48, 0], [2, "385", 599, 295, 72, 48, 0], [2, "385", 536, 231, 72, 48, 0], [2, "385", 380, 963, 72, 48, 0], [2, "385", 534, 601, 72, 48, 0], [2, "21", 633, 1113, 28, 24, 0], [2, "325", 195, 1031, 50, 37, 2], [2, "325", 145, 372, 50, 37, 2], [2, "5", 34, 414, 42, 66, 0], [2, "5", 77, 414, 42, 66, 2], [2, "326", 244, 479, 18, 14, 0], [2, "385", 676, 646, 72, 48, 0], [2, "385", 521, 709, 72, 48, 0], [2, "174_2", 325, 277, 68, 33, 1], [2, "174_2", 339, 330, 68, 33, 0], [2, "174_2", 438, 541, 68, 33, 0], [2, "174_2", 400, 456, 68, 33, 0], [2, "174_2", 184, 903, 68, 33, 1], [2, "174_2", 490, 970, 68, 33, 1], [2, "305", 298, 334, 30, 24, 0], [2, "306", 295, 350, 46, 25, 0], [2, "308", 259, 330, 52, 22, 0], [2, "308", 545, 539, 52, 22, 0], [2, "306", 538, 515, 46, 25, 0], [2, "309", 509, 485, 46, 33, 0], [2, "307", 325, 368, 42, 19, 0], [2, "308", 349, 383, 52, 22, 0], [2, "309", 313, 379, 46, 33, 0], [2, "309", 421, 286, 46, 33, 0], [2, "307", 410, 319, 42, 19, 0], [2, "308", 407, 338, 52, 22, 0], [2, "306", 322, 415, 46, 25, 0], [2, "309", 371, 394, 46, 33, 0], [2, "308", 381, 423, 52, 22, 0], [2, "309", 434, 433, 46, 33, 0], [2, "306", 445, 413, 46, 25, 0], [2, "305", 405, 435, 30, 24, 0], [2, "306", 474, 493, 46, 25, 0], [2, "308", 461, 473, 52, 22, 0], [2, "309", 432, 467, 46, 33, 0], [2, "306", 418, 449, 46, 25, 0], [2, "307", 350, 448, 42, 19, 0], [2, "308", 436, 496, 52, 22, 0], [2, "306", 149, 845, 46, 25, 0], [2, "308", 120, 858, 52, 22, 0], [2, "309", 112, 874, 46, 33, 0], [2, "305", 143, 870, 30, 24, 0], [2, "307", 56, 921, 42, 19, 0], [2, "307", 86, 898, 42, 19, 0], [2, "327", 403, 487, 30, 22, 0], [2, "338", 21, 314, 34, 31, 2], [2, "338", 65, 336, 34, 31, 2], [2, "337", 330, 237, 24, 26, 0], [2, "338", 307, 243, 34, 31, 0], [2, "338", 285, 254, 34, 31, 0], [2, "338", 219, 287, 34, 31, 0], [2, "338", 175, 309, 34, 31, 0], [2, "338", 131, 331, 34, 31, 0], [2, "338", 109, 342, 34, 31, 0], [2, "338", 87, 347, 34, 31, 2], [2, "340", 286, 215, 34, 18, 2], [2, "340", 242, 193, 34, 18, 2], [2, "340", -13, 188, 34, 18, 3], [2, "328", 284, 274, 32, 29, 2], [2, "43_2", 225, 282, 82, 58, 2], [2, "43_2", 214, 288, 82, 58, 2], [2, "328", 197, 319, 32, 29, 0], [2, "2_1", 10, 85, 90, 66, 2], [2, "328", 277, 903, 32, 29, 0], [2, "331", 260, 106, 104, 108, 0], [2, "2_1", 291, 62, 90, 66, 0], [2, "2_1", 301, 123, 90, 66, 2], [2, "329", 249, 173, 42, 37, 2], [2, "2_1", 208, 75, 90, 66, 0], [2, "328", 108, 1113, 32, 29, 0], [2, "328", 426, 494, 32, 29, 0], [2, "328", 365, 338, 32, 29, 0], [2, "327", 584, 181, 30, 22, 0], [2, "388", 340, 688, 60, 51, 0], [2, "328", 541, 364, 32, 29, 2], [2, "365", 342, 182, 48, 94, 2], [2, "328", 348, 262, 32, 29, 0], [2, "367", 203, 270, 14, 42, 0], [2, "341", 180, 289, 26, 30, 2], [2, "367", 170, 286, 14, 42, 0], [2, "341", 148, 305, 26, 30, 2], [2, "341", 119, 322, 26, 30, 0], [2, "341", 114, 322, 26, 30, 2], [2, "367", 137, 304, 14, 42, 0], [2, "328", 306, 203, 32, 29, 0], [2, "341", 240, 171, 26, 30, 0], [2, "367", 263, 170, 14, 42, 0], [2, "341", 274, 188, 26, 30, 0], [2, "367", 297, 186, 14, 42, 0], [2, "341", 308, 205, 26, 30, 0], [2, "367", 332, 206, 14, 42, 0], [2, "341", 309, 224, 26, 30, 2], [2, "367", 297, 223, 14, 42, 0], [2, "341", 278, 240, 26, 30, 2], [2, "367", 266, 239, 14, 42, 0], [2, "367", -4, 269, 14, 42, 0], [2, "341", 6, 288, 26, 30, 0], [2, "367", 30, 285, 14, 42, 0], [2, "341", 41, 304, 26, 30, 0], [2, "367", 65, 301, 14, 42, 0], [2, "263_2", 434, 1008, 34, 34, 0], [2, "85_2", 434, 1101, 48, 53, 0], [2, "328", 341, 1035, 32, 29, 0], [2, "329", 491, 681, 42, 37, 0], [2, "328", 290, 648, 32, 29, 0], [2, "329", 337, 1119, 42, 37, 0]]}, {"type": 2, "data": [-103, -126, -127, -121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -102, -124, -125, -120, -116, -117, -118, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -101, -122, -123, -119, -113, -114, -115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -126, -126, -127, -121, -1, -1, -1, -1, 67, 90, 91, 96, 69, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 114, 114, 115, 116, -124, -125, -120, -116, -117, 61, 67, -1, 110, 109, 108, -1, 69, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 117, 118, 119, -122, -123, -119, -113, -114, -115, -1, -1, -1, -1, -1, -1, -1, 64, 59, 48, 71, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 121, 121, -1, -1, -1, -1, -1, -1, -1, -1, 64, 59, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, 71, 65, 55, -1, 124, -106, -1, 120, -121, -1, -1, -1, -1, -1, -1, -1, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -90, -1, -1, -1, 71, 54, 55, 126, -105, -1, -102, -124, -1, -1, -1, -1, -1, 49, 55, 57, -1, -1, -84, -85, -85, -1, -1, -1, -1, -1, -90, -89, -88, -1, -98, 72, -1, -1, -1, 49, -101, -122, -1, -1, -1, -1, -1, 53, -1, -1, 120, 120, -95, 65, 59, -1, -1, -1, -1, 65, -100, -99, -98, -1, -1, -1, 71, -1, 55, 61, 67, -1, -1, -126, -1, -1, -1, -1, 114, 115, 116, 121, -93, -94, -95, 65, 59, 65, -100, -99, -98, -1, -1, -1, 71, 66, 67, 61, 67, -1, -1, 61, -1, -124, -1, -1, -126, -127, 117, 118, 119, 122, -1, -1, -93, -94, -95, -99, -98, -1, -1, -1, -1, 72, 66, 67, 98, 97, 97, 96, -1, 64, -1, -122, -1, -1, -1, -1, -1, -1, -1, -118, 71, -1, -1, -1, -1, -1, -1, -1, 65, -1, -1, 36, 41, -1, 105, 106, 107, 108, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 72, 71, -1, -1, -1, 65, 65, 66, 62, 67, -1, -1, -1, 102, 103, 104, -1, -1, -1, -1, -1, -1, 123, 124, -106, -1, -1, 56, 55, 107, 108, 68, 72, 71, -1, 61, 62, 63, 90, 91, 92, -1, -1, -1, 69, 67, -1, -1, 105, 106, 107, -1, 125, 126, -105, -1, 65, 66, 67, 108, 49, 50, 60, 65, 55, -1, -1, -1, 102, 113, 95, 96, -1, -1, -1, -1, -1, -1, 102, 103, 104, 56, 55, -128, -104, -1, 61, 67, -1, -1, 61, 53, 59, 71, 70, -1, 57, 56, 55, 102, 112, 111, -1, -1, -1, -1, -1, -1, -1, -1, 69, 68, 67, -1, -1, -1, -1, -1, -1, -1, 49, 53, -1, 65, 54, 56, 65, 59, 58, 93, 107, 108, -1, -1, -1, 98, 97, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 68, 67, 62, 72, 59, 66, 68, 67, 93, 111, -1, -1, -1, -1, 110, 109, 108, -1, -1, -1, -1, -1, -1, -1, -1, -1, -103, -126, -1, -1, -1, -1, -1, 69, 68, 67, -1, -1, 102, 104, -1, -1, -1, -1, -1, 57, -1, -1, -1, -1, 49, 50, 56, 55, -1, -1, -102, -124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 97, 96, -1, -1, -1, 69, 67, 49, 50, 51, 61, 62, 72, 70, -1, -1, -101, -122, -1, -1, -1, -1, -1, -1, -1, 90, 91, 92, 90, 101, 100, 99, -1, -1, -1, -1, -1, 61, 72, 71, -1, -1, 61, 67, -1, -1, -1, -1, -1, 57, 56, 56, 55, -1, -1, 102, 103, 108, -1, -1, 109, 108, -1, -1, -1, -1, -1, -1, 69, 68, -1, -1, -1, -1, 120, 120, 123, 124, -1, 61, 72, 68, 67, -1, -1, 56, 55, -1, 69, 67, -1, -1, -1, -1, -1, -1, -1, 57, 56, 55, -1, -1, -1, -1, 121, 121, 125, 126, -1, -1, 69, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -124, -125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 125, -110, -109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, -1, 67, 61, 72, 56, 55, -1, -1, -116, -117, -118, -1, -1, -1, -1, 125, -124, -125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 67, -1, -1, 61, 59, 58, 61, -1, -113, -114, -115, 114, 115, 116, -1, 125, -110, -109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, -1, -1, -1, -1, 49, 56, 55, -1, -1, 117, 118, 119, -1, -1, -112, -111, -1, -1, -1, -1, -103, 120, 123, 124, -106, -1, -1, 55, -1, -1, 111, -1, -1, -1, 64, 65, 65, -1, -1, -1, -1, -1, -1, 120, 123, -127, 65, 55, -1, -1, -102, 121, 125, 126, -105, -1, -1, 67, -1, -1, -1, -1, -1, -1, 61, 65, -1, -1, -1, -1, 114, 115, 116, 121, 125, -125, 66, 67, -1, -1, -1, 122, -112, -112, -104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 120, 123, 124, -106, -1, 117, 118, 119, 122, 127, -123, 67, -1, 98, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 121, 125, 126, -105, -1, -1, 69, 68, 67, 69, 68, -1, 90, 101, 99, -1, 59, 59, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 122, 127, -128, -104, -1, -1, 55, -1, -1, -1, -1, -1, 102, 103, 104, 49, 50, 59, 59, -1, -1, -1, -1, -103, -126, -127, -121, -121, -1, -1, -1, -1, -1, -1, 69, 68, 67, -1, -1, -1, -1, 55, 98, 97, 96, -1, -1, -1, -1, 65, 66, -1, -1, -102, -124, -125, -120, -120, -116, -117, -118, -1, -1, 56, 55, -1, -1, -1, -1, 67, 56, 67, 110, 109, 108, -1, -1, 49, 66, 62, 63, 69, -1, -101, -122, -123, -119, -119, -113, -114, -115, -1, -1, -1, 58, -1, -1, -1, 69, 68, 68, -1, -1, -1, -1, -1, -1, 61, 67, -1, -1, 61, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 91, 97, -1, -1, -1, -1, -1, -1, 90, 91, 97, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 68, 67, -1, 57, 56, 55, 110, 109, 113, -1, -1, -1, -1, -1, -1, 110, 113, 106, 111, -1, 57, 56, 55, -1, -1, -1, -1, -1, 67, -1, -1, 69, 56, 61, 62, 63, -1, -1, 110, -1, -1, -1, -1, -1, 56, 55, 105, 106, 96, 49, 50, 51, 58, -1, -1, -1, -1, -1, -1, -1, -1, 69, 71, -1, 70, 70, -1, 56, 55, 23, 46, -1, -1, 49, 50, 58, 93, 100, 111, 61, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, -1, -1, -1, -1, -1, 44, 43, -1, -1, 61, 62, 67, 57, 56, 55, -1, 61, 62, 68, 67, -1, -1, -1, -1, 96, -1, -1, -1, -1, -1, -1, -1, 65, 65, -1, 36, 40, -1, -1, -1, -1, -1, 69, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, -1, -1, -1, 61, 68, 67, 72, 65, 66, 71, 40, -1, -1, -1, 90, 91, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, 70, -1, -1, -1, -1, -1, -1, 69, 72, 65, 68, -1, -1, -1, -1, 102, 103, 104, 90, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 67, -1, -1, -1, -1, -1, -1, -1, 69, 67, -1, -1, -1, -1, -1, 61, 67, -1, 93, 95, 97, 96, -1, 98, 97, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 67, -1, -1, 98, 97, 96, -1, -1, -1, 56, 110, 109, 103, 104, -1, 110, 109, 108, -1, -1, -1, -1, -120, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 102, 103, 108, -1, -1, -1, 68, 67, -1, 69, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 44, 44, 44, 75, 44, 74, 74, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 44, 44, 44, 44, 44, 44, 44, 75, 75, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 44, 36, 45, 44, 44, 44, 44, 44, 44, 80, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 36, 40, 34, 33, 44, 44, 44, 44, 17, 17, 44, 44, 44, 11, 10, 12, 9, 8, 3, 78, 78, 78, 78, 78, 78, 78, 78, 78, 78, 78, 43, 3, 31, 45, 44, 35, 35, 35, 44, 17, 44, 11, 10, 12, 9, 11, 10, 12, 9, 11, 35, 26, 26, 26, 79, 79, 79, 79, 79, 79, 6, 6, 3, 31, 41, 45, 44, 44, 44, 86, 10, 12, 9, 11, 10, 12, 9, 11, 10, 12, 9, 11, 35, 35, 35, 83, 83, 83, 83, 83, 83, 4, 6, 7, 48, 44, 44, 44, 86, 86, 9, 11, 10, 12, 9, 11, 10, 12, 9, 11, 10, 12, 10, 12, 44, 44, 44, 35, 82, 82, 82, 81, 81, 81, 81, 81, 81, 86, 86, 87, 10, 12, 9, 11, 10, 12, 9, 11, 10, 12, 9, 11, 10, 12, 35, 44, 44, 35, 17, 73, 80, 80, 80, 80, 80, 80, 80, 86, 87, 80, 9, 11, 10, 12, 9, 11, 10, 12, 9, 11, 10, 12, 35, 35, 35, 35, 17, 17, 21, 30, 80, 80, 80, 80, 80, 80, 80, 80, 80, 80, 10, 12, 9, 11, 10, 12, 9, 11, 10, 12, 2, 44, 17, 35, 44, 17, 17, 17, 39, 27, 73, 73, 85, 85, 85, 85, 85, 85, 85, 85, 35, 12, 10, 12, 9, 11, 10, 12, 35, 35, 1, 21, 36, 45, 44, 44, 17, 26, 44, 44, 44, 44, 84, 84, 84, 84, 84, 84, 84, 84, 35, 35, 44, 12, 10, 12, 35, 35, 35, 35, 36, 40, 5, 31, 45, 35, 36, 45, 26, 44, 44, 44, 44, 74, 74, 74, 74, 74, 23, 22, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 18, 14, 22, 6, 31, 32, 33, 34, 35, 35, 44, 44, 44, 75, 75, 75, 75, 36, 32, 33, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 26, 26, 18, 22, 4, 5, 4, 31, 32, 45, 26, 44, 44, 44, 76, 76, 76, 18, 23, 22, 45, 35, 26, 26, 35, 35, 35, 35, 44, 44, 44, 44, 26, 25, 7, 8, 1, 3, 4, 42, 45, 44, 44, 44, 44, 77, 77, 35, 35, 18, 34, 35, 2, 0, 35, 35, 35, 35, 44, 44, 44, 35, 36, 40, 4, 2, 13, 14, 15, 8, 42, 45, 44, 44, 44, 78, 78, 78, 73, 73, 31, 45, 35, 35, 35, 35, 44, 44, 44, 44, 35, 36, 33, 6, 13, 14, 27, 17, 18, 23, 22, 0, 1, 44, 44, 44, 73, 79, 79, 79, 15, 42, 41, 36, 45, 44, 35, 35, 36, 32, 32, 33, 1, 2, 16, 17, 35, 35, 44, 44, 25, 3, 4, 45, 44, 44, 44, 83, 83, 83, 18, 25, 36, 40, 42, 41, 32, 32, 33, 24, 23, 14, 23, 46, 31, 32, 45, 26, 26, 44, 18, 6, 7, 42, 45, 44, 44, 82, 82, 82, 32, 45, 18, 23, 23, 22, 13, 14, 23, 27, 26, 44, 44, 18, 22, 3, 42, 41, 41, 45, 44, 44, 0, 1, 42, 44, 44, 81, 81, 81, 13, 27, 44, 44, 44, 43, 16, 17, 44, 35, 44, 44, 35, 35, 18, 23, 22, 24, 46, 19, 45, 44, 18, 22, 5, 73, 80, 80, 80, 80, 34, 17, 17, 44, 44, 43, 19, 45, 35, 44, 44, 44, 44, 35, 35, 35, 73, 27, 26, 23, 27, 44, 6, 7, 8, 73, 85, 85, 85, 85, 27, 35, 35, 36, 41, 40, 37, 27, 35, 35, 35, 35, 35, 8, 86, 86, 86, 73, 86, 73, 73, 44, 73, 73, 73, 73, 73, 84, 84, 84, 35, 35, 32, 33, 35, 73, 73, 45, 73, 73, 73, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 74, 74, 74, 74, 74, 86, 86, 74, 86, 86, 86, 86, 86, 86, 73, 86, 86, 36, 21, 32, 45, 44, 44, 44, 75, 87, 87, 87, 87, 87, 87, 75, 75, 75, 75, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 21, 40, 1, 2, 42, 44, 44, 44, 44, 76, 84, 84, 84, 84, 84, 84, 76, 76, 76, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 75, 46, 1, 1, 2, 6, 7, 44, 44, 44, 44, 85, 85, 77, 77, 77, 77, 77, 77, 77, 74, 84, 74, 74, 74, 84, 84, 84, 84, 84, 84, 18, 22, 8, 3, 44, 5, 19, 45, 44, 1, 2, 73, 73, 73, 73, 73, 73, 73, 73, 44, 43, 75, 75, 75, 75, 75, 75, 75, 75, 85, 44, 18, 23, 22, 1, 2, 24, 27, 44, 4, 73, 73, 73, 73, 73, 73, 73, 73, 73, 41, 40, 31, 13, 14, 2, 73, 80, 80, 80, 80, 26, 44, 26, 18, 14, 23, 27, 44, 44, 73, 73, 73, 73, 73, 73, 73, 73, 73, 1, 24, 23, 23, 27, 44, 44, 73, 73, 76, 76, 73, 73, 73, 26, 44, 44, 44, 7, 8, 73, 73, 73, 73, 73, 73, 73, 36, 41, 41, 4, 27, 26, 26, 44, 44, 44, 44, 1, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 44, 43, 6, 24, 23, 14, 35, 35, 44, 44, 26, 36, 28, 1, 2, 44, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 73, 1, 41, 40, 24, 27, 26, 44, 35, 35, 44, 44, 29, 28, 3, 48, 44, 44, 73, 73, 73, 73, 73, 73, 79, 79, 79, 79, 79, 73, 73, 25, 0, 3, 34, 35, 44, 44, 45, 44, 36, 32, 33, 13, 14, 44, 44, 44, 17, 73, 73, 73, 73, 73, 73, 73, 83, 83, 83, 73, 73, 18, 22, 13, 27, 44, 44, 44, 42, 41, 40, 8, 4, 16, 17, 44, 44, 44, 44, 17, 17, 73, 73, 73, 73, 73, 73, 73, 73, 0, 3, 4, 25, 31, 32, 45, 44, 44, 6, 7, 0, 6, 37, 27, 35, 44, 44, 44, 44, 44, 1, 44, 73, 73, 73, 73, 73, 73, 36, 45, 35, 7, 28, 4, 5, 34, 35, 44, 0, 1, 3, 37, 38, 35, 35, 35, 44, 44, 44, 44, 44, 44, 81, 81, 81, 81, 81, 35, 46, 42, 45, 0, 1, 2, 8, 31, 32, 41, 3, 4, 6, 34, 35, 35, 35, 35, 35, 35, 44, 44, 44, 44, 80, 80, 80, 80, 80, 35, 43, 1, 16, 35, 35, 5, 2, 0, 1, 2, 6, 7, 8, 34, 35, 35, 35, 35, 35, 35, 35, 44, 44, 44, 73, 85, 85, 85, 85, 36, 40, 4, 34, 35, 44, 35, 0, 3, 4, 5, 3, 4, 5, 31, 32, 41, 41, 45, 44, 35, 35, 44, 44, 44, 44, 84, 84, 84, 89, 46, 1, 2, 34, 35, 36, 2, 3, 6, 44, 8, 6, 7, 8, 7, 8, 2, 0, 42, 41, 41, 45, 44, 35, 26, 25, 89, 89, 89, 44, 43, 4, 5, 31, 32, 40, 1, 6, 45, 44, 44, 3, 4, 0, 13, 14, 14, 14, 15, 4, 5, 42, 41, 32, 41, 28, 88, 88, 88, 35, 43, 7, 8, 8, 8, 3, 37, 38, 44, 44, 44, 0, 1, 3, 16, 17, 17, 35, 18, 23, 14, 15, 2, 6, 13, 14, 87, 87, 87, 35, 18, 23, 22, 7, 8, 6, 34, 35, 44, 44, 44, 0, 1, 2, 34, 35, 35, 35, 35, 35, 17, 18, 23, 23, 27, 17, 84, 84, 74, 74, 35, 26, 25, 2, 2, 3, 42, 41, 32, 45, 44, 14, 14, 22, 31, 32, 32, 45, 35, 35, 35, 35, 35, 35, 35, 44, 89, 89, 89, 89, 35, 35, 18, 23, 23, 14, 15, 3, 4, 42, 41, 26, 35, 18, 22, 24, 23, 27, 35, 35, 35, 35, 35, 44, 44, 88, 88, 88, 88, 88, 88, 73, 35, 35, 26, 17, 18, 22, 7, 8, 7]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0]}