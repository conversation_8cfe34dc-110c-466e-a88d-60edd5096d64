{"mW": 912, "mH": 672, "tW": 24, "tH": 24, "tiles": [["315_4", 0, 3, 3], ["1314", 0, 3, 2], ["1314", 2, 3, 2], ["1314", 1, 3, 2], ["1314", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2]], "layers": [{"type": 2, "data": [62, 62, 62, 62, 79, 87, 62, 79, 82, 82, 76, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 79, 80, 87, 86, 86, 78, 86, 62, 79, 87, 62, 62, 62, 62, 62, 62, 79, 80, 75, 73, 74, 75, -1, -1, 73, 74, 88, 87, 62, 78, 78, 78, 79, 74, 87, 78, 62, 79, 75, -1, 83, 82, 87, 86, 79, 82, 75, 83, 80, 88, 87, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 87, 86, 79, 75, -1, 73, 74, 82, 75, -1, -1, -1, -1, 83, 82, 81, -1, -1, -1, -1, -1, 83, 87, 62, 62, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, 57, -1, -1, 83, 82, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 71, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 66, 71, 70, 62, 79, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 64, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 78, 62, 78, 86, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 71, 78, 78, 70, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 62, 79, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 78, 78, 79, 80, 80, 75, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 64, 59, -1, -1, 77, 62, 62, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 78, 78, 63, 58, 58, 59, 67, 66, 58, 59, -1, -1, 67, 66, 71, 62, 62, 63, 59, -1, 61, 62, 62, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 62, 62, 86, 63, 71, 62, 62, 63, 59, -1, 83, 87, 70, 70, 70, 62, 63, 64, 71, 62, 62, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 78, 79, 82, 82, 87, 62, 62, 62, 62, 63, 64, 58, 71, 70, 70, 70, 70, 62, 62, 62, 62, 62, 70, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 74, 75, -1, -1, 83, 82, 82, 82, 87, 62, 62, 62, 70, 70, 62, 62, 62, 62, 62, 62, 62, 62, 62, 69, -1, -1, -1, -1, -1, 67, 58, 64, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 87, 86, 70, 62, 79, 80, 87, 62, 62, 62, 62, 62, 62, 63, 59, -1, -1, -1, 67, 71, 70, 62, 63, 66, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 82, 74, 75, -1, 83, 88, 87, 62, 62, 62, 62, 62, 63, 64, 64, 72, 71, 62, 62, 78, 62, 62, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 79, 87, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 62, 62, 62, 62, 62, 79, 74, 75, 83, 74, 75, 83, 82, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 62, 62, 62, 79, 75, -1, -1, -1, -1, -1, -1, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 79, 74, 74, 75, -1, -1, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 79, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 66, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 71, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 79, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 78, 62, 63, 65, -1, -1, -1, -1, -1, 57, 58, 58, 59, -1, -1, -1, 57, 71, 62, 85, -1, 67, 66, 59, -1, -1, -1, -1, 67, 66, 59, -1, 67, 66, 66, 59, -1, 61, 78, 62, 62, 63, 59, -1, -1, 67, 66, 60, 70, 70, 63, 58, 59, 67, 71, 62, 62, 63, 66, 71, 70, 63, 64, 64, 64, 64, 60, 62, 63, 64, 60, 62, 62, 68, 66, 71, 78, 70, 62, 62, 63, 58, 72, 71, 62, 62, 62, 62, 62, 62, 63, 71, 62, 62, 62]}, {"type": 3, "obj": [[2, "1308_1", 759, 317, 22, 37, 0], [2, "1308_1", 582, 1, 22, 37, 0], [2, "1310_1", 504, 47, 18, 29, 0], [2, "1310_1", 839, 145, 18, 29, 0], [2, "1310_1", 802, 142, 18, 29, 0], [2, "1310_1", 656, 233, 18, 29, 0], [2, "1308_1", 778, 327, 22, 37, 0], [2, "1308_1", 832, 345, 22, 37, 0], [2, "1309_1", 650, 16, 20, 32, 0], [2, "1310_1", 806, 7, 18, 29, 0], [2, "1309_1", 684, 47, 20, 32, 0], [2, "1309_1", 195, 3, 20, 32, 2], [2, "1308_1", 102, 6, 22, 37, 0], [2, "1308_1", 335, 26, 22, 37, 0], [2, "1310_1", 862, 385, 18, 29, 0], [2, "1308_1", 755, 307, 22, 37, 0], [2, "1308_1", 69, 19, 22, 37, 0], [2, "1310_1", 802, 336, 18, 29, 0], [2, "1308_1", 844, 363, 22, 37, 0], [2, "1310_1", 198, 3, 18, 29, 2], [2, "1308_1", 416, 543, 22, 37, 0], [2, "1308_1", 304, 401, 22, 37, 2], [2, "1308_1", 102, 427, 22, 37, 2], [2, "1310_1", 133, 17, 18, 29, 2], [2, "1308_1", 61, 127, 22, 37, 0], [2, "1308_1", 855, 21, 22, 37, 0], [2, "1308_1", 591, 312, 22, 37, 0], [2, "1310_1", 520, 283, 18, 29, 0], [2, "1308_1", 553, 286, 22, 37, 0], [2, "1310_1", 426, 249, 18, 29, 0], [2, "1310_1", 628, 316, 18, 29, 0], [2, "1310_1", 612, 310, 18, 29, 0], [2, "1308_1", 569, 277, 22, 37, 0], [2, "1308_1", 494, 287, 22, 37, 0], [2, "1310_1", 477, 187, 18, 29, 0], [2, "1308_1", 399, 274, 22, 37, 0], [2, "1310_1", 433, 258, 18, 29, 0], [2, "1308_1", 816, 18, 22, 37, 0], [2, "1308_1", 812, 8, 22, 37, 0], [2, "1310_1", 883, 49, 18, 29, 0], [2, "1310_1", 20, 587, 18, 29, 2], [2, "1308_1", 9, 496, 22, 37, 0], [2, "1308_1", 91, 443, 22, 37, 0]]}, {"type": 4, "obj": []}, {"type": 3, "obj": [[2, "313_1", 275, 507, 70, 44, 0], [2, "313_1", 437, 506, 70, 44, 0], [2, "1144_1", 318, 495, 114, 70, 2], [2, "1311_1", 584, 303, 44, 81, 2], [2, "1311_1", 549, 294, 44, 81, 0], [2, "1311_1", 396, 273, 44, 81, 0], [2, "1311_1", 611, 325, 44, 81, 2], [2, "1311_1", 174, 401, 44, 81, 2], [2, "1311_1", 248, 389, 44, 81, 0], [2, "1312_1", 26, 458, 36, 78, 0], [2, "1311_1", 77, 438, 44, 81, 0], [2, "1311_1", 430, 27, 44, 81, 0], [2, "1311_1", 162, 8, 44, 81, 2], [2, "1312_1", 144, 12, 36, 78, 0], [2, "11_1", 803, 204, 32, 29, 0], [2, "1312_1", 633, 336, 36, 78, 2], [2, "1312_1", 458, 547, 36, 78, 0], [2, "1311_1", 682, 343, 44, 81, 0], [2, "1311_1", 0, 163, 44, 81, 0], [2, "1306", 804, 235, 34, 33, 2], [2, "1312_1", 682, -2, 36, 78, 0], [2, "1311_1", 650, 26, 44, 81, 0], [2, "1312_1", 197, 405, 36, 78, 2], [2, "1311_1", 298, 11, 44, 81, 0], [2, "1312_1", 270, 14, 36, 78, 0], [2, "1311_1", 104, 9, 44, 81, 2], [2, "1309_1", 172, 5, 20, 32, 2], [2, "1308_1", 154, 2, 22, 37, 0], [2, "1311_1", 201, 22, 44, 81, 2], [2, "1311_1", 234, 10, 44, 81, 0], [2, "212_1", 320, 21, 44, 99, 0], [2, "1311_1", 27, 43, 44, 81, 0], [2, "1306", 28, 95, 34, 33, 0], [2, "212_1", 41, 447, 44, 99, 0], [2, "1311_1", 219, 349, 44, 81, 2], [2, "1306", 280, 67, 34, 33, 0], [2, "1311_1", 556, 19, 44, 81, 0], [2, "1311_1", 608, 5, 44, 81, 2], [2, "212_1", 578, 1, 44, 99, 0], [2, "1306", 627, 80, 34, 33, 0], [2, "1312_1", 700, -5, 36, 78, 2], [2, "1311_1", 726, 1, 44, 81, 2], [2, "1311_1", 767, 7, 44, 81, 0], [2, "1312_1", 820, 212, 36, 78, 2], [2, "1311_1", 842, 221, 44, 81, 2], [2, "1311_1", 868, 500, 44, 81, 2], [2, "212_1", 655, 337, 44, 99, 2], [2, "1311_1", 861, 581, 44, 81, 2], [2, "1311_1", 127, 406, 44, 81, 0], [2, "212_1", 275, 401, 44, 99, 0], [2, "1306", 809, 532, 34, 33, 0], [2, "1311_1", 417, 557, 44, 81, 2], [2, "1311_1", 531, 47, 44, 81, 0], [2, "1306", 839, 597, 34, 33, 2], [2, "1312_1", 857, 369, 36, 78, 2], [2, "1312_1", 662, 240, 36, 78, 2], [2, "212_1", 821, 146, 44, 99, 0], [2, "212_1", 678, 25, 44, 99, 0], [2, "1312_1", 417, 46, 36, 78, 0], [2, "212_1", 197, 6, 44, 99, 0], [2, "1311_1", 53, 120, 44, 81, 0], [2, "212_1", 30, 135, 44, 99, 2], [2, "366_2", 80, 156, 32, 48, 0], [2, "1311_1", -10, 194, 44, 81, 0], [2, "1311_1", 387, 68, 44, 81, 0], [2, "364_2", 278, 60, 44, 64, 2], [2, "1311_1", 462, 17, 44, 81, 0], [2, "1311_1", 483, 38, 44, 81, 2], [2, "212_1", 518, 31, 44, 99, 2], [2, "954_1", 60, 244, 24, 25, 0], [2, "955_1", 111, 326, 20, 18, 0], [2, "955_1", 783, 168, 20, 18, 0], [2, "1306", 187, 640, 34, 33, 0], [2, "1312_1", 225, 404, 36, 78, 0], [2, "1312_1", 158, 405, 36, 78, 0], [2, "1311_1", 101, 425, 44, 81, 0], [2, "954_1", 732, 65, 24, 25, 0], [2, "364_2", 846, 426, 44, 64, 2], [2, "1306", 365, 279, 34, 33, 2], [2, "1311_1", -7, 592, 44, 81, 0], [2, "1311_1", 355, 47, 44, 81, 0], [2, "1302", 684, 409, 40, 29, 0], [2, "8_2", 443, 95, 38, 29, 0], [2, "13_2", 462, 83, 22, 24, 0], [2, "12_2", 440, 85, 26, 28, 0], [2, "955_1", 488, 110, 20, 18, 0], [2, "1302", 216, 90, 40, 29, 2], [2, "1301", 263, 66, 24, 49, 2], [2, "1301", 152, 51, 24, 49, 0], [2, "1303", 239, 93, 34, 20, 0], [2, "8_2", 244, 468, 38, 29, 0], [2, "13_2", 263, 456, 22, 24, 0], [2, "12_2", 241, 458, 26, 28, 0], [2, "1302", 150, 467, 40, 29, 2], [2, "1301", 315, 459, 24, 49, 0], [2, "1301", 420, 594, 24, 49, 2], [2, "1302", 37, 262, 40, 29, 0], [2, "1303", 37, 295, 34, 20, 0], [2, "1304", 96, 271, 22, 19, 0], [2, "1305", 95, 201, 20, 14, 0], [2, "1301", 62, 185, 24, 49, 0], [2, "1306", 282, 483, 34, 33, 0], [2, "1302", 579, 89, 40, 29, 2], [2, "1303", 766, 86, 34, 20, 0], [2, "411_2", 196, 457, 44, 40, 2], [2, "411_2", 849, 489, 44, 40, 2], [2, "955_1", 531, 594, 20, 18, 2], [2, "1302", 656, 613, 40, 29, 0], [2, "1304", 94, 511, 22, 19, 0], [2, "1301", 802, 200, 24, 49, 0], [2, "955_1", 338, 472, 20, 18, 3], [2, "1303", 348, 223, 34, 20, 0], [2, "1303", 643, 128, 34, 20, 2], [2, "11_3", 76, 89, 32, 29, 0], [2, "955_1", 306, 508, 20, 18, 0], [2, "366_2", 477, 528, 32, 48, 2], [2, "954_1", 324, 496, 24, 25, 0], [2, "954_1", 528, 123, 24, 25, 0], [2, "1311_1", 74, 23, 44, 81, 0], [2, "1311_1", 45, 33, 44, 81, 0], [2, "1311_1", 436, 177, 44, 81, 0], [2, "364_2", 609, 369, 44, 64, 2], [2, "1311_1", 397, 181, 44, 81, 2], [2, "1311_1", 341, 262, 44, 81, 2], [2, "212_1", 366, 258, 44, 99, 2], [2, "313_1", 210, 320, 70, 44, 2], [2, "1301", 258, 324, 24, 49, 0], [2, "8_2", 118, 275, 38, 29, 2], [2, "955_1", 150, 309, 20, 18, 0], [2, "1320_2", 52, 263, 76, 64, 0], [2, "12_2", 78, 268, 26, 28, 0], [2, "1304", 112, 308, 22, 19, 0], [2, "8_2", 104, 313, 38, 29, 0], [2, "8_2", 37, 306, 38, 29, 2], [2, "11_3", 175, 77, 32, 29, 0], [2, "11_3", 21, 516, 32, 29, 0], [2, "1304", 167, 648, 22, 19, 0], [2, "1311_1", 487, 289, 44, 81, 2], [2, "212_1", 513, 279, 44, 99, 0], [2, "1311_1", 430, 264, 44, 81, 0], [2, "1311_1", 451, 264, 44, 81, 0], [2, "1311_1", 468, 271, 44, 81, 2], [2, "1306", 581, 358, 34, 33, 0], [2, "1303", 527, 371, 34, 20, 2], [2, "1302", 453, 340, 40, 29, 2], [2, "1311_1", 832, 359, 44, 81, 0], [2, "1311_1", 770, 355, 44, 81, 2], [2, "212_1", 796, 347, 44, 99, 0], [2, "1311_1", 713, 333, 44, 81, 0], [2, "1311_1", 732, 311, 44, 81, 0], [2, "1311_1", 751, 336, 44, 81, 2], [2, "1303", 810, 446, 34, 20, 2], [2, "1302", 727, 395, 40, 29, 2], [2, "1306", 396, 534, 34, 33, 0], [2, "1301", 421, 493, 24, 49, 2], [2, "955_1", 395, 566, 20, 18, 3], [2, "1306", 57, 332, 34, 33, 0], [2, "11_3", 507, 566, 32, 29, 0], [2, "11_3", 409, 630, 32, 29, 0], [2, "1306", 113, 477, 34, 33, 0], [2, "1306", 403, 323, 34, 33, 2], [2, "1306", 780, 57, 34, 33, 2], [2, "181_1", 803, 26, 104, 100, 0], [2, "181_1", 780, 357, 104, 100, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 49, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, 55, -1, 49, 49, 49, 49, -1, 55, 55, -1, 55, 55, 49, 50, 52, 56, 49, 49, 49, 49, 49, 43, 43, 43, 49, 49, 49, 50, -1, -1, 37, 37, 37, 37, 49, 50, 52, 49, 49, 50, 50, 46, 46, 56, 55, 55, 55, 37, 37, 37, 50, 47, -1, 45, 46, 56, 55, 49, 49, 56, 43, 43, 46, 49, 50, 55, 55, -1, 50, 52, 52, 52, 56, 38, 49, 50, 46, 47, 17, 16, 15, 53, 52, 55, 55, 37, 37, 50, 51, 9, 10, 16, 15, 53, 52, 46, 52, 52, 56, 43, 49, 50, 56, 55, 55, 50, 51, -1, -1, -1, 53, 56, 49, 54, 9, 10, 20, 26, 27, 55, -1, 55, 55, 55, 37, 54, -1, 12, 13, 19, 14, 16, 15, -1, -1, -1, 45, 46, 46, 51, 53, 52, 52, 51, -1, 17, 16, 15, -1, 53, 52, 51, 12, 31, 19, 31, -1, 55, -1, 27, -1, 37, 37, 54, 17, 20, 31, 25, 25, 25, 14, 16, 15, -1, -1, 15, -1, -1, -1, -1, -1, -1, -1, 24, 19, 14, 16, 16, 15, -1, 21, 22, 28, 27, 40, -1, -1, -1, 37, 37, 37, 54, 24, 25, 25, 25, 25, 25, 31, 31, 30, -1, -1, 30, -1, -1, -1, 40, 39, -1, -1, 24, 31, 19, 19, 26, 27, -1, -1, -1, -1, 44, 43, -1, -1, 37, 37, 50, 52, 51, 21, 22, 32, 19, 25, 25, 31, 26, 23, -1, 26, 27, -1, -1, 49, 43, 38, -1, -1, 21, 28, 28, 28, 27, -1, 29, 28, 33, 34, 35, 49, -1, -1, 37, 52, 51, -1, -1, -1, -1, 29, 28, 28, 32, 25, 14, 15, -1, 27, -1, -1, 50, 46, 43, 43, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 38, 46, -1, -1, 50, 51, 33, 34, 40, 39, -1, 33, 34, 35, 29, 32, 31, 30, 33, -1, -1, 49, 50, 52, 53, 52, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, 45, 46, -1, -1, -1, 33, 34, 55, 49, 49, 40, 39, 45, 56, 55, 39, 24, 31, 30, 33, 45, 45, 46, 47, -1, -1, -1, 48, -1, -1, 29, 28, -1, 49, 49, 49, 42, -1, -1, -1, -1, -1, -1, 55, 55, 55, 49, 49, 50, 51, -1, 53, 52, 42, 21, 22, 23, 36, 55, 15, 33, 34, 40, 39, 41, 40, 40, 34, 35, -1, 49, 52, 52, 52, 43, 43, -1, -1, -1, -1, 46, 55, 49, 55, 49, 46, 47, -1, -1, -1, -1, 46, 47, -1, -1, 45, 56, 49, 50, 52, 52, 56, 55, 45, 46, 56, 40, 40, 10, 16, 15, 43, 43, 43, 43, 43, -1, -1, -1, 55, 55, 43, 38, -1, -1, -1, -1, -1, -1, -1, -1, 40, -1, -1, 53, 46, 47, -1, -1, 53, 52, -1, 56, 55, 56, 49, 55, 26, 27, 43, 43, 43, 43, 43, 43, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 16, 16, 15, -1, -1, 53, 52, 44, 43, 55, 23, -1, -1, 49, 50, 56, 55, 43, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, 9, -1, -1, -1, 24, 19, 19, 19, 14, 16, 15, -1, 48, 49, 55, 55, 49, 37, 49, 50, 56, 55, 52, 56, -1, -1, 55, 55, 49, 50, -1, 50, 46, 17, 16, 16, 49, 49, 40, 40, 39, 9, 20, 31, 26, 32, 31, 19, 18, -1, 45, 56, 49, 49, 50, 56, 50, 47, 53, 52, 56, 55, -1, -1, -1, 49, 50, 46, 46, 9, 49, 49, 49, 49, 49, 49, 43, 43, 42, 29, 32, 19, 30, 29, 28, 28, 27, -1, -1, 53, 52, 46, 47, 53, 47, 17, 16, 15, 53, 52, 37, -1, -1, 46, 40, 39, 49, 50, 49, 50, 46, 46, 46, 56, 55, 55, 54, -1, 29, 22, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 20, 19, 14, 15, 48, 37, -1, -1, -1, 49, 49, 49, 50, 46, 47, 17, 16, 15, 53, 52, 51, 17, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 16, 10, 20, 31, 25, 25, 30, 48, 37, -1, 49, 55, 55, 55, 50, 47, 17, 16, 20, 31, 14, 16, 16, 16, 20, 19, 30, 33, -1, -1, 34, -1, 47, -1, 9, 10, 20, 19, 19, 13, 19, 25, 26, 28, 27, 44, 37, -1, 52, 55, 50, 46, 51, -1, 24, 25, 31, 31, 25, 19, 25, 19, 26, 28, 27, 36, -1, 45, 37, 38, 39, -1, 24, 19, 19, 19, 19, 25, 19, 19, 30, 33, 44, 43, 37, -1, -1, 55, 54, -1, -1, -1, 24, 25, 31, 31, 31, 31, 31, 31, 30, 33, 40, 40, 34, 15, 45, 46, 42, -1, 29, 28, 22, 22, 32, 31, 31, 26, 27, 41, 37, 43, 37, -1, 33, 55, 51, -1, -1, -1, 21, 22, 28, 28, 32, 25, 31, 26, 27, 45, 56, 49, 37, 14, 16, 49, 40, 39, -1, 41, 40, 39, 29, 28, 28, 27, 35, 48, 37, 49, 37, -1, 37, 38, 39, -1, -1, -1, -1, -1, -1, -1, 21, 22, 28, 23, -1, -1, 45, 56, 49, 31, 25, 14, 43, 38, 40, 44, 43, 42, 39, 49, 22, 37, 38, 44, 49, 48, 43, -1, -1, 43, 38, 44, 43, -1, 47, 49, 49, 49, -1, 40, 40, 40, -1, -1, 49, 44, 49, 49, 49, -1, -1, 43, 49, 49, 43, 49, -1, -1, -1, -1, 29, 49, 49, 44, 43, -1]}, {"type": 2, "data": [0, 1, 0, 1, 2, 3, 4, 5, 1, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 1, 2, 0, 0, 1, 2, 1, 2, 0, 1, 2, 1, 2, 1, 2, 0, 1, 2, 0, 3, 0, 1, 2, 2, 6, 7, 8, 4, 3, 4, 5, 3, 3, 4, 5, 3, 4, 5, 4, 5, 3, 3, 4, 5, 4, 5, 3, 4, 5, 4, 5, 4, 0, 1, 2, 2, 3, 6, 3, 4, 5, 5, 3, 0, 1, 2, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 7, 8, 0, 1, 2, 0, 1, 2, 6, 7, 8, 7, 8, 7, 3, 4, 5, 5, 6, 0, 6, 7, 8, 8, 0, 0, 1, 2, 3, 4, 5, 1, 2, 1, 2, 3, 0, 1, 2, 1, 3, 4, 5, 3, 4, 5, 0, 1, 2, 0, 1, 2, 6, 7, 8, 2, 6, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 4, 5, 6, 3, 4, 5, 0, 6, 7, 8, 6, 7, 8, 3, 4, 0, 1, 2, 5, 3, 3, 4, 5, 0, 6, 7, 0, 1, 2, 5, 6, 0, 1, 0, 1, 2, 4, 5, 0, 1, 2, 6, 7, 8, 2, 4, 5, 2, 3, 0, 1, 2, 7, 3, 4, 5, 0, 1, 6, 7, 8, 3, 0, 1, 3, 4, 5, 8, 0, 3, 4, 3, 0, 1, 2, 0, 1, 2, 5, 5, 3, 4, 5, 0, 0, 1, 0, 1, 2, 5, 6, 6, 7, 8, 3, 4, 5, 6, 7, 6, 0, 1, 2, 7, 0, 0, 1, 6, 7, 6, 3, 4, 5, 3, 4, 5, 8, 1, 6, 7, 8, 3, 3, 4, 3, 4, 5, 8, 1, 2, 4, 0, 6, 7, 8, 5, 3, 3, 3, 4, 5, 1, 3, 3, 4, 3, 4, 5, 6, 7, 8, 6, 7, 8, 3, 4, 0, 1, 2, 1, 6, 7, 6, 0, 1, 2, 4, 5, 7, 3, 6, 7, 8, 0, 1, 6, 6, 7, 8, 4, 6, 6, 7, 6, 7, 8, 3, 4, 5, 4, 5, 3, 6, 7, 3, 4, 5, 4, 5, 5, 1, 3, 4, 5, 1, 2, 0, 6, 7, 8, 1, 3, 4, 0, 0, 1, 6, 7, 8, 8, 0, 0, 1, 2, 6, 7, 0, 1, 2, 6, 6, 3, 6, 7, 8, 7, 8, 8, 4, 6, 7, 8, 0, 1, 2, 4, 5, 3, 4, 6, 7, 3, 3, 4, 0, 1, 2, 3, 3, 3, 4, 5, 8, 6, 3, 4, 5, 2, 0, 1, 0, 1, 2, -1, 4, 5, 0, 1, 2, 8, 3, 4, 5, 7, 8, 6, 7, 8, 6, 6, 6, 7, 3, 4, 5, 6, 6, 6, 7, 8, 5, 0, 6, 7, 8, 5, 3, 4, 3, 4, 0, 1, 2, 8, 3, 4, 5, 1, 2, 7, 8, 2, 0, 3, 0, 1, 2, 6, 3, 4, 6, 7, 8, 7, 8, 1, 2, 8, 8, 3, 0, 6, 7, 8, 6, 7, 6, 7, 3, 4, 5, 8, 6, 7, 8, 4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 6, 7, 8, 0, 1, 0, 3, 4, 5, 4, 0, 1, 3, 4, 0, 1, 2, 4, 5, 3, 6, 7, 8, 0, 0, 3, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 0, 1, 0, 0, 1, 3, 6, 7, 8, 2, 3, 4, 6, 7, 3, 4, 5, 7, 8, 6, 3, 0, 1, 2, 3, 6, 6, 7, 0, 6, 7, 8, 6, 7, 8, 6, 7, 8, 3, 4, 3, 0, 1, 2, 7, 3, 4, 5, 6, 7, 8, 4, 6, 7, 8, 2, 4, 5, 0, 1, 2, 5, 6, 7, 8, 3, 3, 4, 5, 3, 4, 5, 3, 4, 5, 0, 6, 0, 6, 3, 4, 5, 6, 6, 7, 8, 0, 1, 6, 7, 8, 0, 1, 2, 7, 0, 1, 2, 0, 1, 2, 7, 0, 6, 6, 7, 8, 6, 7, 8, 6, 7, 8, 3, 0, 1, 2, 6, 7, 8, 0, 1, 2, 2, 3, 4, 5, 0, 1, 2, 4, 5, 5, 3, 4, 5, 3, 4, 5, 2, 3, 4, 5, 3, 4, 5, 3, 4, 6, 7, 8, 6, 3, 4, 5, 3, 3, 3, 3, 4, 5, 5, 6, 7, 8, 3, 4, 5, 7, 8, 0, 6, 7, 8, 6, 7, 8, 0, 1, 2, 8, 6, 7, 8, 6, 7, 8, 3, 4, 0, 6, 7, 8, 0, 1, 6, 6, 7, 8, 8, 0, 1, 2, 6, 7, 8, 5, 4, 0, 1, 2, 8, 4, 5, 4, 3, 4, 5, 0, 1, 2, 2, 3, 4, 5, 6, 7, 3, 0, 0, 1, 3, 4, 5, 0, 3, 4, 5, 3, 4, 0, 6, 7, 8, 0, 0, 3, 4, 5, 6, 7, 8, 7, 6, 7, 8, 3, 4, 5, 8, 6, 7, 8, 3, 4, 6, 3, 3, 4, 6, 7, 8, 0, 1, 2, 1, 0, 1, 2, 2, 5, 0, 3, 3, 6, 7, 8, 4, 3, 4, 5, 6, 7, 8, 6, 7, 8, 5, 0, 1, 2, 0, 1, 0, 6, 6, 7, 0, 0, 4, 3, 4, 5, 4, 3, 5, 5, 5, 8, 0, 6, 6, 7, 8, 8, 0, 6, 7, 8, 4, 5, 3, 0, 1, 2, 8, 3, 4, 5, 0, 1, 3, 0, 0, 1, 3, 3, 4, 6, 7, 8, 0, 6, 7, 8, 8, 5, 3, 4, 3, 4, 5, 0, 3, 4, 5, 6, 7, 8, 6, 3, 4, 5, 2, 6, 7, 8, 3, 4, 6, 3, 3, 4, 6, 6, 7, 3, 4, 5, 3, 4, 5, 7, 8, 8, 6, 7, 6, 7, 8, 0, 6, 7, 8, 2, 3, 4, 5, 6, 0, 1, 2, 2, 0, 1, 6, 7, 0, 6, 6, 7, 0, 3, 4, 6, 7, 8, 6, 7, 8, 2, 0, 1, 3, 4, 5, 5, 3, 3, 4, 3, 4, 5, 6, 7, 8, 4, 3, 4, 5, 5, 3, 4, 5, 8, 3, 0, 1, 2, 3, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 4, 6, 7, 8, 8, 6, 6, 7, 6, 7, 8, 7, 8, 6, 7, 6, 7, 8, 8, 6, 7, 8, 6, 6]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}