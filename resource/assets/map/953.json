{"mW": 960, "mH": 672, "tW": 24, "tH": 24, "tiles": [["302_6", 0, 2, 2], ["302_6", 2, 2, 2], ["497_5", 0, 2, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1134", 0, 3, 2], ["1134", 2, 3, 2], ["1134", 1, 3, 2], ["1134", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "1129_1", 817, 214, 30, 29, 2], [2, "703_1", 825, 204, 16, 23, 2], [2, "1133_1", 839, 213, 24, 34, 2], [2, "3009", 839, 199, 32, 36, 0], [2, "1133_1", 802, 211, 24, 34, 0], [2, "3009", 796, 197, 32, 36, 2], [2, "87_2", 242, -6, 72, 57, 0], [2, "268_3", 294, -38, 106, 82, 0], [2, "1133_1", 478, 374, 24, 34, 0], [2, "3009", 472, 360, 32, 36, 2], [2, "1133_1", 515, 376, 24, 34, 2], [2, "3009", 515, 362, 32, 36, 0], [2, "1129_1", 493, 377, 30, 29, 2], [2, "703_1", 501, 367, 16, 23, 2], [2, "1328_1", 305, 448, 20, 29, 2], [2, "1328_1", 199, 505, 20, 29, 2], [2, "1328_1", 306, 562, 20, 29, 2], [2, "1328_1", 409, 613, 20, 29, 0], [2, "1129_1", 897, 569, 30, 29, 0], [2, "1319_1", 913, 577, 52, 42, 0], [2, "1129_1", 941, 591, 30, 29, 0], [2, "1328_1", 943, 572, 20, 29, 0], [2, "1129_1", 858, 214, 30, 29, 0], [2, "1319_1", 874, 222, 52, 42, 0], [2, "1129_1", 899, 235, 30, 29, 0], [2, "1319_1", 914, 242, 52, 42, 0], [2, "1129_1", 939, 255, 30, 29, 0], [2, "1319_1", 954, 262, 52, 42, 0], [2, "1328_1", 861, 195, 20, 29, 2], [2, "980", 338, 560, 58, 41, 0]]}, {"type": 4, "obj": [[2, "89_2", 287, 18, 48, 95, 0], [2, "1130_1", 816, 236, 30, 53, 0], [2, "713_1", 748, 265, 18, 27, 0], [2, "714_1", 739, 220, 54, 132, 0], [2, "4_5", 258, 266, 122, 119, 0], [2, "1128", 458, 323, 90, 99, 2], [2, "1130_1", 492, 399, 30, 53, 0], [2, "1128", 221, 393, 90, 99, 0], [2, "1319_1", 307, 451, 52, 42, 2], [2, "1129_1", 299, 467, 30, 29, 2], [2, "1319_1", 261, 475, 52, 42, 2], [2, "1319_1", 222, 495, 52, 42, 2], [2, "1319_1", 194, 510, 52, 42, 2], [2, "1319_1", 207, 531, 52, 42, 0], [2, "1319_1", 247, 551, 52, 42, 0], [2, "1302_2", 357, 570, 40, 29, 0], [2, "4_5", 854, 480, 122, 119, 0], [2, "1128", 305, 505, 90, 99, 2], [2, "1319_1", 287, 571, 52, 42, 0], [2, "1319_1", 327, 591, 52, 42, 0], [2, "1128", 373, 548, 90, 99, 2], [2, "1319_1", 367, 611, 52, 42, 0], [2, "1129_1", 406, 630, 30, 29, 2], [2, "1319_1", 372, 636, 52, 42, 2], [2, "1128", 792, 580, 90, 99, 0], [2, "1129_1", 361, 652, 30, 29, 2]]}, {"type": 3, "obj": [[2, "1141_1", 768, -60, 54, 67, 2], [2, "958_1", 594, 423, 90, 68, 0], [2, "958_1", 749, 612, 90, 68, 0], [2, "1252", 434, -51, 96, 71, 0], [2, "1129_1", 523, 12, 30, 29, 0], [2, "703_1", 530, 0, 16, 23, 2], [2, "1330", 394, -17, 112, 80, 2], [2, "1252", 349, -15, 96, 71, 0], [2, "990_1", 222, 438, 54, 27, 0], [2, "700_2", 209, 410, 22, 48, 2], [2, "700_2", 232, 410, 22, 48, 0], [2, "1328_1", 223, 394, 20, 29, 0], [2, "1302_2", 787, 147, 40, 29, 2], [2, "1303_1", 775, 183, 34, 20, 0], [2, "174_4", 864, 237, 68, 33, 0], [2, "1132_1", 829, 270, 48, 42, 0], [2, "1132_1", 857, 285, 48, 42, 0], [2, "1132_1", 886, 300, 48, 42, 0], [2, "1132_1", 794, 276, 48, 42, 2], [2, "1132_1", 766, 290, 48, 42, 2], [2, "1132_1", 738, 304, 48, 42, 2], [2, "1132_1", 710, 319, 48, 42, 2], [2, "1132_1", 682, 333, 48, 42, 2], [2, "1132_1", 654, 347, 48, 42, 2], [2, "1132_1", 627, 361, 48, 42, 2], [2, "1132_1", 599, 375, 48, 42, 2], [2, "1132_1", 571, 389, 48, 42, 2], [2, "1132_1", 361, 368, 48, 42, 0], [2, "1132_1", 388, 383, 48, 42, 0], [2, "1132_1", 416, 398, 48, 42, 0], [2, "1132_1", 445, 413, 48, 42, 0], [2, "1132_1", 472, 427, 48, 42, 0], [2, "1132_1", 825, 593, 48, 42, 0], [2, "1132_1", 854, 608, 48, 42, 0], [2, "1132_1", 881, 622, 48, 42, 0], [2, "1132_1", 909, 637, 48, 42, 0], [2, "1132_1", 924, 645, 48, 42, 0], [2, "1132_1", 332, 378, 48, 42, 2], [2, "1132_1", 304, 393, 48, 42, 2], [2, "1132_1", 276, 408, 48, 42, 2], [2, "1132_1", 325, 495, 48, 42, 2], [2, "1132_1", 297, 509, 48, 42, 2], [2, "1132_1", 269, 523, 48, 42, 2], [2, "1132_1", 241, 537, 48, 42, 2], [2, "1132_1", 213, 551, 48, 42, 2], [2, "1132_1", 935, 646, 48, 42, 2], [2, "1131_1", 876, 581, 48, 53, 0], [2, "1130_1", 896, 592, 30, 53, 0], [2, "1329_1", 879, 587, 22, 35, 0], [2, "1131_1", 919, 603, 48, 53, 0], [2, "1329_1", 922, 609, 22, 35, 0], [2, "1130_1", 24, 295, 30, 53, 2], [2, "1131_1", -10, 303, 48, 53, 2], [2, "1329_1", 8, 311, 22, 35, 2], [2, "1130_1", -21, 317, 30, 53, 2], [2, "1131_1", -4, 271, 48, 53, 0], [2, "1130_1", 939, 614, 30, 53, 0], [2, "703_1", 295, 401, 16, 23, 2], [2, "703_1", 356, 369, 16, 23, 2], [2, "990_1", 276, 465, 54, 27, 0], [2, "990_1", 305, 479, 54, 27, 0], [2, "1302_2", 903, 663, 40, 29, 2], [2, "1303_1", 874, 669, 34, 20, 0], [2, "1305_1", 934, 204, 20, 14, 0], [2, "1303_1", 360, 549, 34, 20, 0], [2, "1304_1", 299, 116, 22, 19, 0], [2, "1303_1", 286, 131, 34, 20, 0], [2, "90_1", -5, 343, 28, 36, 0], [2, "1145_1", 721, 599, 112, 61, 0], [2, "1319_1", -12, 253, 52, 42, 0], [2, "1129_1", 24, 272, 30, 29, 2], [2, "1319_1", -10, 278, 52, 42, 2], [2, "1129_1", -21, 294, 30, 29, 2], [2, "1133", 856, 571, 24, 34, 0], [2, "1133", 930, 601, 24, 34, 0], [2, "1328_1", 27, 256, 20, 29, 0], [2, "1193_1", -7, 65, 34, 71, 2], [2, "470_4", 16, 72, 18, 62, 0], [2, "470_4", 27, 80, 18, 62, 0], [2, "470_4", 121, 78, 18, 62, 2], [2, "470_4", 158, 61, 18, 62, 2], [2, "1212_1", 171, 79, 40, 58, 2], [2, "1211_1", 177, 59, 30, 35, 2], [2, "470_4", 43, 84, 18, 62, 0], [2, "470_4", 80, 84, 18, 62, 2], [2, "470_4", 64, 92, 18, 62, 2], [2, "1208_1", 61, 105, 52, 56, 2], [2, "1208_1", 15, 98, 52, 56, 0], [2, "1200_1", 74, 90, 24, 32, 0], [2, "1203_1", 157, 86, 24, 55, 2], [2, "1209_1", 122, 67, 66, 95, 2], [2, "1206_1", 143, 121, 14, 42, 2], [2, "1206_1", 181, 105, 14, 42, 0], [2, "1210_1", 136, 77, 62, 59, 2], [2, "1193_1", 51, 95, 34, 71, 0], [2, "3010", 26, 80, 22, 44, 2], [2, "1133_1", 200, 63, 24, 34, 2], [2, "3009", 200, 49, 32, 36, 0], [2, "3009", 20, 81, 32, 36, 0], [2, "181_3", 96, 63, 104, 100, 2], [2, "1212_1", 90, 106, 40, 58, 2], [2, "1211_1", 96, 88, 30, 35, 2], [2, "1230_1", 148, 21, 58, 34, 2], [2, "1228_1", 121, -12, 60, 75, 2], [2, "1228_1", 23, -9, 60, 75, 0], [2, "1227_1", 171, 6, 46, 62, 2], [2, "1228_1", 47, 3, 60, 75, 0], [2, "1197_1", 127, 42, 54, 44, 2], [2, "1228_1", 69, 12, 60, 75, 0], [2, "1228_1", 65, 18, 60, 75, 2], [2, "1228_1", 93, 20, 60, 75, 0], [2, "1197_1", 15, 53, 54, 44, 0], [2, "1228_1", 39, 28, 60, 75, 2], [2, "1228_1", 5, -12, 60, 75, 0], [2, "1227_1", 92, 34, 46, 62, 2], [2, "1230_1", -12, 38, 58, 34, 0], [2, "1225_2", 108, -26, 48, 44, 2], [2, "1225_2", 37, -35, 48, 44, 0], [2, "1229_2", 78, -11, 48, 39, 0], [2, "1226_2", 109, 5, 70, 47, 0], [2, "1226_2", 24, 12, 70, 47, 2], [2, "90_1", 55, 156, 36, 28, 7], [2, "90_1", 41, 158, 28, 36, 0], [2, "1133_1", 115, 94, 24, 34, 2], [2, "3009", 115, 80, 32, 36, 0], [2, "1244", 660, -34, 30, 67, 2], [2, "1141_1", 681, -16, 54, 67, 2], [2, "1141_1", 681, -60, 54, 67, 2], [2, "1141_1", 725, 6, 54, 67, 2], [2, "1141_1", 725, -38, 54, 67, 2], [2, "1141_1", 768, 28, 54, 67, 2], [2, "1141_1", 768, -16, 54, 67, 2], [2, "1141_1", 811, 49, 54, 67, 2], [2, "1141_1", 811, 5, 54, 67, 2], [2, "1141_1", 811, -38, 54, 67, 2], [2, "1249", 769, -4, 22, 53, 0], [2, "1249", 750, 2, 22, 53, 0], [2, "43_9", 746, 24, 82, 58, 0], [2, "955_5", 713, 73, 20, 18, 0], [2, "954_4", 834, 103, 24, 25, 0], [2, "1244", 855, 51, 30, 67, 2], [2, "1248", 750, -37, 22, 52, 0], [2, "1244", 855, 1, 30, 67, 2], [2, "1244", 854, -49, 30, 67, 2], [2, "181_3", 761, 6, 104, 100, 0], [2, "181_3", 711, -25, 104, 100, 0], [2, "1245", 737, -47, 76, 130, 2], [2, "1252", 748, 38, 96, 71, 2], [2, "700_2", 737, 97, 22, 48, 2], [2, "700_2", 760, 97, 22, 48, 0], [2, "1253", 824, 4, 22, 35, 2], [2, "1242", 359, 341, 96, 56, 0], [2, "1329_1", 681, -2, 22, 35, 0], [2, "1252", 622, -16, 96, 71, 2], [2, "1132_1", 865, 90, 48, 42, 0], [2, "1132_1", 892, 104, 48, 42, 0], [2, "1132_1", 922, 119, 48, 42, 0], [2, "1132_1", 950, 133, 48, 42, 0], [2, "1131_1", 866, 52, 48, 53, 0], [2, "1130_1", 896, 71, 30, 53, 0], [2, "1329_1", 879, 62, 22, 35, 0], [2, "1131_1", 911, 76, 48, 53, 0], [2, "1329_1", 920, 84, 22, 35, 0], [2, "1130_1", 936, 92, 30, 53, 0], [2, "1131_1", 950, 97, 48, 53, 0], [2, "1329_1", 959, 104, 22, 35, 0], [2, "1319_1", 869, 35, 52, 42, 0], [2, "1129_1", 894, 48, 30, 29, 0], [2, "1319_1", 909, 55, 52, 42, 0], [2, "1129_1", 937, 68, 30, 29, 0], [2, "1319_1", 953, 76, 52, 42, 0], [2, "1131_1", 867, 6, 48, 53, 0], [2, "1130_1", 897, 25, 30, 53, 0], [2, "1329_1", 880, 16, 22, 35, 0], [2, "1131_1", 912, 30, 48, 53, 0], [2, "1329_1", 921, 38, 22, 35, 0], [2, "1130_1", 937, 46, 30, 53, 0], [2, "1131_1", 951, 51, 48, 53, 0], [2, "1329_1", 960, 58, 22, 35, 0], [2, "1319_1", 870, -11, 52, 42, 0], [2, "1129_1", 895, 2, 30, 29, 0], [2, "1319_1", 910, 9, 52, 42, 0], [2, "1129_1", 938, 22, 30, 29, 0], [2, "1319_1", 954, 30, 52, 42, 0], [2, "1328_1", 934, -1, 20, 29, 2], [2, "1133_1", 889, 58, 24, 34, 0], [2, "3009", 883, 44, 32, 36, 2], [2, "1133_1", 942, 78, 24, 34, 0], [2, "3009", 936, 64, 32, 36, 2], [2, "700_2", 614, 32, 22, 48, 2], [2, "700_2", 637, 33, 22, 48, 0], [2, "1132_1", 246, 423, 48, 42, 2], [2, "990_1", 250, 452, 54, 27, 0], [2, "1132_1", 191, 579, 48, 42, 0], [2, "1132_1", 218, 593, 48, 42, 0], [2, "1131_1", 305, 477, 48, 53, 2], [2, "1329_1", 325, 483, 22, 35, 2], [2, "1130_1", 298, 488, 30, 53, 2], [2, "1131_1", 261, 497, 48, 53, 2], [2, "1329_1", 281, 503, 22, 35, 2], [2, "1130_1", 255, 511, 30, 53, 2], [2, "1131_1", 218, 520, 48, 53, 2], [2, "1329_1", 238, 526, 22, 35, 2], [2, "1130_1", 213, 532, 30, 53, 2], [2, "1329_1", 197, 543, 22, 35, 2], [2, "1130_1", 193, 546, 30, 53, 0], [2, "1131_1", 207, 551, 48, 53, 0], [2, "1130_1", 231, 568, 30, 53, 0], [2, "1329_1", 214, 559, 22, 35, 0], [2, "1131_1", 244, 568, 48, 53, 0], [2, "1130_1", 274, 587, 30, 53, 0], [2, "1329_1", 257, 578, 22, 35, 0], [2, "1131_1", 289, 592, 48, 53, 0], [2, "1329_1", 298, 600, 22, 35, 0], [2, "1303_1", 243, 473, 34, 20, 0], [2, "1129_1", 255, 490, 30, 29, 2], [2, "1129_1", 213, 511, 30, 29, 2], [2, "1129_1", 191, 523, 30, 29, 0], [2, "1129_1", 232, 544, 30, 29, 0], [2, "1129_1", 272, 564, 30, 29, 0], [2, "1129_1", 315, 584, 30, 29, 0], [2, "1132_1", 243, 606, 48, 42, 0], [2, "1132_1", 270, 620, 48, 42, 0], [2, "1132_1", 300, 635, 48, 42, 0], [2, "1132_1", 328, 649, 48, 42, 0], [2, "1130_1", 314, 608, 30, 53, 0], [2, "1131_1", 328, 613, 48, 53, 0], [2, "1329_1", 337, 620, 22, 35, 0], [2, "1303_1", 317, 679, 34, 20, 0], [2, "1130_1", 406, 653, 30, 53, 2], [2, "1131_1", 372, 661, 48, 53, 2], [2, "1329_1", 390, 669, 22, 35, 2], [2, "1130_1", 361, 675, 30, 53, 2], [2, "1131_1", 378, 629, 48, 53, 0], [2, "1132_1", 542, 403, 48, 42, 2], [2, "1132_1", 514, 417, 48, 42, 2], [2, "1132_1", 486, 431, 48, 42, 2], [2, "1302_2", 194, 459, 40, 29, 0], [2, "1303_1", 174, 483, 34, 20, 0], [2, "90_1", 831, 203, 28, 36, 0], [2, "174_4", 375, 115, 68, 33, 0], [2, "1129_1", 308, 388, 30, 29, 2], [2, "90_1", 362, 399, 36, 28, 7], [2, "90_1", 348, 401, 28, 36, 0], [2, "1328_1", 627, 18, 20, 29, 0], [2, "1328_1", 752, 80, 20, 29, 0], [2, "1132_1", 915, 315, 48, 42, 0], [2, "1132_1", 943, 330, 48, 42, 0], [2, "1330", 823, 125, 112, 80, 0], [2, "90_1", 938, 161, 36, 28, 7], [2, "90_1", 924, 163, 28, 36, 0], [2, "1303_1", 888, 209, 34, 20, 0], [2, "1302_2", 898, 183, 40, 29, 0], [2, "703_1", 729, 307, 16, 23, 2], [2, "703_1", 624, 359, 16, 23, 2], [2, "1132_1", 858, 270, 48, 42, 0], [2, "1132_1", 885, 284, 48, 42, 0], [2, "1132_1", 910, 297, 48, 42, 0], [2, "1132_1", 937, 311, 48, 42, 0], [2, "1130_1", 880, 223, 30, 53, 2], [2, "1329_1", 864, 234, 22, 35, 2], [2, "1130_1", 860, 237, 30, 53, 0], [2, "1131_1", 874, 242, 48, 53, 0], [2, "1130_1", 898, 259, 30, 53, 0], [2, "1329_1", 881, 250, 22, 35, 0], [2, "1131_1", 911, 259, 48, 53, 0], [2, "1130_1", 941, 278, 30, 53, 0], [2, "1329_1", 924, 269, 22, 35, 0], [2, "1131_1", 956, 283, 48, 53, 0], [2, "1329_1", 965, 291, 22, 35, 0], [2, "1133_1", 886, 274, 24, 34, 0], [2, "3009", 880, 260, 32, 36, 2], [2, "1133_1", 939, 296, 24, 34, 0], [2, "3009", 933, 282, 32, 36, 2], [2, "1302_2", 840, 306, 40, 29, 2], [2, "1303_1", 811, 312, 34, 20, 0], [2, "90_1", 350, 92, 36, 28, 7], [2, "90_1", 380, 85, 28, 36, 0], [2, "90_1", 526, 448, 28, 36, 0], [2, "1303_1", 505, 480, 34, 20, 0], [2, "1252", 799, 268, 96, 71, 2], [2, "1129_1", 797, 329, 30, 29, 2], [2, "703_1", 803, 318, 16, 23, 0], [2, "1252", 880, 312, 96, 71, 2], [2, "1128", 837, 281, 90, 99, 2], [2, "1129_1", 878, 373, 30, 29, 2], [2, "703_1", 884, 362, 16, 23, 0], [2, "700_2", 827, 329, 22, 48, 2], [2, "700_2", 850, 329, 22, 48, 0], [2, "1328_1", 841, 313, 20, 29, 0], [2, "1302_2", 918, 368, 40, 29, 0], [2, "1303_1", 908, 394, 34, 20, 0], [2, "90_1", 39, 331, 36, 28, 7], [2, "90_2", 884, 635, 28, 36, 0], [2, "1302_2", 399, 414, 40, 29, 0], [2, "1303_1", 389, 440, 34, 20, 0], [2, "1303_1", 181, 634, 34, 20, 0], [2, "1302_2", 191, 608, 40, 29, 0], [2, "1303_1", 154, 599, 34, 20, 0], [2, "1303_1", 768, 641, 34, 20, 0], [2, "700_2", 923, 375, 22, 48, 2], [2, "700_2", 946, 375, 22, 48, 0], [2, "1328_1", 937, 359, 20, 29, 0], [2, "1302_2", 15, 360, 40, 29, 0], [2, "958_1", 757, 363, 90, 68, 0], [2, "955_3", 875, 432, 20, 18, 0], [2, "954_3", 557, 441, 24, 25, 0], [2, "1129_1", 439, 47, 30, 29, 0], [2, "703_1", 445, 36, 16, 23, 2], [2, "1328_1", 479, 2, 20, 29, 2], [2, "700_2", 149, 559, 22, 48, 2], [2, "700_2", 172, 559, 22, 48, 0], [2, "1328_1", 163, 543, 20, 29, 0], [2, "1133_1", 229, 584, 24, 34, 0], [2, "3009", 223, 570, 32, 36, 2], [2, "1133_1", 311, 621, 24, 34, 0], [2, "3009", 305, 607, 32, 36, 2], [2, "1303_1", -2, 389, 34, 20, 0], [2, "958_1", 48, 358, 90, 68, 0], [2, "958_1", 107, 598, 90, 68, 0], [2, "980", 887, 369, 58, 41, 0], [2, "980", 833, 26, 58, 41, 0], [2, "980", 900, 281, 58, 41, 2], [2, "980", 132, 71, 58, 41, 2], [2, "1303_1", 707, 631, 34, 20, 2], [2, "1302_2", 117, 146, 40, 29, 0], [2, "43_8", 688, 337, 82, 58, 2], [2, "43_8", 637, 363, 82, 58, 2], [2, "1130_1", 411, 48, 30, 53, 0], [2, "1129_1", 412, 26, 30, 29, 2], [2, "703_1", 419, 16, 16, 23, 2], [2, "1133_1", 434, 25, 24, 34, 2], [2, "3009", 434, 11, 32, 36, 0], [2, "1133_1", 397, 23, 24, 34, 0], [2, "3009", 391, 9, 32, 36, 2], [2, "1145_1", 396, 613, 112, 61, 0], [2, "958_1", 394, 551, 90, 68, 0], [2, "1302_2", 493, 640, 40, 29, 0], [2, "1303_1", 532, 639, 34, 20, 0], [2, "954_3", 396, 553, 24, 25, 0], [2, "174_4", 493, 607, 68, 33, 0], [2, "1303_1", 395, 521, 34, 20, 0], [2, "1302_2", 614, 396, 40, 29, 0], [2, "1302_2", 846, 370, 40, 29, 0], [2, "1303_1", 857, 400, 34, 20, 0], [2, "1303_1", 765, 351, 34, 20, 0], [2, "1303_1", 319, 234, 34, 20, 0]]}, {"type": 2, "data": [26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, -1, -1, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 27, 29, 28, -1, 20, 26, 26, 26, -1, -1, -1, 26, 26, 26, 26, 26, 26, 26, -1, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 33, 26, 26, 26, 26, 26, 26, 27, 29, 28, -1, -1, -1, 20, 26, 26, 32, 26, 26, -1, -1, -1, 26, 26, 26, 26, 26, -1, -1, -1, -1, -1, -1, -1, -1, 26, 26, 33, 15, 15, 30, 29, 33, 32, 26, 26, 29, 28, -1, -1, 25, 26, 26, 26, 27, 29, 33, 32, 26, 32, 32, -1, -1, 26, 26, 26, 26, 26, 26, 26, 26, 26, -1, 20, 20, 26, 27, 27, 29, 29, -1, -1, 30, 29, 29, 28, 42, 41, 41, 40, 22, 23, 23, 23, 24, -1, 30, 33, 32, 32, 32, -1, -1, 26, 26, 26, 26, -1, 26, 26, 26, 26, 20, 20, 20, 26, 15, 12, -1, -1, -1, -1, -1, 34, 35, 38, 45, 44, 38, 39, 41, 40, -1, -1, -1, 18, 17, 21, 20, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 31, 20, 20, 26, 26, 14, 17, 16, -1, -1, 42, 41, 45, 44, 38, 38, 38, 56, 56, 53, 52, -1, -1, 18, 21, 20, 26, 26, 26, 26, 26, -1, -1, 26, 26, 33, 32, 26, 26, 26, 28, 30, 29, 33, 32, 14, 14, 19, -1, 42, 45, 44, 38, 38, 51, 53, 53, 52, -1, -1, -1, 10, 17, 21, 20, 26, 26, 27, 23, 33, 32, 26, 26, 26, 26, 30, 29, 26, 29, 28, -1, -1, -1, 30, 29, 33, 32, 19, 42, 41, 51, 53, 53, 53, 52, -1, -1, 18, 18, 17, 21, 21, 20, 26, 26, 26, 23, 24, 23, 30, 29, 33, 32, 26, 26, -1, -1, -1, -1, 34, 41, 41, 41, 41, 40, 30, 29, 34, 45, 53, 52, 14, 15, 14, 15, 17, 12, 18, 17, 20, 26, 26, 26, 26, 27, 23, 24, 18, 17, 16, -1, 30, 29, 33, 32, -1, -1, 34, 41, 45, 38, 38, 38, 38, 39, 41, 41, 45, 55, 21, 26, 26, 26, 26, 26, 14, 15, 21, 20, 26, 27, 26, 27, 23, 24, -1, 10, 33, 32, 26, 18, 17, 16, 30, 29, -1, -1, 37, 38, 38, 38, 38, 51, 53, 53, 53, 53, 53, 52, 26, 26, 26, 27, 29, 33, 26, 26, 26, 26, 26, 27, 23, 24, 42, 41, 41, 40, 30, 33, 32, 29, 31, -1, 18, 17, -1, -1, 49, 38, 38, 38, 51, 52, 18, 17, 21, 20, -1, -1, 26, 27, 23, 24, -1, 22, 33, 26, 27, 23, 23, 34, 35, 41, 45, 51, 53, 52, -1, 30, 29, 29, 33, 17, 21, 20, 26, 27, 46, 53, 53, 53, 52, 17, 21, 20, 26, 26, 26, 27, 23, 24, -1, -1, -1, -1, 22, 23, 24, 34, 35, 45, 38, 38, 51, 52, 18, 17, 16, -1, 18, 17, 21, 20, -1, -1, 26, 27, -1, -1, -1, 18, 21, 20, 26, 26, 26, 27, 23, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 38, 38, 38, 38, 55, 10, 21, 20, 15, 17, 21, 26, 26, 26, -1, 26, 23, 24, -1, -1, -1, 25, 20, 26, 26, 26, 23, 24, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, 46, 47, 57, 38, 38, 55, 22, 23, 29, 33, 20, 26, 26, 26, 26, 26, 26, 12, -1, -1, -1, -1, 25, 32, 26, 26, 26, 26, 11, 11, 12, -1, -1, -1, -1, -1, -1, -1, 14, 15, 11, 16, 46, 57, 38, 39, 41, 40, -1, 25, 26, -1, 26, 26, 26, 26, 26, 15, 12, -1, 34, 40, 30, 29, 33, 32, 26, -1, 14, 14, 26, 26, 26, 33, 32, -1, 32, 32, 32, 32, 14, 26, 27, 54, 57, 56, 44, 43, 18, 30, 29, -1, -1, 22, 33, 32, 32, 14, 15, 12, 49, 39, 41, 40, 30, 29, 33, 32, -1, 26, 26, 26, 26, 26, 29, 33, 32, 32, -1, 26, 26, 27, 32, 26, 54, 53, 53, 52, -1, -1, -1, -1, -1, -1, 30, 29, 29, 20, 14, 15, 49, 38, 38, 38, 39, 40, 30, 29, 26, 14, 15, 26, 29, 28, -1, 30, 29, 33, 32, 23, 23, 32, 32, 32, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 20, 20, 19, 49, 38, 38, 38, 39, 40, -1, -1, -1, -1, 14, 15, 11, 12, 34, 35, 36, 29, 33, 32, 15, 32, 14, 15, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 20, 26, 27, 28, 46, 57, 50, 44, 50, 43, -1, -1, -1, -1, -1, 14, 14, 31, 46, 57, 39, 40, 30, 29, 33, 32, 26, 26, 27, 31, -1, -1, -1, -1, -1, -1, 30, -1, 13, 14, 26, 27, 24, -1, -1, 54, 47, 53, 47, 48, -1, -1, -1, -1, 26, 29, 28, 24, -1, 49, 38, 39, 35, 36, 30, 33, 17, 23, 29, 28, -1, -1, 10, 11, 17, 17, 16, -1, 29, -1, 23, 24, -1, -1, -1, 18, 17, 21, 20, 26, 17, 16, 26, 26, 23, -1, -1, 28, 34, 45, 50, 50, 38, 39, 41, 36, -1, -1, -1, -1, 10, 11, 21, 20, 20, 14, 15, 17, -1, -1, -1, 11, 12, 18, 17, 21, 20, 26, -1, -1, 20, 19, 23, 23, 24, 12, -1, -1, 49, 38, 50, 50, 50, 50, 44, 43, -1, -1, -1, -1, 13, 14, 27, 26, 20, 26, 21, 20, -1, 26, -1, 14, 15, 21, 20, 26, 26, 26, 26, 26, -1, -1, -1, -1, 14, 15, 11, 12, 46, 57, 50, 53, 51, 53, 53, 52, -1, 18, 17, 16, 21, 20, 26, 26, 26, -1, -1, -1, -1, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, -1, -1, -1, 14, 15, 14, 15, 11, 46, 47, 53, 52, -1, -1, 18, 17, 21, 20, 19, 33, 32, 21, 20, 19, -1, -1, -1, -1, -1, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, -1, -1, -1, 14, 15, 14, 17, 16, -1, -1, 18, 17, 21, 20, 32, 32, 31, 30, 29, 33, 32, 32, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "990_1", 427, 72, 54, 27, 2], [2, "990_1", 455, 58, 54, 27, 2], [2, "990_1", 481, 43, 54, 27, 2], [2, "990_1", 514, 28, 54, 27, 2], [2, "990_1", 545, 17, 54, 27, 2], [2, "990_1", 578, 2, 54, 27, 2], [2, "990_1", 352, 33, 54, 27, 0], [2, "990_1", 377, 46, 54, 27, 0], [2, "990_1", 401, 60, 54, 27, 0], [2, "958_1", 786, 337, 90, 68, 0], [2, "958_1", 753, 173, 90, 68, 0], [2, "959_2", 674, 410, 40, 22, 0], [2, "959_2", 731, 523, 40, 22, 0], [2, "174_4", 514, 269, 68, 33, 0]]}, {"type": 2, "data": [0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 8, 9, 8, 9, 8, 9, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 3, 3, 2, 2, 2, 3, 3, 2, 3, 9, 8, 9, 9, 9, 8, 2, 3, 2, 3, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 4, -1, -1, -1, 5, 5, 4, 5, 4, 7, 6, 4, 5, 4, 0, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 9, 0, 1, 1, 2, 3, 0, 1, 0, 1, 6, 5, 4, -1, 0, 1, 6, 7, 6, 5, 4, 6, 7, 5, 2, 3, 9, 8, 9, 8, 9, 9, 9, 8, 9, 8, 9, 8, 9, 9, 9, 2, 3, 1, 0, 1, 2, 3, 2, 3, 8, 7, 6, 9, 2, 8, 9, 8, 9, 7, 6, 4, 7, 7, 5, 4, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 9, 9, 9, 9, 8, 2, 2, 3, 2, 3, 2, 3, 8, 9, 9, 8, 9, 8, 9, -1, 5, 8, 9, 9, 4, 5, 4, 7, 7, 5, 9, 9, 9, 8, 9, 9, 9, 8, 9, 8, 8, 9, 8, 0, 1, 0, 1, 0, 1, 0, 0, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 5, 4, 5, 4, 7, 7, 7, 6, 5, 4, 8, 9, 8, 9, 9, 9, 8, 0, 1, 2, 3, 2, 3, 2, 3, 0, 1, 3, 9, 9, 9, 8, 9, 9, 9, 8, 9, 9, 8, 9, 7, 5, 4, 5, 5, 4, 7, 6, 5, 4, 5, 4, 9, 8, 0, 1, 9, 2, 3, 3, 9, 8, 9, 8, 9, 2, 3, 0, 9, 9, 8, 9, 8, 9, 8, 8, 9, 8, 9, 9, 8, 9, 8, 9, 7, 6, 4, 7, 7, 5, 7, 6, 4, 0, 2, 3, 8, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9, 9, 8, 9, 8, 9, 9, 4, 0, 1, 4, 4, 4, 6, 7, 6, 2, 3, 8, 8, 8, 9, 9, 9, 8, 9, 8, 9, 8, 9, 8, 8, 9, 8, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 7, 8, 9, 9, 0, 1, 0, 1, 0, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, -1, 8, 9, 8, 9, 9, 9, 8, 9, 8, 9, 8, 9, 0, 8, 9, 9, 8, 9, 2, 3, 2, 3, 2, 3, 8, 9, 8, 9, 8, 8, 8, 9, 8, 9, 8, 9, 8, 9, 8, -1, 9, 8, 9, 9, 9, 9, 9, 9, 9, 8, 8, 9, 2, 3, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 8, 8, 9, 8, 9, 8, 9, 9, -1, 8, 9, 9, 9, 8, 9, 8, 0, 0, 8, 9, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 9, 9, 8, 9, 8, 9, 8, 8, 8, 9, 8, 9, 8, 9, 9, 0, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 3, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 9, 9, 8, 9, 8, 9, 8, 9, 0, 1, 0, 1, 1, 8, 9, 8, 9, 8, 9, 0, 8, 9, 9, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 9, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 3, 2, 3, 3, 8, 9, 9, 8, 9, 8, 2, 8, 9, 8, 9, 8, 9, 8, 9, 9, -1, 8, 8, 8, 9, 4, 9, 8, 9, 8, 9, 9, 9, 8, 9, 8, 8, 8, 9, 0, 1, 3, 2, 3, 8, 9, 8, 8, 9, 9, 9, 8, 9, 8, 9, 0, 1, -1, 5, 4, 5, 4, -1, 8, 9, 4, 4, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 3, 5, 4, 0, 0, 1, 9, 8, 9, 9, 8, 9, 8, 9, 7, 2, 3, 5, 4, 5, 4, 6, 5, 4, 7, 6, 6, 4, 8, 9, 8, 9, 0, 1, 8, 0, 1, 0, 1, 1, 0, 1, 0, 1, 2, 8, 9, 9, 8, 9, 9, 9, 8, 9, 9, 9, 6, 7, 5, 4, 5, 4, 4, 6, 5, 4, 7, 5, 4, 5, 4, 1, 0, 0, 1, 2, 3, 2, 3, 3, 2, 3, 2, 3, 0, 1, 8, 9, 9, 9, 8, 9, 9, 8, 9, 9, 8, 9, 5, 4, 7, 6, 6, 7, 5, 4, 5, 7, 6, 7, 6, 3, 2, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 4, 2, 3, 9, 8, 9, 8, 9, 9, 8, 9, 8, 9, 8, 9, 9, 9, 5, 4, 5, 4, 7, 6, 7, 6, 4, 5, 4, 5, 4, 5, 4, 2, 3, 2, 3, 2, 3, 2, 3, 5, 8, 9, 8, 9, 9, 9, 8, 9, 8, 9, 9, 8, 9, 8, 5, 5, 7, 6, 7, 6, 5, 4, 7, 7, 6, 7, 6, 5, 4, 5, 4, 5, 0, 1, 0, 1, 5, 4, 5, 7, 8, 9, 9, 8, 9, 8, 9, 8, 9, 8, 9, 5, 4, 8, 7, 7, 6, 6, 7, 6, 7, 6, 4, 7, 6, 5, 4, 5, 4, 7, 6, 7, 2, 3, 0, 1, 7, 6, 7, 6, 2, 3, 8, 9, 8, 9, 6, 7, 6, 0, 1, 5, 4, 5, 4, 4, 5, 4, 7, 5, 4, 5, 4, 5, 4, 5, 4, 7, 5, 4, 5, 4, 6, 0, 2, 3, 0, 1, 5, 4, 0, 1, 6, 7, 6, 0, 1, 0, 1, 2, 3, 7, 6, 7, 5, 4, 7, 6, 4, 7, 6, 7, 6, 7, 6, 7, 5, 4, 7, 6, 7, 6, 0, 2, 3, 1, 2, 3, 7, 6, 0, 0, 1, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 7, 6, 6, 5, 4, 5, 4, 5, 4, 5, 4, 5, 7, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 2, 0, 1, 2, 2, 3, 2, 3, 0, 1, 0, 1, 3, 2, 3, 2, 3, 3, 2, 3, 7, 6, 7, 6, 7, 6, 7, 6, 5, 4, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1]}