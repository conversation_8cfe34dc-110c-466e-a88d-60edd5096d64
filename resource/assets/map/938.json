{"mW": 888, "mH": 672, "tW": 24, "tH": 24, "tiles": [["315_4", 0, 3, 3], ["1314", 0, 3, 2], ["1314", 2, 3, 2], ["1314", 1, 3, 2], ["1314", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 3, 4, 2], ["709_3", 0, 2, 1]], "layers": [{"type": 4, "obj": [[2, "1381_1", 107, 11, 54, 88, 0], [2, "1381_3", 732, 25, 54, 88, 0], [2, "1381_2", 520, 511, 54, 88, 0]]}, {"type": 3, "obj": [[2, "1311_1", 385, 186, 44, 81, 0], [2, "1310_1", 412, 241, 18, 29, 2], [2, "1310_1", 294, 538, 18, 29, 2], [2, "1310_1", 756, 379, 18, 29, 2], [2, "1310_1", 855, 323, 18, 29, 2], [2, "1311_1", 799, 256, 44, 81, 0], [2, "1311_1", 817, 260, 44, 81, 2], [2, "1311_1", 845, 267, 44, 81, 2], [2, "1311_1", 783, 254, 44, 81, 0], [2, "1311_1", 702, 343, 44, 81, 0], [2, "1311_1", 671, 358, 44, 81, 0], [2, "1311_1", 748, 264, 44, 81, 0], [2, "1311_1", 636, 374, 44, 81, 0], [2, "1311_1", 612, 375, 44, 81, 0], [2, "1311_1", 576, -15, 44, 81, 2], [2, "1311_1", 10, 556, 44, 81, 0], [2, "1312_1", 91, 525, 36, 78, 0], [2, "1311_1", 60, 542, 44, 81, 0], [2, "1311_1", 354, 319, 44, 81, 0], [2, "1312_1", 323, 334, 36, 78, 2], [2, "1311_1", 297, 341, 44, 81, 0], [2, "1312_1", 271, 365, 36, 78, 0], [2, "1311_1", 207, 519, 44, 81, 0], [2, "1311_1", 134, 513, 44, 81, 2], [2, "212_1", 118, 517, 44, 99, 2], [2, "955_1", 155, 587, 20, 18, 2], [2, "212_1", 41, 546, 44, 99, 2], [2, "1311_1", 167, 526, 44, 81, 2], [2, "1306", 385, 1, 30, 29, 0], [2, "1302", 418, 5, 40, 29, 2], [2, "1303", 406, 27, 34, 20, 0], [2, "1302", 253, 59, 40, 29, 2], [2, "1303", 241, 81, 34, 20, 0], [2, "1303", 229, 48, 34, 20, 0], [2, "1305", 230, 98, 20, 14, 0], [2, "1311_1", 210, 505, 44, 81, 0], [2, "1301", 255, 11, 24, 49, 0], [2, "1303", 189, 521, 34, 20, 2], [2, "1311_1", 385, 186, 44, 81, 0], [2, "1312_1", 354, 201, 36, 78, 2], [2, "1303", 367, 277, 34, 20, 0], [2, "1302", 328, 224, 40, 29, 0], [2, "1309_1", 407, 179, 20, 32, 2], [2, "1311_1", 272, 485, 44, 81, 0], [2, "1311_1", 239, 493, 44, 81, 0], [2, "1310_1", 314, 468, 18, 29, 2], [2, "1310_1", 208, 519, 18, 29, 0], [2, "1311_1", -26, 639, 44, 81, 0], [2, "1310_1", 17, 621, 18, 29, 2], [2, "1311_1", 396, 452, 44, 81, 2], [2, "1310_1", 394, 447, 18, 29, 2], [2, "1311_1", 452, 266, 44, 81, 2], [2, "1310_1", 449, 259, 18, 29, 2], [2, "1311_1", 477, 292, 44, 81, 2], [2, "1302", 662, 571, 40, 29, 2], [2, "1303", 650, 593, 34, 20, 0], [2, "1303", 638, 560, 34, 20, 0], [2, "1305", 639, 610, 20, 14, 0], [2, "1301", 661, 523, 24, 49, 2], [2, "1306", 726, 238, 30, 29, 2], [2, "1327_1", 578, -19, 70, 44, 0], [2, "1324_3", 595, 4, 40, 43, 0], [2, "1324_3", 731, 277, 40, 43, 2], [2, "1327_1", 484, 282, 70, 44, 0], [2, "1324_3", 397, 402, 40, 43, 0], [2, "1324_3", 720, 395, 40, 43, 2], [2, "1327_1", 313, 202, 70, 44, 2], [2, "1322_3", 321, 233, 64, 38, 0], [2, "1324_3", 365, 266, 40, 43, 2], [2, "1327_1", 334, 301, 70, 44, 2], [2, "1327_1", 286, 328, 70, 44, 2], [2, "1327_1", 242, 356, 70, 44, 2], [2, "1322_3", 250, 388, 64, 38, 0], [2, "1324_3", 294, 423, 40, 43, 2], [2, "1327_1", 263, 458, 70, 44, 2], [2, "1327_1", 218, 482, 70, 44, 2], [2, "1327_1", 83, 502, 70, 44, 2], [2, "1322_3", 335, 543, 64, 38, 2], [2, "1324_3", 464, -3, 40, 43, 2], [2, "1327_1", 392, 438, 70, 44, 0], [2, "1306", 266, 411, 30, 29, 2], [2, "1327_1", 409, 503, 70, 44, 0], [2, "1325_2", 170, 505, 80, 36, 0], [2, "1327_1", 139, 498, 70, 44, 0], [2, "1327_1", 42, 522, 70, 44, 2], [2, "1325_2", -8, 545, 80, 36, 0], [2, "1324_3", -6, 577, 40, 43, 2], [2, "1324_3", -14, 395, 40, 43, 2], [2, "1327_1", -34, 611, 70, 44, 2], [2, "1324_3", 409, 466, 40, 43, 0], [2, "1324_3", 382, 506, 40, 43, 0], [2, "1323_3", 230, 613, 94, 24, 0], [2, "1322_3", 421, 373, 64, 38, 2], [2, "1324_3", 489, 317, 40, 43, 0], [2, "1327_1", 440, 355, 70, 44, 2], [2, "1322_3", 458, 211, 64, 38, 2], [2, "1327_1", 477, 193, 70, 44, 2], [2, "1324_3", 309, 579, 40, 43, 0], [2, "1322_3", 173, 620, 64, 38, 2], [2, "1322_3", 449, 249, 64, 38, 0], [2, "1302", 497, 343, 40, 29, 0], [2, "1322_3", 679, 97, 64, 38, 0], [2, "1325_2", 675, 51, 80, 36, 0], [2, "1326_2", 750, 56, 58, 21, 0], [2, "1327_1", 803, 53, 70, 44, 0], [2, "425_1", 810, 86, 30, 36, 0], [2, "1306", 667, 73, 30, 29, 2], [2, "426_1", 701, 74, 26, 22, 0], [2, "1323_3", 734, 119, 94, 24, 0], [2, "1324_3", 824, 82, 40, 43, 0], [2, "955_1", 699, 119, 20, 18, 0], [2, "955_1", 812, 121, 20, 18, 0], [2, "1303", 800, 134, 34, 20, 0], [2, "1305", 691, 119, 20, 14, 0], [2, "1305", 822, 121, 20, 14, 0], [2, "1322_3", 58, 83, 64, 38, 0], [2, "1325_2", 54, 37, 80, 36, 0], [2, "1326_2", 129, 42, 58, 21, 0], [2, "1327_1", 182, 39, 70, 44, 0], [2, "425_1", 189, 72, 30, 36, 0], [2, "1306", 46, 59, 30, 29, 2], [2, "426_1", 80, 60, 26, 22, 0], [2, "1323_3", 113, 105, 94, 24, 0], [2, "1324_3", 203, 68, 40, 43, 0], [2, "955_1", 78, 105, 20, 18, 0], [2, "955_1", 191, 107, 20, 18, 0], [2, "1303", 179, 120, 34, 20, 0], [2, "1305", 70, 105, 20, 14, 0], [2, "1305", 201, 107, 20, 14, 0], [2, "1322_3", 465, 586, 64, 38, 0], [2, "1325_2", 461, 540, 80, 36, 0], [2, "1326_2", 536, 545, 58, 21, 0], [2, "1327_1", 589, 542, 70, 44, 0], [2, "1306", 453, 562, 30, 29, 2], [2, "426_1", 487, 563, 26, 22, 0], [2, "1323_3", 520, 608, 94, 24, 0], [2, "1324_3", 610, 571, 40, 43, 0], [2, "955_1", 485, 608, 20, 18, 0], [2, "955_1", 598, 610, 20, 18, 0], [2, "1303", 586, 623, 34, 20, 0], [2, "1305", 477, 608, 20, 14, 0], [2, "1305", 608, 610, 20, 14, 0], [2, "1302", 401, 566, 40, 29, 0], [2, "1303", 418, 591, 34, 20, 2], [2, "1303", 420, 558, 34, 20, 2], [2, "1305", 446, 612, 20, 14, 2], [2, "1301", 434, 529, 24, 49, 0], [2, "1301", 843, 22, 24, 49, 2], [2, "1302", 25, 32, 40, 29, 0], [2, "955_1", 75, 28, 20, 18, 0], [2, "1305", 67, 28, 20, 14, 0], [2, "1325_2", 735, 249, 80, 36, 0], [2, "1323_3", 751, 424, 94, 24, 0], [2, "1322_3", 733, -2, 64, 38, 0], [2, "1306", 458, 228, 30, 29, 2], [2, "1325_2", 815, 254, 80, 36, 2], [2, "1327_1", 772, 422, 70, 44, 2], [2, "1303", 735, 435, 34, 20, 2], [2, "1302", 750, 425, 40, 29, 2], [2, "1305", 776, 446, 20, 14, 0], [2, "1305", 871, 258, 20, 14, 0], [2, "1305", 175, 657, 20, 14, 0], [2, "1305", 317, 614, 20, 14, 0], [2, "1325_2", -11, 365, 80, 36, 0], [2, "1327_1", 52, 360, 70, 44, 0], [2, "1324_3", 75, 388, 40, 43, 0], [2, "1323_3", 7, 420, 94, 24, 0], [2, "1305", 99, 253, 20, 14, 0], [2, "1303", 77, 266, 34, 20, 0], [2, "1305", 541, 357, 20, 14, 0], [2, "1303", 519, 370, 34, 20, 0], [2, "1306", 844, 442, 30, 29, 2], [2, "1306", 527, 312, 30, 29, 2], [2, "1303", 566, 229, 34, 20, 2], [2, "1303", 459, 428, 34, 20, 2], [2, "1306", 68, 236, 30, 29, 2], [2, "1303", 201, 394, 34, 20, 2], [2, "1303", 283, 223, 34, 20, 2], [2, "1303", 142, 213, 34, 20, 2], [2, "1303", 623, 344, 34, 20, 2], [2, "1303", 367, 629, 34, 20, 2], [2, "1303", 385, 542, 34, 20, 2], [2, "1324_3", 595, 401, 40, 43, 2], [2, "1325_2", 598, 371, 80, 36, 0], [2, "1324_3", 684, 394, 40, 43, 0], [2, "1323_3", 616, 426, 94, 24, 0], [2, "958_1", 665, 441, 90, 68, 0], [2, "1306", 611, 424, 30, 29, 2], [2, "1305", 638, 443, 20, 14, 0], [2, "958_1", 241, 227, 90, 68, 0], [2, "1302", 698, 420, 40, 29, 2], [2, "1302", 856, 465, 40, 29, 2], [2, "1302", 25, 237, 40, 29, 2], [2, "1303", 775, 640, 34, 20, 2], [2, "1303", 335, 284, 34, 20, 2], [2, "1305", 0, 150, 20, 14, 0], [2, "1311_1", 25, 269, 44, 81, 0], [2, "1311_1", 53, 266, 44, 81, 2], [2, "1311_1", 1, 270, 44, 81, 0], [2, "1324_3", -16, 296, 40, 43, 2], [2, "1325_2", -13, 266, 80, 36, 0], [2, "1327_1", 50, 261, 70, 44, 0], [2, "1324_3", 73, 289, 40, 43, 0], [2, "1323_3", 5, 321, 94, 24, 0], [2, "958_1", 58, 322, 90, 68, 0], [2, "1306", 0, 319, 30, 29, 2], [2, "1305", 27, 338, 20, 14, 0], [2, "1302", 87, 315, 40, 29, 2], [2, "1311_1", 735, 326, 44, 81, 0], [2, "1327_1", 706, 311, 70, 44, 2], [2, "425_1", 597, 570, 30, 36, 0], [2, "425_1", 39, 385, 30, 36, 0], [2, "1325_2", 681, 333, 80, 36, 0], [2, "1323_3", 833, 416, 94, 24, 0], [2, "1303", 862, 429, 34, 20, 2], [2, "955_1", 835, 423, 20, 18, 2], [2, "1303", 751, 490, 34, 20, 2], [2, "955_1", 842, 235, 20, 18, 2], [2, "1303", 786, 233, 34, 20, 0], [2, "958_1", 796, 145, 90, 68, 0], [2, "1311_1", 483, 151, 44, 81, 2], [2, "955_1", 413, 171, 20, 18, 2], [2, "1310_1", 482, 42, 18, 29, 2], [2, "1309_1", 407, 179, 20, 32, 2], [2, "1324_3", 388, 144, 40, 43, 2], [2, "1327_1", 357, 171, 70, 44, 2], [2, "1327_1", 429, 30, 70, 44, 2], [2, "1303", 375, 173, 34, 20, 2], [2, "1324_3", 541, 45, 40, 43, 0], [2, "1327_1", 556, 25, 70, 44, 2], [2, "1303", 554, 66, 34, 20, 0], [2, "1305", 555, 116, 20, 14, 0], [2, "1311_1", 420, 146, 44, 81, 2], [2, "1312_1", 452, 149, 36, 78, 0], [2, "1312_1", 464, 150, 36, 78, 2], [2, "1311_1", 400, 143, 44, 81, 0], [2, "1303", 472, 40, 34, 20, 0], [2, "1324_3", 502, 165, 40, 43, 0], [2, "1324_3", 470, 29, 40, 43, 2], [2, "1303", 440, 47, 34, 20, 2], [2, "1327_1", 479, 151, 70, 44, 0], [2, "1325_2", 410, 134, 80, 36, 2], [2, "1327_1", 498, 49, 70, 44, 0], [2, "1306", 477, 53, 30, 29, 2], [2, "1301", 400, 114, 24, 49, 0], [2, "1306", 383, 150, 30, 29, 2], [2, "1324_3", 481, 195, 40, 43, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, 45, 56, 55, 54, 33, 34, 40, 39, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, 53, 52, 51, -1, -1, -1, -1, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 51, 36, 49, 43, 42, -1, -1, 40, 40, 39, -1, 53, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 52, 51, -1, -1, -1, 33, 34, 40, 39, -1, -1, 33, 34, 44, 49, 49, 41, 40, 39, 46, 52, 51, -1, -1, -1, -1, 33, 34, 40, 39, -1, -1, -1, -1, -1, 52, -1, 33, 34, 35, -1, -1, 44, 52, 52, 56, 56, 40, -1, 55, 55, 37, 49, 39, 52, 51, 41, 40, 16, 15, 17, 16, 15, 53, 56, 43, 54, -1, -1, -1, 41, 40, 40, 39, 53, 52, 51, -1, -1, -1, -1, -1, 45, 46, 56, 55, 55, 56, 55, 50, 42, 52, 51, 9, 20, 13, 14, 20, 13, 14, 15, 48, 43, 54, -1, 33, 34, 44, 43, 43, 42, 35, -1, -1, -1, 17, 16, 10, 10, 11, -1, 48, 49, 55, 53, 52, 51, -1, -1, -1, 12, 25, 25, 13, 13, 13, 13, 30, 48, 55, 54, -1, -1, -1, -1, 49, 52, 51, -1, -1, -1, 9, 20, 19, 13, 13, 14, 15, 45, 46, 52, -1, -1, -1, -1, -1, -1, 21, 32, 13, 13, 25, 13, 26, 27, 53, 52, 51, -1, -1, -1, 56, 43, 39, -1, -1, -1, -1, 21, 32, 31, 31, 13, 31, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 32, 13, 13, 30, -1, -1, 45, -1, -1, -1, 52, 43, 43, 42, -1, -1, -1, -1, -1, 29, 28, 32, 31, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, -1, 21, 22, 28, 27, -1, -1, -1, -1, -1, -1, -1, 43, 52, 47, -1, -1, -1, -1, -1, -1, -1, 29, 28, 27, -1, -1, -1, -1, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 33, 34, 35, 37, 38, 40, 39, -1, -1, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 44, 50, 46, 37, 50, 52, 51, -1, -1, -1, -1, -1, 33, 34, 40, 39, -1, -1, -1, -1, -1, -1, 46, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 50, 52, 51, 33, 52, 51, -1, -1, -1, 9, 10, 11, -1, 45, 46, 52, 52, 51, -1, -1, 33, 34, 49, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 54, -1, 45, 46, 40, 39, -1, -1, -1, 12, 13, 14, 15, -1, -1, -1, -1, -1, -1, -1, 36, 37, 49, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 33, 34, 34, 55, 38, 41, 40, 39, 21, 32, 25, 18, -1, -1, -1, -1, -1, 39, -1, -1, -1, 34, 43, -1, -1, -1, -1, -1, 33, 34, 40, 39, -1, -1, -1, -1, -1, 44, 55, 52, 50, 46, 46, 52, -1, -1, 29, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, -1, -1, -1, -1, -1, -1, 45, 56, 55, 54, -1, -1, -1, -1, 45, 46, 47, -1, 38, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 33, 34, -1, -1, -1, -1, 9, 10, 16, 15, -1, 53, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, -1, 41, 40, 39, -1, -1, -1, 47, -1, -1, -1, 45, 46, 56, 49, -1, -1, -1, -1, 24, 25, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 44, 43, 42, -1, -1, -1, 17, 16, 15, -1, -1, -1, -1, 46, 43, -1, -1, -1, 21, 22, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 56, 50, 46, 47, -1, 9, 10, 20, 25, 30, -1, 41, -1, -1, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 47, -1, -1, -1, 29, 28, 25, 25, 30, -1, -1, 33, 44, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, 32, 25, 25, 26, 27, -1, -1, 45, 46, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, 12, 13, 19, 14, 15, -1, -1, -1, -1, -1, 54, -1, 24, 25, -1, -1, -1, -1, -1, 55, -1, -1, 33, 34, 35, 33, 52, 51, -1, 45, 46, 45, 46, 47, 33, 40, 39, 29, 32, 31, 19, 18, -1, -1, -1, 55, 54, 51, -1, 21, -1, -1, 43, 43, 43, 43, -1, -1, -1, 45, 46, 47, 34, 34, 40, 40, 39, -1, -1, 33, 34, 40, 52, 51, -1, 29, 28, 28, 27, -1, -1, -1, 52, 43, 43, 43, 43, 43, 43, 43, 49, 43, -1, -1, -1, -1, -1, -1, 36, 56, 37, 55, 43, 43, 42, -1, 45, 46, 52, 51, -1, -1, -1, 9, 10, 11, -1, -1, -1, 41, 40, 43, 43, 43, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 46, 56, 55, 54, -1, -1, 41, 40, 40, 39, -1, -1, -1, 12, 13, 14, -1, -1, 38, 44, 43, 38, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 51, 33, 34, 44, 50, 46, 47, -1, -1, -1, 21, 32, 31]}, {"type": 2, "data": [0, 1, 0, 1, 2, 3, 4, 5, 1, 0, 1, 2, 6, 7, 8, 2, 0, 1, 2, 1, 62, 62, 62, 62, 62, 62, 0, 1, 2, 2, 1, 2, 1, 2, 0, 1, 2, 3, 0, 1, 2, 2, 6, 7, 8, 4, 3, 4, 5, 3, 3, 4, 5, 3, 4, 5, 4, 62, 62, 62, 62, 62, 62, 3, 4, 5, 5, 4, 5, 4, 0, 1, 2, 2, 6, 3, 4, 5, 5, 3, 0, 1, 2, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 7, 62, 62, 62, 62, 0, 1, 6, 7, 8, 8, 7, 8, 7, 3, 4, 5, 5, 0, 6, 7, 8, 8, 0, 0, 1, 2, 3, 4, 5, 1, 2, 1, 2, 3, 0, 1, 1, 1, 2, 2, 0, 1, 2, 5, 0, 1, 0, 1, 2, 0, 1, 2, 0, 1, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 4, 5, 6, 3, 4, 4, 4, 5, 1, 2, 4, 5, 8, 3, 4, 3, 4, 5, 3, 4, 0, 1, 2, 6, 7, 0, 1, 2, 5, 6, 0, 1, 0, 1, 2, 4, 5, 0, 1, 1, 2, 1, 2, 4, 5, 4, 5, 7, 8, 1, 2, 7, 6, 7, 8, 6, 7, 3, 4, 5, 0, 1, 3, 4, 5, 8, 0, 3, 4, 3, 4, 5, 7, 8, 3, 4, 4, 5, 4, 5, 4, 5, 4, 5, 7, 8, 2, 5, 6, 6, 7, 8, 3, 4, 6, 7, 8, 0, 1, 2, 7, 0, 0, 1, 6, 7, 6, 7, 8, 4, 5, 6, 7, 8, 62, 62, 62, 62, 62, 3, 4, 5, 4, 5, 8, 1, 2, 4, 0, 6, 7, 8, 5, 3, 3, 4, 5, 1, 3, 3, 4, 3, 4, 5, 0, 1, 2, 8, 7, 8, 3, 62, 62, 62, 62, 62, 6, 7, 8, 0, 1, 2, 4, 5, 7, 3, 6, 7, 8, 0, 1, 6, 7, 8, 4, 6, 6, 7, 6, 7, 8, 3, 4, 5, 4, 5, 3, 62, 62, 62, 62, 62, 0, 1, 2, 1, 3, 4, 5, 1, 2, 0, 6, 7, 8, 1, 3, 4, 0, 1, 6, 7, 8, 8, 0, 0, 1, 2, 6, 7, 0, 1, 2, 6, 62, 62, 62, 7, 0, 3, 4, 5, 4, 6, 7, 8, 0, 1, 2, 4, 5, 3, 4, 6, 7, 3, 4, 0, 1, 2, 3, 3, 3, 4, 5, 8, 6, 3, 4, 5, 2, 62, 62, 62, 62, 3, 6, 7, 8, 0, 1, 2, 8, 3, 4, 5, 65, 65, 66, 65, 66, 65, 6, 7, 3, 4, 5, 6, 6, 6, 7, 8, 5, 0, 6, 7, 8, 5, 62, 62, 62, 62, 6, 7, 8, 2, 3, 4, 5, 1, 2, 7, 8, 65, 66, 65, 66, 65, 66, 3, 4, 6, 7, 8, 7, 8, 1, 2, 8, 8, 3, 0, 6, 7, 8, 62, 62, 62, 62, 62, 3, 4, 5, 6, 7, 8, 4, 5, 3, 4, 5, 62, 62, 62, 62, 62, 6, 7, 8, 0, 1, 0, 3, 4, 5, 4, 0, 1, 3, 4, 0, 1, 62, 62, 62, 62, 0, 6, 7, 8, 0, 3, 6, 7, 8, 6, 7, 8, 62, 62, 62, 62, 62, 0, 1, 0, 0, 1, 3, 6, 7, 8, 2, 3, 4, 6, 7, 3, 62, 62, 62, 62, 62, 3, 4, 5, 2, 3, 6, 6, 7, 8, 2, 7, 8, 62, 62, 62, 62, 62, 65, 66, 65, 66, 1, 2, 7, 3, 4, 5, 6, 7, 8, 62, 62, 62, 62, 62, 62, 0, 6, 7, 8, 5, 6, 7, 8, 3, 4, 5, 3, 62, 62, 62, 62, 62, 62, 66, 65, 66, 65, 4, 5, 6, 6, 7, 8, 0, 1, 6, 62, 62, 62, 62, 62, 0, 3, 4, 6, 7, 8, 2, 7, 0, 6, 7, 0, 1, 62, 62, 62, 62, 62, 62, 0, 1, 2, 6, 7, 8, 0, 1, 2, 2, 3, 4, 5, 62, 62, 62, 62, 0, 3, 6, 7, 8, 3, 4, 5, 2, 3, 4, 5, 3, 4, 5, 3, 4, 6, 7, 8, 3, 4, 5, 3, 3, 3, 3, 4, 5, 5, 6, 7, 8, 62, 62, 62, 62, 3, 6, 7, 8, 8, 6, 7, 8, 0, 1, 2, 8, 6, 7, 8, 6, 7, 8, 3, 4, 6, 7, 8, 0, 1, 6, 6, 7, 8, 8, 0, 1, 62, 62, 62, 62, 62, 6, 7, 8, 2, 8, 4, 5, 4, 3, 4, 5, 0, 1, 2, 8, 3, 4, 5, 6, 7, 0, 0, 1, 3, 4, 5, 0, 3, 4, 5, 3, 4, 62, 62, 62, 62, 62, 62, 3, 4, 5, 6, 7, 8, 5, 6, 7, 8, 3, 4, 5, 8, 6, 7, 8, 3, 4, 3, 3, 4, 6, 7, 8, 3, 0, 1, 0, 1, 62, 62, 62, 62, 62, 62, 0, 1, 2, 8, 4, 3, 4, 5, 6, 7, 8, 6, 7, 8, 5, 0, 1, 2, 0, 1, 6, 6, 7, 0, 0, 1, 2, 0, 1, 3, 62, 62, 62, 62, 62, 0, 1, 3, 4, 5, 8, 0, 6, 7, 8, 4, 5, 3, 0, 1, 2, 8, 3, 4, 5, 0, 1, 0, 0, 1, 3, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 0, 3, 4, 6, 7, 8, 0, 3, 4, 5, 6, 7, 8, 6, 3, 4, 5, 2, 6, 7, 8, 0, 1, 3, 3, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 3, 6, 7, 8, 7, 8, 0, 6, 7, 8, 2, 3, 4, 5, 6, 0, 1, 2, 2, 0, 1, 3, 4, 62, 62, 62, 62, 62, 62, 62, 62, 62, 0, 1, 0, 1, 2, 6, 7, 8, 5, 5, 3, 3, 4, 3, 4, 5, 6, 7, 8, 4, 3, 4, 5, 5, 3, 4, 6, 7, 62, 62, 62, 62, 62, 62, 62, 62, 0, 3, 4, 3, 4, 5, 4, 5, 7, 8, 8, 6, 6, 7, 6, 7, 8, 7, 8, 6, 7, 6, 7, 8, 8, 6, 7, 8, 0]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}