{"mW": 984, "mH": 720, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1233", 0, 3, 2], ["315", 0, 3, 3], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["335", 0, 1, 1], ["335", 2, 1, 1], ["696", 0, 2, 2]], "layers": [{"type": 3, "obj": [[2, "3978", -31, 171, 50, 26, 0], [2, "3984", -24, 168, 24, 24, 0], [2, "3984", -48, 168, 24, 24, 0], [2, "3984", -72, 168, 24, 24, 0], [2, "3984", -72, 144, 24, 24, 0], [2, "3984", -48, 144, 24, 24, 0], [2, "3984", -24, 144, 24, 24, 0], [2, "3988", 335, -12, 148, 179, 0], [2, "3988", 699, 474, 148, 179, 0], [2, "3988", 195, 219, 148, 179, 0], [2, "3988", 391, 304, 148, 179, 0], [2, "3746", 700, 352, 38, 54, 0], [2, "3410", 720, 357, 54, 74, 2], [2, "1501", 936, 520, 50, 26, 0], [2, "1501", 911, 533, 50, 26, 0], [2, "1501", 785, 600, 50, 26, 0], [2, "1501", 811, 587, 50, 26, 0], [2, "1501", 760, 613, 50, 26, 0], [2, "1501", 808, 589, 50, 26, 0], [2, "1501", 834, 576, 50, 26, 0], [2, "1501", 783, 602, 50, 26, 0], [2, "1501", 815, 605, 50, 26, 0], [2, "1501", 841, 592, 50, 26, 0], [2, "1501", 790, 618, 50, 26, 0], [2, "1501", 829, 615, 50, 26, 0], [2, "1501", 855, 602, 50, 26, 0], [2, "1501", 804, 628, 50, 26, 0], [2, "1501", 734, 626, 50, 26, 0], [2, "1501", 760, 613, 50, 26, 0], [2, "1501", 709, 639, 50, 26, 0], [2, "1501", 657, 665, 50, 26, 0], [2, "1501", 683, 652, 50, 26, 0], [2, "1501", 632, 678, 50, 26, 0], [2, "1501", 580, 704, 50, 26, 0], [2, "1501", 606, 691, 50, 26, 0], [2, "1501", 555, 717, 50, 26, 0], [2, "1501", 676, 681, 50, 26, 0], [2, "1501", 702, 668, 50, 26, 0], [2, "1501", 651, 694, 50, 26, 0], [2, "1501", 810, 638, 50, 26, 0], [2, "1501", 836, 625, 50, 26, 0], [2, "1501", 785, 651, 50, 26, 0], [2, "1501", 708, 666, 50, 26, 0], [2, "1501", 734, 653, 50, 26, 0], [2, "1501", 683, 679, 50, 26, 0], [2, "1501", 753, 642, 50, 26, 0], [2, "1501", 779, 629, 50, 26, 0], [2, "1501", 728, 655, 50, 26, 0], [2, "1501", 861, 561, 50, 26, 0], [2, "1501", 887, 548, 50, 26, 0], [2, "1501", 836, 574, 50, 26, 0], [2, "1501", 895, 566, 50, 26, 0], [2, "1501", 921, 553, 50, 26, 0], [2, "1501", 870, 579, 50, 26, 0], [2, "1501", 906, 581, 50, 26, 0], [2, "1501", 932, 568, 50, 26, 0], [2, "1501", 881, 594, 50, 26, 0], [2, "1501", 931, 512, 50, 26, 0], [2, "1501", 957, 525, 50, 26, 0], [2, "1501", 983, 538, 50, 26, 0], [2, "1501", 1009, 551, 50, 26, 0], [2, "1501", 885, 550, 50, 26, 0], [2, "1501", 911, 537, 50, 26, 0], [2, "1501", 860, 563, 50, 26, 0], [2, "1501", 920, 532, 50, 26, 0], [2, "1501", 946, 519, 50, 26, 0], [2, "1501", 895, 545, 50, 26, 0], [2, "1501", 904, 538, 50, 26, 0], [2, "1501", 930, 525, 50, 26, 0], [2, "1501", 879, 551, 50, 26, 0], [2, "1501", 908, 551, 50, 26, 0], [2, "1501", 934, 538, 50, 26, 0], [2, "1501", 883, 564, 50, 26, 0], [2, "1501", 293, 194, 50, 26, 0], [2, "1501", 318, 206, 50, 26, 0], [2, "1501", 344, 219, 50, 26, 0], [2, "1501", 370, 232, 50, 26, 0], [2, "1501", 908, 573, 50, 26, 0], [2, "1501", 934, 560, 50, 26, 0], [2, "1501", 960, 547, 50, 26, 0], [2, "1501", 986, 560, 50, 26, 0], [2, "1501", 960, 573, 50, 26, 0], [2, "1501", 934, 586, 50, 26, 0], [2, "1501", 960, 599, 50, 26, 0], [2, "1501", 986, 586, 50, 26, 0], [2, "1501", 1012, 573, 50, 26, 0], [2, "1501", 899, 582, 50, 26, 0], [2, "1501", 925, 569, 50, 26, 0], [2, "1501", 951, 556, 50, 26, 0], [2, "1501", 977, 569, 50, 26, 0], [2, "1501", 951, 582, 50, 26, 0], [2, "1501", 925, 595, 50, 26, 0], [2, "1501", 951, 608, 50, 26, 0], [2, "1501", 977, 595, 50, 26, 0], [2, "1501", 1003, 582, 50, 26, 0], [2, "1501", 825, 618, 50, 26, 0], [2, "1501", 851, 605, 50, 26, 0], [2, "1501", 877, 592, 50, 26, 0], [2, "1501", 903, 605, 50, 26, 0], [2, "1501", 877, 618, 50, 26, 0], [2, "1501", 851, 631, 50, 26, 0], [2, "1501", 877, 644, 50, 26, 0], [2, "1501", 903, 631, 50, 26, 0], [2, "1501", 929, 618, 50, 26, 0], [2, "1501", 718, 700, 50, 26, 0], [2, "1501", 744, 687, 50, 26, 0], [2, "1501", 770, 674, 50, 26, 0], [2, "1501", 796, 687, 50, 26, 0], [2, "1501", 770, 700, 50, 26, 0], [2, "1501", 744, 713, 50, 26, 0], [2, "1501", 770, 726, 50, 26, 0], [2, "1501", 796, 713, 50, 26, 0], [2, "1501", 822, 700, 50, 26, 0], [2, "1501", 792, 664, 50, 26, 0], [2, "1501", 818, 651, 50, 26, 0], [2, "1501", 844, 638, 50, 26, 0], [2, "1501", 870, 651, 50, 26, 0], [2, "1501", 844, 664, 50, 26, 0], [2, "1501", 818, 677, 50, 26, 0], [2, "1501", 844, 690, 50, 26, 0], [2, "1501", 870, 677, 50, 26, 0], [2, "1501", 896, 664, 50, 26, 0], [2, "1501", 863, 623, 50, 26, 0], [2, "1501", 889, 610, 50, 26, 0], [2, "1501", 915, 597, 50, 26, 0], [2, "1501", 941, 610, 50, 26, 0], [2, "1501", 915, 623, 50, 26, 0], [2, "1501", 889, 636, 50, 26, 0], [2, "1501", 915, 649, 50, 26, 0], [2, "1501", 941, 636, 50, 26, 0], [2, "1501", 967, 623, 50, 26, 0], [2, "1501", 825, 669, 50, 26, 0], [2, "1501", 851, 656, 50, 26, 0], [2, "1501", 877, 643, 50, 26, 0], [2, "1501", 903, 656, 50, 26, 0], [2, "1501", 877, 669, 50, 26, 0], [2, "1501", 851, 682, 50, 26, 0], [2, "1501", 877, 695, 50, 26, 0], [2, "1501", 903, 682, 50, 26, 0], [2, "1501", 929, 669, 50, 26, 0], [2, "1501", 899, 633, 50, 26, 0], [2, "1501", 925, 620, 50, 26, 0], [2, "1501", 951, 607, 50, 26, 0], [2, "1501", 977, 620, 50, 26, 0], [2, "1501", 951, 633, 50, 26, 0], [2, "1501", 925, 646, 50, 26, 0], [2, "1501", 951, 659, 50, 26, 0], [2, "1501", 977, 646, 50, 26, 0], [2, "1501", 1003, 633, 50, 26, 0], [2, "1501", 970, 592, 50, 26, 0], [2, "1501", 996, 579, 50, 26, 0], [2, "1501", 1022, 566, 50, 26, 0], [2, "1501", 1048, 579, 50, 26, 0], [2, "1501", 1022, 592, 50, 26, 0], [2, "1501", 996, 605, 50, 26, 0], [2, "1501", 1022, 618, 50, 26, 0], [2, "1501", 1048, 605, 50, 26, 0], [2, "1501", 1074, 592, 50, 26, 0], [2, "1501", 899, 633, 50, 26, 0], [2, "1501", 925, 620, 50, 26, 0], [2, "1501", 951, 607, 50, 26, 0], [2, "1501", 977, 620, 50, 26, 0], [2, "1501", 951, 633, 50, 26, 0], [2, "1501", 925, 646, 50, 26, 0], [2, "1501", 951, 659, 50, 26, 0], [2, "1501", 977, 646, 50, 26, 0], [2, "1501", 1003, 633, 50, 26, 0], [2, "1501", 573, 729, 50, 26, 0], [2, "1501", 599, 716, 50, 26, 0], [2, "1501", 625, 703, 50, 26, 0], [2, "1501", 651, 716, 50, 26, 0], [2, "1501", 625, 729, 50, 26, 0], [2, "1501", 599, 742, 50, 26, 0], [2, "1501", 625, 755, 50, 26, 0], [2, "1501", 651, 742, 50, 26, 0], [2, "1501", 677, 729, 50, 26, 0], [2, "1501", 644, 718, 50, 26, 0], [2, "1501", 670, 705, 50, 26, 0], [2, "1501", 696, 692, 50, 26, 0], [2, "1501", 722, 705, 50, 26, 0], [2, "1501", 696, 718, 50, 26, 0], [2, "1501", 670, 731, 50, 26, 0], [2, "1501", 696, 744, 50, 26, 0], [2, "1501", 722, 731, 50, 26, 0], [2, "1501", 748, 718, 50, 26, 0], [2, "1501", 796, 715, 50, 26, 0], [2, "1501", 822, 702, 50, 26, 0], [2, "1501", 848, 689, 50, 26, 0], [2, "1501", 874, 702, 50, 26, 0], [2, "1501", 848, 715, 50, 26, 0], [2, "1501", 822, 728, 50, 26, 0], [2, "1501", 554, 717, 50, 26, 0], [2, "1501", -116, 181, 50, 26, 0], [2, "1501", -90, 168, 50, 26, 0], [2, "1501", -64, 155, 50, 26, 0], [2, "1501", -38, 168, 50, 26, 0], [2, "1501", -64, 181, 50, 26, 0], [2, "1501", -90, 194, 50, 26, 0], [2, "1501", -64, 207, 50, 26, 0], [2, "1501", -38, 194, 50, 26, 0], [2, "1501", -12, 181, 50, 26, 0], [2, "1501", -46, 216, 50, 26, 0], [2, "1501", -20, 203, 50, 26, 0], [2, "1501", 6, 190, 50, 26, 0], [2, "1501", 32, 203, 50, 26, 0], [2, "1501", 6, 216, 50, 26, 0], [2, "1501", -20, 229, 50, 26, 0], [2, "1501", 6, 242, 50, 26, 0], [2, "1501", 32, 229, 50, 26, 0], [2, "1501", 58, 216, 50, 26, 0], [2, "1501", -89, 229, 50, 26, 0], [2, "1501", -63, 216, 50, 26, 0], [2, "1501", -37, 203, 50, 26, 0], [2, "1501", -11, 216, 50, 26, 0], [2, "1501", -37, 229, 50, 26, 0], [2, "1501", -63, 242, 50, 26, 0], [2, "1501", -37, 255, 50, 26, 0], [2, "1501", -11, 242, 50, 26, 0], [2, "1501", 15, 229, 50, 26, 0], [2, "1501", 702, 681, 50, 26, 0], [2, "1501", 728, 668, 50, 26, 0], [2, "1501", 754, 655, 50, 26, 0], [2, "1501", 780, 668, 50, 26, 0], [2, "1501", 754, 681, 50, 26, 0], [2, "1501", 728, 694, 50, 26, 0], [2, "1501", 754, 707, 50, 26, 0], [2, "1501", 780, 694, 50, 26, 0], [2, "1501", 806, 681, 50, 26, 0], [2, "1501", 825, 600, 50, 26, 0], [2, "1501", 851, 587, 50, 26, 0], [2, "1501", 877, 574, 50, 26, 0], [2, "1501", 903, 587, 50, 26, 0], [2, "1501", 877, 600, 50, 26, 0], [2, "1501", 851, 613, 50, 26, 0], [2, "1501", 877, 626, 50, 26, 0], [2, "1501", 903, 613, 50, 26, 0], [2, "1501", 929, 600, 50, 26, 0], [2, "1228", 968, 250, 60, 75, 2], [2, "1228", 968, 250, 60, 75, 2], [2, "1228", 948, 260, 60, 75, 2], [2, "1228", 929, 269, 60, 75, 2], [2, "1228", 906, 278, 60, 75, 2], [2, "1228", 883, 289, 60, 75, 2], [2, "1228", 860, 299, 60, 75, 2], [2, "1228", 837, 310, 60, 75, 2], [2, "1228", 813, 322, 60, 75, 2], [2, "1228", 790, 332, 60, 75, 2], [2, "1228", 769, 343, 60, 75, 2], [2, "1228", 747, 357, 60, 75, 2], [2, "1225_1", 960, 221, 48, 44, 2], [2, "1225_1", 920, 242, 48, 44, 2], [2, "1225_1", 879, 265, 48, 44, 2], [2, "1225_1", 840, 284, 48, 44, 2], [2, "1226_1", 779, 307, 70, 47, 2], [2, "1226_1", 729, 325, 70, 47, 2], [2, "1199", 933, 229, 38, 73, 2], [2, "894", 448, 61, 24, 20, 2], [2, "894", 429, 71, 24, 20, 2], [2, "894", 405, 83, 24, 20, 2], [2, "3984", -24, 696, 24, 24, 0], [2, "3984", -48, 696, 24, 24, 0], [2, "3984", -72, 696, 24, 24, 0], [2, "3984", -72, 672, 24, 24, 0], [2, "3984", -48, 672, 24, 24, 0], [2, "3984", -24, 672, 24, 24, 0], [2, "3984", -24, 648, 24, 24, 0], [2, "3984", -48, 648, 24, 24, 0], [2, "3984", -72, 648, 24, 24, 0], [2, "3984", -72, 624, 24, 24, 0], [2, "3984", -48, 624, 24, 24, 0], [2, "3984", -24, 624, 24, 24, 0], [2, "3984", -24, 552, 24, 24, 0], [2, "3984", -48, 552, 24, 24, 0], [2, "3984", -72, 552, 24, 24, 0], [2, "3984", -72, 528, 24, 24, 0], [2, "3984", -48, 528, 24, 24, 0], [2, "3984", -24, 528, 24, 24, 0], [2, "3984", -24, 600, 24, 24, 0], [2, "3984", -48, 600, 24, 24, 0], [2, "3984", -72, 600, 24, 24, 0], [2, "3984", -72, 576, 24, 24, 0], [2, "3984", -48, 576, 24, 24, 0], [2, "3984", -24, 576, 24, 24, 0], [2, "3984", -24, 456, 24, 24, 0], [2, "3984", -48, 456, 24, 24, 0], [2, "3984", -72, 456, 24, 24, 0], [2, "3984", -72, 432, 24, 24, 0], [2, "3984", -48, 432, 24, 24, 0], [2, "3984", -24, 432, 24, 24, 0], [2, "3984", -24, 504, 24, 24, 0], [2, "3984", -48, 504, 24, 24, 0], [2, "3984", -72, 504, 24, 24, 0], [2, "3984", -72, 480, 24, 24, 0], [2, "3984", -48, 480, 24, 24, 0], [2, "3984", -24, 480, 24, 24, 0], [2, "3984", -24, 360, 24, 24, 0], [2, "3984", -48, 360, 24, 24, 0], [2, "3984", -72, 360, 24, 24, 0], [2, "3984", -72, 336, 24, 24, 0], [2, "3984", -48, 336, 24, 24, 0], [2, "3984", -24, 336, 24, 24, 0], [2, "3984", -24, 408, 24, 24, 0], [2, "3984", -48, 408, 24, 24, 0], [2, "3984", -72, 408, 24, 24, 0], [2, "3984", -72, 384, 24, 24, 0], [2, "3984", -48, 384, 24, 24, 0], [2, "3984", -24, 384, 24, 24, 0], [2, "3984", -24, 264, 24, 24, 0], [2, "3984", -48, 264, 24, 24, 0], [2, "3984", -72, 264, 24, 24, 0], [2, "3984", -72, 240, 24, 24, 0], [2, "3984", -48, 240, 24, 24, 0], [2, "3984", -24, 240, 24, 24, 0], [2, "3984", -24, 312, 24, 24, 0], [2, "3984", -48, 312, 24, 24, 0], [2, "3984", -72, 312, 24, 24, 0], [2, "3984", -72, 288, 24, 24, 0], [2, "3984", -48, 288, 24, 24, 0], [2, "3984", -24, 288, 24, 24, 0], [2, "3984", -24, 72, 24, 24, 0], [2, "3984", -48, 72, 24, 24, 0], [2, "3984", -72, 72, 24, 24, 0], [2, "3984", -72, 48, 24, 24, 0], [2, "3984", -48, 48, 24, 24, 0], [2, "3984", -24, 48, 24, 24, 0], [2, "3984", -24, 120, 24, 24, 0], [2, "3984", -48, 120, 24, 24, 0], [2, "3984", -72, 120, 24, 24, 0], [2, "3984", -72, 96, 24, 24, 0], [2, "3984", -48, 96, 24, 24, 0], [2, "3984", -24, 96, 24, 24, 0], [2, "3984", -24, 744, 24, 24, 0], [2, "3984", -48, 744, 24, 24, 0], [2, "3984", -72, 744, 24, 24, 0], [2, "3984", -72, 720, 24, 24, 0], [2, "3984", -48, 720, 24, 24, 0], [2, "3984", -24, 720, 24, 24, 0], [2, "3984", 48, 744, 24, 24, 0], [2, "3984", 24, 744, 24, 24, 0], [2, "3984", 0, 744, 24, 24, 0], [2, "3984", 0, 720, 24, 24, 0], [2, "3984", 24, 720, 24, 24, 0], [2, "3984", 48, 720, 24, 24, 0], [2, "3984", 192, 744, 24, 24, 0], [2, "3984", 168, 744, 24, 24, 0], [2, "3984", 144, 744, 24, 24, 0], [2, "3984", 144, 720, 24, 24, 0], [2, "3984", 168, 720, 24, 24, 0], [2, "3984", 192, 720, 24, 24, 0], [2, "3984", 120, 744, 24, 24, 0], [2, "3984", 96, 744, 24, 24, 0], [2, "3984", 72, 744, 24, 24, 0], [2, "3984", 72, 720, 24, 24, 0], [2, "3984", 96, 720, 24, 24, 0], [2, "3984", 120, 720, 24, 24, 0], [2, "3984", 480, 744, 24, 24, 0], [2, "3984", 456, 744, 24, 24, 0], [2, "3984", 432, 744, 24, 24, 0], [2, "3984", 432, 720, 24, 24, 0], [2, "3984", 456, 720, 24, 24, 0], [2, "3984", 480, 720, 24, 24, 0], [2, "3984", 408, 744, 24, 24, 0], [2, "3984", 384, 744, 24, 24, 0], [2, "3984", 360, 744, 24, 24, 0], [2, "3984", 360, 720, 24, 24, 0], [2, "3984", 384, 720, 24, 24, 0], [2, "3984", 408, 720, 24, 24, 0], [2, "3984", 336, 744, 24, 24, 0], [2, "3984", 312, 744, 24, 24, 0], [2, "3984", 288, 744, 24, 24, 0], [2, "3984", 288, 720, 24, 24, 0], [2, "3984", 312, 720, 24, 24, 0], [2, "3984", 336, 720, 24, 24, 0], [2, "3984", 264, 744, 24, 24, 0], [2, "3984", 240, 744, 24, 24, 0], [2, "3984", 216, 744, 24, 24, 0], [2, "3984", 216, 720, 24, 24, 0], [2, "3984", 240, 720, 24, 24, 0], [2, "3984", 264, 720, 24, 24, 0], [2, "3984", 768, 744, 24, 24, 0], [2, "3984", 744, 744, 24, 24, 0], [2, "3984", 720, 744, 24, 24, 0], [2, "3984", 720, 720, 24, 24, 0], [2, "3984", 744, 720, 24, 24, 0], [2, "3984", 768, 720, 24, 24, 0], [2, "3984", 696, 744, 24, 24, 0], [2, "3984", 672, 744, 24, 24, 0], [2, "3984", 648, 744, 24, 24, 0], [2, "3984", 648, 720, 24, 24, 0], [2, "3984", 672, 720, 24, 24, 0], [2, "3984", 696, 720, 24, 24, 0], [2, "3984", 624, 744, 24, 24, 0], [2, "3984", 600, 744, 24, 24, 0], [2, "3984", 576, 744, 24, 24, 0], [2, "3984", 576, 720, 24, 24, 0], [2, "3984", 600, 720, 24, 24, 0], [2, "3984", 624, 720, 24, 24, 0], [2, "3984", 552, 744, 24, 24, 0], [2, "3984", 528, 744, 24, 24, 0], [2, "3984", 504, 744, 24, 24, 0], [2, "3984", 504, 720, 24, 24, 0], [2, "3984", 528, 720, 24, 24, 0], [2, "3984", 552, 720, 24, 24, 0], [2, "3984", -24, 24, 24, 24, 0], [2, "3984", -48, 24, 24, 24, 0], [2, "3984", -72, 24, 24, 24, 0], [2, "3984", -72, 0, 24, 24, 0], [2, "3984", -48, 0, 24, 24, 0], [2, "3984", -24, 0, 24, 24, 0], [2, "3984", 1032, 404, 24, 24, 0], [2, "3984", 1008, 404, 24, 24, 0], [2, "3984", 984, 404, 24, 24, 0], [2, "3984", 984, 380, 24, 24, 0], [2, "3984", 1008, 380, 24, 24, 0], [2, "3984", 1032, 380, 24, 24, 0], [2, "3984", 1032, 356, 24, 24, 0], [2, "3984", 1008, 356, 24, 24, 0], [2, "3984", 984, 356, 24, 24, 0], [2, "3984", 984, 332, 24, 24, 0], [2, "3984", 1008, 332, 24, 24, 0], [2, "3984", 1032, 332, 24, 24, 0], [2, "3984", 1032, 308, 24, 24, 0], [2, "3984", 1008, 308, 24, 24, 0], [2, "3984", 984, 308, 24, 24, 0], [2, "3984", 984, 284, 24, 24, 0], [2, "3984", 1008, 284, 24, 24, 0], [2, "3984", 1032, 284, 24, 24, 0], [2, "3984", 1032, 260, 24, 24, 0], [2, "3984", 1008, 260, 24, 24, 0], [2, "3984", 984, 260, 24, 24, 0], [2, "3984", 984, 236, 24, 24, 0], [2, "3984", 1008, 236, 24, 24, 0], [2, "3984", 1032, 236, 24, 24, 0], [2, "3984", 1032, 212, 24, 24, 0], [2, "3984", 1008, 212, 24, 24, 0], [2, "3984", 984, 212, 24, 24, 0], [2, "3984", 984, 188, 24, 24, 0], [2, "3984", 1008, 188, 24, 24, 0], [2, "3984", 1032, 188, 24, 24, 0], [2, "3984", 1032, 164, 24, 24, 0], [2, "3984", 1008, 164, 24, 24, 0], [2, "3984", 984, 164, 24, 24, 0], [2, "3984", 984, 140, 24, 24, 0], [2, "3984", 1008, 140, 24, 24, 0], [2, "3984", 1032, 140, 24, 24, 0], [2, "3984", 1032, 548, 24, 24, 0], [2, "3984", 1008, 548, 24, 24, 0], [2, "3984", 984, 548, 24, 24, 0], [2, "3984", 984, 524, 24, 24, 0], [2, "3984", 1008, 524, 24, 24, 0], [2, "3984", 1032, 524, 24, 24, 0], [2, "3984", 1032, 500, 24, 24, 0], [2, "3984", 1008, 500, 24, 24, 0], [2, "3984", 984, 500, 24, 24, 0], [2, "3984", 984, 476, 24, 24, 0], [2, "3984", 1008, 476, 24, 24, 0], [2, "3984", 1032, 476, 24, 24, 0], [2, "3984", 1032, 452, 24, 24, 0], [2, "3984", 1008, 452, 24, 24, 0], [2, "3984", 984, 452, 24, 24, 0], [2, "3984", 984, 428, 24, 24, 0], [2, "3984", 1008, 428, 24, 24, 0], [2, "3984", 1032, 428, 24, 24, 0], [2, "3978", -27, 275, 50, 26, 0], [2, "3978", -1, 262, 50, 26, 0], [2, "3978", 49, 236, 50, 26, 0], [2, "3978", 23, 249, 50, 26, 0], [2, "3978", 98, 210, 50, 26, 0], [2, "3978", 72, 223, 50, 26, 0], [2, "3978", 148, 186, 50, 26, 0], [2, "3978", 122, 199, 50, 26, 0], [2, "3978", 192, 163, 50, 26, 0], [2, "3978", 166, 176, 50, 26, 0], [2, "3978", 192, 163, 50, 26, 0], [2, "3978", 166, 176, 50, 26, 0], [2, "3978", 268, 126, 50, 26, 0], [2, "3978", 312, 103, 50, 26, 0], [2, "3978", 286, 116, 50, 26, 0], [2, "3978", 242, 139, 50, 26, 0], [2, "3978", 218, 150, 50, 26, 0], [2, "3978", 388, 66, 50, 26, 0], [2, "3978", 432, 43, 50, 26, 0], [2, "3978", 406, 56, 50, 26, 0], [2, "3978", 362, 79, 50, 26, 0], [2, "3978", 338, 90, 50, 26, 0], [2, "3978", 443, 39, 50, 26, 0], [2, "3978", 487, 16, 50, 26, 0], [2, "3978", 461, 29, 50, 26, 0], [2, "3978", 417, 52, 50, 26, 0], [2, "3978", 393, 63, 50, 26, 0], [2, "3978", -6, 183, 50, 26, 0], [2, "3978", 43, 208, 50, 26, 0], [2, "3978", 18, 196, 50, 26, 0], [2, "3978", 47, 210, 50, 26, 0], [2, "3978", 22, 198, 50, 26, 0], [2, "3984", -24, 216, 24, 24, 0], [2, "3984", -48, 216, 24, 24, 0], [2, "3984", -72, 216, 24, 24, 0], [2, "3984", -72, 192, 24, 24, 0], [2, "3984", -48, 192, 24, 24, 0], [2, "3984", -24, 192, 24, 24, 0], [2, "3978", -25, 174, 50, 26, 0], [2, "3984", -24, 192, 24, 24, 0], [2, "3984", -48, 192, 24, 24, 0], [2, "3984", -72, 192, 24, 24, 0], [2, "3984", -72, 168, 24, 24, 0], [2, "3984", -48, 168, 24, 24, 0], [2, "3984", -24, 168, 24, 24, 0], [2, "3984", -24, 288, 24, 24, 0], [2, "3984", -48, 288, 24, 24, 0], [2, "3984", -72, 288, 24, 24, 0], [2, "3984", -72, 264, 24, 24, 0], [2, "3984", -48, 264, 24, 24, 0], [2, "3984", -24, 264, 24, 24, 0], [2, "3978", 238, 165, 50, 26, 2], [2, "3978", 264, 178, 50, 26, 2], [2, "3978", 314, 204, 50, 26, 2], [2, "3978", 288, 191, 50, 26, 2], [2, "3978", 366, 230, 50, 26, 2], [2, "3978", 340, 217, 50, 26, 2], [2, "3978", 419, 256, 50, 26, 2], [2, "3978", 393, 243, 50, 26, 2], [2, "3978", 469, 282, 50, 26, 2], [2, "3978", 443, 269, 50, 26, 2], [2, "3978", 521, 308, 50, 26, 2], [2, "3978", 495, 295, 50, 26, 2], [2, "3978", 573, 333, 50, 26, 2], [2, "3978", 547, 320, 50, 26, 2], [2, "3978", 619, 356, 50, 26, 2], [2, "3978", 593, 343, 50, 26, 2], [2, "3978", 667, 380, 50, 26, 2], [2, "3978", 641, 367, 50, 26, 2], [2, "3978", 714, 404, 50, 26, 2], [2, "3978", 688, 391, 50, 26, 2], [2, "3978", 764, 429, 50, 26, 2], [2, "3978", 738, 416, 50, 26, 2], [2, "3978", 812, 453, 50, 26, 2], [2, "3978", 786, 440, 50, 26, 2], [2, "3978", 862, 478, 50, 26, 2], [2, "3978", 836, 465, 50, 26, 2], [2, "3978", 910, 502, 50, 26, 2], [2, "3978", 884, 489, 50, 26, 2], [2, "3978", 916, 505, 50, 26, 2], [2, "3978", 890, 492, 50, 26, 2], [2, "894", 508, 315, 24, 20, 0], [2, "894", 484, 303, 24, 20, 0], [2, "894", 460, 291, 24, 20, 0], [2, "894", 533, 327, 24, 20, 0], [2, "894", 509, 315, 24, 20, 0], [2, "894", 485, 303, 24, 20, 0]]}, {"type": 4, "obj": [[2, "3987", 476, 55, 42, 42, 0], [2, "879", 447, 82, 26, 56, 0], [2, "3987", 366, 108, 42, 42, 0], [2, "1231", 532, 25, 114, 162, 0], [4, 1, 656, 217, 1, 4022], [2, "3566", 114, 135, 38, 91, 0], [2, "1231", 703, 114, 114, 162, 0], [4, 2, 705, 336, 1, 4006], [2, "72", 107, 299, 42, 44, 2], [2, "879", 457, 291, 26, 56, 2], [2, "72", 136, 315, 42, 44, 2], [2, "72", 136, 315, 42, 44, 2], [2, "673", 329, 315, 80, 63, 2], [2, "3566", 387, 288, 38, 91, 0], [2, "3571", 106, 375, 34, 34, 0], [2, "72", 222, 365, 42, 44, 2], [2, "3571", 20, 376, 34, 34, 0], [2, "673", 258, 351, 80, 63, 2], [2, "3566", 268, 345, 38, 91, 0], [2, "3987", 849, 395, 42, 42, 0], [2, "3566", 509, 352, 38, 91, 0], [2, "3363", 42, 393, 80, 54, 0], [2, "3571", 106, 437, 34, 34, 0], [2, "3571", 15, 438, 34, 34, 0], [4, 4, 229, 527, 0, 4023], [2, "673", 2, 476, 80, 63, 2], [2, "3567", 400, 506, 50, 39, 0], [2, "3567", 400, 506, 50, 39, 0], [2, "3566", 51, 458, 38, 91, 0], [2, "3566", 18, 474, 38, 91, 0], [2, "3579", 456, 516, 66, 71, 0], [2, "3579", 324, 527, 66, 71, 2], [2, "3579", 549, 573, 66, 71, 0], [2, "3557", 404, 610, 22, 38, 0], [2, "3579", 225, 583, 66, 71, 2], [2, "3567", 597, 616, 50, 39, 0], [2, "3567", 185, 625, 50, 39, 0], [2, "3566", 687, 585, 38, 91, 0]]}, {"type": 3, "obj": [[2, "3978", 497, -4, 50, 26, 0], [2, "884", 258, -14, 24, 25, 2], [2, "884", 278, -24, 24, 25, 2], [2, "3609", 259, 580, 112, 59, 2], [2, "3609", 472, 569, 112, 59, 0], [2, "884", 433, 44, 24, 25, 0], [2, "884", 453, 54, 24, 25, 0], [2, "884", 409, 32, 24, 25, 0], [2, "884", 429, 42, 24, 25, 0], [2, "884", 528, 387, 24, 25, 0], [2, "884", 548, 397, 24, 25, 0], [2, "895", 348, 267, 8, 31, 0], [2, "895", 348, 259, 8, 31, 0], [2, "895", 348, 232, 8, 31, 0], [2, "895", 893, 559, 8, 31, 0], [2, "895", 901, 563, 8, 31, 0], [2, "894", 882, 575, 24, 20, 0], [2, "895", 160, -11, 8, 31, 2], [2, "895", 160, 14, 8, 31, 2], [2, "895", 957, 590, 8, 31, 0], [2, "895", 957, 563, 8, 31, 0], [2, "895", 957, 536, 8, 31, 0], [2, "895", 965, 594, 8, 31, 0], [2, "895", 965, 567, 8, 31, 0], [2, "895", 965, 540, 8, 31, 0], [2, "895", 973, 598, 8, 31, 0], [2, "895", 973, 571, 8, 31, 0], [2, "895", 973, 544, 8, 31, 0], [2, "895", 981, 602, 8, 31, 0], [2, "895", 981, 575, 8, 31, 0], [2, "895", 981, 548, 8, 31, 0], [2, "895", 989, 606, 8, 31, 0], [2, "895", 989, 579, 8, 31, 0], [2, "895", 989, 552, 8, 31, 0], [2, "895", 997, 610, 8, 31, 0], [2, "895", 997, 583, 8, 31, 0], [2, "895", 997, 556, 8, 31, 0], [2, "894", 957, 536, 24, 20, 0], [2, "894", 980, 548, 24, 20, 0], [2, "894", 956, 575, 24, 20, 0], [2, "894", 979, 587, 24, 20, 0], [2, "894", 956, 611, 24, 20, 0], [2, "894", 978, 622, 24, 20, 0], [2, "895", 238, 218, 8, 31, 0], [2, "895", 238, 204, 8, 31, 0], [2, "895", 238, 177, 8, 31, 0], [2, "884", 236, 241, 24, 25, 0], [2, "884", 256, 251, 24, 25, 0], [2, "22", 468, 236, 62, 38, 0], [2, "21", 442, 219, 28, 24, 0], [2, "21", 529, 265, 28, 24, 0], [2, "3554", 375, 107, 20, 31, 2], [2, "3525", 487, 230, 24, 23, 0], [2, "895", 53, 80, 8, 31, 2], [2, "895", 53, 67, 8, 31, 2], [2, "895", 53, 40, 8, 31, 2], [2, "1207", 964, 344, 22, 81, 0], [2, "1207", 964, 306, 22, 81, 0], [2, "1207", 846, 392, 22, 81, 0], [2, "1207", 846, 354, 22, 81, 0], [2, "1197", 943, 321, 54, 44, 2], [2, "1197", 938, 320, 54, 44, 2], [2, "1197", 889, 344, 54, 44, 2], [2, "1197", 839, 369, 54, 44, 2], [2, "1197", 790, 393, 54, 44, 2], [2, "884", 276, 261, 24, 25, 0], [2, "884", 296, 271, 24, 25, 0], [2, "884", 317, 281, 24, 25, 0], [2, "884", 337, 291, 24, 25, 0], [2, "884", 357, 301, 24, 25, 0], [2, "884", 377, 311, 24, 25, 0], [2, "884", 435, 341, 24, 25, 0], [2, "884", 455, 351, 24, 25, 0], [2, "884", 395, 321, 24, 25, 0], [2, "884", 415, 331, 24, 25, 0], [2, "884", 476, 361, 24, 25, 0], [2, "884", 496, 371, 24, 25, 0], [2, "884", 516, 381, 24, 25, 0], [2, "884", 536, 391, 24, 25, 0], [2, "884", 198, 253, 24, 25, 2], [2, "884", 218, 243, 24, 25, 2], [2, "884", 158, 273, 24, 25, 2], [2, "884", 178, 263, 24, 25, 2], [2, "884", 118, 293, 24, 25, 2], [2, "884", 138, 283, 24, 25, 2], [2, "884", 78, 313, 24, 25, 2], [2, "884", 98, 303, 24, 25, 2], [2, "884", 38, 333, 24, 25, 2], [2, "884", 58, 323, 24, 25, 2], [2, "884", -2, 353, 24, 25, 2], [2, "884", 18, 343, 24, 25, 2], [2, "884", 700, 473, 24, 25, 0], [2, "884", 720, 483, 24, 25, 0], [2, "884", 740, 493, 24, 25, 0], [2, "884", 760, 503, 24, 25, 0], [2, "884", 781, 514, 24, 25, 0], [2, "884", 801, 524, 24, 25, 0], [2, "884", 771, 509, 24, 25, 0], [2, "884", 791, 519, 24, 25, 0], [2, "884", 852, 548, 24, 25, 0], [2, "884", 872, 558, 24, 25, 0], [2, "884", 650, 448, 24, 25, 0], [2, "884", 670, 458, 24, 25, 0], [2, "884", 690, 468, 24, 25, 0], [2, "884", 710, 478, 24, 25, 0], [2, "895", 230, 207, 8, 31, 2], [2, "895", 230, 217, 8, 31, 2], [2, "895", 230, 180, 8, 31, 2], [2, "895", 138, 262, 8, 31, 2], [2, "895", 138, 252, 8, 31, 2], [2, "895", 138, 225, 8, 31, 2], [2, "895", 43, 309, 8, 31, 2], [2, "895", 43, 299, 8, 31, 2], [2, "895", 43, 272, 8, 31, 2], [2, "895", 453, 312, 8, 31, 0], [2, "895", 453, 285, 8, 31, 0], [2, "895", 453, 320, 8, 31, 0], [2, "895", 753, 463, 8, 31, 0], [2, "895", 753, 436, 8, 31, 0], [2, "895", 753, 471, 8, 31, 0], [2, "884", 811, 530, 24, 25, 0], [2, "884", 831, 540, 24, 25, 0], [2, "895", 398, 118, 8, 31, 2], [2, "895", 398, 108, 8, 31, 2], [2, "895", 398, 81, 8, 31, 2], [2, "895", 471, 79, 8, 31, 2], [2, "895", 471, 69, 8, 31, 2], [2, "895", 471, 42, 8, 31, 2], [2, "1207", 529, 47, 22, 81, 0], [2, "1207", 529, 28, 22, 81, 0], [2, "895", 320, 155, 8, 31, 2], [2, "895", 320, 145, 8, 31, 2], [2, "895", 320, 118, 8, 31, 2], [2, "895", 575, 48, 8, 31, 0], [2, "895", 575, 72, 8, 31, 0], [2, "895", 887, 228, 8, 31, 0], [2, "895", 887, 204, 8, 31, 0], [2, "895", 789, 178, 8, 31, 0], [2, "895", 789, 154, 8, 31, 0], [2, "895", 674, 121, 8, 31, 0], [2, "895", 674, 97, 8, 31, 0], [2, "894", 213, 178, 24, 20, 2], [2, "894", 189, 191, 24, 20, 2], [2, "894", 165, 203, 24, 20, 2], [2, "894", 141, 216, 24, 20, 2], [2, "884", 246, -7, 24, 25, 2], [2, "884", 266, -17, 24, 25, 2], [2, "884", 206, 13, 24, 25, 2], [2, "884", 226, 3, 24, 25, 2], [2, "884", 166, 33, 24, 25, 2], [2, "884", 186, 23, 24, 25, 2], [2, "884", 86, 73, 24, 25, 2], [2, "884", 106, 63, 24, 25, 2], [2, "884", 46, 93, 24, 25, 2], [2, "884", 66, 83, 24, 25, 2], [2, "884", 6, 113, 24, 25, 2], [2, "884", 26, 103, 24, 25, 2], [2, "884", 126, 53, 24, 25, 2], [2, "884", 146, 43, 24, 25, 2], [2, "884", 557, 402, 24, 25, 0], [2, "884", 577, 412, 24, 25, 0], [2, "884", 597, 423, 24, 25, 0], [2, "884", 617, 433, 24, 25, 0], [2, "884", 629, 437, 24, 25, 0], [2, "884", 649, 447, 24, 25, 0], [2, "895", 542, 357, 8, 31, 0], [2, "895", 542, 330, 8, 31, 0], [2, "895", 542, 365, 8, 31, 0], [2, "895", 652, 411, 8, 31, 0], [2, "895", 652, 384, 8, 31, 0], [2, "895", 652, 419, 8, 31, 0], [2, "895", 871, 520, 8, 31, 0], [2, "895", 871, 493, 8, 31, 0], [2, "895", 871, 528, 8, 31, 0], [2, "3554", 485, 53, 20, 31, 2], [2, "3554", 857, 394, 20, 31, 2], [2, "3554", 973, 343, 20, 31, 2], [2, "1235", 542, 72, 12, 18, 2], [2, "1236", 515, 89, 42, 43, 0], [2, "1235", 531, 58, 12, 18, 2], [2, "1236", 865, 245, 42, 43, 0], [2, "894", 115, 227, 24, 20, 2], [2, "894", 91, 240, 24, 20, 2], [2, "894", 67, 252, 24, 20, 2], [2, "894", 43, 265, 24, 20, 2], [2, "894", 19, 277, 24, 20, 2], [2, "894", -5, 290, 24, 20, 2], [2, "894", 68, 17, 24, 20, 2], [2, "894", 44, 29, 24, 20, 2], [2, "894", 20, 41, 24, 20, 2], [2, "894", -4, 53, 24, 20, 2], [2, "894", 163, -31, 24, 20, 2], [2, "894", 139, -19, 24, 20, 2], [2, "894", 115, -7, 24, 20, 2], [2, "894", 91, 5, 24, 20, 2], [2, "894", 441, -18, 24, 20, 0], [2, "894", 465, -6, 24, 20, 0], [2, "894", 489, 6, 24, 20, 0], [2, "894", 418, -30, 24, 20, 0], [2, "894", 394, -42, 24, 20, 0], [2, "894", 370, -54, 24, 20, 0], [2, "894", 286, 203, 24, 20, 0], [2, "894", 262, 191, 24, 20, 0], [2, "894", 238, 179, 24, 20, 0], [2, "894", 359, 240, 24, 20, 0], [2, "894", 335, 228, 24, 20, 0], [2, "894", 311, 216, 24, 20, 0], [2, "894", 429, 275, 24, 20, 0], [2, "894", 405, 263, 24, 20, 0], [2, "894", 381, 251, 24, 20, 0], [2, "894", 597, 359, 24, 20, 0], [2, "894", 573, 347, 24, 20, 0], [2, "894", 549, 335, 24, 20, 0], [2, "894", 669, 395, 24, 20, 0], [2, "894", 645, 383, 24, 20, 0], [2, "894", 621, 371, 24, 20, 0], [2, "894", 741, 430, 24, 20, 0], [2, "894", 717, 418, 24, 20, 0], [2, "894", 693, 406, 24, 20, 0], [2, "894", 813, 466, 24, 20, 0], [2, "894", 789, 454, 24, 20, 0], [2, "894", 765, 442, 24, 20, 0], [2, "894", 954, 537, 24, 20, 0], [2, "894", 930, 525, 24, 20, 0], [2, "894", 906, 513, 24, 20, 0], [2, "894", 882, 501, 24, 20, 0], [2, "894", 858, 489, 24, 20, 0], [2, "894", 834, 477, 24, 20, 0], [2, "43_1", 422, 345, 82, 58, 0], [2, "43_1", 466, 367, 82, 58, 0], [2, "1398", 618, 35, 28, 66, 0], [2, "1398", 714, 82, 28, 66, 0], [2, "1398", 832, 141, 28, 66, 0], [2, "3127", 373, 169, 30, 30, 0], [2, "3128", 350, 190, 38, 22, 0], [2, "1457", 507, 104, 22, 30, 0], [2, "1456", 487, 109, 24, 32, 0], [2, "1456", 858, 260, 24, 32, 0], [2, "1457", 773, 250, 22, 30, 0], [2, "1457", 658, 160, 22, 30, 0], [2, "1457", 640, 151, 22, 30, 0], [2, "1456", 695, 180, 24, 32, 2], [2, "1457", 332, 187, 22, 30, 0], [2, "1457", 655, 336, 22, 30, 0], [2, "1457", 647, 352, 22, 30, 0], [2, "1457", 666, 353, 22, 30, 0], [2, "3127", 682, 350, 30, 30, 0], [2, "1456", 698, 366, 24, 32, 0], [2, "3499", 287, 207, 30, 75, 0], [2, "3499", 307, 217, 30, 75, 0], [2, "3359", 316, 281, 32, 43, 0], [2, "3359", 343, 295, 32, 43, 0], [2, "7", 172, 268, 28, 27, 0], [2, "48", 204, 255, 52, 38, 0], [2, "48", 219, 264, 52, 38, 0], [2, "3745", 149, 267, 78, 60, 2], [2, "7", 172, 265, 28, 27, 0], [2, "3522", 217, 251, 44, 31, 0], [2, "3518", 168, 222, 42, 37, 2], [2, "3560", 424, 287, 28, 31, 0], [2, "3560", 543, 344, 28, 31, 0], [2, "3597", 10, 294, 28, 52, 2], [2, "3601", 56, 271, 26, 39, 2], [2, "3604", 58, 302, 26, 26, 2], [2, "679", 341, 279, 36, 32, 0], [2, "3603", 679, 408, 50, 54, 0], [2, "884", 294, -25, 24, 25, 0], [2, "884", 314, -15, 24, 25, 0], [2, "884", 334, -5, 24, 25, 0], [2, "884", 354, 5, 24, 25, 0], [2, "884", 375, 15, 24, 25, 0], [2, "884", 395, 25, 24, 25, 0], [2, "3586", 158, 33, 54, 45, 2], [2, "3586", 47, 92, 54, 45, 2], [2, "41", 78, 95, 12, 11, 0], [2, "3564", 172, 3, 24, 45, 0], [2, "3564", 53, 66, 24, 45, 0], [2, "3499", 576, 351, 30, 75, 0], [2, "3499", 596, 361, 30, 75, 0], [2, "3499", 792, 458, 30, 75, 0], [2, "3499", 812, 468, 30, 75, 0], [2, "3499", 416, -27, 30, 75, 0], [2, "3499", 436, -17, 30, 75, 0], [2, "3535", 4, 104, 44, 61, 2], [2, "3586", 504, 569, 54, 45, 0], [2, "3586", 286, 579, 54, 45, 2], [2, "3479", 210, -36, 62, 83, 2], [2, "3579", 295, -35, 66, 71, 0], [2, "3586", 346, 12, 54, 45, 0], [2, "3643", 413, -38, 56, 101, 0], [2, "3643", 87, -7, 56, 101, 2], [2, "3593", 98, 51, 96, 73, 2], [2, "3643", 282, 189, 56, 101, 0], [2, "3643", 571, 334, 56, 101, 0], [2, "3643", 787, 440, 56, 101, 0], [2, "3479", 615, 403, 62, 83, 0], [2, "3745", 653, 465, 78, 60, 0], [2, "3479", 727, 459, 62, 83, 0], [2, "117", 199, 647, 22, 27, 0], [2, "117", 414, 530, 22, 27, 0], [2, "117", 609, 637, 22, 27, 0], [2, "3566", 851, 503, 38, 91, 0], [2, "3213", 679, 455, 20, 25, 0], [2, "3212", 696, 462, 14, 27, 0], [2, "3588", 393, 618, 72, 56, 0], [2, "3588", 365, 631, 72, 56, 0], [2, "3560", 22, 54, 28, 31, 2], [2, "3560", 194, -17, 28, 31, 2], [2, "3557", 367, -8, 22, 38, 0], [2, "3525", 522, 567, 24, 23, 0], [2, "3525", 299, 575, 24, 23, 0], [2, "895", 887, 178, 8, 31, 0], [2, "895", 887, 154, 8, 31, 0], [2, "895", 887, 149, 8, 31, 0], [2, "895", 887, 125, 8, 31, 0], [2, "895", 789, 129, 8, 31, 0], [2, "895", 789, 105, 8, 31, 0], [2, "895", 789, 99, 8, 31, 0], [2, "895", 789, 75, 8, 31, 0], [2, "895", 674, 70, 8, 31, 0], [2, "895", 674, 46, 8, 31, 0], [2, "895", 674, 42, 8, 31, 0], [2, "895", 674, 18, 8, 31, 0], [2, "895", 575, 22, 8, 31, 0], [2, "895", 575, -2, 8, 31, 0], [2, "895", 575, -6, 8, 31, 0], [2, "895", 575, -30, 8, 31, 0], [2, "1207", 530, -43, 22, 81, 0], [2, "1398", 929, 185, 28, 66, 0], [2, "3987", 965, 344, 42, 42, 0], [2, "894", 252, -76, 24, 20, 2], [2, "894", 228, -64, 24, 20, 2], [2, "894", 204, -52, 24, 20, 2], [2, "894", 180, -40, 24, 20, 2], [2, "894", 278, -89, 24, 20, 2], [2, "894", 254, -77, 24, 20, 2], [2, "894", 230, -65, 24, 20, 2], [2, "894", 206, -53, 24, 20, 2], [2, "894", 350, -64, 24, 20, 0], [2, "894", 326, -76, 24, 20, 0], [2, "894", 302, -88, 24, 20, 0], [2, "884", -14, 123, 24, 25, 2], [2, "3978", -29, 39, 50, 26, 0], [2, "3978", -5, 27, 50, 26, 0], [2, "3978", 21, 14, 50, 26, 0], [2, "3978", 46, 1, 50, 26, 0], [2, "3978", 73, -11, 50, 26, 0], [2, "3978", 99, -24, 50, 26, 0], [2, "3978", 124, -37, 50, 26, 0], [2, "3978", 471, -17, 50, 26, 0]]}, {"type": 3, "obj": [[2, "617", 252, 194, 22, 43, 2], [2, "617", 242, 199, 22, 43, 2], [2, "617", 242, 168, 22, 43, 2], [2, "617", 252, 163, 22, 43, 2], [2, "617", 252, 146, 22, 43, 2], [2, "617", 242, 151, 22, 43, 2], [2, "617", 941, 280, 22, 43, 0], [2, "617", 931, 275, 22, 43, 0], [2, "617", 931, 244, 22, 43, 0], [2, "617", 941, 249, 22, 43, 0], [2, "617", 941, 232, 22, 43, 0], [2, "617", 931, 227, 22, 43, 0], [2, "617", 974, 296, 22, 43, 0], [2, "617", 964, 291, 22, 43, 0], [2, "617", 964, 260, 22, 43, 0], [2, "617", 974, 265, 22, 43, 0], [2, "617", 974, 248, 22, 43, 0], [2, "617", 964, 243, 22, 43, 0], [2, "617", 775, 464, 22, 43, 2], [2, "617", 765, 469, 22, 43, 2], [2, "617", 765, 438, 22, 43, 2], [2, "617", 775, 433, 22, 43, 2], [2, "617", 775, 416, 22, 43, 2], [2, "617", 765, 421, 22, 43, 2], [2, "617", 968, 368, 22, 43, 2], [2, "617", 958, 373, 22, 43, 2], [2, "617", 958, 342, 22, 43, 2], [2, "617", 968, 337, 22, 43, 2], [2, "617", 968, 320, 22, 43, 2], [2, "617", 958, 325, 22, 43, 2], [2, "617", 935, 386, 22, 43, 2], [2, "617", 925, 391, 22, 43, 2], [2, "617", 925, 360, 22, 43, 2], [2, "617", 935, 355, 22, 43, 2], [2, "617", 935, 338, 22, 43, 2], [2, "617", 925, 343, 22, 43, 2], [2, "617", 903, 401, 22, 43, 2], [2, "617", 893, 406, 22, 43, 2], [2, "617", 893, 375, 22, 43, 2], [2, "617", 903, 370, 22, 43, 2], [2, "617", 903, 353, 22, 43, 2], [2, "617", 893, 358, 22, 43, 2], [2, "617", 870, 417, 22, 43, 2], [2, "617", 860, 422, 22, 43, 2], [2, "617", 860, 391, 22, 43, 2], [2, "617", 870, 386, 22, 43, 2], [2, "617", 870, 369, 22, 43, 2], [2, "617", 860, 374, 22, 43, 2], [2, "617", 838, 432, 22, 43, 2], [2, "617", 828, 437, 22, 43, 2], [2, "617", 828, 406, 22, 43, 2], [2, "617", 838, 401, 22, 43, 2], [2, "617", 838, 384, 22, 43, 2], [2, "617", 828, 389, 22, 43, 2], [2, "617", 805, 449, 22, 43, 2], [2, "617", 795, 454, 22, 43, 2], [2, "617", 795, 423, 22, 43, 2], [2, "617", 805, 418, 22, 43, 2], [2, "617", 805, 401, 22, 43, 2], [2, "617", 795, 406, 22, 43, 2], [2, "617", 286, 189, 22, 43, 2], [2, "617", 276, 194, 22, 43, 2], [2, "617", 276, 163, 22, 43, 2], [2, "617", 286, 158, 22, 43, 2], [2, "617", 286, 141, 22, 43, 2], [2, "617", 276, 146, 22, 43, 2], [2, "617", 275, 187, 22, 43, 2], [2, "617", 265, 192, 22, 43, 2], [2, "617", 265, 161, 22, 43, 2], [2, "617", 275, 156, 22, 43, 2], [2, "617", 275, 139, 22, 43, 2], [2, "617", 265, 144, 22, 43, 2], [2, "3401", 491, 28, 44, 81, 0], [2, "3401", 491, 7, 44, 81, 0], [2, "3401", 478, 21, 44, 81, 0], [2, "3401", 478, 0, 44, 81, 0], [2, "3401", -4, 43, 44, 81, 2], [2, "3025", 604, 376, 92, 53, 0], [2, "3401", -4, 64, 44, 81, 2], [2, "3401", 302, -88, 44, 81, 0], [2, "3401", 302, -67, 44, 81, 0], [2, "3401", 40, 42, 44, 81, 2], [2, "3401", 40, 21, 44, 81, 2], [2, "3401", 82, 21, 44, 81, 2], [2, "3401", 82, 0, 44, 81, 2], [2, "3401", 126, -1, 44, 81, 2], [2, "3401", 126, -22, 44, 81, 2], [2, "3401", 213, -45, 44, 81, 2], [2, "3401", 213, -66, 44, 81, 2], [2, "3401", 169, -23, 44, 81, 2], [2, "3401", 169, -44, 44, 81, 2], [2, "3401", 257, -67, 44, 81, 2], [2, "3401", 257, -88, 44, 81, 2], [2, "3401", 346, -45, 44, 81, 0], [2, "3401", 346, -66, 44, 81, 0], [2, "3401", 390, -23, 44, 81, 0], [2, "3401", 390, -44, 44, 81, 0], [2, "3401", 434, -1, 44, 81, 0], [2, "3401", 434, -22, 44, 81, 0], [2, "617", 513, 78, 22, 43, 2], [2, "617", 503, 83, 22, 43, 2], [2, "617", 503, 52, 22, 43, 2], [2, "617", 513, 47, 22, 43, 2], [2, "617", 513, 30, 22, 43, 2], [2, "617", 503, 35, 22, 43, 2], [2, "617", 546, 83, 22, 43, 0], [2, "617", 536, 78, 22, 43, 0], [2, "617", 536, 47, 22, 43, 0], [2, "617", 546, 52, 22, 43, 0], [2, "617", 546, 35, 22, 43, 0], [2, "617", 536, 30, 22, 43, 0], [2, "617", 579, 99, 22, 43, 0], [2, "617", 569, 94, 22, 43, 0], [2, "617", 569, 63, 22, 43, 0], [2, "617", 579, 68, 22, 43, 0], [2, "617", 579, 51, 22, 43, 0], [2, "617", 569, 46, 22, 43, 0], [2, "617", 481, 93, 22, 43, 2], [2, "617", 471, 98, 22, 43, 2], [2, "617", 471, 67, 22, 43, 2], [2, "617", 481, 62, 22, 43, 2], [2, "617", 481, 45, 22, 43, 2], [2, "617", 471, 50, 22, 43, 2], [2, "617", 383, 142, 22, 43, 2], [2, "617", 373, 147, 22, 43, 2], [2, "617", 373, 116, 22, 43, 2], [2, "617", 383, 111, 22, 43, 2], [2, "617", 383, 94, 22, 43, 2], [2, "617", 373, 99, 22, 43, 2], [2, "617", 351, 157, 22, 43, 2], [2, "617", 341, 162, 22, 43, 2], [2, "617", 341, 131, 22, 43, 2], [2, "617", 351, 126, 22, 43, 2], [2, "617", 351, 109, 22, 43, 2], [2, "617", 341, 114, 22, 43, 2], [2, "617", 318, 174, 22, 43, 2], [2, "617", 308, 179, 22, 43, 2], [2, "617", 308, 148, 22, 43, 2], [2, "617", 318, 143, 22, 43, 2], [2, "617", 318, 126, 22, 43, 2], [2, "617", 308, 131, 22, 43, 2], [2, "617", 612, 116, 22, 43, 0], [2, "617", 602, 111, 22, 43, 0], [2, "617", 602, 80, 22, 43, 0], [2, "617", 612, 85, 22, 43, 0], [2, "617", 612, 68, 22, 43, 0], [2, "617", 602, 63, 22, 43, 0], [2, "617", 645, 132, 22, 43, 0], [2, "617", 635, 127, 22, 43, 0], [2, "617", 635, 96, 22, 43, 0], [2, "617", 645, 101, 22, 43, 0], [2, "617", 645, 84, 22, 43, 0], [2, "617", 635, 79, 22, 43, 0], [2, "617", 677, 148, 22, 43, 0], [2, "617", 667, 143, 22, 43, 0], [2, "617", 667, 112, 22, 43, 0], [2, "617", 677, 117, 22, 43, 0], [2, "617", 677, 100, 22, 43, 0], [2, "617", 667, 95, 22, 43, 0], [2, "617", 710, 164, 22, 43, 0], [2, "617", 700, 159, 22, 43, 0], [2, "617", 700, 128, 22, 43, 0], [2, "617", 710, 133, 22, 43, 0], [2, "617", 710, 116, 22, 43, 0], [2, "617", 700, 111, 22, 43, 0], [2, "617", 743, 181, 22, 43, 0], [2, "617", 733, 176, 22, 43, 0], [2, "617", 733, 145, 22, 43, 0], [2, "617", 743, 150, 22, 43, 0], [2, "617", 743, 133, 22, 43, 0], [2, "617", 733, 128, 22, 43, 0], [2, "617", 776, 197, 22, 43, 0], [2, "617", 766, 192, 22, 43, 0], [2, "617", 766, 161, 22, 43, 0], [2, "617", 776, 166, 22, 43, 0], [2, "617", 776, 149, 22, 43, 0], [2, "617", 766, 144, 22, 43, 0], [2, "617", 809, 214, 22, 43, 0], [2, "617", 799, 209, 22, 43, 0], [2, "617", 799, 178, 22, 43, 0], [2, "617", 809, 183, 22, 43, 0], [2, "617", 809, 166, 22, 43, 0], [2, "617", 799, 161, 22, 43, 0], [2, "617", 842, 230, 22, 43, 0], [2, "617", 832, 225, 22, 43, 0], [2, "617", 832, 194, 22, 43, 0], [2, "617", 842, 199, 22, 43, 0], [2, "617", 842, 182, 22, 43, 0], [2, "617", 832, 177, 22, 43, 0], [2, "617", 875, 247, 22, 43, 0], [2, "617", 865, 242, 22, 43, 0], [2, "617", 865, 211, 22, 43, 0], [2, "617", 875, 216, 22, 43, 0], [2, "617", 875, 199, 22, 43, 0], [2, "617", 865, 194, 22, 43, 0], [2, "617", 908, 263, 22, 43, 0], [2, "617", 898, 258, 22, 43, 0], [2, "617", 898, 227, 22, 43, 0], [2, "617", 908, 232, 22, 43, 0], [2, "617", 908, 215, 22, 43, 0], [2, "617", 898, 210, 22, 43, 0], [2, "1208", 530, 80, 52, 56, 0], [2, "1208", 574, 102, 52, 56, 0], [2, "1208", 622, 125, 52, 56, 0], [2, "1208", 666, 147, 52, 56, 0], [2, "1208", 710, 169, 52, 56, 0], [2, "1208", 754, 191, 52, 56, 0], [2, "1208", 800, 215, 52, 56, 0], [2, "1208", 844, 237, 52, 56, 0], [2, "1208", 890, 259, 52, 56, 0], [2, "1208", 491, 80, 52, 56, 2], [2, "1208", 471, 88, 52, 56, 2], [2, "1208", 358, 146, 52, 56, 2], [2, "1208", 319, 165, 52, 56, 2], [2, "1208", 290, 178, 52, 56, 2], [2, "1208", 952, 366, 52, 56, 2], [2, "1208", 952, 368, 52, 56, 2], [2, "1208", 910, 389, 52, 56, 2], [2, "1208", 874, 406, 52, 56, 2], [2, "1208", 832, 427, 52, 56, 2], [2, "866", 903, 422, 42, 26, 0], [2, "1194", 893, 381, 46, 60, 0], [2, "3401", 193, 199, 44, 81, 2], [2, "3401", 193, 178, 44, 81, 2], [2, "3401", 149, 222, 44, 81, 2], [2, "3401", 149, 201, 44, 81, 2], [2, "3401", 105, 243, 44, 81, 2], [2, "3401", 105, 222, 44, 81, 2], [2, "3401", 61, 266, 44, 81, 2], [2, "3401", 61, 245, 44, 81, 2], [2, "3401", 16, 289, 44, 81, 2], [2, "3401", 16, 268, 44, 81, 2], [2, "3401", -28, 312, 44, 81, 2], [2, "3401", -28, 291, 44, 81, 2], [2, "3401", 238, 198, 44, 81, 0], [2, "3401", 238, 177, 44, 81, 0], [2, "3401", 283, 221, 44, 81, 0], [2, "3401", 283, 200, 44, 81, 0], [2, "3401", 372, 266, 44, 81, 0], [2, "3401", 372, 245, 44, 81, 0], [2, "3401", 327, 243, 44, 81, 0], [2, "3401", 327, 222, 44, 81, 0], [2, "3401", 415, 287, 44, 81, 0], [2, "3401", 415, 266, 44, 81, 0], [2, "3401", 542, 347, 44, 81, 0], [2, "3401", 542, 326, 44, 81, 0], [2, "3401", 646, 404, 44, 81, 0], [2, "3401", 646, 383, 44, 81, 0], [2, "3401", 689, 425, 44, 81, 0], [2, "3401", 689, 404, 44, 81, 0], [2, "3401", 734, 448, 44, 81, 0], [2, "3401", 734, 427, 44, 81, 0], [2, "3401", 778, 470, 44, 81, 0], [2, "3401", 778, 449, 44, 81, 0], [2, "3401", 823, 492, 44, 81, 0], [2, "3401", 823, 471, 44, 81, 0], [2, "3401", 866, 513, 44, 81, 0], [2, "3401", 866, 492, 44, 81, 0], [2, "3401", 911, 536, 44, 81, 0], [2, "3401", 911, 515, 44, 81, 0], [2, "3401", 955, 558, 44, 81, 0], [2, "3401", 955, 537, 44, 81, 0], [2, "3401", 573, 367, 44, 81, 0], [2, "3401", 573, 346, 44, 81, 0], [2, "3401", 616, 388, 44, 81, 0], [2, "3401", 616, 367, 44, 81, 0], [2, "43_1", -53, 605, 82, 58, 2], [2, "43_1", -21, 634, 82, 58, 2], [2, "617", 549, 5, 22, 43, 0], [2, "617", 539, 0, 22, 43, 0], [2, "617", 539, -31, 22, 43, 0], [2, "617", 549, -26, 22, 43, 0], [2, "617", 549, -43, 22, 43, 0], [2, "617", 539, -48, 22, 43, 0], [2, "617", 582, 22, 22, 43, 0], [2, "617", 572, 17, 22, 43, 0], [2, "617", 572, -14, 22, 43, 0], [2, "617", 582, -9, 22, 43, 0], [2, "617", 582, -26, 22, 43, 0], [2, "617", 572, -31, 22, 43, 0], [2, "617", 612, 35, 22, 43, 0], [2, "617", 602, 30, 22, 43, 0], [2, "617", 602, -1, 22, 43, 0], [2, "617", 612, 4, 22, 43, 0], [2, "617", 612, -13, 22, 43, 0], [2, "617", 602, -18, 22, 43, 0], [2, "617", 645, 52, 22, 43, 0], [2, "617", 635, 47, 22, 43, 0], [2, "617", 635, 16, 22, 43, 0], [2, "617", 645, 21, 22, 43, 0], [2, "617", 645, 4, 22, 43, 0], [2, "617", 635, -1, 22, 43, 0], [2, "617", 675, 67, 22, 43, 0], [2, "617", 665, 62, 22, 43, 0], [2, "617", 665, 31, 22, 43, 0], [2, "617", 675, 36, 22, 43, 0], [2, "617", 675, 19, 22, 43, 0], [2, "617", 665, 14, 22, 43, 0], [2, "617", 708, 84, 22, 43, 0], [2, "617", 698, 79, 22, 43, 0], [2, "617", 698, 48, 22, 43, 0], [2, "617", 708, 53, 22, 43, 0], [2, "617", 708, 36, 22, 43, 0], [2, "617", 698, 31, 22, 43, 0], [2, "617", 740, 98, 22, 43, 0], [2, "617", 730, 93, 22, 43, 0], [2, "617", 730, 62, 22, 43, 0], [2, "617", 740, 67, 22, 43, 0], [2, "617", 740, 50, 22, 43, 0], [2, "617", 730, 45, 22, 43, 0], [2, "617", 773, 115, 22, 43, 0], [2, "617", 763, 110, 22, 43, 0], [2, "617", 763, 79, 22, 43, 0], [2, "617", 773, 84, 22, 43, 0], [2, "617", 773, 67, 22, 43, 0], [2, "617", 763, 62, 22, 43, 0], [2, "617", 801, 130, 22, 43, 0], [2, "617", 791, 125, 22, 43, 0], [2, "617", 791, 94, 22, 43, 0], [2, "617", 801, 99, 22, 43, 0], [2, "617", 801, 82, 22, 43, 0], [2, "617", 791, 77, 22, 43, 0], [2, "617", 834, 147, 22, 43, 0], [2, "617", 824, 142, 22, 43, 0], [2, "617", 824, 111, 22, 43, 0], [2, "617", 834, 116, 22, 43, 0], [2, "617", 834, 99, 22, 43, 0], [2, "617", 824, 94, 22, 43, 0], [2, "617", 863, 161, 22, 43, 0], [2, "617", 853, 156, 22, 43, 0], [2, "617", 853, 125, 22, 43, 0], [2, "617", 863, 130, 22, 43, 0], [2, "617", 863, 113, 22, 43, 0], [2, "617", 853, 108, 22, 43, 0], [2, "617", 896, 178, 22, 43, 0], [2, "617", 886, 173, 22, 43, 0], [2, "617", 886, 142, 22, 43, 0], [2, "617", 896, 147, 22, 43, 0], [2, "617", 896, 130, 22, 43, 0], [2, "617", 886, 125, 22, 43, 0], [2, "617", 927, 193, 22, 43, 0], [2, "617", 917, 188, 22, 43, 0], [2, "617", 917, 157, 22, 43, 0], [2, "617", 927, 162, 22, 43, 0], [2, "617", 927, 145, 22, 43, 0], [2, "617", 917, 140, 22, 43, 0], [2, "617", 960, 210, 22, 43, 0], [2, "617", 950, 205, 22, 43, 0], [2, "617", 950, 174, 22, 43, 0], [2, "617", 960, 179, 22, 43, 0], [2, "617", 960, 162, 22, 43, 0], [2, "617", 950, 157, 22, 43, 0], [2, "1501", 880, 692, 50, 26, 0], [2, "1501", 906, 679, 50, 26, 0], [2, "1501", 932, 666, 50, 26, 0], [2, "1501", 958, 679, 50, 26, 0], [2, "1501", 932, 692, 50, 26, 0], [2, "1501", 906, 705, 50, 26, 0], [2, "1501", 932, 718, 50, 26, 0], [2, "1501", 958, 705, 50, 26, 0], [2, "1501", 984, 692, 50, 26, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 30, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 39, 24, 25, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 24, 25, 26, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, 24, 25, 30, 29, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 24, 25, 26, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, 26, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, 24, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 35, 36, -1, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 10, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 24, 25, 26, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 10, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 24, 25, 26, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, -1, -1, 15, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 26, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "3736", 191, 254, 76, 41, 0], [2, "3736", 143, 278, 76, 41, 0], [2, "3736", 221, 269, 76, 41, 0], [2, "3736", 173, 293, 76, 41, 0], [2, "3736", 250, 284, 76, 41, 0], [2, "3736", 202, 308, 76, 41, 0], [2, "3736", 279, 299, 76, 41, 0], [2, "3736", 231, 323, 76, 41, 0], [2, "3736", 96, 302, 76, 41, 0], [2, "3736", 48, 326, 76, 41, 0], [2, "3736", 126, 317, 76, 41, 0], [2, "3736", 78, 341, 76, 41, 0], [2, "3736", 155, 332, 76, 41, 0], [2, "3736", 107, 356, 76, 41, 0], [2, "3736", 184, 347, 76, 41, 0], [2, "3736", 136, 371, 76, 41, 0], [2, "3736", 28, 336, 76, 41, 0], [2, "3736", -20, 360, 76, 41, 0], [2, "3736", 58, 351, 76, 41, 0], [2, "3736", 10, 375, 76, 41, 0], [2, "3736", 87, 366, 76, 41, 0], [2, "3736", 39, 390, 76, 41, 0], [2, "3736", 116, 381, 76, 41, 0], [2, "3736", 68, 405, 76, 41, 0], [2, "1501", -81, 15, 50, 26, 0], [2, "1501", -55, 2, 50, 26, 0], [2, "1501", -29, -11, 50, 26, 0], [2, "1501", -3, 2, 50, 26, 0], [2, "1501", -29, 15, 50, 26, 0], [2, "1501", -55, 28, 50, 26, 0], [2, "1501", -29, 41, 50, 26, 0], [2, "1501", -3, 28, 50, 26, 0], [2, "1501", 23, 15, 50, 26, 0], [2, "1501", -15, -15, 50, 26, 0], [2, "1501", 11, -28, 50, 26, 0], [2, "1501", 37, -41, 50, 26, 0], [2, "1501", 63, -28, 50, 26, 0], [2, "1501", 37, -15, 50, 26, 0], [2, "1501", 11, -2, 50, 26, 0], [2, "1501", 37, 11, 50, 26, 0], [2, "1501", 63, -2, 50, 26, 0], [2, "1501", 89, -15, 50, 26, 0], [2, "1501", 472, -12, 50, 26, 0], [2, "1501", 498, -25, 50, 26, 0], [2, "1501", 524, -38, 50, 26, 0], [2, "1501", 550, -25, 50, 26, 0], [2, "1501", 524, -12, 50, 26, 0], [2, "1501", 498, 1, 50, 26, 0], [2, "1501", 524, 14, 50, 26, 0], [2, "1501", 550, 1, 50, 26, 0], [2, "1501", 576, -12, 50, 26, 0], [2, "1501", 548, 23, 50, 26, 0], [2, "1501", 574, 10, 50, 26, 0], [2, "1501", 600, -3, 50, 26, 0], [2, "1501", 626, 10, 50, 26, 0], [2, "1501", 600, 23, 50, 26, 0], [2, "1501", 574, 36, 50, 26, 0], [2, "1501", 600, 49, 50, 26, 0], [2, "1501", 626, 36, 50, 26, 0], [2, "1501", 652, 23, 50, 26, 0], [2, "1501", 619, 61, 50, 26, 0], [2, "1501", 645, 48, 50, 26, 0], [2, "1501", 671, 35, 50, 26, 0], [2, "1501", 697, 48, 50, 26, 0], [2, "1501", 671, 61, 50, 26, 0], [2, "1501", 645, 74, 50, 26, 0], [2, "1501", 671, 87, 50, 26, 0], [2, "1501", 697, 74, 50, 26, 0], [2, "1501", 723, 61, 50, 26, 0], [2, "1501", 693, 97, 50, 26, 0], [2, "1501", 719, 84, 50, 26, 0], [2, "1501", 745, 71, 50, 26, 0], [2, "1501", 771, 84, 50, 26, 0], [2, "1501", 745, 97, 50, 26, 0], [2, "1501", 719, 110, 50, 26, 0], [2, "1501", 745, 123, 50, 26, 0], [2, "1501", 771, 110, 50, 26, 0], [2, "1501", 797, 97, 50, 26, 0], [2, "1501", 767, 137, 50, 26, 0], [2, "1501", 793, 124, 50, 26, 0], [2, "1501", 819, 111, 50, 26, 0], [2, "1501", 845, 124, 50, 26, 0], [2, "1501", 819, 137, 50, 26, 0], [2, "1501", 793, 150, 50, 26, 0], [2, "1501", 819, 163, 50, 26, 0], [2, "1501", 845, 150, 50, 26, 0], [2, "1501", 871, 137, 50, 26, 0], [2, "1501", 847, 174, 50, 26, 0], [2, "1501", 873, 161, 50, 26, 0], [2, "1501", 899, 148, 50, 26, 0], [2, "1501", 925, 161, 50, 26, 0], [2, "1501", 899, 174, 50, 26, 0], [2, "1501", 873, 187, 50, 26, 0], [2, "1501", 899, 200, 50, 26, 0], [2, "1501", 925, 187, 50, 26, 0], [2, "1501", 951, 174, 50, 26, 0], [2, "1501", 919, 214, 50, 26, 0], [2, "1501", 945, 201, 50, 26, 0], [2, "1501", 971, 188, 50, 26, 0], [2, "1501", 997, 201, 50, 26, 0], [2, "1501", 971, 214, 50, 26, 0], [2, "1501", 945, 227, 50, 26, 0], [2, "1501", 971, 240, 50, 26, 0], [2, "1501", 997, 227, 50, 26, 0], [2, "1501", 1023, 214, 50, 26, 0], [2, "1501", 592, -30, 50, 26, 0], [2, "1501", 618, -43, 50, 26, 0], [2, "1501", 644, -56, 50, 26, 0], [2, "1501", 670, -43, 50, 26, 0], [2, "1501", 644, -30, 50, 26, 0], [2, "1501", 618, -17, 50, 26, 0], [2, "1501", 644, -4, 50, 26, 0], [2, "1501", 670, -17, 50, 26, 0], [2, "1501", 696, -30, 50, 26, 0], [2, "1501", 663, 8, 50, 26, 0], [2, "1501", 689, -5, 50, 26, 0], [2, "1501", 715, -18, 50, 26, 0], [2, "1501", 741, -5, 50, 26, 0], [2, "1501", 715, 8, 50, 26, 0], [2, "1501", 689, 21, 50, 26, 0], [2, "1501", 715, 34, 50, 26, 0], [2, "1501", 741, 21, 50, 26, 0], [2, "1501", 767, 8, 50, 26, 0], [2, "1501", 737, 44, 50, 26, 0], [2, "1501", 763, 31, 50, 26, 0], [2, "1501", 789, 18, 50, 26, 0], [2, "1501", 815, 31, 50, 26, 0], [2, "1501", 789, 44, 50, 26, 0], [2, "1501", 763, 57, 50, 26, 0], [2, "1501", 789, 70, 50, 26, 0], [2, "1501", 815, 57, 50, 26, 0], [2, "1501", 841, 44, 50, 26, 0], [2, "1501", 811, 84, 50, 26, 0], [2, "1501", 837, 71, 50, 26, 0], [2, "1501", 863, 58, 50, 26, 0], [2, "1501", 889, 71, 50, 26, 0], [2, "1501", 863, 84, 50, 26, 0], [2, "1501", 837, 97, 50, 26, 0], [2, "1501", 863, 110, 50, 26, 0], [2, "1501", 889, 97, 50, 26, 0], [2, "1501", 915, 84, 50, 26, 0], [2, "1501", 891, 121, 50, 26, 0], [2, "1501", 917, 108, 50, 26, 0], [2, "1501", 943, 95, 50, 26, 0], [2, "1501", 969, 108, 50, 26, 0], [2, "1501", 943, 121, 50, 26, 0], [2, "1501", 917, 134, 50, 26, 0], [2, "1501", 943, 147, 50, 26, 0], [2, "1501", 969, 134, 50, 26, 0], [2, "1501", 995, 121, 50, 26, 0], [2, "1501", 963, 161, 50, 26, 0], [2, "1501", 989, 148, 50, 26, 0], [2, "1501", 1015, 135, 50, 26, 0], [2, "1501", 1041, 148, 50, 26, 0], [2, "1501", 1015, 161, 50, 26, 0], [2, "1501", 989, 174, 50, 26, 0], [2, "1501", 1015, 187, 50, 26, 0], [2, "1501", 1041, 174, 50, 26, 0], [2, "1501", 1067, 161, 50, 26, 0], [2, "1501", 799, 2, 50, 26, 0], [2, "1501", 825, -11, 50, 26, 0], [2, "1501", 851, -24, 50, 26, 0], [2, "1501", 877, -11, 50, 26, 0], [2, "1501", 851, 2, 50, 26, 0], [2, "1501", 825, 15, 50, 26, 0], [2, "1501", 851, 28, 50, 26, 0], [2, "1501", 877, 15, 50, 26, 0], [2, "1501", 903, 2, 50, 26, 0], [2, "1501", 873, 42, 50, 26, 0], [2, "1501", 899, 29, 50, 26, 0], [2, "1501", 925, 16, 50, 26, 0], [2, "1501", 951, 29, 50, 26, 0], [2, "1501", 925, 42, 50, 26, 0], [2, "1501", 899, 55, 50, 26, 0], [2, "1501", 925, 68, 50, 26, 0], [2, "1501", 951, 55, 50, 26, 0], [2, "1501", 977, 42, 50, 26, 0], [2, "1501", 953, 79, 50, 26, 0], [2, "1501", 979, 66, 50, 26, 0], [2, "1501", 1005, 53, 50, 26, 0], [2, "1501", 1031, 66, 50, 26, 0], [2, "1501", 1005, 79, 50, 26, 0], [2, "1501", 979, 92, 50, 26, 0], [2, "1501", 1005, 105, 50, 26, 0], [2, "1501", 1031, 92, 50, 26, 0], [2, "1501", 1057, 79, 50, 26, 0], [2, "1501", 725, -34, 50, 26, 0], [2, "1501", 751, -47, 50, 26, 0], [2, "1501", 777, -60, 50, 26, 0], [2, "1501", 803, -47, 50, 26, 0], [2, "1501", 777, -34, 50, 26, 0], [2, "1501", 751, -21, 50, 26, 0], [2, "1501", 777, -8, 50, 26, 0], [2, "1501", 803, -21, 50, 26, 0], [2, "1501", 829, -34, 50, 26, 0], [2, "1501", 884, 1, 50, 26, 0], [2, "1501", 910, -12, 50, 26, 0], [2, "1501", 936, -25, 50, 26, 0], [2, "1501", 962, -12, 50, 26, 0], [2, "1501", 936, 1, 50, 26, 0], [2, "1501", 910, 14, 50, 26, 0], [2, "1501", 936, 27, 50, 26, 0], [2, "1501", 962, 14, 50, 26, 0], [2, "1501", 988, 1, 50, 26, 0], [2, "3736", 294, 294, 76, 41, 0], [2, "3736", 246, 318, 76, 41, 0], [2, "3736", 324, 309, 76, 41, 0], [2, "3736", 276, 333, 76, 41, 0], [2, "3736", 353, 324, 76, 41, 0], [2, "3736", 305, 348, 76, 41, 0], [2, "3736", 199, 342, 76, 41, 0], [2, "3736", 151, 366, 76, 41, 0], [2, "3736", 229, 357, 76, 41, 0], [2, "3736", 181, 381, 76, 41, 0], [2, "3736", 258, 372, 76, 41, 0], [2, "3736", 210, 396, 76, 41, 0], [2, "3736", 131, 376, 76, 41, 0], [2, "3736", 83, 400, 76, 41, 0], [2, "3736", 161, 391, 76, 41, 0], [2, "3736", 113, 415, 76, 41, 0], [2, "3736", 190, 406, 76, 41, 0], [2, "3736", 142, 430, 76, 41, 0], [2, "3736", -53, 370, 76, 41, 0], [2, "3736", -47, 394, 76, 41, 0], [2, "3736", -18, 409, 76, 41, 0], [2, "3736", -51, 425, 76, 41, 0], [2, "3736", 11, 424, 76, 41, 0], [2, "3736", -37, 448, 76, 41, 0], [2, "3736", 49, 416, 76, 41, 0], [2, "3736", 1, 440, 76, 41, 0], [2, "3736", 79, 431, 76, 41, 0], [2, "3736", 31, 455, 76, 41, 0], [2, "3736", 108, 446, 76, 41, 0], [2, "3736", 60, 470, 76, 41, 0], [2, "3736", -46, 464, 76, 41, 0], [2, "3736", -16, 479, 76, 41, 0], [2, "3736", -43, 492, 76, 41, 0], [2, "3736", 13, 494, 76, 41, 0], [2, "3736", -35, 518, 76, 41, 0], [2, "3736", -40, 520, 76, 41, 0], [2, "163", 427, 110, 60, 33, 0], [2, "163", 380, 133, 60, 33, 0], [2, "163", 440, 117, 60, 33, 0], [2, "163", 393, 140, 60, 33, 0], [2, "163", 453, 339, 60, 33, 2], [2, "163", 503, 363, 60, 33, 2], [2, "43_1", 398, 121, 82, 58, 2], [2, "3609", 114, 85, 112, 59, 2], [2, "3609", 397, 379, 112, 59, 0], [2, "3611", 38, 161, 52, 30, 0]]}, {"type": 2, "data": [48, 48, 48, 48, 48, 48, 48, 48, 48, 78, 77, 78, 77, 78, 77, 78, 50, 51, 52, 73, 72, 48, 48, 48, 48, 48, 48, 69, 70, 67, 51, 52, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 55, 54, 55, 68, 48, 48, 48, 48, 70, 66, 67, 53, 54, 55, 51, 52, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 58, 68, 48, 48, 48, 48, 74, 54, 55, 56, 50, 51, 52, 50, 51, 52, 48, 48, 48, 48, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 60, 64, 48, 48, 48, 48, 49, 59, 50, 51, 52, 54, 55, 50, 51, 52, 51, 52, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 63, 48, 48, 48, 48, 48, 48, 62, 53, 54, 55, 57, 58, 53, 54, 55, 50, 51, 61, 60, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 48, 48, 77, 78, 77, 76, 75, 48, 48, 48, 48, 48, 48, 49, 45, 46, 58, 51, 52, 50, 51, 52, 61, 60, 64, 63, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 78, 48, 48, 48, 77, 54, 54, 73, 72, 76, 75, 48, 48, 48, 48, 48, 48, 49, 46, 54, 55, 53, 61, 60, 64, 63, 69, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 48, 48, 48, 48, 48, 48, 48, 45, 46, 54, 50, 73, 76, 75, 48, 48, 48, 48, 48, 48, 49, 45, 45, 64, 64, 63, 48, 48, 48, 48, 48, 48, 78, 77, 78, 77, 78, 77, 78, 77, 69, 69, 48, 48, 48, 48, 48, 48, 48, 49, 45, 46, 54, 73, 66, 66, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 78, 77, 48, 48, 48, 69, 69, 69, 69, 69, 48, 48, 48, 48, 48, 48, 48, 49, 45, 46, 54, 55, 73, 72, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 69, 69, 69, 69, 78, 77, 69, 69, 48, 48, 48, 48, 75, 48, 48, 48, 48, 49, 45, 46, 51, 50, 73, 72, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 69, 69, 69, 69, 78, 77, 78, 77, 78, 77, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 69, 74, 50, 50, 51, 55, 73, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 69, 48, 69, 69, 69, 69, 78, 77, 78, 77, 78, 77, 78, 77, 78, 48, 48, 48, 48, 48, 48, 48, 69, 70, 66, 67, 50, 53, 54, 51, 52, 73, 72, 76, 75, 48, 48, 48, 48, 75, 70, 66, 69, 69, 69, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 69, 48, 48, 48, 48, 69, 70, 66, 67, 50, 50, 61, 60, 60, 45, 46, 50, 50, 73, 72, 72, 48, 48, 70, 72, 71, 54, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 69, 69, 77, 78, 77, 66, 67, 50, 50, 48, 50, 64, 63, 48, 48, 49, 46, 57, 53, 54, 55, 48, 48, 62, 53, 56, 57, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 50, 50, 61, 60, 69, 70, 75, 75, 75, 48, 49, 48, 56, 57, 58, 48, 48, 49, 46, 57, 61, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 64, 63, 66, 67, 54, 55, 57, 75, 63, 48, 48, 48, 48, 48, 48, 48, 49, 45, 64, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 56, 61, 60, 48, 69, 48, 48, 48, 48, 48, 48, 48, 48, 48, 79, 80, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 63, 48, 48, 48, 69, 48, 48, 48, 48, 79, 79, 79, 81, 82, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 69, 69, 69, 69, 69, 79, 80, 79, 80, 79, 80, 79, 78, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 69, 77, 81, 82, 81, 82, 81, 82, 81, 78, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 69, 69, 69, 81, 82, 81, 82, 79, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 69, 69, 69, 69, 69, 81, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 78, 69, 69, 69, 69, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 78, 69, 69, 69, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 78, 69, 48, 69, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 69, 69, 48, 69, 69, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 69, 78, 69, 48, 48, 48, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 69, 69, 69, 69, 48, 48, 69, 48, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 69, 48, 48, 48, 48, 69, 48, 48, 48, 48, 48, 48, 69, 69, 48]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}