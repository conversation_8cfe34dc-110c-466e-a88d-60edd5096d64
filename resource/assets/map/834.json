{"mW": 1056, "mH": 768, "tW": 24, "tH": 24, "tiles": [["315_7", 0, 3, 3], ["1233", 0, 3, 2], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["444_4", 0, 2, 2], ["1234", 0, 3, 2], ["1234", 2, 3, 2], ["1234", 1, 3, 2], ["1234", 3, 3, 2], ["1237", 0, 3, 2], ["1237", 2, 3, 2], ["1237", 1, 3, 2], ["1237", 3, 3, 2], ["451", 0, 1, 1], ["450", 0, 1, 1], ["449", 0, 1, 1]], "layers": [{"type": 3, "obj": [[2, "1231", 857, 655, 114, 162, 0], [2, "1232", 974, 572, 100, 158, 0], [2, "1231", 920, 628, 114, 162, 0], [2, "1231", 3, 636, 114, 162, 0], [2, "1232", 459, 685, 100, 158, 0], [2, "1232", 78, 697, 100, 158, 0], [2, "1231", 315, 656, 114, 162, 0], [2, "1231", 641, 683, 114, 162, 0], [2, "1231", 735, 657, 114, 162, 0], [2, "1232", -48, 672, 100, 158, 0], [2, "1232", 252, 682, 100, 158, 0], [2, "1231", 151, 677, 114, 162, 0], [2, "1231", 375, 679, 114, 162, 0], [2, "1231", 969, 641, 114, 162, 0], [2, "1232", 799, 711, 100, 158, 0], [2, "1231", 505, 712, 114, 162, 0], [2, "1231", 569, 710, 114, 162, 0], [2, "1232", 609, 716, 100, 158, 0]]}, {"type": 4, "obj": [[2, "1231", 412, -71, 114, 162, 0], [2, "1231", 754, -51, 114, 162, 0], [2, "1231", 203, -41, 114, 162, 0], [2, "1231", -47, -26, 114, 162, 0], [2, "1232", 251, -6, 100, 158, 0], [2, "1232", 714, 0, 100, 158, 0], [2, "1231", 194, 3, 114, 162, 0], [2, "1231", 984, 17, 114, 162, 0], [2, "3095", 540, 142, 62, 42, 0], [2, "1231", -58, 67, 114, 162, 0], [2, "1232", 993, 98, 100, 158, 0], [2, "3095", 111, 224, 62, 42, 0], [2, "3095", 111, 224, 62, 42, 0], [2, "1231", 476, 137, 114, 162, 0], [2, "3095", 792, 259, 62, 42, 0], [2, "1232", 416, 149, 100, 158, 0], [2, "1232", 551, 158, 100, 158, 0], [2, "3095", 778, 277, 62, 42, 0], [2, "1231", 358, 172, 114, 162, 0], [2, "1232", 603, 198, 100, 158, 0], [2, "1232", 319, 252, 100, 158, 0], [2, "1231", 637, 249, 114, 162, 0], [2, "1231", 625, 298, 114, 162, 0], [2, "1231", 315, 314, 114, 162, 0], [2, "1232", 602, 345, 100, 158, 0], [2, "1232", 356, 353, 100, 158, 0], [2, "1231", 978, 359, 114, 162, 0], [2, "3095", 384, 533, 62, 42, 0], [2, "3095", 384, 533, 62, 42, 0], [2, "3095", 839, 547, 62, 42, 0], [2, "3095", 366, 548, 62, 42, 0], [2, "3095", 863, 562, 62, 42, 0], [2, "1232", 971, 457, 100, 158, 0], [2, "3095", 463, 586, 62, 42, 0], [2, "1231", 737, 489, 114, 162, 0], [2, "3095", 172, 612, 62, 42, 0], [2, "1232", -45, 503, 100, 158, 0], [2, "1232", 992, 507, 100, 158, 0], [2, "1455", 25, 592, 42, 76, 2], [2, "3095", 695, 637, 62, 42, 0], [2, "3095", 695, 637, 62, 42, 0], [2, "3095", 21, 638, 62, 42, 0], [2, "1232", 253, 522, 100, 158, 0], [2, "1232", 253, 522, 100, 158, 0], [2, "1231", 705, 545, 114, 162, 0], [2, "1231", 277, 561, 114, 162, 0], [2, "1231", -59, 573, 114, 162, 0]]}, {"type": 3, "obj": [[2, "439", 434, 452, 64, 42, 2], [2, "433", 536, 294, 62, 61, 0], [2, "433", 474, 293, 62, 61, 2], [2, "435", 595, 305, 50, 77, 0], [2, "435", 423, 314, 50, 77, 2], [2, "599", 617, 300, 40, 34, 0], [2, "328_2", 982, 481, 32, 29, 0], [2, "599", 605, 41, 40, 34, 0], [2, "328_2", 1017, 211, 32, 29, 0], [2, "328_2", 338, 18, 32, 29, 0], [2, "328_2", 11, 200, 32, 29, 0], [2, "328_2", 771, 105, 32, 29, 0], [2, "328_2", 710, 6, 32, 29, 0], [2, "328_2", 411, 326, 32, 29, 0], [2, "328_2", 284, -7, 32, 29, 0], [2, "1231", 193, -110, 114, 162, 0], [2, "327_2", 185, 733, 30, 22, 0], [2, "327_2", 915, 33, 30, 22, 0], [2, "1231", 51, -113, 114, 162, 0], [2, "328_2", 181, 30, 32, 29, 0], [2, "328_2", 510, 14, 32, 29, 0], [2, "327_2", 42, 69, 30, 22, 0], [2, "327_2", 127, 24, 30, 22, 0], [2, "599", 53, 34, 40, 34, 2], [2, "599", 90, 42, 40, 34, 2], [2, "328_2", 569, 282, 32, 29, 0], [2, "327_2", 691, 28, 30, 22, 0], [2, "420_2", 741, 612, 16, 13, 0], [2, "420_2", 322, 174, 16, 13, 0], [2, "422_2", 732, 606, 16, 14, 0], [2, "420_2", 566, 64, 16, 13, 0], [2, "422_2", 1037, 60, 16, 14, 0], [2, "422_2", 36, 153, 16, 14, 0], [2, "422_2", 287, 416, 16, 14, 0], [2, "422_2", 749, 124, 16, 14, 0], [2, "420_2", 404, 646, 16, 13, 0], [2, "420_2", 446, 479, 16, 13, 0], [2, "422_2", 435, 464, 16, 14, 0], [2, "1231", -17, -77, 114, 162, 0], [2, "420_2", 624, 198, 16, 13, 0], [2, "326", 990, 597, 18, 14, 0], [2, "326", 1044, 72, 18, 14, 0], [2, "326", 990, 63, 18, 14, 0], [2, "599", 871, 31, 40, 34, 0], [2, "21", 905, 84, 28, 24, 0], [2, "22", 913, 104, 62, 38, 0], [2, "420_2", 725, 455, 16, 13, 0], [2, "420_2", 997, 72, 16, 13, 0], [2, "420_2", 255, 283, 16, 13, 0], [2, "420_2", 774, 136, 16, 13, 0], [2, "328_2", 596, 159, 32, 29, 0], [2, "420_2", 629, 478, 16, 13, 0], [2, "21", 921, 618, 28, 24, 0], [2, "1231", 524, -102, 114, 162, 0], [2, "1231", 911, -115, 114, 162, 0], [2, "1231", 678, -112, 114, 162, 0], [2, "420_2", 159, 280, 16, 13, 0], [2, "328_2", 741, 56, 32, 29, 0], [2, "328_2", 657, 459, 32, 29, 0], [2, "328_2", 797, 641, 32, 29, 0], [2, "439", 587, 447, 64, 42, 0], [2, "437", 496, 486, 20, 19, 0], [2, "437", 517, 488, 20, 19, 0], [2, "437", 568, 484, 20, 19, 0], [2, "437", 551, 487, 20, 19, 0], [2, "437", 532, 489, 20, 19, 0], [2, "3095", 458, 475, 62, 42, 0], [2, "3094", 495, 485, 58, 34, 0], [2, "3095", 560, 488, 62, 42, 0], [2, "326", 519, 490, 18, 14, 0], [2, "362", 504, 381, 64, 42, 0], [2, "1454", 88, 96, 90, 55, 0], [2, "3253", 132, 104, 10, 14, 0], [2, "3257", 126, 111, 10, 13, 0], [2, "3258", 133, 114, 10, 13, 0], [2, "3253", 549, 281, 10, 14, 0], [2, "3254", 521, 279, 10, 13, 0], [2, "3255", 477, 284, 10, 13, 0], [2, "3256", 486, 287, 10, 13, 0], [2, "3259", 503, 292, 14, 11, 0], [2, "3258", 416, 489, 10, 13, 0], [2, "3253", 398, 487, 10, 14, 0], [2, "3255", 640, 482, 10, 13, 0], [2, "3257", 698, 440, 10, 13, 0], [2, "1231", 117, -120, 114, 162, 0], [2, "1231", 266, -135, 114, 162, 0], [2, "1232", 324, -100, 100, 158, 0], [2, "1232", 372, -92, 100, 158, 0], [2, "1231", 453, -113, 114, 162, 0], [2, "1232", 742, -94, 100, 158, 0], [2, "1232", 817, -115, 100, 158, 0], [2, "1232", 863, -91, 100, 158, 0], [2, "1231", 966, -76, 114, 162, 0], [2, "1231", 603, -114, 114, 162, 0], [2, "3258", 944, 132, 10, 13, 0], [2, "3094", 922, 331, 58, 34, 0], [2, "3256", 936, 134, 10, 13, 0], [2, "3259", 936, 104, 14, 11, 0], [2, "3255", 908, 97, 10, 13, 0], [2, "3253", 967, 112, 10, 14, 0], [2, "3254", 955, 33, 10, 13, 0], [2, "3258", 638, 34, 10, 13, 0], [2, "3255", 651, 39, 10, 13, 0], [2, "3256", 410, 40, 10, 13, 0], [2, "3257", 406, 46, 10, 13, 0], [2, "3259", 380, 48, 14, 11, 0], [2, "3094", 540, 486, 58, 34, 0], [2, "3094", 567, 484, 58, 34, 0], [2, "1457", 592, 474, 22, 30, 0], [2, "1457", 548, 494, 22, 30, 0], [2, "1456", 451, 470, 24, 32, 0], [2, "3258", 458, 83, 10, 13, 0], [2, "3259", 527, 392, 14, 11, 0], [2, "3254", 528, 380, 10, 13, 0], [2, "3253", 521, 383, 10, 14, 0], [2, "3257", 519, 392, 10, 13, 0], [2, "3256", 540, 390, 10, 13, 0], [2, "3254", 22, 207, 10, 13, 0], [2, "3253", 33, 212, 10, 14, 0], [2, "3255", 238, 145, 10, 13, 0], [2, "3258", 192, 33, 10, 13, 0], [2, "3127", 615, 455, 30, 30, 0], [2, "3127", 437, 69, 30, 30, 0], [2, "3127", 302, 109, 30, 30, 0], [2, "3127", 780, 112, 30, 30, 0], [2, "3127", 840, -4, 30, 30, 0], [2, "3127", 998, 187, 30, 30, 0], [2, "3127", 409, 458, 30, 30, 0], [2, "3127", 493, 264, 30, 30, 0], [2, "3127", 604, 282, 30, 30, 0], [2, "3127", 426, 300, 30, 30, 0], [2, "1456", 640, 329, 24, 32, 0], [2, "1457", 431, 469, 22, 30, 0], [2, "1457", 386, 456, 22, 30, 0], [2, "1457", 470, 257, 22, 30, 0], [2, "1457", 577, 269, 22, 30, 0], [2, "1457", 713, 630, 22, 30, 0], [2, "1457", 787, 271, 22, 30, 0], [2, "1457", 884, 552, 22, 30, 0], [2, "1456", 945, 320, 24, 32, 0], [2, "3127", 586, 732, 30, 30, 0], [2, "3127", 444, 692, 30, 30, 0], [2, "3127", 802, 630, 30, 30, 0], [2, "1457", 824, 708, 22, 30, 0], [2, "1457", 936, 619, 22, 30, 0], [2, "326", 860, 581, 18, 14, 0], [2, "326", 281, 670, 18, 14, 0], [2, "1456", 461, 722, 24, 32, 0], [2, "1456", 750, 709, 24, 32, 0], [2, "3254", 476, 593, 10, 13, 0], [2, "3253", 484, 593, 10, 14, 0], [2, "3257", 699, 655, 10, 13, 0], [2, "3255", 943, 687, 10, 13, 0], [2, "3095", 830, 682, 62, 42, 0], [2, "3094", 867, 692, 58, 34, 0], [2, "3095", 586, 707, 62, 42, 0], [2, "3094", 912, 693, 58, 34, 0], [2, "3094", 939, 691, 58, 34, 0], [2, "1457", 574, 713, 22, 30, 0], [2, "1457", 1003, 674, 22, 30, 0], [2, "1457", 994, 688, 22, 30, 0], [2, "1457", 728, 735, 22, 30, 0], [2, "1457", 746, 739, 22, 30, 0], [2, "1457", 396, 698, 22, 30, 0], [2, "1457", 379, 662, 22, 30, 0], [2, "1457", 391, 677, 22, 30, 0], [2, "1457", 407, 684, 22, 30, 0], [2, "1457", 827, 687, 22, 30, 0], [2, "3257", 897, 676, 10, 13, 0], [2, "3257", 969, 694, 10, 13, 0], [2, "3253", 895, 683, 10, 14, 0], [2, "3253", 837, 676, 10, 14, 0], [2, "3254", 1017, 677, 10, 13, 0], [2, "3254", 635, 716, 10, 13, 0], [2, "3254", 468, 710, 10, 13, 0], [2, "3257", 737, 720, 10, 13, 0], [2, "3255", 888, 693, 10, 13, 0], [2, "3255", 402, 669, 10, 13, 0], [2, "3255", 744, 710, 10, 13, 0], [2, "3258", 403, 686, 10, 13, 0], [2, "3258", 641, 725, 10, 13, 0], [2, "3258", 725, 724, 10, 13, 0], [2, "3259", 916, 630, 14, 11, 0], [2, "3259", 384, 553, 14, 11, 0], [2, "3259", 212, 648, 14, 11, 0], [2, "420_2", 23, 656, 16, 13, 0], [2, "3127", 205, 742, 30, 30, 0], [2, "3127", 63, 702, 30, 30, 0], [2, "1456", 80, 732, 24, 32, 0], [2, "3095", 103, 704, 62, 42, 0], [2, "3094", 140, 714, 58, 34, 0], [2, "3095", 205, 717, 62, 42, 0], [2, "3094", 185, 715, 58, 34, 0], [2, "3094", 212, 713, 58, 34, 0], [2, "1457", 193, 723, 22, 30, 0], [2, "1457", 267, 710, 22, 30, 0], [2, "1457", 15, 708, 22, 30, 0], [2, "1457", 10, 687, 22, 30, 0], [2, "1457", 26, 694, 22, 30, 0], [2, "1457", 100, 709, 22, 30, 0], [2, "3257", 170, 698, 10, 13, 0], [2, "3257", 242, 716, 10, 13, 0], [2, "3253", 168, 705, 10, 14, 0], [2, "3253", 110, 698, 10, 14, 0], [2, "3254", 254, 726, 10, 13, 0], [2, "3254", 87, 720, 10, 13, 0], [2, "3255", 161, 715, 10, 13, 0], [2, "3255", 21, 679, 10, 13, 0], [2, "3258", 22, 696, 10, 13, 0], [2, "3258", 260, 735, 10, 13, 0], [2, "3127", 586, 732, 30, 30, 0], [2, "3127", 444, 692, 30, 30, 0], [2, "1456", 461, 722, 24, 32, 0], [2, "3095", 830, 682, 62, 42, 0], [2, "3094", 867, 692, 58, 34, 0], [2, "3095", 586, 707, 62, 42, 0], [2, "3094", 912, 693, 58, 34, 0], [2, "3094", 939, 691, 58, 34, 0], [2, "1457", 574, 713, 22, 30, 0], [2, "1457", 407, 684, 22, 30, 0], [2, "1457", 827, 687, 22, 30, 0], [2, "3257", 897, 676, 10, 13, 0], [2, "3257", 969, 694, 10, 13, 0], [2, "3253", 895, 683, 10, 14, 0], [2, "3253", 837, 676, 10, 14, 0], [2, "3254", 635, 716, 10, 13, 0], [2, "3254", 468, 710, 10, 13, 0], [2, "3255", 888, 693, 10, 13, 0], [2, "1457", 868, 712, 22, 30, 0], [2, "3127", 674, 62, 30, 30, 0], [2, "3127", 532, 22, 30, 30, 0], [2, "1456", 549, 52, 24, 32, 0], [2, "3095", 674, 37, 62, 42, 0], [2, "1457", 662, 43, 22, 30, 0], [2, "3254", 723, 46, 10, 13, 0], [2, "3254", 556, 40, 10, 13, 0], [2, "3258", 729, 55, 10, 13, 0], [2, "3127", 674, 62, 30, 30, 0], [2, "3127", 532, 22, 30, 30, 0], [2, "1456", 549, 52, 24, 32, 0], [2, "3095", 674, 37, 62, 42, 0], [2, "1457", 662, 43, 22, 30, 0], [2, "3254", 723, 46, 10, 13, 0], [2, "3254", 556, 40, 10, 13, 0], [2, "1457", 578, 46, 22, 30, 0], [2, "1457", 513, 40, 22, 30, 0], [2, "1457", 386, 22, 22, 30, 0], [2, "1457", 208, 610, 22, 30, 0], [2, "1457", 115, 224, 22, 30, 0], [2, "1457", 136, 278, 22, 30, 0], [2, "1457", 69, 364, 22, 30, 0], [2, "3255", 65, 381, 10, 13, 0], [2, "3253", 72, 387, 10, 14, 0], [2, "3253", 961, 349, 10, 14, 0], [2, "3259", 924, 351, 14, 11, 0], [2, "3254", 810, 459, 10, 13, 0], [2, "326", 98, 593, 18, 14, 0], [2, "1456", 59, 622, 24, 32, 0], [2, "1457", 974, 506, 22, 30, 0], [2, "3127", 978, 548, 30, 30, 0], [2, "3127", 260, 539, 30, 30, 0], [2, "3127", 76, 382, 30, 30, 0], [2, "599", 225, 496, 40, 34, 0], [2, "3127", 570, 136, 30, 30, 0], [2, "3253", 593, 153, 10, 14, 0], [2, "3259", 260, 142, 14, 11, 0], [2, "3258", 625, 484, 10, 13, 0], [2, "3258", 597, 496, 10, 13, 0], [2, "3258", 663, 447, 10, 13, 0], [2, "3258", 699, 428, 10, 13, 0], [2, "3258", 661, 302, 10, 13, 0], [2, "3258", 560, 285, 10, 13, 0], [2, "3258", 472, 297, 10, 13, 0], [2, "3258", 359, 453, 10, 13, 0], [2, "3253", 337, 344, 10, 14, 0], [2, "3253", 482, 296, 10, 14, 0], [2, "3253", 448, 310, 10, 14, 0], [2, "3253", 585, 299, 10, 14, 0], [2, "3253", 633, 327, 10, 14, 0], [2, "3253", 641, 468, 10, 14, 0], [2, "3253", 608, 496, 10, 14, 0], [2, "3253", 534, 501, 10, 14, 0], [2, "3257", 430, 486, 10, 13, 0], [2, "3257", 376, 468, 10, 13, 0], [2, "3257", 423, 349, 10, 13, 0], [2, "3257", 454, 270, 10, 13, 0], [2, "3257", 567, 275, 10, 13, 0], [2, "3257", 616, 303, 10, 13, 0], [2, "3257", 494, 504, 10, 13, 0], [2, "3255", 569, 502, 10, 13, 0], [2, "3255", 475, 482, 10, 13, 0], [2, "3255", 518, 288, 10, 13, 0], [2, "3255", 497, 294, 10, 13, 0], [2, "3255", 552, 271, 10, 13, 0], [2, "3255", 440, 324, 10, 13, 0], [2, "3255", 628, 335, 10, 13, 0], [2, "3255", 442, 489, 10, 13, 0], [2, "3254", 428, 491, 10, 13, 0], [2, "3254", 388, 482, 10, 13, 0], [2, "3254", 618, 488, 10, 13, 0], [2, "3254", 545, 501, 10, 13, 0], [2, "3254", 673, 466, 10, 13, 0], [2, "3254", 562, 294, 10, 13, 0], [2, "3254", 609, 309, 10, 13, 0], [2, "3254", 460, 309, 10, 13, 0], [2, "3254", 469, 303, 10, 13, 0], [2, "3254", 422, 333, 10, 13, 0], [2, "3256", 455, 498, 10, 13, 0], [2, "3256", 978, 546, 10, 13, 0], [2, "3256", 961, 647, 10, 13, 0], [2, "3256", 816, 690, 10, 13, 0], [2, "3256", 419, 568, 10, 13, 0], [2, "3256", 91, 696, 10, 13, 0], [2, "3256", 32, 115, 10, 13, 0], [2, "3256", 90, 31, 10, 13, 0], [2, "3256", 213, 42, 10, 13, 0], [2, "3256", 284, 132, 10, 13, 0], [2, "3256", 343, 48, 10, 13, 0], [2, "3256", 408, 24, 10, 13, 0], [2, "3256", 481, 70, 10, 13, 0], [2, "3256", 600, 49, 10, 13, 0], [2, "3256", 682, 38, 10, 13, 0], [2, "3256", 827, 93, 10, 13, 0], [2, "3256", 943, 30, 10, 13, 0], [2, "3255", 201, 48, 10, 13, 0], [2, "3255", 158, 27, 10, 13, 0], [2, "3255", 82, 62, 10, 13, 0], [2, "3255", 413, 49, 10, 13, 0], [2, "3255", 492, 66, 10, 13, 0], [2, "3255", 599, 63, 10, 13, 0], [2, "3255", 856, 27, 10, 13, 0], [2, "3255", 974, 37, 10, 13, 0], [2, "3255", 1010, 69, 10, 13, 0], [2, "3255", 1031, 234, 10, 13, 0], [2, "3255", 108, 580, 10, 13, 0], [2, "3254", 617, 69, 10, 13, 0], [2, "3254", 756, 132, 10, 13, 0], [2, "3254", 793, 100, 10, 13, 0], [2, "3254", 897, 18, 10, 13, 0], [2, "3254", 964, 41, 10, 13, 0], [2, "3254", 168, 35, 10, 13, 0], [2, "3254", 70, 65, 10, 13, 0], [2, "3254", 38, 114, 10, 13, 0], [2, "3254", 280, 140, 10, 13, 0], [2, "3258", 393, 48, 10, 13, 0], [2, "3258", 350, 20, 10, 13, 0], [2, "3258", 361, 34, 10, 13, 0], [2, "3258", 125, 42, 10, 13, 0], [2, "3258", 64, 86, 10, 13, 0], [2, "3258", 13, 224, 10, 13, 0], [2, "3258", 267, 152, 10, 13, 0], [2, "3258", 834, 293, 10, 13, 0], [2, "3258", 1016, 208, 10, 13, 0], [2, "3258", 974, 53, 10, 13, 0], [2, "3258", 849, 35, 10, 13, 0], [2, "3258", 835, 86, 10, 13, 0], [2, "3259", 577, 72, 14, 11, 0], [2, "3259", 895, 60, 14, 11, 0], [2, "3259", 941, 44, 14, 11, 0], [2, "3259", 544, 48, 14, 11, 0], [2, "3259", 430, 89, 14, 11, 0], [2, "3259", 140, 44, 14, 11, 0], [2, "3259", 210, 60, 14, 11, 0], [2, "3259", 21, 223, 14, 11, 0], [2, "3253", 868, 32, 10, 14, 0], [2, "3253", 951, 40, 10, 14, 0], [2, "3253", 834, 95, 10, 14, 0], [2, "3253", 778, 139, 10, 14, 0], [2, "3253", 601, 33, 10, 14, 0], [2, "3253", 353, 40, 10, 14, 0], [2, "3253", 155, 38, 10, 14, 0], [2, "3253", 270, 139, 10, 14, 0], [2, "3254", 919, 340, 10, 13, 0], [2, "3254", 829, 399, 10, 13, 0], [2, "3255", 236, 523, 10, 13, 0], [2, "3259", 921, 269, 14, 11, 0], [2, "3253", 247, 369, 10, 14, 0], [2, "3253", 183, 449, 10, 14, 0], [2, "3253", 841, 193, 10, 14, 0], [2, "3253", 150, 295, 10, 14, 0], [2, "3257", 827, 198, 10, 13, 0], [2, "3257", 800, 460, 10, 13, 0], [2, "3257", 114, 241, 10, 13, 0], [2, "3257", 237, 368, 10, 13, 0], [2, "3257", 179, 458, 10, 13, 0], [2, "326", 296, 425, 18, 14, 0], [2, "3258", 832, 194, 10, 13, 0], [2, "3258", 192, 452, 10, 13, 0], [2, "3259", 819, 469, 14, 11, 0], [2, "3259", 837, 408, 14, 11, 0], [2, "3258", 852, 401, 10, 13, 0], [2, "3258", 970, 409, 10, 13, 0], [2, "3258", 134, 228, 10, 13, 0], [2, "3255", 956, 401, 10, 13, 0], [2, "3255", 910, 264, 10, 13, 0], [2, "3256", 919, 556, 10, 13, 0], [2, "3256", 833, 202, 10, 13, 0], [2, "3256", 807, 468, 10, 13, 0], [2, "3256", 187, 461, 10, 13, 0], [2, "3256", 117, 584, 10, 13, 0], [2, "3256", 131, 290, 10, 13, 0], [2, "3256", 241, 373, 10, 13, 0], [2, "3256", 227, 519, 10, 13, 0], [2, "3256", 918, 260, 10, 13, 0], [2, "1457", 836, 386, 22, 30, 0], [2, "1457", 955, 397, 22, 30, 0], [2, "1457", 914, 236, 22, 30, 0], [2, "1456", 926, 258, 24, 32, 0], [2, "3258", 815, 462, 10, 13, 0], [2, "599", -4, 324, 40, 34, 0]]}, {"type": 2, "data": [49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 55, 95, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, -1, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 98, 99, 52, 52, 52, 52, 52, 56, 49, 49, 49, 107, 100, 101, 55, 50, 52, 46, 52, 46, 56, 55, 49, 50, 46, 46, 46, 52, 56, 55, 55, 55, 49, 50, 52, 56, 55, 49, 49, 50, 46, 46, 46, 46, 46, 46, -1, -1, -1, 103, -1, 53, 52, 51, 56, 104, 55, 98, 50, 51, -1, -1, -1, -1, 53, 52, 52, 47, -1, -1, -1, -1, 53, 56, 55, 55, 50, 47, -1, 53, 52, 46, 46, 47, -1, -1, -1, -1, -1, 33, 34, 40, 35, 93, 86, 86, 86, 87, -1, -1, 55, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 52, 47, 69, 62, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 52, 51, 100, 89, 89, 89, 90, 92, -1, 52, 51, -1, -1, 61, 62, 62, 62, 68, 67, -1, -1, -1, -1, -1, 61, 62, 67, -1, -1, 69, 72, 77, 77, 77, 68, 63, -1, -1, -1, -1, -1, -1, -1, 61, 67, 93, 96, 95, 89, 89, 89, 95, 95, 94, -1, -1, -1, 64, 65, 71, 71, 71, 66, 67, -1, -1, -1, -1, 76, 77, 66, 62, 68, 72, 77, 77, 77, 77, 65, 66, 63, -1, -1, -1, -1, 61, 62, 72, 82, 100, 101, 89, 89, 89, 101, 95, 95, -1, -1, -1, -1, 81, 84, 83, 83, 78, 80, 79, -1, -1, -1, -1, 73, 74, 74, 80, 80, 84, 83, 77, 77, 77, 65, 77, 82, -1, -1, -1, -1, 76, 77, 83, 82, 97, 98, 98, 108, 107, 101, 101, 101, 101, 102, -1, -1, 69, 72, 83, 78, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 83, 83, 77, 77, 65, 77, 82, -1, -1, -1, -1, 73, 74, 80, 75, -1, -1, -1, 105, 104, 108, 107, 101, 98, 99, -1, 61, 72, 71, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 95, 95, 95, 102, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 108, 101, -1, -1, -1, 64, 71, 71, 71, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 86, 86, 86, 86, 95, 95, 106, -1, -1, -1, -1, -1, -1, 61, 62, 68, 67, -1, -1, -1, -1, -1, -1, 100, 101, 87, -1, -1, 73, 84, 71, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 89, 89, 89, 89, 89, 89, 102, 89, 89, -1, -1, -1, -1, -1, 76, 77, 83, 82, -1, -1, -1, -1, -1, -1, 97, 98, 94, -1, -1, -1, 73, 84, 78, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, -1, -1, -1, 61, 72, 65, 78, 79, -1, -1, -1, -1, -1, -1, -1, -1, 94, -1, -1, -1, -1, 73, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 89, 89, -1, -1, -1, -1, -1, -1, 89, 89, 89, -1, -1, -1, 81, 74, 80, 79, -1, -1, -1, 69, 68, 68, 67, -1, -1, 90, 92, 91, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 72, 77, 77, 70, -1, -1, 95, 95, 94, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 89, -1, -1, -1, -1, -1, -1, -1, -1, 76, 77, 77, 77, 82, -1, -1, 95, 95, 90, 91, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, 89, -1, -1, -1, 93, 92, -1, -1, -1, 61, 62, 89, 89, 89, 94, -1, -1, -1, -1, -1, -1, 76, 77, 77, 78, 75, -1, -1, 101, 102, 104, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 89, -1, -1, -1, 105, 98, 104, 103, 61, 72, 84, 67, 89, 89, 94, -1, -1, -1, -1, -1, -1, 81, 80, 80, 79, -1, -1, -1, 102, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 88, 89, 89, 89, -1, -1, -1, -1, -1, -1, 64, 71, 72, 89, 89, 89, 94, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 106, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 100, 101, 89, 89, 89, -1, -1, -1, -1, -1, 76, 77, 89, 89, 101, 102, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 55, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, 108, 107, 89, 89, 102, -1, -1, -1, -1, 73, 74, 98, 98, 98, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 93, 48, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 63, 69, 68, 63, 105, 104, 98, 98, 99, 69, 68, 63, -1, -1, -1, -1, -1, -1, -1, -1, 61, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, 53, 46, 33, -1, -1, -1, -1, -1, -1, -1, -1, 64, 65, 66, 72, 71, 66, 62, 62, 62, 62, 62, 72, 65, 66, 67, -1, -1, -1, -1, -1, -1, -1, 64, 71, 70, -1, -1, 69, 68, 67, -1, 85, 86, 86, 86, 53, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 84, 77, 77, 71, 71, 65, 77, 83, 83, 71, 77, 83, 82, -1, -1, -1, -1, -1, -1, -1, 76, 83, 70, 61, 62, 72, 71, 66, 67, 100, 95, 101, 101, 86, 87, -1, 93, 86, 87, -1, -1, -1, -1, -1, 73, 74, 74, 84, 77, 78, 74, 84, 83, 83, 83, 78, 79, -1, -1, -1, -1, -1, -1, -1, 73, 74, 75, 64, 71, 71, 71, 71, 70, 105, 108, 107, 101, 89, 90, 92, 96, 95, 106, -1, -1, -1, -1, -1, -1, -1, -1, 73, 80, 79, -1, 81, 84, 77, 83, 82, -1, -1, -1, -1, 61, 62, 63, -1, -1, -1, -1, 73, 74, 84, 83, 71, 66, 67, 105, 104, 101, 95, 95, 95, 95, 95, 94, -1, 61, 62, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 80, 79, -1, -1, -1, -1, 64, 65, 66, 68, 67, 93, 92, 92, 91, 81, 84, 71, 71, 82, -1, 100, 101, 95, 101, 107, 102, 104, 103, -1, 76, 77, 70, -1, 93, 92, -1, 86, 86, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 72, 71, 71, 71, 70, 96, 95, 95, 94, -1, 73, 74, 74, 79, -1, 97, 98, 95, 95, 95, 86, 87, -1, -1, 73, 74, 79, 93, 96, 95, 89, 89, 89, 90, 86, 87, -1, -1, 85, 86, 87, -1, -1, 76, 77, 77, 78, 80, 79, 95, 95, 95, 90, 87, -1, -1, -1, -1, -1, -1, 55, -1, 40, -1, 89, 90, -1, -1, -1, -1, 85, 96, 95, 89, 89, 89, -1, 89, 89, 90, 85, 86, 96, 95, 94, -1, -1, 73, 74, 74, 75, -1, 93, 95, 95, 95, 95, 90, 86, 87, -1, -1, 93, 92, 55, -1, 95, 90, 89, 89, 89, 92, 92, 92, 92, 89, 89, 89, -1, -1, -1, 89, 89, 89, 96, 89, 89, 89, 90, 86, 86, 86, 86, 86, 86, 86, 96, 95, 95, 89, 89, 95, 95, 95, 95, -1, 96, 95, -1, -1, 49, -1, 90, 89, 89, 89, 89, 89, 95, 89, 89, 90, 91, -1, -1, 85, 89, 89, 89, 89, 102, 108, 89, 89, 89, 89, 89, 89, 89, 89, 95, 95, 107, 95, 95, 95, 95, 95, 101, 101, 101, 101, -1]}, {"type": 2, "data": [57, 58, 57, 57, 58, 57, 58, 25, 25, 57, 57, 57, 58, 57, 58, 57, 58, 58, 57, 58, 19, 19, 57, 58, 57, 57, 57, 57, 58, 58, 57, 57, 58, 57, 58, 57, 58, 57, 58, 59, 60, 59, 60, 57, 59, 57, 57, 58, 60, 59, 60, 57, 58, 57, 58, 59, 60, 57, 58, 59, 60, 60, 59, 60, 19, 19, 59, 60, 57, 58, 57, 58, 57, 58, 57, 58, 60, 57, 58, 59, 60, 59, 60, 57, 58, 60, 59, 59, 25, 59, 59, 60, 58, 25, 25, 59, 60, 59, 60, 57, 58, 59, 60, 57, 58, 59, 60, 57, 58, 57, 57, 58, 59, 60, 57, 58, 57, 58, 57, 58, 59, 59, 60, 13, 57, 59, 60, 59, 60, 57, 58, 31, 57, 58, 60, 57, 58, 31, 57, 58, 31, 57, 58, 59, 60, 58, 58, 59, 57, 58, 59, 59, 60, 59, 59, 60, 59, 59, 59, 57, 58, 57, 57, 58, 57, 57, 58, 57, 58, 57, 58, 57, 58, 59, 60, 57, 57, 58, 58, 59, 60, 31, 59, 60, 57, 59, 57, 58, 57, 58, 57, 58, 59, 60, 25, 57, 57, 58, 59, 60, 57, 58, 25, 59, 60, 59, 59, 60, 59, 59, 60, 59, 57, 58, 57, 58, 57, 57, 58, 59, 59, 57, 58, 57, 58, 57, 59, 57, 58, 58, 59, 60, 59, 60, 59, 60, 58, 58, 57, 58, 59, 60, 57, 58, 59, 60, 19, 57, 58, 60, 59, 60, 58, 59, 60, 59, 59, 60, 59, 57, 58, 59, 60, 57, 57, 59, 60, 57, 58, 57, 58, 59, 60, 60, 58, 59, 59, 60, 60, 59, 60, 60, 59, 60, 58, 59, 59, 60, 57, 58, 19, 59, 60, 59, 60, 59, 60, 59, 60, 57, 58, 57, 58, 59, 60, 57, 57, 58, 59, 57, 58, 59, 60, 59, 60, 59, 59, 59, 60, 57, 58, 57, 58, 57, 58, 57, 58, 58, 60, 57, 58, 60, 59, 60, 58, 59, 57, 57, 58, 57, 58, 59, 60, 59, 60, 59, 60, 59, 57, 58, 59, 60, 57, 59, 60, 59, 57, 58, 57, 57, 58, 57, 59, 59, 60, 59, 60, 59, 60, 59, 60, 60, 58, 59, 60, 58, 25, 57, 58, 25, 57, 59, 60, 59, 60, 59, 60, 60, 57, 58, 57, 58, 59, 60, 58, 58, 59, 57, 58, 57, 59, 60, 59, 59, 60, 57, 58, 57, 58, 57, 58, 57, 58, 58, 57, 58, 58, 57, 58, 57, 58, 59, 57, 58, 57, 59, 60, 60, 57, 58, 58, 58, 59, 60, 59, 60, 60, 59, 60, 57, 57, 59, 60, 57, 58, 57, 58, 58, 57, 59, 60, 59, 60, 59, 60, 59, 60, 60, 59, 60, 57, 59, 60, 59, 57, 57, 58, 57, 57, 57, 58, 58, 59, 60, 60, 60, 57, 57, 58, 57, 58, 57, 58, 59, 59, 60, 57, 59, 60, 59, 60, 60, 59, 60, 59, 59, 57, 58, 59, 57, 57, 58, 58, 59, 59, 57, 57, 58, 59, 59, 57, 58, 59, 59, 60, 57, 58, 58, 13, 13, 57, 58, 58, 59, 60, 57, 57, 58, 57, 58, 59, 60, 59, 60, 59, 60, 57, 58, 25, 57, 59, 60, 57, 59, 59, 60, 60, 58, 57, 59, 59, 60, 57, 58, 59, 60, 57, 58, 58, 59, 60, 60, 19, 13, 59, 60, 60, 57, 58, 59, 59, 60, 59, 60, 57, 59, 57, 57, 58, 58, 59, 60, 57, 59, 60, 57, 59, 60, 57, 58, 57, 58, 111, 111, 111, 111, 111, 57, 58, 58, 59, 57, 57, 58, 58, 60, 19, 13, 57, 58, 57, 58, 60, 58, 57, 58, 59, 60, 59, 59, 59, 57, 58, 58, 59, 57, 58, 57, 58, 59, 60, 13, 59, 60, 59, 111, 111, 111, 111, 111, 111, 111, 60, 57, 58, 59, 59, 60, 60, 58, 25, 13, 59, 60, 59, 60, 58, 60, 58, 60, 57, 58, 57, 57, 59, 59, 60, 58, 25, 59, 60, 57, 58, 57, 58, 59, 57, 58, 111, 111, 111, 111, 111, 111, 111, 111, 111, 59, 60, 60, 57, 58, 60, 57, 58, 58, 57, 57, 58, 60, 60, 59, 60, 58, 57, 58, 59, 59, 60, 57, 58, 58, 57, 58, 57, 58, 60, 59, 60, 57, 59, 60, 111, 111, 111, 111, 111, 111, 111, 111, 111, 59, 60, 13, 59, 60, 58, 59, 60, 60, 59, 59, 60, 58, 58, 57, 58, 60, 59, 60, 25, 57, 58, 59, 60, 60, 59, 60, 59, 60, 57, 57, 58, 57, 58, 57, 111, 111, 111, 111, 111, 111, 111, 111, 111, 58, 58, 57, 59, 60, 60, 57, 58, 58, 57, 57, 58, 57, 58, 57, 58, 58, 59, 57, 58, 57, 58, 57, 58, 57, 57, 58, 59, 60, 59, 57, 58, 59, 60, 59, 111, 111, 111, 111, 111, 111, 111, 111, 111, 60, 60, 57, 57, 58, 58, 59, 60, 60, 59, 59, 60, 59, 60, 59, 60, 57, 57, 58, 60, 59, 60, 59, 60, 59, 59, 57, 58, 60, 60, 59, 60, 57, 58, 59, 59, 111, 111, 111, 111, 111, 111, 111, 57, 58, 59, 57, 58, 60, 60, 58, 58, 57, 58, 57, 57, 58, 59, 57, 58, 59, 59, 60, 57, 58, 58, 58, 57, 58, 57, 59, 60, 58, 58, 59, 57, 57, 58, 57, 58, 59, 111, 111, 111, 111, 111, 57, 59, 60, 57, 59, 60, 57, 59, 60, 57, 59, 60, 59, 59, 60, 57, 58, 60, 57, 57, 57, 59, 60, 57, 58, 59, 60, 57, 58, 57, 58, 57, 58, 59, 59, 60, 59, 60, 58, 59, 60, 59, 60, 57, 59, 60, 57, 58, 57, 58, 58, 59, 57, 59, 60, 58, 57, 58, 57, 59, 60, 58, 59, 59, 57, 58, 57, 59, 60, 58, 59, 59, 60, 59, 60, 59, 60, 58, 57, 59, 57, 58, 57, 58, 60, 58, 57, 58, 57, 58, 59, 60, 59, 60, 60, 57, 58, 58, 59, 60, 59, 60, 59, 60, 59, 60, 58, 59, 59, 60, 59, 60, 59, 60, 60, 57, 59, 60, 60, 59, 60, 60, 59, 60, 59, 60, 59, 60, 58, 58, 59, 60, 59, 60, 58, 58, 59, 60, 57, 59, 60, 60, 25, 57, 58, 58, 59, 60, 58, 59, 60, 57, 57, 58, 57, 59, 57, 58, 58, 59, 60, 60, 57, 58, 59, 59, 59, 59, 60, 59, 60, 58, 60, 60, 57, 58, 58, 58, 60, 60, 57, 58, 57, 58, 58, 58, 25, 59, 60, 57, 58, 59, 60, 57, 58, 59, 59, 60, 59, 60, 59, 60, 60, 13, 19, 57, 59, 57, 58, 58, 57, 57, 58, 57, 58, 60, 58, 58, 59, 60, 60, 60, 57, 58, 59, 60, 59, 60, 60, 60, 13, 57, 58, 59, 57, 58, 58, 59, 60, 57, 58, 58, 58, 58, 57, 58, 58, 57, 58, 57, 58, 59, 60, 60, 59, 59, 60, 59, 60, 58, 58, 60, 57, 58, 58, 57, 58, 57, 58, 57, 59, 57, 58, 58, 13, 59, 60, 57, 59, 60, 57, 57, 58, 57, 58, 60, 60, 60, 59, 60, 57, 57, 58, 59, 60, 59, 60, 57, 57, 58, 57, 58, 58, 60, 60, 58, 59, 60, 60, 59, 60, 59, 60, 59, 57, 59, 60, 60, 13, 57, 58, 59, 57, 58, 59, 59, 60, 59, 60, 57, 57, 58, 58, 57, 59, 59, 60, 57, 58, 57, 57, 58, 59, 60, 59, 60, 60, 58, 57, 58, 57, 58, 57, 57, 58, 57, 58, 57, 59, 60, 59, 60, 57, 59, 60, 57, 57, 58, 57, 58, 59, 57, 58, 57, 58, 60, 57, 58, 57, 59, 57, 59, 60, 59, 59, 60, 58, 57, 58, 58, 59, 60, 59, 60, 59, 60, 59, 59, 60, 59, 60, 59, 57, 58, 57, 58, 57, 57, 58, 59, 59, 60, 59, 60, 57, 59, 60, 59, 60, 57, 57, 58, 57, 57, 57, 58, 58, 59, 60, 59, 57, 59, 60, 60, 57, 58, 57, 58, 60, 57, 58, 59, 60, 60, 59, 60, 59, 60, 59, 60, 59, 59, 60, 57, 59, 60, 58, 57, 59, 57, 58, 57, 58, 59, 59, 60, 59, 59, 59, 60, 60, 59, 60, 59, 59, 60, 58, 60, 59, 60, 59, 57, 58, 59, 60, 58, 59, 59, 60, 59, 60, 60, 60, 59, 60, 57, 58, 59, 60, 59, 60, 59, 60]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}