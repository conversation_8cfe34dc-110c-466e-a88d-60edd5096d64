{"mW": 1128, "mH": 960, "tW": 24, "tH": 24, "tiles": [["302_5", 0, 2, 2], ["1246", 0, 3, 2], ["1246", 2, 3, 2], ["1246", 1, 3, 2], ["1246", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["497_1", 0, 2, 1], ["497_5", 0, 2, 1], ["1316", 0, 4, 2]], "layers": [{"type": 3, "obj": [[2, "1141_2", 1083, 306, 54, 67, 0], [2, "1254", 1084, 298, 54, 32, 2], [2, "1244", 1067, 314, 30, 67, 2], [2, "1141_2", 1104, 819, 54, 67, 0], [2, "1244", 1145, 799, 30, 67, 2], [2, "1254", 1105, 811, 54, 32, 2], [2, "1244", 1095, 823, 30, 67, 2], [2, "1244", 976, 881, 30, 67, 2], [2, "1141_2", 933, 903, 54, 67, 0], [2, "1254", 935, 893, 54, 32, 2], [2, "1244", 921, 908, 30, 67, 2], [2, "1141_2", 879, 929, 54, 67, 0], [2, "1254", 880, 921, 54, 32, 2], [2, "1244", 863, 937, 30, 67, 2], [2, "1254", 822, 949, 54, 32, 2], [2, "1244", 952, 369, 30, 67, 2], [2, "1141_2", 909, 391, 54, 67, 0], [2, "1254", 911, 381, 54, 32, 2], [2, "1244", 897, 396, 30, 67, 2], [2, "1254", 230, 908, 54, 32, 0], [2, "1244", 211, 895, 30, 67, 0], [2, "1141_1", 229, 916, 54, 67, 2], [2, "1244", 272, 925, 30, 67, 0]]}, {"type": 4, "obj": [[2, "3246", 110, -36, 28, 70, 0], [2, "3236", 651, -24, 90, 61, 2], [2, "3237", 404, 2, 44, 43, 2], [2, "3246", 8, 5, 28, 70, 0], [2, "3247", 55, 35, 48, 97, 0], [2, "3246", 342, 63, 28, 70, 0], [2, "3236", 204, 83, 90, 61, 2], [2, "3246", 764, 81, 28, 70, 0], [2, "3246", 452, 109, 28, 70, 0], [2, "3240", 988, 10, 178, 176, 0], [2, "3246", 641, 136, 28, 70, 0], [2, "3236", 788, 167, 90, 61, 0], [2, "3237", 66, 253, 44, 43, 2], [2, "3236", 10, 268, 90, 61, 0], [2, "3236", 391, 285, 90, 61, 2], [2, "1254", 244, 358, 54, 32, 0], [2, "1244", 225, 345, 30, 67, 0], [2, "1141_1", 243, 366, 54, 67, 2], [2, "1244", 286, 375, 30, 67, 0], [2, "3236", 659, 541, 90, 61, 0], [2, "3246", 957, 558, 28, 70, 0], [2, "3246", 371, 571, 28, 70, 0], [2, "3237", 578, 605, 44, 43, 0], [2, "3246", 1081, 620, 28, 70, 0], [2, "3233", 1007, 584, 76, 130, 2], [2, "3237", 167, 725, 44, 43, 2], [2, "3236", 105, 742, 90, 61, 2], [2, "1254", 16, 788, 54, 32, 0], [2, "1244", -3, 775, 30, 67, 0], [2, "1141_1", 15, 796, 54, 67, 2], [2, "1244", 58, 805, 30, 67, 0], [2, "3236", -35, 819, 90, 61, 2], [2, "3236", 587, 824, 90, 61, 2], [2, "3233", 1006, 785, 76, 130, 0], [2, "3247", 1028, 818, 48, 97, 0], [2, "3236", 350, 859, 90, 61, 0], [2, "3237", 321, 886, 44, 43, 0]]}, {"type": 3, "obj": [[2, "3249", 295, 147, 110, 63, 0], [2, "3249", 901, 642, 110, 63, 0], [2, "3248", 34, 781, 110, 63, 0], [2, "3248", 792, 916, 110, 63, 0], [2, "3248", 1020, 704, 110, 63, 0], [2, "3248", 895, 634, 110, 63, 0], [2, "3248", 922, 650, 110, 63, 0], [2, "3248", 886, 123, 110, 63, 0], [2, "1254", 433, 456, 54, 32, 2], [2, "1254", 359, 499, 54, 32, 2], [2, "1141_1", 114, -36, 54, 67, 0], [2, "1141_1", 71, -63, 54, 67, 0], [2, "1141_1", 71, -18, 54, 67, 0], [2, "1141_1", 28, -56, 54, 67, 0], [2, "1141_1", 28, -11, 54, 67, 0], [2, "1249", 55, 42, 22, 53, 2], [2, "1249", 75, 51, 22, 53, 2], [2, "43_9", 15, 70, 82, 58, 2], [2, "1141_1", 109, 18, 54, 67, 0], [2, "1141_1", 92, 15, 54, 67, 0], [2, "1141_1", -14, 80, 54, 67, 0], [2, "1141_1", -15, 35, 54, 67, 0], [2, "1141_1", -15, -11, 54, 67, 0], [2, "1141_1", -15, -53, 54, 67, 0], [2, "1141_1", 94, 26, 54, 67, 0], [2, "3243", 1064, 4, 32, 34, 0], [2, "3242", 1078, 48, 32, 34, 0], [2, "1141_2", 457, 338, 54, 67, 0], [2, "1141_2", 415, 359, 54, 67, 0], [2, "1141_2", 372, 381, 54, 67, 0], [2, "1141_2", 330, 402, 54, 67, 0], [2, "1141_2", 288, 422, 54, 67, 0], [2, "1141_2", 246, 443, 54, 67, 0], [2, "1141_2", 203, 465, 54, 67, 0], [2, "1141_2", 161, 486, 54, 67, 0], [2, "1254", 454, 334, 54, 32, 2], [2, "1254", 410, 355, 54, 32, 2], [2, "1254", 367, 377, 54, 32, 2], [2, "1254", 323, 398, 54, 32, 2], [2, "1254", 278, 420, 54, 32, 2], [2, "1254", 234, 441, 54, 32, 2], [2, "1254", 191, 464, 54, 32, 2], [2, "1254", 147, 485, 54, 32, 2], [2, "1244", 356, 490, 30, 67, 2], [2, "1141_1", 315, 509, 54, 67, 0], [2, "1254", 315, 501, 54, 32, 2], [2, "1244", 297, 519, 30, 67, 2], [2, "1141_1", 258, 538, 54, 67, 0], [2, "1254", 258, 530, 54, 32, 2], [2, "1141_1", 514, 392, 54, 67, 0], [2, "1254", 514, 384, 54, 32, 2], [2, "1244", 496, 402, 30, 67, 2], [2, "1141_1", 457, 421, 54, 67, 0], [2, "1254", 457, 413, 54, 32, 2], [2, "1254", 79, 752, 54, 32, 2], [2, "1254", 547, 430, 54, 32, 2], [2, "1254", 615, 431, 54, 32, 2], [2, "1254", 1088, 444, 54, 32, 2], [2, "1254", 1049, 464, 54, 32, 2], [2, "1254", 1004, 488, 54, 32, 2], [2, "1254", 963, 512, 54, 32, 2], [2, "1254", 912, 529, 54, 32, 2], [2, "3242", 952, 463, 32, 34, 0], [2, "1254", 613, 730, 54, 32, 2], [2, "1254", 583, 756, 54, 32, 2], [2, "1254", 711, 818, 54, 32, 2], [2, "1254", 587, 805, 54, 32, 0], [2, "1254", 693, 714, 54, 32, 0], [2, "1254", 658, 720, 54, 32, 0], [2, "1254", 697, 723, 54, 32, 2], [2, "1254", 730, 792, 54, 32, 2], [2, "1254", 715, 743, 54, 32, 0], [2, "1254", 670, 851, 54, 32, 2], [2, "1254", 648, 835, 54, 32, 0], [2, "3235", 784, 174, 22, 51, 0], [2, "3235", 807, 159, 22, 51, 0], [2, "3235", 827, 152, 22, 51, 0], [2, "3235", 849, 136, 22, 51, 0], [2, "3235", 870, 125, 22, 51, 0], [2, "3235", 885, 116, 22, 51, 0], [2, "3235", 906, 105, 22, 51, 0], [2, "3235", 926, 95, 22, 51, 0], [2, "3235", 947, 84, 22, 51, 0], [2, "3245", 1069, 407, 46, 24, 0], [2, "3244", 1100, 421, 46, 24, 0], [2, "3245", 719, 453, 46, 24, 0], [2, "3245", 759, 490, 46, 24, 0], [2, "3243", 97, 613, 32, 34, 0], [2, "1244", 960, 82, 30, 67, 0], [2, "3245", 814, 466, 46, 24, 0], [2, "3244", 803, 492, 46, 24, 0], [2, "1241", 1082, 876, 38, 50, 0], [2, "1244", 954, 31, 30, 67, 2], [2, "1244", 672, 505, 30, 67, 2], [2, "1141_1", 631, 524, 54, 67, 0], [2, "1254", 631, 516, 54, 32, 2], [2, "1244", 613, 534, 30, 67, 2], [2, "1141_1", 574, 553, 54, 67, 0], [2, "1254", 574, 545, 54, 32, 2], [2, "1254", 698, 354, 54, 32, 0], [2, "1254", 736, 373, 54, 32, 0], [2, "1244", 322, 778, 30, 67, 0], [2, "1141_1", 340, 796, 54, 67, 2], [2, "1254", 340, 789, 54, 32, 0], [2, "1244", 380, 805, 30, 67, 0], [2, "1141_1", 342, 826, 54, 67, 0], [2, "1254", 342, 818, 54, 32, 2], [2, "1244", 324, 836, 30, 67, 2], [2, "1141_1", 283, 855, 54, 67, 0], [2, "1254", 283, 847, 54, 32, 2], [2, "1244", 265, 865, 30, 67, 2], [2, "1141_1", 226, 884, 54, 67, 0], [2, "1254", 226, 876, 54, 32, 2], [2, "1141_1", 596, -52, 54, 67, 0], [2, "1244", 585, -46, 30, 67, 2], [2, "1141_1", 543, -26, 54, 67, 0], [2, "1254", 544, -34, 54, 32, 2], [2, "1141_1", 438, -50, 54, 67, 2], [2, "1254", 440, -55, 54, 32, 0], [2, "1244", 476, -42, 30, 67, 0], [2, "1141_1", 492, -24, 54, 67, 2], [2, "1254", 493, -30, 54, 32, 0], [2, "1244", 524, -18, 30, 67, 0], [2, "1141_1", 401, -42, 54, 67, 0], [2, "1254", 402, -50, 54, 32, 2], [2, "1244", 391, -36, 30, 67, 2], [2, "1141_1", 348, -16, 54, 67, 0], [2, "1254", 349, -24, 54, 32, 2], [2, "1244", 338, -10, 30, 67, 2], [2, "1141_1", 296, 10, 54, 67, 0], [2, "1254", 297, 2, 54, 32, 2], [2, "1244", 285, 16, 30, 67, 2], [2, "1141_1", 243, 36, 54, 67, 0], [2, "1254", 244, 28, 54, 32, 2], [2, "1244", 237, 553, 30, 67, 0], [2, "1141_1", 255, 573, 54, 67, 2], [2, "1254", 256, 566, 54, 32, 0], [2, "1244", 297, 583, 30, 67, 0], [2, "1141_1", 314, 604, 54, 67, 2], [2, "1254", 315, 597, 54, 32, 0], [2, "1255", 145, 491, 26, 27, 0], [2, "1244", 157, 2, 30, 67, 0], [2, "1141_1", 173, 23, 54, 67, 2], [2, "1254", 173, 14, 54, 32, 0], [2, "1244", 210, 29, 30, 67, 0], [2, "1141_1", 227, 50, 54, 67, 2], [2, "1254", 228, 43, 54, 32, 0], [2, "1253", 112, 21, 22, 35, 0], [2, "1253", 11, 62, 22, 35, 0], [2, "1244", -23, 141, 30, 67, 0], [2, "1244", 266, 58, 30, 67, 0], [2, "1141_1", 281, 78, 54, 67, 2], [2, "1254", 282, 70, 54, 32, 0], [2, "1244", 316, 84, 30, 67, 0], [2, "1141_1", 333, 106, 54, 67, 2], [2, "1254", 332, 98, 54, 32, 0], [2, "249_1", 347, 107, 48, 72, 0], [2, "249_1", 461, 162, 48, 72, 0], [2, "1244", 499, 175, 30, 67, 2], [2, "1141_1", 458, 196, 54, 67, 0], [2, "1254", 457, 189, 54, 32, 2], [2, "1244", 443, 203, 30, 67, 2], [2, "1141_1", 401, 224, 54, 67, 0], [2, "1254", 401, 216, 54, 32, 2], [2, "1244", 386, 232, 30, 67, 2], [2, "1141_1", 402, 252, 54, 67, 2], [2, "1254", 402, 245, 54, 32, 0], [2, "1244", 440, 259, 30, 67, 0], [2, "1141_1", 458, 280, 54, 67, 2], [2, "1254", 459, 273, 54, 32, 0], [2, "1241", 160, 500, 38, 50, 0], [2, "264_2", 225, 602, 66, 34, 2], [2, "264_2", 263, 620, 66, 34, 2], [2, "1255", 213, 589, 26, 27, 0], [2, "1240", 70, 502, 84, 50, 0], [2, "1240", 81, 517, 84, 50, 0], [2, "1240", 91, 531, 84, 50, 0], [2, "1240", 101, 545, 84, 50, 0], [2, "1240", 110, 560, 84, 50, 0], [2, "1240", 119, 574, 84, 50, 0], [2, "1240", 129, 588, 84, 50, 0], [2, "1240", 141, 601, 84, 50, 0], [2, "1255", 55, 534, 26, 27, 0], [2, "1241", 70, 543, 38, 50, 0], [2, "1241", 97, 589, 38, 50, 0], [2, "1255", 124, 634, 26, 27, 0], [2, "10_1", 200, 616, 50, 26, 0], [2, "10_1", 173, 628, 50, 26, 2], [2, "10_1", 147, 641, 50, 26, 2], [2, "264_2", 155, 672, 66, 34, 2], [2, "10_1", 226, 629, 50, 26, 0], [2, "10_1", 198, 641, 50, 26, 2], [2, "10_1", 172, 654, 50, 26, 2], [2, "10_1", 251, 642, 50, 26, 0], [2, "10_1", 224, 654, 50, 26, 2], [2, "10_1", 198, 667, 50, 26, 0], [2, "1244", 353, 614, 30, 67, 0], [2, "251_2", 300, 637, 68, 57, 2], [2, "1240", 252, 680, 84, 50, 0], [2, "1240", 239, 667, 84, 50, 0], [2, "1240", 226, 654, 84, 50, 0], [2, "1240", 232, 691, 84, 50, 0], [2, "1240", 217, 679, 84, 50, 0], [2, "1240", 206, 665, 84, 50, 0], [2, "251_2", 194, 690, 68, 57, 2], [2, "1244", 173, 678, 30, 67, 2], [2, "1141_1", 131, 700, 54, 67, 0], [2, "1244", 57, 737, 30, 67, 2], [2, "1141_1", 14, 759, 54, 67, 0], [2, "1254", 14, 751, 54, 32, 2], [2, "1244", 666, 397, 30, 67, 0], [2, "1141_1", 687, 421, 54, 67, 2], [2, "1141_1", 699, -44, 54, 67, 2], [2, "1244", 735, -37, 30, 67, 0], [2, "1141_1", 753, -16, 54, 67, 2], [2, "1254", 754, -24, 54, 32, 0], [2, "1244", 796, -7, 30, 67, 0], [2, "1141_1", 815, 14, 54, 67, 2], [2, "1254", 814, 6, 54, 32, 0], [2, "1244", 851, 21, 30, 67, 0], [2, "1141_1", 869, 42, 54, 67, 2], [2, "1254", 870, 34, 54, 32, 0], [2, "1255", 780, 392, 26, 27, 2], [2, "1240", 745, 481, 84, 50, 2], [2, "1240", 780, 420, 84, 50, 2], [2, "1240", 791, 407, 84, 50, 2], [2, "1255", 864, 433, 26, 27, 2], [2, "1141_1", 841, 494, 54, 67, 2], [2, "1254", 842, 487, 54, 32, 0], [2, "1244", 870, 499, 30, 67, 0], [2, "1141_1", 887, 518, 54, 67, 2], [2, "1254", 888, 511, 54, 32, 0], [2, "1244", 927, 527, 30, 67, 0], [2, "1141_1", 888, 549, 54, 67, 0], [2, "1254", 888, 541, 54, 32, 2], [2, "1244", 870, 559, 30, 67, 2], [2, "1141_1", 885, 579, 54, 67, 2], [2, "1254", 886, 572, 54, 32, 0], [2, "1244", 925, 588, 30, 67, 0], [2, "1141_1", 941, 608, 54, 67, 2], [2, "1244", 1079, 662, 30, 67, 0], [2, "1141_1", 1098, 683, 54, 67, 2], [2, "1254", 1097, 675, 54, 32, 0], [2, "1244", 1138, 692, 30, 67, 0], [2, "1255", 740, 190, 26, 27, 0], [2, "1241", 781, 243, 38, 50, 0], [2, "1255", 807, 288, 26, 27, 0], [2, "1240", 666, 202, 84, 50, 0], [2, "1240", 676, 216, 84, 50, 0], [2, "1240", 686, 230, 84, 50, 0], [2, "1240", 696, 244, 84, 50, 0], [2, "1240", 705, 259, 84, 50, 0], [2, "1240", 714, 273, 84, 50, 0], [2, "1240", 724, 287, 84, 50, 0], [2, "1240", 736, 300, 84, 50, 0], [2, "1255", 650, 233, 26, 27, 0], [2, "1241", 665, 241, 38, 50, 0], [2, "1255", 718, 331, 26, 27, 0], [2, "249_1", 615, 184, 48, 72, 2], [2, "1244", 518, 186, 30, 67, 2], [2, "1141_1", 980, 106, 54, 67, 2], [2, "1254", 979, 97, 54, 32, 0], [2, "1244", 1021, 115, 30, 67, 0], [2, "1141_1", 1039, 135, 54, 67, 2], [2, "1254", 1040, 128, 54, 32, 0], [2, "1244", 1079, 145, 30, 67, 0], [2, "1141_1", 1098, 165, 54, 67, 2], [2, "1254", 1099, 158, 54, 32, 0], [2, "1244", 1137, 174, 30, 67, 0], [2, "1244", 571, 555, 30, 67, 0], [2, "1141_1", 589, 575, 54, 67, 2], [2, "1254", 590, 568, 54, 32, 0], [2, "1244", 631, 585, 30, 67, 0], [2, "11_4", 814, 62, 32, 29, 0], [2, "11_4", 437, -6, 32, 29, 0], [2, "1141_2", 1100, 713, 54, 67, 0], [2, "1254", 1099, 705, 54, 32, 2], [2, "1244", 1088, 719, 30, 67, 2], [2, "1141_2", 1045, 738, 54, 67, 0], [2, "1254", 1048, 733, 54, 32, 2], [2, "1244", 1039, 747, 30, 67, 2], [2, "1141_1", 1055, 767, 54, 67, 2], [2, "1254", 1056, 760, 54, 32, 0], [2, "1244", 1095, 776, 30, 67, 0], [2, "1141_1", 1112, 792, 54, 67, 2], [2, "1254", 1113, 785, 54, 32, 0], [2, "1141_2", -17, 472, 54, 67, 2], [2, "1254", -19, 464, 54, 32, 0], [2, "1244", 22, 479, 30, 67, 0], [2, "1141_2", -7, 160, 54, 67, 2], [2, "1254", -6, 154, 54, 32, 0], [2, "1244", 35, 167, 30, 67, 0], [2, "1141_2", 51, 186, 54, 67, 2], [2, "1254", 52, 180, 54, 32, 0], [2, "1244", 91, 202, 30, 67, 2], [2, "1141_1", 50, 223, 54, 67, 0], [2, "1254", 50, 216, 54, 32, 2], [2, "1244", 35, 230, 30, 67, 2], [2, "1141_1", -7, 251, 54, 67, 0], [2, "1254", -7, 242, 54, 32, 2], [2, "1244", -22, 259, 30, 67, 2], [2, "11_4", 102, 254, 32, 29, 0], [2, "1141_2", 913, 51, 54, 67, 0], [2, "1254", 914, 43, 54, 32, 2], [2, "1244", 897, 59, 30, 67, 2], [2, "1141_2", 854, 81, 54, 67, 0], [2, "1254", 856, 71, 54, 32, 2], [2, "1244", 842, 86, 30, 67, 2], [2, "1141_2", 799, 105, 54, 67, 0], [2, "1254", 800, 97, 54, 32, 2], [2, "1244", 792, 109, 30, 67, 2], [2, "1141_2", 749, 131, 54, 67, 0], [2, "1254", 750, 122, 54, 32, 2], [2, "249_1", 742, 128, 48, 72, 2], [2, "1141_2", 536, 207, 54, 67, 2], [2, "1254", 535, 198, 54, 32, 0], [2, "1141_2", 579, 211, 54, 67, 0], [2, "1254", 583, 202, 54, 32, 2], [2, "1244", 571, 213, 30, 67, 0], [2, "1241", 755, 199, 38, 50, 0], [2, "1240", 1003, 884, 84, 50, 0], [2, "1240", 1017, 896, 84, 50, 0], [2, "1240", 1032, 908, 84, 50, 0], [2, "1240", 1046, 922, 84, 50, 0], [2, "1240", 1060, 934, 84, 50, 0], [2, "1241", 1007, 914, 38, 50, 0], [2, "973_2", 977, 158, 42, 70, 2], [2, "973_2", 1020, 158, 42, 70, 0], [2, "3244", 757, 440, 46, 24, 0], [2, "3244", 768, 461, 46, 24, 0], [2, "3242", 536, 280, 32, 34, 0], [2, "3243", 625, 347, 32, 34, 0], [2, "3243", 613, 324, 32, 34, 0], [2, "3242", 384, 446, 32, 34, 0], [2, "3233", 33, 5, 76, 130, 0], [2, "3239", -27, 914, 112, 61, 2], [2, "1240", 983, 362, 84, 50, 0], [2, "1240", 987, 379, 84, 50, 0], [2, "1240", 996, 393, 84, 50, 0], [2, "1254", 135, 690, 54, 32, 2], [2, "1244", 116, 706, 30, 67, 2], [2, "3242", 29, 592, 32, 34, 0], [2, "3243", -5, 696, 32, 34, 0], [2, "1244", 863, 874, 30, 67, 2], [2, "1141_1", 878, 894, 54, 67, 2], [2, "1254", 879, 887, 54, 32, 0], [2, "961_1", 454, 358, 28, 36, 2], [2, "541_3", 467, 373, 38, 42, 0], [2, "3245", 1010, 438, 46, 24, 0], [2, "3245", 1070, 436, 46, 24, 0], [2, "3244", 1041, 423, 46, 24, 0], [2, "3244", 1039, 452, 46, 24, 0], [2, "961_1", 596, 227, 28, 36, 2], [2, "962_1", 610, 243, 18, 19, 2], [2, "541_3", 614, 250, 38, 42, 0], [2, "541_3", 642, 279, 38, 42, 0], [2, "541_3", 670, 308, 38, 42, 0], [2, "541_3", 698, 337, 38, 42, 0], [2, "961_1", 253, 474, 28, 36, 2], [2, "541_3", 266, 489, 38, 42, 0], [2, "11_4", 289, 130, 32, 29, 0], [2, "11_4", 942, 129, 32, 29, 0], [2, "11_4", 729, 731, 32, 29, 0], [2, "11_4", 401, 858, 32, 29, 0], [2, "11_4", 343, 661, 32, 29, 0], [2, "1254", 686, 413, 54, 32, 0], [2, "1254", 939, 598, 54, 32, 0], [2, "3248", 855, 139, 110, 63, 0], [2, "3249", 27, 98, 110, 63, 0], [2, "3249", 41, 100, 110, 63, 0], [2, "3249", 41, 100, 110, 63, 0], [2, "3249", 2, 108, 110, 63, 0], [2, "3249", 65, 80, 110, 63, 0], [2, "3249", 81, 69, 110, 63, 0], [2, "3249", 222, 179, 110, 63, 0], [2, "3249", 26, 341, 110, 63, 0], [2, "3249", 479, 82, 110, 63, 0], [2, "3249", 601, 116, 110, 63, 0], [2, "3249", 864, 280, 110, 63, 0], [2, "3249", 1054, 199, 110, 63, 0], [2, "3249", 47, 271, 110, 63, 0], [2, "3249", 401, 465, 110, 63, 0], [2, "3249", 352, 488, 110, 63, 0], [2, "3249", 753, 499, 110, 63, 0], [2, "3248", 690, 473, 110, 63, 0], [2, "3248", 1071, 440, 110, 63, 0], [2, "3249", 1033, 455, 110, 63, 0], [2, "3248", 351, 14, 110, 63, 0], [2, "3249", 550, 619, 110, 63, 0], [2, "3249", 951, 858, 110, 63, 0], [2, "3249", 980, 854, 110, 63, 0], [2, "3249", 1019, 837, 110, 63, 0], [2, "3249", 953, 832, 110, 63, 0], [2, "3249", 989, 882, 110, 63, 0], [2, "3249", 1016, 872, 110, 63, 0], [2, "3249", 997, 882, 110, 63, 0], [2, "3249", 348, 651, 110, 63, 0], [2, "3249", 807, 761, 110, 63, 0], [2, "1253", 642, 188, 22, 35, 0], [2, "1253", 767, 132, 22, 35, 0], [2, "1253", 457, 163, 22, 35, 2], [2, "1253", 347, 115, 22, 35, 2], [2, "1253", 960, 616, 22, 35, 2], [2, "1253", 1085, 678, 22, 35, 2], [2, "1253", 372, 623, 22, 35, 0], [2, "3249", 1014, 702, 110, 63, 0], [2, "3249", 626, 215, 110, 63, 0], [2, "3249", 688, 178, 110, 63, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, 12, 11, 5, 6, -1, -1, 48, 51, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 51, 50, 51, 50, -1, 44, 45, 41, 42, -1, -1, -1, -1, 12, 11, 11, 11, 5, 5, 15, 20, 20, 9, 10, -1, -1, 43, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 4, 5, 6, 48, 47, 51, 41, 42, -1, -1, -1, -1, 12, 11, 15, 20, 20, 20, 20, 20, 20, 20, 20, 21, 18, -1, -1, 43, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 4, 5, 15, 8, 9, 5, 6, 48, 47, 51, 50, -1, -1, -1, 19, 20, 20, 21, 27, 26, 20, 20, 20, 20, 21, 18, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, -1, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, 7, 8, 14, 20, 8, 26, 25, -1, -1, 48, 51, 50, -1, -1, 16, 17, 23, 22, 24, 23, 23, 27, 26, 26, 9, 6, -1, -1, -1, -1, -1, 50, 50, 50, 50, 50, -1, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 17, 27, 8, 20, 9, 5, 11, 10, 48, 47, -1, 51, 50, -1, -1, -1, -1, -1, -1, 24, 23, 23, 23, 25, -1, -1, -1, -1, 50, 50, 50, 50, 50, 44, 50, 44, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 27, 8, 8, 8, 14, 13, -1, -1, -1, 31, 50, 50, -1, -1, -1, -1, 36, 35, -1, -1, -1, -1, -1, -1, 50, 50, -1, 50, 50, 44, 44, 45, 50, 50, 50, 48, 50, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, 19, 20, 21, 17, 23, 22, -1, -1, 36, 35, 47, 47, -1, -1, -1, -1, 39, 38, -1, -1, -1, -1, -1, -1, 50, 50, 50, 45, 40, 51, 50, 50, 38, 50, 50, 50, 50, 50, 50, -1, -1, 43, 44, 45, -1, 4, 5, 15, 21, 18, -1, -1, -1, -1, 36, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 45, 42, -1, 48, 47, 47, 51, 50, 50, 50, 44, 50, 50, 43, 44, 45, 41, 42, -1, 7, 8, 8, 25, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 41, 42, -1, -1, 12, 11, 10, 48, 47, 47, 51, 50, 50, 50, 40, 41, 42, 4, 5, 5, 15, 8, 21, 22, -1, 36, 35, 39, 38, 44, 45, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 15, 14, 13, -1, -1, -1, 43, 50, 50, 50, 40, 41, 42, 7, 8, 8, 8, 8, 9, 10, -1, 43, 44, 44, 45, 41, 51, 50, 51, 50, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 4, 5, 5, 6, 19, 20, 20, 9, 10, -1, -1, 43, 44, 38, -1, -1, 12, 11, 15, 8, 8, 8, 8, 9, 13, -1, 40, 41, 41, 42, -1, 48, 47, 51, 50, 44, -1, -1, -1, -1, -1, -1, -1, -1, 44, 50, 49, 7, 8, 8, 9, 15, 26, 26, 26, 13, -1, -1, 40, 44, -1, -1, -1, 19, 20, 8, 20, 21, 23, 27, 8, 9, 6, 12, 11, 11, 10, -1, -1, -1, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 49, 24, 23, 8, 20, 20, 20, 21, 23, 22, -1, -1, -1, 44, -1, -1, -1, 16, 17, 23, 17, 18, -1, 19, 8, 8, 9, 15, 14, 14, 13, -1, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 24, 27, 20, 21, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 27, 8, 8, 8, 21, 18, -1, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, -1, -1, -1, -1, 24, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, -1, 24, 23, 23, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, -1, -1, -1, 44, 45, -1, -1, -1, -1, -1, -1, -1, -1, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 30, -1, -1, -1, -1, 2, 3, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 38, 38, 38, 38, 44, 45, 41, 42, -1, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 30, -1, -1, 0, 1, 1, 2, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 41, 41, 41, 41, 41, 42, -1, -1, -1, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, -1, -1, -1, -1, 32, 33, 34, -1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 49, -1, -1, -1, -1, -1, -1, 38, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 35, 35, 34, -1, -1, -1, 28, 29, 32, -1, -1, -1, 44, 44, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 44, 37, -1, -1, -1, 31, 32, 32, -1, -1, 44, 44, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, 36, 35, 14, 45, 38, 38, 44, 45, -1, -1, -1, 51, 50, 32, -1, -1, 44, 41, 42, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, 50, 35, 38, 45, 41, 42, -1, -1, -1, 48, 51, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 47, -1, 38, 49, -1, -1, -1, -1, -1, -1, 48, 47, 47, 51, 50, -1, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 5, 6, -1, -1, -1, -1, -1, 40, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 41, 51, 44, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, -1, 7, 8, 9, 11, 11, 5, 6, -1, -1, -1, -1, -1, -1, 12, 11, 11, 10, -1, -1, -1, -1, 40, 51, 50, 44, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 12, 11, 15, 14, 14, 14, 14, 8, 25, -1, 28, 29, 29, 30, -1, 19, 14, 14, 13, -1, -1, -1, -1, -1, 48, 47, 51, 50, 44, 44, 35, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 41, 42, 12, 11, 15, 14, 14, 14, 26, 14, 14, 21, 22, -1, 31, 32, 33, -1, -1, 16, 23, 27, 9, 11, 11, 10, -1, -1, -1, -1, 48, 47, 44, 44, 38, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 41, 42, -1, 12, 15, 14, 14, 14, 14, 14, 14, 14, 14, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 14, 14, 14, 13, -1, -1, -1, -1, -1, 39, 38, 38, -1, -1, -1, -1, -1, 44, 44, 44, 45, 41, 42, -1, -1, -1, 16, 17, 27, 26, 14, 14, 14, 14, 14, 14, 25, 43, -1, -1, -1, -1, -1, -1, -1, 37, 19, 14, 14, 14, 9, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 38, 41, 42, -1, -1, -1, -1, -1, -1, -1, 24, 23, 27, 26, 14, 26, 14, 21, 18, 43, -1, -1, -1, -1, -1, -1, -1, 12, 15, 14, 14, 26, 26, 13, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, 41, 38, 38, 38, 34, -1, -1, -1, -1, -1, 36, 35, -1, -1, -1, 24, 23, 23, 27, 26, 9, 10, -1, -1, -1, -1, -1, -1, -1, -1, 7, 14, 14, 20, 21, 23, 22, -1, -1, -1, -1, 48, 47, 35, -1, -1, 51, 50, 44, 45, 38, 37, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, 19, 20, 14, 9, 11, 11, -1, -1, -1, -1, 12, 11, 15, 26, 21, 17, 18, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, 38, 38, 38, 41, 42, -1, -1, -1, -1, -1, 39, 38, -1, -1, -1, 44, 45, -1, 35, 34, 16, 17, 27, 26, 14, 14, 25, 12, 11, 11, 20, 14, 21, 23, 22, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, 50, -1, 45, 41, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 38, 44, 45, 42, -1, -1, 24, 23, 23, 23, 18, 24, 27, 20, 25, -1, -1, -1, -1, -1, -1, -1, 36, 35, 39, 38, 50, 50, -1, -1, -1, 42, -1, -1, -1, -1, -1, -1, 36, 35, 35, -1, -1, 38, 38, 38, 38, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 22, -1, -1, -1, 36, 35, 35, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 39, 38, 38, -1, 38, 38, 38, 38, 45, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 39, 38, 38, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, 0, 0, 1, 0, 1, -1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, -1, -1, -1, -1, -1, -1, 61, 61, 61, 61, 61, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 3, 2, 0, 1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, -1, -1, -1, -1, 0, 1, -1, 61, 61, -1, 0, 1, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 1, -1, -1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, -1, 0, 1, 1, 1, 3, 0, 1, 1, -1, 2, 3, 2, 2, 3, 2, 0, 1, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 3, 2, 3, 0, 1, 0, 61, 61, 61, 61, 61, 61, 61, 61, 61, -1, 0, 1, 2, 3, 3, 3, 2, 2, 3, 0, 1, -1, -1, 2, 0, 1, 0, 2, 3, 1, 2, 3, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 0, 61, 61, 61, 61, 61, 61, 61, 0, 2, 3, 2, 0, 1, 1, 3, 2, 3, 0, 1, 0, 1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 3, 2, 3, 2, 3, 0, 1, 2, -1, 61, 61, 61, 61, 61, 61, 2, 2, 0, 1, 2, 3, 3, 0, 1, 0, 1, 0, 1, 3, 0, 0, 1, 0, 1, 2, 0, 1, 2, 3, 0, 1, 1, 0, 0, 1, 2, 3, -1, -1, -1, 0, 1, 2, 3, 0, 1, 0, 1, 61, 61, 61, 61, -1, 2, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 2, 3, 2, 3, 2, 3, 3, 2, 2, 3, 3, -1, -1, 0, 1, 2, 3, 0, 1, 2, 3, 2, 3, 0, 1, 61, 61, 61, -1, -1, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 0, 1, 0, 1, -1, -1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 0, 0, 1, 3, -1, 61, 61, 61, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 2, 3, 2, 3, 2, 3, -1, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, -1, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 2, 2, 3, 3, 0, 61, 61, -1, 0, 1, 0, 1, 1, 0, 1, 3, 0, 1, 0, 0, 1, 0, -1, 61, 61, 61, 61, 61, -1, -1, -1, 61, 61, 3, -1, -1, -1, 2, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 61, 0, 1, 2, 3, 2, 3, 3, 2, 3, 2, 2, 3, 2, 2, 3, -1, -1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, -1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, -1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 3, 2, 0, 1, 0, 1, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 2, 0, 1, 0, 1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, -1, 0, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 0, 1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 0, 1, 2, 2, 3, 0, 1, 3, 2, 3, 2, 3, 2, 3, 2, 3, -1, 1, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 61, 61, 61, 61, 61, 61, 61, 61, 61, 1, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, 61, 61, 61, 3, 2, 3, 0, 1, 0, 0, 1, 3, 0, 1, 0, 1, 2, 3, 2, 0, 1, -1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 1, 0, 0, 1, 0, 1, 0, 0, 1, 3, -1, -1, 61, 61, 61, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 61, 61, 61, 61, 61, 0, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 2, 2, 3, 0, 1, 1, 2, 61, 61, 61, 61, 61, 61, 61, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 61, 61, 61, 61, 61, 61, -1, -1, 0, 61, 61, 61, 61, 3, 0, 61, 61, 61, 61, 61, 61, 3, 2, 1, 61, 61, 61, 61, 61, 61, 61, 61, 61, 0, 1, 0, 1, 0, 1, -1, -1, 0, 1, 0, 1, 0, 61, 61, 61, 61, 61, 61, -1, 2, 1, 0, 1, 3, 1, 2, 3, 2, 3, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 0, 2, 0, 1, 3, 2, 0, 1, -1, -1, -1, 1, 61, 61, 61, 61, 61, 61, 61, 0, 3, 0, 1, 2, 3, 2, 3, 1, 1, 0, 0, 0, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 0, 1, 2, -1, 0, 1, 0, 1, 2, 3, -1, -1, 61, 61, 61, 61, 61, 61, -1, 3, 2, 3, 0, 1, 0, 1, 0, 1, 2, 3, 3, 2, 2, 2, 3, 0, 1, 61, 61, 0, 61, 61, 61, 61, 61, 0, 1, 2, 3, 3, 61, 61, 1, 2, 3, -1, -1, 61, 61, 61, 61, 61, 61, 61, -1, -1, 1, 0, 1, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 0, 1, -1, 0, 0, 61, 0, 1, 1, 3, 1, 0, 1, 61, 61, 61, -1, -1, -1, -1, 61, 61, 61, 61, -1, -1, 1, 1, 3, 3, 2, 3, 2, 3, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 0, 1, -1, -1, 0, 2, 3, 3, 2, 3, 2, 3, 61, 61, 61, 61, -1, 0, -1, 0, 61, 61, 61, 2, 0, 1, 0, 1, 0, 1, 1, 0, 1, 2, 2, 3, 0, 2, 3, 0, 1, 0, 1, 0, 0, 1, 0, 2, 3, -1, -1, 2, 3, 2, 0, 1, 2, 0, 1, 61, 61, 61, 61, 61, -1, -1, -1, -1, -1, -1, -1, 2, 3, 1, 3, 2, 3, 1, 0, 0, 1, 0, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 2, 3, 3, -1, -1, 2, 0, 1, 2, 3, 1, 0, 1, 61, 61, 61, 61, 61, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, -1, 2, 3, 0, 2, 3, 2, 3, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 1, 0, 1, 0, 2, 0, 1, 61, 61, 61, 61, 61, 61, 61, 61, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 3, 1, 2, 3, 2, 3, 2, 3, 2, 3, 61, 61, 61, 61, 61, 61, 61, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 2, 3, 0, 2, 3, 0, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 2, 3, 3, 3, 2, 3, 2, 3, 0, 1, -1, -1, 61, 61, 61, 61, 61, 61, 1, 0, 1, 0, 1, 0, 2, 0, 1, 0, 1, 1, 0, 1, 1, 2, 2, 3, 2, 0, 0, 1, 0, 61, 1, 0, 2, 3, 2, 3, 1, 0, 0, 1, 1, 0, 1, 2, 3, 0, 1, 61, 61, 61, 61, 61, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 2, 3, 3, 2, 3, 3, 2, 2, 61, 61, 61, 61, 61, 2, 3, 2, 0, 1, 3, 2, 2, 3, 0, 1, 3, 3, 3, 61, 61, 61, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 2, 3, 2, 3, 0, 1, 0, 61, 61, 61, 61, 61, 61, 61, 1, 1, 2, 3, 1, 1, 0, 1, 2, 3, 0, 1, -1, 61, 61, 61, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 61, 61, 61, 61, 61, 61, 61, 3, 3, 3, 2, 3, 0, 1, 0, 1, 0, 1, 3, -1, -1, 61, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 2, 61, 61, 61, 61, 61, 0, 1, 3, 1, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 0, 1, 2, 3, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, -1, 61, 61, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 1, 2, 3, 2, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 2, 3, 0, 1, 2, 3, 1, 0, 1, 3, 61, 2, 3, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 1, 2, 2, 0, 1, 1, 0, 1, 0, 1, 3, 0, 0, 1, 3, 3, 0, 1, 2, 3, -1, 61, 0, 1, 0, 1, 2, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 1, 0, 2, 3, 1, 2, 3, 1, 1, 0, 1, 0, 1, 0, 1, 2, 3, -1, -1, 0, 61, 2, 3, 2, 0, 1, 2, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 2, 3, 3, 2, 3, 2, 3, 2, 61, 61, 61, 61, 61, 61, 61]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0]}