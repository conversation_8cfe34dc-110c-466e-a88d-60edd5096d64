{"mW": 1200, "mH": 960, "tW": 24, "tH": 24, "tiles": [["315_7", 0, 3, 3], ["1233", 0, 3, 2], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["444_4", 0, 2, 2], ["417", 0, 1, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1300", 0, 3, 2], ["803", 0, 2, 2], ["996", 0, 2, 2]], "layers": [{"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 49, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 47, -1, -1, -1, 53, 48, 48, 48, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 56, -1, -1, -1, 53, 48, 48, 48, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 48, 48, 48, 48, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 61, 48, 48, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 39, 45, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 54, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, 51, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "1471", 20, 487, 44, 39, 2], [2, "124", 19, 426, 142, 70, 0], [2, "124", 226, 656, 142, 70, 2], [2, "1471", 18, 440, 44, 39, 2], [2, "1471", -1, 449, 44, 39, 2], [2, "1471", -25, 460, 44, 39, 2], [2, "1471", 37, 451, 44, 39, 2], [2, "1471", 18, 462, 44, 39, 2], [2, "1471", -4, 472, 44, 39, 2], [2, "967", 1063, 486, 24, 41, 0], [2, "1456", 401, 693, 24, 32, 0], [2, "1457", 387, 688, 22, 30, 0], [2, "1457", 373, 682, 22, 30, 0], [2, "1457", 304, 682, 22, 30, 0], [2, "329", 273, 679, 42, 37, 2], [2, "1236", 1039, 263, 42, 43, 0], [2, "1236", 697, 42, 42, 43, 0], [2, "1236", 793, 75, 42, 42, 6], [2, "1483", 844, 618, 22, 38, 0], [2, "460", 844, 591, 20, 37, 2], [2, "460", 823, 580, 20, 37, 2], [2, "1483", 822, 606, 22, 38, 0], [2, "1483", 864, 629, 22, 38, 0], [2, "460", 864, 603, 20, 37, 2], [2, "1483", 929, 654, 22, 38, 2], [2, "1483", 886, 641, 22, 38, 0], [2, "460", 885, 615, 20, 37, 2], [2, "1483", 908, 652, 22, 38, 0], [2, "460", 906, 626, 20, 37, 2], [2, "460", 927, 628, 20, 37, 0], [2, "460", 948, 617, 20, 37, 0], [2, "1483", 948, 643, 22, 38, 2], [2, "1483", 970, 632, 22, 38, 2], [2, "460", 969, 606, 20, 37, 0], [2, "396", 853, 606, 34, 59, 0], [2, "258", 962, 621, 16, 39, 2], [2, "897", 810, 581, 22, 62, 0], [2, "897", 905, 632, 22, 62, 0], [2, "1483", 983, 624, 22, 38, 2], [2, "460", 982, 598, 20, 37, 0], [2, "897", 997, 604, 22, 62, 2], [2, "897", 931, 633, 22, 62, 2], [2, "1471", 882, 532, 44, 39, 0], [2, "1471", 904, 543, 44, 39, 0], [2, "1471", 925, 555, 44, 39, 0], [2, "1471", 961, 576, 44, 39, 0], [2, "1471", 852, 547, 44, 39, 0], [2, "1471", 874, 557, 44, 39, 0], [2, "1471", 895, 569, 44, 39, 0], [2, "1471", 916, 581, 44, 39, 0], [2, "1471", 822, 560, 44, 39, 0], [2, "1471", 843, 571, 44, 39, 0], [2, "1471", 864, 583, 44, 39, 0], [2, "1471", 885, 595, 44, 39, 0], [2, "733", 1008, 652, 54, 35, 2], [2, "733", 992, 672, 54, 35, 0], [2, "733", 954, 692, 54, 35, 0], [2, "733", 952, 701, 54, 35, 2], [2, "733", 757, 618, 54, 35, 0], [2, "733", 760, 638, 54, 35, 2], [2, "733", 801, 658, 54, 35, 2], [2, "733", 841, 679, 54, 35, 2], [2, "733", 880, 699, 54, 35, 2], [2, "733", 920, 719, 54, 35, 2], [2, "733", 945, 707, 54, 35, 0], [2, "1475", 886, 714, 36, 32, 0], [2, "1475", 921, 732, 36, 32, 0], [2, "1475", 803, 671, 36, 32, 0], [2, "1475", 767, 653, 36, 32, 0], [2, "1475", 956, 732, 36, 32, 2], [2, "1475", 971, 725, 36, 32, 2], [2, "1236", 973, 691, 42, 43, 0], [2, "1236", 754, 618, 42, 42, 5], [2, "1236", 877, 704, 42, 42, 7], [2, "1236", 818, 676, 42, 42, 7], [2, "1236", 814, 554, 42, 42, 7], [2, "1457", 962, 710, 22, 30, 0], [2, "1457", 944, 716, 22, 30, 0], [2, "1457", 1002, 639, 22, 30, 0], [2, "1457", 772, 617, 22, 30, 0], [2, "1457", 787, 611, 22, 30, 0], [2, "1456", 779, 632, 24, 32, 0], [2, "1456", 827, 555, 24, 32, 0], [2, "1456", 926, 707, 24, 32, 0], [2, "329", 784, 675, 42, 37, 2], [2, "329", 775, 590, 42, 37, 2], [2, "1457", 1023, 647, 22, 30, 0], [2, "1456", 983, 676, 24, 32, 0], [2, "1456", 1033, 644, 24, 32, 2], [2, "1456", 1016, 655, 24, 32, 2], [2, "1457", 999, 668, 22, 30, 0], [2, "1457", 965, 561, 22, 30, 0], [2, "1457", 840, 563, 22, 30, 0], [2, "1476", 338, 751, 18, 32, 0], [2, "1476", 321, 743, 18, 32, 0], [2, "1476", 304, 734, 18, 32, 0], [2, "1476", 286, 725, 18, 32, 0], [2, "1476", 356, 761, 18, 32, 0], [2, "1476", 374, 771, 18, 32, 0], [2, "1476", 391, 770, 18, 32, 2], [2, "1476", 409, 761, 18, 32, 2], [2, "1476", 427, 752, 18, 32, 2], [2, "1476", 445, 743, 18, 32, 2], [2, "930", 286, 721, 42, 22, 0], [2, "930", 321, 739, 42, 22, 0], [2, "930", 356, 757, 42, 22, 0], [2, "930", 392, 753, 42, 22, 2], [2, "930", 420, 739, 42, 22, 2], [2, "930", 296, 703, 42, 22, 2], [2, "930", 412, 723, 42, 22, 0], [2, "733", 313, 695, 54, 35, 0], [2, "733", 331, 703, 54, 35, 0], [2, "733", 364, 719, 54, 35, 0], [2, "733", 380, 727, 54, 35, 0], [2, "14", 377, 703, 32, 30, 0], [2, "11", 365, 745, 32, 29, 0], [2, "26", 374, 710, 10, 25, 0], [2, "1236", 272, 720, 42, 42, 7], [2, "1236", 368, 767, 42, 42, 7], [2, "1236", 436, 733, 42, 42, 6], [2, "1457", 443, 716, 22, 30, 0], [2, "1456", 286, 696, 24, 32, 0], [2, "967", 1083, 488, 40, 24, 5], [2, "967", 935, 409, 24, 41, 0], [2, "1236", 31, 310, 42, 43, 0], [2, "1236", 95, 279, 42, 43, 2], [2, "1471", 59, 463, 44, 39, 2], [2, "1471", 40, 474, 44, 39, 2], [2, "1476", -4, 521, 18, 32, 2], [2, "1476", 86, 475, 18, 32, 2], [2, "1476", 68, 484, 18, 32, 2], [2, "14", 12, 441, 32, 30, 0], [2, "11", 42, 450, 32, 29, 0], [2, "35", 30, 466, 22, 16, 0], [2, "15", -11, 449, 22, 27, 0], [2, "679", 24, 476, 36, 32, 0], [2, "1471", 935, 592, 44, 39, 0], [2, "1471", 906, 604, 44, 39, 0], [2, "1456", 946, 560, 24, 32, 0], [2, "124", 976, 671, 142, 70, 2], [2, "1476", 50, 493, 18, 32, 2], [2, "1471", -7, 497, 44, 39, 2], [2, "1476", 14, 512, 18, 32, 2], [2, "1476", 32, 503, 18, 32, 2], [2, "14", 308, 714, 32, 30, 0], [2, "14", 334, 726, 32, 30, 0]]}, {"type": 4, "obj": [[2, "683", 827, -21, 22, 34, 0], [2, "1462", 523, -56, 64, 76, 2], [2, "967", 975, -3, 24, 41, 0], [2, "683", 768, 6, 22, 34, 0], [2, "1462", 581, -28, 64, 76, 2], [2, "1464", 182, -68, 66, 123, 0], [2, "1464", 131, -60, 66, 123, 2], [2, "1462", 624, 0, 64, 76, 2], [2, "1463", 679, 19, 22, 59, 0], [2, "1479", 107, -53, 78, 135, 2], [2, "1462", 608, 14, 64, 76, 0], [2, "1479", 1029, -45, 78, 135, 0], [2, "1466", 605, -69, 158, 161, 2], [2, "1462", 429, 20, 64, 76, 0], [2, "1463", 592, 38, 22, 59, 0], [2, "1463", 409, 42, 22, 59, 0], [4, 13, 223, 111, 0, 4024], [2, "683", 776, 78, 22, 34, 0], [2, "683", 740, 97, 22, 34, 0], [2, "1479", -2, 1, 78, 135, 2], [2, "1479", 1123, 4, 78, 135, 0], [2, "1464", -29, 30, 66, 123, 2], [2, "683", 1190, 128, 22, 34, 0], [2, "1464", 679, 46, 66, 123, 0], [2, "1462", 515, 129, 64, 76, 2], [2, "1463", 572, 148, 22, 59, 0], [2, "1464", 771, 90, 66, 123, 0], [2, "1462", 435, 143, 64, 76, 2], [2, "1462", 494, 143, 64, 76, 0], [2, "1462", 437, 172, 64, 76, 0], [2, "967", 556, 208, 24, 41, 0], [4, 1, 519, 251, 0, 4005], [2, "1463", 417, 195, 22, 59, 0], [2, "683", 1132, 253, 22, 34, 0], [2, "1464", 925, 165, 66, 123, 0], [2, "969", 499, 264, 36, 30, 0], [4, 6, 967, 296, 0, 4001], [2, "683", 1089, 274, 22, 34, 0], [2, "683", 1178, 274, 22, 34, 0], [2, "1457", 418, 280, 22, 30, 0], [2, "326", 418, 301, 18, 14, 0], [2, "1478", 643, 236, 96, 90, 0], [2, "1464", 1017, 211, 66, 123, 0], [2, "1457", 470, 306, 22, 30, 0], [2, "1456", 485, 316, 24, 32, 0], [2, "1463", 110, 337, 22, 59, 0], [2, "1466", -51, 267, 158, 161, 2], [2, "1463", 44, 369, 22, 59, 0], [2, "1478", 824, 338, 96, 90, 0], [2, "1457", 185, 401, 22, 30, 0], [2, "1456", 177, 410, 24, 32, 2], [4, 3, 230, 444, 0, 4005], [2, "1462", -17, 390, 64, 76, 0], [2, "1456", 242, 444, 24, 32, 2], [2, "968", 936, 441, 24, 38, 0], [2, "1471", 84, 485, 44, 39, 2], [4, 2, 710, 529, 0, 4005], [2, "125", 135, 463, 18, 70, 0], [2, "1471", 63, 496, 44, 39, 2], [2, "1471", 43, 507, 44, 39, 2], [2, "970", 1106, 503, 48, 47, 0], [2, "1471", 22, 517, 44, 39, 2], [2, "968", 1059, 518, 24, 38, 0], [4, 11, 495, 563, 0, 4021], [2, "1471", 3, 527, 44, 39, 2], [2, "1471", -17, 537, 44, 39, 2], [2, "1471", 759, 643, 44, 39, 0], [2, "1471", 777, 652, 44, 39, 0], [4, 10, 680, 694, 0, 4019], [2, "1466", 1113, 535, 158, 161, 2], [2, "1471", 797, 663, 44, 39, 0], [4, 9, 1146, 716, 0, 4022], [2, "1471", 836, 683, 44, 39, 0], [2, "1471", 339, 690, 44, 39, 0], [2, "1471", 857, 693, 44, 39, 0], [2, "1471", 323, 697, 44, 39, 0], [2, "1471", 352, 700, 44, 39, 0], [2, "1471", 878, 704, 44, 39, 0], [2, "1471", 336, 707, 44, 39, 0], [2, "1471", 294, 709, 44, 39, 0], [2, "1471", 373, 710, 44, 39, 0], [2, "1471", 897, 715, 44, 39, 0], [2, "1471", 305, 717, 44, 39, 0], [2, "1471", 357, 717, 44, 39, 0], [2, "1471", 395, 721, 44, 39, 0], [2, "1471", 963, 721, 44, 39, 0], [2, "1471", 919, 726, 44, 39, 0], [2, "125", 231, 696, 18, 70, 0], [2, "1471", 379, 728, 44, 39, 0], [2, "1471", 331, 731, 44, 39, 0], [2, "1471", 417, 732, 44, 39, 0], [2, "125", 1083, 701, 18, 70, 0], [2, "1471", 287, 736, 44, 39, 0], [2, "1471", 938, 736, 44, 39, 0], [2, "1471", 401, 739, 44, 39, 0], [2, "1471", 353, 742, 44, 39, 0], [2, "1471", 308, 746, 44, 39, 0], [2, "1471", 422, 749, 44, 39, 0], [4, 4, 186, 792, 0, 4001], [2, "1471", 375, 753, 44, 39, 0], [2, "125", 1047, 724, 18, 70, 0], [2, "1471", 330, 757, 44, 39, 0], [2, "1471", 396, 763, 44, 39, 0], [2, "1471", 352, 768, 44, 39, 0], [2, "52", 237, 792, 46, 22, 0], [2, "1471", 371, 776, 44, 39, 0], [2, "31", 271, 755, 14, 63, 0], [2, "52", 238, 802, 46, 22, 0], [2, "1463", 52, 771, 22, 59, 0], [2, "258", 423, 795, 16, 39, 2], [2, "1466", 461, 676, 158, 161, 2], [2, "31", 230, 778, 14, 63, 0], [2, "1462", -3, 783, 64, 76, 0], [2, "679", 438, 831, 36, 32, 0], [2, "52", 334, 846, 46, 22, 0], [2, "31", 368, 809, 14, 63, 0], [2, "52", 334, 855, 46, 22, 0], [2, "1105", -18, 810, 104, 75, 2], [2, "31", 327, 832, 14, 63, 0], [4, 12, 923, 900, 0, 4020], [4, 7, 218, 920, 0, 4010], [2, "63", 827, 892, 16, 31, 0], [2, "1105", 715, 851, 104, 75, 2], [2, "52", 785, 905, 46, 22, 0], [4, 8, 264, 931, 0, 4018], [2, "63", 775, 913, 16, 31, 0], [2, "52", 734, 926, 46, 22, 0], [2, "63", 720, 932, 16, 31, 0]]}, {"type": 2, "data": [54, 54, 54, 54, 54, -1, -1, -1, -1, -1, 60, 60, 60, 55, 56, -1, -1, -1, -1, -1, -1, -1, 53, 54, 48, -1, -1, -1, 54, 54, 54, 54, 55, 57, 56, -1, -1, -1, -1, -1, -1, 48, 48, 42, 42, -1, -1, -1, -1, -1, 54, 54, 54, 54, 54, -1, -1, -1, 54, 60, 55, 57, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 54, 54, -1, -1, -1, 48, 54, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, 57, 61, 42, -1, -1, 45, 45, 44, -1, -1, 54, 54, 54, 54, 54, -1, 54, 55, 51, 56, -1, -1, -1, -1, -1, 46, 54, -1, -1, 55, -1, 58, 61, 54, -1, -1, -1, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 61, -1, 48, 48, 48, -1, -1, -1, -1, 54, 54, 54, 54, 55, 51, 56, -1, -1, -1, -1, -1, -1, -1, 53, 54, 54, 55, 56, -1, -1, 58, 61, 54, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 48, 48, 48, -1, -1, -1, -1, 48, 54, 48, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 57, 57, 56, -1, -1, -1, -1, 58, 61, 54, 54, 54, 54, -1, -1, 48, 48, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, 58, 61, 48, 48, -1, 48, 54, 48, 48, 55, 51, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 39, 39, 40, 58, 51, 51, 61, -1, 48, 48, 48, 48, 48, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, 58, 51, 61, 48, 48, 54, 55, 51, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 45, 49, -1, -1, 43, 44, -1, -1, -1, 51, 51, 61, 48, -1, 48, 48, 43, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, 58, 57, 57, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, -1, -1, -1, -1, -1, 58, 61, -1, 48, 60, 48, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 39, -1, -1, -1, 48, 48, 54, 54, 47, -1, -1, -1, -1, -1, -1, 61, 48, 48, 48, 48, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 48, -1, 54, 55, 57, 51, 57, 54, 59, -1, -1, -1, -1, -1, -1, 58, 48, 48, 48, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 55, 51, 52, -1, -1, 58, 57, 52, -1, -1, -1, -1, -1, -1, -1, 48, 48, 55, 56, -1, -1, -1, -1, 45, 45, 45, 45, 45, 45, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, 46, 46, -1, 45, 44, -1, 48, 55, 56, -1, -1, -1, 48, 55, 61, 48, 48, 48, 48, 48, 48, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 49, 48, 48, 55, 52, -1, 55, 52, -1, -1, -1, -1, -1, -1, 58, 61, -1, 48, 48, 48, 48, 55, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 44, -1, -1, -1, 46, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 48, 48, 48, 48, 48, 59, -1, -1, -1, -1, -1, -1, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 48, 47, -1, -1, -1, 58, 51, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 59, -1, 48, -1, 45, 45, 49, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, -1, -1, -1, -1, 48, 48, 48, 48, 48, 59, -1, 48, 48, 48, 48, 48, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 54, 54, 59, -1, -1, 46, -1, 48, 48, 48, 48, 55, 56, -1, -1, -1, 48, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 54, 55, 52, -1, -1, 53, 49, 48, 48, 48, 55, 56, -1, -1, -1, 48, 48, 48, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 51, 52, -1, -1, -1, 50, 61, 54, 55, 57, 56, -1, -1, -1, -1, -1, -1, 48, -1, 47, 46, 45, 43, 45, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 44, -1, 50, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 49, 48, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 48, 48, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 51, 52, -1, -1, -1, -1, 60, 39, 39, 39, 39, -1, -1, -1, -1, 53, 48, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 60, 60, 60, 60, 60, -1, -1, -1, -1, 53, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 60, 60, 60, 60, -1, 45, 44, -1, -1, 58, 51, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 61, 60, 60, 60, -1, -1, 54, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 49, 54, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 57, 61, 60, -1, -1, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 49, 54, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, -1, 48, -1, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 48, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 51, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, -1, 53, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 57, 57, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 40, 50, 61, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 42, 43, 40, 58, 51, 51, -1, -1, -1, -1, -1, -1, -1, -1, 46, 49, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 48, 46, 45, -1, -1, -1, -1, 48, 48, 48, 48, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, 45, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 48, -1, 54, -1, -1, 48, 48, 48, 48, 48, 48, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 61, 48, 48, -1, -1, -1, -1, -1, -1, 48, 42, 48, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 57, -1, 54, -1, 53, 48, 48, 48, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, 40, -1, -1, -1, -1, -1, 58, 51, 51, -1, -1, -1, -1, 48, 48, 48, 48, 48, 43, 39, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, -1, 53, 48, 48, 48, 48, 55, 56, -1, -1, -1, -1, -1, -1, -1, 43, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 57, 57, 57, 57, 56, -1, -1, -1, -1, -1, 48, 48, -1, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 48, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 55, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 51, 51, 51, 51, 61, 48, 59, -1, -1, -1, -1, -1, -1, -1, -1, 45, 45, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 51, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 52, -1, -1, -1, -1, -1, 46, 49, 48, 48, 48, 48, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 49, 48, 48, 48, 48, 48, 59, -1, -1, -1, -1, -1, 46, 45, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 48, 48, 48, 48, 48, 55, 52, -1, -1, -1, -1, -1, 53, 48, 48, 43, 44, -1, -1, -1]}, {"type": 3, "obj": [[2, "253", 411, 309, 92, 53, 2], [2, "253", 648, 504, 92, 53, 0], [2, "253", 685, 528, 92, 53, 0], [2, "253", 33, 734, 92, 53, 0], [2, "253", 564, 634, 92, 53, 0], [2, "253", 440, 647, 92, 53, 0], [2, "253", 498, 360, 92, 53, 2], [2, "253", 359, 254, 92, 53, 2], [2, "253", 86, 377, 92, 53, 2], [2, "253", 240, 483, 92, 53, 2], [2, "253", 113, 688, 92, 53, 0], [2, "253", 605, 565, 92, 53, 0], [2, "219_1", 712, 551, 36, 30, 0], [2, "1456", 665, 588, 24, 32, 0], [2, "1457", 691, 565, 22, 30, 0], [2, "1358", 35, 649, 62, 34, 2], [2, "1358", -6, 670, 62, 34, 2], [2, "1358", 24, 623, 62, 34, 0], [2, "1456", 626, 826, 24, 32, 2], [2, "1457", 607, 809, 22, 30, 0], [2, "220_1", 604, 829, 40, 29, 2], [2, "1456", 187, 808, 24, 32, 0], [2, "1457", 203, 796, 22, 30, 0], [2, "220_1", 58, 783, 40, 29, 2], [2, "1358", 93, 555, 62, 34, 2], [2, "460", 55, 534, 20, 37, 0], [2, "390", 429, -32, 102, 80, 2], [2, "390", 365, 6, 102, 80, 2], [2, "390", 304, 44, 102, 80, 2], [2, "1473", 53, -14, 18, 58, 0], [2, "1472", 69, -9, 36, 67, 0], [2, "1473", 105, 9, 18, 58, 0], [2, "390", 239, 79, 102, 80, 2], [2, "1457", 534, 236, 22, 30, 0], [2, "220_1", 959, 448, 40, 29, 0], [2, "1456", 1092, 740, 24, 32, 0], [2, "329", 831, 855, 42, 37, 0], [2, "1358", 423, 821, 62, 34, 2], [2, "1358", 384, 842, 62, 34, 2], [2, "1358", 341, 838, 62, 34, 0], [2, "1358", 267, 797, 62, 34, 0], [2, "897", 449, 768, 22, 62, 2], [2, "1358", 729, 699, 62, 34, 2], [2, "1358", 947, 787, 62, 34, 0], [2, "1358", 949, 805, 62, 34, 2], [2, "1358", 883, 790, 62, 34, 0], [2, "1358", 779, 742, 62, 34, 0], [2, "1358", 728, 717, 62, 34, 0], [2, "897", 1052, 683, 22, 62, 2], [2, "897", 756, 668, 22, 62, 0], [2, "460", 1017, 701, 20, 37, 0], [2, "460", 1038, 690, 20, 37, 0], [2, "460", 906, 741, 20, 37, 2], [2, "460", 811, 691, 20, 37, 2], [2, "396", 848, 711, 34, 59, 0], [2, "866", 838, 755, 42, 26, 2], [2, "1358", 842, 26, 62, 34, 2], [2, "1469", 1007, 110, 40, 22, 0], [2, "1358", 649, 179, 62, 34, 2], [2, "1469", 879, 337, 40, 22, 0], [2, "1469", 852, 351, 40, 22, 0], [2, "1469", 780, 286, 40, 22, 0], [2, "1472", 915, 152, 36, 67, 2], [2, "1474", 826, 253, 18, 28, 2], [2, "1474", 808, 264, 18, 28, 2], [2, "1473", 826, 204, 18, 58, 2], [2, "1473", 790, 224, 18, 58, 2], [2, "1473", 808, 214, 18, 58, 2], [2, "1473", 844, 194, 18, 58, 2], [2, "1472", 809, 202, 36, 67, 2], [2, "1473", 665, 169, 18, 58, 0], [2, "1473", 718, 196, 18, 58, 0], [2, "1472", 682, 178, 36, 67, 0], [2, "1473", 772, 224, 18, 58, 0], [2, "1472", 736, 206, 36, 67, 0], [2, "1473", 963, 315, 18, 58, 0], [2, "1473", 1017, 343, 18, 58, 0], [2, "1472", 981, 325, 36, 67, 0], [2, "1473", 1035, 342, 18, 58, 2], [2, "1472", 1053, 324, 36, 67, 2], [2, "1473", 1089, 314, 18, 58, 2], [2, "1472", 1107, 296, 36, 67, 2], [2, "1473", 1143, 296, 18, 58, 0], [2, "1472", 1160, 305, 36, 67, 0], [2, "1473", 1196, 323, 18, 58, 0], [2, "1475", 982, 312, 36, 32, 0], [2, "1475", 1001, 322, 36, 32, 0], [2, "1475", 756, 203, 36, 32, 0], [2, "1475", 737, 193, 36, 32, 0], [2, "1475", 701, 175, 36, 32, 0], [2, "1475", 665, 157, 36, 32, 0], [2, "1474", 928, 346, 18, 28, 0], [2, "1474", 964, 364, 18, 28, 0], [2, "1474", 946, 355, 18, 28, 0], [2, "1474", 1000, 382, 18, 28, 0], [2, "1474", 982, 373, 18, 28, 0], [2, "1474", 1017, 391, 18, 28, 0], [2, "1474", 772, 273, 18, 28, 0], [2, "1474", 755, 264, 18, 28, 0], [2, "1474", 737, 255, 18, 28, 0], [2, "1474", 719, 246, 18, 28, 0], [2, "1474", 701, 237, 18, 28, 0], [2, "1474", 683, 228, 18, 28, 0], [2, "1474", 665, 218, 18, 28, 0], [2, "1474", 790, 273, 18, 28, 2], [2, "1475", 791, 202, 36, 32, 2], [2, "1475", 827, 184, 36, 32, 2], [2, "1475", 862, 166, 36, 32, 2], [2, "1472", 862, 179, 36, 67, 2], [2, "1473", 897, 171, 18, 58, 2], [2, "43", 811, 257, 82, 58, 0], [2, "43", 839, 229, 82, 58, 0], [2, "43", 866, 201, 82, 58, 0], [2, "43", 890, 255, 82, 58, 0], [2, "43", 917, 227, 82, 58, 0], [2, "43", 893, 173, 82, 58, 0], [2, "43", 944, 199, 82, 58, 0], [2, "1474", 910, 337, 18, 28, 0], [2, "43", 861, 283, 82, 58, 0], [2, "1475", 911, 276, 36, 32, 0], [2, "1475", 946, 294, 36, 32, 0], [2, "1473", 910, 288, 18, 58, 0], [2, "1474", 1035, 391, 18, 28, 2], [2, "1474", 1053, 382, 18, 28, 2], [2, "1474", 1089, 364, 18, 28, 2], [2, "1474", 1071, 373, 18, 28, 2], [2, "1474", 1107, 355, 18, 28, 2], [2, "1474", 1125, 346, 18, 28, 2], [2, "1474", 1196, 372, 18, 28, 0], [2, "1474", 1179, 363, 18, 28, 0], [2, "1474", 1161, 354, 18, 28, 0], [2, "1474", 1143, 345, 18, 28, 0], [2, "1472", 927, 297, 36, 67, 0], [2, "1475", 1072, 303, 36, 32, 2], [2, "1475", 1107, 285, 36, 32, 2], [2, "1475", 1036, 321, 36, 32, 2], [2, "1475", 1179, 302, 36, 32, 0], [2, "1475", 1143, 284, 36, 32, 0], [2, "1500", 766, 195, 50, 26, 0], [2, "1500", 740, 182, 50, 26, 0], [2, "1500", 766, 169, 50, 26, 0], [2, "1500", 792, 182, 50, 26, 0], [2, "1500", 740, 156, 50, 26, 0], [2, "1500", 714, 143, 50, 26, 0], [2, "1500", 688, 156, 50, 26, 0], [2, "1500", 714, 169, 50, 26, 0], [2, "1500", 667, 145, 50, 26, 0], [2, "1500", 818, 169, 50, 26, 0], [2, "1500", 792, 156, 50, 26, 0], [2, "1500", 766, 143, 50, 26, 0], [2, "1500", 740, 130, 50, 26, 0], [2, "1500", 719, 119, 50, 26, 0], [2, "1500", 843, 156, 50, 26, 0], [2, "1500", 817, 143, 50, 26, 0], [2, "1500", 791, 130, 50, 26, 0], [2, "1500", 765, 117, 50, 26, 0], [2, "1500", 744, 106, 50, 26, 0], [2, "1500", 869, 143, 50, 26, 0], [2, "1500", 843, 130, 50, 26, 0], [2, "1500", 817, 117, 50, 26, 0], [2, "1500", 791, 104, 50, 26, 0], [2, "1500", 770, 93, 50, 26, 0], [2, "1500", 1011, 314, 50, 26, 0], [2, "1500", 985, 301, 50, 26, 0], [2, "1500", 959, 288, 50, 26, 0], [2, "1500", 933, 275, 50, 26, 0], [2, "1500", 912, 264, 50, 26, 0], [2, "1500", 1036, 301, 50, 26, 0], [2, "1500", 1010, 288, 50, 26, 0], [2, "1500", 984, 275, 50, 26, 0], [2, "1500", 958, 262, 50, 26, 0], [2, "1500", 1062, 288, 50, 26, 0], [2, "1500", 1036, 275, 50, 26, 0], [2, "1500", 1010, 262, 50, 26, 0], [2, "1500", 984, 249, 50, 26, 0], [2, "1500", 963, 238, 50, 26, 0], [2, "1500", 1087, 275, 50, 26, 0], [2, "1500", 1061, 262, 50, 26, 0], [2, "1500", 1035, 249, 50, 26, 0], [2, "1500", 1009, 236, 50, 26, 0], [2, "1500", 988, 225, 50, 26, 0], [2, "1500", 1112, 262, 50, 26, 0], [2, "1500", 1086, 249, 50, 26, 0], [2, "1500", 1060, 236, 50, 26, 0], [2, "1500", 1144, 271, 50, 26, 0], [2, "1500", 1165, 282, 50, 26, 0], [2, "1500", 1191, 295, 50, 26, 0], [2, "1500", 1112, 236, 50, 26, 0], [2, "1500", 1138, 249, 50, 26, 0], [2, "1475", 898, 148, 36, 32, 2], [2, "1500", 1034, 223, 50, 26, 0], [2, "1500", 1013, 212, 50, 26, 0], [2, "1500", 1038, 199, 50, 26, 0], [2, "1500", 1060, 210, 50, 26, 0], [2, "1500", 1086, 223, 50, 26, 0], [2, "1469", 754, 299, 40, 22, 0], [2, "1469", 727, 313, 40, 22, 0], [2, "1469", 701, 326, 40, 22, 0], [2, "1469", 674, 340, 40, 22, 0], [2, "1469", 648, 353, 40, 22, 0], [2, "1469", 621, 367, 40, 22, 0], [2, "1469", 826, 363, 40, 22, 0], [2, "1469", 800, 375, 40, 22, 0], [2, "1469", 747, 402, 40, 22, 0], [2, "1469", 774, 388, 40, 22, 0], [2, "1469", 697, 427, 40, 22, 0], [2, "1469", 723, 414, 40, 22, 0], [2, "1469", 595, 380, 40, 22, 0], [2, "1358", 746, 425, 62, 34, 0], [2, "1358", 797, 451, 62, 34, 0], [2, "1358", 848, 443, 62, 34, 2], [2, "1358", 900, 416, 62, 34, 2], [2, "1358", 926, 403, 62, 34, 2], [2, "1358", 977, 410, 62, 34, 0], [2, "1358", 995, 420, 62, 34, 0], [2, "1358", 1046, 410, 62, 34, 2], [2, "1358", 1098, 384, 62, 34, 2], [2, "1358", 1150, 392, 62, 34, 0], [2, "1358", 554, 318, 62, 34, 0], [2, "1358", 588, 337, 62, 34, 0], [2, "1358", 569, 291, 62, 34, 2], [2, "1358", 622, 263, 62, 34, 2], [2, "1358", 613, 233, 62, 34, 0], [2, "1358", 598, 206, 62, 34, 2], [2, "1456", 989, 396, 24, 32, 0], [2, "1457", 953, 405, 22, 30, 0], [2, "329", 1089, 397, 42, 37, 0], [2, "326", 915, 439, 18, 14, 0], [2, "1368", 1145, 366, 50, 42, 0], [2, "1368", 597, 277, 50, 42, 0], [2, "422", 594, 348, 16, 14, 0], [2, "421", 811, 472, 14, 11, 0], [2, "422", 819, 469, 16, 14, 0], [2, "422", 851, 470, 16, 14, 0], [2, "422", 901, 446, 16, 14, 0], [2, "422", 1082, 420, 16, 14, 0], [2, "930", 1028, 318, 42, 22, 2], [2, "930", 1066, 298, 42, 22, 2], [2, "930", 1101, 281, 42, 22, 2], [2, "930", 1136, 278, 42, 22, 0], [2, "930", 1172, 296, 42, 22, 0], [2, "930", 991, 314, 42, 22, 0], [2, "930", 956, 297, 42, 22, 0], [2, "930", 921, 280, 42, 22, 0], [2, "930", 912, 274, 42, 22, 0], [2, "930", 969, 230, 42, 22, 2], [2, "930", 1003, 213, 42, 22, 2], [2, "930", 755, 199, 42, 22, 0], [2, "930", 719, 182, 42, 22, 0], [2, "930", 685, 165, 42, 22, 0], [2, "930", 665, 153, 42, 22, 0], [2, "930", 727, 108, 42, 22, 2], [2, "930", 762, 91, 42, 22, 2], [2, "930", 740, 28, 42, 22, 2], [2, "930", 775, 12, 42, 22, 2], [2, "930", 811, -5, 42, 22, 2], [2, "1500", 741, 41, 50, 26, 0], [2, "1500", 767, 30, 50, 26, 0], [2, "1500", 790, 18, 50, 26, 0], [2, "1500", 816, 5, 50, 26, 0], [2, "1500", 841, -8, 50, 26, 0], [2, "1500", 837, 18, 50, 26, 0], [2, "1500", 812, 31, 50, 26, 0], [2, "1500", 786, 44, 50, 26, 0], [2, "1500", 763, 56, 50, 26, 0], [2, "1500", 864, 6, 50, 26, 0], [2, "1500", 864, -19, 50, 26, 0], [2, "1500", 890, -7, 50, 26, 0], [2, "1500", 916, -20, 50, 26, 0], [2, "271", 958, 29, 64, 50, 0], [2, "271", 970, 34, 64, 50, 0], [2, "271", 981, 40, 64, 50, 0], [2, "271", 992, 46, 64, 50, 0], [2, "271", 1003, 52, 64, 50, 0], [2, "271", 1014, 58, 64, 50, 0], [2, "271", 1025, 64, 64, 50, 0], [2, "271", 1036, 70, 64, 50, 0], [2, "43", 1032, 97, 82, 58, 0], [2, "43", 1058, 70, 82, 58, 0], [2, "271", 1103, 105, 64, 50, 0], [2, "271", 1114, 111, 64, 50, 0], [2, "271", 1125, 117, 64, 50, 0], [2, "271", 1136, 123, 64, 50, 0], [2, "271", 1147, 129, 64, 50, 0], [2, "271", 1158, 135, 64, 50, 0], [2, "271", 1169, 141, 64, 50, 0], [2, "271", 1180, 147, 64, 50, 0], [2, "70_1", 1178, 138, 50, 26, 0], [2, "930", 1114, 121, 42, 22, 0], [2, "930", 1148, 139, 42, 22, 0], [2, "930", 1168, 150, 42, 22, 0], [2, "930", 1024, 74, 42, 22, 0], [2, "930", 1004, 63, 42, 22, 0], [2, "930", 970, 45, 42, 22, 0], [2, "930", 979, 28, 42, 22, 2], [2, "930", 1014, 11, 42, 22, 2], [2, "70_1", 1154, 124, 50, 26, 0], [2, "70_1", 1177, 111, 50, 26, 0], [2, "70_1", 1153, 97, 50, 26, 0], [2, "70_1", 1104, 98, 50, 26, 0], [2, "70_1", 1053, 72, 50, 26, 0], [2, "70_1", 1078, 85, 50, 26, 0], [2, "70_1", 1004, 45, 50, 26, 0], [2, "70_1", 1028, 59, 50, 26, 0], [2, "70_1", 989, 36, 50, 26, 0], [2, "70_1", 1127, 84, 50, 26, 0], [2, "70_1", 1103, 70, 50, 26, 0], [2, "70_1", 1076, 59, 50, 26, 0], [2, "70_1", 1040, 38, 50, 26, 0], [2, "70_1", 1016, 24, 50, 26, 0], [2, "70_1", 1163, 28, 50, 26, 0], [2, "70_1", 1164, 53, 50, 26, 0], [2, "70_1", 1163, 80, 50, 26, 0], [2, "265", 1108, 14, 68, 77, 0], [2, "1469", 981, 123, 40, 22, 0], [2, "1469", 955, 136, 40, 22, 0], [2, "1469", 1075, 147, 40, 22, 0], [2, "1469", 1048, 160, 40, 22, 0], [2, "1469", 1023, 173, 40, 22, 0], [2, "965", 879, 6, 40, 33, 0], [2, "969", 884, 31, 36, 30, 0], [2, "1358", 886, 131, 62, 34, 0], [2, "1358", 837, 105, 62, 34, 0], [2, "1358", 787, 81, 62, 34, 0], [2, "1358", 790, 53, 62, 34, 2], [2, "1358", 914, -9, 62, 34, 2], [2, "1358", 1057, 202, 62, 34, 0], [2, "1358", 1111, 225, 62, 34, 0], [2, "1500", 1168, 260, 50, 26, 0], [2, "1358", 1165, 251, 62, 34, 0], [2, "930", 952, 125, 42, 22, 2], [2, "930", 987, 107, 42, 22, 2], [2, "930", 939, 132, 42, 22, 2], [2, "930", 1082, 155, 42, 22, 2], [2, "930", 1048, 173, 42, 22, 2], [2, "930", 1114, 160, 42, 22, 0], [2, "930", 1148, 178, 42, 22, 0], [2, "930", 980, 88, 42, 22, 0], [2, "930", 950, 72, 42, 22, 0], [2, "220_1", 748, 98, 40, 29, 0], [2, "220_1", 847, -12, 40, 29, 0], [2, "220_1", 1146, 252, 40, 29, 0], [2, "220_1", 997, 225, 40, 29, 0], [2, "174_4", 812, 133, 68, 33, 0], [2, "329", 1086, 261, 42, 37, 0], [2, "1456", 752, 173, 24, 32, 0], [2, "1456", 702, 146, 24, 32, 0], [2, "1456", 726, 158, 24, 32, 0], [2, "1456", 736, 162, 24, 32, 0], [2, "1457", 742, 169, 22, 30, 0], [2, "1457", 753, 85, 22, 30, 0], [2, "1456", 738, 24, 24, 32, 0], [2, "1457", 997, 293, 22, 30, 0], [2, "1456", 948, 269, 24, 32, 0], [2, "1456", 980, 281, 24, 32, 0], [2, "1456", 965, 276, 24, 32, 0], [2, "326", 874, 154, 18, 14, 0], [2, "1456", 833, 159, 24, 32, 0], [2, "1457", 848, 156, 22, 30, 0], [2, "1457", 1016, 210, 22, 30, 0], [2, "1483", 769, 703, 22, 38, 0], [2, "1483", 791, 714, 22, 38, 0], [2, "1483", 901, 764, 22, 38, 0], [2, "1483", 921, 775, 22, 38, 0], [2, "1483", 943, 786, 22, 38, 0], [2, "1471", 864, 762, 44, 39, 0], [2, "1471", 865, 745, 44, 39, 0], [2, "1471", 866, 728, 44, 39, 0], [2, "1483", 965, 786, 22, 38, 2], [2, "1483", 975, 748, 22, 38, 2], [2, "1483", 997, 737, 22, 38, 2], [2, "460", 769, 677, 20, 37, 2], [2, "460", 790, 688, 20, 37, 2], [2, "460", 927, 752, 20, 37, 2], [2, "460", 945, 761, 20, 37, 2], [2, "460", 966, 760, 20, 37, 0], [2, "460", 996, 712, 20, 37, 0], [2, "1471", 978, 646, 44, 39, 0], [2, "1471", 998, 657, 44, 39, 0], [2, "1471", 1020, 669, 44, 39, 0], [2, "1471", 909, 638, 44, 39, 0], [2, "1471", 931, 649, 44, 39, 0], [2, "1471", 952, 660, 44, 39, 0], [2, "1471", 972, 671, 44, 39, 0], [2, "1471", 994, 682, 44, 39, 0], [2, "1471", 819, 618, 44, 39, 0], [2, "1471", 839, 630, 44, 39, 0], [2, "1471", 860, 641, 44, 39, 0], [2, "1471", 883, 653, 44, 39, 0], [2, "1471", 905, 664, 44, 39, 0], [2, "1471", 926, 675, 44, 39, 0], [2, "1471", 946, 686, 44, 39, 0], [2, "1471", 793, 632, 44, 39, 0], [2, "1471", 813, 644, 44, 39, 0], [2, "1471", 834, 655, 44, 39, 0], [2, "1471", 857, 667, 44, 39, 0], [2, "1471", 879, 678, 44, 39, 0], [2, "1471", 900, 689, 44, 39, 0], [2, "1471", 968, 697, 44, 39, 0], [2, "1471", 920, 700, 44, 39, 0], [2, "1471", 942, 711, 44, 39, 0], [2, "1483", 1019, 726, 22, 38, 2], [2, "1483", 1041, 714, 22, 38, 2], [2, "897", 942, 766, 22, 62, 0], [2, "897", 900, 743, 22, 62, 0], [2, "897", 785, 690, 22, 62, 0], [2, "897", 988, 742, 22, 62, 2], [2, "11", 1059, 729, 32, 29, 0], [2, "14", 1009, 752, 32, 30, 0], [2, "15", 1014, 736, 22, 27, 0], [2, "35", 1059, 750, 22, 16, 0], [2, "115", 1010, 755, 16, 37, 0], [2, "253", 758, 777, 92, 53, 0], [2, "1457", 980, 787, 22, 30, 0], [2, "1456", 925, 803, 24, 32, 0], [2, "1358", 695, 433, 62, 34, 2], [2, "1358", 578, 360, 62, 34, 2], [2, "1483", 307, 789, 22, 38, 0], [2, "1483", 350, 811, 22, 38, 0], [2, "1483", 372, 822, 22, 38, 0], [2, "1483", 394, 823, 22, 38, 2], [2, "1483", 416, 812, 22, 38, 2], [2, "1483", 438, 801, 22, 38, 2], [2, "460", 308, 763, 20, 37, 2], [2, "460", 371, 795, 20, 37, 2], [2, "460", 350, 784, 20, 37, 2], [2, "460", 287, 751, 20, 37, 2], [2, "1483", 286, 777, 22, 38, 0], [2, "460", 392, 797, 20, 37, 0], [2, "460", 413, 787, 20, 37, 0], [2, "460", 434, 777, 20, 37, 0], [2, "396", 323, 779, 34, 59, 0], [2, "897", 281, 758, 22, 62, 0], [2, "1475", 393, 808, 36, 32, 2], [2, "1475", 424, 792, 36, 32, 2], [2, "1475", 292, 778, 36, 32, 0], [2, "1475", 351, 808, 36, 32, 0], [2, "897", 374, 805, 22, 62, 0], [2, "22", 494, 876, 62, 38, 0], [2, "1457", 480, 812, 22, 30, 0], [2, "1456", 517, 821, 24, 32, 0], [2, "253", 239, 841, 92, 53, 0], [2, "11", 833, 886, 32, 29, 0], [2, "15", 806, 859, 22, 27, 0], [2, "14", 729, 913, 32, 30, 0], [2, "219_1", 862, 901, 36, 30, 0], [2, "220_1", 693, 927, 40, 29, 0], [2, "1457", 821, 876, 22, 30, 0], [2, "1457", 1177, 657, 22, 30, 0], [2, "326", 1133, 674, 18, 14, 0], [2, "329", 1170, 676, 42, 37, 0], [2, "969", 1160, 861, 36, 30, 0], [2, "1457", 1022, 925, 22, 30, 0], [2, "1457", 1183, 842, 22, 30, 0], [2, "969", 1085, 568, 36, 30, 0], [2, "965", 1050, 546, 40, 33, 0], [2, "911", 315, 824, 32, 22, 2], [2, "422", 1155, 779, 16, 14, 0], [2, "422", 1089, 520, 16, 14, 0], [2, "422", 1121, 579, 16, 14, 0], [2, "421", 908, 477, 14, 11, 0], [2, "420", 1168, 515, 16, 13, 0], [2, "422", 583, 722, 16, 14, 0], [2, "965", 926, 468, 40, 33, 0], [2, "174_4", 1085, 612, 68, 33, 0], [2, "174_4", 547, 839, 68, 33, 0], [2, "654", 375, 502, 96, 45, 0], [2, "654", 471, 502, 96, 45, 2], [2, "654", 472, 457, 96, 45, 3], [2, "654", 376, 457, 96, 45, 1], [2, "1358", 124, 357, 62, 34, 2], [2, "1358", 55, 392, 62, 34, 2], [2, "1358", 175, 332, 62, 34, 2], [2, "1358", 310, 265, 62, 34, 2], [2, "1358", 363, 242, 62, 34, 2], [2, "390", 174, 117, 102, 80, 2], [2, "390", 109, 151, 102, 80, 2], [2, "632", 400, 293, 56, 45, 0], [2, "632", 462, 325, 56, 45, 0], [2, "632", 160, 419, 56, 45, 0], [2, "632", 222, 451, 56, 45, 0], [2, "1456", 556, 262, 24, 32, 0], [2, "390", 46, 185, 102, 80, 2], [2, "390", -19, 222, 102, 80, 2], [2, "43", 38, 45, 82, 58, 2], [2, "43", 10, 14, 82, 58, 2], [2, "43", -21, -16, 82, 58, 2], [2, "1473", 89, -46, 18, 58, 0], [2, "1358", 44, 578, 62, 34, 2], [2, "1358", -9, 602, 62, 34, 2], [2, "1483", -7, 579, 22, 38, 0], [2, "1483", 15, 580, 22, 38, 2], [2, "1483", 37, 569, 22, 38, 2], [2, "460", -8, 552, 20, 37, 2], [2, "460", 13, 554, 20, 37, 0], [2, "460", 34, 544, 20, 37, 0], [2, "1475", 22, 557, 36, 32, 2], [2, "897", -1, 572, 22, 62, 0], [2, "460", 76, 522, 20, 37, 0], [2, "1483", 78, 548, 22, 38, 2], [2, "1483", 100, 537, 22, 38, 2], [2, "460", 97, 512, 20, 37, 0], [2, "1475", 81, 527, 36, 32, 2], [2, "396", 50, 538, 34, 59, 2], [2, "911", 61, 584, 32, 22, 0], [2, "897", 114, 511, 22, 62, 2], [2, "253", 71, 600, 92, 53, 2], [2, "421", 1020, 37, 14, 11, 0], [2, "1456", 576, 56, 24, 32, 0], [2, "1457", 486, 34, 22, 30, 2], [2, "174_4", 621, 84, 68, 33, 0], [2, "1457", 484, 100, 22, 30, 2], [2, "326", 624, 84, 18, 14, 0], [2, "329", 581, 164, 42, 37, 0], [2, "220_1", 581, 672, 40, 29, 0], [2, "220_1", 377, 217, 40, 29, 0], [2, "220_1", 69, 331, 40, 29, 0], [2, "220_1", 543, 47, 40, 29, 2], [2, "220_1", 380, 91, 40, 29, 2], [2, "220_1", 785, 499, 40, 29, 2], [2, "220_1", 616, 848, 40, 29, 2], [2, "220_1", 199, 822, 40, 29, 2], [2, "220_1", 238, 794, 40, 29, 0], [2, "220_1", 208, 807, 40, 29, 0], [2, "220_1", 365, 861, 40, 29, 0], [2, "220_1", 339, 874, 40, 29, 0], [2, "220_1", 309, 886, 40, 29, 2], [2, "220_1", 165, 824, 40, 29, 0], [2, "253", 145, 856, 92, 53, 0], [2, "253", 3, 916, 92, 53, 0], [2, "116", 251, 709, 46, 39, 2], [2, "422", 33, 915, 16, 14, 0], [2, "422", 407, 931, 16, 14, 0], [2, "421", 172, 859, 14, 11, 0], [2, "220_1", 58, 798, 40, 29, 0], [2, "220_1", 197, 726, 40, 29, 2], [2, "1457", 389, 854, 22, 30, 0], [2, "14", 138, 531, 32, 30, 0], [2, "11", 148, 508, 32, 29, 0], [2, "35", 148, 530, 22, 16, 0], [2, "26", 142, 542, 10, 25, 0], [2, "8", 423, 870, 38, 29, 0], [2, "27", 435, 874, 6, 8, 0], [2, "27", 446, 873, 6, 8, 0], [2, "220_1", 576, 814, 40, 29, 2], [2, "220_1", 542, 808, 40, 29, 2], [2, "220_1", 532, 931, 40, 29, 0], [2, "220_1", 158, 914, 40, 29, 0], [2, "220_1", 130, 928, 40, 29, 0], [2, "220_1", 9, 616, 40, 29, 2], [2, "220_1", -3, 661, 40, 29, 0], [2, "220_1", 33, 635, 40, 29, 2], [2, "220_1", 20, 648, 40, 29, 0], [2, "1456", -1, 636, 24, 32, 0], [2, "1457", 19, 631, 22, 30, 0], [2, "1456", 3, 608, 24, 32, 0], [2, "1457", 18, 655, 22, 30, 0], [2, "1457", 40, 634, 22, 30, 0], [2, "1358", 129, 567, 62, 34, 0], [2, "1358", 175, 569, 62, 34, 2], [2, "14", 219, 474, 32, 30, 0], [2, "422", 68, 424, 16, 14, 2], [2, "422", 92, 352, 16, 14, 2], [2, "220_1", 588, 689, 40, 29, 2], [2, "220_1", 601, 708, 40, 29, 2], [2, "220_1", 653, 748, 40, 29, 2], [2, "220_1", 663, 765, 40, 29, 2], [2, "1457", 678, 742, 22, 30, 0], [2, "174_4", 641, 644, 68, 33, 0], [2, "174_4", 11, 864, 68, 33, 0], [2, "327", 1157, 701, 30, 22, 0], [2, "220_1", 1120, 802, 40, 29, 0], [2, "220_1", 954, 853, 40, 29, 0], [2, "422", 567, 674, 16, 14, 0], [2, "219_1", 405, 671, 36, 30, 0], [2, "219_1", 431, 679, 36, 30, 0], [2, "219_1", 459, 680, 36, 30, 0], [2, "219_1", 372, 653, 36, 30, 0], [2, "219_1", 712, 521, 36, 30, 0], [2, "219_1", 709, 482, 36, 30, 0], [2, "1457", 718, 510, 22, 30, 0], [2, "219_1", 108, 736, 36, 30, 2], [2, "219_1", 137, 726, 36, 30, 2], [2, "219_1", 159, 706, 36, 30, 2], [2, "219_1", 192, 697, 36, 30, 2], [2, "219_1", 211, 681, 36, 30, 2], [2, "219_1", 278, 654, 36, 30, 2], [2, "253", 229, 180, 92, 53, 2], [2, "253", 47, 309, 92, 53, 2], [2, "253", 293, 210, 92, 53, 2], [2, "253", 384, 618, 92, 53, 2], [2, "253", 197, 653, 92, 53, 0], [2, "253", 266, 615, 92, 53, 0], [2, "253", 220, 523, 92, 53, 0], [2, "253", 490, 668, 92, 53, 0], [2, "219_1", 502, 379, 36, 30, 0], [2, "1471", 808, 732, 44, 39, 0], [2, "1471", 808, 715, 44, 39, 0], [2, "1471", 808, 699, 44, 39, 0], [2, "1457", 803, 748, 22, 30, 0], [2, "253", 226, 293, 92, 53, 0], [2, "220_1", 324, 155, 40, 29, 0], [2, "1457", 347, 140, 22, 30, 0], [2, "969", 129, 347, 36, 30, 2], [2, "220_1", 351, 210, 40, 29, 2], [2, "220_1", 236, 163, 40, 29, 2], [2, "220_1", 716, 65, 40, 29, 2], [2, "220_1", 285, 264, 40, 29, 0], [2, "220_1", 266, 279, 40, 29, 0], [2, "220_1", 118, 222, 40, 29, 0], [2, "1457", 114, 221, 22, 30, 0], [2, "220_1", 1036, 192, 40, 29, 0], [2, "253", 608, 604, 92, 53, 0], [2, "422", 1034, 426, 16, 14, 0], [2, "253", 310, 632, 92, 53, 0], [2, "422", 763, 444, 16, 14, 0], [2, "422", 772, 447, 16, 14, 0], [2, "1471", 868, 372, 44, 39, 0], [2, "1471", 884, 380, 44, 39, 0], [2, "1471", 839, 385, 44, 39, 0], [2, "1471", 858, 393, 44, 39, 0], [2, "1471", 817, 396, 44, 39, 0], [2, "1471", 838, 406, 44, 39, 0], [2, "1471", 683, 270, 44, 39, 0], [2, "1471", 703, 282, 44, 39, 0], [2, "1471", 659, 281, 44, 39, 0], [2, "1471", 679, 293, 44, 39, 0], [2, "1471", 637, 292, 44, 39, 0], [2, "1471", 657, 304, 44, 39, 0], [2, "220_1", 622, 247, 40, 29, 2], [2, "422", 1134, 400, 16, 14, 0], [2, "420", 1148, 401, 16, 13, 0], [2, "969", 919, 154, 36, 30, 2], [2, "1368", 773, 382, 50, 42, 0], [2, "1368", 826, 348, 50, 42, 0], [2, "1368", 897, 333, 50, 42, 2], [2, "422", 757, 420, 16, 14, 0], [2, "422", 646, 354, 16, 14, 0], [2, "422", 690, 330, 16, 14, 0], [2, "422", 738, 304, 16, 14, 0], [2, "1471", 1180, 19, 44, 39, 0], [2, "1471", 1171, -7, 44, 39, 0], [2, "1471", 1189, 7, 44, 39, 0]]}, {"type": 2, "data": [62, -1, 64, 62, -1, -1, -1, 66, -1, -1, 63, 64, 62, 63, 64, 62, 62, 62, 62, -1, -1, 34, 33, 34, 35, -1, -1, -1, 33, 33, 33, 33, 33, 33, 33, -1, -1, -1, 62, 63, 62, 63, 62, 62, 63, -1, -1, -1, -1, 63, -1, -1, -1, -1, -1, -1, -1, -1, 62, 62, 63, 64, 63, 64, 67, 62, 62, -1, -1, -1, 33, 34, 35, 36, 33, 34, -1, -1, -1, 34, 33, 33, 33, -1, -1, -1, 66, 66, 65, 66, 65, 66, 65, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, -1, 62, 63, -1, 63, 65, 65, 62, 63, 64, 67, 62, -1, -1, -1, -1, -1, 35, 36, 33, 34, 35, -1, -1, -1, 35, 36, 33, 33, -1, -1, 63, 66, 65, 66, 67, 62, 62, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 62, 63, 65, 62, 63, 64, 65, 66, 67, 62, -1, -1, 36, 33, 34, 34, 36, 33, 35, 36, 35, 36, 35, 36, 33, 33, 35, 36, 33, -1, 66, 67, 62, 63, 64, 62, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 66, 62, 62, 63, 65, 66, 67, 62, 63, -1, -1, 33, 34, 36, 35, 36, 34, 33, 33, 34, 33, 33, 33, 33, 33, 33, 34, 35, 36, 36, -1, 64, 62, 65, 62, 63, 64, 66, 67, 62, -1, -1, -1, -1, -1, -1, -1, 62, 62, 63, 62, 63, 62, 63, 65, 66, 62, -1, -1, 33, 34, 35, 36, 13, 13, 33, 33, 34, 35, 36, 33, 33, 33, 34, 33, 33, -1, -1, -1, -1, -1, -1, -1, 64, 65, 66, 67, 62, 63, 64, 66, 67, -1, -1, -1, -1, -1, 65, 62, 62, 63, 62, 63, 64, 67, -1, -1, -1, 33, 34, 36, 34, 36, 13, 13, 35, 35, 36, 33, -1, 33, 33, 33, 33, 34, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 67, 65, 66, 62, 62, 63, 65, 66, -1, -1, -1, 62, 62, 65, 66, 65, 62, -1, -1, -1, 68, 35, 35, 36, 35, 36, 13, 13, 13, 33, -1, -1, -1, -1, -1, 36, 33, 34, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, 63, 62, 63, 64, 62, 63, 62, 63, -1, 62, 63, 62, 63, 62, -1, -1, 68, 68, 70, 71, 68, 35, 33, 34, 34, 13, 13, 35, -1, -1, 35, 36, 35, 33, 34, 36, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 62, 63, 62, 63, 65, 66, 62, 63, 64, 65, 66, 65, -1, -1, -1, 68, 69, 68, 69, 70, 70, 71, 68, 36, 36, 13, 13, -1, 36, 36, 35, 36, 33, 35, 36, 37, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 65, 66, -1, -1, 62, 63, 65, 66, 67, 62, -1, -1, 68, 68, 69, 70, 71, 70, 71, 69, 68, 69, 68, 69, 68, 13, 13, 33, 34, 33, 34, 34, 35, 33, 34, 33, 37, 37, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 66, -1, 65, 68, 69, 69, 71, 71, 68, 69, 70, 71, 70, 71, 70, 71, 73, 68, 13, 35, 33, 34, 33, 34, 33, 33, 34, 33, 37, 37, 37, 37, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, -1, -1, 70, 71, 69, 68, 69, 70, 68, 68, 69, 70, 68, 66, 62, 62, 73, 68, 33, 35, 36, 35, 33, 34, 35, 36, 37, 37, 37, 37, 37, 37, 37, 62, 63, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, -1, 70, 68, 68, 69, 68, 69, 70, 71, 66, 65, 66, 65, 65, 66, 67, 33, 33, 33, 34, 35, 36, 37, 37, 37, 37, 37, 37, 37, 62, 63, 65, 62, 65, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, 35, 36, 70, 71, 70, 71, 65, 66, 64, 62, 63, 64, 63, 62, 63, 64, 73, -1, 33, 33, 33, 34, 37, 37, 37, 37, 62, 63, 63, 66, 63, 62, 63, 65, 66, 37, -1, -1, -1, -1, -1, -1, -1, 37, 37, -1, -1, 36, -1, 35, 36, 13, 68, 68, 62, 63, 65, 66, 67, 65, 66, 67, 62, 65, 62, 63, 64, 73, 68, 13, 35, 36, 33, -1, 62, 63, 63, 62, 62, 62, 63, 65, 66, -1, 37, 37, 37, 37, -1, -1, -1, -1, -1, 37, 37, 37, 37, 25, 33, 34, 33, 34, 73, 66, 65, 66, 66, 67, 66, 67, 65, 66, 65, 66, 65, 62, 63, 64, 73, 68, 68, 68, 65, 66, 65, 66, 62, 62, 63, 65, 66, 37, 37, 37, 37, 37, 37, 37, 37, -1, -1, 37, 37, 37, 37, 37, 37, -1, -1, 34, 68, 69, 73, 73, 66, 67, 62, 63, 64, 62, 63, 62, 63, 64, 65, 65, 66, 67, 64, 64, 62, 62, 63, 62, 65, 66, 65, 65, 66, 37, 37, 37, 37, 37, 37, 37, 37, 33, 33, 37, 37, 37, 37, 33, 34, 33, 33, 35, 35, 68, 70, 71, 71, 68, 73, 64, 65, 66, 67, 65, 66, 65, 66, 67, 65, 66, 67, 66, 67, 62, 63, 64, 64, 63, 64, 66, 67, 36, 34, 37, 37, 37, 37, 37, 37, 33, 34, 33, 33, 34, 33, 34, 34, 35, 36, 33, 33, -1, -1, -1, 71, -1, 69, 68, 69, 68, 73, -1, 64, 65, 62, 63, 64, 67, 62, 63, 64, 64, 62, 65, 66, 67, 62, 63, 64, 66, 68, 33, 34, 33, 33, 37, 37, 33, 34, 35, 36, 35, 35, 36, 35, 36, 36, 36, 35, 13, 13, -1, -1, -1, -1, 70, 71, 70, 68, 68, 69, 73, 73, 64, 65, 66, 67, 62, -1, -1, -1, -1, -1, 63, 64, 62, 65, 66, 67, 62, 68, 35, 36, 35, 35, 36, 33, 34, 33, 34, 33, 34, 35, 36, 35, 36, 35, 13, 13, 13, 13, -1, -1, -1, -1, -1, 68, 69, 68, 70, 71, 68, 73, 73, 65, 62, 63, 64, -1, -1, -1, -1, -1, 63, 64, 62, 63, 64, 62, 63, 68, 13, 13, 33, 34, 34, 33, 34, 33, 34, 35, 36, 33, 34, 36, 33, 33, 34, 34, 33, 34, -1, -1, -1, -1, -1, 69, 68, 70, 71, 68, 73, 66, 67, 64, 65, 66, 67, 62, 65, 66, 67, 64, 66, 67, 65, 66, 67, 62, 63, 68, 13, 13, 35, 36, 36, 33, 34, 35, 36, 33, 34, 33, 34, 33, 34, 35, 36, 36, 35, 33, -1, -1, -1, -1, 70, 68, 69, 71, 68, 73, 62, 63, 62, 63, 64, 66, 67, 65, 65, 66, 67, 62, 63, 64, 64, 62, 62, 65, 72, 68, 13, 13, 33, 33, 34, 33, -1, -1, -1, 34, 36, 33, 34, 35, 36, 33, 34, 33, -1, -1, -1, -1, -1, 68, 69, 70, 71, 68, 73, 62, 63, 64, 65, 66, 67, 64, 65, 66, 67, 63, 64, 65, 66, 67, 66, 62, 63, 72, 68, 34, 33, 34, 33, 35, 36, -1, -1, -1, -1, -1, -1, 34, 33, 34, 33, 34, 36, 35, -1, -1, 68, 68, 69, 70, 71, 68, 73, 62, 63, 64, 66, 67, 63, 64, 66, 67, 63, 64, 64, 66, 67, 62, 63, 64, 62, 65, 73, 68, 33, 34, 35, 36, 35, 33, -1, -1, -1, -1, -1, -1, -1, 36, 33, 34, 35, 36, 34, -1, -1, -1, 68, 69, 71, 68, 73, 62, 62, 65, 66, 67, 67, 65, 66, 73, 68, 68, 62, 63, 63, 64, 64, 65, 62, 63, 62, 63, 68, 35, 35, 36, 33, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 33, 34, 33, -1, 35, 36, 70, 71, -1, 62, 62, 62, 63, 64, 66, 67, 64, 73, 68, 13, 33, 13, 68, 73, 68, 69, 73, 64, 62, 63, 68, 33, 34, 34, 35, 36, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, 35, 36, 35, 33, 33, 34, 72, 62, 62, 62, 63, 64, 66, 67, 66, 73, 68, 72, -1, 19, 13, 13, 13, 68, 73, 73, 68, 68, 68, 68, 13, 35, 36, 36, 35, 36, 34, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 34, 33, 35, 35, 36, 62, 62, 63, 65, 66, 67, 67, 73, 68, 34, 13, -1, -1, -1, -1, -1, 13, 13, 13, 13, 18, 4, 4, 29, 32, 35, 36, 33, 35, 35, 36, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 36, 35, 35, 33, 34, 62, 65, 66, 67, 64, 73, 68, 33, 33, 34, 13, -1, -1, -1, -1, -1, -1, -1, 19, 33, 33, -1, -1, 4, 12, 35, 36, 35, 33, 34, 35, 36, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 33, 33, 34, 33, 33, 62, 62, 62, 73, 68, 32, 33, 34, 35, 36, 13, 13, -1, -1, -1, -1, -1, -1, -1, 35, 33, -1, 4, 4, 29, 22, 32, 13, 13, 33, 34, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 36, 35, 35, 33, 35, 35, 62, 73, 68, 14, 11, 12, 34, 36, 33, 34, 33, 34, -1, -1, -1, -1, -1, -1, -1, 33, 35, 36, 14, 15, 0, 1, 29, 32, 13, 13, 26, 22, 22, 27, 4, 4, -1, -1, -1, -1, -1, 33, 34, 33, 34, 33, 34, 35, 35, 36, 68, -1, -1, 13, 18, 21, 32, 34, 35, 36, 35, 36, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, 33, 14, 15, 4, 4, 29, 28, 28, 27, 4, 4, 4, 4, 9, 20, 35, -1, -1, -1, 33, 34, 33, 34, 35, 36, 35, 26, 22, -1, -1, -1, 13, 18, 4, 24, 36, 33, 34, 34, 26, 23, 4, 4, -1, -1, -1, -1, 33, 34, 33, 35, 13, 14, 15, 8, 3, 4, 3, 4, 4, 17, 16, 15, 21, 28, 32, 33, 33, 34, 35, 36, 35, 36, 26, 22, 22, 27, 4, 34, 34, -1, 33, 18, 4, 21, 22, 22, 22, 28, 27, 4, 17, 20, 13, -1, 33, 34, 35, 36, 33, 33, 34, 33, 14, 16, 15, 4, 9, 16, 16, 20, 35, 14, 10, 15, 29, 28, 32, 36, 34, 35, 26, 22, 23, 4, 4, 4, 9, 13, 34, 26, 22, 23, 4, 4, 4, 4, 4, 4, 4, 9, 20, 36, 33, 34, 35, 33, 34, 33, 35, 35, 36, 35, 36, 26, 27, 17, 20, 35, 36, -1, 13, 13, 13, 14, 10, 11, 21, 22, 22, 22, 23, 4, 4, 4, 17, 16, 20, 13, 26, 23, 4, 4, 4, 4, 4, 17, 16, 16, 16, 20, 13, 13, 35, 36, 13, 35, 36, 35, 33, 34, 33, 33, 26, 23, 17, 20, 34, 33, 34, 33, 34, 13, 36, 13, 13, 14, 16, 16, 16, 15, 4, 4, 4, 4, 24, 19, 26, 28, 27, 4, 4, 4, 4, 17, 10, 20, 13, 13, 13, 34, 13, 13, 26, 32, 33, 35, 33, 34, 33, 34, 33, 26, 23, 17, 20, 33, 34, 34, 33, 34, 36, 13, 13, 13, 13, 13, 33, 35, 36, 14, 10, 10, 15, 4, 21, 22, 23, 4, 4, 4, 4, 17, 16, 20, 34, 13, 13, 13, 34, 34, 13, 26, 23, 21, 22, 32, 35, 36, 35, 36, 26, 23, 4, 12, 36, 35, 36, 36, 35, 36, 33, 34, 13, 13, 13, 13, 33, 34, 34, 36, 33, 34, 14, 15, 4, 4, 4]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}