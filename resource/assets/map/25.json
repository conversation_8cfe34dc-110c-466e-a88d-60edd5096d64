{"mW": 840, "mH": 840, "tW": 24, "tH": 24, "tiles": [["106_4", 0, 3, 3], ["139", 0, 1, 1], ["141", 0, 1, 1], ["142", 0, 3, 4], ["140", 2, 3, 4], ["142", 2, 3, 4], ["140", 0, 3, 4], ["140", 1, 3, 4], ["142", 1, 3, 4], ["203", 0, 2, 1], ["203_2", 0, 2, 1], ["203_3", 0, 2, 1], ["203_4", 0, 2, 1], ["203_1", 0, 2, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["311_1", 0, 3, 2], ["311_1", 2, 3, 2], ["311_1", 1, 3, 2], ["311_1", 3, 3, 2], ["142", 3, 3, 4]], "layers": [{"type": 3, "obj": [[2, "224", 694, 750, 124, 194, 0], [2, "224", 606, 706, 124, 194, 0], [2, "2", 247, 679, 90, 66, 2], [2, "2", 307, 722, 90, 66, 2], [2, "2", 210, 739, 90, 66, 0]]}, {"type": 4, "obj": [[2, "19", 536, 21, 44, 123, 0], [2, "224", 185, -7, 124, 194, 2], [2, "219_2", 219, 163, 36, 30, 0], [2, "263_1", 310, 259, 34, 34, 0], [2, "224", 760, 223, 124, 194, 0], [2, "219_2", 821, 393, 36, 30, 2], [4, 1, 420, 447, 0, 4024], [2, "224", 711, 260, 124, 194, 0], [2, "219_2", 764, 427, 36, 30, 2], [2, "224", -56, 284, 124, 194, 0], [2, "224", 775, 289, 124, 194, 2], [2, "219_2", 809, 459, 36, 30, 0], [2, "224", -44, 351, 124, 194, 0], [2, "4_2", 560, 436, 122, 119, 0], [2, "219_2", 616, 530, 36, 30, 0], [4, 2, 537, 646, 1, 4024], [2, "224", 319, 464, 124, 194, 2], [2, "219_2", 345, 633, 36, 30, 0], [2, "219_2", 38, 667, 36, 30, 0], [2, "224", -24, 515, 124, 194, 0], [2, "224", 82, 520, 124, 194, 0], [2, "219_2", 7, 686, 36, 30, 0], [2, "219_2", 143, 686, 36, 30, 2], [2, "14", 73, 702, 32, 30, 2], [2, "14", 45, 709, 32, 30, 2], [2, "219_2", 70, 713, 36, 30, 0], [2, "224", 46, 553, 124, 194, 0], [2, "219_2", 102, 720, 36, 30, 2], [2, "14", 49, 724, 32, 30, 2], [4, 3, 84, 759, 0, 4024], [2, "4_2", 235, 717, 122, 119, 0], [2, "219_2", 301, 810, 36, 30, 2]]}, {"type": 3, "obj": [[2, "213_2", 794, 644, 64, 45, 0], [2, "213_2", 167, 637, 64, 45, 0], [2, "208_2", 635, 28, 78, 40, 1], [2, "208_2", 743, 46, 78, 40, 3], [2, "213_2", 783, 346, 64, 45, 2], [2, "208_2", 520, 28, 78, 40, 1], [2, "208_2", 236, 179, 78, 40, 3], [2, "214_2", 334, 64, 54, 40, 0], [2, "213_2", 397, 23, 64, 45, 0], [2, "214_2", 358, 118, 54, 40, 0], [2, "214_2", 382, 132, 54, 40, 0], [2, "205_2", 456, 37, 54, 40, 2], [2, "208_2", 384, 97, 78, 40, 0], [2, "152_2", 382, 117, 76, 40, 0], [2, "208_2", 409, 57, 78, 40, 1], [2, "208_2", 464, 20, 78, 40, 3], [2, "205_2", 395, 3, 54, 40, 0], [2, "208_2", 372, 49, 78, 40, 0], [2, "152_2", 346, 86, 76, 40, 2], [2, "152_2", 328, 30, 76, 40, 2], [2, "208_2", 429, 10, 78, 40, 0], [2, "213_2", 212, 649, 64, 45, 2], [2, "213_2", 463, 369, 64, 45, 2], [2, "213_2", 397, 549, 64, 45, 2], [2, "214_2", 739, 356, 54, 40, 0], [2, "213_2", 316, 601, 64, 45, 0], [2, "213_2", 364, 599, 64, 45, 2], [2, "208_2", 316, 568, 78, 40, 1], [2, "208_2", 314, 576, 78, 40, 0], [2, "208_2", 280, 555, 78, 40, 1], [2, "208_2", 310, 557, 78, 40, 1], [2, "152_2", 384, 533, 76, 40, 2], [2, "214_2", 330, 283, 54, 40, 2], [2, "213_2", 130, 321, 64, 45, 0], [2, "213_2", 165, 317, 64, 45, 0], [2, "213_2", 231, 322, 64, 45, 0], [2, "213_2", 428, 375, 64, 45, 2], [2, "214_2", 604, 270, 54, 40, 2], [2, "213_2", 537, 216, 64, 45, 2], [2, "213_2", 276, 339, 64, 45, 0], [2, "214_2", 582, 324, 54, 40, 2], [2, "214_2", 798, 36, 54, 40, 0], [2, "213_2", 783, 15, 64, 45, 0], [2, "214_2", 318, 301, 54, 40, 2], [2, "213_2", 207, 325, 64, 45, 0], [2, "214_2", 376, 324, 54, 40, 2], [2, "213_2", 317, 372, 64, 45, 0], [2, "213_2", 365, 367, 64, 45, 2], [2, "208_2", 323, 166, 78, 40, 3], [2, "208_2", 500, 317, 78, 40, 2], [2, "208_2", 526, 249, 78, 40, 3], [2, "208_2", 479, 213, 78, 40, 3], [2, "208_2", 355, 352, 78, 40, 3], [2, "208_2", 796, 2, 78, 40, 2], [2, "205_2", 552, 209, 54, 40, 2], [2, "208_2", 534, 268, 78, 40, 2], [2, "174", 64, 762, 68, 33, 0], [2, "174", 183, 187, 68, 33, 0], [2, "174", 184, 688, 68, 33, 0], [2, "174", -3, 801, 68, 33, 0], [2, "152_2", 563, 292, 76, 40, 0], [2, "164", 607, 295, 60, 30, 2], [2, "152_2", 584, 244, 76, 40, 0], [2, "166", 576, 202, 30, 35, 2], [2, "208_2", 478, 220, 78, 40, 2], [2, "208_2", 317, 339, 78, 40, 1], [2, "208_2", 315, 347, 78, 40, 0], [2, "208_2", 130, 297, 78, 40, 2], [2, "208_2", 200, 301, 78, 40, 0], [2, "165", 623, 238, 42, 37, 2], [2, "205_2", 380, 151, 54, 40, 0], [2, "208_2", 278, 315, 78, 40, 1], [2, "208_2", 311, 328, 78, 40, 1], [2, "152_2", 355, 314, 76, 40, 2], [2, "205_2", 783, -4, 54, 40, 0], [2, "208_2", 274, 277, 78, 40, 3], [2, "208_2", 304, 269, 78, 40, 3], [2, "36", 224, 587, 140, 103, 2], [2, "213_2", 187, 624, 64, 45, 2], [2, "152_2", 176, 603, 76, 40, 0], [2, "213_2", 725, 365, 64, 45, 2], [2, "213_2", 747, 335, 64, 45, 0], [2, "214_2", 810, 73, 54, 40, 2], [2, "213_2", 751, 383, 64, 45, 2], [2, "208_2", 779, 100, 78, 40, 3], [2, "208_2", 778, 324, 78, 40, 2], [2, "152_2", 793, 63, 76, 40, 2], [2, "205_2", 738, 325, 54, 40, 0], [2, "152_2", 721, 342, 76, 40, 2], [2, "205_2", 706, 377, 54, 40, 2], [2, "208_2", 190, 635, 78, 40, 3], [2, "213_2", 596, 575, 64, 45, 0], [2, "213_2", 638, 565, 64, 45, 2], [2, "208_2", 622, 546, 78, 40, 2], [2, "205_2", 652, 541, 54, 40, 2], [2, "208_2", 553, 550, 78, 40, 2], [2, "213_2", 391, 380, 64, 45, 2], [2, "213_2", 556, 571, 64, 45, 2], [2, "213_2", 445, 566, 64, 45, 0], [2, "213_2", 490, 565, 64, 45, 2], [2, "152_2", 553, 549, 76, 40, 0], [2, "208_2", 482, 545, 78, 40, 3], [2, "208_2", 442, 540, 78, 40, 0], [2, "213_2", 515, 574, 64, 45, 2], [2, "205_2", 522, 557, 54, 40, 2], [2, "213_2", 127, 74, 64, 45, 2], [2, "214_2", 222, 138, 54, 40, 2], [2, "214_2", 176, 85, 54, 40, 2], [2, "208_2", -6, 47, 78, 40, 1], [2, "208_2", 60, 44, 78, 40, 1], [2, "208_2", 218, 158, 78, 40, 3], [2, "205_2", 92, 82, 54, 40, 2], [2, "208_2", 144, 98, 78, 40, 0], [2, "208_2", 106, 49, 78, 40, 0], [2, "208_2", 137, 45, 78, 40, 0], [2, "152_2", 200, 121, 76, 40, 2], [2, "205_2", 177, 64, 54, 40, 2], [2, "213_2", 643, 577, 64, 45, 2], [2, "63", 809, 731, 16, 31, 2], [2, "64", 825, 731, 14, 15, 0], [2, "64", 819, 747, 14, 15, 2], [2, "62", 828, 748, 16, 27, 0], [2, "62", 792, 810, 16, 27, 0], [2, "5", 316, 793, 42, 66, 2], [2, "5", 273, 793, 42, 66, 0], [2, "208_2", 112, 605, 78, 40, 2], [2, "213_2", 124, 625, 64, 45, 2], [2, "213_2", 13, 621, 64, 45, 0], [2, "213_2", 58, 616, 64, 45, 2], [2, "152_2", 121, 605, 76, 40, 0], [2, "208_2", 50, 601, 78, 40, 3], [2, "208_2", 10, 596, 78, 40, 0], [2, "213_2", 81, 629, 64, 45, 2], [2, "205_2", 90, 613, 54, 40, 2], [2, "166", 112, 603, 30, 35, 2], [2, "174", 374, 801, 68, 33, 0], [2, "174", 185, 805, 68, 33, 0], [2, "174", 266, 532, 68, 33, 0], [2, "174", 17, 503, 68, 33, 2], [2, "208_2", 283, 170, 78, 40, 1], [2, "219_2", 467, 523, 36, 30, 0], [2, "219_2", 622, 610, 36, 30, 0], [2, "214_2", 500, 372, 54, 40, 2], [2, "152_2", 432, 355, 76, 40, 0], [2, "205_2", 397, 363, 54, 40, 2], [2, "208_2", 713, 400, 78, 40, 0], [2, "213_2", 539, 345, 64, 45, 2], [2, "152_2", 532, 321, 76, 40, 2], [2, "205_2", 500, 350, 54, 40, 2], [2, "213_2", 784, 369, 64, 45, 2], [2, "208_2", 704, 20, 78, 40, 0], [2, "36", 647, 570, 140, 103, 0], [2, "213_2", 757, 613, 64, 45, 0], [2, "213_2", 797, 618, 64, 45, 2], [2, "208_2", 794, 593, 78, 40, 3], [2, "208_2", 754, 588, 78, 40, 0], [2, "213_2", 732, 639, 64, 45, 0], [2, "205_2", 735, 622, 54, 40, 0], [2, "208_2", 130, 345, 78, 40, 0], [2, "205_2", 111, 321, 54, 40, 2], [2, "205_2", 588, 17, 54, 40, 2], [2, "213_2", 388, 589, 64, 45, 2], [2, "208_2", 354, 581, 78, 40, 3], [2, "205_2", 398, 559, 54, 40, 2], [2, "213_2", 790, 744, 64, 45, 2], [2, "214_2", 793, 724, 54, 40, 0], [2, "214_2", 722, 687, 54, 40, 0], [2, "213_2", 712, 662, 64, 45, 0], [2, "213_2", 762, 684, 64, 45, 0], [2, "213_2", 752, 671, 64, 45, 0], [2, "214_2", 765, 714, 54, 40, 0], [2, "208_2", 745, 646, 78, 40, 0], [2, "208_2", 770, 665, 78, 40, 0], [2, "205_2", 823, 679, 54, 40, 2], [2, "208_2", 787, 705, 78, 40, 2], [2, "205_2", 780, 728, 54, 40, 0], [2, "214_2", 812, 798, 54, 40, 0], [2, "174", 277, 624, 68, 33, 1], [2, "174", 333, 672, 68, 33, 0], [2, "219_2", 549, 371, 36, 30, 2], [2, "220_2", 172, 363, 40, 29, 0], [2, "219_2", 405, 619, 36, 30, 2], [2, "208_2", -49, 606, 78, 40, 3], [2, "174", 538, 765, 68, 33, 1], [2, "174", 594, 813, 68, 33, 0], [2, "174", 569, 644, 68, 33, 0], [2, "174", 663, 522, 68, 33, 0], [2, "174", 559, 520, 68, 33, 0], [2, "174", 757, 460, 68, 33, 0], [2, "220_2", 206, 672, 40, 29, 0], [2, "220_2", -11, 645, 40, 29, 0], [2, "220_2", 350, 823, 40, 29, 0], [2, "62", 264, 665, 16, 27, 0], [2, "62", 638, 619, 16, 27, 0], [2, "62", 699, 656, 16, 27, 0], [2, "208_2", 805, 767, 78, 40, 0], [2, "263_1", 436, 518, 34, 34, 0], [2, "263_1", 414, 805, 34, 34, 0], [2, "152_2", 714, 635, 76, 40, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 109, 109, 97, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 116, 115, 110, 111, -1, -1, 11, 36, 35, -1, -1, -1, 11, 12, 36, 36, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 106, 111, -1, -1, -1, 14, 10, -105, 36, 35, 11, 81, 47, 48, 48, 23, -105, 35, 11, 80, 12, 12, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 79, 80, 80, 81, 35, 71, -114, -114, -114, -115, 71, 73, 59, 57, 28, 28, 23, -105, 74, 10, 47, 24, 24, 23, 36, 35, -1, -1, -1, -1, -1, -1, -1, 11, 10, 10, 10, 45, -114, -115, -1, -1, -1, -1, -1, -1, -1, 74, 62, 9, 9, 30, 10, 74, 10, 65, 33, 60, 31, 10, -105, 35, -1, -1, -1, -1, -1, -1, 71, 72, 72, 73, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 59, 60, 32, 34, -114, 71, 21, 65, 29, 10, 10, 10, 45, -115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 81, 10, 10, 10, -114, -115, -1, 74, 58, 34, -112, 71, -114, -115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 36, 35, -1, -1, 71, 72, -1, -1, 74, 10, 10, 10, -114, -115, -1, -1, 17, 10, 45, -115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, 11, 81, 10, 10, -105, 35, -1, 101, -1, 100, 99, 74, 10, 10, 44, -1, -1, -1, -1, -1, 74, -112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, 98, 71, 72, 72, -114, -114, -115, -1, 109, 100, 115, 114, 71, -111, -112, 100, 99, 100, 99, -1, -1, 71, -115, 101, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, -1, -1, -1, -1, -1, 109, 109, 109, 110, 111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 116, 109, 109, 109, 109, 109, 109, 109, 109, -1, -1, 97, 98, -1, -1, -1, -1, -1, 106, 115, 110, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 106, 116, 109, 110, 106, 106, 116, 109, 109, 109, 109, 109, 109, -1, 109, -1, 109, 110, 112, 111, -1, -1, -1, -1, 94, -1, 115, -1, -1, -1, -1, 11, 12, 36, 35, -1, 105, 106, 107, -1, -1, 105, 106, 112, 116, 109, 109, 109, 109, 109, 109, 110, 107, -1, -1, -1, -1, -1, -1, -1, 115, -1, 115, 115, -1, -1, 71, 76, 10, -105, 35, -1, -1, 11, 12, 13, 36, 36, 35, 113, 112, 106, 112, 112, 112, 106, 107, -1, -1, -1, -1, -1, -1, 71, -1, 108, 115, 115, 115, 109, 100, 99, 77, 10, 10, -105, -104, 11, 16, 47, 49, 24, 23, 38, -1, -1, 11, 12, 12, 12, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, 105, 106, 116, 111, 97, 110, 111, 74, 12, 35, 45, -115, 14, 10, 50, 9, 9, 26, 38, 35, -1, 14, 47, 48, 24, 23, -105, -106, -104, -1, -1, -1, 11, 12, 36, 35, -1, 105, 116, 97, -1, -1, 71, 72, 44, -1, -1, 14, 70, 9, 33, 32, 34, -1, -1, -1, 74, 59, 56, 60, 34, 45, -114, -114, -115, -1, -1, 71, 10, 10, -105, 36, -104, 105, 97, 99, 99, -1, -1, -1, -1, 11, 16, 50, 9, 26, 45, 43, 93, 94, 95, 71, 72, 73, -114, -114, -115, -1, 101, 100, 99, 100, 99, 71, 14, 10, 10, 38, -1, 97, 102, 102, -1, -1, -1, -1, -1, 47, 69, 9, 26, 38, -1, 105, 106, 97, 98, -1, -1, -1, -1, -1, -1, -1, 105, 106, 107, 109, 99, 71, 10, 10, 41, -1, 97, 97, 114, -1, -1, -1, -1, 74, 59, 60, 61, 34, 38, -1, 116, 115, -1, 109, 109, 109, -1, 109, 109, 109, 109, 109, 109, 109, 109, 109, -1, -1, -1, -1, -1, 105, 106, 107, 94, 95, -1, -1, 71, 75, 10, 10, 10, 38, -1, 113, 109, 109, 109, 109, 109, 109, 109, 109, 109, 109, 109, 109, 109, 110, 106, -1, -1, -1, -1, -1, 97, -1, 103, 109, 102, -1, 109, 109, 109, 74, 10, 10, 10, 37, 109, 109, 110, 105, 112, 116, 109, 110, 112, 106, 112, 116, 110, 106, 107, -1, -1, -1, -1, -1, -1, 103, 103, 109, 109, 109, 109, 109, 109, 109, 71, 21, 67, 68, 24, 36, 36, 36, 35, -1, 113, 112, 111, 11, 36, 35, 113, 111, 47, 33, 80, 36, 35, 97, 97, 97, 116, 109, 109, 110, 116, 109, 109, 109, 109, 109, 74, -1, -1, 57, 28, 25, 48, 23, 37, 37, 36, 12, 81, 10, -105, 12, 81, 50, 30, 38, -114, -115, 93, 97, 97, 105, 116, 97, 98, 104, 115, 115, 110, 111, 14, 70, 52, -1, 69, 9, 9, 9, 9, 25, 24, 23, 10, 10, 10, 10, 47, 69, 9, 26, -112, 108, -1, 105, 108, 109, 93, 94, 94, 110, 107, 112, 106, 107, 80, 10, 35, -1, 42, 17, 59, 61, 60, 57, 9, 9, 28, 25, 24, 25, 49, 69, 9, 9, 26, -115, 105, 116, 115, 105, 106, 105, 106, 106, 111, -1, -1, 11, 13, 47, 25, -1, 10, -1, -1, 19, 44, 74, 53, 9, 9, 9, 9, 9, 9, 9, 33, 32, 10, 36, 36, 36, 113, 116, -1, -1, -1, -1, -1, -1, 11, 81, 16, 47, 52, 33, 34, 44, -1, 100, 99, -1, 18, 55, 56, 60, 60, 63, 60, 60, 60, 32, 10, 10, 10, -113, -114, -115, 113, 116, 105, -1, 11, 12, 37, 81, 10, 70, 33, 61, 34, 46, -1, -1, 97, 107, -1, 71, 75, 75, 76, 10, 10, 10, 10, 10, 10, 10, 10, -114, -115, -1, -1, 101, 116, 35, 11, 81, 10, 10, 10, 10, 58, 34, 73, 46, 93, 94, -1, 97, 97, 100, 99, 93, 99, 22, 21, 76, 76, 10, 10, 10, 45, 44, -1, -1, -1, 93, 104, 14, 103]}, {"type": 3, "obj": [[2, "313", 789, 466, 70, 44, 0], [2, "313", 783, 567, 70, 44, 0], [2, "313", 211, 572, 70, 44, 2], [2, "313", 569, 351, 70, 44, 2], [2, "313", 494, 385, 70, 44, 2], [2, "313", 545, 388, 70, 44, 0], [2, "313", 653, 382, 70, 44, 2], [2, "313", 90, 461, 70, 44, 2], [2, "313", 351, 641, 70, 44, 2], [2, "313", 649, 636, 70, 44, 0], [2, "313", 315, 808, 70, 44, 2], [2, "313", 251, 808, 70, 44, 0], [2, "313", 428, 601, 70, 44, 2], [2, "313", 0, 719, 70, 44, 2], [2, "313", 616, 609, 70, 44, 0]]}, {"type": 2, "data": [83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 0, 1, 2, 0, 1, 2, 91, 91, 91, 91, 91, 91, 91, 91, 91, 91, 5, -1, -1, 4, -1, 0, 6, 7, 8, 6, 7, 6, 7, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 4, 5, 92, 92, 92, 92, 92, 92, 92, 92, 92, 2, 0, 1, 2, 5, 1, 2, 0, 1, 2, 7, 8, 5, 6, 3, 4, 5, 3, 4, 0, 3, 4, 5, 6, 7, 8, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 3, 117, 124, 123, 4, 117, 118, 118, 119, 6, 7, 0, 1, 2, 7, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 1, 0, 1, 2, 85, 85, 85, 85, 85, 85, 6, 7, 8, -125, 2, -124, 127, 122, 122, 123, 6, 3, 4, 5, 5, 3, 4, 5, 3, 3, 4, 5, 3, 4, 5, 4, 3, 4, 5, 2, 85, 85, 85, 85, 85, 85, 8, 117, 118, 123, -124, -116, -122, -120, -121, 0, 6, 7, 8, 8, 6, 7, 8, 6, 6, 7, 8, 6, 7, 8, 7, 6, 7, 8, 5, 85, 85, 85, 4, 5, 5, -118, -126, -120, -121, -119, -120, -121, 4, 5, 3, 4, 5, 3, 6, 6, 7, 3, 3, 6, 7, 8, 0, 1, 2, 1, 2, 6, 7, 8, 4, 5, 4, 7, 8, -127, -121, 2, 0, 1, 2, 2, 1, 0, 1, 117, 118, 119, 6, 0, 3, 4, 6, 6, 1, 2, 0, 3, 4, 5, 4, 5, 6, 7, 8, 7, 8, 7, 0, 1, 2, 0, 1, 2, 4, 5, 8, 0, 3, 4, -127, -126, -125, 2, 6, 6, 0, 1, 2, 0, 1, 2, 6, 7, 8, 2, 8, 1, 125, 124, 123, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 6, 0, 1, 2, 0, 0, 1, 2, 3, 4, 5, 3, 4, 5, 5, 3, 4, 5, 5, 0, 120, -117, 126, 7, 0, 3, 4, 5, 6, 7, 8, 3, 4, 5, 0, 1, 2, 4, 5, 3, 3, 0, 1, 2, 7, 8, 6, 7, 8, 8, 6, 7, 8, 8, 1, -119, -120, -121, 8, 3, 6, 7, 8, 6, 7, 8, 6, 7, 8, 3, 4, 5, 7, 8, 6, 6, 3, 4, 5, 8, 0, 5, 3, 4, 5, 4, 5, 2, -1, -1, 5, 3, 3, 4, 6, 3, 4, 0, 1, 2, 2, 6, 7, 6, 6, 7, 124, 123, 2, 0, 1, 6, 7, 8, 0, 3, 3, 6, 7, 8, 7, 8, 5, 1, 121, -1, -1, -1, -1, -1, 6, 7, 3, 4, 5, 5, 4, 117, 0, 1, 2, 124, 123, 0, 3, 4, 5, 7, 8, 1, 6, 118, 3, 4, 5, 125, 124, 121, 121, 121, 127, 127, 121, 121, 121, -1, -1, 6, 7, 8, 8, 7, 3, 3, 4, 5, 127, 126, 3, 6, 7, 8, 88, 88, 4, 5, 0, 1, 2, 2, -127, -116, -117, 121, 121, -122, -116, -117, -117, -116, -117, -117, 0, 1, 2, -1, 5, 6, 6, 7, 8, -120, -121, 6, 7, -124, 0, 1, 2, 88, 88, 3, 4, 5, 5, 4, -119, -120, -120, -120, -121, -119, -120, -121, 4, 3, 4, -124, -123, -123, -117, -117, -117, -117, -117, -122, 125, 124, 123, 1, -124, 3, 4, 5, -117, -117, 123, 7, 8, 8, 7, 8, 3, 4, 3, 4, 3, 3, 4, 7, 6, 7, 6, -127, -116, -117, -117, -122, -120, -117, 122, -128, 127, 126, 4, -127, -126, -116, -117, -117, -117, 126, 8, 7, 6, 7, 8, 6, 7, 6, 7, 6, 6, 7, 3, 117, 118, 118, 119, -119, -120, -120, -121, -119, -120, -120, -116, -117, -118, 7, 8, 6, -127, -126, -116, -117, -121, 1, 2, 0, 1, -127, -121, 1, 2, 0, 1, 2, 2, 6, -127, -126, -120, -121, 4, 5, 6, 7, 8, 8, 8, -119, -120, -118, 7, 8, 0, 1, 117, 118, 127, 124, 124, 123, 3, 4, 5, 3, 4, 5, 3, 4, 5, 5, 0, 117, 118, 119, 3, 7, 8, 1, 0, 0, 1, 2, 0, 117, 122, 118, 119, 3, 4, -127, -116, 127, 127, 127, 126, 6, 7, 8, 6, 7, 8, 6, 7, 8, 8, 3, 120, 121, 122, 123, 7, 0, 1, 2, 3, 117, 118, 118, -128, 121, 121, 119, 4, 7, 8, -119, -116, -122, -120, -121, 0, 1, 2, 0, 1, 2, 0, 1, 2, 2, 6, 7, 8, 127, 126, 0, 1, 2, 5, 0, 120, 121, 121, 121, 121, 127, 122, 123, 4, 125, 124, -128, -121, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 5, -127, -126, -120, 0, 0, 1, -116, -117, 8, 1, 2, 5, 4, 5, 2, -118, 121, 121, 122, -128, 127, -117, 6, 7, 8, 123, 7, 8, 6, 7, 8, 6, 7, 8, 8, 7, 8, 7, 3, 3, 4, -119, -116, -117, -118, -116, -117, -117, 121, 121, -120, -121, 6, 0, 0, -120, -121, 0, 1, 127, 126, 1, 6, 7, 0, 1, 4, 5, 3, 4, 5, 3, 4, 6, -122, 7, 8, -119, -120, -121, -124, -117, -117, -117, -117, -118, 1, 0, 3, 0, 1, 2, 3, 4, 5, 3, 4, -117, -117, -117, -117, -117, 0, 1, 2, 8, -119, -119, -120, -121, 4, 5, 117, 118, 118, -128, -120, -120, -120, -120, -121, 0, 3, 6, 3, 4, 0, 6, 117, 118, 123, 117, 118, -117, -117, -122, -121, 3, -122, 5, 0, 1, 2, 2, 6, 7, 8, -127, -126, -120, -121, 0, 1, 2, 0, -119, -116, 6, 0, 6, 7, 3, 5, 120, 121, 126, 4, 127, -122, -122, -121, -119, -120, -121, 8, 3, 4, 5, 5, 4, 5, 4, 5, 3, 4, 5, 3, 2, 5, 117, 118, -128, -123, 3, -124, -123, 6, 119, 6, -116, -117, -118, -120, -120, -121, 6, 7, 6, 7, 8, 6, 7, 8, 8, 7, 8, 7, 8, 6, 7, 8, 6, 7, 8, -127, 127, -117, -122, -116, -117, -126, 0, 122, 123, -119, -120, -121, 2, 3, 3, 4, 5, 1, 2, 2, 3, 4, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, -119, -120, -121, -124, -123, -116, 3, 127, 126, 5, 3, 4, 5, 3, 4, 7, 8, 4, 5, 5, 124, 123, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 0, -127, -126, -116, 6, -120, -121, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 5, 5, 122, 124, 124, 117, 124, 123, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 3, 117, 118, 127, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, -127, -126, 0, 1, -120, 121, 121, 127, 126, 7, 8, 0, 1, 2, 0, 1, 2, 0, 117, 124, 127, 127, 127, 127]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}