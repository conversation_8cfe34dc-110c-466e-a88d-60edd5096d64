{"mW": 960, "mH": 1080, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["373", 0, 1, 1], ["203_10", 0, 2, 1], ["203_11", 0, 2, 1], ["203_12", 0, 2, 1], ["203_11", 1, 2, 1], ["203_12", 1, 2, 1], ["203_10", 1, 2, 1], ["203_9", 0, 2, 1], ["203_9", 1, 2, 1], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1331", 0, 3, 3], ["1803", 0, 3, 3], ["3769", 0, 3, 2], ["3769", 2, 3, 2], ["3769", 1, 3, 2], ["3769", 3, 3, 2], ["3153", 0, 1, 1]], "layers": [{"type": 3, "obj": [[2, "516", 37, 71, 58, 52, 2], [2, "150", 165, 220, 36, 37, 0], [2, "151", 199, 192, 30, 25, 0], [2, "471", 246, 189, 52, 47, 0], [2, "472", 144, 214, 38, 27, 0], [2, "475", 226, 160, 30, 42, 0], [2, "473", 177, 186, 28, 38, 2], [2, "3226", 228, 190, 48, 45, 2], [2, "3226", 208, 200, 48, 45, 2], [2, "3226", 187, 211, 48, 45, 2], [2, "3226", 179, 215, 48, 45, 2], [2, "471", 179, 219, 52, 47, 0], [2, "3168", 910, 349, 96, 107, 0], [2, "3226", 103, 46, 48, 45, 2], [2, "3226", 123, 35, 48, 45, 2], [2, "3226", 81, 57, 48, 45, 2], [2, "3226", 30, 50, 48, 45, 2], [2, "3226", 53, 42, 48, 45, 2], [2, "3226", 2, 93, 48, 45, 2], [2, "3226", 10, 62, 48, 45, 2], [2, "3226", -41, 116, 48, 45, 2], [2, "3226", -18, 105, 48, 45, 2], [2, "3226", -61, 128, 48, 45, 2], [2, "150", 88, 236, 36, 37, 0], [2, "151", 122, 208, 30, 25, 0], [2, "471", 169, 205, 52, 47, 0], [2, "472", 67, 230, 38, 27, 0], [2, "475", 149, 176, 30, 42, 0], [2, "473", 100, 202, 28, 38, 2], [2, "3226", 151, 206, 48, 45, 2], [2, "3226", 131, 216, 48, 45, 2], [2, "3226", 110, 227, 48, 45, 2], [2, "3226", 102, 231, 48, 45, 2], [2, "471", 102, 235, 52, 47, 0], [2, "150", 17, 280, 36, 37, 0], [2, "151", 51, 252, 30, 25, 0], [2, "471", 98, 249, 52, 47, 0], [2, "472", -4, 274, 38, 27, 0], [2, "475", 78, 220, 30, 42, 0], [2, "473", 29, 246, 28, 38, 2], [2, "3226", 80, 250, 48, 45, 2], [2, "3226", 60, 260, 48, 45, 2], [2, "3226", 39, 271, 48, 45, 2], [2, "3226", 31, 275, 48, 45, 2], [2, "471", 31, 279, 52, 47, 0], [2, "150", 46, 32, 36, 37, 0], [2, "151", 80, 4, 30, 25, 0], [2, "471", 127, 1, 52, 47, 0], [2, "472", 25, 26, 38, 27, 0], [2, "475", 107, -28, 30, 42, 0], [2, "473", 58, -2, 28, 38, 2], [2, "3226", 109, 2, 48, 45, 2], [2, "3226", 89, 12, 48, 45, 2], [2, "3226", 68, 23, 48, 45, 2], [2, "3226", 60, 27, 48, 45, 2], [2, "471", 60, 31, 52, 47, 0], [2, "150", -18, 39, 36, 37, 0], [2, "151", 16, 11, 30, 25, 0], [2, "471", 63, 8, 52, 47, 0], [2, "472", -39, 33, 38, 27, 0], [2, "475", 43, -21, 30, 42, 0], [2, "473", -6, 5, 28, 38, 2], [2, "3226", 45, 9, 48, 45, 2], [2, "3226", 25, 19, 48, 45, 2], [2, "3226", 4, 30, 48, 45, 2], [2, "3226", -4, 34, 48, 45, 2], [2, "471", -4, 38, 52, 47, 0], [2, "150", -80, 97, 36, 37, 0], [2, "151", -46, 69, 30, 25, 0], [2, "471", 1, 66, 52, 47, 0], [2, "472", -101, 91, 38, 27, 0], [2, "475", -19, 37, 30, 42, 0], [2, "473", -68, 63, 28, 38, 2], [2, "3226", -17, 67, 48, 45, 2], [2, "3226", -37, 77, 48, 45, 2], [2, "3226", -58, 88, 48, 45, 2], [2, "3226", -66, 92, 48, 45, 2], [2, "471", -66, 96, 52, 47, 0], [2, "516", 116, 198, 58, 52, 2], [2, "556", 4, -7, 64, 52, 2], [2, "3168", -69, 365, 96, 107, 2], [2, "3168", 629, 1025, 96, 107, 0], [2, "180", 844, -1, 104, 84, 0]]}, {"type": 4, "obj": [[2, "3168", 636, 5, 96, 107, 0], [2, "3030", 153, 93, 38, 48, 2], [2, "3168", 190, 43, 96, 107, 2], [2, "3030", 190, 111, 38, 48, 2], [2, "3030", 227, 129, 38, 48, 2], [2, "3030", 263, 147, 38, 48, 2], [2, "1381", 671, 116, 54, 88, 0], [2, "1381", 671, 116, 54, 88, 0], [2, "3030", 300, 167, 38, 48, 2], [2, "3030", 335, 185, 38, 48, 2], [2, "3030", 335, 185, 38, 48, 2], [2, "3183", 325, 171, 44, 75, 0], [2, "3030", 365, 198, 38, 48, 2], [2, "3030", 372, 209, 38, 48, 0], [2, "3168", 370, 155, 96, 107, 0], [2, "3030", 335, 226, 38, 48, 0], [2, "3030", 300, 244, 38, 48, 0], [2, "357", 238, 207, 48, 91, 0], [2, "3168", 432, 194, 96, 107, 0], [2, "3749", 665, 237, 54, 70, 0], [2, "3030", 264, 263, 38, 48, 0], [2, "357", 171, 235, 48, 91, 0], [2, "3030", 229, 281, 38, 48, 0], [2, "357", 102, 265, 48, 91, 0], [2, "3771", 267, 268, 86, 103, 2], [2, "3030", 108, 333, 38, 48, 0], [2, "3168", 721, 281, 96, 107, 2], [2, "357", 29, 300, 48, 91, 0], [2, "3030", 73, 351, 38, 48, 0], [2, "3030", 37, 370, 38, 48, 0], [2, "3030", 2, 388, 38, 48, 0], [2, "3771", 118, 336, 86, 103, 2], [2, "475", 459, 402, 30, 42, 0], [2, "151", 432, 434, 30, 25, 0], [2, "473", 410, 428, 28, 38, 2], [2, "3226", 461, 432, 48, 45, 2], [2, "471", 479, 431, 52, 47, 0], [2, "472", 377, 456, 38, 27, 0], [2, "3226", 441, 442, 48, 45, 2], [2, "475", 377, 448, 30, 42, 0], [2, "3226", 420, 453, 48, 45, 2], [2, "150", 398, 462, 36, 37, 0], [2, "3226", 412, 457, 48, 45, 2], [2, "3767", 884, 452, 18, 51, 0], [2, "151", 350, 480, 30, 25, 0], [2, "471", 412, 461, 52, 47, 0], [4, 1, 107, 509, 0, 4023], [2, "473", 328, 474, 28, 38, 2], [2, "3226", 379, 478, 48, 45, 2], [2, "471", 397, 477, 52, 47, 0], [2, "472", 295, 502, 38, 27, 0], [2, "3226", 359, 488, 48, 45, 2], [2, "1381", 704, 450, 54, 88, 0], [2, "3226", 338, 499, 48, 45, 2], [2, "150", 316, 508, 36, 37, 0], [2, "3226", 330, 503, 48, 45, 2], [2, "471", 330, 507, 52, 47, 0], [2, "3752", 256, 517, 54, 81, 0], [2, "3183", 70, 524, 44, 75, 0], [2, "3752", 141, 525, 54, 81, 2], [2, "3767", 576, 603, 18, 51, 0], [2, "3168", 5, 550, 96, 107, 2], [2, "3768", 546, 636, 26, 46, 2], [2, "3168", 861, 593, 96, 107, 2], [2, "1381", 45, 626, 54, 88, 0], [2, "3748", 407, 679, 50, 58, 0], [2, "3752", 870, 658, 54, 81, 0], [2, "1381", 904, 662, 54, 88, 0], [2, "3183", 352, 689, 44, 75, 0], [2, "3748", 79, 714, 50, 58, 2], [4, 2, 854, 785, 0, 4021], [2, "3767", 910, 748, 18, 51, 0], [2, "3751", 702, 743, 50, 62, 0], [2, "3749", 95, 747, 54, 70, 0], [2, "3756", 680, 776, 40, 42, 0], [2, "3749", 595, 763, 54, 70, 0], [2, "3168", 110, 739, 96, 107, 2], [2, "3768", 866, 802, 26, 46, 2], [2, "3183", 517, 781, 44, 75, 0], [2, "3183", 199, 785, 44, 75, 0], [2, "3756", 148, 826, 40, 42, 0], [2, "3749", 807, 825, 54, 70, 0], [2, "3183", 357, 864, 44, 75, 0], [2, "3168", 748, 889, 96, 107, 2], [2, "3749", 748, 967, 54, 70, 0], [2, "3752", 389, 960, 54, 81, 0], [2, "3751", 721, 992, 50, 62, 0]]}, {"type": 3, "obj": [[2, "1414", 189, 429, 46, 30, 2], [2, "3749", 535, 350, 54, 70, 0], [2, "973", 315, 340, 42, 70, 0], [2, "973", 272, 340, 42, 70, 2], [2, "3751", 173, 67, 50, 62, 2], [2, "3764", 799, 3, 58, 103, 0], [2, "3764", 822, -16, 58, 103, 0], [2, "3748", 922, -10, 50, 58, 0], [2, "3748", 926, 14, 50, 58, 0], [2, "3749", 931, 55, 54, 70, 0], [2, "3751", 932, 253, 50, 62, 0], [2, "3756", 877, 495, 40, 42, 0], [2, "3753", 881, 474, 38, 24, 0], [2, "3751", 921, 416, 50, 62, 0], [2, "3749", 901, 438, 54, 70, 0], [2, "3748", 933, 467, 50, 58, 2], [2, "3756", 895, 488, 40, 42, 0], [2, "3749", 908, 486, 54, 70, 0], [2, "3751", 726, 384, 50, 62, 0], [2, "3756", 685, 524, 40, 42, 2], [2, "3748", 490, 337, 50, 58, 0], [2, "3753", 509, 384, 38, 24, 0], [2, "3756", 519, 399, 40, 42, 0], [2, "3749", 500, 416, 54, 70, 2], [2, "3756", 529, 465, 40, 42, 0], [2, "3756", 913, 298, 40, 42, 2], [2, "3748", 906, 269, 50, 58, 2], [2, "3756", 930, 309, 40, 42, 0], [2, "3756", 922, 326, 40, 42, 0], [2, "181_4", 842, 54, 104, 100, 0], [2, "3749", 938, 83, 54, 70, 0], [2, "3749", 790, 60, 54, 70, 0], [2, "3756", 751, 65, 40, 42, 0], [2, "3756", 905, 124, 40, 42, 0], [2, "3748", 919, 141, 50, 58, 0], [2, "3749", 543, 470, 54, 70, 0], [2, "3756", 742, 454, 40, 42, 2], [2, "617", -39, 238, 22, 43, 2], [2, "617", -39, 206, 22, 43, 2], [2, "617", -16, 227, 22, 43, 2], [2, "617", -16, 195, 22, 43, 2], [2, "617", 30, 205, 22, 43, 2], [2, "617", 7, 216, 22, 43, 2], [2, "617", 7, 184, 22, 43, 2], [2, "617", 30, 173, 22, 43, 2], [2, "617", 122, 161, 22, 43, 2], [2, "617", 99, 172, 22, 43, 2], [2, "617", 99, 140, 22, 43, 2], [2, "617", 122, 129, 22, 43, 2], [2, "617", 76, 183, 22, 43, 2], [2, "617", 53, 194, 22, 43, 2], [2, "617", 53, 162, 22, 43, 2], [2, "617", 76, 151, 22, 43, 2], [2, "617", 122, 97, 22, 43, 2], [2, "617", 99, 108, 22, 43, 2], [2, "617", 99, 76, 22, 43, 2], [2, "617", 122, 65, 22, 43, 2], [2, "617", 76, 119, 22, 43, 2], [2, "617", 53, 130, 22, 43, 2], [2, "617", 53, 98, 22, 43, 2], [2, "617", 76, 87, 22, 43, 2], [2, "617", 30, 141, 22, 43, 2], [2, "617", 7, 152, 22, 43, 2], [2, "617", 7, 120, 22, 43, 2], [2, "617", 30, 109, 22, 43, 2], [2, "617", -16, 163, 22, 43, 2], [2, "617", -39, 174, 22, 43, 2], [2, "617", -39, 142, 22, 43, 2], [2, "617", -16, 131, 22, 43, 2], [2, "518", 136, 75, 16, 108, 2], [2, "518", -41, 144, 16, 108, 2], [2, "518", 17, 115, 16, 108, 2], [2, "518", 89, 82, 16, 108, 2], [2, "3146", -33, 237, 54, 42, 2], [2, "3146", 35, 205, 54, 42, 2], [2, "3146", 89, 178, 54, 42, 2], [2, "520", -43, 252, 20, 37, 2], [2, "520", 15, 223, 20, 37, 2], [2, "520", 87, 190, 20, 37, 2], [2, "520", 134, 168, 20, 37, 2], [2, "3751", 229, 86, 50, 62, 2], [2, "3749", 264, 104, 54, 70, 0], [2, "3756", 295, 147, 40, 42, 2], [2, "3756", 325, 155, 40, 42, 2], [2, "3748", 344, 158, 50, 58, 0], [2, "3772", 170, 341, 80, 57, 0], [2, "3772", 199, 373, 80, 57, 0], [2, "3772", 228, 404, 80, 57, 0], [2, "3773", 170, 383, 14, 20, 0], [2, "3773", 186, 395, 14, 20, 0], [2, "3773", 186, 389, 14, 20, 0], [2, "3773", 175, 389, 14, 20, 0], [2, "3773", 170, 387, 14, 20, 0], [2, "3773", 175, 384, 14, 20, 0], [2, "3773", 217, 427, 14, 20, 0], [2, "3773", 217, 423, 14, 20, 0], [2, "3773", 207, 419, 14, 20, 0], [2, "3773", 207, 422, 14, 20, 0], [2, "3773", 194, 411, 14, 20, 0], [2, "3773", 194, 415, 14, 20, 0], [2, "3773", 180, 408, 14, 20, 0], [2, "3773", 180, 403, 14, 20, 0], [2, "3773", 170, 403, 14, 20, 0], [2, "3773", 170, 398, 14, 20, 0], [2, "3751", 697, 308, 50, 62, 0], [2, "973", 170, 410, 42, 70, 0], [2, "973", 127, 410, 42, 70, 2], [2, "3767", 634, 502, 18, 51, 0], [2, "3768", 667, 509, 26, 46, 0], [2, "3768", 575, 388, 26, 46, 0], [2, "3767", 536, 423, 18, 51, 0], [2, "1381", 763, 51, 54, 88, 0], [2, "1381", 891, 121, 54, 88, 2], [2, "1207", 353, 563, 22, 81, 0], [2, "1207", 353, 537, 22, 81, 0], [2, "1207", 487, 488, 22, 81, 0], [2, "1207", 487, 462, 22, 81, 0], [2, "1207", 417, 515, 22, 81, 0], [2, "3748", 12, 524, 50, 58, 0], [2, "3749", 288, 593, 54, 70, 0], [2, "3756", 291, 650, 40, 42, 0], [2, "3756", 105, 663, 40, 42, 2], [2, "3123", 239, 753, 192, 97, 2], [2, "3122", 317, 783, 142, 79, 2], [2, "3121", 339, 794, 198, 135, 2], [2, "3150", 548, 468, 62, 122, 2], [2, "3752", 132, 624, 54, 81, 2], [2, "3752", 706, 544, 54, 81, 2], [2, "3150", 87, 111, 62, 122, 0], [2, "3150", -14, 154, 62, 122, 0], [2, "3015", 162, 329, 72, 40, 0], [2, "1438", 658, 708, 28, 30, 0], [2, "1436", 379, 49, 30, 21, 0], [2, "1437", 433, 28, 24, 27, 0], [2, "3210", 42, 173, 54, 79, 2], [2, "3767", 110, 620, 18, 51, 0], [2, "3767", 305, 607, 18, 51, 0], [2, "3768", 325, 587, 26, 46, 0], [2, "3768", 48, 699, 26, 46, 2], [2, "3768", 514, 510, 26, 46, 0], [2, "3768", 643, 751, 26, 46, 2], [2, "1416", 432, 837, 24, 17, 0], [2, "1416", 457, 900, 24, 17, 0], [2, "1414", 455, 875, 46, 30, 0], [2, "1414", 555, 847, 46, 30, 0], [2, "1414", 649, 567, 46, 30, 0], [2, "1416", 626, 603, 24, 17, 0], [2, "1416", 750, 448, 24, 17, 0], [2, "1414", 135, 612, 46, 30, 0], [2, "1412", 303, 670, 32, 28, 0], [2, "1414", 443, 539, 46, 30, 0], [2, "1416", 390, 602, 24, 17, 0], [2, "1416", 430, 399, 24, 17, 0], [2, "1416", 159, 313, 24, 17, 0], [2, "1416", 824, 431, 24, 17, 0], [2, "1416", 779, 550, 24, 17, 0], [2, "1416", 852, 447, 24, 17, 0], [2, "1416", 815, 749, 24, 17, 0], [2, "1416", 699, 870, 24, 17, 0], [2, "1416", 529, 918, 24, 17, 0], [2, "1416", 260, 737, 24, 17, 0], [2, "1416", 232, 763, 24, 17, 0], [2, "1416", 128, 508, 24, 17, 0], [2, "1416", 43, 494, 24, 17, 0], [2, "1416", 417, 340, 24, 17, 0], [2, "1416", 525, 561, 24, 17, 0], [2, "1416", 746, 265, 24, 17, 0], [2, "1416", 928, 217, 24, 17, 0], [2, "1416", 765, 168, 24, 17, 0], [2, "1414", 80, 694, 46, 30, 0], [2, "1414", 96, 582, 46, 30, 2], [2, "1412", 119, 571, 32, 28, 2], [2, "1414", 93, 450, 46, 30, 0], [2, "1414", 220, 272, 46, 30, 2], [2, "1414", 409, 253, 46, 30, 2], [2, "1414", 433, 271, 46, 30, 2], [2, "1414", 456, 291, 46, 30, 2], [2, "1412", 496, 382, 32, 28, 2], [2, "1412", 384, 727, 32, 28, 2], [2, "1416", 183, 860, 24, 17, 0], [2, "1414", 675, 805, 46, 30, 2], [2, "1414", 784, 875, 46, 30, 2], [2, "1412", 752, 575, 32, 28, 0], [2, "1414", 862, 521, 46, 30, 2], [2, "1414", 904, 356, 46, 30, 2], [2, "1412", 864, 775, 32, 28, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, -1, -1, -1, -1, 41, 42, 43, -1, 51, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 21, -1, -1, -1, -1, -1, -1, 41, 42, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, 41, 42, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 41, 42, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, -1, -1, -1, -1, -1, -1, 8, 11, 10, -1, 42, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 9, 2, -1, -1, -1, -1, -1, 15, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 6, -1, -1, -1, -1, -1, -1, -1, -1, 4, 5, -1, -1, -1, -1, -1, 15, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 9, -1, -1, -1, -1, -1, -1, -1, 15, 1, 2, -1, -1, -1, -1, -1, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 5, 6, -1, -1, -1, -1, 20, 19, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, -1, -1, -1, -1, -1, -1, -1, -1, 20, 10, 21, -1, -1, -1, -1, -1, -1, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 7, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, 21, -1, -1, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, -1, -1, -1, 0, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 10, -1, 22, 10, 10, 10, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, 22, 21, -1, -1, 8, 11, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, 22, 16, 10, 10, 14, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, 16, 21, -1, -1, 15, 22, 22, -1, -1, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, 22, 22, 13, 16, 17, -1, -1, 23, 22, 5, 7, 6, -1, -1, -1, -1, 13, 14, -1, -1, 20, 19, 23, 22, 22, 4, 5, -1, 1, 2, -1, -1, -1, -1, -1, -1, 8, 10, 10, 16, 22, 22, 16, 17, 14, -1, -1, 20, 19, 13, 13, 13, -1, 8, 7, -1, 21, -1, -1, -1, -1, -1, 20, 19, 23, 16, 16, 16, 4, 5, -1, -1, -1, -1, -1, -1, 15, 22, 16, 17, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, 22, 21, -1, -1, -1, -1, -1, -1, -1, 20, 23, 22, 16, 15, 22, 22, 22, 21, -1, -1, -1, 15, 22, 13, 14, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 22, 15, 22, 22, 22, 21, -1, -1, -1, 15, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 15, 22, 22, -1, 21, -1, -1, -1, 15, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, -1, -1, 15, 22, 22, 16, 23, 22, 21, -1, -1, -1, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, -1, -1, -1, -1, 20, 19, 15, 16, 17, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, -1, 22, -1, -1, -1, 15, 16, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 16, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, -1, -1, -1, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 16, 17, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, -1, -1, -1, 20, 19, 7, 1, 2, -1, -1, -1, -1, -1, -1, 11, 10, -1, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 23, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 16, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 23, 23, 16, 4, 5, 16, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 16, 17, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 23, 16, 16, 16, 16, 16, 16, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 16, 16, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 13, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 10, 16, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, 10, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 22, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 14, -1, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, 8, 7, 10, 22, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 16, 16, 4, 5, 6, -1, -1, -1, 8, 7, 11, 10, 22, 22, 22, 9, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "385", 242, 1013, 72, 48, 2], [2, "384", 289, 976, 24, 72, 0], [2, "383", 241, 977, 48, 72, 0], [2, "385", 813, 992, 72, 48, 0], [2, "382", 603, 166, 24, 72, 0], [2, "3764", 610, 143, 58, 103, 0], [2, "385", 320, 906, 72, 48, 0], [2, "385", 916, 872, 72, 48, 0], [2, "385", 535, 797, 72, 48, 0], [2, "385", -36, 652, 72, 48, 2], [2, "385", 4, 684, 72, 48, 2], [2, "385", 113, 910, 72, 48, 2], [2, "385", 674, 501, 72, 48, 0], [2, "385", 590, 493, 72, 48, 0], [2, "385", 612, 510, 72, 48, 0], [2, "385", 670, 492, 72, 48, 0], [2, "385", 667, 449, 72, 48, 0], [2, "385", 620, 497, 72, 48, 0], [2, "385", 635, 514, 72, 48, 0], [2, "385", 593, 480, 72, 48, 0], [2, "385", 491, 271, 72, 48, 0], [2, "126", 249, 104, 150, 54, 0], [2, "3764", 630, 204, 58, 103, 0], [2, "3750", 623, 245, 58, 45, 0], [2, "3750", 609, 257, 58, 45, 0], [2, "163", 365, 268, 60, 33, 2], [2, "730", 126, 172, 36, 25, 0], [2, "730", 145, 181, 36, 25, 0], [2, "383", 527, 326, 48, 72, 0], [2, "384", 575, 327, 24, 72, 0], [2, "381", 531, 190, 72, 48, 0], [2, "126", 380, 158, 150, 54, 2], [2, "126", 590, 295, 150, 54, 2], [2, "126", 538, 417, 150, 54, 0], [2, "126", 597, 447, 150, 54, 0], [2, "3764", -31, 565, 58, 103, 0], [2, "3764", -1, 594, 58, 103, 0], [2, "3764", 21, 662, 58, 103, 0], [2, "3764", 39, 730, 58, 103, 2], [2, "3764", 68, 788, 58, 103, 2], [2, "3764", 110, 829, 58, 103, 0], [2, "3764", 153, 860, 58, 103, 0], [2, "3764", 309, 778, 58, 103, 0], [2, "3764", 295, 937, 58, 103, 2], [2, "3764", 926, 795, 58, 103, 2], [2, "3764", 886, 823, 58, 103, 0], [2, "3764", 850, 850, 58, 103, 0], [2, "3764", 820, 908, 58, 103, 0], [2, "3764", 539, 390, 58, 103, 0], [2, "3764", 717, 418, 58, 103, 0], [2, "3764", 665, 259, 58, 103, 0], [2, "3764", 719, 337, 58, 103, 0], [2, "3754", 865, 83, 48, 31, 0], [2, "3753", 895, 101, 38, 24, 0], [2, "3755", 919, 118, 40, 24, 0], [2, "3755", 824, 85, 40, 24, 0], [2, "3755", 662, 103, 40, 24, 0], [2, "3755", 638, 116, 40, 24, 0], [2, "3754", 615, 131, 48, 31, 0], [2, "3755", 725, 83, 40, 24, 0], [2, "3754", 754, 85, 48, 31, 0], [2, "3755", 796, 92, 40, 24, 0], [2, "3755", 941, 133, 40, 24, 0], [2, "3754", 637, 146, 48, 31, 0], [2, "3753", 659, 168, 38, 24, 0], [2, "3753", 660, 187, 38, 24, 0], [2, "3753", 647, 206, 38, 24, 0], [2, "3754", 639, 216, 48, 31, 2], [2, "3754", 687, 84, 48, 31, 0], [2, "3754", 670, 247, 48, 31, 2], [2, "3755", 682, 273, 40, 24, 0], [2, "3755", 682, 298, 40, 24, 0], [2, "3754", 192, 85, 48, 31, 2], [2, "3754", 220, 104, 48, 31, 2], [2, "3753", 259, 118, 38, 24, 0], [2, "3753", 278, 134, 38, 24, 0], [2, "3755", 300, 148, 40, 24, 0], [2, "3755", 331, 156, 40, 24, 0], [2, "3754", 361, 166, 48, 31, 2], [2, "3754", 369, 190, 48, 31, 0], [2, "3754", 373, 211, 48, 31, 0], [2, "3755", 403, 232, 40, 24, 0], [2, "3755", 422, 249, 40, 24, 0], [2, "3753", 442, 266, 38, 24, 0], [2, "3753", 468, 278, 38, 24, 0], [2, "3753", 492, 341, 38, 24, 0], [2, "3754", 485, 318, 48, 31, 2], [2, "3753", 746, 377, 38, 24, 0], [2, "3754", 532, 391, 48, 31, 2], [2, "3754", 542, 417, 48, 31, 2], [2, "3754", 736, 397, 48, 31, 2], [2, "3755", 736, 458, 40, 24, 0], [2, "3753", 731, 480, 38, 24, 0], [2, "3754", 724, 500, 48, 31, 2], [2, "3753", 551, 445, 38, 24, 0], [2, "3755", 545, 489, 40, 24, 0], [2, "3755", 561, 484, 40, 24, 0], [2, "3753", 579, 502, 38, 24, 0], [2, "3754", 588, 522, 48, 31, 2], [2, "3754", 612, 539, 48, 31, 2], [2, "3754", 715, 523, 48, 31, 0], [2, "3753", 653, 547, 38, 24, 0], [2, "3754", -22, 548, 48, 31, 0], [2, "3754", -22, 548, 48, 31, 0], [2, "3754", 0, 567, 48, 31, 0], [2, "3763", 764, 306, 78, 60, 0], [2, "3763", 701, 308, 78, 60, 2], [2, "3763", 798, 306, 78, 60, 2], [2, "3763", 861, 304, 78, 60, 0], [2, "3763", 759, 475, 78, 60, 2], [2, "3763", 822, 473, 78, 60, 0], [2, "3754", 932, 766, 48, 31, 0], [2, "3753", 927, 792, 38, 24, 0], [2, "3753", 909, 808, 38, 24, 0], [2, "3755", 890, 823, 40, 24, 0], [2, "3755", 858, 835, 40, 24, 0], [2, "3754", 846, 856, 48, 31, 0], [2, "3754", 832, 882, 48, 31, 0], [2, "3754", 818, 905, 48, 31, 0], [2, "3755", 800, 929, 40, 24, 0], [2, "3753", 784, 947, 38, 24, 0], [2, "3753", 23, 588, 38, 24, 0], [2, "3755", 38, 610, 40, 24, 0], [2, "3755", 36, 632, 40, 24, 0], [2, "3754", 41, 651, 48, 31, 0], [2, "3754", 61, 678, 48, 31, 2], [2, "3754", 48, 702, 48, 31, 2], [2, "3754", 57, 728, 48, 31, 2], [2, "3754", 70, 754, 48, 31, 2], [2, "3755", 91, 782, 40, 24, 0], [2, "3755", 105, 799, 40, 24, 0], [2, "3755", 116, 819, 40, 24, 0], [2, "3753", 124, 835, 38, 24, 0], [2, "3753", 140, 851, 38, 24, 0], [2, "3754", 168, 857, 48, 31, 2], [2, "3754", 200, 879, 48, 31, 2], [2, "3753", 303, 932, 38, 24, 0], [2, "3753", 334, 940, 38, 24, 0], [2, "3754", 145, 80, 48, 31, 0], [2, "3754", 97, 87, 48, 31, 0], [2, "3754", 60, 95, 48, 31, 0], [2, "3753", 41, 116, 38, 24, 0], [2, "3755", 33, 136, 40, 24, 0], [2, "3755", -5, 173, 40, 24, 0], [2, "3754", -21, 184, 48, 31, 0], [2, "3754", 480, 296, 48, 31, 2], [2, "3764", 694, 335, 58, 103, 0], [2, "3753", 684, 313, 38, 24, 0], [2, "3754", 734, 436, 48, 31, 2], [2, "385", 621, 432, 72, 48, 0], [2, "385", 582, 466, 72, 48, 0], [2, "385", 665, 477, 72, 48, 0], [2, "385", 482, 178, 72, 48, 2], [2, "730", 125, 190, 36, 25, 0], [2, "730", 107, 199, 36, 25, 0], [2, "730", 87, 209, 36, 25, 0], [2, "730", 68, 218, 36, 25, 0], [2, "730", 50, 227, 36, 25, 0], [2, "730", 30, 237, 36, 25, 0], [2, "730", 10, 247, 36, 25, 0], [2, "730", -8, 256, 36, 25, 0], [2, "730", -28, 266, 36, 25, 0], [2, "730", -47, 275, 36, 25, 0], [2, "730", -65, 284, 36, 25, 0], [2, "617", 22, 444, 22, 43, 2], [2, "617", -1, 455, 22, 43, 2], [2, "617", -1, 423, 22, 43, 2], [2, "617", 22, 412, 22, 43, 2], [2, "617", 68, 422, 22, 43, 2], [2, "617", 45, 433, 22, 43, 2], [2, "617", 45, 401, 22, 43, 2], [2, "617", 68, 390, 22, 43, 2], [2, "617", 159, 379, 22, 43, 2], [2, "617", 136, 390, 22, 43, 2], [2, "617", 136, 358, 22, 43, 2], [2, "617", 159, 347, 22, 43, 2], [2, "617", 113, 401, 22, 43, 2], [2, "617", 90, 412, 22, 43, 2], [2, "617", 90, 380, 22, 43, 2], [2, "617", 113, 369, 22, 43, 2], [2, "617", 342, 292, 22, 43, 2], [2, "617", 319, 303, 22, 43, 2], [2, "617", 319, 271, 22, 43, 2], [2, "617", 342, 260, 22, 43, 2], [2, "617", 296, 314, 22, 43, 2], [2, "617", 273, 325, 22, 43, 2], [2, "617", 273, 293, 22, 43, 2], [2, "617", 296, 282, 22, 43, 2], [2, "617", 251, 335, 22, 43, 2], [2, "617", 228, 346, 22, 43, 2], [2, "617", 228, 314, 22, 43, 2], [2, "617", 251, 303, 22, 43, 2], [2, "617", 205, 357, 22, 43, 2], [2, "617", 182, 368, 22, 43, 2], [2, "617", 182, 336, 22, 43, 2], [2, "617", 205, 325, 22, 43, 2], [2, "617", 385, 271, 22, 43, 2], [2, "617", 362, 282, 22, 43, 2], [2, "617", 362, 250, 22, 43, 2], [2, "617", 385, 239, 22, 43, 2], [2, "163", 179, 310, 60, 33, 0], [2, "163", 130, 333, 60, 33, 0], [2, "163", 178, 141, 60, 33, 2], [2, "163", 227, 165, 60, 33, 2], [2, "163", 276, 189, 60, 33, 2], [2, "163", 325, 213, 60, 33, 2], [2, "163", 380, 292, 60, 33, 0], [2, "163", 331, 316, 60, 33, 0], [2, "163", 282, 340, 60, 33, 0], [2, "163", 233, 364, 60, 33, 0], [2, "163", 209, 375, 60, 33, 0], [2, "163", 160, 399, 60, 33, 0], [2, "163", 111, 421, 60, 33, 0], [2, "163", 62, 445, 60, 33, 0], [2, "163", 13, 469, 60, 33, 0], [2, "163", -36, 493, 60, 33, 0], [2, "3750", 574, 280, 58, 45, 0], [2, "3750", 529, 287, 58, 45, 0], [2, "3750", 658, 377, 58, 45, 0], [2, "3750", 500, 312, 58, 45, 0], [2, "385", 639, 325, 72, 48, 0], [2, "385", 609, 269, 72, 48, 0], [2, "163", 129, 116, 60, 33, 2], [2, "385", 324, 132, 72, 48, 0], [2, "385", 603, 69, 72, 48, 0], [2, "385", 512, 112, 72, 48, 0], [2, "385", 631, 471, 72, 48, 0], [2, "385", 571, 455, 72, 48, 0], [2, "385", 566, 354, 72, 48, 0], [2, "385", 510, 212, 72, 48, 0], [2, "163", 105, 266, 60, 33, 2], [2, "163", 154, 290, 60, 33, 2], [2, "163", 42, 297, 60, 33, 2], [2, "163", 91, 321, 60, 33, 2], [2, "163", 7, 275, 60, 33, 2], [2, "3767", 323, 107, 18, 51, 0], [2, "3768", 649, 349, 26, 46, 2], [2, "3767", 632, 84, 18, 51, 0], [2, "3767", 612, 98, 18, 51, 0], [2, "3768", 727, 48, 26, 46, 2], [2, "3764", 494, 446, 58, 103, 0], [2, "3764", 454, 455, 58, 103, 0], [2, "3764", 418, 457, 58, 103, 0], [2, "3764", 387, 475, 58, 103, 0], [2, "3764", 363, 495, 58, 103, 0], [2, "3764", 333, 520, 58, 103, 0], [2, "3764", 305, 540, 58, 103, 0], [2, "3764", 268, 552, 58, 103, 0], [2, "3764", 62, 587, 58, 103, 0], [2, "3764", 94, 591, 58, 103, 0], [2, "3763", 163, 577, 78, 60, 2], [2, "3763", 226, 575, 78, 60, 0], [2, "3764", 127, 591, 58, 103, 0], [2, "3754", 466, 449, 48, 31, 2], [2, "3754", 430, 453, 48, 31, 2], [2, "3753", 78, 584, 38, 24, 0], [2, "3753", 78, 584, 38, 24, 0], [2, "3753", 110, 587, 38, 24, 0], [2, "3754", 125, 580, 48, 31, 2], [2, "3753", 400, 467, 38, 24, 0], [2, "3754", 374, 480, 48, 31, 0], [2, "3754", 345, 502, 48, 31, 0], [2, "3754", 319, 522, 48, 31, 0], [2, "3755", 294, 537, 40, 24, 0], [2, "3755", 276, 551, 40, 24, 0], [2, "3044", 423, 594, 122, 62, 0], [2, "1332", 856, 547, 106, 57, 0], [2, "3763", 162, 629, 78, 60, 2], [2, "3763", 225, 627, 78, 60, 0], [2, "3764", 270, 802, 58, 103, 0], [2, "3764", 228, 823, 58, 103, 0], [2, "3764", 188, 857, 58, 103, 0], [2, "3753", 360, 926, 38, 24, 0], [2, "3764", 533, 648, 58, 103, 0], [2, "3764", 489, 676, 58, 103, 0], [2, "3764", 449, 693, 58, 103, 0], [2, "3764", 410, 716, 58, 103, 0], [2, "3764", 410, 716, 58, 103, 0], [2, "3764", 368, 744, 58, 103, 0], [2, "3764", 672, 532, 58, 103, 2], [2, "3764", 578, 635, 58, 103, 2], [2, "3764", 645, 563, 58, 103, 2], [2, "3764", 681, 585, 58, 103, 2], [2, "3764", 639, 590, 58, 103, 2], [2, "3764", 609, 600, 58, 103, 2], [2, "3764", 706, 620, 58, 103, 2], [2, "3764", 925, 607, 58, 103, 2], [2, "3764", 893, 622, 58, 103, 0], [2, "3764", 605, 633, 58, 103, 2], [2, "3754", 708, 702, 48, 31, 2], [2, "3755", 559, 644, 40, 24, 0], [2, "3755", 535, 650, 40, 24, 0], [2, "3755", 517, 667, 40, 24, 0], [2, "3755", 517, 667, 40, 24, 0], [2, "3755", 495, 675, 40, 24, 0], [2, "3755", 477, 686, 40, 24, 0], [2, "3755", 477, 686, 40, 24, 0], [2, "3755", 454, 695, 40, 24, 0], [2, "3754", 427, 702, 48, 31, 2], [2, "3754", 405, 718, 48, 31, 2], [2, "3754", 378, 737, 48, 31, 2], [2, "3754", 350, 753, 48, 31, 2], [2, "3754", 590, 630, 48, 31, 2], [2, "3754", 648, 556, 48, 31, 0], [2, "3754", 648, 556, 48, 31, 0], [2, "3754", 631, 583, 48, 31, 0], [2, "3754", 606, 604, 48, 31, 0], [2, "3755", 679, 563, 40, 24, 0], [2, "3755", 664, 582, 40, 24, 0], [2, "3755", 222, 834, 40, 24, 2], [2, "3755", 222, 834, 40, 24, 2], [2, "3755", 204, 850, 40, 24, 2], [2, "3755", 389, 913, 40, 24, 2], [2, "3151", 108, 297, 96, 49, 0], [2, "3151", 55, 274, 96, 49, 0], [2, "3151", 22, 251, 96, 49, 0], [2, "3755", 690, 765, 40, 24, 0], [2, "3753", 664, 774, 38, 24, 0], [2, "3753", 643, 787, 38, 24, 0], [2, "3753", 624, 801, 38, 24, 0], [2, "3754", 598, 811, 48, 31, 0], [2, "3754", 559, 823, 48, 31, 0], [2, "3754", 523, 835, 48, 31, 2], [2, "3753", 506, 850, 38, 24, 0], [2, "385", 644, 641, 72, 48, 0], [2, "385", 627, 658, 72, 48, 0], [2, "385", 657, 650, 72, 48, 0], [2, "385", 671, 670, 72, 48, 0], [2, "385", 583, 698, 72, 48, 0], [2, "385", 503, 684, 72, 48, 0], [2, "385", 201, 894, 72, 48, 0], [2, "385", 66, 861, 72, 48, 2], [2, "385", 25, 842, 72, 48, 2], [2, "385", -24, 895, 72, 48, 2], [2, "385", 2, 739, 72, 48, 2], [2, "385", 435, 771, 72, 48, 0], [2, "385", 850, 932, 72, 48, 0], [2, "385", 607, 370, 72, 48, 0], [2, "3764", 274, 938, 58, 103, 0], [2, "3751", 303, 893, 50, 62, 0], [2, "385", 280, 932, 72, 48, 0], [2, "3754", 702, 724, 48, 31, 2], [2, "3755", 703, 746, 40, 24, 0], [2, "381", 160, 20, 72, 48, 2], [2, "382", 136, -4, 24, 72, 2], [2, "3758", 117, 711, 32, 15, 0], [2, "3759", 182, 707, 16, 8, 0], [2, "3759", 188, 840, 16, 8, 0], [2, "3759", 473, 365, 16, 8, 0], [2, "3758", 448, 318, 32, 15, 2], [2, "385", 582, 132, 72, 48, 0], [2, "385", 762, 32, 72, 48, 0], [2, "385", 347, 22, 72, 48, 0], [2, "3755", 714, 612, 40, 24, 0], [2, "3762", 703, 798, 80, 57, 2], [2, "3762", 755, 824, 80, 57, 2], [2, "3758", 528, 878, 32, 15, 0], [2, "3759", 669, 825, 16, 8, 0], [2, "3759", 862, 740, 16, 8, 0], [2, "3758", 787, 374, 32, 15, 0], [2, "3759", 845, 589, 16, 8, 0], [2, "3759", 766, 543, 16, 8, 2], [2, "3754", 472, 673, 48, 31, 2], [2, "1334", 786, 682, 30, 30, 0], [2, "1334", 155, 711, 30, 30, 0], [2, "1334", 466, 906, 30, 30, 0], [2, "1334", 811, 542, 30, 30, 0], [2, "1334", 791, 567, 30, 30, 0], [2, "1334", 809, 766, 30, 30, 0], [2, "1334", 832, 644, 30, 30, 2], [2, "1334", 788, 732, 30, 30, 2], [2, "1334", 778, 780, 30, 30, 0], [2, "1334", 222, 692, 30, 30, 0], [2, "1334", 206, 724, 30, 30, 0], [2, "1334", 239, 739, 30, 30, 0], [2, "1334", 234, 709, 30, 30, 0], [2, "1334", 199, 686, 30, 30, 0], [2, "1334", 256, 770, 30, 30, 0], [2, "1334", 493, 906, 30, 30, 2], [2, "1334", 551, 900, 30, 30, 2], [2, "1334", 701, 834, 30, 30, 2], [2, "1334", 794, 803, 30, 30, 2], [2, "1334", 803, 439, 30, 30, 2], [2, "1334", 828, 411, 30, 30, 0], [2, "1334", 828, 411, 30, 30, 0], [2, "1334", 826, 362, 30, 30, 0], [2, "1334", 236, 467, 30, 30, 0], [2, "1334", 264, 445, 30, 30, 0], [2, "1334", 265, 472, 30, 30, 0], [2, "1334", 177, 508, 30, 30, 0], [2, "1334", 449, 340, 30, 30, 0], [2, "1334", 303, 421, 30, 30, 2], [2, "1334", 289, 441, 30, 30, 2], [2, "1334", 779, 275, 30, 30, 0], [2, "1334", 719, 241, 30, 30, 0], [2, "1334", 829, 285, 30, 30, 0], [2, "1334", 642, 862, 30, 30, 2], [2, "1334", 761, 866, 30, 30, 2], [2, "1334", 400, 658, 30, 30, 2], [2, "1334", 339, 696, 30, 30, 2], [2, "1334", 316, 716, 30, 30, 2], [2, "1334", 281, 714, 30, 30, 2], [2, "1334", 90, 496, 30, 30, 2], [2, "3764", 330, 974, 58, 103, 0], [2, "3764", 330, 974, 58, 103, 0], [2, "3764", 368, 1002, 58, 103, 0], [2, "3764", 405, 1039, 58, 103, 0], [2, "3764", 443, 1059, 58, 103, 0], [2, "3764", 787, 965, 58, 103, 0], [2, "3764", 787, 965, 58, 103, 0], [2, "3764", 752, 1012, 58, 103, 0], [2, "3764", 707, 1040, 58, 103, 0], [2, "3755", 318, 957, 40, 24, 2], [2, "3754", 345, 969, 48, 31, 2], [2, "3754", 365, 994, 48, 31, 2], [2, "3754", 397, 1017, 48, 31, 2], [2, "3754", 424, 1040, 48, 31, 2], [2, "3755", 458, 1061, 40, 24, 0], [2, "385", 290, 1008, 72, 48, 2], [2, "385", 126, 1011, 72, 48, 2], [2, "385", 814, 959, 72, 48, 0], [2, "385", 908, 1007, 72, 48, 0], [2, "381", 146, 1001, 72, 48, 0], [2, "382", 218, 977, 24, 72, 0], [2, "385", 325, 1048, 72, 48, 2], [2, "3754", 765, 999, 48, 31, 0], [2, "3754", 739, 1019, 48, 31, 0], [2, "3754", 710, 1035, 48, 31, 0], [2, "385", 658, 582, 72, 48, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 103, 103, 103, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 65, 65, 65, 65, 65, 65, 65, 65, -1, -1, -1, -1, 70, 61, 64, 65, 103, 103, 103, 103, 103, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, -1, -1, 61, -1, -1, -1, 103, 103, 103, 103, 103, 103, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 65, 63, 87, 86, 85, 65, 65, 87, 80, 81, 63, 65, 65, -1, -1, -1, -1, -1, 61, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 87, 86, 90, 89, 84, 85, 65, 94, 83, 84, 86, 85, 65, -1, -1, -1, -1, 61, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, -1, -1, -1, -1, -1, -1, -1, -1, 74, 90, 72, 77, 95, 96, 97, 87, 90, 71, 95, 95, 96, 97, -1, -1, 103, 103, 103, 67, 68, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 95, 96, 92, 93, 65, 94, 89, 74, 96, 92, 93, 65, 103, 103, 103, 103, 103, 103, 103, 103, 69, 103, 103, 103, 103, 103, 103, 103, 61, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, 99, 92, 93, 65, 62, 62, 94, 71, 95, 100, 65, 62, 61, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 69, 103, 64, 61, 62, 63, -1, -1, -1, -1, -1, -1, -1, 65, 65, 65, 63, 62, 62, 99, 98, 92, 93, 61, 62, 64, 103, 103, 103, 67, 103, 103, 103, 103, 103, 103, 103, 69, 103, 103, 103, 103, 67, 64, 61, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, 65, 63, 62, 62, 62, 65, 65, 65, 64, 65, 67, 103, 103, 103, 103, 61, 103, 65, 66, 67, 68, 69, 103, 103, 103, 61, 61, 67, 67, 61, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, 74, 70, 71, 72, 78, 71, 70, 70, 67, 68, 69, 103, 103, 103, 103, 103, 103, 68, 69, 103, 103, 103, 103, 103, 103, 64, 64, 67, 68, 61, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, 77, 65, 74, 75, 72, 72, 73, 70, 71, 70, 71, 103, 103, 103, 103, 103, 68, 69, 103, 103, 103, 103, 103, 61, 62, 67, 67, 68, 69, 61, 62, 63, 103, -1, -1, -1, -1, -1, -1, -1, -1, 65, 65, 65, 61, 61, 61, 62, 61, 62, 63, 103, 103, 103, 69, 103, 103, 103, 103, 103, 103, 61, 62, 64, 65, 67, 61, 87, 86, 86, 80, 81, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 65, 64, 64, 64, 65, 64, 65, 66, 103, 68, 69, 103, 103, 103, 103, 103, 103, 103, 64, 65, 67, 68, 69, 64, 94, 74, 70, 83, 84, 80, 81, -1, -1, -1, -1, -1, -1, -1, -1, 65, 61, 67, 67, 67, 68, 87, 86, 69, 103, 103, 103, 103, 103, 61, 61, 61, 62, 63, 67, 68, 69, 65, 66, 87, 90, 77, 73, 74, 75, 83, 84, 72, -1, -1, -1, -1, -1, -1, -1, 65, 64, 65, 61, 87, 86, 90, 89, 66, 103, 103, 103, 61, 62, 64, 64, 61, 62, 61, 64, 65, 66, 87, 86, 90, 89, 75, 76, 77, 78, 62, 63, 75, -1, -1, -1, -1, -1, -1, 65, 65, 67, 68, 87, 90, 89, 95, 61, 62, 103, 61, 61, 64, 65, 67, 67, 61, 62, 64, 67, 87, 86, 90, 89, 76, 77, 78, 62, 63, 64, 65, 66, 78, -1, -1, -1, -1, -1, -1, -1, 75, 75, 72, 67, 68, 89, 76, 61, 62, 61, 64, 61, 67, 68, 69, 69, 64, 65, 87, 86, 90, 89, 76, 77, 78, 61, 64, 65, 66, 67, 68, 69, 71, 72, -1, -1, -1, -1, -1, -1, 61, 62, 63, 61, 62, 102, 101, 64, 89, 64, 67, 64, 64, 67, 68, 69, 67, 87, 90, 89, 96, 92, 76, 77, 78, 64, 67, 68, 69, 62, 63, 61, 62, 63, -1, -1, -1, -1, 70, 71, 64, 65, 66, 64, 61, 99, 98, 102, 101, 67, 68, 67, 67, 67, 68, 69, 69, 94, 95, 96, 97, 77, 78, 62, 63, 67, 68, 95, 88, 65, 66, 64, 65, 66, 61, 62, 63, 61, 62, 63, 67, 68, 69, 67, 64, 65, 66, 99, 98, 103, 103, 103, 103, 66, 64, 65, 61, 94, 95, 76, 65, 78, 61, 62, 63, 89, 95, 96, 88, 68, 69, 67, 68, 69, 64, 65, 66, 64, 65, 66, 65, 67, 68, 64, 67, 68, 61, 62, 63, -1, -1, 61, 62, 63, 62, 63, 64, 65, 61, 62, 63, 64, 64, 65, 84, 96, 92, 93, 68, 69, 64, 64, 65, 61, 61, 62, 63, 67, 68, 69, 68, 67, 61, 62, 63, 62, 63, 62, 63, -1, -1, 64, 65, 66, 63, 66, 67, 61, 64, 65, 66, 67, 61, 102, 101, 100, 69, 61, 62, 63, 67, 67, 61, 62, 63, 65, 66, 62, 63, 69, 86, 86, 85, 65, 66, 65, 66, 63, 66, -1, -1, 67, 68, 69, 66, 69, 61, 64, 67, 68, 61, 62, 63, 99, 98, 97, 63, 64, 61, 61, 62, 61, 64, 65, 66, 74, -1, -1, 25, 62, 95, 96, 93, 68, 69, 68, 69, 66, 72, -1, -1, -1, 67, 61, 62, 63, 63, 67, 61, 62, 64, 65, 63, 63, 63, 65, 66, 67, 61, 62, 63, 64, 65, 66, 70, -1, -1, -1, -1, 70, 92, 93, 67, 68, 69, 66, 63, 61, 62, -1, -1, -1, 61, 62, 65, 66, 66, 68, 64, 65, 67, 68, 69, 63, 67, 68, 61, 62, 63, 65, 66, 70, 71, -1, 70, -1, -1, -1, -1, 61, 62, 63, 61, 62, 63, 87, 86, 64, 101, -1, -1, -1, 64, 65, 68, 69, 69, 61, 62, 63, 69, 64, 65, 66, 62, 61, 64, 65, 66, 76, 77, 25, -1, -1, -1, -1, -1, -1, -1, 64, 85, 66, 61, 62, 63, 91, 102, 101, 95, -1, -1, -1, 76, 67, 61, 62, 63, 64, 65, 66, 67, 67, 68, 69, 65, 64, 65, 66, 70, -1, 25, -1, -1, -1, -1, -1, -1, -1, -1, 95, 100, 63, 64, 65, 66, 67, 99, 98, 102, -1, -1, -1, -1, 61, 62, 65, 66, 67, 68, 69, 64, 61, 62, 63, 70, 71, 72, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 92, 93, 66, 67, 68, 69, 69, 67, 68, 99, -1, -1, -1, -1, 64, 61, 62, 62, 63, 61, 62, 63, 64, 65, 65, 73, 74, 75, 71, -1, -1, -1, -1, -1, -1, -1, -1, 66, 68, 61, 67, 68, 69, 64, 65, 66, 64, 67, 68, 69, -1, -1, -1, -1, -1, 64, 65, 61, 62, 64, 65, 70, 71, 70, 70, 76, 77, 78, -1, -1, -1, -1, -1, -1, 25, -1, 62, 66, 63, 64, 64, 65, 66, 67, 68, 69, 67, 68, 69, -1, -1, -1, -1, -1, -1, -1, 67, 64, 61, 62, 70, 71, 72, 73, 73, 76, -1, -1, -1, -1, -1, -1, -1, 65, 67, 68, 69, 69, 66, 67, 67, 68, 69, 64, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 70, 73, 74, -1, -1, -1, -1, -1, -1, 78, 62, 63, 61, 62, 63, 61, 62, 63, 68, 87, 86, 80, 81, 61, 62, 63, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 76, 25, -1, -1, -1, -1, -1, -1, 62, 63, 66, 64, 65, 66, 61, 62, 87, 86, 90, 71, 72, 84, 81, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, -1, 25, -1, -1, -1, -1, 25, 61, 65, 66, 69, 61, 62, 63, 87, 86, 90, 89, 73, 74, 75, 83, 100, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 67, 67, 68, 69, 61, 62, 63, 87, 90, 89, 71, 72, 70, 71, 72, 96, 93, 61, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 94, 70, 71, 70, 71, 73, 74, 75, 70, 71, 72, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 86, 80, 81, 61, 62, 63, 65, 91, 102, 101, 95, 96, 98, 102, 101, 73, 74, 75, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, -1, -1, -1, 74, 83, 84, 80, 81, 63, 61, 62, 99, 98, 92, 93, 64, 99, 98, 102, 101, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 74, 74, 83, 88, 66, 64, 65, 66, 67, 68, 69, 67, 68, 69, 99, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 88, 69, 67, 68, 69, 65, 65, 65, 65, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 31, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 103, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 103, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 103, 103, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 33, 33, 33, 33, 33, 33, 29, 33, 33, 33, 29, 33, 29, 33, 33, 33, 33, 33, 29, 33, 29, 29, 29, 29, 29, 29, 29, 29, 33, 33, 29, 29, 29, 29, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 29, 29, 29, 29, 29, 29, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 29, 29, 29, 29, 29, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 33, 33, 33, 33, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 33, 29, 33, 29, 29, 33, 29, 29, 29, 33, 33, 33, 33, 33, 33, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 61, 62, 63, 29, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 61, 62, 61, 62, 61, 61, 62, 63, 32, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 61, 61, 62, 63, 62, 63, 63, 66, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 31, 31, 27, 27, 64, 64, 65, 66, 65, 66, 66, 69, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 31, 31, 31, 27, 27, 67, 67, 68, 69, 68, 69, 69, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 36, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 31, 31, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 31, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 25, 25, 25, 25, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, 39, -1, -1, -1, -1, -1, -1, -1, -1, 39, -1, 39, 39, 39, 39, 39, 39, 39, 39, 39]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}