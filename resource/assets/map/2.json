{"mW": 864, "mH": 1032, "tW": 24, "tH": 24, "tiles": [["369_1", 0, 3, 3], ["369_1", 2, 3, 3], ["369_1", 1, 3, 3], ["369_1", 3, 3, 3], ["315_1", 0, 3, 3], ["432", 0, 1, 1], ["415", 0, 4, 1], ["415", 2, 4, 1], ["415", 1, 4, 1], ["415", 3, 4, 1], ["415_1", 0, 4, 1], ["416", 0, 4, 1], ["416", 3, 4, 1], ["416", 1, 4, 1], ["416", 3, 4, 1], ["417", 0, 1, 1], ["418", 0, 1, 1], ["419", 0, 4, 1], ["419", 2, 4, 1], ["416", 2, 4, 1], ["380", 0, 3, 2], ["380", 2, 3, 2], ["380", 1, 3, 2], ["380", 3, 3, 2], ["418", 0, 1, 1], ["444", 0, 2, 2], ["444", 3, 2, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 4, "obj": [[2, "421", 724, -16, 14, 11, 0], [2, "421", 597, -3, 14, 11, 0], [2, "422", 663, 28, 16, 14, 0], [2, "262_2", -3, 4, 48, 39, 2], [2, "421", 679, 40, 14, 11, 0], [2, "420", 705, 48, 16, 13, 0], [2, "421", 819, 55, 14, 11, 0], [2, "420", 846, 53, 16, 13, 0], [4, 8, 588, 71, 0, 4006], [2, "423", 824, 76, 4, 6, 0], [2, "429", 607, 25, 64, 63, 0], [2, "429", -17, 45, 64, 63, 0], [2, "420", 315, 97, 16, 13, 0], [4, 1, 767, 111, 1, 4008], [2, "429", 29, 56, 64, 63, 2], [2, "423", 706, 113, 4, 6, 0], [2, "429", 515, 62, 64, 63, 0], [2, "429", 624, 62, 64, 63, 0], [2, "422", 679, 114, 16, 14, 2], [2, "420", 828, 117, 16, 13, 0], [2, "420", 797, 145, 16, 13, 0], [2, "422", 509, 152, 16, 14, 0], [2, "420", 654, 167, 16, 13, 0], [2, "421", 779, 170, 14, 11, 0], [2, "395", 736, 115, 42, 72, 0], [2, "395", 707, 134, 42, 72, 2], [2, "429", 392, 309, 64, 63, 0], [4, 2, 525, 383, 1, 4008], [2, "262_2", 839, 384, 48, 39, 2], [2, "329", 789, 399, 42, 37, 2], [2, "328", 817, 416, 32, 29, 0], [2, "422", 585, 442, 16, 14, 0], [2, "423", 602, 464, 4, 6, 0], [4, 6, 516, 503, 0, 4006], [2, "328", 748, 504, 32, 29, 0], [2, "393", 302, 519, 44, 77, 0], [4, 3, 96, 612, 0, 4005], [2, "395", 291, 618, 42, 72, 0], [4, 5, 516, 719, 0, 4001], [4, 4, 468, 791, 0, 4005], [2, "329", 700, 766, 42, 37, 2], [4, 7, 91, 867, 0, 4022], [2, "429", 841, 804, 64, 63, 2], [2, "429", 817, 892, 64, 63, 0], [2, "366_1", 154, 959, 32, 48, 0], [2, "422", 155, 997, 16, 14, 0], [2, "429", 175, 968, 64, 63, 0], [2, "429", 393, 972, 64, 63, 0], [2, "85_3", 626, 991, 48, 53, 2]]}, {"type": 3, "obj": [[2, "327", 322, 199, 30, 22, 0], [2, "403", 94, 543, 40, 28, 0], [2, "404", 46, 559, 74, 45, 2], [2, "410", 110, 540, 14, 35, 0], [2, "402", 465, 682, 48, 26, 0], [2, "403", 19, 612, 40, 28, 0], [2, "401", 173, 510, 62, 36, 2], [2, "443", 218, 507, 40, 58, 0], [2, "445", 194, 365, 50, 22, 2], [2, "445", 236, 330, 50, 22, 2], [2, "445", -10, 374, 50, 22, 2], [2, "445", 61, 365, 50, 22, 2], [2, "253_1", -12, 823, 92, 53, 0], [2, "413", -4, 750, 44, 72, 0], [2, "413", 13, 777, 44, 72, 0], [2, "253_1", 687, 787, 92, 53, 0], [2, "253_1", 693, 958, 92, 53, 2], [2, "325", 14, 370, 50, 37, 2], [2, "325", 179, 0, 50, 37, 0], [2, "253_1", 349, 121, 92, 53, 2], [2, "325", 642, 336, 50, 37, 0], [2, "253_1", 589, 260, 92, 53, 2], [2, "253_1", 334, 69, 92, 53, 2], [2, "253_1", 516, 201, 92, 53, 2], [2, "253_1", 319, 208, 92, 53, 2], [2, "253_1", 304, 855, 92, 53, 2], [2, "253_1", 399, 929, 92, 53, 2], [2, "253_1", 639, 444, 92, 53, 2], [2, "253_1", 268, 267, 92, 53, 2], [2, "253_1", 331, 135, 92, 53, 2], [2, "253_1", 328, 318, 92, 53, 0], [2, "398", 318, 87, 58, 78, 2], [2, "325", -5, 933, 50, 37, 2], [2, "443", 455, 401, 40, 58, 2], [2, "391", 313, 338, 86, 55, 0], [2, "391", 562, 431, 86, 55, 0], [2, "403", 654, 806, 40, 28, 0], [2, "439", 649, 811, 64, 42, 0], [2, "411", 429, 359, 44, 40, 2], [2, "414", 540, 273, 74, 164, 0], [2, "414", 473, 245, 74, 164, 0], [2, "414", 503, 260, 74, 164, 0], [2, "414", 724, -22, 74, 164, 0], [2, "399", 77, 104, 58, 72, 0], [2, "411", 450, 267, 44, 40, 2], [2, "398", 281, 98, 58, 78, 2], [2, "398", 242, 113, 58, 78, 2], [2, "398", 206, 124, 58, 78, 2], [2, "399", 243, 191, 58, 72, 0], [2, "398", 271, 204, 58, 78, 0], [2, "402", 226, 690, 48, 26, 0], [2, "402", 262, 730, 48, 26, 0], [2, "402", 257, 705, 48, 26, 0], [2, "405", 153, 600, 6, 13, 0], [2, "392", 38, 372, 118, 69, 2], [2, "392", 233, 333, 118, 69, 2], [2, "392", 214, 354, 118, 69, 2], [2, "436", 552, 639, 34, 28, 0], [2, "399", 650, 381, 58, 72, 0], [2, "390", 661, 407, 102, 80, 0], [2, "398", 603, 370, 58, 78, 0], [2, "390", 668, 395, 102, 80, 0], [2, "391", 685, 485, 86, 55, 0], [2, "392", 588, 460, 118, 69, 0], [2, "426", 315, 1005, 26, 22, 0], [2, "426", 465, 535, 26, 22, 0], [2, "399", 779, 428, 58, 72, 0], [2, "398", 738, 418, 58, 78, 0], [2, "397", 596, 342, 28, 61, 0], [2, "399", 827, 425, 58, 72, 2], [2, "391", 677, 501, 86, 55, 0], [2, "392", 580, 474, 118, 69, 0], [2, "399", -13, 176, 58, 72, 0], [2, "398", 116, 115, 58, 78, 0], [2, "398", 589, 76, 58, 78, 2], [2, "407", 127, 574, 54, 27, 0], [2, "398", 549, 95, 58, 78, 2], [2, "390", 287, 233, 102, 80, 0], [2, "403", 547, 529, 40, 28, 0], [2, "403", 161, 687, 40, 28, 0], [2, "401", 204, 705, 62, 36, 0], [2, "405", 430, 584, 6, 13, 2], [2, "398", 399, 66, 58, 78, 2], [2, "398", 431, 69, 58, 78, 0], [2, "398", 471, 85, 58, 78, 0], [2, "433", 626, 682, 62, 61, 0], [2, "434", 557, 667, 70, 54, 0], [2, "434", 315, 891, 70, 54, 2], [2, "433", 257, 907, 62, 61, 2], [2, "435", 216, 923, 50, 77, 2], [2, "434", 362, 999, 70, 54, 0], [2, "434", 758, 510, 70, 54, 0], [2, "433", 827, 525, 62, 61, 0], [2, "435", 682, 699, 50, 77, 0], [2, "438", 700, 776, 26, 43, 0], [2, "437", 610, 584, 20, 19, 0], [2, "436", 356, 972, 34, 28, 0], [2, "439", 382, 905, 64, 42, 0], [2, "437", 446, 901, 20, 19, 0], [2, "437", 495, 888, 20, 19, 0], [2, "436", 356, 864, 34, 28, 2], [2, "437", 336, 827, 20, 19, 0], [2, "438", 220, 998, 26, 43, 2], [2, "437", 178, 764, 20, 19, 0], [2, "437", 58, 717, 20, 19, 0], [2, "437", 701, 572, 20, 19, 2], [2, "437", 845, 621, 20, 19, 2], [2, "440", 554, 605, 34, 34, 0], [2, "440", 357, 832, 34, 34, 2], [2, "440", 227, 783, 34, 34, 2], [2, "440", 355, 940, 34, 34, 0], [2, "441", 581, 585, 28, 21, 0], [2, "437", 659, 572, 20, 19, 0], [2, "437", 826, 620, 20, 19, 0], [2, "441", 630, 571, 28, 21, 0], [2, "437", 680, 572, 20, 19, 0], [2, "441", 718, 573, 28, 21, 2], [2, "441", 799, 607, 28, 21, 2], [2, "441", 770, 596, 28, 21, 2], [2, "437", 749, 593, 20, 19, 0], [2, "437", 742, 590, 20, 19, 2], [2, "392", 46, 386, 118, 69, 2], [2, "391", -8, 400, 86, 55, 0], [2, "391", 163, 380, 86, 55, 0], [2, "441", 466, 889, 28, 21, 0], [2, "441", 538, 873, 28, 21, 0], [2, "437", 516, 888, 20, 19, 0], [2, "437", 566, 872, 20, 19, 0], [2, "439", 603, 838, 64, 42, 0], [2, "437", 587, 871, 20, 19, 0], [2, "405", 330, 787, 6, 13, 0], [2, "409", 255, 687, 28, 22, 0], [2, "441", 310, 811, 28, 21, 2], [2, "437", 246, 813, 20, 19, 2], [2, "437", 266, 814, 20, 19, 2], [2, "437", 277, 813, 20, 19, 0], [2, "437", 289, 811, 20, 19, 2], [2, "441", 198, 766, 28, 21, 2], [2, "439", 75, 723, 64, 42, 2], [2, "437", 137, 756, 20, 19, 0], [2, "441", 154, 757, 28, 21, 2], [2, "437", 47, 712, 20, 19, 0], [2, "441", -1, 696, 28, 21, 2], [2, "437", 27, 709, 20, 19, 0], [2, "56_1", 365, 127, 76, 47, 2], [2, "412", 560, 307, 64, 100, 0], [2, "411", 576, 379, 44, 40, 0], [2, "411", 510, 222, 44, 40, 0], [2, "411", 484, 231, 44, 40, 0], [2, "411", 454, 241, 44, 40, 0], [2, "412", 419, 264, 64, 100, 0], [2, "413", 441, 309, 44, 72, 0], [2, "411", 430, 347, 44, 40, 0], [2, "411", 568, 391, 44, 40, 0], [2, "411", 596, 292, 44, 40, 0], [2, "399", 517, 98, 58, 72, 0], [2, "390", 294, 223, 102, 80, 0], [2, "56_1", 349, 111, 76, 47, 2], [2, "56_1", 337, 94, 76, 47, 2], [2, "398", 364, 247, 58, 78, 0], [2, "398", 385, 258, 58, 78, 0], [2, "399", 162, 126, 58, 72, 0], [2, "412", 577, 369, 64, 100, 0], [2, "411", 599, 436, 44, 40, 0], [2, "402", 665, 197, 48, 26, 0], [2, "403", 666, 221, 40, 28, 0], [2, "412", 592, -67, 64, 100, 0], [2, "412", 613, -55, 64, 100, 0], [2, "413", 613, -12, 44, 72, 0], [2, "398", -16, 99, 58, 78, 0], [2, "398", 202, 187, 58, 78, 2], [2, "399", 156, 194, 58, 72, 0], [2, "398", 114, 186, 58, 78, 0], [2, "399", 31, 104, 58, 72, 2], [2, "399", 74, 185, 58, 72, 2], [2, "398", 28, 184, 58, 78, 0], [2, "414", 758, -8, 74, 164, 0], [2, "402", 738, 187, 48, 26, 0], [2, "403", 600, 723, 40, 28, 0], [2, "402", 376, 693, 48, 26, 0], [2, "403", 412, 761, 40, 28, 0], [2, "402", 506, 531, 48, 26, 0], [2, "442", 426, 596, 8, 21, 2], [2, "442", 491, 745, 8, 21, 2], [2, "442", 327, 795, 8, 21, 2], [2, "392", 316, 349, 118, 69, 2], [2, "443", 493, 401, 40, 58, 0], [2, "443", 389, 628, 40, 58, 2], [2, "443", 427, 628, 40, 58, 0], [2, "443", 180, 507, 40, 58, 2], [2, "443", 176, 626, 40, 58, 2], [2, "443", 214, 626, 40, 58, 0], [2, "403", 654, 767, 40, 28, 0], [2, "404", 317, 668, 74, 45, 0], [2, "391", 61, 431, 86, 55, 2], [2, "392", 223, 376, 118, 69, 2], [2, "404", 561, 749, 74, 45, 0], [2, "430", 664, 722, 20, 27, 0], [2, "403", 517, 762, 40, 28, 0], [2, "404", 514, 789, 74, 45, 0], [2, "407", 472, 722, 54, 27, 2], [2, "401", 461, 767, 62, 36, 2], [2, "403", 416, 802, 40, 28, 0], [2, "401", 371, 780, 62, 36, 0], [2, "404", 318, 715, 74, 45, 2], [2, "425", 403, 816, 30, 36, 2], [2, "392", 126, 405, 118, 69, 2], [2, "392", -80, 408, 118, 69, 0], [2, "391", 15, 435, 86, 55, 0], [2, "409", 603, 778, 28, 22, 0], [2, "85_3", 618, 420, 48, 53, 0], [2, "327", 724, 466, 30, 22, 0], [2, "326", 752, 479, 18, 14, 0], [2, "328", 130, 749, 32, 29, 0], [2, "328", 12, 708, 32, 29, 0], [2, "327", 580, 601, 30, 22, 0], [2, "408", 641, 745, 38, 30, 0], [2, "329", 566, 433, 42, 37, 2], [2, "403", 443, 600, 40, 28, 0], [2, "328", -10, 382, 32, 29, 0], [2, "429", 822, 470, 64, 63, 0], [2, "392", 574, 142, 118, 69, 2], [2, "411", 720, -30, 44, 40, 0], [2, "411", 705, -28, 44, 40, 2], [2, "411", 688, 92, 44, 40, 2], [2, "412", 825, -7, 64, 100, 2], [2, "411", 641, 97, 44, 40, 0], [2, "412", 650, -44, 64, 100, 0], [2, "412", 808, 45, 64, 100, 2], [2, "412", 677, -14, 64, 100, 2], [2, "61_1", 379, 941, 16, 17, 2], [2, "59_1", 299, 854, 84, 49, 2], [2, "60_1", 336, 906, 16, 16, 2], [2, "60_1", 349, 916, 16, 16, 2], [2, "61_1", 366, 929, 16, 17, 2], [2, "57_1", 316, 865, 72, 44, 2], [2, "57_1", 325, 867, 72, 44, 2], [2, "56_1", 334, 871, 76, 47, 2], [2, "55_1", 353, 880, 70, 46, 2], [2, "426", 587, 833, 26, 22, 0], [2, "56_1", 363, 893, 76, 47, 2], [2, "55_1", 379, 904, 70, 46, 2], [2, "54_1", 390, 917, 86, 53, 2], [2, "412", 797, 73, 64, 100, 0], [2, "412", 696, -7, 64, 100, 0], [2, "412", 781, 114, 64, 100, 0], [2, "411", 816, 153, 44, 40, 0], [2, "413", 695, 58, 44, 72, 0], [2, "412", 662, 28, 64, 100, 2], [2, "412", 660, 45, 64, 100, 0], [2, "411", 655, 128, 44, 40, 0], [2, "366_1", 660, 130, 32, 48, 0], [2, "61_1", 595, 285, 16, 17, 2], [2, "59_1", 515, 198, 84, 49, 2], [2, "60_1", 552, 250, 16, 16, 2], [2, "60_1", 565, 260, 16, 16, 2], [2, "61_1", 582, 273, 16, 17, 2], [2, "57_1", 532, 209, 72, 44, 2], [2, "57_1", 541, 211, 72, 44, 2], [2, "56_1", 550, 215, 76, 47, 2], [2, "55_1", 569, 224, 70, 46, 2], [2, "56_1", 579, 237, 76, 47, 2], [2, "55_1", 595, 248, 70, 46, 2], [2, "54_1", 606, 261, 86, 53, 2], [2, "411", 619, 22, 44, 40, 0], [2, "366_1", 767, 172, 32, 48, 0], [2, "411", 677, 239, 44, 40, 2], [2, "411", 713, 229, 44, 40, 2], [2, "366_1", 746, 187, 32, 48, 2], [2, "393", 727, 209, 44, 77, 0], [2, "429", 209, 147, 64, 63, 0], [2, "85_3", 607, 114, 48, 53, 0], [2, "262_2", 261, 161, 48, 39, 0], [2, "409", 693, 177, 28, 22, 0], [2, "430", 658, 198, 20, 27, 2], [2, "411", 556, -13, 44, 40, 0], [2, "412", 833, 85, 64, 100, 0], [2, "429", 764, 194, 64, 63, 0], [2, "411", 841, 160, 44, 40, 2], [2, "411", 816, 27, 44, 40, 0], [2, "262_2", 213, 235, 48, 39, 0], [2, "429", 58, 211, 64, 63, 0], [2, "262_2", 93, 147, 48, 39, 2], [2, "262_2", 542, 142, 48, 39, 0], [2, "327", 439, 129, 30, 22, 0], [2, "326", 614, 328, 18, 14, 0], [2, "327", 349, 298, 30, 22, 0], [2, "327", 684, 271, 30, 22, 0], [2, "262_2", 322, 129, 48, 39, 2], [2, "329", 614, 333, 42, 37, 0], [2, "329", 856, 163, 42, 37, 0], [2, "328", 300, 151, 32, 29, 0], [2, "328", 596, 33, 32, 29, 0], [2, "328", 574, 16, 32, 29, 0], [2, "328", 379, 310, 32, 29, 0], [2, "328", 370, 940, 32, 29, 0], [2, "422", 368, 162, 16, 14, 0], [2, "326", 571, 873, 18, 14, 0], [2, "262_2", 183, 238, 48, 39, 0], [2, "262_2", 520, -15, 48, 39, 2], [2, "328", 11, 230, 32, 29, 0], [2, "328", 496, 74, 32, 29, 0], [2, "329", 396, 40, 42, 37, 0], [2, "328", 719, 274, 32, 29, 0], [2, "326", 708, 268, 18, 14, 0], [2, "329", 157, 99, 42, 37, 2], [2, "327", 190, 117, 30, 22, 0], [2, "328", 266, 248, 32, 29, 2], [2, "328", 476, 1006, 32, 29, 0], [2, "85_3", 827, 607, 48, 53, 2], [2, "327", 16, 935, 30, 22, 0], [2, "326", 3, 965, 18, 14, 0], [2, "325", 172, 919, 50, 37, 0], [2, "420", 739, 586, 16, 13, 0], [2, "420", 790, 608, 16, 13, 0], [2, "420", 711, 797, 16, 13, 0], [2, "420", 217, 781, 16, 13, 0], [2, "422", 178, 924, 16, 14, 0], [2, "420", 208, 942, 16, 13, 0], [2, "422", -1, 936, 16, 14, 0], [2, "420", 465, 262, 16, 13, 0], [2, "421", 467, 329, 14, 11, 2], [2, "421", 418, 372, 14, 11, 2], [2, "420", 426, 376, 16, 13, 0], [2, "421", 591, 318, 14, 11, 0], [2, "420", 586, 359, 16, 13, 0], [2, "422", 580, 382, 16, 14, 0], [2, "420", 600, 408, 16, 13, 0], [2, "421", 270, 209, 14, 11, 0], [2, "325", 486, 894, 50, 37, 2], [2, "329", -22, 692, 42, 37, 2], [2, "56_1", 325, 76, 76, 47, 2], [2, "253_1", 118, 375, 92, 53, 0], [2, "420", 681, 283, 16, 13, 0], [2, "420", 746, 413, 16, 13, 0], [2, "327", 32, 351, 30, 22, 0], [2, "422", 337, 299, 16, 14, 0], [2, "422", 631, 293, 16, 14, 0], [2, "420", 686, 385, 16, 13, 0], [2, "327", 196, 3, 30, 22, 0], [2, "325", 382, 967, 50, 37, 0], [2, "327", 367, 966, 30, 22, 0], [2, "422", 151, 376, 16, 14, 0], [2, "422", 28, 386, 16, 14, 0], [2, "422", 225, 355, 16, 14, 0], [2, "422", 785, 487, 16, 14, 0], [2, "425", 803, 557, 30, 36, 0], [2, "426", 752, 541, 26, 22, 0], [2, "403", 471, 807, 40, 28, 0], [2, "328", 570, 609, 32, 29, 0], [2, "392", 786, 942, 118, 69, 0], [2, "391", 732, 959, 86, 55, 0], [2, "435", 846, 827, 50, 77, 2], [2, "391", 668, 985, 86, 55, 2], [2, "325", 636, 1006, 50, 37, 0], [2, "426", 824, 1004, 26, 22, 0], [2, "325", 773, 627, 50, 37, 2], [2, "327", 768, 624, 30, 22, 0], [2, "328", 690, 809, 32, 29, 0], [2, "328", 790, 929, 32, 29, 0], [2, "327", 781, 938, 30, 22, 0], [2, "328", 818, 169, 32, 29, 2], [2, "420", 805, 187, 16, 13, 0], [2, "412", -27, 737, 64, 100, 2], [2, "412", -29, 806, 64, 100, 0], [2, "411", -5, 868, 44, 40, 0], [2, "420", 11, 864, 16, 13, 0], [2, "422", 49, 831, 16, 14, 0], [2, "422", 9, 791, 16, 14, 0], [2, "421", 42, 794, 14, 11, 2], [2, "411", 15, 888, 44, 40, 0], [2, "366_1", -15, 891, 32, 48, 2], [2, "325", 33, 843, 50, 37, 2], [2, "327", 65, 846, 30, 22, 0], [2, "420", -7, 834, 16, 13, 0], [2, "420", 14, 919, 16, 13, 0], [2, "401", 578, 516, 62, 36, 0], [2, "430", 203, 687, 20, 27, 0], [2, "327", 521, 878, 30, 22, 0], [2, "328", 543, 875, 32, 29, 0], [2, "327", 452, 898, 30, 22, 0], [2, "420", 137, 994, 16, 13, 2], [2, "422", 522, 917, 16, 14, 0], [2, "422", 404, 961, 16, 14, 0], [2, "326", 47, 916, 18, 14, 0], [2, "422", 36, 920, 16, 14, 0], [2, "325", -14, 1003, 50, 37, 0], [2, "420", 6, 1021, 16, 13, 0], [2, "420", 473, 923, 16, 13, 0], [2, "395", 533, 784, 42, 72, 2], [2, "329", 335, 826, 42, 37, 2], [2, "53_1", 369, 857, 18, 9, 2], [2, "49_1", 357, 835, 20, 34, 0], [2, "50_1", 386, 853, 8, 23, 0], [2, "52_1", 388, 864, 46, 22, 2], [2, "53_1", 315, 883, 18, 9, 2], [2, "49_1", 302, 861, 20, 34, 0], [2, "50_1", 332, 877, 8, 23, 0], [2, "50_1", 428, 879, 8, 23, 0], [2, "52_1", 334, 887, 46, 22, 2], [2, "51_1", 431, 886, 38, 35, 2], [2, "262_2", 275, 883, 48, 39, 0], [2, "85_3", 478, 869, 48, 53, 0], [2, "50_1", 374, 904, 8, 23, 0], [2, "49_1", 454, 901, 20, 34, 0], [2, "51_1", 377, 910, 38, 35, 2], [2, "49_1", 399, 927, 20, 34, 0], [2, "53_1", 586, 202, 18, 9, 2], [2, "49_1", 574, 180, 20, 34, 0], [2, "50_1", 603, 198, 8, 23, 0], [2, "52_1", 605, 209, 46, 22, 2], [2, "53_1", 532, 228, 18, 9, 2], [2, "49_1", 519, 206, 20, 34, 0], [2, "421", 513, 230, 14, 11, 2], [2, "50_1", 549, 222, 8, 23, 0], [2, "50_1", 645, 224, 8, 23, 0], [2, "52_1", 551, 232, 46, 22, 2], [2, "51_1", 648, 231, 38, 35, 2], [2, "50_1", 591, 249, 8, 23, 0], [2, "49_1", 671, 246, 20, 34, 0], [2, "51_1", 594, 255, 38, 35, 2], [2, "421", 673, 279, 14, 11, 0], [2, "49_1", 616, 272, 20, 34, 0], [2, "422", 631, 304, 16, 14, 0], [2, "328", 620, 302, 32, 29, 0], [2, "401", 386, 467, 62, 36, 0], [2, "443", 371, 497, 40, 58, 2], [2, "443", 409, 497, 40, 58, 0], [2, "403", 131, 502, 40, 28, 0], [2, "402", 349, 431, 48, 26, 0], [2, "401", 375, 733, 62, 36, 0], [2, "408", 498, 511, 38, 30, 0], [2, "63_1", 316, 192, 16, 31, 0], [2, "62_1", 379, 227, 16, 27, 0], [2, "62_1", 389, 54, 16, 27, 0], [2, "63_1", 316, 74, 16, 31, 0], [2, "407", 305, 766, 54, 27, 0], [2, "407", 403, 564, 54, 27, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, 96, 97, 98, 116, 111, 112, 112, 112, 118, 118, 118, 118, 118, 101, 98, -112, -110, -104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -120, -121, -1, -1, -1, -1, 99, 100, 101, 103, 107, 112, 112, 112, 118, 118, 118, 118, 118, 118, 101, 97, 98, -107, -104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -122, -121, -1, -1, -1, 111, 112, 112, 112, 112, 112, 113, 110, 116, 115, 119, 115, 115, 119, 118, 100, 101, 102, -112, -111, -110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -117, -118, -120, -121, -1, 116, 119, 112, 112, 112, 113, 114, -119, -120, -121, -1, -1, -1, 116, 115, 115, 115, 114, -115, -114, -113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -118, -1, -1, 116, 115, 115, 109, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -105, -106, -1, -111, -1, -1, -1, -1, -1, -1, -1, -1, -123, -1, -1, -1, -1, -1, -1, -1, -1, -111, -105, -111, -110, -104, -105, -1, -1, -1, -1, -1, -1, -104, -105, -110, -104, -109, -114, -114, -108, -114, -114, -114, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -108, -108, -114, -108, -104, -105, -1, -1, -1, -1, -1, -104, -105, -110, -109, 104, 103, 102, -1, 96, 97, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -107, -108, -1, -1, -1, -1, -1, -107, -108, -118, -121, 107, 106, 105, -1, 99, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -123, -111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -109, 116, 118, 117, -1, 108, 109, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -105, -1, -105, -110, -114, -115, -114, -113, -104, -111, -110, -104, -1, -111, -111, -111, -111, -111, -1, -1, -1, -109, 114, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -105, -110, -108, -109, -1, 103, 102, -1, -107, -108, -113, -1, -115, -114, -108, -108, -108, -109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -104, -105, -106, 96, 97, 103, 106, 118, 117, -1, -1, -1, -1, 104, 103, 102, -1, -1, -107, -108, -109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -115, -114, -113, -1, -107, -108, -109, 99, 100, 106, 115, 115, 114, -1, 96, 98, -1, 107, 106, 105, -1, -1, -1, -1, -115, -114, -104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 103, 102, -1, -1, -1, -1, 108, 109, 115, -1, -1, -1, -1, 111, 112, 102, -1, -1, -1, -1, 31, 31, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 108, 119, 101, 102, -1, 104, 103, 102, -1, -1, -1, -107, -109, -1, 108, 109, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -109, 116, 119, 105, -1, 111, 112, 117, -1, -1, -120, -121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 114, -1, 108, 109, 110, -119, -120, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -127, -126, -116, -111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -105, -106, -1, -1, -1, -1, -1, -123, -123, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -108, -109, -1, -1, -1, -111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -110, -110, -109, -112, -111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -115, -114, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -120, -121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -115, -114, -113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 107, 106, 105, -1, -1, -1, -1, -119, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 108, 108, 109, 115, 115, 114, -1, -1, -115, -108, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 97, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 119, 101, 102, -1, -114, 104, 103, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 107, 106, 105, -1, -1, 111, 113, 119, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 112, 113, -1, -1, 104, 116, 110, 111, 101, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 108, 109, 110, -1, -1, 107, -1, -1, 111, 118, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, -1, -1, 111, 118, 101, 97, 98, -1, -1, -1, -1, 96, 97, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 102, -120, -121, 119, 112, 115, 109, 115, 114, -1, -1, -1, -1, 99, 100, 101, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 102, -1, -1, -1, 107, 106, 105, -117, -118, 111, 112, 113, -1, -1, 96, 97, 97, 98, -1, -1, -119, -120, -121, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 103, 107, 106, 105, -1, -1, -1, 111, 112, 113, -111, -1, 108, 109, 110, -1, -1, 108, 115, 115, 114, -119, -120, -121, -1, -1, -1, -1, -1, -1, -1, -1, -115, -109, -1, 111, 112, 113, 109, 110, -1, -1, -1, 108, 109, 110, -111, -121, -1, -1, -1, -1, -1, -119, -121, -1, -1, -112, -111, -1, -1, 55, -1, -1, -105, -115, -114, -113, -1, -1, -1, 108, 109, 110, -1, -1, -1, -1, -1, -127, -126, -125, -111, -106, -1, -1, -1, -1, -1, -107, -109, -1, -1, -1, -1, -1, -1, -1, -105, -115, -114, -113, -1, 96, 97, 98, -1, 104, 103, 102, 103, 102, -1, -1, -1, -124, -123, -122, -108, -109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -108, -109, -1, -1, -1, 99, 100, 101, 113, 115, 115, 119, 106, 105, -1, -1, -1, -107, -108, -109, 104, 103, 102, -1, -1, -1, -127, -126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 108, 109, 109, 114, -1, -1, 116, 115, 114, -1, -1, -1, 82, 73, 77, 107, 106, 105, -1, -1, -1, -124, -123, -117, -117, -1, -1, -1, -1, -1, -1, -1, -127, -126, -120, -126, -121, -1, -1, -1, -1, -127, -126, -125, -1, 82, 82, 81, 80, 79, 74]}, {"type": 3, "obj": [[2, "427", 294, 642, 24, 24, 2], [2, "427", 270, 630, 24, 24, 2], [2, "427", 199, 595, 24, 24, 2], [2, "427", 223, 607, 24, 24, 2], [2, "427", 247, 619, 24, 24, 2], [2, "427", 298, 644, 24, 24, 2], [2, "427", 322, 643, 24, 24, 0], [2, "427", 346, 631, 24, 24, 0], [2, "427", 370, 619, 24, 24, 0], [2, "427", 394, 607, 24, 24, 0], [2, "427", 418, 595, 24, 24, 0], [2, "427", 424, 593, 24, 24, 0], [2, "338", 299, 627, 34, 31, 2], [2, "338", 411, 576, 34, 31, 0], [2, "338", 389, 587, 34, 31, 0], [2, "338", 199, 577, 34, 31, 2], [2, "338", 367, 598, 34, 31, 0], [2, "338", 345, 609, 34, 31, 0], [2, "338", 310, 626, 34, 31, 0], [2, "338", 323, 620, 34, 31, 0], [2, "338", 220, 588, 34, 31, 2], [2, "338", 242, 599, 34, 31, 2], [2, "338", 264, 610, 34, 31, 2], [2, "338", 286, 621, 34, 31, 2], [2, "340", 209, 565, 34, 18, 0], [2, "340", 230, 554, 34, 18, 0], [2, "340", 252, 543, 34, 18, 0], [2, "340", 274, 532, 34, 18, 0], [2, "340", 296, 521, 34, 18, 0], [2, "340", 401, 565, 34, 18, 2], [2, "340", 379, 554, 34, 18, 2], [2, "340", 357, 543, 34, 18, 2], [2, "340", 335, 532, 34, 18, 2], [2, "340", 313, 521, 34, 18, 2], [2, "70_1", 374, 569, 50, 26, 0], [2, "70_1", 349, 582, 50, 26, 0], [2, "70_1", 323, 595, 50, 26, 0], [2, "70_1", 297, 608, 50, 26, 0], [2, "70_1", 296, 530, 50, 26, 0], [2, "70_1", 271, 543, 50, 26, 0], [2, "70_1", 245, 556, 50, 26, 0], [2, "70_1", 219, 569, 50, 26, 0], [2, "70_1", 322, 543, 50, 26, 0], [2, "70_1", 297, 556, 50, 26, 0], [2, "70_1", 271, 569, 50, 26, 0], [2, "70_1", 245, 582, 50, 26, 0], [2, "70_1", 348, 556, 50, 26, 0], [2, "70_1", 323, 569, 50, 26, 0], [2, "70_1", 297, 582, 50, 26, 0], [2, "70_1", 271, 595, 50, 26, 0], [2, "253_1", 778, 192, 92, 53, 0], [2, "404", 510, 487, 74, 45, 0], [2, "93_1", 325, 555, 56, 29, 0], [2, "93_1", 268, 555, 56, 29, 2], [2, "93_1", 268, 584, 56, 29, 3], [2, "93_1", 325, 584, 56, 29, 1]]}, {"type": 2, "data": [121, 121, -128, 127, 122, -128, 127, 121, 122, 31, 34, 33, 18, 32, 22, 31, 121, 122, 121, 122, 121, 122, 121, 122, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 82, 123, 123, 126, 125, 124, 126, 125, 121, 122, 122, 13, 12, 40, 18, 19, 32, 123, 124, 123, 124, 123, 124, 123, 124, 13, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 121, 122, 121, 122, 121, 123, -128, 127, 124, 124, 31, 5, 10, 9, 37, 21, 31, 121, 122, 121, 122, 124, 121, 122, 121, 122, 13, 13, -1, -1, -1, -1, -1, -1, -1, -1, 123, 124, 123, 124, 123, 124, 126, 125, 122, 121, 122, 13, 13, 5, 9, 18, 19, 123, 124, 123, 124, 31, 123, 124, 123, 124, 31, 13, -1, -1, -1, -1, -1, -1, -1, -1, 31, 31, 31, 31, 31, 31, 31, 123, 124, 123, 124, 31, 4, 13, 12, 31, 31, -1, -1, 31, -1, -1, 31, 31, -1, -1, 31, 13, -1, -1, 82, 82, -1, -1, -1, -1, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 32, 31, 31, -1, -1, -1, -1, -1, 31, 13, 13, 82, 82, 82, 82, -1, -1, -1, -1, 22, 22, 31, 22, 31, 31, 31, 31, 31, 31, -1, -1, -1, 31, 31, 31, 31, 23, 28, 32, 31, 31, -1, -1, 31, 13, 13, 13, 82, 81, 82, 83, 74, -1, -1, 13, 13, 13, 22, 22, 22, 22, 22, 31, 22, 22, 22, 22, 31, 31, 31, 5, 23, 27, 44, 29, 28, 28, 19, 32, 31, 31, 13, -1, 82, 70, 68, 68, 69, -1, -1, 13, 13, 13, 13, 13, 31, 31, 31, 22, 13, 22, 31, 22, 31, 22, 23, 32, 30, 40, 40, 40, 37, 40, 40, 29, 31, 83, 83, 83, 82, 74, 82, 82, -1, 13, 13, 13, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, -1, 28, 27, 18, 20, 43, 11, 10, 10, 10, 9, 14, -1, -1, 83, 67, 68, 69, 82, 13, 13, 13, 13, 13, 28, 32, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, -1, -1, -1, 9, 10, 10, 14, 13, 4, -1, 83, 83, 83, -1, -1, 82, 82, 82, 13, 13, 13, 13, 13, 23, 44, 29, 28, 28, 32, 31, 22, 121, 122, 31, 23, 28, 27, 40, 40, -1, -1, 14, 13, -1, -1, -1, -1, 83, 83, -1, -1, -1, 23, 32, 31, 31, 13, 22, 31, 30, 37, 38, 36, 37, 29, 28, 32, 123, 124, 23, 20, 40, 40, 37, 11, 31, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 23, 27, 29, 32, 31, 23, 28, 28, 27, 1, 2, 11, 10, 10, 9, 18, 19, 19, 27, 11, 10, 10, 10, 14, 13, 13, 13, 13, 21, -1, -1, -1, -1, -1, 31, 5, 1, 2, 44, 18, 28, 27, 40, 40, 11, 31, 5, 14, 13, 13, 5, 9, 41, 0, 1, 14, 22, -1, -1, 31, 31, 31, 31, -1, -1, -1, -1, -1, -1, -1, 31, 13, 13, 5, 10, 9, 41, 0, 1, 10, 14, -128, 127, 22, 4, 13, 28, 27, 40, 18, 32, -1, -1, -1, -1, -1, -1, 31, 31, 31, -1, -1, -1, -1, -1, -1, 13, 13, 13, 13, 23, 27, 44, 3, 4, 121, 122, 126, 125, 31, -1, -1, 13, 5, 14, 14, -1, -1, -1, -1, -1, -1, 45, 45, 45, 61, 60, 73, 83, 83, -1, 31, -1, 13, 13, 13, 12, 43, 11, 14, 13, 123, 124, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, 45, 45, 45, 61, 60, 59, 73, 71, 83, 45, 45, -1, 31, 31, 31, 13, 31, 5, 14, 13, 13, 31, 31, -1, -1, -1, -1, -1, -1, -1, 45, 45, 45, 45, 45, 61, 60, 59, 83, 73, 76, 77, 81, 83, 82, 56, 57, 65, 13, 13, 13, 31, -1, 13, 13, 13, 13, 31, 31, 83, -1, -1, -1, -1, 45, 45, 45, 45, 45, 61, 60, 59, 82, 82, 70, 83, 83, 83, 83, 83, 76, 77, 54, 45, 31, 13, 13, 13, 13, 31, -1, 31, 31, 31, -1, 83, 83, 83, 77, 55, 56, 56, 56, 56, 59, 82, 82, 82, 82, 82, 70, 79, 83, 83, 83, 83, 83, 66, 54, 45, -1, -1, 31, 31, 31, 13, 13, 31, 13, 13, 13, 83, 83, 83, 75, 76, 77, 72, 76, 77, 82, 82, 82, 82, 73, 72, 71, 83, 83, 83, 83, 83, 83, 74, 55, 57, 45, 45, -1, -1, -1, -1, 31, 31, 13, 13, 31, 83, 83, 83, 83, 83, 83, 83, 83, 74, 82, 82, 81, 80, 79, 83, 83, 83, 83, 83, 83, 83, 83, 83, 77, 55, 60, 59, 82, 82, 82, 82, 82, 82, -1, -1, 31, 83, 83, 83, 83, 83, 83, 83, 83, 75, 76, 77, -1, 83, 83, 83, -1, 83, 83, 83, 83, 120, 120, 67, 69, 47, 48, 48, 48, 49, 53, 52, 51, 82, 82, 82, 82, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, -1, -1, -1, -1, 83, 83, 83, 83, 83, 67, 68, 69, 47, 49, 86, 87, 22, 23, 32, 91, 53, 52, 52, 51, 82, 93, 83, 83, 67, 68, 69, 95, 94, 93, 83, -1, -1, 83, 83, 83, 83, -1, 83, 83, 67, 69, 82, 47, 49, 87, 28, 28, 19, 20, 29, 32, 13, 91, 90, 53, 52, 95, 93, 83, 75, 80, 77, 82, 81, 79, 83, 83, 83, 83, -1, 95, 94, 93, 83, 83, 74, 82, 82, 46, 84, 5, 9, 38, 0, 9, 37, 29, 32, 13, 13, 31, 31, 82, 95, 94, 68, 68, 69, 73, 71, 83, 83, 83, 75, 76, 77, 81, 80, 79, 83, 67, 69, 82, 82, 54, 31, 23, 20, 37, 21, 5, 9, 40, 29, 28, 19, 32, 31, 52, 51, 82, 82, 82, 82, 95, 94, 93, 83, 83, 83, 83, 75, 71, 83, 83, 83, 75, 76, 77, 82, 55, 57, -1, 10, 9, 14, 13, 5, 10, 9, 40, 40, 29, 32, 90, 53, 52, 52, 51, 82, 82, 82, 95, 94, 93, 83, 83, 83, 120, 120, 83, 83, 83, 83, 74, 82, 82, 82, 82, 82, 45, -1, 31, 13, 31, 5, 10, 33, 40, 21, 121, 122, 13, 91, 53, 52, 51, 82, 82, 94, 82, 94, 93, 83, -1, 83, 83, 83, 83, 83, 75, 77, 82, 82, 82, 82, 45, 45, 45, 13, 121, 122, 31, 30, 40, 21, -128, 127, 122, 13, 91, 90, 53, 48, 52, 52, 51, 82, 95, 68, 68, 94, 94, 93, 83, 83, 83, 75, 76, 77, 77, 82, 60, 60, 57, 45, 123, 124, 31, 30, 40, 18, 126, 125, 127, 122, 121, 122, 122, 13, 91, 90, 53, 52, 51, 82, 82, 82, 82, 95, 93, 83, 83, 83, 83, 83, 120, -1, 82, 82, 54, 45, 121, 122, 31, 5, 9, 0, 123, 126, 125, 124, 123, 124, 124, 13, 23, 32, 45, 45, 53, 52, 52, 51, 82, 82, 95, 94, 93, 67, 68, 69, 68, 69, 82, 47, 49, 85, 123, 124, 31, 31, 12, 3, 121, 122, 123, 124, 22, 123, 124, 23, 20, 29, 28, 32, 31, 91, 90, 53, 51, 82, 82, 82, 82, 94, 94, 82, 82, 82, 47, 49, 87, 13, 4, 22, 23, 32, 5, 14, 123, 124, 22, 23, 19, 19, 28, 27, 40, 41, 40, 29, 28, 19, 32, 91, 53, 51, 82, 82, 82, 82, 47, 52, 52, 52, 49, 87, 13, 22, 22, 22, 12, 29, 28, 32, 121, 122, 23, 20, 36, 37, 43, 44, 43, 39, 40, 41, 44, 37, 29, 28, 45, 50, 47, 48, 52, 48, 49, 85, 86, 87, 13, 22, 31, 23, 19, 32, 5, 10, 10, 14, 123, 124, 5, 33, 37, 43, 44, 37, 0, 1, 10, 9, 11, 10, 11, -1, -1, 45, 49, 90, 87, 31, 13, 13, 13, 13, 4, 23, 28, 27, 43, 29, 32, 31, 31, 31, 121, 122, 22, 30, 40, 40, 11, 10, 14, 13, 13, 5, 14, -1, -1, -1, -1, 31, 23, 28, 28, 32, 31, 4, 23, 32, 23, 15, 11, 1, 1, 2, 3, 121, 122, 121, 123, 124, 23, 27, 43, 0, 14, 121, 122, 13, -1, 77, 59, 60, 59, 47, 31, 23, 27, 44, 37, 29, 28, 19, 20, 18, 27, 37, 18, 32, 22, 5, 14, 123, -128, 127, 121, 122, 30, 36, 11, 14, 22, 123, 124, 13, 61, 73, 76, 77, 47, 49, 13, 5, 2, 43, 44, 43, 44, 43, 37, 43, 44, 40, 11, 14, 22, 121, 122, 121, 126, 125, 123, 124, 12, 37, 21, 22, 22, 121, 122, 31, 50, 95, 93, 74, 46, -1, 13, 13, 5, 10, 10, 1, 2, 43, 44, 11, 10, 10, 14, 13, 121, 122, 124, 123, 124, 123, 22, 22, 15, 41, 18, 32, 22, 123, 124, 31, 53, 51, 95, 94, 55, 57, 13, 4, 13, 121, 122, 4, 5, 10, 1, 14, 13, 121, 122, 121, 122, 124, 124, 123, 124, 4]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1]}