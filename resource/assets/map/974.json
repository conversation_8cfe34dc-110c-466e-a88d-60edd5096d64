{"mW": 960, "mH": 720, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1]], "layers": [{"type": 3, "obj": [[2, "1672", 869, 576, 44, 88, 0], [2, "1228", 866, 43, 60, 75, 0], [2, "1228", 893, 56, 60, 75, 0], [2, "1228", 922, 69, 60, 75, 0], [2, "1229_1", 900, 30, 48, 39, 0], [2, "1229_1", 929, 44, 48, 39, 0], [2, "1672", 921, 84, 44, 88, 0], [2, "1228", 633, 35, 60, 75, 0], [2, "1228", 657, 45, 60, 75, 0], [2, "1228", 681, 57, 60, 75, 0], [2, "1228", 706, 67, 60, 75, 0], [2, "1228", 731, 79, 60, 75, 0], [2, "1228", 756, 91, 60, 75, 0], [2, "1663", 683, 110, 62, 59, 0], [2, "1229_1", 684, -5, 48, 39, 0], [2, "1229_1", 713, 10, 48, 39, 0], [2, "1229_1", 741, 24, 48, 39, 0], [2, "1229_1", 770, 39, 48, 39, 0], [2, "1229_1", 799, 54, 48, 39, 0], [2, "1229_1", 670, 12, 48, 39, 0], [2, "1229_1", 699, 27, 48, 39, 0], [2, "1229_1", 727, 41, 48, 39, 0], [2, "1229_1", 756, 56, 48, 39, 0], [2, "1229_1", 785, 71, 48, 39, 0], [2, "1227", 921, -5, 46, 62, 0], [2, "1229_1", 873, 26, 48, 39, 2], [2, "1229_1", 844, 39, 48, 39, 2], [2, "1229_1", 815, 54, 48, 39, 2], [2, "1227", 855, -26, 46, 62, 0], [2, "1227", 806, 2, 46, 62, 0], [2, "1227", 696, -50, 46, 62, 0], [2, "1672", 636, 34, 44, 88, 0], [2, "1672", 741, 87, 44, 88, 0], [2, "1228", 793, 638, 60, 75, 0], [2, "1228", 817, 648, 60, 75, 0], [2, "1228", 841, 660, 60, 75, 0], [2, "1228", 866, 670, 60, 75, 0], [2, "1228", 891, 682, 60, 75, 0], [2, "1228", 916, 694, 60, 75, 0], [2, "1229_1", 824, 616, 48, 39, 0], [2, "1229_1", 852, 630, 48, 39, 0], [2, "1229_1", 881, 645, 48, 39, 0], [2, "1229_1", 910, 660, 48, 39, 0], [2, "1227", 886, 614, 46, 62, 0], [2, "1672", 785, 637, 44, 88, 0], [2, "1229_1", 939, 674, 48, 39, 0], [2, "1227", 923, 627, 46, 62, 0], [2, "1663", 96, -6, 62, 59, 2], [2, "1228", 34, -58, 60, 75, 0], [2, "1228", 65, -63, 60, 75, 2], [2, "1228", 38, -52, 60, 75, 2]]}, {"type": 4, "obj": [[2, "1645", 362, -37, 54, 88, 0], [4, 6, 358, 100, 0, 4035], [2, "1645", 54, 93, 54, 88, 0], [2, "1645", 247, 170, 54, 88, 0], [4, 2, 24, 336, 0, 4035], [2, "1645", 882, 288, 54, 88, 0], [2, "1645", 226, 318, 54, 88, 0], [4, 1, 824, 499, 0, 4035], [2, "1645", 683, 430, 54, 88, 0], [2, "1645", 538, 464, 54, 88, 0], [4, 3, 428, 582, 0, 4035], [2, "1645", 53, 572, 54, 88, 0]]}, {"type": 3, "obj": [[2, "1646", 239, 2, 26, 28, 0], [2, "1671", 180, -17, 22, 74, 0], [2, "1476", 90, -1, 18, 32, 2], [2, "1476", 72, 8, 18, 32, 2], [2, "1476", 36, -1, 18, 32, 0], [2, "1476", 54, 8, 18, 32, 0], [2, "1476", 0, -19, 18, 32, 0], [2, "1476", 18, -10, 18, 32, 0], [2, "1666", 38, 79, 18, 20, 0], [2, "1664", 38, 24, 18, 64, 0], [2, "891", 97, 14, 54, 75, 0], [2, "1646", 486, 93, 26, 28, 0], [2, "1476", 634, 93, 18, 32, 0], [2, "1671", 628, 114, 22, 74, 0], [2, "1225_1", 872, 6, 48, 44, 2], [2, "1225_1", 887, 15, 48, 44, 2], [2, "347_1", 763, -31, 60, 45, 0], [2, "347_1", 724, -13, 60, 45, 0], [2, "1483", 846, 74, 22, 38, 2], [2, "1476", 687, 108, 18, 32, 0], [2, "1476", 669, 100, 18, 32, 0], [2, "1476", 651, 91, 18, 32, 0], [2, "1476", 741, 156, 18, 32, 0], [2, "1476", 723, 148, 18, 32, 0], [2, "1476", 705, 139, 18, 32, 0], [2, "1476", 723, 125, 18, 32, 0], [2, "1476", 705, 116, 18, 32, 0], [2, "1476", 741, 133, 18, 32, 0], [2, "1476", 759, 142, 18, 32, 0], [2, "1664", 948, 153, 18, 64, 2], [2, "1666", 948, 208, 18, 20, 2], [2, "1476", 777, 142, 18, 32, 2], [2, "1671", 861, 125, 22, 74, 0], [2, "1671", 774, 165, 22, 74, 0], [2, "1671", 730, 166, 22, 74, 0], [2, "1662", 635, 155, 48, 50, 0], [2, "1646", 843, 466, 26, 28, 0], [2, "1649", 930, 427, 8, 7, 0], [2, "1650", 898, 452, 4, 5, 0], [2, "1647", 890, 416, 4, 4, 0], [2, "1662", 738, 207, 48, 50, 0], [2, "1662", 738, 183, 48, 50, 0], [2, "1662", 739, 160, 48, 50, 0], [2, "1662", 636, 131, 48, 50, 0], [2, "1662", 637, 108, 48, 50, 0], [2, "1671", 667, 136, 22, 74, 0], [2, "1664", 795, 158, 18, 64, 2], [2, "1666", 795, 212, 18, 20, 2], [2, "1666", 813, 203, 18, 20, 2], [2, "1664", 813, 148, 18, 64, 2], [2, "1666", 848, 184, 18, 20, 2], [2, "1664", 848, 129, 18, 64, 2], [2, "1664", 830, 139, 18, 64, 2], [2, "1666", 830, 193, 18, 20, 2], [2, "1476", 795, 133, 18, 32, 2], [2, "1476", 831, 115, 18, 32, 2], [2, "1476", 813, 124, 18, 32, 2], [2, "1476", 849, 106, 18, 32, 2], [2, "1664", 896, 141, 18, 64, 0], [2, "1664", 914, 151, 18, 64, 0], [2, "1666", 914, 206, 18, 20, 0], [2, "1671", 930, 163, 22, 74, 0], [2, "1476", 867, 105, 18, 32, 0], [2, "1476", 885, 114, 18, 32, 0], [2, "1476", 903, 123, 18, 32, 0], [2, "1476", 921, 132, 18, 32, 0], [2, "1476", 957, 124, 18, 32, 2], [2, "1476", 939, 133, 18, 32, 2], [2, "1642", 808, 81, 44, 125, 2], [2, "910", 681, 162, 50, 68, 0], [2, "1483", 868, 63, 22, 38, 2], [2, "208", 678, 339, 78, 40, 0], [2, "206", 621, 353, 66, 40, 0], [2, "207", 735, 363, 38, 27, 0], [2, "207", 756, 377, 38, 27, 0], [2, "206", 788, 387, 66, 40, 2], [2, "208", 830, 371, 78, 40, 2], [2, "207", 895, 367, 38, 27, 0], [2, "208", 920, 347, 78, 40, 0], [2, "1646", 879, 360, 26, 28, 0], [2, "1649", 831, 404, 8, 7, 0], [2, "1650", 843, 396, 4, 5, 0], [2, "347_1", 863, 22, 60, 45, 0], [2, "347_1", 824, 40, 60, 45, 0], [2, "347_1", 789, 19, 60, 45, 0], [2, "347_1", 829, 2, 60, 45, 0], [2, "347_1", 756, 2, 60, 45, 0], [2, "347_1", 795, -16, 60, 45, 0], [2, "208", 211, 526, 78, 40, 0], [2, "206", 172, 494, 66, 40, 0], [2, "205", 170, 463, 54, 40, 0], [2, "207", 182, 437, 38, 27, 0], [2, "208", 181, 402, 78, 40, 2], [2, "205", 239, 374, 54, 40, 0], [2, "425_2", 732, 459, 30, 36, 0], [2, "426_2", 761, 494, 26, 22, 0], [2, "1650", 750, 473, 4, 5, 0], [2, "1646", 718, 462, 26, 28, 0], [2, "425_2", 242, 480, 30, 36, 0], [2, "426_2", 396, 570, 26, 22, 0], [2, "1646", 375, 555, 26, 28, 0], [2, "1649", 622, 407, 8, 7, 0], [2, "1647", 672, 415, 4, 4, 0], [2, "1646", 631, 412, 26, 28, 0], [2, "1646", 648, 429, 26, 28, 0], [2, "1646", 673, 450, 26, 28, 0], [2, "1646", 551, 461, 26, 28, 0], [2, "1646", 528, 433, 26, 28, 2], [2, "1650", 539, 464, 4, 5, 0], [2, "1650", 514, 437, 4, 5, 0], [2, "1648", 292, 397, 6, 8, 0], [2, "1654", 806, 491, 30, 35, 0], [2, "1655", 838, 504, 20, 12, 0], [2, "425_2", 501, 525, 30, 36, 0], [2, "425_2", -8, 657, 30, 36, 0], [2, "425_2", -5, 336, 30, 36, 0], [2, "205", 42, 348, 54, 40, 0], [2, "207", 38, 387, 38, 27, 0], [2, "208", 408, -3, 78, 40, 2], [2, "208", -9, 115, 78, 40, 0], [2, "205", 42, 142, 54, 40, 1], [2, "207", 66, 246, 38, 27, 0], [2, "207", 64, 327, 38, 27, 0], [2, "207", 67, 266, 38, 27, 0], [2, "206", 57, 284, 66, 40, 0], [2, "211", 64, 312, 48, 24, 0], [2, "206", 15, 420, 66, 40, 0], [2, "211", 34, 404, 48, 24, 0], [2, "209", 13, 533, 44, 30, 0], [2, "207", 15, 451, 38, 27, 0], [2, "205", 6, 472, 54, 40, 0], [2, "207", 8, 510, 38, 27, 0], [2, "152", 407, 70, 76, 40, 2], [2, "152", 478, 42, 76, 40, 2], [2, "208", 552, 19, 78, 40, 2], [2, "208", 627, -5, 78, 40, 2], [2, "152", 358, 27, 76, 40, 2], [2, "208", 307, 50, 78, 40, 2], [2, "206", 364, 110, 66, 40, 0], [2, "206", 285, 197, 66, 40, 0], [2, "205", 270, 230, 54, 40, 0], [2, "206", 364, 110, 66, 40, 0], [2, "208", 223, 232, 78, 40, 0], [2, "207", 309, 173, 38, 27, 0], [2, "209", 328, 152, 44, 30, 0], [2, "207", 356, 128, 38, 27, 0], [2, "205", 281, 78, 54, 40, 0], [2, "209", 267, 111, 44, 30, 0], [2, "205", 235, 130, 54, 40, 0], [2, "207", 233, 166, 38, 27, 0], [2, "207", 217, 187, 38, 27, 0], [2, "211", 210, 211, 48, 24, 0], [2, "1650", 880, 240, 4, 5, 0], [2, "1649", 867, 246, 8, 7, 0], [2, "1646", 893, 220, 26, 28, 0], [2, "1646", 596, 139, 26, 28, 2], [2, "425_2", 518, -1, 30, 36, 2], [2, "426_2", 307, 141, 26, 22, 0], [2, "1646", 320, 119, 26, 28, 0], [2, "1649", 284, 123, 8, 7, 0], [2, "1647", 444, 49, 4, 4, 0], [2, "1649", 451, 140, 8, 7, 0], [2, "1650", 536, 86, 4, 5, 0], [2, "1647", 506, 127, 4, 4, 0], [2, "1647", 506, 127, 4, 4, 0], [2, "1647", 598, 173, 4, 4, 0], [2, "1647", 598, 173, 4, 4, 0], [2, "1649", 438, 470, 8, 7, 0], [2, "1650", 633, 400, 4, 5, 0], [2, "1648", 765, 374, 6, 8, 0], [2, "1646", 400, 458, 26, 28, 0], [2, "1646", 206, 397, 26, 28, 2], [2, "1648", 203, 434, 6, 8, 0], [2, "1650", 216, 428, 4, 5, 0], [2, "1649", 271, 566, 8, 7, 0], [2, "211", 74, 169, 48, 24, 2], [2, "205", 66, 209, 54, 40, 0], [2, "209", 79, 188, 44, 30, 0], [2, "1650", 327, 505, 4, 5, 0], [2, "1647", 345, 493, 4, 4, 0], [2, "1647", 345, 493, 4, 4, 0], [2, "1650", 469, 622, 4, 5, 0], [2, "1647", 482, 617, 4, 4, 0], [2, "1646", 204, 501, 26, 28, 0], [2, "1646", 85, 185, 26, 28, 0], [2, "1648", 84, 234, 6, 8, 0], [2, "1649", 89, 247, 8, 7, 0], [2, "1647", 96, 237, 4, 4, 0], [2, "1649", 89, 247, 8, 7, 0], [2, "1649", 62, 368, 8, 7, 0], [2, "1648", 41, 440, 6, 8, 0], [2, "1650", 51, 457, 4, 5, 0], [2, "1650", 60, 384, 4, 5, 0], [2, "1648", 36, 563, 6, 8, 0], [2, "1646", 99, 648, 26, 28, 0], [2, "1649", 169, 687, 8, 7, 0], [2, "1647", 157, 679, 4, 4, 0], [2, "1647", 104, 678, 4, 4, 0], [2, "1647", 31, 497, 4, 4, 0], [2, "1650", 869, 532, 4, 5, 0], [2, "1647", 879, 524, 4, 4, 0], [2, "1650", 489, 503, 4, 5, 0], [2, "1648", 575, 41, 6, 8, 0], [2, "1657", 66, 53, 48, 47, 0], [2, "1657", 65, 19, 48, 47, 0], [2, "1657", 144, 22, 48, 47, 0], [2, "1657", 143, -12, 48, 47, 0], [2, "1666", 878, 186, 18, 20, 0], [2, "1664", 878, 131, 18, 64, 0], [2, "1398", 892, 129, 28, 66, 0], [2, "1666", 896, 196, 18, 20, 0], [2, "1671", 55, 34, 22, 74, 0], [2, "1666", 22, 72, 18, 20, 0], [2, "1664", 22, 17, 18, 64, 0], [2, "1666", 4, 63, 18, 20, 0], [2, "1664", 4, 8, 18, 64, 0], [2, "1666", -14, 54, 18, 20, 0], [2, "1664", -14, -1, 18, 64, 0], [2, "1398", 15, 5, 28, 66, 0], [2, "1476", 108, -9, 18, 32, 2], [2, "1650", 290, 110, 4, 5, 0], [2, "1650", 362, 56, 4, 5, 0], [2, "1650", 378, 58, 4, 5, 0], [2, "1648", 426, 24, 6, 8, 0], [2, "1647", 248, 164, 4, 4, 0], [2, "1647", 424, 93, 4, 4, 0], [2, "1650", 423, 131, 4, 5, 0], [2, "1647", 433, 116, 4, 4, 0], [2, "1647", 554, 250, 4, 4, 0], [2, "1650", 543, 239, 4, 5, 0], [2, "1650", 302, 329, 4, 5, 0], [2, "1647", 316, 306, 4, 4, 0], [2, "1649", 295, 286, 8, 7, 0], [2, "1648", 236, 271, 6, 8, 0], [2, "1650", 424, 430, 4, 5, 0], [2, "1650", 430, 331, 4, 5, 0], [2, "1647", 610, 421, 4, 4, 0], [2, "1647", 833, 318, 4, 4, 0], [2, "1650", 384, 217, 4, 5, 0], [2, "1649", 364, 400, 8, 7, 0], [2, "1649", 95, 407, 8, 7, 0], [2, "1650", 149, 518, 4, 5, 0], [2, "1648", 357, 653, 6, 8, 0], [2, "1649", 23, 93, 8, 7, 0], [2, "1650", 290, 110, 4, 5, 0], [2, "1650", 217, 49, 4, 5, 0], [2, "1648", 211, 34, 6, 8, 0], [2, "1647", 121, 132, 4, 4, 0]]}, {"type": 3, "obj": [[2, "173", 268, 379, 70, 45, 2], [2, "1659", 144, 692, 44, 81, 0], [2, "1659", 116, 690, 44, 81, 0], [2, "1659", 85, 675, 44, 81, 0], [2, "1660", 12, 574, 36, 78, 0], [2, "1660", 31, 611, 36, 78, 0], [2, "1659", 54, 647, 44, 81, 0], [2, "1658", 251, 400, 44, 99, 0], [2, "1659", 282, 435, 44, 81, 0], [2, "1660", 311, 452, 36, 78, 0], [2, "1660", 840, 417, 36, 78, 2], [2, "1660", 864, 406, 36, 78, 2], [2, "1660", 885, 392, 36, 78, 2], [2, "1659", 799, 416, 44, 81, 0], [2, "1659", 909, 379, 44, 81, 2], [2, "1659", 939, 369, 44, 81, 2], [2, "1660", 755, 394, 36, 78, 0], [2, "1659", 726, 377, 44, 81, 0], [2, "1660", 701, 369, 36, 78, 0], [2, "1659", 672, 357, 44, 81, 0], [2, "1659", 651, 369, 44, 81, 2], [2, "1658", 627, 358, 44, 99, 2], [2, "1659", 779, 410, 44, 81, 0], [2, "1660", 338, 475, 36, 78, 2], [2, "1660", 468, 462, 36, 78, 2], [2, "1658", 493, 437, 44, 99, 2], [2, "1659", 435, 477, 44, 81, 2], [2, "1659", 402, 488, 44, 81, 2], [2, "1660", 357, 490, 36, 78, 0], [2, "1660", 374, 497, 36, 78, 0], [2, "1659", 511, 465, 44, 81, 0], [2, "1660", 543, 490, 36, 78, 0], [2, "1659", 231, 420, 44, 81, 2], [2, "1660", 207, 438, 36, 78, 2], [2, "206", 527, 554, 66, 40, 0], [2, "207", 556, 529, 38, 27, 0], [2, "207", 566, 505, 38, 27, 0], [2, "208", 477, 586, 78, 40, 2], [2, "207", 556, 529, 38, 27, 0], [2, "208", 253, 555, 78, 40, 0], [2, "205", 308, 579, 54, 40, 0], [2, "207", 345, 602, 38, 27, 0], [2, "208", 371, 600, 78, 40, 0], [2, "208", 435, 599, 78, 40, 0], [2, "207", 488, 610, 38, 27, 0], [2, "208", 71, 652, 78, 40, 0], [2, "205", 55, 621, 54, 40, 0], [2, "206", 32, 589, 66, 40, 0], [2, "211", 121, 682, 48, 24, 0], [2, "152", 163, 691, 76, 40, 0], [2, "206", 922, 475, 66, 40, 0], [2, "207", 555, 476, 38, 27, 0], [2, "207", 530, 455, 38, 27, 0], [2, "206", 694, 500, 66, 40, 2], [2, "208", 747, 522, 78, 40, 0], [2, "208", 855, 500, 78, 40, 2], [2, "207", 840, 529, 38, 27, 0], [2, "207", 817, 535, 38, 27, 0], [2, "207", 674, 483, 38, 27, 0], [2, "207", 657, 461, 38, 27, 0], [2, "207", 638, 444, 38, 27, 0], [2, "207", 624, 430, 38, 27, 0], [2, "209", 608, 385, 44, 30, 0], [2, "205", 599, 405, 54, 40, 2], [2, "207", 279, 398, 38, 27, 0], [2, "207", 288, 416, 38, 27, 0], [2, "208", 340, 456, 78, 40, 0], [2, "211", 310, 436, 48, 24, 0], [2, "206", 413, 452, 66, 40, 0], [2, "207", 469, 435, 38, 27, 0], [2, "209", 499, 424, 44, 30, 0], [2, "205", 18, 554, 54, 40, 2], [2, "1658", 45, 160, 44, 99, 0], [2, "173", 663, 201, 70, 45, 0], [2, "173", 708, 216, 70, 45, 0], [2, "173", 755, 216, 70, 45, 0], [2, "173", 805, 196, 70, 45, 0], [2, "173", 858, 188, 70, 45, 0], [2, "173", 898, 207, 70, 45, 0], [2, "173", 623, 174, 70, 45, 0], [2, "173", 604, 149, 70, 45, 0], [2, "173", 839, 305, 70, 45, 0], [2, "173", 903, 317, 70, 45, 0], [2, "173", 851, 341, 70, 45, 0], [2, "173", 340, 160, 70, 45, 0], [2, "173", 306, 194, 70, 45, 2], [2, "173", 363, 128, 70, 45, 2], [2, "173", 466, 65, 70, 45, 2], [2, "173", 408, 94, 70, 45, 2], [2, "173", 533, 47, 70, 45, 0], [2, "173", 608, 28, 70, 45, 0], [2, "173", 581, 53, 70, 45, 0], [2, "173", 280, 232, 70, 45, 2], [2, "173", 243, 253, 70, 45, 0], [2, "173", 213, 282, 70, 45, 0], [2, "173", 243, 300, 70, 45, 0], [2, "173", 210, 320, 70, 45, 0], [2, "173", 224, 353, 70, 45, 0], [2, "173", 190, 374, 70, 45, 0], [2, "173", 256, 335, 70, 45, 2], [2, "173", 246, 287, 70, 45, 2], [2, "173", 193, 227, 70, 45, 0], [2, "1660", 386, 16, 36, 78, 2], [2, "1660", 415, -7, 36, 78, 2], [2, "1660", 446, -20, 36, 78, 2], [2, "1658", 475, -51, 44, 99, 2], [2, "1660", 357, 32, 36, 78, 2], [2, "1660", 328, 55, 36, 78, 2], [2, "1660", 302, 77, 36, 78, 2], [2, "1660", 272, 104, 36, 78, 2], [2, "1660", 247, 155, 36, 78, 2], [2, "173", 91, 55, 70, 45, 2], [2, "173", -33, 68, 70, 45, 2], [2, "173", -9, 94, 70, 45, 2], [2, "173", 26, 90, 70, 45, 2], [2, "173", 906, 622, 70, 45, 0], [2, "173", 741, 697, 70, 45, 0], [2, "1658", -14, 123, 44, 99, 0], [2, "1658", 16, 147, 44, 99, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, 54, 98, 99, 100, 77, 90, 91, 88, -1, -1, -1, 60, 60, 60, 60, 60, 60, 60, 60, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 99, 100, 82, 85, 91, 88, -1, -1, 60, 60, 60, 60, 60, 60, 60, 60, -1, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 99, 100, 82, 81, 85, 91, 88, -1, -1, 60, 60, 60, 60, 60, -1, -1, -1, -1, -1, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, -1, 54, 98, 99, 74, 75, 85, 84, 91, 88, -1, -1, -1, -1, 63, 63, -1, -1, -1, 69, 69, 69, -1, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 103, 54, 54, 98, 99, 100, -1, 77, 84, 91, 93, 92, -1, -1, -1, -1, -1, -1, -1, 54, 54, 67, 68, -1, -1, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 99, 100, -1, -1, 82, 85, 91, 88, -1, -1, -1, -1, -1, -1, -1, 54, 66, 67, 69, 68, -1, -1, -1, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, 66, 67, 64, -1, -1, -1, -1, 115, 114, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 84, 83, -1, -1, 60, 55, -1, -1, -1, 66, 67, 64, -1, -1, -1, -1, -1, 53, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 85, 84, 83, -1, 60, 60, 60, -1, -1, -1, 64, -1, -1, -1, 82, 75, 75, 80, 105, 104, 103, 54, 54, -1, -1, -1, -1, -1, 54, 60, 60, 60, 60, -1, -1, -1, 53, -1, -1, 89, 84, 91, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 81, 85, 78, 78, 79, 80, -1, 105, 104, 103, 54, 54, -1, -1, 98, 63, 66, 66, 66, 60, 60, -1, -1, 53, -1, -1, 89, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 85, 78, 78, 78, 78, 78, 79, 80, -1, -1, 105, 104, 103, 98, 98, 63, 63, 63, 63, 63, 63, 103, -1, -1, 53, -1, -1, 77, 84, 83, -1, -1, -1, -1, -1, -1, 82, 81, 81, 85, 78, 78, 78, 78, 78, 78, 78, 95, -1, -1, -1, -1, 105, 100, -1, -1, 82, 75, 80, -1, -1, -1, -1, -1, 53, -1, -1, 77, 84, 79, 76, -1, -1, 82, 79, 81, 85, 84, 84, 78, 78, 78, 78, 78, 78, 78, 78, 95, -1, -1, -1, -1, -1, -1, -1, 82, 85, 78, 79, 81, 76, -1, -1, 58, 61, -1, -1, 89, 78, 78, 79, 81, 81, 85, 90, 90, 84, 91, 93, 97, 78, 78, 78, 78, 78, 78, 91, 92, -1, -1, -1, -1, -1, -1, 82, 85, 78, 78, 78, 90, 79, 81, -1, 53, 66, -1, 82, 85, 78, 78, 78, 78, 78, 78, 90, 91, 87, 88, -1, 94, 97, 78, 78, 78, 78, 91, 88, -1, -1, -1, -1, -1, -1, -1, 86, 97, 78, 78, 91, 93, 84, 84, -1, 65, -1, -1, 89, 78, 78, 91, 87, 97, -1, -1, 91, 88, -1, -1, -1, -1, 86, 87, 87, 87, 87, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 93, 93, 88, -1, -1, -1, 58, 61, -1, -1, 89, 78, 91, 88, -1, 94, -1, -1, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 60, -1, -1, 89, 78, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 61, -1, -1, -1, 89, 78, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, 66, 66, -1, -1, -1, -1, 0, 1, 3, 4, 60, -1, -1, 82, 85, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 63, 66, 73, 66, -1, -1, -1, 3, 4, 6, 7, 60, -1, -1, 89, 84, 91, 88, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 73, 66, 66, 67, 6, 7, 8, -1, 60, -1, -1, 89, 84, 83, -1, -1, -1, -1, 3, 4, 5, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 69, 64, -1, -1, -1, -1, 60, -1, -1, 89, 84, 79, 80, -1, -1, -1, 6, 7, 8, 3, 4, 5, -1, -1, 0, 0, 1, 2, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, -1, -1, 86, 97, 84, 79, 80, -1, -1, -1, -1, -1, 6, 7, 0, 1, 2, 3, 3, 4, 5, 3, 4, -1, -1, -1, 82, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 60, -1, -1, -1, 86, 97, 84, 79, 80, -1, -1, -1, -1, -1, -1, 3, 4, 5, 6, 6, 7, 8, -1, -1, -1, -1, 82, 85, 84, 79, 80, -1, -1, -1, -1, -1, 82, 81, 81, 85, 60, -1, -1, -1, -1, 86, 97, 84, 79, 75, 80, -1, -1, -1, -1, 6, 7, 8, 6, 7, 8, -1, -1, -1, -1, -1, 89, 84, 84, 84, 79, 75, 76, -1, -1, 82, 85, 84, 84, 84, 73, -1, -1, -1, -1, -1, 89, 84, 84, 84, 79, 81, 81, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 85, 78, 84, 84, 84, 84, 79, 76, 81, 85, 84, -1, 84, 84, 62, 73, -1, -1, -1, -1, 86, 87, 87, 97, 84, 84, 84, 79, 75, 75, 75, 76, -1, -1, -1, -1, -1, -1, 82, 85, 84, 84, 91, 87, 87, 97, 84, 84, 84, -1, -1, 84, 84, 84, -1, 62, 73, -1, -1, -1, -1, -1, -1, 94, 93, 93, 97, 84, 84, 84, 84, 79, 75, 75, 75, 76, -1, 82, 85, 84, 84, 91, 88, -1, -1, 94, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 93, 97, 84, 84, 84, 84, 84, 79, 81, 85, 84, 84, 91, 88, -1, -1, -1, -1, 94, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 11, 9, 9, 10, 11, 9, 10, 9, 1, 2, 3, 4, 0, 1, 3, 4, 5, -1, -1, 4, 5, 2, 10, 9, 10, 11, 9, 10, 9, 10, 11, 12, 13, 14, 12, 13, 14, 12, 13, 14, 14, 12, 12, 13, 14, 12, 0, 1, 4, 5, 6, 7, 3, 9, 10, 11, 9, 10, 11, 7, 8, 12, 13, 12, 13, 14, 12, 13, 12, 13, 14, 15, 9, 10, 11, 11, 17, 15, 16, 17, 17, 15, 15, 16, 0, 1, 2, 4, 3, 9, 10, 11, 11, 12, 13, 14, 12, 13, 14, 11, 9, 10, 9, 15, 16, 17, 15, 16, 15, 16, 17, 9, 10, 11, 9, 10, 9, 10, 11, 11, 16, 9, 3, 4, 3, 4, 5, 9, 10, 11, 11, 14, 14, 15, 16, 17, 15, 16, 17, 14, 12, 13, 12, 9, 10, 11, 9, 10, 12, 13, 9, 10, 11, 14, 12, 13, 12, 13, 9, 10, 11, 4, 6, 7, 1, 9, 10, 11, 11, 14, 14, 17, 17, 14, 15, 16, 17, 15, 16, 17, 15, 16, 15, 12, 13, 14, 12, 13, 8, 16, 12, 13, 9, 10, 11, 16, 15, 16, 12, 13, 0, 7, 7, 3, 9, 10, 11, 14, 14, 17, 17, 14, 16, 17, 11, 9, 10, 11, 13, 12, 13, 9, 9, 15, 16, 17, 15, 16, 7, 8, 10, 9, 10, 11, 14, 9, 10, 11, 15, 3, 4, 7, 5, 9, 10, 11, 14, 17, 17, 15, 16, 17, 10, 11, 14, 12, 13, 14, 16, 9, 10, 11, 12, 9, 10, 11, 9, 10, 0, 1, 2, 12, 13, 14, 17, 12, 9, 10, 11, 6, 7, 5, 9, 12, 13, 14, 17, 16, 17, 13, 14, 12, 13, 14, 17, 15, 16, 17, 17, 12, 13, 14, 15, 12, 13, 14, 12, 13, 3, 4, 5, 15, 16, 17, 17, 15, 12, 13, 4, 2, 5, 9, 12, 15, 16, 17, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 10, 11, 15, 16, 17, 15, 15, 16, 17, 15, 16, 6, 7, 8, 15, 16, 17, 9, 9, 15, 16, 7, 5, 9, 12, 15, 16, 17, 12, 13, 14, 9, 10, 11, 10, 11, 14, 9, 10, 11, 9, 10, 11, 11, 9, 9, 9, 10, 11, 9, 10, 0, 1, 2, 13, 14, 14, 12, 12, 13, 11, 7, 8, 12, 15, 15, 16, 17, 15, 16, 17, 12, 13, 14, 11, 14, 10, 12, 13, 14, 12, 13, 14, 14, 12, 12, 12, 13, 14, 12, 13, 0, 1, 2, 10, 11, 9, 10, 9, 10, 14, 11, 9, 15, 16, 17, 14, 12, 13, 14, 16, 15, 16, 17, 14, 17, 13, 15, 16, 17, 15, 16, 17, 17, 9, 10, 15, 16, 17, 15, 16, 0, 1, 2, 13, 14, 12, 9, 12, 13, 14, 14, 12, 15, 16, 17, 11, 10, 11, 10, 9, 10, 9, 10, 11, 9, 10, 11, 12, 13, 9, 10, 11, 11, 12, 13, 9, 10, 11, 9, 10, 3, 4, 5, 16, 17, 15, 12, 15, 16, 17, 14, 15, 16, 17, 14, 14, 9, 10, 11, 12, 9, 10, 11, 14, 12, 13, 14, 15, 16, 12, 13, 14, 14, 15, 16, 12, 13, 14, 12, 13, 6, 7, 8, 16, 17, 16, 15, 16, 17, 12, 15, 16, 17, 16, 17, 9, 10, 11, 10, 9, 12, 13, 14, 17, 15, 16, 17, 11, 9, 15, 16, 17, 17, 15, 9, 15, 16, 17, 15, 16, 7, 8, 17, 9, 10, 9, 10, 11, 13, 15, 12, 13, 14, 17, 12, 12, 13, 9, 10, 11, 15, 16, 17, 10, 12, 13, 14, 14, 9, 10, 11, 9, 10, 11, 12, 9, 10, 11, 9, 10, 1, 2, 9, 10, 11, 12, 13, 14, 16, 17, 15, 16, 17, 9, 10, 15, 16, 12, 13, 14, 12, 13, 14, 13, 15, 16, 9, 1, 2, 0, 1, 2, 11, 14, 15, 12, 13, 14, -1, -1, 4, 5, 12, 13, 9, 15, 16, 17, 12, 13, 14, 14, 10, 12, 13, 9, 10, 11, 16, 17, 15, 16, 17, 9, 10, 11, 9, -1, -1, -1, -1, 0, 1, 2, 11, 15, 16, -1, -1, -1, 7, 8, 15, 16, 12, 13, 14, 10, 15, 16, 17, 17, 9, 10, 11, 12, 9, 10, 11, 15, 16, 17, 9, 10, 13, 14, 12, 5, 0, 1, 2, 0, 1, 2, 1, -1, -1, -1, -1, -1, 2, 17, 16, 17, 15, 16, 17, 13, 14, 0, 1, 2, 11, 9, 10, 11, 12, 13, 14, 9, 10, 2, 12, 9, 10, 9, 15, 11, 3, 4, 5, 3, 0, 1, 2, -1, -1, -1, -1, 4, 5, 10, 11, 10, 11, 10, 15, 16, 17, 3, -1, -1, -1, 12, 13, 14, 15, 16, 17, 12, 0, 5, -1, 9, 10, 10, 11, 9, 11, 7, 8, 6, 3, 0, 1, 2, 6, 7, 8, 9, 8, 9, 10, 11, 9, 10, 9, 10, 11, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 13, 9, 12, 13, 14, 11, 5, 6, 3, 4, 5, 1, 2, 10, 12, 1, 12, 13, 14, 12, 13, 12, 13, 14, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 0, 1, 15, 16, 12, 15, 16, 17, 17, 12, 4, 6, 7, 8, 11, 9, 10, 15, 4, 15, 16, 17, 15, 16, 15, 16, 17, 9, 10, 1, 6, -1, -1, -1, -1, -1, -1, -1, -1, 65, 1, 8, 12, 13, 15, 16, 17, 17, 17, 15, 9, 10, 11, 13, 14, 12, 13, 14, 7, 8, 10, 11, 9, 10, 9, 9, 10, 11, 10, 11, 9, 1, 0, -1, -1, -1, -1, -1, -1, 65, 4, 12, 15, 16, 17, 17, 12, 13, 14, 15, 16, 17, 15, 16, 17, 15, 16, 17, 0, 1, 2, 14, 9, 10, 11, 12, 13, 14, 11, 14, 12, 13, 14, -1, -1, -1, 8, 6, 7, 8, 16, 15, 16, 17, 16, 17, 15, 9, 9, 10, 11, 9, 10, 11, 9, 10, 11, 11, 3, 4, 5, 2, 12, 9, 10, 11, 16, 17, 14, 17, 15, 16, 17, 15, 16, 17, 15, 12, 13, 14, 13, 14, 9, 10, 11, 17, 9, 10, 11, 13, 14, 12, 13, 14, 12, 13, 14, 14, 0, 1, 2, 5, 15, 12, 13, 14, 15, 9, 10, 11, 15, 16, 17, 15, 16, 17, 15, 15, 16, 17, 16, 9, 10, 11, 9, 10, 11, 11, 14, 16, 17, 15, 16, 17, 15, 16, 17, 17, 3, 4, 5, 1, 2, 15, 16, 17, 12, 12, 13, 14, 13, 9, 10, 11, 14, 9, 10, 11, 9, 10, 11, 12, 13, 14, 12, 13, 14, 10, 11, 10, 11, 9, 9, 10, 11, 9, 10, 11, 6, 7, 8, 4, 5, 0, 1, 2, 15, 15, 16, 17, 16, 12, 13, 14, 17, 12, 13, 14, 12, 13, 14, 15, 16, 17, 15, 16, 17, 13, 14, 13, 14, 12, 12, 13, 14, 12, 13, 14]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1]}