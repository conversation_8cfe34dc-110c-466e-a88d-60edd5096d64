{"mW": 1200, "mH": 720, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2], ["588", 0, 1, 1], ["203_13", 0, 2, 1], ["203_14", 0, 2, 1], ["203_15", 0, 2, 1], ["203_16", 0, 2, 1], ["311", 0, 3, 2], ["311", 2, 3, 2], ["311", 1, 3, 2], ["311", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "1320", 1148, 154, 76, 64, 0], [2, "1104", -41, 536, 138, 132, 0], [2, "598", 1045, 451, 18, 22, 2], [2, "268_3", 1059, 321, 106, 82, 0], [2, "1104", 844, 545, 138, 132, 0], [2, "1496", 956, 421, 112, 43, 2], [2, "87_2", 688, 641, 72, 57, 0], [2, "268_3", 774, 635, 106, 82, 0], [2, "268_3", 743, 673, 106, 82, 0], [2, "268_3", 930, 655, 106, 82, 0], [2, "268_3", 846, 629, 106, 82, 0], [2, "87_2", 812, 658, 72, 57, 0], [2, "87_2", 883, 688, 72, 57, 0], [2, "87_2", 837, 688, 72, 57, 0], [2, "1499", 984, 420, 76, 60, 2], [2, "598", 1047, 436, 18, 22, 0], [2, "180", 1099, -26, 104, 84, 0], [2, "87_2", 1116, 376, 72, 57, 0], [2, "12_2", 968, 82, 26, 28, 0], [2, "12", 954, 86, 26, 28, 0], [2, "27", 977, 114, 6, 8, 0], [2, "594", 213, 226, 52, 46, 2], [2, "87_2", 19, 614, 72, 57, 0], [2, "268_3", -50, 629, 106, 82, 0], [2, "268_3", -4, 662, 106, 82, 0], [2, "268_3", 254, 681, 106, 82, 2], [2, "87_2", 315, 665, 72, 57, 0], [2, "598", 217, 259, 18, 22, 2], [2, "1496", 55, 34, 112, 43, 2], [2, "1499", 86, 34, 76, 60, 2], [2, "1502", 960, 483, 18, 25, 2], [2, "1502", 1052, 466, 18, 25, 2], [2, "165", 1139, 148, 42, 37, 0], [2, "166", 1170, 171, 30, 35, 0]]}, {"type": 4, "obj": [[2, "1497", 330, -90, 116, 150, 0], [4, 3, 331, 96, 0, 4024], [2, "165_1", 12, 65, 42, 37, 2], [2, "829", 154, 70, 42, 54, 0], [2, "31", 132, 66, 14, 63, 0], [2, "1492", 130, 3, 38, 142, 0], [2, "1320", 936, 92, 76, 64, 0], [2, "1497", 556, 24, 116, 150, 0], [2, "31", 39, 116, 14, 63, 0], [2, "1492", 54, 39, 38, 142, 2], [2, "592", 824, 220, 30, 65, 0], [2, "1320", 205, 239, 76, 64, 0], [2, "1497", 998, 226, 116, 150, 2], [2, "31", 575, 382, 14, 63, 0], [2, "711", 584, 426, 92, 34, 0], [2, "1497", 1073, 322, 116, 150, 0], [2, "31", 527, 429, 14, 63, 0], [2, "31", 667, 429, 14, 63, 0], [2, "1497", 1093, 346, 116, 150, 0], [2, "711", 541, 468, 92, 34, 0], [2, "127", 1078, 435, 46, 82, 2], [2, "127", 1032, 442, 46, 82, 0], [2, "31", 627, 472, 14, 63, 0], [2, "73", 1090, 470, 46, 72, 0], [2, "1492", 1031, 401, 38, 142, 0], [4, 2, 87, 544, 0, 4008], [2, "1492", 955, 421, 38, 142, 2], [2, "127", 913, 499, 46, 82, 2], [2, "73", 927, 535, 46, 72, 0], [2, "127", 880, 533, 46, 82, 2], [2, "127", 880, 533, 46, 82, 2], [2, "127", 842, 564, 46, 82, 2], [2, "127", 814, 595, 46, 82, 2], [2, "1497", 945, 540, 116, 150, 0], [2, "1460", 923, 636, 84, 59, 0], [2, "1497", 262, 563, 116, 150, 0]]}, {"type": 3, "obj": [[2, "385", 1120, 447, 72, 48, 0], [2, "385", 675, 682, 72, 48, 2], [2, "385", 640, 665, 72, 48, 0], [2, "385", 381, 682, 72, 48, 0], [2, "1311", 296, 262, 44, 81, 0], [2, "313_2", 512, 437, 70, 44, 2], [2, "313_2", 393, 209, 70, 44, 2], [2, "313_2", 434, 200, 70, 44, 2], [2, "313_2", 345, 151, 70, 44, 2], [2, "1311", 489, 118, 44, 81, 0], [2, "1144", 391, 157, 114, 70, 0], [2, "313_2", 95, 306, 70, 44, 0], [2, "313_2", 132, 323, 70, 44, 2], [2, "313_2", 99, 338, 70, 44, 2], [2, "313_2", 64, 145, 70, 44, 2], [2, "206", 296, 416, 66, 40, 0], [2, "1311", 553, 53, 44, 81, 0], [2, "208_3", 448, 91, 78, 40, 2], [2, "1144", 326, 186, 114, 70, 0], [2, "1311", 119, 366, 44, 81, 0], [2, "1312", 95, 388, 36, 78, 0], [2, "181", -2, 396, 104, 100, 2], [2, "1308_2", 847, 252, 22, 37, 0], [2, "1308_2", 831, 240, 22, 37, 0], [2, "1310_2", 770, 229, 18, 29, 0], [2, "1308_2", 755, 230, 22, 37, 0], [2, "1312", 247, 303, 36, 78, 0], [2, "1311", 216, 328, 44, 81, 0], [2, "1312", 191, 350, 36, 78, 0], [2, "1312", 156, -16, 36, 78, 0], [2, "1311", 179, -29, 44, 81, 0], [2, "313_2", 1069, 106, 70, 44, 0], [2, "1311", 1066, -14, 44, 81, 2], [2, "364_3", 866, 150, 44, 64, 2], [2, "954", 755, 188, 24, 25, 0], [2, "313", 631, 490, 70, 44, 2], [2, "313_2", 700, 216, 70, 44, 0], [2, "1312", 804, 159, 36, 78, 2], [2, "1311", 826, 172, 44, 81, 2], [2, "212", 848, 181, 44, 99, 0], [2, "1312", 722, 271, 36, 78, 0], [2, "1308_2", 780, 226, 22, 37, 0], [2, "1308_2", 796, 238, 22, 37, 0], [2, "212", 695, 318, 44, 99, 2], [2, "1311", 614, 381, 44, 81, 0], [2, "1312", 637, 376, 36, 78, 2], [2, "1311", 667, 370, 44, 81, 0], [2, "1312", 567, 468, 36, 78, 0], [2, "1312", 543, 493, 36, 78, 0], [2, "1312", 624, 553, 36, 78, 2], [2, "212", 516, 520, 44, 99, 2], [2, "207_2", 582, 422, 38, 27, 2], [2, "206", 505, 492, 66, 40, 0], [2, "207_2", 623, 513, 38, 27, 2], [2, "1311", 663, 614, 44, 81, 2], [2, "1312", 644, 595, 36, 78, 2], [2, "181_3", 1100, 32, 104, 100, 0], [2, "207_2", 679, 342, 38, 27, 2], [2, "207_2", 642, 561, 38, 27, 0], [2, "1144", 119, 355, 114, 70, 0], [2, "208_3", 669, 586, 78, 40, 0], [2, "205_3", 649, 569, 54, 40, 0], [2, "1151", 630, 295, 38, 33, 2], [2, "1308_2", 816, 221, 22, 37, 0], [2, "1302_3", 601, 325, 40, 29, 0], [2, "1305_2", 648, 328, 20, 14, 0], [2, "1311", 496, 590, 44, 81, 0], [2, "1311", 467, 610, 44, 81, 0], [2, "1312", 437, 616, 36, 78, 2], [2, "1311", 414, 621, 44, 81, 0], [2, "1312", 388, 625, 36, 78, 0], [2, "212", 361, 630, 44, 99, 2], [2, "208_3", 458, 562, 78, 40, 2], [2, "206", 629, 527, 66, 40, 0], [2, "1311", 694, 622, 44, 81, 2], [2, "597", 636, 482, 34, 26, 2], [2, "597", 563, 453, 34, 26, 2], [2, "597", 573, 460, 34, 26, 2], [2, "597", 585, 465, 34, 26, 2], [2, "597", 597, 469, 34, 26, 2], [2, "597", 611, 473, 34, 26, 2], [2, "597", 625, 477, 34, 26, 2], [2, "597", 618, 497, 34, 26, 2], [2, "597", 540, 469, 34, 26, 2], [2, "597", 550, 476, 34, 26, 2], [2, "597", 562, 481, 34, 26, 2], [2, "597", 574, 485, 34, 26, 2], [2, "597", 588, 489, 34, 26, 2], [2, "597", 603, 493, 34, 26, 2], [2, "597", 540, 481, 34, 26, 0], [2, "597", 569, 493, 34, 26, 0], [2, "597", 600, 502, 34, 26, 0], [2, "597", 587, 444, 34, 26, 0], [2, "597", 612, 459, 34, 26, 0], [2, "597", 643, 471, 34, 26, 0], [2, "207_2", 512, 535, 38, 27, 2], [2, "313_2", 683, 501, 70, 44, 2], [2, "313_2", 780, 489, 70, 44, 2], [2, "313_2", 976, 522, 70, 44, 2], [2, "313_2", 1041, 568, 70, 44, 2], [2, "313_2", 1053, 601, 70, 44, 2], [2, "313_2", 1141, 624, 70, 44, 2], [2, "313_2", 1136, 652, 70, 44, 2], [2, "313_2", 1096, 614, 70, 44, 2], [2, "313_2", 873, 497, 70, 44, 2], [2, "313_2", 928, 495, 70, 44, 2], [2, "208_3", 323, 628, 78, 40, 2], [2, "1311", 713, 639, 44, 81, 2], [2, "1311", 744, 647, 44, 81, 2], [2, "1312", 758, 301, 36, 78, 2], [2, "1311", 779, 320, 44, 81, 2], [2, "1311", 810, 336, 44, 81, 2], [2, "208_3", 554, 387, 78, 40, 2], [2, "208_3", 611, 350, 78, 40, 2], [2, "208_3", 666, 440, 78, 40, 2], [2, "207_2", 665, 458, 38, 27, 2], [2, "208_3", 744, 420, 78, 40, 2], [2, "207_2", 816, 408, 38, 27, 2], [2, "207_2", 691, 316, 38, 27, 2], [2, "206", 681, 288, 66, 40, 2], [2, "362_1", 775, 208, 64, 42, 0], [2, "1505", 785, 24, 16, 16, 0], [2, "208_3", 689, 245, 78, 40, 2], [2, "208_3", 775, 267, 78, 40, 2], [2, "207_2", 845, 265, 38, 27, 2], [2, "208_3", 840, 151, 78, 40, 0], [2, "207_2", 813, 138, 38, 27, 2], [2, "313_2", 859, 241, 70, 44, 2], [2, "313_2", 878, 210, 70, 44, 2], [2, "313_2", 877, 177, 70, 44, 2], [2, "207_2", 879, 200, 38, 27, 2], [2, "208_3", 894, 48, 78, 40, 2], [2, "152_3", 840, 66, 76, 40, 2], [2, "205_3", 807, 103, 54, 40, 0], [2, "954", 843, 229, 24, 25, 0], [2, "313_2", 699, 180, 70, 44, 2], [2, "313_2", 666, 205, 70, 44, 0], [2, "313", 1012, 545, 70, 44, 0], [2, "313", 1087, 641, 70, 44, 0], [2, "164", 598, 565, 60, 30, 0], [2, "165", 637, 585, 42, 37, 0], [2, "166_2", 698, 603, 30, 35, 0], [2, "95", 648, 566, 22, 33, 0], [2, "1507_2", 612, 20, 0, 1, 0], [2, "1507_4", 614, 47, 0, 1, 0], [2, "1506", 675, 36, 2, 3, 0], [2, "1506", 654, 45, 2, 3, 0], [2, "1506", 780, 169, 2, 3, 0], [2, "1506", 842, 54, 2, 3, 0], [2, "1506", 833, 66, 2, 3, 0], [2, "1506", 705, 83, 2, 3, 0], [2, "1506", 705, 83, 2, 3, 0], [2, "1507_1", 878, 21, 0, 1, 0], [2, "1507_1", 888, 37, 0, 1, 0], [2, "1507_4", 643, 43, 0, 1, 0], [2, "1507_4", 560, 31, 0, 1, 0], [2, "1506", 589, 36, 2, 3, 0], [2, "1507_4", 601, 24, 0, 1, 0], [2, "1507_4", 683, 89, 0, 1, 0], [2, "1507_4", 691, 62, 0, 1, 0], [2, "152_3", 687, 146, 76, 40, 0], [2, "208_3", 626, 108, 78, 40, 0], [2, "205_3", 597, 86, 54, 40, 0], [2, "1301_2", 820, 113, 24, 49, 0], [2, "1507_4", 624, 61, 0, 1, 0], [2, "1507_4", 653, 57, 0, 1, 0], [2, "1144", 157, 398, 114, 70, 0], [2, "1144", 178, 444, 114, 70, 0], [2, "1144", 168, 479, 114, 70, 0], [2, "1144", -34, 25, 114, 70, 0], [2, "1144", 10, 57, 114, 70, 0], [2, "1144", 34, 101, 114, 70, 0], [2, "96", 515, 493, 18, 37, 2], [2, "165", 521, 534, 42, 37, 2], [2, "1311", 1041, -16, 44, 81, 0], [2, "212", 1001, -27, 44, 99, 0], [2, "1325", 934, 32, 80, 36, 0], [2, "1312", 1130, 366, 36, 78, 2], [2, "1311", 1160, 360, 44, 81, 0], [2, "206", 1073, 390, 66, 40, 0], [2, "207_2", 1101, 417, 38, 27, 2], [2, "208_3", 1124, 477, 78, 40, 0], [2, "207_2", 1116, 438, 38, 27, 2], [2, "207_2", 1132, 460, 38, 27, 2], [2, "1502", 1068, -1, 18, 25, 0], [2, "14_6", 995, 90, 32, 30, 0], [2, "14_6", 920, 98, 32, 30, 0], [2, "11_3", 866, 157, 32, 29, 0], [2, "313_2", 1053, 148, 70, 44, 0], [2, "313_2", 1092, 134, 70, 44, 0], [2, "1144", -51, -20, 114, 70, 0], [2, "1311", 12, 105, 44, 81, 0], [2, "1312", -11, 118, 36, 78, 0], [2, "208_3", -34, 80, 78, 40, 2], [2, "1311", 136, 20, 44, 81, 0], [2, "1312", 113, 33, 36, 78, 0], [2, "208_3", 398, -3, 78, 40, 0], [2, "1327", 438, 1, 70, 44, 0], [2, "1322", 486, 19, 64, 38, 0], [2, "207_2", 482, 37, 38, 27, 2], [2, "1327", 520, 52, 70, 44, 0], [2, "166_2", 631, 70, 30, 35, 2], [2, "165_1", 705, 126, 42, 37, 2], [2, "212", 206, -62, 44, 99, 2], [2, "1311", 271, 281, 44, 81, 0], [2, "362_1", 824, 218, 64, 42, 2], [2, "362_1", 738, 198, 64, 42, 0], [2, "212", 312, 199, 44, 99, 2], [2, "208_3", 167, 305, 78, 40, 2], [2, "206", 380, 328, 66, 40, 2], [2, "207_2", 183, 332, 38, 27, 2], [2, "207_2", 253, 267, 38, 27, 0], [2, "208_3", 245, 230, 78, 40, 2], [2, "414", 44, 395, 74, 164, 2], [2, "180", 16, 344, 104, 84, 2], [2, "208_3", 49, 575, 78, 40, 2], [2, "152_3", 114, 549, 76, 40, 2], [2, "208_3", -29, 566, 78, 40, 0], [2, "364_3", -10, 440, 44, 64, 0], [2, "366_3", 836, 325, 32, 48, 2], [2, "1311", 340, 185, 44, 81, 0], [2, "152_3", 287, 199, 76, 40, 2], [2, "207_2", 337, 173, 38, 27, 2], [2, "1311", 271, 281, 44, 81, 0], [2, "208_3", 333, 357, 78, 40, 2], [2, "207_2", 409, 273, 38, 27, 2], [2, "207_2", 406, 314, 38, 27, 0], [2, "206", 395, 278, 66, 40, 0], [2, "364_3", 386, 296, 44, 64, 0], [2, "411_3", 279, 440, 44, 40, 0], [2, "366_3", 323, 373, 32, 48, 0], [2, "364_3", 836, 354, 44, 64, 0], [2, "96", 721, 415, 18, 37, 0], [2, "166_2", 813, 387, 30, 35, 0], [2, "166_2", 769, 290, 30, 35, 0], [2, "165_1", 798, 311, 42, 37, 0], [2, "96", 793, 254, 18, 37, 0], [2, "364_3", 1099, 350, 44, 64, 0], [2, "31", 1046, 0, 14, 63, 0], [2, "598", 656, 314, 18, 22, 0], [2, "411_3", 279, 241, 44, 40, 0], [2, "1311", 71, -19, 44, 81, 2], [2, "1311", 44, -33, 44, 81, 2], [2, "1312", 111, -29, 36, 78, 0], [2, "212", 135, -59, 44, 99, 2], [2, "1502", 13, 129, 18, 25, 2], [2, "1502", 184, 7, 18, 25, 2], [2, "208_3", 355, 240, 78, 40, 2], [2, "411_3", 387, 347, 44, 40, 0], [2, "366_3", 396, 243, 32, 48, 0], [2, "1144", 171, 510, 114, 70, 0], [2, "212", 443, 597, 44, 99, 2], [2, "205_3", 444, 583, 54, 40, 2], [2, "164_1", 470, 587, 60, 30, 2], [2, "165_1", 409, 604, 42, 37, 2], [2, "1495", 399, 2, 146, 87, 2], [2, "411_3", 423, 45, 44, 40, 2], [2, "366_3", 384, 0, 32, 48, 0], [2, "220", 477, 74, 40, 29, 0], [2, "220", 457, 66, 40, 29, 2], [2, "220", 423, 273, 40, 29, 0], [2, "220", 237, -8, 40, 29, 2], [2, "220", 264, -1, 40, 29, 2], [2, "220", 286, -10, 40, 29, 2], [2, "220", 325, 20, 40, 29, 2], [2, "597", 328, 2, 34, 26, 0], [2, "598", 301, 6, 18, 22, 0], [2, "598", 279, 16, 22, 19, 4], [2, "11_3", 238, 10, 32, 29, 0], [2, "220", 237, 29, 40, 29, 2], [2, "220", 184, 304, 40, 29, 2], [2, "220", 359, 374, 40, 29, 0], [2, "220", 342, 399, 40, 29, 0], [2, "220", 666, 325, 40, 29, 0], [2, "220", 593, 299, 40, 29, 2], [2, "1312", 520, 93, 36, 78, 0], [2, "1311", 544, 71, 44, 81, 0], [2, "366_3", 465, 115, 32, 48, 2], [2, "220", 581, 104, 40, 29, 0], [2, "1302_3", 256, 163, 40, 29, 0], [2, "220", 18, 224, 40, 29, 0], [2, "364_3", -8, 220, 44, 64, 0], [2, "166_2", 316, 209, 30, 35, 2], [2, "164_1", 291, 259, 60, 30, 2], [2, "212", -6, 351, 44, 99, 2], [2, "208_3", -7, 317, 78, 40, 2], [2, "594", 390, 24, 52, 46, 0], [2, "955", 407, 70, 20, 18, 0], [2, "166_2", 199, 319, 30, 35, 2], [2, "313_2", 106, 156, 70, 44, 2], [2, "1302_3", 879, 370, 40, 29, 0], [2, "220", 858, 392, 40, 29, 0], [2, "598", 418, 578, 18, 22, 0], [2, "1302_3", 381, 586, 40, 29, 2], [2, "220", 464, 560, 40, 29, 2], [2, "220", 64, 598, 40, 29, 0], [2, "220", 96, 616, 40, 29, 0], [2, "220", 358, 241, 40, 29, 0], [2, "220", 538, 52, 40, 29, 0], [2, "220", 882, 174, 40, 29, 2], [2, "593", 871, 178, 28, 48, 2], [2, "220", 706, 590, 40, 29, 0], [2, "220", 796, 276, 40, 29, 0], [2, "220", 1121, 488, 40, 29, 0], [2, "220", 1120, 448, 40, 29, 2], [2, "220", 902, 599, 40, 29, 0], [2, "220", 273, 515, 40, 29, 0], [2, "220", 343, 630, 40, 29, 0], [2, "313_2", 88, 274, 70, 44, 2], [2, "313_2", 80, 219, 70, 44, 2], [2, "14_6", 212, 21, 32, 30, 0], [2, "411_3", 144, 525, 44, 40, 2], [2, "220", 589, 131, 40, 29, 0], [2, "597", 624, 128, 34, 26, 0], [2, "1506", 773, 273, 2, 3, 0], [2, "1507_2", 612, 20, 0, 1, 0], [2, "1507_4", 601, 24, 0, 1, 0], [2, "1507_4", 683, 89, 0, 1, 0], [2, "1507_4", 782, 264, 0, 1, 0], [2, "1507_4", 785, 279, 0, 1, 0], [2, "1506", 930, 19, 2, 3, 0], [2, "1507_4", 810, 86, 0, 1, 0], [2, "1507_4", 820, 74, 0, 1, 0], [2, "1507_4", 797, 120, 0, 1, 0], [2, "1507_4", 790, 179, 0, 1, 0], [2, "1507_4", 802, 151, 0, 1, 0], [2, "1506", 739, 11, 2, 3, 0], [2, "1506", 749, 398, 2, 3, 0], [2, "1507_4", 761, 405, 0, 1, 0], [2, "1507_4", 761, 405, 0, 1, 0], [2, "1507_4", 762, 381, 0, 1, 0], [2, "1507_4", 752, 412, 0, 1, 0], [2, "1507_4", 716, 425, 0, 1, 0], [2, "1506", 577, 629, 2, 3, 0], [2, "1507_4", 661, 465, 0, 1, 0], [2, "1507_4", 647, 465, 0, 1, 0], [2, "1507_4", 609, 601, 0, 1, 0], [2, "1507_4", 603, 619, 0, 1, 0], [2, "1507_4", 630, 647, 0, 1, 0], [2, "1507_4", 646, 679, 0, 1, 0], [2, "1506", 607, 686, 2, 3, 0], [2, "1506", 616, 541, 2, 3, 0], [2, "1507_4", 585, 610, 0, 1, 0], [2, "1507_4", 565, 575, 0, 1, 0], [2, "1507_4", 570, 565, 0, 1, 0], [2, "1507_4", 608, 528, 0, 1, 0], [2, "1506", 511, 703, 2, 3, 0], [2, "1507_4", 543, 670, 0, 1, 0], [2, "1507_4", 529, 683, 0, 1, 0], [2, "1507_4", 518, 673, 0, 1, 0], [2, "1506", 1184, 453, 2, 3, 0], [2, "1507_4", 773, 108, 0, 1, 0], [2, "1507_4", 780, 93, 0, 1, 0], [2, "1507_4", 727, 112, 0, 1, 0], [2, "1507_4", 745, 55, 0, 1, 0], [2, "1507_2", 647, 28, 0, 1, 0], [2, "1507_2", 647, 28, 0, 1, 0], [2, "1507_1", 863, 53, 0, 1, 0], [2, "1507_1", 809, 63, 0, 1, 0], [2, "1507_1", 833, 18, 0, 1, 0], [2, "1378", 212, -18, 28, 51, 0], [2, "313_2", 449, 227, 70, 44, 2], [2, "313_2", 498, 216, 70, 44, 2], [2, "313_2", 542, 245, 70, 44, 2], [2, "313_2", 456, 441, 70, 44, 2], [2, "364_3", 1143, 310, 44, 64, 0], [2, "166_2", 1172, 352, 30, 35, 2], [2, "96", 793, 254, 18, 37, 0], [2, "1312", 1171, 213, 36, 78, 2], [2, "212", 1156, 178, 44, 99, 0], [2, "208_3", 350, 268, 78, 40, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 60, 60, 63, 73, 63, 60, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 60, 60, 67, -1, 65, 60, 60, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 67, 63, 64, -1, -1, 62, 63, 73, 66, 66, 66, -1, -1, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 73, 54, 67, -1, 65, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 67, 64, -1, -1, -1, -1, -1, -1, 70, 73, 66, 66, 66, 66, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, 64, -1, 65, 72, -1, -1, -1, -1, -1, -1, -1, -1, 60, 60, 71, -1, 82, 81, 81, 81, 76, -1, -1, 70, 69, 69, 73, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 56, -1, -1, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 64, -1, 77, 90, 78, 78, 79, 76, -1, -1, -1, -1, 65, -1, -1, -1, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 54, 54, 54, 71, -1, -1, -1, -1, 60, 60, 60, -1, -1, -1, -1, 67, 69, 68, 74, 81, 85, 91, 93, 93, 97, 79, 75, 81, 75, 75, 76, -1, -1, 54, 54, 54, 54, 67, 63, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 63, 63, 63, 64, -1, -1, -1, -1, 62, 63, 60, 66, 67, 69, 69, 97, 84, -1, 87, 87, 87, 88, -1, -1, 86, 87, -1, -1, 90, 90, -1, -1, 54, 54, 67, 63, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 66, 55, 52, -1, 77, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 63, 63, 64, -1, 74, 81, 81, 81, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 66, 66, 71, -1, 77, 84, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 85, 91, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 79, 81, 80, -1, -1, -1, -1, 58, 57, 57, 61, -1, 66, 71, 77, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, -1, -1, -1, -1, 85, 78, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, -1, 96, 78, 78, 79, 80, -1, -1, 58, 61, 60, 60, 60, 66, 67, 64, 86, 97, 83, -1, -1, 50, -1, 54, 54, -1, -1, -1, -1, -1, 54, 54, 55, 52, -1, -1, 77, 78, 91, 88, -1, -1, -1, -1, -1, -1, -1, -1, 72, 59, -1, 93, 97, 78, 78, 95, -1, 58, 61, 60, 54, 60, 60, 66, 64, -1, -1, -1, -1, -1, -1, 53, 54, 54, 54, -1, 54, 67, 63, 53, -1, 54, 54, 71, -1, -1, 77, 78, 83, -1, -1, -1, -1, -1, -1, -1, 72, 72, 67, 64, -1, -1, 94, 87, 93, 92, 58, 60, 60, 60, 54, 54, 60, 64, -1, -1, -1, -1, -1, -1, -1, 53, -1, -1, -1, -1, 67, 64, -1, 53, 54, 54, 67, 64, -1, 74, 85, 91, 88, -1, -1, -1, -1, -1, -1, -1, 72, 72, 71, -1, -1, -1, -1, -1, -1, 50, 61, 60, 60, 54, 54, 54, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 64, -1, 58, 60, 54, 54, 59, -1, -1, 77, 78, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 56, -1, -1, -1, -1, 53, 60, 60, 60, -1, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 67, 64, -1, 58, 61, 54, 54, 67, 64, -1, 82, 85, 78, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 71, -1, -1, -1, -1, 53, 60, 60, -1, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 64, -1, -1, 54, 54, 54, 67, 64, -1, 64, 77, 84, 91, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 71, -1, -1, -1, -1, 53, 60, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, -1, -1, 64, -1, -1, 58, 54, 54, 67, 64, -1, -1, -1, 77, 84, 91, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 72, 72, 71, -1, -1, -1, -1, 70, 73, 60, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 63, 73, -1, -1, 54, -1, -1, 65, 54, 67, 64, -1, -1, -1, 82, 85, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 72, 67, 69, 68, -1, -1, -1, -1, -1, 65, 60, 60, -1, -1, -1, -1, -1, 54, -1, -1, 54, 64, -1, 65, -1, -1, 54, -1, 54, 54, 67, 64, -1, -1, -1, -1, -1, 60, 60, -1, -1, -1, -1, -1, -1, 60, 60, 60, 67, 63, 63, 64, -1, -1, -1, -1, -1, -1, -1, 65, 60, 60, -1, -1, -1, -1, 54, 67, 54, -1, 54, -1, -1, 53, 54, -1, -1, 54, 54, 67, 64, -1, -1, -1, -1, -1, 58, 61, 60, -1, -1, -1, -1, -1, 60, 60, 60, 67, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 60, 60, 60, -1, -1, -1, 67, 64, -1, -1, -1, -1, -1, 53, 54, -1, 60, 54, 67, 64, -1, -1, -1, -1, -1, -1, 65, 60, 60, -1, -1, -1, -1, -1, -1, 60, 66, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 60, 60, 60, 60, 72, 72, 64, -1, -1, -1, -1, -1, -1, 61, 60, 60, 60, 60, 59, -1, -1, -1, -1, -1, -1, -1, 65, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, -1, -1, 62, 69, 73, 60, 67, 63, 63, -1, -1, -1, -1, -1, -1, -1, 60, 60, 60, 60, 67, 64, -1, -1, -1, -1, -1, -1, -1, 62, 60, -1, -1, -1, -1, -1, 60, 55, 56, -1, -1, -1, -1, -1, -1, 60, 60, -1, -1, 60, 59, -1, -1, -1, 70, 69, 68, -1, 82, 57, 57, 57, 57, -1, 66, 67, 63, 63, 63, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 55, 57, 52, -1, -1, -1, -1, 57, 60, 60, 60, 60, 59, -1, -1, -1, -1, -1, 74, 75, 85, 60, 60, 60, -1, 66, 67, 64, -1, -1, -1, -1, -1, -1, -1, 58, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 55, 52, -1, -1, 60, -1, 60, -1, 60, 60, 59, -1, -1, -1, -1, 74, 85, 78, 78, 60, -1, 54, 67, 63, 64, -1, -1, -1, -1, -1, -1, -1, 57, 61, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 55, 57, 57, -1, 60, 60, 60, 60, 60, 55, 56, -1, -1, -1, -1, -1, 78, 78, -1, -1, 54, 59, -1, -1, -1, -1, -1, -1, 58, 57, 54, 54, 60, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, 54, -1, 60, 60, 60, -1, -1, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 52, -1, -1, -1, 58, 57, 61, 54, 54, 54, 54, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 57, 56, 86, 97, 90, 90, -1, -1, -1, -1, 60, 59, -1, -1, 58, 61, 54, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 60, 55, 52, 94, 87, 97, 96]}, {"type": 2, "data": [9, 9, 10, 9, 10, 11, 9, 10, 11, 12, 13, 9, 10, 11, 9, 10, 11, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 12, 12, 9, 9, 10, 11, 9, 10, 9, 10, 11, 12, 13, 14, 12, 13, 14, 9, 10, 9, 10, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 15, 15, 12, 12, 13, 14, 12, 13, 12, 13, 14, 9, 9, 10, 11, 16, 17, 12, 9, 12, 13, 14, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 15, 16, 17, 15, 12, 13, 14, 10, 11, 99, 9, 10, 15, 15, 16, 17, 15, 16, 15, 16, 17, 12, 12, 13, 9, 10, 9, 10, 11, 15, 16, 17, 11, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 9, 10, 11, 11, 15, 16, 17, 15, 16, 17, 13, 14, 99, 9, 9, 9, 10, 11, 12, 9, 10, 11, 12, 13, 15, 9, 10, 12, 13, 12, 13, 14, 16, 9, 9, 10, 11, 9, 10, 11, 99, 99, 99, 99, 99, 99, 99, 99, 9, 10, 12, 13, 14, 14, 14, 10, 9, 9, 10, 11, 16, 17, 99, 12, 12, 12, 13, 14, 15, 9, 10, 11, 9, 10, 11, 9, 10, 15, 16, 15, 16, 17, 9, 10, 11, 10, 11, 9, 10, 11, 9, 100, 100, 100, 100, 100, 100, 14, 12, 13, 15, 16, 17, 17, 11, 9, 10, 12, 13, 14, 10, 11, 9, 15, 9, 10, 11, 17, 13, 12, 13, 14, 12, 13, 14, 12, 9, 10, 11, 15, 16, 17, 9, 10, 11, 13, 9, 10, 11, 14, 12, 13, 14, 101, 101, 101, 101, 17, 15, 16, 17, 9, 10, 11, 14, 12, 13, 15, 16, 17, 9, 10, 12, 9, 12, 13, 14, 15, 16, 15, 16, 17, 15, 9, 9, 10, 12, 13, 14, 9, 10, 11, 9, 10, 11, 16, 12, 13, 14, 17, 15, 16, 11, 13, 101, 101, 101, 12, 13, 14, 9, 9, 10, 11, 17, 15, 9, 10, 11, 17, 12, 13, 13, 12, 15, 16, 17, 9, 10, 9, 10, 11, 16, 9, 10, 11, 15, 16, 17, 9, 10, 11, 12, 13, 14, 11, 15, 16, 17, 17, 15, 16, 17, 9, 101, 101, 101, 101, 16, 17, 12, 12, 13, 14, 15, 9, 12, 13, 14, 11, 9, 9, 10, 15, 9, 10, 11, 12, 13, 12, 13, 14, 9, 12, 13, 14, 9, 9, 10, 11, 9, 10, 11, 16, 17, 11, 9, 9, 10, 9, 9, 10, 9, 12, 101, 101, 101, 101, 101, 9, 9, 15, 16, 17, 9, 10, 11, 14, 10, 11, 12, 12, 13, 9, 9, 10, 9, 10, 11, 9, 10, 11, 12, 15, 16, 17, 12, 12, 13, 14, 9, 10, 11, 11, 11, 14, 12, 12, 13, 9, 12, 13, 12, 13, 101, 101, 101, 101, 101, 101, 9, 15, 9, 9, 10, 11, 14, 17, 13, 14, 10, 12, 13, 12, 12, 13, 12, 13, 9, 12, 13, 14, 15, 16, 17, 11, 9, 15, 16, 17, 12, 9, 10, 11, 14, 9, 10, 9, 9, 9, 9, 9, 12, 13, 101, 101, 101, 101, 101, 9, 10, 11, 12, 12, 13, 14, 17, 15, 16, 17, 13, 9, 10, 15, 15, 16, 15, 16, 9, 10, 11, 17, 13, 14, 11, 101, 103, 103, 103, 103, 15, 12, 13, 14, 17, 12, 13, 12, 9, 10, 12, 12, 13, 101, 101, 101, 9, 10, 11, 9, 10, 11, 15, 15, 16, 17, 9, 10, 11, 15, 16, 12, 13, 9, 10, 17, 9, 10, 12, 13, 14, 15, 16, 17, 101, 101, 103, 103, 103, 103, 103, 15, 16, 17, 9, 9, 10, 9, 12, 13, 9, 9, 10, 101, 101, 9, 10, 13, 14, 12, 13, 14, 14, 15, 16, 9, 10, 10, 11, 14, 9, 10, 11, 12, 13, 9, 10, 11, 15, 9, 10, 9, 10, 101, 101, 103, 103, 103, 103, 103, 103, 9, 10, 11, 12, 9, 10, 9, 12, 9, 12, 12, 13, 101, 101, 12, 13, 16, 17, 15, 16, 17, 17, 9, 10, 12, 13, 11, 14, 17, 12, 13, 14, 9, 10, 9, 10, 11, 9, 12, 13, 12, 13, 103, 103, 103, 103, 103, 103, 103, 9, 10, 11, 14, 9, 12, 9, 9, 10, 12, 13, 101, 101, 101, 101, 101, 101, 101, 9, 10, 15, 9, 10, 12, 13, 14, 13, 14, 17, 15, 15, 16, 17, 12, 13, 10, 13, 14, 12, 13, 101, 101, 101, 103, 103, 103, 103, 9, 10, 9, 12, 13, 14, 17, 12, 9, 12, 12, 13, 101, 101, 101, 101, 101, 101, 101, 101, 101, 12, 13, 9, 9, 10, 15, 16, 17, 16, 9, 10, 101, 101, 101, 101, 9, 10, 13, 10, 17, 9, 105, 105, 9, 10, 103, 103, 103, 103, 12, 13, 12, 15, 16, 17, 17, 12, 12, 13, 9, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 9, 9, 10, 9, 10, 15, 15, 9, 10, 12, 13, 101, 101, 101, 101, 9, 10, 10, 13, 10, 105, 105, 105, 12, 13, 10, 103, 103, 9, 15, 16, 15, 16, 17, 17, 13, 14, 11, 12, 9, 101, 101, 101, 101, 101, 101, 101, 101, 9, 10, 11, 12, 13, 12, 13, 9, 10, 12, 13, 14, 9, 10, 101, 101, 101, 12, 13, 13, 10, 10, 105, 105, 105, 105, 10, 10, 103, 9, 12, 13, 14, 16, 17, 17, 14, 16, 17, 14, 15, 16, 101, 101, 101, 101, 14, 10, 9, 10, 9, 10, 14, 13, 9, 10, 11, 12, 9, 15, 16, 17, 12, 9, 10, 102, 102, 105, 105, 105, 105, 105, 105, 105, 105, 105, 13, 13, 9, 12, 15, 16, 17, 14, 14, 16, 17, 15, 9, 10, 101, 101, 101, 101, 101, 16, 17, 11, 12, 13, 12, 13, 11, 9, 9, 10, 11, 15, 12, 13, 14, 10, 11, 12, 13, 103, 103, 105, 105, 105, 105, 105, 105, 105, 105, 9, 10, 10, 12, 15, 16, 17, 17, 17, 17, 17, 14, 9, 12, 9, 101, 101, 101, 101, 17, 12, 13, 14, 12, 13, 14, 14, 14, 12, 12, 13, 14, 12, 15, 16, 17, 10, 11, 10, 11, 9, 10, 105, 105, 105, 105, 105, 105, 105, 105, 10, 13, 13, 15, 16, 17, 16, 17, 17, 9, 10, 11, 12, 15, 101, 101, 101, 101, 101, 11, 15, 16, 17, 15, 16, 17, 17, 17, 15, 15, 16, 17, 15, 12, 13, 14, 13, 14, 9, 10, 11, 10, 105, 105, 105, 105, 105, 105, 16, 17, 13, 10, 12, 16, 15, 16, 17, 15, 12, 12, 13, 14, 15, 16, 101, 101, 101, 101, 101, 14, 17, 15, 15, 16, 17, 10, 11, 15, 16, 15, 16, 17, 15, 15, 16, 17, 16, 17, 12, 13, 14, 13, 105, 105, 105, 105, 105, 14, 13, 13, 14, 13, 15, 16, 9, 9, 10, 11, 15, 15, 9, 9, 9, 10, 101, 101, 101, 101, 101, 17, 101, 12, 13, 14, 14, 13, 14, 14, 11, 11, 10, 11, 9, 10, 15, 16, 15, 16, 15, 16, 17, 16, 12, 13, 9, 10, 16, 17, 16, 16, 17, 10, 11, 12, 12, 12, 13, 14, 9, 9, 12, 12, 12, 101, 101, 101, 101, 101, 101, 101, 101, 15, 16, 17, 17, 16, 17, 17, 14, 14, 10, 11, 9, 10, 15, 16, 15, 9, 10, 11, 9, 10, 12, 13, 12, 13, 10, 17, 15, 16, 12, 13, 14, 15, 15, 15, 16, 17, 12, 12, 13, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 9, 10, 9, 10, 11, 10, 17, 17, 13, 14, 12, 13, 14, 9, 10, 12, 13, 14, 12, 13, 9, 10, 12, 13, 13, 10, 11, 9, 15, 16, 17, 13, 15, 16, 17, 17, 101, 101, 101, 102, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 12, 13, 12, 13, 14, 13, 101, 15, 16, 17, 15, 16, 17, 12, 13, 15, 16, 17, 15, 16, 9, 10, 9, 9, 10, 13, 14, 12, 13, 14, 15, 9, 10, 11, 9, 9, 102, 102, 102, 102, 102, 102, 102, 102, 102, 102, 102, 102, 102, 102, 15, 16, 15, 16, 17, 101, 101, 101, 101, 101, 101, 101, 9, 10, 16, 15, 15, 16, 12, 13, 12, 13, 12, 12, 13, 16, 17, 15, 16, 17, 15, 12, 13, 14, 12, 13, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 103, 101, 101, 101, 101, 101, 101, 101, 101, 12, 13, 16, 15, 16, 17, 15, 16]}], "blocks": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0]}