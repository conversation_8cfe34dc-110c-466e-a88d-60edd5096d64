{"mW": 1080, "mH": 840, "tW": 24, "tH": 24, "tiles": [["497", 0, 2, 1], ["497_4", 0, 2, 1], ["928", 0, 2, 1], ["302", 0, 2, 2], ["162", 0, 2, 2], ["160", 0, 1, 1], ["496", 0, 2, 1], ["696", 0, 2, 2], ["937", 0, 2, 1], ["940", 0, 2, 1], ["1300", 0, 3, 2], ["497_1", 0, 2, 1], ["203_13", 0, 2, 1], ["203_14", 0, 2, 1], ["588", 0, 1, 1], ["203_15", 0, 2, 1], ["203_16", 0, 2, 1], ["106", 0, 3, 3], ["302_6", 0, 2, 2], ["940_1", 0, 2, 1], ["1516", 0, 1, 1], ["1509", 0, 3, 3], ["1509_1", 0, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["154", 0, 2, 1], ["1233", 0, 3, 2], ["304", 0, 3, 2], ["369", 0, 3, 3], ["678", 0, 3, 2]], "layers": [{"type": 3, "obj": [[2, "88", 695, 360, 88, 61, 0], [2, "268", 747, 305, 106, 82, 0], [2, "1511", 878, 376, 84, 42, 0], [2, "1511", 878, 362, 84, 42, 0], [2, "1511", 879, 346, 84, 42, 0], [2, "1510", 892, 334, 58, 32, 0], [2, "1450", 463, 594, 44, 48, 2], [2, "1448", 422, 576, 44, 48, 2], [2, "1449", 382, 555, 44, 47, 2], [2, "1451", 342, 534, 44, 47, 2], [2, "1596", 1065, 501, 62, 44, 0], [2, "1596", 722, 376, 62, 44, 0], [2, "1596", 768, 354, 62, 44, 0], [2, "1600", 778, 380, 68, 43, 0], [2, "1600", 738, 399, 68, 43, 0], [2, "1600", 811, 396, 68, 43, 0], [2, "1600", 771, 415, 68, 43, 0], [2, "1600", 844, 412, 68, 43, 0], [2, "1600", 804, 431, 68, 43, 0], [2, "1600", 875, 427, 68, 43, 0], [2, "1600", 835, 446, 68, 43, 0], [2, "1601", 715, 410, 42, 31, 0], [2, "1569", 386, 560, 14, 16, 0], [2, "1571", 370, 564, 22, 17, 0], [2, "1569", 368, 567, 14, 16, 0], [2, "1571", 353, 572, 22, 17, 0], [2, "1571", 342, 577, 22, 17, 0], [2, "1571", 332, 582, 22, 17, 0], [2, "1597", 70, -29, 30, 34, 0], [2, "1612", 615, 167, 50, 46, 0], [2, "1612", 592, 176, 50, 46, 0], [2, "1554", 571, 184, 50, 46, 0], [2, "1514", 611, 171, 18, 16, 0], [2, "1514", 632, 162, 18, 16, 0], [2, "1557", 621, 194, 14, 15, 0], [2, "1557", 647, 182, 14, 15, 0], [2, "1574", 615, 164, 10, 13, 0], [2, "1514", 611, 154, 18, 16, 0], [2, "1600", 637, 124, 68, 43, 0], [2, "1531_1", 716, 115, 46, 24, 0], [2, "1531_1", 738, 104, 46, 24, 0], [2, "1531_1", 757, 94, 46, 24, 0], [2, "1541", 880, 53, 18, 29, 0], [2, "1541", 895, 60, 18, 29, 0], [2, "1541", 868, 60, 18, 29, 0], [2, "1541", 879, 67, 18, 29, 0], [2, "1535", 849, 74, 32, 32, 0], [2, "1526", 834, 83, 30, 29, 0], [2, "1526", 819, 90, 30, 29, 0], [2, "1526", 833, 69, 30, 29, 0], [2, "1526", 820, 74, 30, 29, 0], [2, "1535", 755, 86, 32, 32, 2], [2, "1535", 734, 95, 32, 32, 2], [2, "1622", 569, 202, 22, 23, 0], [2, "1624", 670, 135, 24, 24, 0], [2, "1624", 650, 124, 24, 24, 0], [2, "1577", 998, 110, 8, 30, 0], [2, "1577", 998, 81, 8, 30, 0], [2, "1578", 995, 72, 14, 13, 0], [2, "1556", 997, 58, 10, 18, 0], [2, "1577", 779, -18, 8, 30, 0], [2, "1577", 739, 234, 8, 30, 0], [2, "1577", 739, 205, 8, 30, 0], [2, "1578", 736, 196, 14, 13, 0], [2, "1556", 738, 182, 10, 18, 0], [2, "1577", 540, 96, 8, 30, 0], [2, "1577", 540, 67, 8, 30, 0], [2, "1556", 539, 44, 10, 18, 0], [2, "1578", 537, 58, 14, 13, 0], [2, "1549", 1002, 73, 34, 26, 2], [2, "1549", 1025, 86, 34, 26, 2], [2, "1549", 1049, 99, 34, 26, 2], [2, "1549", 1072, 111, 34, 26, 2], [2, "1549", 1051, 117, 34, 26, 0], [2, "1549", 1028, 130, 34, 26, 0], [2, "1549", 1004, 143, 34, 26, 0], [2, "1549", 980, 155, 34, 26, 0], [2, "1549", 956, 167, 34, 26, 0], [2, "1549", 932, 180, 34, 26, 0], [2, "1577", 848, 179, 8, 30, 0], [2, "1577", 848, 150, 8, 30, 0], [2, "1556", 847, 127, 10, 18, 0], [2, "1578", 845, 141, 14, 13, 0], [2, "1577", 1003, 265, 8, 30, 0], [2, "1577", 1003, 236, 8, 30, 0], [2, "1549", 854, 145, 34, 26, 2], [2, "1549", 877, 158, 34, 26, 2], [2, "1549", 901, 170, 34, 26, 2], [2, "1549", 924, 182, 34, 26, 2], [2, "1549", 948, 194, 34, 26, 2], [2, "1549", 973, 206, 34, 26, 2], [2, "1549", 1052, 183, 34, 26, 0], [2, "1549", 1028, 195, 34, 26, 0], [2, "1549", 1005, 207, 34, 26, 0], [2, "1578", 1000, 227, 14, 13, 0], [2, "1556", 1002, 213, 10, 18, 0], [2, "1543", 725, 193, 24, 35, 2], [2, "1543", 526, 59, 24, 35, 2], [2, "1531_1", -5, 54, 46, 24, 0], [2, "1531_1", 18, 43, 46, 24, 0], [2, "1531_1", 63, 20, 46, 24, 0], [2, "1531_1", 40, 31, 46, 24, 0], [2, "1531_1", 106, 0, 46, 24, 0], [2, "1531_1", 83, 11, 46, 24, 0], [2, "1531_1", 129, -11, 46, 24, 0], [2, "1531_1", 152, -22, 46, 24, 0], [2, "1597", 55, -22, 30, 34, 0], [2, "1597", 40, -16, 30, 34, 0], [2, "1597", 26, -9, 30, 34, 0], [2, "1517", 90, -10, 40, 30, 2], [2, "1517", 67, 1, 40, 30, 2], [2, "1517", 44, 11, 40, 30, 2], [2, "1517", 21, 22, 40, 30, 2], [2, "1517", -2, 33, 40, 30, 2], [2, "1597", 13, -3, 30, 34, 0], [2, "1597", -2, 3, 30, 34, 0], [2, "1597", -16, 10, 30, 34, 0], [2, "1531_1", -25, -13, 46, 24, 0], [2, "1546", 447, 622, 38, 28, 2], [2, "1546", 416, 636, 38, 28, 2], [2, "1546", 387, 649, 38, 28, 2], [2, "1546", 302, 617, 38, 28, 0], [2, "1546", 331, 632, 38, 28, 0], [2, "1546", 362, 647, 38, 28, 0], [2, "1531_1", 350, 625, 46, 24, 0], [2, "1531_1", 373, 637, 46, 24, 0], [2, "1531_1", 396, 626, 46, 24, 0], [2, "1531_1", 372, 613, 46, 24, 0], [2, "1531_1", 311, 605, 46, 24, 0], [2, "1531_1", 334, 618, 46, 24, 0], [2, "1531_1", 356, 605, 46, 24, 0], [2, "1531_1", 333, 593, 46, 24, 0], [2, "1531_1", 335, 594, 46, 24, 0], [2, "1531_1", 358, 606, 46, 24, 0], [2, "1531_1", 380, 594, 46, 24, 0], [2, "1531_1", 357, 582, 46, 24, 0], [2, "1531_1", 389, 608, 46, 24, 0], [2, "1531_1", 411, 618, 46, 24, 0], [2, "1531_1", 434, 608, 46, 24, 0], [2, "1531_1", 411, 596, 46, 24, 0], [2, "1531_1", 369, 576, 46, 24, 0], [2, "1531_1", 392, 587, 46, 24, 0], [2, "1510", 364, 598, 58, 32, 0], [2, "1596", 418, 596, 62, 44, 0], [2, "1596", 374, 619, 62, 44, 0], [2, "1575", 325, 612, 14, 8, 0], [2, "1574", 327, 601, 10, 13, 0], [2, "1573", 327, 587, 16, 14, 0], [2, "1573", 386, 565, 16, 14, 2], [2, "1574", 391, 577, 10, 13, 2], [2, "1575", 389, 585, 14, 8, 0], [2, "1596", 26, 147, 62, 44, 0], [2, "1596", -18, 169, 62, 44, 0], [2, "1596", 9, 123, 62, 44, 0], [2, "1596", -35, 145, 62, 44, 0], [2, "1510_1", -30, 119, 58, 32, 0], [2, "268", -30, 37, 106, 82, 0], [2, "1549", 474, -17, 34, 26, 2], [2, "1549", 497, -5, 34, 26, 2], [2, "1549", 521, 7, 34, 26, 2], [2, "1549", 546, 19, 34, 26, 2], [2, "1549", 641, -13, 34, 26, 0], [2, "1549", 617, 0, 34, 26, 0], [2, "1549", 593, 12, 34, 26, 0], [2, "1549", 569, 24, 34, 26, 0], [2, "1214", 80, 163, 34, 55, 2], [2, "1624", 76, 176, 24, 24, 0], [2, "1449", 456, 103, 44, 47, 0], [2, "1451", 493, 85, 44, 47, 0], [2, "1451", 84, 279, 44, 47, 0], [2, "1449", 47, 297, 44, 47, 0], [2, "1448", 117, 263, 44, 48, 0], [2, "1450", 151, 246, 44, 48, 0], [2, "1449", 184, 230, 44, 47, 0], [2, "88", 177, 550, 88, 61, 0], [2, "88", 497, 465, 88, 61, 0], [2, "268", 535, 424, 106, 82, 0], [2, "87", 574, 493, 72, 57, 0], [2, "1601", 732, 418, 42, 31, 0], [2, "1601", 767, 435, 42, 31, 0], [2, "1601", 801, 452, 42, 31, 0], [2, "1601", 836, 469, 42, 31, 0], [2, "1601", 930, 443, 42, 31, 2], [2, "1601", 897, 459, 42, 31, 2], [2, "1601", 867, 473, 42, 31, 2], [2, "1596", 722, 387, 62, 44, 2], [2, "1596", 769, 409, 62, 44, 2], [2, "1596", 813, 433, 62, 44, 2], [2, "1596", 896, 404, 62, 44, 2], [2, "1596", 852, 380, 62, 44, 2], [2, "1596", 805, 358, 62, 44, 2], [2, "1596", 901, 423, 62, 44, 0], [2, "1596", 855, 445, 62, 44, 0], [2, "1510_1", 781, 385, 58, 32, 0], [2, "1510_1", 846, 418, 58, 32, 0], [2, "1581", 823, 406, 36, 23, 2], [2, "1596", 1022, 522, 62, 44, 0], [2, "1600", 1032, 544, 68, 43, 0], [2, "1601", 1009, 555, 42, 31, 0], [2, "1601", 1026, 563, 42, 31, 0], [2, "1601", 1061, 580, 42, 31, 0], [2, "1596", 1016, 532, 62, 44, 2], [2, "1596", 1063, 554, 62, 44, 2], [2, "1428", 804, 615, 148, 126, 0], [2, "268", 215, 509, 106, 82, 0], [2, "1428", 830, 631, 148, 126, 0], [2, "1428", 856, 648, 148, 126, 0], [2, "1428", 887, 663, 148, 126, 0], [2, "1428", 914, 682, 148, 126, 0], [2, "1236", 909, 725, 42, 43, 0], [2, "1236", 1026, 678, 42, 43, 2], [2, "1449", 682, 698, 44, 47, 2], [2, "1448", 722, 719, 44, 48, 2], [2, "1450", 763, 737, 44, 48, 2], [2, "1451", 805, 760, 44, 47, 2], [2, "1449", 847, 781, 44, 47, 2], [2, "1450", 888, 800, 44, 48, 2], [2, "1451", 931, 821, 44, 47, 2], [2, "1449", 1002, 553, 44, 47, 2], [2, "1448", 1042, 574, 44, 48, 2], [2, "1449", 219, 472, 44, 47, 2], [2, "1448", 259, 493, 44, 48, 2], [2, "1450", 300, 511, 44, 48, 2], [2, "268", -67, 669, 106, 82, 0], [2, "1596", -46, 718, 62, 44, 0], [2, "1600", -36, 744, 68, 43, 0], [2, "1600", -3, 760, 68, 43, 0], [2, "1600", 30, 776, 68, 43, 0], [2, "1600", -10, 795, 68, 43, 0], [2, "1600", 61, 791, 68, 43, 0], [2, "1600", 21, 810, 68, 43, 0], [2, "1601", -13, 816, 42, 31, 0], [2, "1601", 22, 833, 42, 31, 0], [2, "1601", 116, 807, 42, 31, 2], [2, "1601", 83, 823, 42, 31, 2], [2, "1601", 53, 837, 42, 31, 2], [2, "1596", -1, 797, 62, 44, 2], [2, "1596", 82, 768, 62, 44, 2], [2, "1596", 38, 744, 62, 44, 2], [2, "1596", -9, 722, 62, 44, 2], [2, "1596", 87, 787, 62, 44, 0], [2, "1596", 41, 809, 62, 44, 0], [2, "1510_1", -33, 749, 58, 32, 0], [2, "1510_1", 32, 782, 58, 32, 0], [2, "1581", 9, 770, 36, 23, 2], [2, "1600", -43, 779, 68, 43, 0]]}, {"type": 4, "obj": [[2, "1538", 696, -1, 32, 39, 0], [2, "1577", 779, 10, 8, 30, 0], [2, "1563", 476, -10, 18, 58, 0], [2, "1563", 669, -8, 18, 58, 0], [2, "1548", 872, 21, 18, 36, 0], [2, "1540", 375, 44, 36, 25, 2], [2, "1545", 401, 58, 24, 32, 2], [2, "1563", 570, 38, 18, 58, 0], [2, "1548", 986, 86, 18, 36, 0], [2, "169", 234, 101, 36, 49, 0], [2, "169", 270, 101, 36, 49, 2], [2, "1577", 540, 124, 8, 30, 0], [2, "1607", 541, 60, 32, 95, 2], [4, 2, 620, 156, 0, 4033], [4, 2, 620, 156, 0, 4033], [2, "1548", 656, 125, 18, 36, 0], [2, "1556", 176, 145, 10, 18, 0], [2, "1577", 998, 140, 8, 30, 0], [2, "1556", 355, 153, 10, 18, 0], [2, "1607", 47, 81, 32, 95, 0], [2, "89", -18, 90, 48, 95, 0], [2, "1518", 857, 154, 22, 34, 0], [2, "1523", 821, 172, 44, 35, 2], [2, "1607", 437, 115, 32, 95, 0], [2, "1540", 335, 187, 36, 25, 0], [2, "1556", 261, 196, 10, 18, 0], [2, "1540", 311, 198, 36, 25, 0], [2, "1523", 787, 189, 44, 35, 2], [2, "1548", 770, 190, 18, 36, 0], [2, "1540", 289, 209, 36, 25, 0], [2, "1577", 848, 208, 8, 30, 0], [2, "1612", 1034, 194, 50, 46, 0], [4, 3, 788, 258, 0, 4033], [2, "904", 937, 216, 36, 47, 0], [2, "1518", 694, 235, 22, 34, 0], [2, "1535", 852, 242, 32, 32, 0], [2, "1548", 424, 239, 18, 36, 0], [2, "1523", 656, 255, 44, 35, 2], [2, "1577", 739, 264, 8, 30, 0], [2, "1607", 739, 199, 32, 95, 2], [2, "1523", 379, 261, 44, 35, 2], [2, "1607", 227, 213, 32, 95, 0], [2, "1577", 1003, 291, 8, 30, 0], [2, "1545", 1011, 307, 24, 32, 0], [2, "1548", 538, 304, 18, 36, 0], [2, "1523", 967, 314, 44, 35, 0], [4, 1, 198, 359, 0, 4033], [2, "1523", 491, 329, 44, 35, 2], [2, "1518", 476, 336, 22, 34, 0], [2, "1545", 587, 339, 24, 32, 0], [2, "1546", 554, 359, 38, 28, 2], [4, 4, 465, 395, 0, 4033], [2, "1545", 536, 363, 24, 32, 0], [2, "1548", 169, 364, 18, 36, 0], [2, "1607", 511, 308, 32, 95, 0], [2, "1607", 15, 319, 32, 95, 0], [2, "1523", 120, 389, 44, 35, 2], [2, "1545", 3, 393, 24, 32, 0], [2, "1518", 103, 397, 22, 34, 0], [2, "1518", 353, 401, 22, 34, 0], [2, "1523", 313, 419, 44, 35, 2], [2, "1548", 283, 429, 18, 36, 0], [2, "1521", 992, 448, 30, 19, 0], [2, "1521", 967, 461, 30, 19, 0], [2, "1607", 679, 394, 32, 95, 0], [2, "1607", 201, 460, 32, 95, 0], [2, "89", 551, 477, 48, 95, 0], [2, "1523", 61, 541, 44, 35, 2], [2, "1523", 25, 560, 44, 35, 2], [2, "1548", 8, 565, 18, 36, 0], [2, "1607", 987, 545, 32, 95, 0], [2, "1546", 14, 629, 38, 28, 2], [2, "89", 225, 563, 48, 95, 0], [2, "1545", -4, 633, 24, 32, 0], [2, "1577", 947, 659, 8, 30, 0], [2, "1607", 488, 604, 32, 95, 0], [2, "1577", 971, 675, 8, 30, 0], [2, "1577", 998, 690, 8, 30, 0], [2, "1577", 1028, 707, 8, 30, 0], [2, "1577", 1056, 725, 8, 30, 0], [2, "1577", 804, 739, 8, 30, 0], [2, "1607", 664, 683, 32, 95, 0], [2, "1577", 828, 755, 8, 30, 0], [2, "1577", 828, 755, 8, 30, 0], [2, "1577", 828, 755, 8, 30, 0], [2, "1577", 855, 770, 8, 30, 0], [2, "1577", 885, 787, 8, 30, 0], [2, "1577", 913, 805, 8, 30, 0], [2, "310", 719, 807, 44, 33, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 92, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 92, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, 92, 92, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 110, 111, -1, -1, 117, 116, 115, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 110, 111, 112, -1, -1, 107, 106, 105, 117, 116, 115, 92, 100, 90, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 83, 98, 98, 98, 101, -1, -1, -1, -1, -1, -1, -1, 100, 101, 102, -1, -1, -1, -1, 84, 83, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 98, 98, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 83, 83, 83, 83, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 96, 95, 95, 94, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 98, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 98, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 115, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 92, 92, 94, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 110, 111, 112, -1, -1, -1, -1, -1, -1, 117, 116, 115, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 110, 111, 112, -1, -1, 117, 116, 115, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 98, 98, 82, -1, -1, -1, -1, -1, 107, 106, 110, 111, 112, 98, 98, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 92, 92, 92, 92, 110, 111, 112, -1, -1, -1, -1, -1, 107, 106, 105, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 92, 92, 92, 92, 110, 78, -1, -1, -1, -1, -1, -1, 107, 106, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 83, 83, 83, 83, 83, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 92, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 98, 98, 98, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 110, 111, -1, -1, -1, -1, -1, -1, -1, -1, 91, 98, 98, 98, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, -1, -1, -1, -1, -1, -1, -1, -1, 106, 105, 98, 115, 110, 111, 112, -1, -1, 117, 116, 115, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 110, 111, 112, -1, -1, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 98, 98, 110, 111, 112, 116, 115, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 98, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 110, 111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 110, 111, 112, -1, -1, -1, -1, -1, -1, 107, 106, 105, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 110, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, -1, 110, 111, 112, -1, -1, -1]}, {"type": 3, "obj": [[2, "1457", 693, 455, 22, 30, 0], [2, "1541", 291, 652, 18, 29, 0], [2, "1535", 960, 454, 32, 32, 2], [2, "1511", 879, 408, 84, 42, 0], [2, "1511", 881, 392, 84, 42, 0], [2, "1604", 240, -48, 60, 83, 0], [2, "1585", 457, 623, 30, 67, 2], [2, "1585", 426, 607, 30, 67, 2], [2, "1585", 395, 592, 30, 67, 2], [2, "1585", 365, 593, 30, 67, 0], [2, "1585", 335, 608, 30, 67, 0], [2, "1585", 304, 623, 30, 67, 0], [2, "1616", 108, 62, 30, 24, 0], [2, "1582", 74, 35, 30, 59, 0], [2, "1535", 903, 193, 32, 32, 2], [2, "1535", 881, 205, 32, 32, 2], [2, "1613", 652, 177, 24, 28, 0], [2, "1556", 569, 218, 10, 18, 0], [2, "1613", 857, 76, 24, 28, 0], [2, "1613", 808, 99, 24, 28, 0], [2, "1613", 749, 130, 24, 28, 0], [2, "1613", 713, 147, 24, 28, 0], [2, "1613", 594, 205, 24, 28, 0], [2, "1613", 575, 214, 24, 28, 0], [2, "1604", 954, -52, 60, 83, 2], [2, "1604", 997, -33, 60, 83, 2], [2, "1603", 987, -12, 54, 69, 2], [2, "1604", 1033, -10, 60, 83, 2], [2, "1549", 888, 74, 34, 26, 2], [2, "1549", 876, 81, 34, 26, 2], [2, "1549", 864, 88, 34, 26, 2], [2, "1549", 852, 94, 34, 26, 2], [2, "1549", 840, 99, 34, 26, 2], [2, "1549", 828, 105, 34, 26, 2], [2, "1549", 816, 111, 34, 26, 2], [2, "1549", 803, 117, 34, 26, 2], [2, "1549", 764, 137, 34, 26, 2], [2, "1549", 753, 143, 34, 26, 2], [2, "1549", 742, 149, 34, 26, 2], [2, "1549", 730, 154, 34, 26, 2], [2, "1549", 718, 160, 34, 26, 2], [2, "1549", 707, 166, 34, 26, 2], [2, "1549", 670, 183, 34, 26, 2], [2, "1549", 658, 189, 34, 26, 2], [2, "1549", 647, 195, 34, 26, 2], [2, "1549", 634, 201, 34, 26, 2], [2, "1549", 623, 207, 34, 26, 2], [2, "1549", 611, 212, 34, 26, 2], [2, "1549", 599, 218, 34, 26, 2], [2, "1549", 588, 224, 34, 26, 2], [2, "1549", 575, 230, 34, 26, 2], [2, "1613", 886, 89, 24, 28, 0], [2, "1613", 621, 221, 24, 28, 0], [2, "1613", 835, 113, 24, 28, 0], [2, "1613", 771, 145, 24, 28, 0], [2, "1613", 738, 160, 24, 28, 0], [2, "1613", 675, 191, 24, 28, 0], [2, "1613", 601, 229, 24, 28, 0], [2, "1597", 576, 220, 30, 34, 2], [2, "1517", 562, 229, 40, 30, 0], [2, "1546", 655, 183, 38, 28, 2], [2, "1546", 627, 197, 38, 28, 2], [2, "1546", 598, 210, 38, 28, 2], [2, "1599", 573, 217, 34, 27, 2], [2, "1556", 591, 227, 10, 18, 0], [2, "1585", 640, 135, 30, 67, 2], [2, "1582", 670, 135, 30, 59, 0], [2, "1584", 671, 176, 30, 23, 0], [2, "1614", 609, 224, 30, 19, 0], [2, "1614", 639, 217, 30, 19, 0], [2, "1614", 659, 199, 30, 19, 0], [2, "1614", 756, 159, 30, 19, 0], [2, "1614", 845, 106, 30, 19, 0], [2, "1614", 868, 102, 30, 19, 0], [2, "1569", 706, 172, 14, 16, 0], [2, "1571", 691, 177, 22, 17, 0], [2, "1569", 712, 176, 14, 16, 0], [2, "1571", 698, 181, 22, 17, 0], [2, "1576_1", 690, 178, 14, 25, 2], [2, "1576_1", 676, 184, 14, 25, 2], [2, "1546", 673, 184, 38, 28, 2], [2, "1582", 771, 107, 30, 59, 0], [2, "1582", 740, 123, 30, 59, 0], [2, "1582", 711, 123, 30, 59, 2], [2, "1546", 702, 159, 38, 28, 0], [2, "1531", 807, 101, 46, 24, 0], [2, "1531", 830, 90, 46, 24, 0], [2, "1531", 876, 68, 46, 24, 0], [2, "1531", 853, 79, 46, 24, 0], [2, "1546", 802, 108, 38, 28, 0], [2, "1569", 806, 126, 14, 16, 0], [2, "1571", 792, 130, 22, 17, 0], [2, "1546", 770, 139, 38, 28, 2], [2, "1546", 740, 153, 38, 28, 2], [2, "1558", 849, 166, 14, 8, 0], [2, "1558", 909, 136, 14, 8, 0], [2, "1558", 969, 106, 14, 8, 0], [2, "1558", 1025, 78, 14, 8, 0], [2, "1558", 799, 189, 14, 8, 0], [2, "1558", 743, 217, 14, 8, 0], [2, "1558", 683, 247, 14, 8, 0], [2, "1558", 623, 277, 14, 8, 0], [2, "1558", 572, 301, 14, 8, 0], [2, "1558", 516, 329, 14, 8, 0], [2, "1558", 456, 359, 14, 8, 0], [2, "1558", 396, 389, 14, 8, 0], [2, "1558", 222, 473, 14, 8, 0], [2, "1558", 282, 443, 14, 8, 0], [2, "1558", 338, 415, 14, 8, 0], [2, "1558", 162, 503, 14, 8, 0], [2, "1558", -6, 584, 14, 8, 0], [2, "1558", 54, 554, 14, 8, 0], [2, "1558", 110, 526, 14, 8, 0], [2, "1558", 24, 479, 14, 8, 0], [2, "1558", 84, 449, 14, 8, 0], [2, "1558", 144, 419, 14, 8, 0], [2, "1558", 251, 366, 14, 8, 0], [2, "1558", 311, 336, 14, 8, 0], [2, "1558", 371, 306, 14, 8, 0], [2, "1558", 200, 390, 14, 8, 0], [2, "1558", 474, 255, 14, 8, 0], [2, "1558", 534, 225, 14, 8, 0], [2, "1558", 594, 195, 14, 8, 0], [2, "1558", 423, 279, 14, 8, 0], [2, "1558", 816, 90, 14, 8, 0], [2, "1558", 876, 60, 14, 8, 0], [2, "1558", 936, 30, 14, 8, 0], [2, "1558", 765, 114, 14, 8, 0], [2, "1622", 1021, 70, 22, 23, 0], [2, "1558", 474, 255, 14, 8, 0], [2, "1558", 534, 225, 14, 8, 0], [2, "1558", 594, 195, 14, 8, 0], [2, "1558", 423, 279, 14, 8, 0], [2, "1622", 932, 21, 22, 23, 0], [2, "1622", 965, 98, 22, 23, 0], [2, "1622", 905, 127, 22, 23, 0], [2, "1622", 845, 159, 22, 23, 0], [2, "1622", 795, 181, 22, 23, 0], [2, "1622", 739, 209, 22, 23, 0], [2, "1622", 679, 240, 22, 23, 0], [2, "1622", 569, 224, 22, 23, 0], [2, "1622", 620, 269, 22, 23, 0], [2, "1622", 568, 293, 22, 23, 0], [2, "1622", 512, 321, 22, 23, 0], [2, "1622", 452, 351, 22, 23, 0], [2, "1622", 391, 381, 22, 23, 0], [2, "1622", 334, 407, 22, 23, 0], [2, "1622", 278, 435, 22, 23, 0], [2, "1622", 218, 465, 22, 23, 0], [2, "1622", 157, 495, 22, 23, 0], [2, "1622", 106, 518, 22, 23, 0], [2, "1622", 49, 546, 22, 23, 0], [2, "1622", -10, 576, 22, 23, 0], [2, "1622", 20, 471, 22, 23, 0], [2, "1622", 80, 440, 22, 23, 0], [2, "1622", 140, 410, 22, 23, 0], [2, "1622", 196, 382, 22, 23, 0], [2, "1622", 247, 357, 22, 23, 0], [2, "1622", 306, 327, 22, 23, 0], [2, "1622", 368, 297, 22, 23, 0], [2, "1622", 418, 271, 22, 23, 0], [2, "1622", 470, 246, 22, 23, 0], [2, "1622", 529, 216, 22, 23, 0], [2, "1526", 1038, 145, 30, 29, 0], [2, "1526", 1057, 156, 30, 29, 0], [2, "1526", 1024, 155, 30, 29, 0], [2, "1526", 1048, 166, 30, 29, 0], [2, "1526", 1011, 162, 30, 29, 0], [2, "1526", 1033, 172, 30, 29, 0], [2, "1526", 923, 205, 30, 29, 0], [2, "1526", 943, 217, 30, 29, 0], [2, "1526", 907, 212, 30, 29, 0], [2, "1526", 927, 224, 30, 29, 0], [2, "1526", 891, 220, 30, 29, 0], [2, "1526", 912, 231, 30, 29, 0], [2, "1526", 1038, 138, 30, 29, 0], [2, "1526", 1056, 141, 30, 29, 0], [2, "1526", 1027, 144, 30, 29, 0], [2, "1526", 1046, 154, 30, 29, 0], [2, "1578", 995, 165, 14, 13, 0], [2, "1578", 776, 35, 14, 13, 0], [2, "1578", 736, 290, 14, 13, 0], [2, "1578", 537, 150, 14, 13, 0], [2, "1578", 845, 235, 14, 13, 0], [2, "1578", 1000, 317, 14, 13, 0], [2, "1526", 922, 197, 30, 29, 0], [2, "1526", 905, 203, 30, 29, 0], [2, "1526", 927, 208, 30, 29, 2], [2, "1541", 874, 228, 18, 29, 0], [2, "1541", 887, 238, 18, 29, 0], [2, "1541", 903, 242, 18, 29, 0], [2, "1541", 1013, 173, 18, 29, 0], [2, "1541", 977, 157, 18, 29, 0], [2, "1541", 1029, 178, 18, 29, 0], [2, "1541", 983, 293, 18, 29, 0], [2, "1541", 1006, 290, 18, 29, 0], [2, "1604", 203, -30, 60, 83, 0], [2, "1604", 166, -12, 60, 83, 0], [2, "1604", 56, 40, 60, 83, 0], [2, "1604", 19, 59, 60, 83, 0], [2, "1582", 135, 6, 30, 59, 0], [2, "1582", 105, 20, 30, 59, 0], [2, "1616", 138, 48, 30, 24, 0], [2, "1590", -11, 62, 30, 67, 2], [2, "1585", 303, 637, 30, 67, 2], [2, "1585", 334, 652, 30, 67, 2], [2, "1585", 365, 668, 30, 67, 2], [2, "1585", 396, 668, 30, 67, 0], [2, "1585", 427, 653, 30, 67, 0], [2, "1585", 457, 638, 30, 67, 0], [2, "1582", 426, 662, 30, 59, 0], [2, "1593", 323, 662, 26, 37, 2], [2, "1593", 348, 675, 26, 37, 2], [2, "1584", 458, 689, 30, 23, 0], [2, "1584", 427, 704, 30, 23, 0], [2, "1584", 396, 719, 30, 23, 0], [2, "1584", 365, 719, 30, 23, 2], [2, "1584", 334, 703, 30, 23, 2], [2, "1584", 303, 688, 30, 23, 2], [2, "1584", 456, 631, 30, 23, 0], [2, "1584", 425, 646, 30, 23, 0], [2, "1584", 394, 661, 30, 23, 0], [2, "1584", 365, 661, 30, 23, 2], [2, "1584", 334, 645, 30, 23, 2], [2, "1584", 303, 630, 30, 23, 2], [2, "1552", 462, 666, 10, 15, 0], [2, "1556", 474, 661, 10, 18, 0], [2, "1563", 386, 689, 18, 58, 0], [2, "1563", 386, 642, 18, 58, 0], [2, "1585", 57, 163, 30, 67, 2], [2, "1585", 26, 147, 30, 67, 2], [2, "1585", -5, 132, 30, 67, 2], [2, "1585", -4, 208, 30, 67, 0], [2, "1585", 27, 193, 30, 67, 0], [2, "1585", 57, 178, 30, 67, 0], [2, "1582", 26, 202, 30, 59, 0], [2, "1584", 58, 229, 30, 23, 0], [2, "1584", 27, 244, 30, 23, 0], [2, "1584", -4, 259, 30, 23, 0], [2, "1584", 56, 171, 30, 23, 0], [2, "1584", 25, 186, 30, 23, 0], [2, "1584", -6, 201, 30, 23, 0], [2, "1552", 62, 206, 10, 15, 0], [2, "1556", 74, 201, 10, 18, 0], [2, "1563", -14, 229, 18, 58, 0], [2, "1563", -14, 182, 18, 58, 0], [2, "1527_1", 30, 250, 38, 23, 2], [2, "1535", 494, -7, 32, 32, 2], [2, "1526", 536, -7, 30, 29, 0], [2, "1526", 556, 5, 30, 29, 0], [2, "1526", 520, 0, 30, 29, 0], [2, "1526", 540, 12, 30, 29, 0], [2, "1526", 504, 8, 30, 29, 0], [2, "1526", 525, 19, 30, 29, 0], [2, "1526", 518, -9, 30, 29, 0], [2, "1526", 540, -4, 30, 29, 2], [2, "1541", 500, 26, 18, 29, 0], [2, "1541", 516, 30, 18, 29, 0], [2, "1526", 685, 11, 30, 29, 2], [2, "1526", 685, -7, 30, 29, 2], [2, "1537", 705, 28, 16, 31, 0], [2, "1535", 746, -4, 32, 32, 0], [2, "1535", 729, 7, 32, 32, 0], [2, "1526", 401, -6, 30, 29, 0], [2, "1526", 416, -13, 30, 29, 0], [2, "1541", 444, -5, 18, 29, 0], [2, "1541", 650, 7, 18, 29, 0], [2, "1585", 722, 433, 30, 67, 2], [2, "1585", 753, 448, 30, 67, 2], [2, "1585", 784, 464, 30, 67, 2], [2, "1582", 753, 456, 30, 59, 2], [2, "1584", 784, 515, 30, 23, 2], [2, "1584", 753, 499, 30, 23, 2], [2, "1584", 722, 484, 30, 23, 2], [2, "1584", 784, 457, 30, 23, 2], [2, "1584", 753, 441, 30, 23, 2], [2, "1584", 722, 426, 30, 23, 2], [2, "1552", 789, 488, 10, 15, 2], [2, "1556", 738, 463, 10, 18, 0], [2, "1584", 815, 531, 30, 23, 2], [2, "1585", 814, 479, 30, 67, 2], [2, "1584", 813, 471, 30, 23, 2], [2, "1546", 370, 55, 38, 28, 0], [2, "1546", 329, 198, 38, 28, 2], [2, "1546", 297, 214, 38, 28, 2], [2, "1558_2", 216, 76, 14, 8, 0], [2, "1558_2", 259, 54, 14, 8, 0], [2, "1558_2", 298, 34, 14, 8, 0], [2, "1558_2", 76, 144, 14, 8, 0], [2, "1558_2", 119, 122, 14, 8, 0], [2, "1623", 115, 113, 22, 23, 0], [2, "1623", 70, 135, 22, 23, 0], [2, "1623", 211, 68, 22, 23, 0], [2, "1623", 254, 45, 22, 23, 0], [2, "1623", 294, 26, 22, 23, 0], [2, "1457", -9, 265, 22, 30, 0], [2, "1456", 85, 209, 24, 32, 0], [2, "1457", 77, 220, 22, 30, 0], [2, "1584", 845, 546, 30, 23, 2], [2, "1585", 844, 494, 30, 67, 2], [2, "1584", 844, 486, 30, 23, 2], [2, "5", 529, 530, 42, 66, 0], [2, "5", 572, 530, 42, 66, 2], [2, "1584", 876, 547, 30, 23, 0], [2, "1585", 875, 496, 30, 67, 0], [2, "1584", 875, 488, 30, 23, 0], [2, "1584", 906, 532, 30, 23, 0], [2, "1585", 905, 481, 30, 67, 0], [2, "1584", 905, 473, 30, 23, 0], [2, "1584", 937, 517, 30, 23, 0], [2, "1585", 936, 466, 30, 67, 0], [2, "1584", 936, 458, 30, 23, 0], [2, "1567", 708, 434, 18, 65, 2], [2, "1567", 858, 508, 18, 65, 2], [2, "1593", 816, 492, 26, 37, 2], [2, "1592", 890, 509, 22, 29, 0], [2, "1592", 933, 490, 22, 29, 0], [2, "1585", 1020, 582, 30, 67, 2], [2, "1585", 1051, 597, 30, 67, 2], [2, "1582", 1051, 605, 30, 59, 2], [2, "1584", 1051, 648, 30, 23, 2], [2, "1584", 1020, 633, 30, 23, 2], [2, "1584", 1051, 590, 30, 23, 2], [2, "1584", 1020, 575, 30, 23, 2], [2, "1457", 789, 748, 22, 30, 0], [2, "1457", 809, 758, 22, 30, 0], [2, "1457", 831, 770, 22, 30, 0], [2, "1457", 857, 788, 22, 30, 0], [2, "1457", 857, 788, 22, 30, 0], [2, "1456", 799, 758, 24, 32, 0], [2, "1456", 885, 803, 24, 32, 0], [2, "1456", 911, 815, 24, 32, 0], [2, "1456", 927, 659, 24, 32, 0], [2, "1457", 1002, 696, 22, 30, 0], [2, "1457", 1030, 714, 22, 30, 0], [2, "1457", 1060, 734, 22, 30, 0], [2, "1457", 949, 671, 22, 30, 0], [2, "1457", 978, 686, 22, 30, 0], [2, "1456", 1009, 707, 24, 32, 0], [2, "328", 654, 772, 32, 29, 0], [2, "1457", 645, 775, 22, 30, 0], [2, "1457", 608, 792, 22, 30, 0], [2, "1456", 677, 771, 24, 32, 0], [2, "329", 754, 799, 42, 37, 0], [2, "1456", 799, 758, 24, 32, 0], [2, "326", 632, 799, 18, 14, 0], [2, "328", 714, 790, 32, 29, 0], [2, "1457", 566, 814, 22, 30, 0], [2, "1456", 579, 807, 24, 32, 0], [2, "1456", 624, 788, 24, 32, 0], [2, "1456", 699, 781, 24, 32, 0], [2, "1457", 690, 801, 22, 30, 0], [2, "1541", 927, 526, 18, 29, 0], [2, "1541", 909, 535, 18, 29, 0], [2, "1535", 960, 436, 32, 32, 2], [2, "1522", 969, 479, 4, 16, 0], [2, "1522", 994, 466, 4, 16, 0], [2, "1522", 1018, 453, 4, 16, 0], [2, "1613", 626, 566, 24, 28, 2], [2, "1613", 647, 577, 24, 28, 2], [2, "1549", 606, 578, 34, 26, 0], [2, "1549", 619, 585, 34, 26, 0], [2, "1531", 608, 577, 46, 24, 0], [2, "679", 622, 558, 36, 32, 0], [2, "637", 604, 566, 26, 32, 0], [2, "1613", 615, 595, 24, 28, 2], [2, "1613", 594, 583, 24, 28, 2], [2, "1585", 123, 820, 30, 67, 0], [2, "5", 202, 621, 42, 66, 0], [2, "5", 245, 621, 42, 66, 2], [2, "14", 266, 675, 32, 30, 0], [2, "1541", 272, 657, 18, 29, 0], [2, "1524", 284, 688, 60, 40, 0], [2, "1581", 730, 640, 36, 23, 0], [2, "1581", 965, 762, 36, 23, 0], [2, "1581", 837, 693, 36, 23, 0], [2, "1457", 545, 527, 22, 30, 0], [2, "1457", 538, 538, 22, 30, 0], [2, "1457", 584, 538, 22, 30, 0], [2, "1457", 567, 550, 22, 30, 0], [2, "1457", 254, 624, 22, 30, 0], [2, "1457", 223, 614, 22, 30, 0], [2, "1456", 209, 627, 24, 32, 0], [2, "1581", 451, 498, 36, 23, 0], [2, "1581", 427, 796, 36, 23, 2], [2, "1581", 524, 742, 36, 23, 2], [2, "1561", 340, 726, 20, 15, 0], [2, "1561", 161, 814, 20, 15, 0], [2, "1561", 186, 669, 20, 15, 0], [2, "1622", 185, 661, 22, 23, 0], [2, "1622", 160, 805, 22, 23, 0], [2, "1622", 340, 717, 22, 23, 0], [2, "1558_1", 837, 573, 14, 8, 2], [2, "1558_2", 385, 768, 14, 8, 3], [2, "1558", 784, 547, 14, 8, 2], [2, "1558_1", 674, 494, 14, 8, 2], [2, "1558_2", 730, 520, 14, 8, 1], [2, "1558", 621, 468, 14, 8, 2], [2, "1558", 951, 628, 14, 8, 2], [2, "1558_1", 1004, 654, 14, 8, 2], [2, "1558_2", 1060, 680, 14, 8, 1], [2, "1558", 709, 767, 14, 8, 2], [2, "1558_1", 762, 793, 14, 8, 2], [2, "1558_2", 818, 819, 14, 8, 1], [2, "1558", 528, 420, 14, 8, 2], [2, "1558", 503, 708, 14, 8, 0], [2, "1558_1", 445, 737, 14, 8, 0], [2, "1558", 328, 796, 14, 8, 0], [2, "1558_1", 270, 825, 14, 8, 0], [2, "1558", 640, 768, 14, 8, 0], [2, "1558_1", 582, 797, 14, 8, 0], [2, "1558_2", 522, 828, 14, 8, 3], [2, "1622", 780, 538, 22, 23, 0], [2, "1622", 946, 618, 22, 23, 0], [2, "1622", 702, 758, 22, 23, 0], [2, "1622", 635, 761, 22, 23, 0], [2, "1622", 498, 700, 22, 23, 0], [2, "1622", 324, 787, 22, 23, 0], [2, "1623", 381, 760, 22, 23, 0], [2, "1623", 517, 819, 22, 23, 0], [2, "1623", 814, 812, 22, 23, 0], [2, "1623", 1056, 671, 22, 23, 0], [2, "1623", 724, 512, 22, 23, 0], [2, "1624", 833, 563, 24, 24, 0], [2, "1624", 668, 483, 24, 24, 0], [2, "1624", 440, 727, 24, 24, 0], [2, "1624", 263, 817, 24, 24, 0], [2, "1624", 759, 785, 24, 24, 0], [2, "1624", 1000, 644, 24, 24, 0], [2, "1622", 522, 411, 22, 23, 0], [2, "1623", 737, 639, 22, 23, 0], [2, "1623", 845, 693, 22, 23, 0], [2, "1623", 971, 761, 22, 23, 0], [2, "1623", 458, 496, 22, 23, 0], [2, "1623", 531, 741, 22, 23, 0], [2, "1623", 433, 795, 22, 23, 0], [2, "1561", 791, 671, 20, 15, 0], [2, "1622", 790, 663, 22, 23, 0], [2, "1561", 677, 614, 20, 15, 0], [2, "1622", 676, 606, 22, 23, 0], [2, "1561", 908, 729, 20, 15, 0], [2, "1622", 907, 721, 22, 23, 0], [2, "1561", 1037, 800, 20, 15, 0], [2, "1622", 1036, 792, 22, 23, 0], [2, "1561", 507, 525, 20, 15, 0], [2, "1622", 506, 517, 22, 23, 0], [2, "1561", 406, 474, 20, 15, 0], [2, "1622", 405, 466, 22, 23, 0]]}, {"type": 3, "obj": [[2, "1520", 912, 4, 64, 33, 0], [2, "1520", 862, 28, 64, 33, 0], [2, "1520", 762, 77, 64, 33, 0], [2, "1520", 812, 53, 64, 33, 0], [2, "1520", 714, 101, 64, 33, 0], [2, "1520", 664, 125, 64, 33, 0], [2, "1520", 614, 150, 64, 33, 0], [2, "1520", 564, 174, 64, 33, 0], [2, "1520", 515, 198, 64, 33, 0], [2, "1520", 465, 222, 64, 33, 0], [2, "1520", 365, 271, 64, 33, 0], [2, "1520", 415, 247, 64, 33, 0], [2, "1520", 317, 295, 64, 33, 0], [2, "1520", 267, 319, 64, 33, 0], [2, "1520", 217, 344, 64, 33, 0], [2, "1520", 167, 368, 64, 33, 0], [2, "1520", 119, 392, 64, 33, 0], [2, "1520", 69, 416, 64, 33, 0], [2, "1520", 19, 441, 64, 33, 0], [2, "1520", 831, 164, 64, 33, 0], [2, "1520", 781, 188, 64, 33, 0], [2, "1520", 681, 237, 64, 33, 0], [2, "1520", 731, 213, 64, 33, 0], [2, "1520", 633, 261, 64, 33, 0], [2, "1520", 583, 285, 64, 33, 0], [2, "1520", 533, 310, 64, 33, 0], [2, "1520", 483, 334, 64, 33, 0], [2, "1520", 434, 358, 64, 33, 0], [2, "1520", 384, 382, 64, 33, 0], [2, "1520", 284, 431, 64, 33, 0], [2, "1520", 334, 407, 64, 33, 0], [2, "1520", 236, 455, 64, 33, 0], [2, "1520", 186, 479, 64, 33, 0], [2, "1520", 136, 504, 64, 33, 0], [2, "1520", 86, 528, 64, 33, 0], [2, "1520", 38, 552, 64, 33, 0], [2, "1520", -12, 576, 64, 33, 0], [2, "1520", 1030, 67, 64, 33, 0], [2, "1520", 980, 91, 64, 33, 0], [2, "1520", 880, 140, 64, 33, 0], [2, "1520", 930, 116, 64, 33, 0], [2, "1617", 756, 142, 66, 35, 0], [2, "1617", 789, 126, 66, 35, 0], [2, "1617", 820, 110, 66, 35, 0], [2, "1617", 853, 94, 66, 35, 0], [2, "1617", 884, 79, 66, 35, 0], [2, "1617", 917, 63, 66, 35, 0], [2, "1617", 948, 47, 66, 35, 0], [2, "1617", 981, 31, 66, 35, 0], [2, "1617", 1012, 17, 66, 35, 0], [2, "1617", 437, 299, 66, 35, 0], [2, "1617", 470, 283, 66, 35, 0], [2, "1617", 501, 267, 66, 35, 0], [2, "1617", 534, 251, 66, 35, 0], [2, "1617", 565, 236, 66, 35, 0], [2, "1617", 598, 220, 66, 35, 0], [2, "1617", 629, 204, 66, 35, 0], [2, "1617", 662, 188, 66, 35, 0], [2, "1617", 693, 174, 66, 35, 0], [2, "1617", 726, 158, 66, 35, 0], [2, "1617", 116, 455, 66, 35, 0], [2, "1617", 149, 439, 66, 35, 0], [2, "1617", 180, 423, 66, 35, 0], [2, "1617", 213, 407, 66, 35, 0], [2, "1617", 244, 392, 66, 35, 0], [2, "1617", 277, 376, 66, 35, 0], [2, "1617", 308, 360, 66, 35, 0], [2, "1617", 341, 344, 66, 35, 0], [2, "1617", 372, 330, 66, 35, 0], [2, "1617", 405, 314, 66, 35, 0], [2, "1617", -12, 518, 66, 35, 0], [2, "1617", 21, 502, 66, 35, 0], [2, "1617", 85, 470, 66, 35, 0], [2, "1617", 52, 486, 66, 35, 0], [2, "1617", -45, 535, 66, 35, 0], [2, "1616", 1054, 22, 30, 24, 0], [2, "1616", 1021, 5, 30, 24, 0], [2, "1616", 992, 19, 30, 24, 0], [2, "1616", 966, 32, 30, 24, 0], [2, "1616", 999, 49, 30, 24, 0], [2, "1616", 1025, 36, 30, 24, 0], [2, "1616", 882, 73, 30, 24, 0], [2, "1616", 856, 86, 30, 24, 0], [2, "1616", 889, 103, 30, 24, 0], [2, "1616", 915, 90, 30, 24, 0], [2, "1616", 944, 76, 30, 24, 0], [2, "1616", 911, 59, 30, 24, 0], [2, "1616", 937, 46, 30, 24, 0], [2, "1616", 970, 63, 30, 24, 0], [2, "1616", 772, 127, 30, 24, 0], [2, "1616", 746, 140, 30, 24, 0], [2, "1616", 779, 157, 30, 24, 0], [2, "1616", 805, 144, 30, 24, 0], [2, "1616", 834, 130, 30, 24, 0], [2, "1616", 801, 113, 30, 24, 0], [2, "1616", 827, 100, 30, 24, 0], [2, "1616", 860, 117, 30, 24, 0], [2, "1616", 662, 181, 30, 24, 0], [2, "1616", 636, 194, 30, 24, 0], [2, "1616", 669, 211, 30, 24, 0], [2, "1616", 695, 198, 30, 24, 0], [2, "1616", 724, 184, 30, 24, 0], [2, "1616", 691, 167, 30, 24, 0], [2, "1616", 717, 154, 30, 24, 0], [2, "1616", 750, 171, 30, 24, 0], [2, "1616", 552, 236, 30, 24, 0], [2, "1616", 526, 249, 30, 24, 0], [2, "1616", 559, 266, 30, 24, 0], [2, "1616", 585, 253, 30, 24, 0], [2, "1616", 614, 239, 30, 24, 0], [2, "1616", 581, 222, 30, 24, 0], [2, "1616", 607, 209, 30, 24, 0], [2, "1616", 640, 226, 30, 24, 0], [2, "1616", 442, 290, 30, 24, 0], [2, "1616", 416, 303, 30, 24, 0], [2, "1616", 449, 320, 30, 24, 0], [2, "1616", 475, 307, 30, 24, 0], [2, "1616", 504, 293, 30, 24, 0], [2, "1616", 471, 276, 30, 24, 0], [2, "1616", 497, 263, 30, 24, 0], [2, "1616", 530, 280, 30, 24, 0], [2, "1616", 332, 344, 30, 24, 0], [2, "1616", 306, 357, 30, 24, 0], [2, "1616", 339, 374, 30, 24, 0], [2, "1616", 365, 361, 30, 24, 0], [2, "1616", 394, 347, 30, 24, 0], [2, "1616", 361, 330, 30, 24, 0], [2, "1616", 387, 317, 30, 24, 0], [2, "1616", 420, 334, 30, 24, 0], [2, "1616", 223, 398, 30, 24, 0], [2, "1616", 197, 411, 30, 24, 0], [2, "1616", 230, 428, 30, 24, 0], [2, "1616", 256, 415, 30, 24, 0], [2, "1616", 285, 401, 30, 24, 0], [2, "1616", 252, 384, 30, 24, 0], [2, "1616", 278, 371, 30, 24, 0], [2, "1616", 311, 388, 30, 24, 0], [2, "1616", 113, 452, 30, 24, 0], [2, "1616", 87, 465, 30, 24, 0], [2, "1616", 120, 482, 30, 24, 0], [2, "1616", 146, 469, 30, 24, 0], [2, "1616", 175, 455, 30, 24, 0], [2, "1616", 142, 438, 30, 24, 0], [2, "1616", 168, 425, 30, 24, 0], [2, "1616", 201, 442, 30, 24, 0], [2, "1616", 3, 506, 30, 24, 0], [2, "1616", -23, 519, 30, 24, 0], [2, "1616", 10, 536, 30, 24, 0], [2, "1616", 36, 523, 30, 24, 0], [2, "1616", 65, 509, 30, 24, 0], [2, "1616", 32, 492, 30, 24, 0], [2, "1616", 58, 479, 30, 24, 0], [2, "1616", 91, 496, 30, 24, 0], [2, "1616", -20, 551, 30, 24, 0], [2, "1520", -28, 465, 64, 33, 0], [2, "1530", 1055, 85, 46, 24, 0], [2, "1530", 1031, 96, 46, 24, 0], [2, "1530", 1008, 107, 46, 24, 0], [2, "1530", 941, 140, 46, 24, 0], [2, "1530", 963, 129, 46, 24, 0], [2, "1530", 985, 118, 46, 24, 0], [2, "1530", 805, 206, 46, 24, 0], [2, "1530", 827, 195, 46, 24, 0], [2, "1530", 849, 184, 46, 24, 0], [2, "1530", 872, 173, 46, 24, 0], [2, "1530", 895, 162, 46, 24, 0], [2, "1530", 919, 151, 46, 24, 0], [2, "1530", 670, 272, 46, 24, 0], [2, "1530", 692, 261, 46, 24, 0], [2, "1530", 714, 250, 46, 24, 0], [2, "1530", 737, 239, 46, 24, 0], [2, "1530", 760, 228, 46, 24, 0], [2, "1530", 784, 217, 46, 24, 0], [2, "1530", 534, 338, 46, 24, 0], [2, "1530", 556, 327, 46, 24, 0], [2, "1530", 578, 316, 46, 24, 0], [2, "1530", 601, 305, 46, 24, 0], [2, "1530", 624, 294, 46, 24, 0], [2, "1530", 648, 283, 46, 24, 0], [2, "1530", 399, 404, 46, 24, 0], [2, "1530", 421, 393, 46, 24, 0], [2, "1530", 443, 382, 46, 24, 0], [2, "1530", 466, 371, 46, 24, 0], [2, "1530", 489, 360, 46, 24, 0], [2, "1530", 513, 349, 46, 24, 0], [2, "1530", 265, 470, 46, 24, 0], [2, "1530", 287, 459, 46, 24, 0], [2, "1530", 309, 448, 46, 24, 0], [2, "1530", 332, 437, 46, 24, 0], [2, "1530", 355, 426, 46, 24, 0], [2, "1530", 379, 415, 46, 24, 0], [2, "1530", 131, 535, 46, 24, 0], [2, "1530", 153, 524, 46, 24, 0], [2, "1530", 175, 513, 46, 24, 0], [2, "1530", 198, 502, 46, 24, 0], [2, "1530", 221, 491, 46, 24, 0], [2, "1530", 245, 480, 46, 24, 0], [2, "1530", -3, 601, 46, 24, 0], [2, "1530", 19, 590, 46, 24, 0], [2, "1530", 41, 579, 46, 24, 0], [2, "1530", 64, 568, 46, 24, 0], [2, "1530", 87, 557, 46, 24, 0], [2, "1530", 111, 546, 46, 24, 0], [2, "1530", -23, 611, 46, 24, 0], [2, "1530", -45, 622, 46, 24, 0], [2, "1530", 786, 55, 46, 24, 0], [2, "1530", 808, 44, 46, 24, 0], [2, "1530", 850, 23, 46, 24, 0], [2, "1530", 828, 34, 46, 24, 0], [2, "1530", 872, 12, 46, 24, 0], [2, "1530", 918, -10, 46, 24, 0], [2, "1530", 895, 1, 46, 24, 0], [2, "1530", 630, 131, 46, 24, 0], [2, "1530", 652, 120, 46, 24, 0], [2, "1530", 694, 99, 46, 24, 0], [2, "1530", 672, 110, 46, 24, 0], [2, "1530", 716, 88, 46, 24, 0], [2, "1530", 762, 66, 46, 24, 0], [2, "1530", 739, 77, 46, 24, 0], [2, "1530", 476, 206, 46, 24, 0], [2, "1530", 498, 195, 46, 24, 0], [2, "1530", 540, 174, 46, 24, 0], [2, "1530", 518, 185, 46, 24, 0], [2, "1530", 562, 163, 46, 24, 0], [2, "1530", 608, 141, 46, 24, 0], [2, "1530", 585, 152, 46, 24, 0], [2, "1530", 321, 282, 46, 24, 0], [2, "1530", 343, 271, 46, 24, 0], [2, "1530", 385, 250, 46, 24, 0], [2, "1530", 363, 261, 46, 24, 0], [2, "1530", 407, 239, 46, 24, 0], [2, "1530", 453, 217, 46, 24, 0], [2, "1530", 430, 228, 46, 24, 0], [2, "1530", 166, 358, 46, 24, 0], [2, "1530", 188, 347, 46, 24, 0], [2, "1530", 230, 326, 46, 24, 0], [2, "1530", 208, 337, 46, 24, 0], [2, "1530", 252, 315, 46, 24, 0], [2, "1530", 298, 293, 46, 24, 0], [2, "1530", 275, 304, 46, 24, 0], [2, "1530", 10, 434, 46, 24, 0], [2, "1530", 32, 423, 46, 24, 0], [2, "1530", 74, 402, 46, 24, 0], [2, "1530", 52, 413, 46, 24, 0], [2, "1530", 96, 391, 46, 24, 0], [2, "1530", 142, 369, 46, 24, 0], [2, "1530", 119, 380, 46, 24, 0], [2, "1530", -12, 445, 46, 24, 0], [2, "1530", -34, 456, 46, 24, 0], [2, "1536", 1061, 106, 36, 20, 0], [2, "1536", 1039, 117, 36, 20, 0], [2, "1536", 994, 139, 36, 20, 0], [2, "1536", 1016, 128, 36, 20, 0], [2, "1536", 904, 182, 36, 20, 0], [2, "1536", 926, 171, 36, 20, 0], [2, "1536", 949, 160, 36, 20, 0], [2, "1536", 971, 149, 36, 20, 0], [2, "1536", 814, 226, 36, 20, 0], [2, "1536", 836, 215, 36, 20, 0], [2, "1536", 859, 204, 36, 20, 0], [2, "1536", 881, 193, 36, 20, 0], [2, "1536", 725, 270, 36, 20, 0], [2, "1536", 747, 259, 36, 20, 0], [2, "1536", 770, 248, 36, 20, 0], [2, "1536", 792, 237, 36, 20, 0], [2, "1536", 636, 313, 36, 20, 0], [2, "1536", 658, 302, 36, 20, 0], [2, "1536", 681, 291, 36, 20, 0], [2, "1536", 703, 280, 36, 20, 0], [2, "1536", 546, 356, 36, 20, 0], [2, "1536", 568, 345, 36, 20, 0], [2, "1536", 591, 334, 36, 20, 0], [2, "1536", 613, 323, 36, 20, 0], [2, "1536", 457, 400, 36, 20, 0], [2, "1536", 479, 389, 36, 20, 0], [2, "1536", 502, 378, 36, 20, 0], [2, "1536", 524, 367, 36, 20, 0], [2, "1536", 366, 444, 36, 20, 0], [2, "1536", 388, 433, 36, 20, 0], [2, "1536", 411, 422, 36, 20, 0], [2, "1536", 433, 411, 36, 20, 0], [2, "1536", 277, 488, 36, 20, 0], [2, "1536", 299, 477, 36, 20, 0], [2, "1536", 322, 466, 36, 20, 0], [2, "1536", 344, 455, 36, 20, 0], [2, "1536", 188, 532, 36, 20, 0], [2, "1536", 210, 521, 36, 20, 0], [2, "1536", 233, 510, 36, 20, 0], [2, "1536", 255, 499, 36, 20, 0], [2, "1536", 97, 576, 36, 20, 0], [2, "1536", 119, 565, 36, 20, 0], [2, "1536", 142, 554, 36, 20, 0], [2, "1536", 164, 543, 36, 20, 0], [2, "1536", 97, 576, 36, 20, 0], [2, "1536", 119, 565, 36, 20, 0], [2, "1536", 142, 554, 36, 20, 0], [2, "1536", 164, 543, 36, 20, 0], [2, "1536", 7, 621, 36, 20, 0], [2, "1536", 29, 610, 36, 20, 0], [2, "1536", 52, 599, 36, 20, 0], [2, "1536", 74, 588, 36, 20, 0], [2, "1536", -15, 631, 36, 20, 0], [2, "1536", 613, 123, 36, 20, 0], [2, "1536", 635, 113, 36, 20, 0], [2, "1536", 680, 91, 36, 20, 0], [2, "1536", 657, 102, 36, 20, 0], [2, "1536", 702, 80, 36, 20, 0], [2, "1536", 725, 68, 36, 20, 0], [2, "1536", 747, 57, 36, 20, 0], [2, "1536", 770, 46, 36, 20, 0], [2, "1536", 792, 35, 36, 20, 0], [2, "1536", 816, 24, 36, 20, 0], [2, "1536", 838, 13, 36, 20, 0], [2, "1536", 861, 2, 36, 20, 0], [2, "1536", 883, -9, 36, 20, 0], [2, "1536", 319, 267, 36, 20, 0], [2, "1536", 341, 256, 36, 20, 0], [2, "1536", 386, 234, 36, 20, 0], [2, "1536", 363, 245, 36, 20, 0], [2, "1536", 408, 223, 36, 20, 0], [2, "1536", 431, 212, 36, 20, 0], [2, "1536", 453, 201, 36, 20, 0], [2, "1536", 476, 190, 36, 20, 0], [2, "1536", 498, 179, 36, 20, 0], [2, "1536", 522, 168, 36, 20, 0], [2, "1536", 544, 156, 36, 20, 0], [2, "1536", 567, 145, 36, 20, 0], [2, "1536", 589, 134, 36, 20, 0], [2, "1536", 26, 410, 36, 20, 0], [2, "1536", 48, 400, 36, 20, 0], [2, "1536", 93, 378, 36, 20, 0], [2, "1536", 70, 389, 36, 20, 0], [2, "1536", 115, 367, 36, 20, 0], [2, "1536", 138, 356, 36, 20, 0], [2, "1536", 160, 345, 36, 20, 0], [2, "1536", 183, 333, 36, 20, 0], [2, "1536", 205, 322, 36, 20, 0], [2, "1536", 229, 311, 36, 20, 0], [2, "1536", 251, 300, 36, 20, 0], [2, "1536", 274, 289, 36, 20, 0], [2, "1536", 296, 278, 36, 20, 0], [2, "1536", -19, 432, 36, 20, 0], [2, "1536", -41, 443, 36, 20, 0], [2, "1536", 3, 421, 36, 20, 0], [2, "1547", 188, 163, 82, 38, 0], [2, "1547", 271, 163, 82, 38, 2], [2, "1547", 271, 125, 82, 38, 3], [2, "1547", 188, 125, 82, 38, 1], [2, "163", 69, 106, 60, 33, 0], [2, "163", 21, 130, 60, 33, 0], [2, "163", -27, 153, 60, 33, 0], [2, "163", 287, 0, 60, 33, 0], [2, "163", 239, 24, 60, 33, 0], [2, "163", 191, 47, 60, 33, 0], [2, "163", -16, 264, 60, 33, 0], [2, "163", 34, 240, 60, 33, 0], [2, "163", 59, 227, 60, 33, 0], [2, "163", 59, 211, 60, 33, 2], [2, "163", 36, 378, 60, 33, 0], [2, "163", -14, 402, 60, 33, 0], [2, "163", 133, 331, 60, 33, 0], [2, "163", 83, 355, 60, 33, 0], [2, "163", 232, 283, 60, 33, 0], [2, "163", 182, 307, 60, 33, 0], [2, "1528", 132, 536, 44, 24, 0], [2, "1528", 460, 211, 44, 24, 0], [2, "1528", 715, 88, 44, 24, 0], [2, "1528", 878, 9, 44, 24, 0], [2, "1529", 466, 372, 44, 24, 0], [2, "1529", 443, 383, 44, 24, 0], [2, "1529", 153, 525, 44, 24, 0], [2, "1529", 188, 348, 44, 24, 0], [2, "1528", 460, 211, 44, 24, 0], [2, "1528", 9, 435, 44, 24, 0], [2, "1528", 1012, 106, 44, 24, 0], [2, "163", 281, 259, 60, 33, 0], [2, "163", 331, 235, 60, 33, 0], [2, "163", 378, 212, 60, 33, 0], [2, "163", 428, 187, 60, 33, 0], [2, "163", 477, 163, 60, 33, 0], [2, "163", 527, 139, 60, 33, 0], [2, "1520", 797, 255, 64, 33, 2], [2, "1520", 847, 280, 64, 33, 2], [2, "1520", 896, 305, 64, 33, 2], [2, "1520", 1044, 380, 64, 33, 2], [2, "1520", 995, 355, 64, 33, 2], [2, "1520", 945, 330, 64, 33, 2], [2, "1536", 780, 262, 36, 20, 2], [2, "1536", 802, 273, 36, 20, 2], [2, "1536", 846, 295, 36, 20, 2], [2, "1536", 824, 284, 36, 20, 2], [2, "1536", 934, 339, 36, 20, 2], [2, "1536", 912, 328, 36, 20, 2], [2, "1536", 890, 317, 36, 20, 2], [2, "1536", 868, 306, 36, 20, 2], [2, "1536", 1022, 384, 36, 20, 2], [2, "1536", 1000, 373, 36, 20, 2], [2, "1536", 978, 362, 36, 20, 2], [2, "1536", 956, 351, 36, 20, 2], [2, "1536", 1066, 406, 36, 20, 2], [2, "1536", 1044, 395, 36, 20, 2], [2, "1520", 536, 100, 64, 33, 2], [2, "1520", 487, 75, 64, 33, 2], [2, "1520", 437, 50, 64, 33, 2], [2, "1536", 541, 119, 36, 20, 2], [2, "1536", 519, 108, 36, 20, 2], [2, "1536", 497, 97, 36, 20, 2], [2, "1536", 475, 86, 36, 20, 2], [2, "1536", 563, 130, 36, 20, 2], [2, "1520", 339, 0, 64, 33, 2], [2, "1520", 388, 25, 64, 33, 2], [2, "1536", 365, 30, 36, 20, 2], [2, "1536", 453, 74, 36, 20, 2], [2, "1536", 431, 63, 36, 20, 2], [2, "1536", 409, 52, 36, 20, 2], [2, "1536", 387, 41, 36, 20, 2], [2, "1520", 556, 110, 64, 33, 2], [2, "1536", 320, 7, 36, 20, 2], [2, "1536", 342, 18, 36, 20, 2], [2, "1536", 298, -4, 36, 20, 2], [2, "1520", 291, -25, 64, 33, 2], [2, "163", 311, 19, 60, 33, 2], [2, "163", 359, 43, 60, 33, 2], [2, "163", 455, 91, 60, 33, 2], [2, "163", 407, 67, 60, 33, 2], [2, "163", 503, 115, 60, 33, 2], [2, "163", 443, 663, 60, 33, 2], [2, "163", 717, 790, 60, 33, 2], [2, "163", 669, 766, 60, 33, 2], [2, "163", 766, 814, 60, 33, 2], [2, "163", 511, 393, 60, 33, 2], [2, "163", 559, 417, 60, 33, 2], [2, "163", 607, 441, 60, 33, 2], [2, "163", 655, 465, 60, 33, 2], [2, "163", 703, 489, 60, 33, 2], [2, "163", 751, 513, 60, 33, 2], [2, "163", 800, 537, 60, 33, 2], [2, "163", 849, 561, 60, 33, 2], [2, "163", 897, 585, 60, 33, 2], [2, "163", 945, 609, 60, 33, 2], [2, "163", 993, 633, 60, 33, 2], [2, "163", 1041, 657, 60, 33, 2], [2, "163", 398, 640, 60, 33, 2], [2, "163", 350, 616, 60, 33, 2], [2, "163", 302, 592, 60, 33, 2], [2, "163", 253, 568, 60, 33, 2], [2, "163", 204, 544, 60, 33, 2], [2, "163", 156, 551, 60, 33, 0], [2, "163", 108, 575, 60, 33, 0], [2, "163", 12, 622, 60, 33, 0], [2, "163", 60, 598, 60, 33, 0], [2, "163", -36, 645, 60, 33, 0], [2, "163", 413, 425, 60, 33, 0], [2, "163", 365, 449, 60, 33, 0], [2, "163", 317, 472, 60, 33, 0], [2, "163", 269, 496, 60, 33, 0], [2, "163", 221, 519, 60, 33, 0], [2, "163", 657, 305, 60, 33, 0], [2, "163", 606, 330, 60, 33, 0], [2, "163", 558, 353, 60, 33, 0], [2, "163", 510, 377, 60, 33, 0], [2, "163", 462, 400, 60, 33, 0], [2, "163", 730, 268, 60, 33, 0], [2, "163", 682, 292, 60, 33, 0], [2, "163", 457, 689, 60, 33, 0], [2, "163", 408, 713, 60, 33, 0], [2, "163", 360, 737, 60, 33, 0], [2, "163", 312, 761, 60, 33, 0], [2, "163", 263, 785, 60, 33, 0], [2, "163", 166, 833, 60, 33, 0], [2, "163", 215, 809, 60, 33, 0], [2, "163", 627, 770, 60, 33, 0], [2, "163", 530, 818, 60, 33, 0], [2, "163", 578, 794, 60, 33, 0], [2, "163", 778, 276, 60, 33, 2], [2, "163", 826, 300, 60, 33, 2], [2, "163", 873, 323, 60, 33, 2], [2, "163", 921, 347, 60, 33, 2], [2, "163", 1016, 395, 60, 33, 2], [2, "163", 968, 371, 60, 33, 2], [2, "163", 1063, 419, 60, 33, 2], [2, "1536", 879, 558, 36, 20, 0], [2, "1536", 900, 547, 36, 20, 0], [2, "1536", 944, 525, 36, 20, 0], [2, "1536", 923, 536, 36, 20, 0], [2, "1536", 967, 513, 36, 20, 0], [2, "1536", 989, 502, 36, 20, 0], [2, "1536", 1011, 490, 36, 20, 0], [2, "1536", 1033, 479, 36, 20, 0], [2, "1536", 1056, 467, 36, 20, 0], [2, "1530", 944, 499, 46, 24, 0], [2, "1530", 967, 488, 46, 24, 0], [2, "1530", 989, 476, 46, 24, 0], [2, "1530", 1056, 442, 46, 24, 0], [2, "1530", 1011, 465, 46, 24, 0], [2, "1530", 1034, 454, 46, 24, 0], [2, "163", 1022, 431, 60, 33, 0], [2, "163", 974, 455, 60, 33, 0], [2, "163", 927, 479, 60, 33, 0], [2, "1526", 287, 629, 30, 29, 0], [2, "1536", 359, 734, 36, 20, 0], [2, "1536", 337, 745, 36, 20, 0], [2, "1536", 314, 756, 36, 20, 0], [2, "1536", 292, 767, 36, 20, 0], [2, "1536", 268, 778, 36, 20, 0], [2, "1536", 246, 789, 36, 20, 0], [2, "1536", 224, 800, 36, 20, 0], [2, "1536", 202, 811, 36, 20, 0], [2, "1536", 178, 822, 36, 20, 0], [2, "1536", 156, 834, 36, 20, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, -1, 100, 98, 102, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 86, 86, 86, 86, 86, 86, 98, 100, 98, 98, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 100, 101, 102, -1, -1, -1, 84, 83, -1, -1, -1, 107, 106, 105, 86, 86, 86, 100, 101, 102, 96, 95, 95, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 106, 105, 93, -1, -1, -1, 107, 106, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 98, 98, 98, -1, -1, -1, 110, 111, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 98, 98, 98, 98, -1, -1, -1, -1, 110, 111, 112, -1, -1, -1, -1, 84, 115, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 98, 98, 98, -1, 98, 98, 98, 98, 98, -1, -1, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 107, 106, 105, 98, 98, 98, 100, 101, 95, 106, 98, 98, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 107, 96, 95, 95, 101, 102, -1, -1, -1, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 84, 83, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 86, 86, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 95, 94, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 100, 101, 102, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 116, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 86, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 86, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 100, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 0, 1, 1, 1, 1, 1, 1, 120, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 121, 71, 67, 68, 69, 121, 120, 120, 121, 120, 121, 120, 121, 120, 121, 120, 120, 121, 120, 121, 121, 121, 120, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 121, 120, 71, 67, 68, 70, 67, 68, 69, 120, 120, 121, 121, 120, 121, 120, 121, 121, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 121, 120, 67, 68, 70, 67, 68, 69, 71, 72, 71, 120, 121, 121, 121, 121, 120, 121, 121, 121, 121, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 121, 120, 121, 67, 68, 70, 67, 68, 69, 71, 72, 74, 75, 71, 120, 121, 121, 120, 121, 121, 121, 121, 121, 121, 121, 120, 121, 120, 120, 121, 120, 121, 120, 120, 121, 120, 120, 0, 1, 1, 0, 1, 1, 121, 120, 121, 67, 68, 70, 67, 68, 69, 71, 72, 74, 75, 71, 121, 121, 120, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 120, 120, 121, 120, 0, 1, 1, 121, 121, 120, 67, 68, 70, 67, 68, 69, 71, 72, 74, 75, 121, 121, 121, 120, 0, 120, 121, 121, 120, 121, 120, 121, 121, 121, 121, 121, 120, 121, 120, 120, 121, 120, 120, 121, 120, 121, 120, 120, 121, 120, 121, 120, 121, 67, 68, 69, 67, 68, 69, 71, 72, 74, 75, 120, 121, 120, 121, 121, 0, 0, 120, 121, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 70, 71, 72, 70, 71, 72, 74, 75, 121, 120, 121, 120, 0, 1, 1, 0, 1, 120, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 71, 121, 120, 67, 68, 73, 74, 75, 73, 74, 75, 120, 120, 121, 120, 121, 0, 1, 1, 0, 1, 0, 120, 121, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 121, 120, 121, 120, 71, 67, 68, 69, 67, 70, 71, 73, 74, 75, 71, 121, 120, 120, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 120, 121, 120, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 121, 120, 71, 67, 68, 75, 71, 72, 70, 73, 74, 75, 71, 120, 121, 120, 121, 0, 1, 1, 120, 0, 0, 1, 0, 1, 0, 1, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 121, 121, 67, 68, 69, 70, 71, 73, 74, 75, 73, 74, 75, 121, 120, 121, 121, 121, 120, 121, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 120, 121, 120, 120, 120, 121, 120, 121, 121, 120, 121, 121, 121, 120, 121, 71, 67, 68, 69, 72, 73, 74, 73, 74, 75, 71, 121, 120, 121, 120, 120, 121, 120, 120, 121, 120, 0, 1, 1, 0, 1, 1, 1, 0, 1, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 120, 121, 71, 67, 68, 70, 71, 72, 75, 73, 74, 75, 71, 121, 120, 121, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 121, 0, 1, 1, 0, 1, 0, 1, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 121, 75, 67, 68, 69, 69, 73, 74, 75, 71, 71, 71, 121, 121, 120, 120, 121, 120, 121, 120, 120, 120, 121, 120, 121, 120, 121, 120, 121, 121, 0, 1, 1, 1, 1, 120, 121, 120, 120, 120, 120, 121, 121, 120, 75, 67, 68, 69, 71, 72, 72, 75, 72, 71, 71, 121, 120, 121, 120, 120, 121, 121, 120, 121, 121, 121, 120, 121, 121, 120, 121, 120, 121, 121, 121, 120, 121, 0, 1, 1, 120, 121, 120, 121, 120, 121, 120, 75, 67, 68, 69, 68, 69, 74, 75, 75, 75, 71, 121, 120, 121, 120, 121, 120, 121, 120, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 121, 121, 120, 121, 120, 120, 0, 120, 121, 120, 120, 121, 75, 67, 68, 67, 68, 69, 71, 72, 75, 75, 71, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 120, 120, 121, 120, 121, 120, 120, 120, 120, 121, 120, 75, 67, 68, 70, 71, 70, 71, 72, 74, 75, 75, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 121, 120, 120, 120, 120, 75, 67, 68, 70, 71, 73, 74, 73, 74, 75, 75, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 120, 120, 121, 120, 121, 120, 120, 120, 67, 68, 70, 71, 73, 74, 75, 74, 75, 75, 120, 121, 120, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 120, 121, 121, 120, 21, 22, 70, 71, 73, 74, 75, 75, 75, 75, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 120, 121, 120, 121, 120, 120, 121, 121, 120, 121, 120, 121, 120, 121, 21, 22, 21, 22, 73, 74, 75, 75, 75, 75, 121, 120, 121, 120, 121, 121, 121, 120, 121, 121, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 121, 120, 121, 120, 121, 121, 121, 21, 22, 21, 22, 21, 22, 73, 74, 75, 75, 121, 120, 121, 0, 1, 120, 121, 121, 120, 121, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 21, 22, 21, 22, 21, 22, 21, 22, 75, 75, 121, 120, 121, 120, 0, 0, 1, 0, 0, 1, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 22, 21, 22, 21, 22, 21, 22, 120, 121, 121, 120, 0, 1, 0, 0, 0, 0, 0, 0, 1, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 121, 120, 120, 120, 121, 22, 21, 22, 22, 121, 120, 17, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 121, 120, 120, 120, 121, 120, 121, 22, 22, 121, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 120, 121, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 121, 120, 121, 120, 120, 121, 120, 120, 121, 121, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 120, 120, 121, 121, 121, 121, 121, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 120, 121, 121, 121, 120, 121, 121, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 1, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 120, 120, 121, 120, 121, 121, 120, 121, 120, 121, 121, 0, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 120, 121, 121, 120, 121, 120, 120, 121, 120, 120, 121, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, -109, 120, 121, 120, 120, 121, 121, 120, 121, 121, 120, 121, 120, 120, 121, 120, 120, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, -109, -109, -109, -109, -109, 120, 121, 120, 121, 120, 121, 120, 120, 121, 120, 120, 121, 121, 121, 0, 1, 0, 0, 0, 1, 0, 0, 1, 120, 121, 121, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 120, -109, -109, -109, -109, -109, -109, -109, -109, -109, 120, 121, 120, 121, 120, 28, 120, 120, 121, 120, 121, 121]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0]}