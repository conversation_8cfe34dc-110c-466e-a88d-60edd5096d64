{"mW": 888, "mH": 672, "tW": 24, "tH": 24, "tiles": [["315_4", 0, 3, 3], ["1314", 0, 3, 2], ["1314", 2, 3, 2], ["1314", 1, 3, 2], ["1314", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2]], "layers": [{"type": 2, "data": [62, 62, 62, 62, 62, 86, 86, 62, 62, 62, 62, 79, 88, 87, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 86, 62, 79, 76, 84, 74, 88, 87, 62, 62, 62, 62, 62, 62, 62, 62, 82, 82, -1, 88, 83, 82, 81, -1, 83, 87, 62, 62, 62, 62, 62, 62, 62, 62, 62, 84, 74, 82, 74, 75, -1, -1, -1, -1, 83, 88, 87, 62, 62, 62, 62, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 87, 76, 62, 62, 62, 62, 79, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, -1, 77, 62, 62, 84, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 62, 62, 79, 75, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 79, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 74, 82, 75, -1, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 60, 68, 67, 66, 66, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 62, 62, 62, 62, 68, 66, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 64, 64, 64, 58, 59, -1, -1, -1, -1, -1, -1, 77, 62, 62, 62, 62, 84, 76, 78, 84, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 87, 62, 62, 62, 63, 58, 66, 65, -1, -1, -1, 61, 78, 86, 62, 79, 81, 83, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 71, 62, 79, 87, 62, 62, 70, 69, -1, -1, -1, 83, 74, 87, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 71, 62, 62, 85, 83, 74, 80, 74, 75, -1, -1, -1, -1, -1, 77, 84, 81, -1, -1, -1, -1, -1, -1, -1, -1, 67, 58, 59, -1, -1, -1, -1, -1, 67, 66, 66, 60, 62, 62, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 68, 59, -1, -1, -1, -1, 67, 60, 68, 58, 71, 62, 63, 65, -1, -1, -1, 57, 71, 62, 62, 62, 62, 62, 75, -1, -1, -1, -1, -1, -1, -1, 57, 58, 72, 71, 62, 62, 68, 66, 65, 57, 58, 71, 70, 62, 62, 62, 62, 62, 69, -1, -1, -1, 73, 87, 70, 78, 84, 80, 82, -1, -1, -1, -1, -1, -1, -1, -1, 61, 78, 62, 62, 62, 62, 62, 62, 62, 62, 78, 70, 79, 76, 84, 74, 82, 74, 75, -1, 57, 58, 66, 71, 79, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 74, 76, 62, 62, 62, 62, 84, 76, 78, 79, 74, 75, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 62, 68, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 62, 79, 81, 73, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 74, 87, 62, 62, 63, 66, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 60, 62, 62, 62, 85, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 71, 62, 62, 62, 62, 62, 63, 59, -1, -1, -1, -1, -1, -1, -1, 77, 78, 62, 62, 62, 84, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 62, 78, 79, 80, 80, 87, 78, 85, -1, -1, -1, -1, -1, 57, 58, 60, 62, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 74, 75, -1, -1, 83, 82, 81, -1, -1, -1, -1, -1, 61, 62, 62, 62, 62, 62, 62, 63, 66, 66, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 80, 82, 87, 86, 70, 70, 62, 62, 62, 63, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 80, 80, 87, 79, 74, 74, 82, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 66, 72, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 87, 70, 70, 79, 75, -1, -1, -1, -1, -1, 57, 66, 65, 67, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 63, -1, -1, 67, 66, 65, -1, 57, 65, -1, 67, 71, 62, 79, 75, -1, -1, -1, 57, 58, 59, 61, 70, 68, 60, 63, 66, 59, 57, 65, -1, -1, -1, -1, 67, 66, 60, 70, 63, 58, 71, 70, 68, 58, 60, 68, 58, 71, 62, 62, 63, 66, 66, 65, -1, 61, 62, 63, 71, 70, 70, 62, 70, 70, 63, 60, 68, 66, 64, 64, 64, 71, 70, 70]}, {"type": 3, "obj": [[2, "1310_1", 546, 191, 18, 29, 0], [2, "1310_1", 822, 167, 18, 29, 2], [2, "1310_1", 716, 238, 18, 29, 0], [2, "1308_1", 778, 323, 22, 37, 0], [2, "1308_1", 736, 466, 22, 37, 0], [2, "1310_1", 371, 382, 18, 29, 0], [2, "1310_1", 562, 252, 18, 29, 2], [2, "1309_1", 650, 16, 20, 32, 0], [2, "1310_1", 806, 7, 18, 29, 0], [2, "1310_1", 131, 190, 18, 29, 2], [2, "1310_1", 480, 432, 18, 29, 2], [2, "1309_1", 201, 20, 20, 32, 0], [2, "1308_1", 98, 29, 22, 37, 0], [2, "1308_1", 335, 26, 22, 37, 0], [2, "1310_1", 385, 444, 18, 29, 0], [2, "1310_1", 309, 330, 18, 29, 0], [2, "1308_1", 296, 330, 22, 37, 0], [2, "1308_1", 178, 335, 22, 37, 0], [2, "1310_1", 842, 499, 18, 29, 0]]}, {"type": 4, "obj": []}, {"type": 3, "obj": [[2, "1312_1", 516, 326, 36, 78, 0], [2, "1306", 84, 530, 30, 29, 0], [2, "1312_1", 324, 612, 36, 78, 0], [2, "1312_1", 782, 489, 36, 78, 2], [2, "1311_1", 467, 333, 44, 81, 0], [2, "1311_1", 513, 67, 44, 81, 0], [2, "1306", 804, 235, 30, 29, 2], [2, "1311_1", 813, 29, 44, 81, 2], [2, "1312_1", 682, -2, 36, 78, 0], [2, "1311_1", 651, 13, 44, 81, 0], [2, "1311_1", 480, 99, 44, 81, 0], [2, "1311_1", 37, 374, 44, 81, 0], [2, "1312_1", 143, 351, 36, 78, 0], [2, "1311_1", 112, 368, 44, 81, 0], [2, "1311_1", 363, 23, 44, 81, 0], [2, "1311_1", 298, 7, 44, 81, 0], [2, "1312_1", 270, 14, 36, 78, 0], [2, "1311_1", 171, 7, 44, 81, 2], [2, "1309_1", 172, 5, 20, 32, 2], [2, "1308_1", 154, 2, 22, 37, 0], [2, "1311_1", 201, 22, 44, 81, 2], [2, "1311_1", 237, 25, 44, 81, 0], [2, "212_1", 322, 21, 44, 99, 0], [2, "1306", 218, 74, 30, 29, 2], [2, "181_1", 85, 13, 104, 100, 2], [2, "212_1", 91, 30, 44, 99, 2], [2, "1311_1", 61, 55, 44, 81, 0], [2, "1312_1", 28, 61, 36, 78, 2], [2, "1311_1", 396, 33, 44, 81, 2], [2, "955_1", 117, 138, 20, 18, 2], [2, "96_1", 104, 96, 18, 37, 2], [2, "95_1", 126, 87, 22, 33, 2], [2, "1306", 62, 107, 30, 29, 0], [2, "954_1", 145, 255, 24, 25, 0], [2, "1311_1", 94, 201, 44, 81, 0], [2, "1312_1", 63, 203, 36, 78, 2], [2, "1311_1", 37, 205, 44, 81, 0], [2, "1312_1", 11, 226, 36, 78, 0], [2, "1302", 50, 259, 40, 29, 0], [2, "1311_1", 259, 345, 44, 81, 0], [2, "1311_1", 186, 339, 44, 81, 2], [2, "212_1", 170, 343, 44, 99, 2], [2, "955_1", 207, 413, 20, 18, 2], [2, "212_1", 93, 372, 44, 99, 2], [2, "366_2", 144, 405, 32, 48, 0], [2, "1311_1", 58, 372, 44, 81, 2], [2, "1312_1", 12, 439, 36, 78, 0], [2, "1312_1", 77, 516, 36, 78, 0], [2, "1312_1", 55, 528, 36, 78, 0], [2, "1311_1", 23, 524, 44, 81, 2], [2, "212_1", -1, 515, 44, 99, 2], [2, "1301", 108, 546, 24, 49, 2], [2, "1302", 87, 572, 40, 29, 2], [2, "1311_1", 679, 242, 44, 81, 0], [2, "1310_1", 281, 289, 18, 29, 0], [2, "212_1", 652, 241, 44, 99, 2], [2, "1310_1", 443, 312, 18, 29, 2], [2, "955_1", 432, 333, 20, 18, 2], [2, "95_1", 405, 150, 22, 33, 0], [2, "364_2", 382, 77, 44, 64, 2], [2, "1311_1", 219, 349, 44, 81, 2], [2, "1306", 280, 67, 30, 29, 0], [2, "1311_1", 617, 225, 44, 81, 2], [2, "1311_1", 594, 221, 44, 81, 0], [2, "1311_1", 793, 331, 44, 81, 2], [2, "212_1", 563, 233, 44, 99, 2], [2, "1311_1", 428, 74, 44, 81, 2], [2, "1312_1", 435, 112, 36, 78, 2], [2, "212_1", 455, 98, 44, 99, 2], [2, "954_1", 434, 176, 24, 25, 0], [2, "1302", 339, 103, 40, 29, 0], [2, "1303", 341, 131, 34, 20, 0], [2, "1305", 333, 150, 20, 14, 0], [2, "1311_1", 560, 31, 44, 81, 0], [2, "1311_1", 585, 28, 44, 81, 2], [2, "212_1", 613, 9, 44, 99, 0], [2, "1306", 681, 59, 30, 29, 0], [2, "1312_1", 700, -5, 36, 78, 2], [2, "1311_1", 725, 1, 44, 81, 2], [2, "1311_1", 759, 7, 44, 81, 0], [2, "1302", 714, 63, 40, 29, 2], [2, "1303", 702, 85, 34, 20, 0], [2, "1305", 672, 121, 20, 14, 0], [2, "1308_1", 852, 46, 22, 37, 0], [2, "1312_1", 820, 212, 36, 78, 2], [2, "1311_1", 842, 221, 44, 81, 2], [2, "1311_1", 531, 255, 44, 81, 0], [2, "1312_1", 540, 306, 36, 78, 0], [2, "212_1", 491, 312, 44, 99, 2], [2, "1311_1", 750, 486, 44, 81, 2], [2, "1308_1", 772, 472, 22, 37, 0], [2, "1310_1", 357, 596, 18, 29, 0], [2, "1310_1", 257, 602, 18, 29, 0], [2, "1312_1", 259, 617, 36, 78, 2], [2, "1302", 829, 599, 40, 29, 2], [2, "1303", 815, 617, 34, 20, 0], [2, "955_1", 632, 581, 20, 18, 0], [2, "954_1", 824, 93, 24, 25, 0], [2, "1302", 584, 349, 40, 29, 2], [2, "1303", 572, 371, 34, 20, 0], [2, "1303", 560, 338, 34, 20, 0], [2, "1305", 561, 388, 20, 14, 0], [2, "1301", 516, 115, 24, 49, 0], [2, "954_1", 84, 634, 24, 25, 2], [2, "1303", 67, 600, 34, 20, 0], [2, "955_1", 424, 641, 20, 18, 2], [2, "1305", 68, 307, 20, 14, 0], [2, "1303", 76, 292, 34, 20, 0], [2, "1302", 46, 438, 40, 29, 0], [2, "1303", 68, 457, 34, 20, 0], [2, "1305", 53, 475, 20, 14, 0], [2, "1303", 191, 572, 34, 20, 0], [2, "1305", 628, 319, 20, 14, 0], [2, "955_1", 691, 330, 20, 18, 2], [2, "955_1", 747, 100, 20, 18, 2], [2, "955_1", 486, 179, 20, 18, 2], [2, "955_1", 311, 396, 20, 18, 2], [2, "1303", 181, 83, 34, 20, 0], [2, "1303", 127, 445, 34, 20, 0], [2, "1303", 247, 645, 34, 20, 0], [2, "1306", 749, 534, 30, 29, 2], [2, "96_1", 417, 151, 18, 37, 0], [2, "1305", 762, 564, 20, 14, 0], [2, "1305", 272, 243, 20, 14, 2], [2, "411_2", 351, 148, 44, 40, 2], [2, "1302", 818, 114, 40, 29, 0], [2, "1301", 751, 41, 24, 49, 2], [2, "1304", 770, 88, 22, 19, 0], [2, "1304", 410, 187, 22, 19, 0], [2, "1311_1", 545, 450, 44, 81, 0], [2, "1311_1", 503, 432, 44, 81, 2], [2, "1311_1", 221, 325, 44, 81, 2], [2, "1311_1", 262, 331, 44, 81, 0], [2, "1312_1", 2, 93, 36, 78, 0], [2, "1312_1", 266, -14, 36, 78, 0], [2, "1311_1", 289, -26, 44, 81, 0], [2, "1311_1", 849, 34, 44, 81, 2], [2, "1311_1", 844, 78, 44, 81, 2], [2, "1311_1", 533, 51, 44, 81, 0], [2, "1311_1", 759, -22, 44, 81, 0], [2, "212_1", 783, 4, 44, 99, 0], [2, "1311_1", 618, 249, 44, 81, 2], [2, "366_2", 597, 284, 32, 48, 2], [2, "1301", 586, 301, 24, 49, 0], [2, "954_1", 796, 649, 24, 25, 0], [2, "181_1", 784, 495, 104, 100, 0], [2, "1301", 854, 562, 24, 49, 2], [2, "1311_1", 484, 427, 44, 81, 0], [2, "212_1", 457, 428, 44, 99, 2], [2, "1311_1", 429, 452, 44, 81, 0], [2, "1311_1", 388, 452, 44, 81, 2], [2, "1301", 369, 477, 24, 49, 0], [2, "954_1", 510, 500, 24, 25, 0], [2, "1311_1", 383, 382, 44, 81, 2], [2, "1306", 557, 109, 30, 29, 2], [2, "1306", 271, 396, 30, 29, 2], [2, "1302", 445, 520, 40, 29, 2], [2, "411_2", 531, 504, 44, 40, 2], [2, "1305", 575, 520, 20, 14, 0], [2, "1303", 339, 420, 34, 20, 0], [2, "1303", 386, 529, 34, 20, 2], [2, "1303", 698, 533, 34, 20, 2], [2, "955_1", 776, 210, 20, 18, 2], [2, "1305", 766, 392, 20, 14, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, 55, -1, -1, -1, 55, 55, 55, 55, -1, -1, -1, -1, -1, 52, 51, -1, -1, -1, -1, 56, 43, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, 13, 48, 49, 55, 55, 52, 52, 56, 47, -1, -1, 55, 55, 55, -1, -1, -1, -1, -1, 17, 16, 15, 17, 16, 15, 53, 56, 43, 43, 43, -1, -1, -1, -1, -1, 49, 50, 47, 45, 46, 52, 51, -1, -1, 45, 46, 56, 55, 55, 55, 55, 50, 39, 52, 51, 9, 20, 13, 14, 20, 13, 14, 15, -1, 43, 43, 43, 43, -1, 43, 43, 49, 50, 47, -1, -1, -1, 17, 16, 10, 10, 11, -1, 53, 52, 55, 55, 55, 55, -1, -1, -1, 12, 25, 25, 13, 13, 13, 13, 30, 33, 45, 46, 56, 43, -1, -1, 49, 50, 47, -1, -1, -1, 9, 20, 19, 13, 13, 14, 15, -1, -1, 55, 55, -1, 37, -1, -1, -1, 21, 32, 13, 13, 25, 13, 26, 27, 53, 56, 40, 50, 56, -1, 56, 46, 47, -1, -1, -1, -1, 21, 32, 31, 31, 13, 31, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 32, 13, 13, 14, 10, 11, 45, 46, 52, 52, 52, 51, -1, 33, 34, 35, -1, -1, -1, 29, 28, 32, 31, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, -1, 21, 22, 28, 28, 32, 14, 15, -1, 17, 16, 15, -1, 33, 34, 37, 38, -1, -1, -1, -1, -1, 29, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 50, 51, -1, -1, -1, -1, -1, 29, 28, 27, 9, 20, 19, 18, -1, 36, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, -1, -1, 48, 49, 49, 49, 50, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, 9, 20, 25, 26, 27, -1, -1, -1, -1, -1, 13, 13, 13, -1, 41, 40, 39, -1, 45, 56, -1, -1, -1, 49, 49, 38, 38, 39, -1, -1, -1, -1, -1, 40, 39, -1, -1, 29, 28, 28, 23, -1, -1, -1, -1, -1, -1, -1, 49, -1, 49, 49, 49, 42, -1, -1, 48, 49, -1, -1, 55, 55, 55, 49, 49, 38, 39, -1, -1, -1, 52, 52, 51, -1, -1, 33, 34, 49, -1, -1, -1, -1, 55, 49, 50, 56, 49, 52, 52, 52, 51, -1, -1, 45, 46, -1, 55, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 49, -1, -1, -1, 45, 55, 55, 54, 9, 10, 16, 15, -1, -1, -1, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, 55, 54, 17, 20, 25, 26, 27, -1, 33, 34, 34, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 43, 42, -1, -1, 25, -1, -1, -1, -1, 49, 54, 9, 20, 13, 26, 23, -1, -1, 36, 37, 46, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, 49, 50, 52, 52, 52, 52, 51, 33, 34, 43, -1, -1, -1, -1, 49, 38, 29, 32, 25, 14, 15, -1, -1, 36, 37, -1, -1, -1, -1, 55, 55, 49, 50, 49, 50, 49, 50, 46, 47, -1, -1, -1, 33, 34, 44, 37, 43, -1, -1, -1, -1, -1, -1, -1, 24, 25, 25, 14, 15, -1, 44, 43, -1, -1, -1, -1, -1, 49, 50, 46, 49, 50, 46, 47, -1, 17, 16, 15, -1, 36, 37, 43, 37, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 25, 25, 30, -1, -1, -1, -1, -1, -1, -1, -1, 46, 49, 50, 46, 47, 9, 10, 16, 20, 25, 30, -1, 45, 46, 56, 37, 49, 37, 37, 37, 37, 37, 40, 39, 24, 25, 25, 25, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 47, -1, -1, 24, 25, 25, 25, 25, 30, -1, -1, 33, 44, 34, 55, 56, 49, 55, 55, 56, 55, 54, 24, 25, 26, 28, 27, -1, 33, 34, -1, -1, -1, -1, -1, -1, -1, 40, 40, 39, 21, 32, 25, 25, 26, 27, -1, -1, 45, 46, 46, 52, 52, 52, 52, 56, 55, 50, 51, 21, 22, 27, -1, -1, 33, 34, -1, 49, -1, -1, -1, -1, -1, -1, 43, 55, 54, -1, 24, 25, 25, 14, 16, 16, 16, 15, -1, -1, -1, -1, -1, -1, 53, 52, 51, -1, -1, -1, -1, -1, -1, 45, 56, 55, 46, 43, 43, -1, -1, 49, 50, 55, 54, 51, -1, 21, 32, 31, 31, 25, 28, 19, 18, -1, -1, -1, -1, 33, 34, 34, 39, -1, -1, -1, -1, 9, 10, 16, 15, 53, 52, 56, 55, 43, -1, 40, 50, 47, 52, 51, -1, -1, -1, 21, 22, 28, 27, -1, -1, -1, -1, -1, -1, -1, 36, 37, 37, 42, 40, 39, -1, -1, 24, 25, 19, 18, -1, -1, 53, 52, 56, 43, 37, 38, 39, 41, 40, 39, -1, -1, 33, 34, -1, 40, 27, 43, 40, 34, 35, 33, 34, 44, 43, 43, -1, 43, 42, 41, 40, 21, 22, 28, 27, 9, 10, 11, -1, 48, 43, -1, 43, 38, 44, 43, 38, 34, -1, 44, 43, 43, 43, -1, 43, 43, 37, 38, 44, 49, -1, 43, 43, 43, -1, -1, 49, 49, 38, -1, -1, -1, 12, 13, 14, -1, 44, 43]}, {"type": 2, "data": [0, 1, 0, 1, 2, 3, 4, 5, 1, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 1, 2, 0, 0, 1, 2, 1, 2, 0, 1, 2, 1, 2, 1, 2, 0, 1, 2, 3, 0, 1, 2, 2, 6, 7, 8, 4, 3, 4, 5, 3, 3, 4, 5, 3, 4, 5, 4, 5, 3, 3, 4, 5, 4, 5, 3, 4, 5, 4, 5, 4, 0, 1, 2, 2, 6, 3, 4, 5, 5, 3, 0, 1, 2, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 7, 8, 0, 1, 2, 0, 1, 2, 6, 7, 8, 7, 8, 7, 3, 4, 5, 5, 0, 6, 7, 8, 8, 0, 0, 1, 2, 3, 4, 5, 1, 2, 1, 2, 3, 0, 1, 2, 1, 3, 4, 5, 3, 4, 5, 0, 1, 2, 0, 1, 2, 6, 7, 8, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 4, 5, 6, 3, 4, 5, 0, 6, 7, 8, 6, 7, 8, 3, 4, 0, 1, 2, 5, 3, 3, 4, 5, 6, 7, 0, 1, 2, 5, 6, 0, 1, 0, 1, 2, 4, 5, 0, 1, 2, 6, 7, 8, 2, 4, 5, 2, 3, 0, 1, 2, 7, 3, 4, 5, 0, 1, 6, 7, 8, 0, 1, 3, 4, 5, 8, 0, 3, 4, 3, 4, 5, 7, 8, 3, 4, 5, 5, 3, 4, 5, 0, 0, 1, 0, 1, 2, 5, 6, 6, 7, 8, 3, 4, 5, 6, 7, 0, 1, 2, 7, 0, 0, 1, 6, 7, 6, 7, 8, 4, 5, 6, 7, 8, 1, 6, 7, 8, 3, 3, 4, 3, 4, 5, 8, 1, 2, 4, 0, 6, 7, 8, 5, 3, 3, 4, 5, 1, 3, 3, 4, 3, 4, 5, 0, 1, 2, 8, 7, 8, 3, 4, 0, 1, 2, 1, 6, 7, 6, 0, 1, 2, 4, 5, 7, 3, 6, 7, 8, 0, 1, 6, 7, 8, 4, 6, 6, 7, 6, 7, 8, 3, 4, 5, 4, 5, 3, 6, 7, 3, 4, 5, 4, 5, 5, 1, 3, 4, 5, 1, 2, 0, 6, 7, 8, 1, 3, 4, 0, 1, 6, 7, 8, 8, 0, 0, 1, 2, 6, 7, 0, 1, 2, 6, 6, 3, 6, 7, 8, 7, 8, 8, 4, 6, 7, 8, 0, 1, 2, 4, 5, 3, 4, 6, 7, 3, 4, 0, 1, 2, 3, 3, 3, 4, 5, 8, 6, 3, 4, 5, 2, 2, 6, 7, 8, 6, 3, 4, 5, 0, 1, 2, 8, 3, 4, 5, 7, 8, 6, 7, 8, 6, 6, 7, 3, 4, 5, 6, 6, 6, 7, 8, 5, 0, 6, 7, 8, 5, 5, 6, 7, 6, 0, 1, 2, 8, 3, 4, 5, 1, 2, 7, 8, 2, 0, 3, 0, 1, 2, 3, 4, 6, 7, 8, 7, 8, 1, 2, 8, 8, 3, 0, 6, 7, 8, 8, 1, 2, 0, 3, 4, 5, 8, 6, 7, 8, 4, 5, 3, 4, 5, 0, 1, 2, 4, 5, 6, 7, 8, 0, 1, 0, 3, 4, 5, 4, 0, 1, 3, 4, 0, 1, 2, 4, 5, 3, 6, 7, 8, 0, 0, 3, 6, 7, 8, 6, 7, 8, 3, 4, 5, 7, 8, 0, 1, 0, 0, 1, 3, 6, 7, 8, 2, 3, 4, 6, 7, 3, 4, 5, 7, 8, 6, 3, 0, 1, 2, 3, 6, 6, 7, 8, 2, 7, 8, 6, 7, 8, 1, 2, 3, 4, 3, 0, 1, 2, 7, 3, 4, 5, 6, 7, 8, 4, 6, 7, 8, 2, 4, 5, 0, 1, 2, 5, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, 6, 0, 6, 3, 4, 5, 6, 6, 7, 8, 0, 1, 6, 7, 8, 0, 1, 2, 7, 0, 1, 2, 0, 1, 2, 7, 0, 6, 7, 0, 1, 2, 0, 1, 3, 4, 5, 0, 1, 2, 6, 7, 8, 0, 1, 2, 2, 3, 4, 5, 0, 1, 2, 4, 5, 5, 3, 4, 5, 3, 4, 5, 2, 3, 4, 5, 3, 4, 5, 3, 4, 6, 7, 8, 3, 4, 5, 3, 3, 3, 3, 4, 5, 5, 6, 7, 8, 3, 4, 5, 7, 8, 0, 6, 7, 8, 6, 7, 8, 0, 1, 2, 8, 6, 7, 8, 6, 7, 8, 3, 4, 6, 7, 8, 0, 1, 6, 6, 7, 8, 8, 0, 1, 2, 6, 7, 8, 5, 4, 0, 1, 2, 8, 4, 5, 4, 3, 4, 5, 0, 1, 2, 8, 3, 4, 5, 6, 7, 0, 0, 1, 3, 4, 5, 0, 3, 4, 5, 3, 4, 0, 6, 7, 8, 0, 0, 3, 4, 5, 6, 7, 8, 7, 6, 7, 8, 3, 4, 5, 8, 6, 7, 8, 3, 4, 3, 3, 4, 6, 7, 8, 3, 0, 1, 0, 1, 2, 3, 4, 5, 0, 3, 3, 6, 7, 8, 4, 3, 4, 5, 6, 7, 8, 6, 7, 8, 5, 0, 1, 2, 0, 1, 6, 6, 7, 0, 0, 1, 2, 0, 1, 3, 4, 5, 1, 2, 8, 0, 6, 6, 7, 8, 8, 0, 6, 7, 8, 4, 5, 3, 0, 1, 2, 8, 3, 4, 5, 0, 1, 0, 0, 1, 3, 3, 4, 5, 3, 4, 6, 7, 8, 4, 5, 5, 3, 4, 3, 4, 5, 0, 3, 4, 5, 6, 7, 8, 6, 3, 4, 5, 2, 6, 7, 8, 3, 4, 3, 3, 4, 6, 6, 7, 8, 6, 7, 8, 5, 6, 7, 8, 8, 6, 7, 6, 7, 8, 0, 6, 7, 8, 2, 3, 4, 5, 6, 0, 1, 2, 2, 0, 1, 6, 7, 6, 6, 7, 0, 3, 4, 5, 3, 4, 5, 0, 1, 2, 0, 1, 3, 4, 5, 5, 3, 3, 4, 3, 4, 5, 6, 7, 8, 4, 3, 4, 5, 5, 3, 4, 5, 8, 0, 1, 2, 3, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 4, 6, 7, 8, 8, 6, 6, 7, 6, 7, 8, 7, 8, 6, 7, 6, 7, 8, 8, 6, 7, 8, 6]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}