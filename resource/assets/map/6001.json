{"mW": 912, "mH": 744, "tW": 24, "tH": 24, "tiles": [["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["928", 0, 2, 1], ["497", 0, 2, 1]], "layers": [{"type": 3, "obj": [[2, "943", 802, 696, 14, 41, 2], [2, "943", 826, 675, 14, 41, 2], [2, "943", 827, 641, 14, 41, 2], [2, "943", 877, 673, 14, 41, 2], [2, "943", 834, 602, 14, 41, 2], [2, "925", 873, 698, 52, 34, 0], [2, "943", 849, 598, 14, 41, 2], [2, "943", 864, 592, 14, 41, 2], [2, "925", 845, 712, 52, 34, 0], [2, "925", 819, 725, 52, 34, 0], [2, "943", 877, 614, 14, 41, 2], [2, "943", 877, 647, 14, 41, 2], [2, "943", 877, 582, 14, 41, 2], [2, "923", 836, 606, 44, 125, 2], [2, "921", 872, 656, 56, 68, 0], [2, "922", 886, 565, 30, 108, 0], [2, "941", 879, 550, 44, 39, 0], [2, "980", 832, 610, 58, 41, 0], [2, "924", 858, 513, 58, 48, 0], [2, "924", 886, 525, 58, 48, 0], [2, "943", 590, 724, 14, 41, 2], [2, "943", 698, 673, 14, 41, 2], [2, "943", 696, 707, 14, 41, 2], [2, "943", 805, 624, 14, 41, 2], [2, "943", 804, 658, 14, 41, 2], [2, "569", 295, 164, 34, 13, 0], [2, "570", 296, 143, 34, 21, 0], [2, "571", 296, 125, 34, 18, 0], [2, "925", 796, 736, 52, 34, 0], [2, "921", 793, 694, 56, 68, 0], [2, "922", 807, 603, 30, 108, 0], [2, "941", 801, 584, 44, 39, 0], [2, "924", 830, 527, 58, 48, 0], [2, "924", 858, 540, 58, 48, 0], [2, "924", 805, 538, 58, 48, 0], [2, "924", 834, 552, 58, 48, 0], [2, "924", 780, 551, 58, 48, 0], [2, "924", 807, 565, 58, 48, 0], [2, "921", 806, 511, 56, 68, 0], [2, "921", 872, 479, 56, 68, 0], [2, "943", 730, 655, 14, 41, 2], [2, "943", 745, 651, 14, 41, 2], [2, "943", 760, 645, 14, 41, 2], [2, "943", 773, 667, 14, 41, 2], [2, "943", 773, 700, 14, 41, 2], [2, "943", 773, 635, 14, 41, 2], [2, "923", 728, 659, 44, 125, 2], [2, "943", 773, 726, 14, 41, 2], [2, "922", 703, 656, 30, 108, 0], [2, "921", 764, 710, 56, 68, 0], [2, "922", 778, 618, 30, 108, 0], [2, "941", 697, 637, 44, 39, 0], [2, "941", 772, 604, 44, 39, 0], [2, "924", 752, 566, 58, 48, 0], [2, "924", 779, 579, 58, 48, 0], [2, "924", 726, 580, 58, 48, 0], [2, "924", 754, 593, 58, 48, 0], [2, "924", 701, 591, 58, 48, 0], [2, "924", 730, 605, 58, 48, 0], [2, "924", 676, 604, 58, 48, 0], [2, "924", 703, 618, 58, 48, 0], [2, "921", 702, 564, 56, 68, 0], [2, "921", 768, 532, 56, 68, 0], [2, "943", 625, 709, 14, 41, 2], [2, "943", 640, 705, 14, 41, 2], [2, "943", 655, 699, 14, 41, 2], [2, "943", 668, 721, 14, 41, 2], [2, "943", 668, 689, 14, 41, 2], [2, "923", 623, 713, 44, 125, 2], [2, "922", 598, 710, 30, 108, 0], [2, "922", 673, 672, 30, 108, 0], [2, "941", 592, 691, 44, 39, 0], [2, "941", 667, 658, 44, 39, 0], [2, "924", 647, 620, 58, 48, 0], [2, "924", 674, 633, 58, 48, 0], [2, "924", 621, 634, 58, 48, 0], [2, "924", 649, 647, 58, 48, 0], [2, "924", 596, 645, 58, 48, 0], [2, "924", 625, 659, 58, 48, 0], [2, "924", 571, 658, 58, 48, 0], [2, "924", 598, 672, 58, 48, 0], [2, "921", 597, 618, 56, 68, 0], [2, "921", 663, 586, 56, 68, 0], [2, "941", 563, 712, 44, 39, 0], [2, "924", 542, 674, 58, 48, 0], [2, "924", 570, 687, 58, 48, 0], [2, "924", 517, 688, 58, 48, 0], [2, "924", 545, 701, 58, 48, 0], [2, "924", 492, 699, 58, 48, 0], [2, "924", 521, 713, 58, 48, 0], [2, "924", 467, 712, 58, 48, 0], [2, "924", 494, 726, 58, 48, 0], [2, "921", 493, 672, 56, 68, 0], [2, "921", 559, 640, 56, 68, 0], [2, "987", 498, 636, 24, 52, 0], [2, "987", 523, 637, 24, 52, 2], [2, "987", 876, 444, 24, 52, 0], [2, "987", 900, 444, 24, 52, 2], [2, "987", 811, 477, 24, 52, 0], [2, "987", 835, 477, 24, 52, 2], [2, "987", 773, 498, 24, 52, 0], [2, "987", 797, 498, 24, 52, 2], [2, "987", 707, 530, 24, 52, 0], [2, "987", 731, 530, 24, 52, 2], [2, "987", 667, 552, 24, 52, 0], [2, "987", 691, 552, 24, 52, 2], [2, "987", 602, 583, 24, 52, 0], [2, "987", 626, 583, 24, 52, 2], [2, "987", 563, 605, 24, 52, 0], [2, "987", 587, 605, 24, 52, 2], [2, "924", 438, 726, 58, 48, 0], [2, "569", 703, 372, 34, 13, 0], [2, "570", 704, 351, 34, 21, 0], [2, "571", 704, 333, 34, 18, 0]]}, {"type": 4, "obj": [[2, "3240", 429, 27, 178, 176, 0], [2, "986", 282, 155, 32, 61, 0], [2, "986", 313, 155, 32, 61, 2], [2, "3240", 660, 142, 178, 176, 0], [2, "986", 688, 362, 32, 61, 0], [2, "986", 719, 362, 32, 61, 2]]}, {"type": 3, "obj": [[2, "943", 899, 298, 14, 41, 0], [2, "943", 898, 266, 14, 41, 0], [2, "943", 898, 231, 14, 41, 0], [2, "943", 897, 198, 14, 41, 0], [2, "943", 814, 234, 14, 41, 0], [2, "943", 813, 201, 14, 41, 0], [2, "943", 815, 166, 14, 41, 0], [2, "949", 657, 132, 98, 51, 0], [2, "944", 659, 114, 12, 22, 0], [2, "931", 648, 115, 24, 37, 0], [2, "944", 611, 101, 12, 22, 0], [2, "944", 611, 70, 12, 22, 0], [2, "944", 611, 86, 12, 22, 0], [2, "931", 611, 98, 24, 37, 0], [2, "944", 605, 53, 12, 22, 0], [2, "943", 878, -10, 14, 41, 0], [2, "943", 880, 13, 14, 41, 0], [2, "943", 865, -7, 14, 41, 0], [2, "943", 672, -26, 14, 41, 0], [2, "943", 672, 8, 14, 41, 0], [2, "943", 850, -4, 14, 41, 0], [2, "943", 836, -8, 14, 41, 0], [2, "943", 807, -19, 14, 41, 0], [2, "943", 757, -20, 14, 41, 0], [2, "943", 507, 1, 14, 41, 0], [2, "943", 496, -2, 14, 41, 0], [2, "943", 482, -7, 14, 41, 0], [2, "943", 467, -8, 14, 41, 0], [2, "943", 541, 19, 14, 41, 2], [2, "923", 541, 24, 44, 125, 2], [2, "943", 390, 30, 14, 41, 0], [2, "943", 390, -9, 14, 41, 0], [2, "943", 356, -15, 14, 41, 2], [2, "943", 386, -23, 14, 41, 0], [2, "943", 386, 9, 14, 41, 0], [2, "943", 357, 14, 14, 41, 2], [2, "925", 342, 70, 52, 34, 0], [2, "943", 386, 42, 14, 41, 0], [2, "925", 360, 80, 52, 34, 0], [2, "925", 385, 92, 52, 34, 0], [2, "925", 410, 105, 52, 34, 0], [2, "925", 436, 118, 52, 34, 0], [2, "925", 460, 130, 52, 34, 0], [2, "925", 484, 143, 52, 34, 0], [2, "925", 508, 155, 52, 34, 0], [2, "943", 432, 12, 14, 41, 0], [2, "943", 432, 45, 14, 41, 0], [2, "943", 432, -18, 14, 41, 0], [2, "923", 396, -18, 44, 125, 0], [2, "943", 461, 47, 14, 41, 0], [2, "943", 461, 80, 14, 41, 0], [2, "943", 461, 15, 14, 41, 0], [2, "943", 506, 49, 14, 41, 0], [2, "943", 506, 88, 14, 41, 0], [2, "943", 505, 20, 14, 41, 0], [2, "943", 465, 29, 14, 41, 0], [2, "943", 465, 68, 14, 41, 0], [2, "923", 471, 20, 44, 125, 0], [2, "921", 502, 108, 56, 68, 0], [2, "922", 516, 15, 30, 108, 0], [2, "921", 426, 71, 56, 68, 0], [2, "922", 440, -22, 30, 108, 0], [2, "941", 511, -3, 44, 39, 0], [2, "921", 350, 34, 56, 68, 0], [2, "924", 495, -33, 58, 48, 0], [2, "921", 570, 76, 56, 68, 0], [2, "922", 584, -17, 30, 108, 0], [2, "924", 524, -18, 58, 48, 0], [2, "924", 551, -5, 58, 48, 0], [2, "969", 174, 237, 36, 30, 0], [2, "943", 149, 124, 14, 41, 2], [2, "943", 148, 91, 14, 41, 2], [2, "943", 151, 57, 14, 41, 2], [2, "943", 45, 171, 14, 41, 2], [2, "943", 41, 139, 14, 41, 2], [2, "943", 43, 106, 14, 41, 2], [2, "943", 254, 62, 14, 41, 2], [2, "943", 254, 30, 14, 41, 2], [2, "943", 253, -2, 14, 41, 2], [2, "943", 52, 3, 14, 41, 2], [2, "943", 20, 16, 14, 41, 2], [2, "943", 20, -17, 14, 41, 2], [2, "943", 52, -30, 14, 41, 2], [2, "965", -6, 310, 40, 33, 0], [2, "922", 365, -60, 30, 108, 0], [2, "925", 326, 89, 52, 34, 0], [2, "943", 287, -7, 14, 41, 2], [2, "943", 302, -11, 14, 41, 2], [2, "943", 317, -17, 14, 41, 2], [2, "925", 298, 103, 52, 34, 0], [2, "925", 272, 116, 52, 34, 0], [2, "925", 249, 127, 52, 34, 0], [2, "943", 330, 5, 14, 41, 2], [2, "943", 330, 38, 14, 41, 2], [2, "943", 330, -27, 14, 41, 2], [2, "923", 285, -3, 44, 125, 2], [2, "943", 330, 64, 14, 41, 2], [2, "921", 246, 85, 56, 68, 0], [2, "922", 260, -6, 30, 108, 0], [2, "921", 321, 48, 56, 68, 0], [2, "922", 335, -44, 30, 108, 0], [2, "941", 254, -25, 44, 39, 0], [2, "941", 329, -58, 44, 39, 0], [2, "924", 280, -110, 58, 48, 0], [2, "924", 308, -96, 58, 48, 0], [2, "924", 336, -83, 58, 48, 0], [2, "924", 283, -82, 58, 48, 0], [2, "924", 311, -69, 58, 48, 0], [2, "924", 258, -71, 58, 48, 0], [2, "924", 287, -57, 58, 48, 0], [2, "924", 233, -58, 58, 48, 0], [2, "924", 260, -44, 58, 48, 0], [2, "923", 294, -181, 44, 125, 2], [2, "921", 259, -98, 56, 68, 0], [2, "922", 273, -191, 30, 108, 0], [2, "921", 325, -130, 56, 68, 0], [2, "925", 221, 141, 52, 34, 0], [2, "943", 182, 45, 14, 41, 2], [2, "943", 197, 41, 14, 41, 2], [2, "943", 212, 35, 14, 41, 2], [2, "925", 193, 155, 52, 34, 0], [2, "925", 167, 168, 52, 34, 0], [2, "925", 144, 179, 52, 34, 0], [2, "943", 225, 57, 14, 41, 2], [2, "943", 225, 90, 14, 41, 2], [2, "943", 225, 25, 14, 41, 2], [2, "923", 180, 49, 44, 125, 2], [2, "943", 225, 116, 14, 41, 2], [2, "921", 141, 137, 56, 68, 0], [2, "922", 155, 46, 30, 108, 0], [2, "921", 216, 100, 56, 68, 0], [2, "922", 230, 8, 30, 108, 0], [2, "941", 149, 27, 44, 39, 0], [2, "941", 224, -6, 44, 39, 0], [2, "924", 175, -58, 58, 48, 0], [2, "924", 203, -44, 58, 48, 0], [2, "924", 231, -31, 58, 48, 0], [2, "924", 178, -30, 58, 48, 0], [2, "924", 206, -17, 58, 48, 0], [2, "924", 153, -19, 58, 48, 0], [2, "924", 182, -5, 58, 48, 0], [2, "924", 128, -6, 58, 48, 0], [2, "924", 155, 8, 58, 48, 0], [2, "923", 189, -129, 44, 125, 2], [2, "921", 154, -46, 56, 68, 0], [2, "922", 168, -139, 30, 108, 0], [2, "921", 220, -78, 56, 68, 0], [2, "925", 117, 194, 52, 34, 0], [2, "943", 78, 98, 14, 41, 2], [2, "943", 93, 94, 14, 41, 2], [2, "943", 108, 88, 14, 41, 2], [2, "925", 89, 208, 52, 34, 0], [2, "925", 63, 221, 52, 34, 0], [2, "925", 40, 232, 52, 34, 0], [2, "943", 121, 110, 14, 41, 2], [2, "943", 121, 143, 14, 41, 2], [2, "943", 121, 78, 14, 41, 2], [2, "923", 76, 102, 44, 125, 2], [2, "943", 121, 169, 14, 41, 2], [2, "921", 37, 190, 56, 68, 0], [2, "922", 51, 99, 30, 108, 0], [2, "921", 112, 153, 56, 68, 0], [2, "922", 126, 61, 30, 108, 0], [2, "941", 45, 80, 44, 39, 0], [2, "941", 120, 47, 44, 39, 0], [2, "924", 71, -5, 58, 48, 0], [2, "924", 99, 9, 58, 48, 0], [2, "924", 127, 22, 58, 48, 0], [2, "924", 74, 23, 58, 48, 0], [2, "924", 102, 36, 58, 48, 0], [2, "924", 49, 34, 58, 48, 0], [2, "924", 78, 48, 58, 48, 0], [2, "924", 24, 47, 58, 48, 0], [2, "924", 51, 61, 58, 48, 0], [2, "923", 85, -76, 44, 125, 2], [2, "921", 50, 7, 56, 68, 0], [2, "922", 64, -86, 30, 108, 0], [2, "921", 116, -25, 56, 68, 0], [2, "925", 13, 245, 52, 34, 0], [2, "943", -26, 149, 14, 41, 2], [2, "943", -11, 145, 14, 41, 2], [2, "943", 4, 139, 14, 41, 2], [2, "925", -15, 259, 52, 34, 0], [2, "925", -42, 270, 52, 34, 0], [2, "943", 17, 161, 14, 41, 2], [2, "943", 17, 194, 14, 41, 2], [2, "943", 17, 129, 14, 41, 2], [2, "923", -28, 153, 44, 125, 2], [2, "943", 17, 220, 14, 41, 2], [2, "921", 8, 204, 56, 68, 0], [2, "922", 22, 112, 30, 108, 0], [2, "941", 16, 98, 44, 39, 0], [2, "924", -33, 46, 58, 48, 0], [2, "924", -5, 60, 58, 48, 0], [2, "924", 23, 73, 58, 48, 0], [2, "924", -30, 74, 58, 48, 0], [2, "924", -2, 87, 58, 48, 0], [2, "924", -55, 85, 58, 48, 0], [2, "924", -26, 99, 58, 48, 0], [2, "924", -53, 112, 58, 48, 0], [2, "923", -19, -25, 44, 125, 2], [2, "921", 12, 26, 56, 68, 0], [2, "922", 26, -67, 30, 108, 0], [2, "965", 349, 113, 40, 33, 0], [2, "978", 290, 112, 66, 56, 0], [2, "975", 354, 107, 26, 27, 0], [2, "978", 19, 277, 66, 56, 2], [2, "974", 0, 281, 26, 48, 0], [2, "975", -1, 277, 26, 27, 0], [2, "980", 357, 10, 58, 41, 0], [2, "1378", 33, 232, 28, 51, 0], [2, "1379", 63, 256, 26, 24, 0], [2, "980", 203, 37, 58, 41, 0], [2, "976", 252, 190, 34, 37, 0], [2, "976", 149, 238, 34, 37, 0], [2, "975", 153, 234, 26, 27, 0], [2, "955", 275, 164, 20, 18, 0], [2, "977", 315, 145, 18, 36, 0], [2, "479", 191, 260, 36, 18, 0], [2, "478", 265, 651, 24, 16, 0], [2, "478", 13, 355, 24, 16, 0], [2, "479", 177, 192, 36, 18, 0], [2, "479", 533, 352, 36, 18, 0], [2, "478", 304, 212, 24, 16, 0], [2, "478", 330, 202, 24, 16, 0], [2, "925", -49, 602, 52, 34, 0], [2, "925", -26, 614, 52, 34, 0], [2, "925", -2, 626, 52, 34, 0], [2, "925", 22, 638, 52, 34, 0], [2, "925", 46, 651, 52, 34, 0], [2, "925", 70, 663, 52, 34, 0], [2, "925", 96, 676, 52, 34, 0], [2, "925", 120, 688, 52, 34, 0], [2, "925", 146, 701, 52, 34, 0], [2, "925", -65, 604, 52, 34, 0], [2, "925", -42, 616, 52, 34, 0], [2, "925", -18, 628, 52, 34, 0], [2, "925", 6, 640, 52, 34, 0], [2, "925", 30, 653, 52, 34, 0], [2, "925", 54, 665, 52, 34, 0], [2, "925", 80, 678, 52, 34, 0], [2, "925", 104, 690, 52, 34, 0], [2, "925", 130, 703, 52, 34, 0], [2, "925", -59, 618, 52, 34, 0], [2, "925", -35, 630, 52, 34, 0], [2, "925", -11, 642, 52, 34, 0], [2, "925", 13, 655, 52, 34, 0], [2, "925", 37, 667, 52, 34, 0], [2, "925", 63, 680, 52, 34, 0], [2, "925", 87, 692, 52, 34, 0], [2, "925", 113, 705, 52, 34, 0], [2, "927", -44, 644, 42, 24, 0], [2, "927", -13, 660, 42, 24, 0], [2, "927", 19, 676, 42, 24, 0], [2, "927", 50, 692, 42, 24, 0], [2, "478", 46, 612, 24, 16, 0], [2, "954", 91, 289, 24, 25, 0], [2, "955", 277, 215, 20, 18, 0], [2, "478", 260, 453, 24, 16, 0], [2, "478", 150, 270, 24, 16, 0], [2, "1378", 263, 124, 28, 51, 2], [2, "1379", 246, 159, 26, 24, 0], [2, "955", 674, 411, 20, 18, 0], [2, "478", 513, 364, 24, 16, 0], [2, "943", 836, 26, 14, 41, 0], [2, "943", 825, 258, 14, 41, 0], [2, "923", 670, 51, 44, 125, 0], [2, "927", 709, 194, 42, 24, 2], [2, "925", 684, 218, 52, 34, 0], [2, "925", 695, 245, 52, 34, 0], [2, "925", 713, 255, 52, 34, 0], [2, "925", 736, 266, 52, 34, 0], [2, "925", 760, 278, 52, 34, 0], [2, "925", 785, 291, 52, 34, 0], [2, "925", 656, 253, 52, 34, 0], [2, "925", 497, 170, 52, 34, 0], [2, "925", 473, 183, 52, 34, 0], [2, "925", 497, 195, 52, 34, 0], [2, "925", 496, 174, 52, 34, 0], [2, "925", 521, 207, 52, 34, 0], [2, "925", 545, 219, 52, 34, 0], [2, "925", 580, 241, 52, 34, 0], [2, "925", 604, 253, 52, 34, 0], [2, "925", 519, 186, 52, 34, 0], [2, "925", 543, 198, 52, 34, 0], [2, "925", 567, 210, 52, 34, 0], [2, "925", 591, 223, 52, 34, 0], [2, "925", 656, 231, 52, 34, 0], [2, "927", 595, 129, 42, 24, 2], [2, "927", 563, 145, 42, 24, 2], [2, "927", 531, 161, 42, 24, 2], [2, "927", 519, 175, 42, 24, 0], [2, "927", 550, 191, 42, 24, 0], [2, "927", 582, 207, 42, 24, 0], [2, "926", 519, 180, 32, 25, 0], [2, "925", 629, 266, 52, 34, 0], [2, "925", 614, 235, 52, 34, 0], [2, "927", 613, 223, 42, 24, 0], [2, "926", 602, 222, 32, 25, 0], [2, "925", 631, 244, 52, 34, 0], [2, "926", 627, 234, 32, 25, 0], [2, "927", 646, 226, 42, 24, 2], [2, "927", 678, 210, 42, 24, 2], [2, "926", 656, 233, 32, 25, 2], [2, "926", 685, 218, 32, 25, 2], [2, "926", 713, 204, 32, 25, 2], [2, "926", 542, 192, 32, 25, 0], [2, "932", 544, 207, 76, 48, 0], [2, "944", 632, 69, 12, 22, 0], [2, "944", 644, 73, 12, 22, 0], [2, "944", 634, 85, 12, 22, 0], [2, "944", 644, 89, 12, 22, 0], [2, "944", 633, 101, 12, 22, 0], [2, "944", 643, 105, 12, 22, 0], [2, "944", 633, 116, 12, 22, 0], [2, "944", 643, 122, 12, 22, 0], [2, "944", 659, 82, 12, 22, 0], [2, "944", 659, 98, 12, 22, 0], [2, "945", 687, 82, 10, 25, 0], [2, "945", 696, 77, 10, 25, 0], [2, "945", 706, 73, 10, 25, 0], [2, "931", 636, 109, 24, 37, 0], [2, "932", 530, 221, 76, 48, 0], [2, "949", 624, 149, 98, 51, 0], [2, "949", 591, 166, 98, 51, 0], [2, "932", 556, 193, 76, 48, 0], [2, "949", 558, 183, 98, 51, 0], [2, "943", 764, 131, 14, 41, 0], [2, "943", 778, 137, 14, 41, 0], [2, "924", 685, 34, 58, 48, 0], [2, "690", 639, 68, 36, 85, 2], [2, "690", 612, 55, 36, 85, 2], [2, "943", 791, 179, 14, 41, 0], [2, "943", 791, 212, 14, 41, 0], [2, "943", 791, 147, 14, 41, 0], [2, "943", 749, 151, 14, 41, 0], [2, "943", 749, 184, 14, 41, 0], [2, "943", 749, 119, 14, 41, 0], [2, "923", 754, 158, 44, 125, 0], [2, "921", 710, 213, 56, 68, 0], [2, "690", 704, 104, 36, 85, 0], [2, "921", 781, 248, 56, 68, 0], [2, "922", 795, 157, 30, 108, 0], [2, "922", 723, 122, 30, 108, 0], [2, "941", 789, 138, 44, 39, 0], [2, "941", 717, 102, 44, 39, 0], [2, "924", 578, 9, 58, 48, 0], [2, "924", 605, 23, 58, 48, 0], [2, "924", 630, 36, 58, 48, 0], [2, "924", 656, 48, 58, 48, 0], [2, "924", 759, 24, 58, 48, 0], [2, "924", 734, 38, 58, 48, 0], [2, "924", 762, 52, 58, 48, 0], [2, "924", 791, 67, 58, 48, 0], [2, "924", 709, 49, 58, 48, 0], [2, "924", 737, 63, 58, 48, 0], [2, "924", 766, 78, 58, 48, 0], [2, "924", 794, 92, 58, 48, 0], [2, "924", 684, 63, 58, 48, 0], [2, "924", 712, 77, 58, 48, 0], [2, "924", 741, 92, 58, 48, 0], [2, "924", 769, 106, 58, 48, 0], [2, "923", 768, -21, 44, 125, 0], [2, "943", 754, 37, 14, 41, 0], [2, "943", 754, 9, 14, 41, 0], [2, "479", 468, 202, 36, 18, 2], [2, "479", 617, 282, 36, 18, 2], [2, "923", 687, -59, 44, 125, 0], [2, "921", 717, 26, 56, 68, 0], [2, "980", 732, 132, 58, 41, 0], [2, "965", 760, 316, 40, 33, 0], [2, "975", 767, 308, 26, 27, 0], [2, "955", 736, 292, 20, 18, 0], [2, "969", 723, 322, 36, 30, 0], [2, "479", 695, 324, 36, 18, 0], [2, "479", 644, 334, 36, 18, 2], [2, "478", 598, 311, 24, 16, 0], [2, "1379", 726, 294, 26, 24, 0], [2, "1378", 755, 262, 28, 51, 2], [2, "478", 524, 274, 24, 16, 0], [2, "925", 813, 305, 52, 34, 0], [2, "925", 837, 317, 52, 34, 0], [2, "925", 862, 330, 52, 34, 0], [2, "943", 841, 170, 14, 41, 0], [2, "943", 855, 176, 14, 41, 0], [2, "943", 868, 218, 14, 41, 0], [2, "943", 868, 251, 14, 41, 0], [2, "943", 868, 186, 14, 41, 0], [2, "943", 826, 190, 14, 41, 0], [2, "943", 825, 223, 14, 41, 0], [2, "943", 826, 158, 14, 41, 0], [2, "923", 831, 197, 44, 125, 0], [2, "921", 858, 287, 56, 68, 0], [2, "922", 872, 196, 30, 108, 0], [2, "941", 866, 177, 44, 39, 0], [2, "924", 827, 105, 58, 48, 0], [2, "924", 796, 120, 58, 48, 0], [2, "924", 856, 120, 58, 48, 0], [2, "924", 818, 131, 58, 48, 0], [2, "924", 846, 145, 58, 48, 0], [2, "924", 873, 159, 58, 48, 0], [2, "943", 836, 79, 14, 41, 0], [2, "943", 836, 51, 14, 41, 0], [2, "921", 795, 65, 56, 68, 0], [2, "922", 731, -67, 30, 108, 0], [2, "923", 600, -95, 44, 125, 0], [2, "921", 630, -18, 56, 68, 0], [2, "942", 655, 36, 68, 61, 0], [2, "943", 585, -37, 14, 41, 0], [2, "923", 847, 20, 44, 125, 0], [2, "921", 875, 103, 56, 68, 0], [2, "922", 889, 10, 30, 108, 0], [2, "941", 884, -13, 44, 39, 0], [2, "924", 880, -36, 58, 48, 0], [2, "922", 810, -28, 30, 108, 0], [2, "925", 887, 343, 52, 34, 0], [2, "969", 864, 455, 36, 30, 0], [2, "976", 839, 456, 34, 37, 0], [2, "975", 843, 452, 26, 27, 0], [2, "479", 881, 478, 36, 18, 0], [2, "478", 840, 488, 24, 16, 0], [2, "479", 736, 419, 36, 18, 0], [2, "478", 704, 413, 24, 16, 0], [2, "978", 422, 637, 66, 56, 2], [2, "954", 473, 633, 24, 25, 0], [2, "954", 644, 562, 24, 25, 0], [2, "925", 172, 714, 52, 34, 0], [2, "925", 156, 716, 52, 34, 0], [2, "925", 139, 718, 52, 34, 0], [2, "925", 198, 727, 52, 34, 0], [2, "925", 181, 729, 52, 34, 0], [2, "925", 164, 731, 52, 34, 0], [2, "927", 82, 708, 42, 24, 0], [2, "927", 114, 724, 42, 24, 0], [2, "927", 145, 740, 42, 24, 0], [2, "980", 809, 247, 58, 41, 0], [2, "1379", 616, 567, 26, 24, 0], [2, "1378", 888, 315, 28, 51, 0], [2, "955", 892, 391, 20, 18, 0], [2, "976", 867, 366, 34, 37, 0], [2, "478", 859, 395, 24, 16, 0], [2, "478", 44, 696, 24, 16, 0], [2, "1378", 400, 638, 28, 51, 2], [2, "1379", 393, 681, 26, 24, 0], [2, "976", 418, 672, 34, 37, 0], [2, "975", 422, 668, 26, 27, 0], [2, "955", 446, 704, 20, 18, 0], [2, "478", 63, 501, 24, 16, 0], [2, "1379", 434, 137, 26, 24, 0], [2, "954", 759, 402, 24, 25, 0], [2, "955", 390, 122, 20, 18, 0], [2, "479", 391, 158, 36, 18, 0], [2, "479", 393, 159, 36, 18, 2], [2, "478", 731, 409, 24, 16, 0], [2, "955", 345, 211, 20, 18, 0], [2, "478", 329, 728, 24, 16, 0], [2, "479", 174, 316, 36, 18, 0], [2, "479", 176, 317, 36, 18, 2], [2, "479", 365, 554, 36, 18, 0], [2, "479", 367, 555, 36, 18, 2], [2, "478", 636, 450, 24, 16, 0], [2, "479", 775, 471, 36, 18, 0], [2, "479", 777, 472, 36, 18, 2], [2, "1380", 14, 302, 56, 56, 2], [2, "1380", 785, 303, 56, 56, 0], [2, "955", 828, 351, 20, 18, 0], [2, "955", 39, 351, 20, 18, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 30, 30, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 15, -1, 30, 30, 30, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 15, 15, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, 21, 30, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 39, -1, -1, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, 24, 30, -1, 24, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 36, 37, 39, 39, 39, -1, -1, -1, -1, 0, 5, -1, -1, -1, -1, -1, -1, -1, -1, 30, 30, -1, 24, 0, 1, -1, -1, -1, -1, -1, -1, -1, 24, 0, 7, 6, 5, 24, 33, 34, -1, -1, 24, 0, 1, 24, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 5, 0, 1, 2, -1, -1, -1, -1, -1, 24, 24, 0, 1, 2, -1, -1, 7, 6, 5, -1, -1, 0, 1, 2, 7, 6, 24, 6, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 0, 1, 2, -1, -1, -1, -1, 17, 16, 36, 36, 36, -1, -1, -1, -1, -1, 7, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 0, 1, 2, 28, 27, 27, 26, -1, -1, 7, 6, 5, 24, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 0, 1, 2, -1, 40, 39, 39, 39, 38, -1, -1, -1, -1, 7, 6, 5, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 0, 1, 2, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, -1, 42, 36, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 0, 1, 2, -1, 40, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 39, 43, 42, 24, 24, -1, -1, -1, -1, -1, -1, 36, 36, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 42, 42, 42, 24, 24, -1, -1, -1, -1, -1, 2, 37, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 39, 43, 43, 5, 24, 24, 24, -1, -1, 39, 40, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, 33, 43, 42, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 6, 5, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 27, 26, -1, -1, -1, -1, 43, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 43, 42, 37, 38, -1, -1, -1, 32, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 39, 38, 24, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 20, 21, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, 32, 33, 31, 33, 27, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, -1, -1, -1, -1, 32, 33, 33, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 47, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, -1, -1, -1, -1, -1, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, -1, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 45, -1, 46, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, -1, -1, 46, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, 44, 45, -1, -1, -1, -1, -1, -1, 46, 46, 47, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, 44, 45, -1, 46, 47, -1, -1, -1, 46, 47, 46, 47, 46, 47, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, -1, -1, -1, -1, 46, 47, -1, -1, -1, 46, 47, 46, 47, 46, 47, 46, 47, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 47]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}