{"mW": 960, "mH": 960, "tW": 24, "tH": 24, "tiles": [["1802", 0, 3, 3], ["1809", 0, 2, 2], ["1800", 0, 3, 2], ["1800", 2, 3, 2], ["1800", 1, 3, 2], ["1800", 3, 3, 2], ["1801", 0, 3, 2], ["1801", 2, 3, 2], ["1801", 1, 3, 2], ["1801", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "1272", 783, 708, 62, 109, 0], [2, "1272", 846, 708, 62, 109, 2], [2, "1813", -35, 898, 90, 95, 0], [2, "127", 540, 882, 46, 82, 0], [2, "127", 580, 899, 46, 82, 0], [2, "127", 618, 918, 46, 82, 0], [2, "127", 565, 640, 46, 82, 2], [2, "127", 603, 621, 46, 82, 2], [2, "127", 641, 601, 46, 82, 2]]}, {"type": 4, "obj": [[2, "1364", 903, 29, 44, 69, 2], [2, "219_4", 883, 80, 36, 30, 0], [2, "220_3", 909, 82, 40, 29, 0], [4, 6, 300, 119, 1, 4036], [2, "1813", 393, 29, 90, 95, 0], [2, "1817", 792, 107, 22, 24, 0], [2, "1813_1", 704, 46, 90, 95, 0], [2, "1816", 718, 113, 26, 29, 0], [2, "1817_1", 764, 118, 22, 24, 0], [4, 14, 724, 154, 1, 4039], [2, "1818", 437, 30, 90, 132, 0], [2, "1488", 423, 113, 64, 53, 0], [2, "14_6", 511, 139, 32, 30, 0], [2, "14_6", 485, 150, 32, 30, 0], [2, "1814_1", 1, 80, 50, 104, 0], [2, "1817_1", 15, 181, 22, 24, 0], [2, "1813_2", 9, 125, 90, 95, 0], [4, 10, 80, 223, 1, 4037], [2, "1815_1", 5, 206, 34, 56, 0], [2, "1818", 145, 222, 90, 132, 0], [2, "14_6", 202, 334, 32, 30, 0], [2, "1817", 19, 342, 22, 24, 0], [2, "1488", 139, 317, 64, 53, 0], [2, "1817_1", 7, 350, 22, 24, 0], [4, 13, 52, 396, 1, 4038], [4, 5, 420, 407, 1, 4036], [2, "1817", 822, 409, 22, 24, 0], [2, "1817_1", 773, 431, 22, 24, 0], [2, "1813", 761, 361, 90, 95, 0], [4, 11, 794, 462, 1, 4037], [4, 7, 511, 496, 1, 4036], [2, "1813", 83, 526, 90, 95, 0], [2, "1814_1", 148, 531, 50, 104, 0], [2, "1817", 98, 617, 22, 24, 0], [2, "127", 836, 559, 46, 82, 0], [4, 12, 149, 644, 1, 4038], [2, "127", 798, 562, 46, 82, 2], [2, "127", 874, 578, 46, 82, 0], [2, "127", 758, 580, 46, 82, 2], [2, "1818", 668, 543, 90, 132, 2], [2, "127", 912, 596, 46, 82, 0], [2, "127", 719, 601, 46, 82, 2], [2, "127", 681, 621, 46, 82, 2], [4, 9, 766, 742, 1, 4036], [2, "891", 605, 684, 54, 75, 0], [2, "1818", 458, 661, 90, 132, 2], [2, "127", 503, 722, 46, 82, 2], [2, "127", 843, 732, 46, 82, 0], [2, "127", 466, 743, 46, 82, 2], [2, "127", 426, 761, 46, 82, 2], [4, 8, 304, 849, 1, 4036], [2, "127", 389, 781, 46, 82, 2], [2, "705", 794, 717, 74, 157, 0], [2, "705", 869, 717, 74, 157, 2], [2, "127", 385, 804, 46, 82, 0], [2, "219_4", 395, 870, 36, 30, 0], [2, "127", 423, 823, 46, 82, 0], [2, "705", 726, 751, 74, 157, 0], [2, "705", 800, 751, 74, 157, 2], [2, "219_4", 762, 883, 36, 30, 0], [2, "219_4", 431, 886, 36, 30, 0], [2, "219_4", 806, 887, 36, 30, 0], [2, "127", 461, 841, 46, 82, 0], [2, "705", 814, 772, 74, 157, 0], [2, "705", 888, 772, 74, 157, 2], [2, "219_4", 892, 907, 36, 30, 2], [2, "127", 501, 862, 46, 82, 0], [2, "219_4", 496, 920, 36, 30, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 14, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 30, 32, 31, -1, -1, -1, -1, 69, 68, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, 64, 65, 77, 78, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 27, 61, 62, 62, 63, -1, -1, 76, 83, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 74, 75, -1, -1, -1, -1, -1, 61, 62, 68, 62, 68, 67, -1, -1, -1, 61, -1, -1, 69, 72, 65, 65, 66, 62, 63, 81, 84, 83, 82, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 65, 71, 71, 71, 82, -1, -1, -1, -1, -1, -1, 64, 65, 65, 65, 83, 65, 70, -1, 81, 80, 79, 65, 66, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 71, 71, 78, 80, 79, -1, -1, -1, -1, -1, -1, 73, 74, 80, 74, 84, 83, 66, 68, 67, -1, -1, 65, 65, 65, 66, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, 80, 79, -1, -1, -1, -1, -1, 73, -1, -1, -1, -1, -1, -1, 76, 77, 65, 65, 82, -1, -1, 65, 65, 71, 65, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 84, 83, 78, 79, -1, -1, 65, 65, 77, 78, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 74, 79, -1, -1, -1, 65, 65, 78, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 78, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 36, 18, 19, 61, 62, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 70, -1, -1, -1, -1, -1, 61, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 23, 34, 64, 65, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 70, 67, -1, -1, 69, 68, 72, 83, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, 33, 32, 27, 76, 77, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 83, 82, -1, -1, 76, 65, 65, 83, 78, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 80, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, 80, 79, -1, -1, 81, 74, 74, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 68, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 68, 67, -1, -1, -1, -1, 20, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 83, 65, 65, 83, 82, -1, -1, 21, 20, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 77, 65, 71, 70, -1, 21, 19, -1, 26, 27, 21, 19, -1, -1, -1, -1, -1, -1, -1, 81, 84, 77, 77, 78, 79, -1, -1, 28, 17, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, 76, 77, 65, 65, 70, -1, 25, 31, -1, -1, -1, 25, 31, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 74, 75, -1, -1, -1, 33, 36, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 74, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 19, -1, -1, -1, 25, 26, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 18, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 62, 62, 63, -1, -1, -1, -1, 21, 24, 30, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 68, 67, -1, -1, -1, -1, -1, -1, -1, 64, 83, 65, 65, 65, 66, 63, -1, -1, -1, 33, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 72, 71, 66, 62, 62, 63, -1, -1, -1, -1, 81, 80, 84, 65, 83, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 68, 72, 77, 77, 77, 77, 65, 66, 62, -1, -1, -1, -1, -1, 73, 80, 80, 80, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 68, 72, 77, 77, 78, 84, 83, 77, 77, 77, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 71, 77, 77, 78, 75, 81, 80, 84, 83, 77, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, 74, 74, 75, -1, -1, -1, 81, 80, 84, 83, -1, -1, -1, -1, 21, 20, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, -1, -1, -1, -1, 28, 29, 29, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 36, 30, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 62, 62, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, -1, -1, -1, -1, -1, -1, -1, 25, 27, 61, 62, 62, 62, 63, -1, -1, -1, -1, 69, 68, 62, 62, 72, 65, 65, 65, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 66, 62, 63, -1, -1, -1, -1, -1, -1, -1, 76, 83, 77, 78, 75, -1, -1, -1, 61, 72, 71, 65, 65, 65, 77, 78, 74, 75, -1, -1, -1, -1, -1, -1, 61, 62, 62, 62, 62, 72, 71, 71, 66, -1, -1, -1, -1, -1, -1, -1, 81, 80, 74, 75, -1, -1, -1, -1, 76, 65, 65, 65, 77, 78, 74, 75, -1, -1, -1, -1, -1, -1, -1, 69, 72, 65, 65, 65, 65, 65, 65, 71, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 84, 83, 65, 65, 66, 62, 63, -1, -1, -1, -1, 61, -1, -1, 76, 83, 65, 65, 65, 65, 65, 65, 71, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, 84, 77, 77, 65, 66, 62, 67, -1, -1, -1, -1, -1, 81, 84, 83, 83, 65, 65, 65, 65, 71, 71, -1, -1, -1, -1, -1, -1, 21, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, 84, 77, 77, -1, 70, -1, -1, -1, -1, -1, -1, 81, 80, 80, 84, 83, 65, 65, 71, 71, -1, -1, -1, -1, -1, 13, 24, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, 84, 83, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, 84, 83, 65, 71]}, {"type": 3, "obj": [[2, "219_4", 398, 463, 36, 30, 2], [2, "219_4", 195, 11, 36, 30, 2], [2, "219_4", 414, 7, 36, 30, 2], [2, "219_4", 208, 27, 36, 30, 0], [2, "219_4", 31, 317, 36, 30, 0], [2, "174_4", 68, 370, 68, 33, 0], [2, "1812", 749, 138, 42, 24, 0], [2, "1812", 30, 218, 42, 24, 0], [2, "1385", 124, -11, 68, 62, 0], [2, "1386", 169, 6, 68, 67, 2], [2, "1387", 172, 55, 50, 38, 0], [2, "1812", 215, 62, 42, 24, 0], [2, "1385", 766, 82, 68, 62, 2], [2, "1812", 124, 620, 42, 24, 0], [2, "219_4", 319, 136, 36, 30, 0], [2, "219_4", 324, 96, 36, 30, 0], [2, "220_3", 250, 49, 40, 29, 0], [2, "219_4", 197, 293, 36, 30, 0], [2, "1457", 279, 45, 22, 30, 0], [2, "174_4", 194, 108, 68, 33, 2], [2, "219_4", 464, 90, 36, 30, 0], [2, "219_4", 507, 108, 36, 30, 2], [2, "219_4", 429, 95, 36, 30, 2], [2, "219_4", 616, 148, 36, 30, 0], [2, "219_4", 634, 163, 36, 30, 0], [2, "1385", 3, 316, 68, 62, 2], [2, "1385", -51, 298, 68, 62, 0], [2, "1386", -26, 261, 68, 67, 2], [2, "220_3", 397, 295, 40, 29, 0], [2, "219_4", 26, 365, 36, 30, 0], [2, "219_4", 790, 162, 36, 30, 0], [2, "220_3", 753, 281, 40, 29, 2], [2, "219_4", 604, 289, 36, 30, 2], [2, "219_4", 605, 309, 36, 30, 2], [2, "219_4", 574, 320, 36, 30, 2], [2, "1458", 307, 59, 34, 36, 0], [2, "219_4", 198, 239, 36, 30, 0], [2, "1812", 824, 441, 42, 24, 0], [2, "219_4", 38, 226, 36, 30, 2], [2, "219_4", 11, 259, 36, 30, 2], [2, "219_4", 129, 311, 36, 30, 0], [2, "219_4", 128, 69, 36, 30, 2], [2, "219_4", 57, 3, 36, 30, 0], [2, "220_3", 235, 69, 40, 29, 0], [2, "219_4", 536, 315, 36, 30, 0], [2, "219_4", 861, 203, 36, 30, 2], [2, "219_4", 616, 89, 36, 30, 0], [2, "220_3", 783, 20, 40, 29, 2], [2, "220_3", 413, 588, 40, 29, 0], [2, "220_3", 745, 521, 40, 29, 2], [2, "219_4", 859, 418, 36, 30, 2], [2, "219_4", 826, 454, 36, 30, 2], [2, "219_4", 800, 462, 36, 30, 2], [2, "220_3", 764, 457, 40, 29, 2], [2, "1817", 787, 523, 22, 24, 0], [2, "220_3", 381, 600, 40, 29, 2], [2, "219_4", 431, 557, 36, 30, 2], [2, "219_4", 252, 505, 36, 30, 2], [2, "219_4", 84, 597, 36, 30, 0], [2, "219_4", 45, 411, 36, 30, 0], [2, "219_4", 16, 724, 36, 30, 0], [2, "219_4", 540, 442, 36, 30, 2], [2, "219_4", 826, 470, 36, 30, 2], [2, "219_4", 840, 484, 36, 30, 2], [2, "219_4", 851, 471, 36, 30, 0], [2, "219_4", 865, 498, 36, 30, 0], [2, "219_4", 852, 536, 36, 30, 2], [2, "219_4", -3, 163, 36, 30, 0], [2, "219_4", 31, 180, 36, 30, 2], [2, "219_4", 55, 180, 36, 30, 0], [2, "219_4", 483, 328, 36, 30, 0], [2, "220_3", 216, 822, 40, 29, 0], [2, "219_4", 768, 408, 36, 30, 2], [2, "1458", 399, 479, 34, 36, 2], [2, "220_3", 404, 495, 40, 29, 0], [2, "219_4", 353, 439, 36, 30, 2], [2, "219_4", 353, 419, 36, 30, 0], [2, "219_4", 924, 868, 36, 30, 2], [2, "219_4", 63, 873, 36, 30, 0], [2, "219_4", 14, 627, 36, 30, 2], [2, "219_4", 17, 394, 36, 30, 2], [2, "219_4", -1, 377, 36, 30, 2], [2, "219_4", -5, 405, 36, 30, 0], [2, "1457", 11, 382, 22, 30, 0], [2, "219_4", 816, 624, 36, 30, 2], [2, "219_4", 295, 886, 36, 30, 0], [2, "219_4", 35, 481, 36, 30, 0], [2, "219_4", 140, 585, 36, 30, 0], [2, "219_4", 325, 282, 36, 30, 2], [2, "219_4", 356, 276, 36, 30, 2], [2, "219_4", 377, 277, 36, 30, 0], [2, "219_4", 267, 363, 36, 30, 0], [2, "219_4", 274, 320, 36, 30, 2], [2, "219_4", 403, 129, 36, 30, 2], [2, "219_4", 309, 309, 36, 30, 2], [2, "219_4", 374, 332, 36, 30, 0], [2, "219_4", 266, 341, 36, 30, 2], [2, "219_4", 443, 377, 36, 30, 0], [2, "219_4", 465, 394, 36, 30, 0], [2, "219_4", 503, 398, 36, 30, 2], [2, "1817", 288, 892, 22, 24, 0], [2, "1817", 54, 869, 22, 24, 0], [2, "1817", 538, 571, 22, 24, 0], [2, "1817", 11, 278, 22, 24, 0], [2, "1817", 414, 111, 22, 24, 0], [2, "219_4", 447, 918, 36, 30, 0], [2, "219_4", 467, 834, 36, 30, 2], [2, "219_4", 678, 770, 36, 30, 0], [2, "219_4", 632, 865, 36, 30, 0], [2, "219_4", 613, 845, 36, 30, 0], [2, "219_4", 557, 771, 36, 30, 0], [2, "219_4", 648, 671, 36, 30, 0], [2, "219_4", 707, 913, 36, 30, 0], [2, "127", 641, 652, 46, 82, 2], [2, "127", 604, 672, 46, 82, 2], [2, "127", 564, 691, 46, 82, 2], [2, "127", 541, 704, 46, 82, 2], [2, "222", 580, 730, 60, 53, 0], [2, "222", 667, 683, 60, 53, 0]]}, {"type": 2, "data": [23, 23, 23, 23, 34, -1, -1, -1, -1, -1, -1, -1, 21, 19, -1, 28, 29, 17, 29, 30, 27, -1, -1, -1, -1, -1, -1, 21, 20, 24, 17, 17, 17, 18, 15, -1, -1, -1, -1, -1, 23, 23, 23, 30, 31, -1, -1, -1, -1, -1, -1, -1, 25, 27, -1, 25, 26, 26, 26, 27, -1, -1, -1, -1, -1, 21, 20, 24, 23, 17, 17, 17, 17, 17, 18, 15, -1, -1, -1, -1, 23, 30, 26, 27, -1, -1, -1, -1, -1, -1, -1, 21, 20, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 23, 17, 17, 17, 17, 29, 17, 17, 17, 18, 15, -1, -1, -1, 30, 27, -1, 13, 14, 20, 14, 15, -1, 21, 20, 24, 23, 23, 18, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 36, 23, 23, 23, 30, 26, 26, 36, 17, 23, 22, -1, -1, -1, 27, -1, -1, 16, 17, 23, 17, 18, 19, 28, 23, 17, 17, 17, 17, 18, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 32, 32, 32, 27, -1, -1, 16, 17, 23, 18, 15, -1, -1, -1, -1, -1, 28, 23, 23, 23, 23, 22, 33, 32, 36, 35, 17, 35, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 17, 17, 17, 23, 18, 15, -1, -1, -1, -1, 25, 36, 35, 23, 30, 27, -1, -1, 33, 32, 32, 32, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 17, 17, 29, 17, 22, -1, -1, -1, -1, -1, 33, 32, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 35, 35, 35, 17, 17, 22, -1, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 36, 35, 35, 29, 30, 27, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 20, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 32, 32, 26, 27, -1, -1, 34, -1, -1, -1, 13, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 24, 23, 17, 17, 18, 20, 20, 20, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, -1, -1, 13, 24, 23, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 17, 17, 17, 17, 17, 23, 17, 17, 18, 19, -1, -1, 21, 20, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 23, 23, 30, 27, -1, -1, -1, -1, -1, -1, 21, 20, 24, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 34, -1, 13, 24, 17, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 26, 27, -1, -1, -1, -1, -1, 21, 20, 24, 23, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 30, 31, -1, 16, 17, 17, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 23, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 34, -1, -1, 28, 29, 17, 30, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 29, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 34, -1, -1, 25, 26, 26, 27, -1, -1, -1, -1, -1, -1, -1, 14, 15, -1, -1, -1, -1, -1, 21, 19, -1, -1, 25, 26, 26, 36, 29, 29, 29, 29, 17, 17, 17, 17, 17, 17, 17, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 17, 18, 14, 15, -1, -1, -1, 33, 31, -1, -1, -1, -1, -1, 33, 36, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 18, 20, 15, -1, -1, -1, -1, -1, -1, -1, 21, 20, 24, 23, 17, 17, 17, 18, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 36, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 18, 15, -1, -1, -1, -1, -1, 13, 24, 23, 29, 29, 35, 17, 17, 17, 22, -1, -1, -1, 13, 14, 14, 15, -1, -1, -1, -1, 28, 17, 17, 17, 17, 17, 17, 17, 17, 30, 36, 17, 17, 18, 14, 14, 15, -1, 21, 20, 17, 17, 29, 30, 32, 36, 35, 35, 34, -1, -1, -1, 16, 17, 17, 18, 15, -1, -1, 21, 24, 17, 17, 17, 17, 17, 17, 17, 30, 27, 33, 36, 35, 17, 17, 17, 18, 14, 24, 23, 17, 30, 32, 31, -1, 33, 32, 32, 31, -1, -1, -1, 33, 36, 17, 35, 34, 21, 20, 24, 23, 17, 17, 17, 17, 17, 17, 17, 34, -1, -1, 33, 32, 36, 17, 30, 26, 26, 36, 17, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 32, 32, 31, 28, 23, 17, 17, 17, 17, 17, 17, 17, 30, 32, 31, -1, -1, -1, -1, 28, 17, 18, 15, -1, 28, 29, 30, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 23, 17, 17, 17, 17, 17, 17, 35, 34, -1, -1, -1, -1, -1, -1, 33, 36, 35, 18, 20, 24, 30, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 23, 17, 17, 17, 17, 17, 17, 30, 32, 31, -1, -1, -1, -1, -1, -1, -1, 33, 32, 26, 26, 26, 27, -1, -1, -1, -1, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 23, 23, 17, 17, 23, 17, 23, 23, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, 33, 36, 23, 23, 23, 23, 35, 30, 32, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 36, 35, 18, 14, 15, -1, -1, -1, -1, -1, -1, 25, 26, 32, 32, 32, 32, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 20, 19, -1, -1, -1, -1, -1, -1, -1, 33, 32, 36, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 17, 17, 18, 14, 19, -1, -1, -1, -1, 20, 15, -1, 28, 17, 17, 17, 18, 19, -1, -1, -1, -1, -1, -1, -1, 21, 20, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 17, 17, 17, 17, 18, 14, 19, -1, -1, 17, 18, 14, 24, 17, 17, 17, 17, 18, 15, -1, -1, -1, -1, 21, 20, 24, 23, 23, 18, 20, 19, -1, -1, -1, -1, -1, -1, -1, 28, 35, 17, 17, 17, 17, 17, 17, 18, 19, -1, 17, 17, 17, 17, 17, 17, 17, 17, 17, 18, 15, -1, 21, 20, 24, 23, 17, 17, 17, 17, 17, 22, -1, -1, -1, -1, -1, -1, -1, 25, 36, 17, 17, 17, 17, 17, 29, 29, 18, 15, 29, 30, 36, 35, 17, 17, 17, 17, 17, 17, 18, 14, 24, 23, 29, 30, 26, 26, 26, 36, 35, 34, -1, -1, -1, -1, -1, -1, -1, -1, 33, 36, 35, -1, -1, -1, -1, 29, 29, 18, 26, 27, 33, 32, 36, 35, 17, 17, 17, 17, 17, 17, 17, 29, 30, 27, -1, -1, -1, 33, 32, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 32, 26, 26, 26, 26, 26, 29, 29, -1, -1, -1, -1, 33, 32, 36, 17, 17, 17, 17, 17, 17, 23, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, 28, 17, 17, 17, 17, 17, 17, 23, 18, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 17, 17, 17, 29, 29, 30, 36, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 17, 17, 29, 30, 26, 27, 33, 36, 17, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 23, 17, 17, 30, 27, -1, -1, -1, 16, 17, 17, 18, 15, -1, -1, -1, -1, -1, -1, 21, 20, 24, 23, 17, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 24, 23, 17, 17, 17, 34, -1, -1, -1, -1, 25, 36, 17, 17, 22, -1, -1, -1, -1, -1, 13, 24, 23, 17, 17, 17, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [0, 60, 59, 9, 10, 9, 10, 9, 10, 9, 9, 9, 10, 9, 9, 10, 9, 9, 10, 10, 9, 10, 53, 54, 51, 52, 9, 10, 9, 10, 9, 9, 10, 9, 10, 9, 10, 9, 9, 10, 3, 57, 56, 59, 9, 10, 12, 11, 12, 11, 11, 9, 10, 11, 11, 12, 11, 11, 12, 9, 10, 54, 50, 51, 37, 48, 11, 12, 11, 9, 11, 11, 12, 11, 12, 11, 9, 10, 11, 12, 39, 4, 57, 56, 60, 59, 10, 12, 9, 10, 9, 10, 12, 9, 10, 9, 10, 9, 10, 11, 9, 46, 5, 45, 48, 41, 11, 12, 9, 9, 10, 12, 11, 9, 10, 9, 10, 9, 9, 10, 42, 38, 39, 4, 57, 56, 60, 10, 11, 12, 11, 12, 9, 11, 12, 9, 9, 10, 12, 11, 11, 46, 0, 52, 53, 10, 9, 10, 10, 11, 9, 10, 10, 11, 9, 9, 10, 11, 11, 12, 9, 41, 42, 39, 4, 1, 52, 12, 11, 9, 10, 10, 11, 9, 10, 11, 11, 9, 9, 10, 10, 42, 43, 49, 60, 12, 11, 12, 12, 10, 11, 12, 10, 9, 11, 11, 12, 10, 9, 10, 11, 12, 41, 42, 39, 45, 48, 12, 10, 9, 10, 9, 10, 11, 9, 11, 9, 11, 9, 10, 12, 47, 46, 7, 52, 53, 9, 10, 11, 12, 9, 11, 12, 11, 9, 11, 11, 12, 11, 12, 11, 9, 9, 10, 42, 48, 47, 9, 10, 10, 12, 10, 9, 9, 10, 9, 10, 10, 9, 10, 9, 10, 46, 3, 49, 60, 59, 12, 12, 9, 10, 12, 9, 10, 9, 10, 9, 10, 9, 10, 9, 10, 11, 12, 9, 11, 12, 11, 12, 12, 9, 9, 10, 10, 9, 10, 12, 12, 11, 12, 9, 10, 42, 39, 3, 57, 56, 60, 59, 9, 10, 10, 10, 9, 10, 12, 9, 10, 11, 12, 11, 12, 11, 12, 11, 12, 11, 11, 54, 50, 60, 11, 12, 10, 9, 10, 9, 10, 9, 10, 11, 12, 12, 42, 43, 7, 0, 57, 56, 60, 9, 10, 10, 11, 12, 10, 10, 12, 9, 10, 11, 12, 9, 9, 10, 9, 10, 11, 46, 1, 57, 56, 60, 9, 10, 12, 10, 10, 11, 12, 9, 10, 11, 9, 42, 44, 39, 4, 5, 40, 11, 12, 12, 9, 9, 10, 12, 10, 9, 10, 9, 10, 11, 11, 12, 11, 9, 10, 42, 39, 1, 2, 40, 9, 10, 10, 12, 12, 10, 12, 11, 12, 11, 11, 11, 9, 42, 39, 8, 40, 9, 10, 10, 11, 11, 12, 10, 9, 10, 10, 11, 12, 9, 9, 10, 9, 10, 9, 9, 42, 44, 44, 48, 11, 12, 12, 9, 10, 10, 9, 10, 12, 10, 11, 11, 11, 12, 58, 7, 40, 11, 12, 12, 9, 10, 10, 9, 10, 12, 12, 9, 10, 9, 11, 12, 11, 12, 11, 11, 11, 12, 10, 9, 10, 59, 54, 56, 56, 60, 10, 12, 11, 12, 11, 12, 11, 12, 58, 1, 40, 11, 12, 10, 11, 12, 12, 11, 12, 9, 10, 11, 12, 11, 9, 10, 9, 9, 10, 9, 11, 11, 12, 11, 12, 54, 55, 1, 2, 52, 12, 12, 11, 9, 11, 12, 9, 54, 55, 4, 40, 9, 10, 12, 10, 11, 9, 10, 11, 11, 12, 9, 10, 9, 11, 9, 9, 10, 12, 11, 11, 12, 11, 12, 9, 51, 3, 0, 1, 52, 9, 9, 9, 11, 54, 50, 50, 51, 5, 45, 48, 11, 12, 12, 12, 10, 11, 12, 11, 12, 10, 9, 10, 11, 12, 11, 11, 12, 9, 9, 10, 12, 11, 9, 9, 58, 6, 3, 4, 52, 53, 11, 9, 54, 51, 1, 2, 7, 45, 48, 47, 9, 10, 11, 12, 12, 11, 12, 10, 12, 12, 11, 9, 10, 10, 10, 9, 10, 11, 11, 12, 9, 10, 9, 10, 42, 39, 6, 7, 49, 60, 59, 11, 58, 3, 4, 45, 44, 48, 47, 12, 11, 12, 9, 9, 10, 11, 11, 12, 9, 10, 9, 10, 12, 9, 10, 9, 10, 9, 10, 9, 10, 10, 11, 9, 10, 46, 3, 4, 3, 57, 56, 50, 55, 45, 44, 48, 10, 10, 9, 10, 9, 10, 11, 11, 12, 9, 11, 12, 11, 12, 9, 10, 9, 11, 12, 11, 12, 11, 12, 11, 12, 9, 10, 11, 9, 42, 38, 39, 6, 7, 0, 1, 37, 48, 9, 11, 12, 12, 11, 12, 11, 9, 10, 9, 10, 11, 12, 10, 11, 12, 11, 12, 11, 12, 11, 9, 10, 9, 9, 10, 9, 10, 12, 9, 11, 9, 10, 46, 1, 2, 1, 2, 40, 41, 11, 12, 12, 9, 10, 10, 10, 11, 12, 11, 12, 9, 10, 10, 9, 10, 9, 9, 11, 12, 9, 11, 12, 9, 11, 12, 11, 12, 9, 9, 10, 10, 12, 46, 4, 0, 1, 2, 52, 11, 9, 10, 12, 11, 12, 12, 12, 11, 12, 9, 10, 11, 12, 12, 9, 10, 11, 11, 9, 9, 11, 12, 9, 11, 9, 9, 9, 10, 9, 10, 12, 53, 54, 6, 7, 3, 4, 5, 49, 56, 60, 12, 10, 9, 11, 12, 10, 9, 10, 11, 9, 9, 10, 9, 11, 12, 9, 10, 10, 11, 12, 11, 11, 9, 11, 11, 11, 12, 9, 53, 54, 50, 51, 5, 6, 6, 7, 8, 3, 4, 57, 60, 12, 11, 12, 11, 12, 11, 12, 9, 10, 11, 12, 9, 9, 10, 9, 10, 12, 9, 9, 10, 10, 11, 12, 9, 9, 53, 54, 50, 51, 5, 7, 45, 38, 44, 44, 38, 39, 7, 3, 57, 60, 59, 12, 11, 12, 9, 10, 9, 9, 10, 9, 9, 11, 12, 11, 12, 9, 10, 11, 9, 10, 12, 11, 11, 54, 50, 51, 6, 7, 8, 5, 40, 41, 11, 12, 41, 42, 38, 39, 3, 57, 60, 10, 11, 12, 11, 12, 9, 10, 9, 10, 10, 9, 10, 9, 10, 11, 12, 10, 11, 12, 9, 10, 59, 58, 0, 1, 3, 6, 45, 44, 48, 47, 10, 9, 9, 9, 41, 42, 43, 3, 40, 12, 12, 12, 11, 9, 10, 12, 11, 12, 9, 10, 12, 11, 12, 9, 11, 12, 11, 9, 10, 10, 54, 55, 3, 4, 45, 44, 48, 47, 47, 11, 12, 11, 11, 11, 9, 47, 46, 0, 40, 9, 10, 10, 10, 11, 9, 10, 9, 9, 10, 12, 10, 9, 10, 9, 11, 9, 10, 9, 10, 9, 46, 1, 45, 44, 48, 47, 9, 11, 12, 10, 9, 9, 10, 9, 11, 12, 46, 3, 40, 11, 12, 12, 9, 9, 10, 9, 10, 11, 12, 11, 9, 11, 9, 10, 53, 54, 56, 60, 12, 11, 42, 38, 48, 47, 10, 9, 10, 12, 11, 12, 11, 11, 12, 11, 53, 54, 51, 45, 48, 10, 10, 10, 9, 10, 12, 11, 9, 9, 10, 9, 10, 9, 10, 53, 54, 51, 1, 57, 56, 60, 9, 10, 9, 11, 12, 9, 10, 9, 10, 9, 10, 9, 10, 9, 54, 51, 4, 40, 11, 12, 12, 12, 11, 9, 10, 9, 10, 11, 12, 11, 12, 11, 53, 54, 51, 2, 2, 6, 7, 52, 9, 10, 11, 12, 12, 11, 12, 11, 12, 11, 12, 11, 12, 9, 46, 1, 1, 40, 11, 9, 10, 9, 10, 10, 9, 10, 9, 10, 10, 9, 10, 54, 50, 51, 4, 5, 5, 45, 44, 48, 11, 12, 11, 12, 12, 9, 9, 10, 9, 10, 10, 10, 9, 10, 46, 4, 4, 49, 60, 59, 9, 10, 10, 9, 10, 12, 10, 9, 10, 11, 12, 46, 0, 1, 2, 8, 37, 48, 47, 12, 11, 12, 11, 9, 10, 11, 11, 12, 11, 12, 9, 10, 9, 9, 42, 43, 0, 0, 57, 60, 59, 12, 12, 11, 12, 11, 12, 11, 12, 9, 10, 42, 43, 4, 0, 1, 40, 41, 11, 12, 9, 10, 9, 10, 9, 10, 11, 9, 9, 9, 11, 12, 11, 11, 47, 42, 43, 0, 1, 57, 56, 50, 50, 50, 6, 7, 6, 7, 8, 11, 12, 9, 42, 39, 0, 1, 52, 9, 10, 10, 11, 12, 9, 10, 11, 12, 10, 11, 11, 11, 9, 10, 9, 10, 9, 9, 46, 3, 0, 1, 2, 6, 6, 6, 7, 8, 0, 1, 2, 0, 0, 11, 12, 46, 0, 1, 57, 60, 12, 10, 11, 12, 11, 9, 10, 11, 12, 9, 10, 10, 11, 12, 11, 12, 11, 9, 42, 39, 0, 1, 2, 7, 8, 3, 4, 5, 3, 4, 5, 0, 3, 11, 11, 42, 44, 43, 5, 57, 60, 12, 10, 9, 10, 11, 12, 9, 10, 11, 12, 12, 9, 10, 11, 11, 9, 11, 12, 42, 39, 0, 1, 2, 1, 0, 1, 2, 6, 7, 8, 3, 6, 9, 9, 9, 10, 42, 43, 1, 57, 60, 12, 11, 12, 12, 11, 11, 12, 9, 9, 9, 10, 12, 9, 10, 9, 10, 11, 12, 9, 38, 39, 1, 2, 3, 4, 5, 0, 1, 2, 6, 7, 11, 11, 11, 12, 9, 46, 4, 1, 40, 10, 9, 10, 9, 10, 9, 10, 9, 10, 11, 12, 9, 11, 12, 11, 12, 9, 10, 11, 41, 42, 38, 39, 6, 7, 8, 3, 4, 5, 0, 1, 11, 12, 9, 10, 11, 42, 39, 4, 49, 60, 11, 12, 11, 12, 11, 12, 11, 10, 10, 10, 11, 9, 10, 9, 10, 11, 12, 11, 12, 11, 41, 42, 43, 3, 4, 6, 7, 8, 3, 4]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}