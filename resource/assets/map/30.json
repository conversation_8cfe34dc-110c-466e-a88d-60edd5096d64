{"mW": 720, "mH": 648, "tW": 24, "tH": 24, "tiles": [["111", 0, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["111", 2, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["369_2", 0, 3, 3], ["369_2", 2, 3, 3], ["369_2", 1, 3, 3], ["369_2", 3, 3, 3], ["315_2", 0, 3, 3]], "layers": [{"type": 3, "obj": [[2, "713", 414, 228, 18, 27, 2], [2, "705", 597, 513, 74, 157, 0], [2, "705", 670, 513, 74, 157, 2], [2, "1269", 683, 602, 34, 39, 2], [2, "1269", 626, 599, 34, 39, 0], [2, "705", 0, 490, 74, 157, 2], [2, "1269", 12, 579, 34, 39, 2]]}, {"type": 4, "obj": [[2, "47", 669, 98, 54, 63, 0], [2, "4", 161, 129, 122, 119, 2], [2, "73", 265, 206, 46, 72, 0], [2, "73", 375, 253, 46, 72, 0], [2, "714", 387, 196, 54, 132, 0], [2, "1272", 540, 237, 62, 109, 0], [2, "1272", 603, 237, 62, 109, 2], [2, "125", 50, 368, 18, 70, 0], [2, "710", 377, 414, 38, 62, 0]]}, {"type": 3, "obj": [[2, "253", 127, 354, 92, 53, 0], [2, "325_1", 176, 617, 50, 37, 2], [2, "422_1", 634, 135, 16, 14, 0], [2, "313_3", 415, 481, 70, 44, 2], [2, "173_1", 530, 3, 70, 45, 0], [2, "173_1", 71, 25, 70, 45, 2], [2, "1358", 107, 250, 62, 34, 2], [2, "1358", 439, 327, 62, 34, 0], [2, "488", 492, 268, 32, 51, 2], [2, "488", 465, 281, 32, 51, 2], [2, "488", 438, 295, 32, 51, 2], [2, "488", 515, 273, 32, 51, 0], [2, "396", 571, 333, 34, 59, 0], [2, "1366", 368, 457, 52, 39, 0], [2, "598", 413, 495, 18, 22, 0], [2, "598", 402, 506, 18, 22, 2], [2, "1358", 484, 350, 62, 34, 0], [2, "1358", 638, 426, 62, 34, 0], [2, "1358", 491, 18, 62, 34, 2], [2, "411", -25, -18, 44, 40, 2], [2, "1358", 622, 389, 62, 34, 2], [2, "1358", 450, 37, 62, 34, 2], [2, "1358", 588, 404, 62, 34, 0], [2, "1357", 310, -10, 58, 32, 2], [2, "313_3", 196, 239, 70, 44, 2], [2, "173_1", 583, -2, 70, 45, 0], [2, "594", 510, 33, 52, 46, 2], [2, "598", 95, 617, 18, 22, 0], [2, "5", 200, 205, 42, 66, 2], [2, "327", 160, 281, 30, 22, 0], [2, "313_3", 584, 416, 70, 44, 2], [2, "313_3", 107, 173, 70, 44, 2], [2, "1358", 535, 377, 62, 34, 0], [2, "12", 66, 606, 28, 26, 7], [2, "12", 49, 622, 26, 28, 0], [2, "420", 592, 441, 16, 13, 0], [2, "325", 184, 271, 50, 37, 0], [2, "22", -13, 409, 62, 38, 0], [2, "411", 66, -14, 44, 40, 2], [2, "413", 36, -49, 44, 72, 0], [2, "366", 12, -23, 32, 48, 0], [2, "420", 71, 22, 16, 13, 0], [2, "420", -8, 434, 16, 13, 2], [2, "1358", 410, -4, 62, 34, 0], [2, "1358", 394, 4, 62, 34, 0], [2, "1358", 422, -10, 62, 34, 0], [2, "1356", 436, -28, 40, 33, 2], [2, "1356", 462, -16, 40, 33, 2], [2, "1356", 485, -6, 40, 33, 2], [2, "1356", 481, -22, 40, 33, 2], [2, "1356", 405, -31, 40, 33, 0], [2, "1356", 383, -20, 40, 33, 0], [2, "1356", 360, -9, 40, 33, 0], [2, "1356", 366, 7, 40, 33, 2], [2, "1356", 392, 19, 40, 33, 2], [2, "1356", 412, 28, 40, 33, 2], [2, "1355", 443, -10, 36, 47, 2], [2, "1359", 406, -2, 22, 19, 0], [2, "1356", 484, 6, 40, 33, 0], [2, "1356", 460, 18, 40, 33, 0], [2, "1356", 468, -1, 40, 33, 0], [2, "1356", 436, 29, 40, 33, 0], [2, "1358", 364, 29, 62, 34, 0], [2, "422", 577, 426, 16, 14, 0], [2, "422", 631, 426, 16, 14, 0], [2, "21", 682, 503, 28, 24, 0], [2, "11", 337, 8, 32, 29, 0], [2, "327", 434, 507, 30, 22, 0], [2, "328", 482, 23, 32, 29, 0], [2, "327", 18, 510, 30, 22, 0], [2, "327", 40, 200, 30, 22, 2], [2, "327", 481, 391, 30, 22, 0], [2, "422", 130, 264, 16, 14, 0], [2, "1358", 252, -12, 62, 34, 0], [2, "1358", 304, 15, 62, 34, 0], [2, "327", 308, 30, 30, 22, 0], [2, "1358", 417, 54, 62, 34, 0], [2, "420", 424, 49, 16, 13, 0], [2, "116", 449, 64, 46, 39, 0], [2, "328_1", 143, 367, 32, 29, 0], [2, "173_1", 40, 52, 70, 45, 0], [2, "174", 82, 56, 68, 33, 1], [2, "422_1", 53, 49, 16, 14, 0], [2, "594", 8, 63, 52, 46, 2], [2, "1366", 53, 359, 52, 39, 2], [2, "710", 62, 318, 38, 62, 2], [2, "339", -1, 57, 22, 22, 0], [2, "325_1", 355, 375, 50, 37, 2], [2, "174", 556, 17, 68, 33, 1], [2, "422_1", 543, 0, 16, 14, 0], [2, "5", 159, 205, 42, 66, 0], [2, "219", 145, 252, 36, 30, 0], [2, "1356", 143, 596, 40, 33, 0], [2, "1356", 150, 600, 40, 33, 0], [2, "1356", 157, 604, 40, 33, 0], [2, "1356", 117, 608, 40, 33, 0], [2, "1356", 124, 612, 40, 33, 0], [2, "1356", 131, 616, 40, 33, 0], [2, "1356", 149, 616, 40, 33, 0], [2, "1356", 156, 620, 40, 33, 0], [2, "1356", 163, 624, 40, 33, 0], [2, "116_2", 74, 619, 46, 39, 2], [2, "56_2", 118, 594, 76, 47, 0], [2, "598", 29, 630, 18, 22, 2], [2, "90", 246, 218, 28, 36, 0], [2, "90", 196, 259, 36, 28, 5], [2, "420", 393, 383, 16, 13, 0], [2, "594", 3, 452, 52, 46, 2], [2, "253", 318, 65, 92, 53, 2], [2, "422", 272, 1, 16, 14, 0], [2, "422_1", 695, 513, 16, 14, 0], [2, "1358", 653, 452, 62, 34, 2], [2, "1358", 600, 478, 62, 34, 2], [2, "1358", 547, 503, 62, 34, 2], [2, "1358", 495, 529, 62, 34, 2], [2, "1358", 480, 555, 62, 34, 0], [2, "1358", 494, 581, 62, 34, 2], [2, "1358", 444, 606, 62, 34, 2], [2, "1358", 393, 631, 62, 34, 2], [2, "396", 556, 324, 34, 59, 0], [2, "99", 575, 337, 20, 32, 0], [2, "1271", 541, 316, 16, 54, 0], [2, "1271", 550, 325, 16, 54, 0], [2, "1271", 648, 319, 16, 54, 0], [2, "1271", 642, 327, 16, 54, 0], [2, "1268", 556, 370, 48, 27, 2], [2, "99", 562, 333, 20, 32, 0], [2, "1271", 633, 334, 16, 54, 0], [2, "1271", 620, 340, 16, 54, 0], [2, "1271", 607, 342, 16, 54, 0], [2, "1271", 594, 343, 16, 54, 0], [2, "1269", 623, 336, 34, 39, 2], [2, "1273", 592, 345, 14, 27, 2], [2, "422_1", 648, 380, 16, 14, 0], [2, "116", 640, 393, 46, 39, 0], [2, "116", 665, 406, 46, 39, 0], [2, "598", 679, 445, 18, 22, 2], [2, "422_1", 708, 447, 16, 14, 0], [2, "1366", 673, 221, 52, 39, 0], [2, "710", 682, 180, 38, 62, 0], [2, "339", 640, 237, 22, 22, 0], [2, "488", 655, 176, 32, 51, 0], [2, "1367", 677, 191, 52, 40, 0], [2, "488", 240, 229, 32, 51, 0], [2, "488", 125, 204, 32, 51, 2], [2, "488", 98, 217, 32, 51, 2], [2, "488", 699, 265, 32, 51, 2], [2, "488", 673, 278, 32, 51, 2], [2, "488", 646, 291, 32, 51, 2], [2, "22", 666, 368, 62, 38, 0], [2, "420", 671, 393, 16, 13, 2], [2, "113", 683, 348, 26, 33, 0], [2, "328_1", 666, 326, 32, 29, 0], [2, "1273", 549, 332, 14, 27, 2], [2, "366", 598, -27, 32, 48, 0], [2, "325_1", 621, 141, 50, 37, 2], [2, "420", 659, 149, 16, 13, 0], [2, "1358", 56, 276, 62, 34, 2], [2, "488", 72, 230, 32, 51, 2], [2, "1358", 4, 301, 62, 34, 2], [2, "488", 44, 243, 32, 51, 2], [2, "488", 17, 256, 32, 51, 2], [2, "488", -9, 269, 32, 51, 2], [2, "763", 114, 269, 32, 31, 0], [2, "115", 129, 271, 16, 37, 2], [2, "114", 113, 281, 18, 32, 0], [2, "763", 77, 289, 32, 31, 0], [2, "488", 96, 313, 32, 51, 2], [2, "1367", 49, 328, 52, 40, 2], [2, "488", 24, 346, 32, 51, 2], [2, "339", 121, 371, 22, 22, 0], [2, "411", 29, -5, 44, 40, 2], [2, "411", -21, 2, 44, 40, 2], [2, "328", 12, 22, 32, 29, 0], [2, "85", -20, 305, 48, 53, 0], [2, "125", 3, 329, 18, 70, 0], [2, "113", 4, 389, 26, 33, 2], [2, "313_3", 320, 488, 70, 44, 0], [2, "411", 561, -10, 44, 40, 2]]}, {"type": 2, "data": [-1, -1, -1, 46, 45, -1, 0, 1, 19, 18, -1, -1, 36, 40, 39, 40, 28, 40, 40, -1, 34, 40, 46, 45, 40, 43, 42, -1, -1, -1, 41, 47, 41, 43, 42, -1, 9, 16, 22, 21, -1, -1, -1, -1, -1, 47, 46, 40, 40, 40, 41, 40, 41, 42, -1, 43, 42, -1, -1, -1, 38, 44, 42, -1, -1, -1, 6, 17, 10, 5, 19, 18, -1, -1, -1, 44, 43, 47, 40, 40, 41, 47, 46, -1, -1, 0, 1, 18, -1, -1, 31, 32, 41, 42, -1, -1, -1, 3, 22, 22, 22, 5, 19, 19, 18, -1, -1, 36, 37, 37, 38, 36, 42, -1, 0, 23, 4, 21, -1, -1, 43, 37, 42, -1, -1, -1, -1, 3, 22, 22, 22, 22, 22, 22, 21, -1, -1, -1, -1, 36, 42, -1, -1, -1, 9, 11, 7, 8, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 11, 13, 17, 22, 22, 10, 5, 1, 19, 18, -1, -1, -1, -1, -1, -1, 6, 8, -1, 24, 25, 30, 18, -1, -1, 0, 1, 23, 22, 22, 15, -1, 3, 4, 22, 22, 22, 4, 4, 5, 18, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 43, -1, -1, -1, 6, 7, 17, 16, 16, 15, -1, 9, 22, 22, 22, 22, 22, 16, 11, 12, 20, 19, 19, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 13, 13, 12, -1, 6, 7, 17, 22, 22, 11, 13, 12, -1, 9, 16, 22, 22, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, -1, -1, 31, 30, 14, 17, 16, 15, -1, -1, -1, 14, 13, 17, 16, 15, -1, -1, -1, 24, 25, 31, -1, -1, -1, -1, -1, 24, 25, 40, 46, 30, 40, 29, 30, 14, 13, 12, -1, -1, -1, -1, -1, 6, 7, 12, -1, -1, -1, 36, 37, 47, -1, -1, -1, 32, 31, -1, 41, 43, 47, 42, 43, 43, 42, 20, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 30, 32, 31, 31, 43, 43, 38, -1, 44, 42, -1, -1, 20, 23, 5, 32, -1, 31, 30, -1, -1, -1, -1, -1, -1, -1, 31, -1, 24, 25, 31, 30, 36, 37, 38, -1, -1, -1, -1, -1, -1, 0, 23, 22, 16, 44, 43, 43, 42, 30, -1, -1, 32, 31, 25, 25, 25, -1, 36, 37, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 22, 22, 11, 12, -1, -1, -1, -1, -1, 32, 35, 46, 46, 46, 40, 31, 30, -1, 38, -1, 32, 31, 30, -1, -1, -1, -1, -1, -1, 6, 7, 13, 12, -1, -1, -1, -1, -1, -1, 39, 46, 40, 40, 34, 33, 46, 45, -1, 36, -1, 36, 37, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 43, 47, 46, 40, 46, 43, 42, 47, -1, 24, 30, -1, -1, 20, 19, 18, -1, -1, -1, -1, 20, 19, 18, -1, -1, 20, 19, 18, -1, -1, -1, 44, 43, 43, 43, -1, -1, 35, 43, 43, 42, 20, 19, 23, 22, 21, -1, -1, -1, 20, 23, 22, 21, -1, -1, 6, 13, 12, 0, 1, 2, -1, -1, -1, 36, 37, 37, 37, 24, 31, 30, 14, 13, 17, 22, 5, 2, -1, -1, 14, 7, 13, 12, 32, 31, 30, -1, -1, 3, 4, 5, 18, -1, -1, -1, -1, 36, 37, 37, 37, 38, -1, -1, 14, 13, 13, 8, -1, -1, -1, -1, -1, -1, 36, 37, 42, -1, 20, 23, 4, 4, 21, -1, -1, -1, -1, -1, -1, 42, -1, -1, 0, 1, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 17, 16, 11, 12, -1, -1, -1, -1, 32, 30, -1, -1, -1, 9, 10, 16, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 13, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, 13, 17, 16, 15, -1, -1, -1, -1, -1, -1, 20, 19, 19, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 13, 8, -1, -1, -1, -1, -1, 20, 23, 22, 22, 5, 19, 2, -1, -1, -1, -1, -1, -1, -1, -1, 40, -1, 40, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 17, 16, 10, 10, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 34, 34, 30, 31, 30, -1, 31, 30, -1, -1, -1, -1, -1, -1, 14, 13, 13, 13, 13, 8, -1, -1, -1, 24, 25, 34, -1, -1, 47]}, {"type": 3, "obj": [[2, "173_1", 14, 365, 70, 45, 0], [2, "313_3", 401, 440, 70, 44, 2], [2, "313_3", 325, 442, 70, 44, 0], [2, "253", 550, 477, 92, 53, 2], [2, "253", 486, 388, 92, 53, 0], [2, "597", 16, 618, 34, 26, 0], [2, "597", 3, 634, 34, 26, 2], [2, "597", 7, 460, 34, 26, 0], [2, "597", -4, 484, 34, 26, 2], [2, "597", 39, 477, 34, 26, 0], [2, "173_1", 552, 345, 70, 45, 0], [2, "597", 512, 32, 34, 26, 0], [2, "597", 497, 56, 34, 26, 2], [2, "597", 10, 63, 34, 26, 0], [2, "597", -5, 87, 34, 26, 2], [2, "253", 586, 565, 92, 53, 2], [2, "253", 584, 519, 92, 53, 0], [2, "253", 569, 580, 92, 53, 2], [2, "1358", 333, 450, 62, 34, 2], [2, "1358", 400, 451, 62, 34, 0], [2, "1358", 383, 457, 62, 34, 0], [2, "1358", 365, 461, 62, 34, 0], [2, "1358", 353, 470, 62, 34, 0], [2, "1358", 343, 478, 62, 34, 0], [2, "1358", 395, 484, 62, 34, 2], [2, "1358", 330, 486, 62, 34, 0], [2, "597", 384, 502, 34, 26, 2], [2, "596", 424, 502, 14, 15, 2], [2, "173_1", 71, 345, 70, 45, 2], [2, "173_1", 45, 370, 70, 45, 2], [2, "313_3", 391, 298, 70, 44, 2]]}, {"type": 2, "data": [59, 58, 49, 49, 61, 79, 71, 76, 75, 77, 76, 80, 79, 52, 52, 52, 52, 51, 52, -1, 61, 61, 61, 61, 61, 52, 52, 78, 84, 48, 62, 61, 61, 52, 70, 71, 75, 92, 87, 88, 84, 77, 80, 52, 70, 79, 78, 51, 52, 61, 61, 61, 61, 52, 52, 70, 71, 75, 87, 51, 61, 61, 61, 61, 61, 53, 58, 57, 88, 88, 85, 88, 66, 80, 79, 79, 79, 79, 52, 52, 52, 52, 52, 52, 71, 67, 68, 86, 91, 69, 80, 61, 52, 52, 52, 52, 79, 78, 85, 88, 88, 84, 48, 62, 79, 79, 52, 79, 70, 71, 61, 52, 52, 52, 78, 89, 87, 89, 59, 62, 69, 52, 71, 80, 79, 71, 76, 75, 72, 49, 81, 87, 51, 52, 70, 52, 52, 71, 67, 80, 79, 70, 71, 76, 75, 48, 49, 49, 62, 79, 66, 67, 68, 77, 76, 75, 84, 85, 77, 65, 63, 92, 66, 67, 80, 79, 71, 75, 87, 77, 76, 67, 68, 89, 48, 62, 52, 79, 79, 79, 87, 88, 84, 88, 88, 87, 87, 88, 89, 84, 85, 86, 90, 91, 77, 76, 75, 84, 85, 86, 84, 85, 86, 92, 51, 79, 52, 79, 79, 79, 48, 49, 50, 88, 88, 88, 88, 91, 92, 87, 88, 89, 85, 86, 90, 87, 88, 87, 88, 89, 87, 88, 89, 86, 77, 76, 80, 79, 79, 79, 51, 52, 53, 57, 92, 90, 91, 92, 84, 92, 88, 92, 84, 85, 84, 84, 85, 90, 91, 92, 90, 91, 92, 89, 88, 48, 62, 79, 79, 52, 62, 61, 79, 78, 84, 59, 58, 49, 88, 58, 58, 57, 87, 88, 87, 87, 84, 90, 91, 92, 90, 91, 92, 92, 88, 51, 52, 79, 52, 52, 52, 79, 79, 53, 58, 62, 70, 52, 70, 79, 78, 52, 58, 57, 84, 48, 49, 50, 59, 58, 58, 57, 88, 48, 49, 62, 80, 79, 52, 52, 52, 52, 52, 52, 52, 70, 61, 79, 79, 79, 70, 70, 61, 60, 87, 51, 52, 53, 62, 61, 70, 60, 88, 66, 67, 88, 51, 79, 79, 79, 61, 52, 52, 61, 61, 61, 61, 71, 75, 77, 76, 76, 76, 75, 87, 51, 52, 61, 61, 70, 61, 53, 49, 50, 59, 58, 54, 79, 79, 79, 61, 61, 70, 52, 52, 52, 71, 68, 86, 84, 85, 86, 90, 90, 90, 66, 67, 76, 67, 80, 70, 70, 52, 52, 52, 61, 61, 79, 79, 79, 61, 61, 52, 52, 52, 52, 53, 58, 58, 57, 84, 85, 84, 72, 73, 74, 83, 82, 81, 66, 80, 52, 52, 52, 71, 80, 52, 79, 79, 79, 52, 61, 52, 52, 52, 61, 70, 70, 71, 75, 87, 48, 57, 69, 70, 71, 80, 79, 78, 48, 62, 52, 52, 52, 53, 62, 52, 79, 79, 79, 61, 61, 52, 52, 70, 71, 67, 76, 75, 59, 58, 62, 78, 66, 67, 68, 77, 76, 75, 69, 70, 52, 52, 52, 52, 52, 52, 61, 79, 61, 52, 52, 52, 52, 52, 78, 92, 48, 49, 62, 61, 71, 75, 88, 88, 84, 85, 87, 88, 66, 67, 80, 79, 79, 52, 71, 80, 61, 79, 79, 52, 52, 61, 52, 71, 68, 84, 69, 70, 79, 79, 78, 85, 88, 88, 87, 88, 90, 91, 88, 89, 69, 79, 79, 71, 80, 80, 61, 79, 79, 52, 52, 52, 70, 78, 86, 87, 66, 67, 67, 76, 75, 88, 88, 72, 58, 58, 58, 81, 91, 92, 51, 79, 79, 53, 58, 62, 61, 79, 79, 52, 52, 79, 79, 78, 89, 90, 48, 49, 49, 49, 49, 57, 92, 69, 70, 52, 52, 53, 81, 88, 77, 80, 79, 70, 52, 52, 79, 52, 52, 70, 71, 76, 76, 75, 92, 90, 51, 52, 61, 79, 79, 53, 57, 66, 67, 67, 80, 79, 78, 48, 49, 62, 61, 79, 60, 69, 79, 52, 52, 79, 78, 86, 84, 84, 85, 86, 66, 67, 80, 52, 79, 61, 53, 58, 57, 85, 77, 76, 75, 51, 52, 70, 61, 61, 53, 62, 61, 61, 52, 79, 53, 57, 59, 58, 58, 57, 88, 89, 69, 70, 79, 70, 70, 61, 60, 88, 89, 89, 88, 66, 80, 70, 52, 52, 52, 52, 52, 79, 52, 79, 79, 53, 62, 61, 61, 60, 84, 85, 66, 67, 67, 80, 79, 79, 78, 91, 92, 86, 72, 58, 62, 70, 52, 52, 52, 61, 52, 52, 52, 79, 79, 79, 61, 61, 61, 53, 58, 57, 89, 84, 85, 77, 76, 76, 75, 87, 88, 89, 51, 52, 70, 52, 52, 52, 52, 52, 52, 79, 79, 79, 52, 52, 52, 52, 52, 52, 61, 53, 57, 85, 86, 86, 84, 85, 86, 59, 58, 58, 62, 61, 52, 52, 52, 52, 52, 52, 52, 61, 61]}], "blocks": [1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1]}