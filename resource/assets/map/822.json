{"mW": 960, "mH": 720, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2]], "layers": [{"type": 2, "data": [47, 47, 47, 47, 39, 47, 39, 40, 37, 47, 40, 41, 49, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 41, 42, 44, 41, 49, 48, 39, 31, 31, 47, 47, 31, 47, 47, 47, 47, 47, 47, 47, 47, 35, 43, 35, 36, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 49, 37, 39, 40, 43, 49, 43, 48, 47, 47, 47, 47, 31, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, 44, 43, 37, 47, 45, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 33, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 47, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 48, 39, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 47, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 39, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 32, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 32, 31, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 47, 47, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 43, 48, 47, 47, 31, 24, 25, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, -1, 47, 47, 47, 31, 30, -1, -1, -1, -1, -1, -1, 28, 27, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 47, 40, 35, 36, -1, -1, -1, -1, -1, 28, 32, 39, 39, 29, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 35, 36, -1, -1, -1, -1, -1, -1, -1, 44, 48, 47, 47, 40, 35, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 49, 49, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 19, 25, 25, 32, 31, 30, -1, -1, -1, -1, 18, 19, 33, 33, 33, 33, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 23, 39, 39, 39, 40, 36, -1, 28, 27, -1, 22, 39, 39, 39, 39, 39, 39, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 37, 47, 47, 47, 24, 33, 33, 32, 31, 28, 32, 39, 39, 39, 39, 40, 35, 42, -1, -1, -1, -1, 28, 25, 25, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 32, 31, 31, 23, 47, 23, 23, 23, 23, 32, 39, 23, 47, 47, 45, 36, -1, -1, -1, -1, -1, 28, 32, 31, 31, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 32, 47, 47, 47, 47, 23, 23, 47, 47, 47, 31, 31, 31, 31, 47, 24, 19, 20, -1, -1, 28, 27, 32, 31, 31, 31, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 48, 47, 47, 47, 47, 40, 49, 49, 48, 47, 47, 31, 31, 31, 31, 47, 31, 31, 24, 33, 33, 32, 31, 31, 31, 31, 31, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 43, 49, 49, 36, -1, -1, 44, 48, 47]}, {"type": 4, "obj": [[2, "1310_2", 60, 76, 18, 29, 2], [2, "1308_2", 569, 110, 22, 37, 0], [2, "1308_2", 637, 134, 22, 37, 0], [2, "1308_2", 856, 327, 22, 37, 0], [2, "1308_2", 937, 343, 22, 37, 0], [2, "1308_2", 912, 351, 22, 37, 0], [2, "1310_2", 74, 403, 18, 29, 0], [2, "1308_2", 85, 396, 22, 37, 0], [2, "1308_2", 56, 400, 22, 37, 0], [2, "1308_2", 254, 419, 22, 37, 0], [2, "1308_2", 367, 428, 22, 37, 0], [2, "1310_2", 291, 441, 18, 29, 2], [2, "1308_2", 307, 437, 22, 37, 0], [2, "1310_2", 274, 457, 18, 29, 2], [2, "1308_2", 327, 452, 22, 37, 0], [2, "1310_2", 167, 625, 18, 29, 0], [2, "1308_2", 178, 618, 22, 37, 0], [2, "1308_2", 149, 622, 22, 37, 0]]}, {"type": 3, "obj": [[2, "214_3", 863, 359, 54, 40, 2], [2, "214_3", 273, 428, 54, 40, 2], [2, "213_3", 900, 551, 64, 45, 0], [2, "1149", 596, 618, 40, 82, 2], [2, "1149", 624, 620, 40, 82, 0], [2, "216", 442, 599, 46, 46, 2], [2, "1149", 514, 635, 40, 82, 0], [2, "1149", 489, 632, 40, 82, 2], [2, "206", 526, 389, 66, 40, 2], [2, "213_3", 112, 425, 64, 45, 0], [2, "1144", 142, 428, 114, 70, 2], [2, "1148", 389, 441, 46, 108, 0], [2, "214_3", 419, 492, 54, 40, 0], [2, "208_3", 412, 509, 78, 40, 2], [2, "214_3", 237, 530, 54, 40, 2], [2, "214_3", 214, 582, 54, 40, 2], [2, "214_3", 432, 413, 54, 40, 0], [2, "1149", 751, 372, 40, 82, 0], [2, "208_3", 760, 437, 78, 40, 1], [2, "214_3", 83, 596, 54, 40, 0], [2, "213_3", 161, 594, 64, 45, 2], [2, "1149", 277, 460, 40, 82, 2], [2, "208_3", 519, 376, 78, 40, 3], [2, "214_3", 254, 180, 54, 40, 2], [2, "216", 447, 335, 46, 46, 2], [2, "214_3", 812, 357, 54, 40, 2], [2, "214_3", 599, 300, 54, 40, 0], [2, "216", 696, 372, 46, 46, 2], [2, "213_3", 723, 366, 64, 45, 2], [2, "213_3", 669, 344, 64, 45, 0], [2, "216", 771, 363, 46, 46, 2], [2, "214_3", 298, 125, 54, 40, 0], [2, "1150", 793, 10, 48, 85, 0], [2, "216", 486, 237, 46, 46, 0], [2, "1150", 37, 115, 48, 85, 0], [2, "1150", 889, 22, 48, 85, 2], [2, "1150", -13, 604, 48, 85, 2], [2, "1149", 320, -12, 40, 82, 0], [2, "1150", 286, -6, 48, 85, 0], [2, "1149", 612, 144, 40, 82, 0], [2, "1149", 582, 143, 40, 82, 2], [2, "1149", 736, 40, 40, 82, 2], [2, "1149", 921, 54, 40, 82, 2], [2, "1150", 10, 174, 48, 85, 0], [2, "214_3", 487, 225, 54, 40, 2], [2, "216", 537, 111, 46, 46, 0], [2, "213_3", 490, 118, 64, 45, 2], [2, "213_3", 348, 131, 64, 45, 0], [2, "1150", 763, 39, 48, 85, 0], [2, "216", 631, 138, 46, 46, 2], [2, "214_3", 644, 167, 54, 40, 0], [2, "214_3", 671, 48, 54, 40, 2], [2, "214_3", 395, 23, 54, 40, 0], [2, "214_3", 349, 163, 54, 40, 0], [2, "1309_2", 393, 153, 20, 32, 0], [2, "1310_2", 48, 168, 18, 29, 0], [2, "1150", 147, 9, 48, 85, 0], [2, "1150", 253, -5, 48, 85, 2], [2, "1150", 234, -5, 48, 85, 0], [2, "1150", 190, 0, 48, 85, 2], [2, "1308_2", 182, 6, 22, 37, 0], [2, "1310_2", 361, -12, 18, 29, 0], [2, "1154", 234, 25, 28, 51, 0], [2, "1153", 897, 45, 34, 54, 2], [2, "216", 480, 290, 46, 46, 2], [2, "1150", 855, 5, 48, 85, 2], [2, "1147", 831, 62, 50, 42, 0], [2, "1150", 31, 55, 48, 85, 0], [2, "1147", 540, 230, 50, 42, 0], [2, "1147", 124, 99, 50, 42, 0], [2, "214_3", 692, 591, 54, 40, 2], [2, "213_3", 637, 597, 64, 45, 2], [2, "425_2", 807, 92, 30, 36, 0], [2, "426_2", 179, 96, 26, 22, 0], [2, "1154", 614, 176, 28, 51, 0], [2, "426_2", 828, 401, 26, 22, 0], [2, "214_3", 576, 612, 54, 40, 2], [2, "213_3", 521, 613, 64, 45, 2], [2, "313_2", 830, 148, 70, 44, 0], [2, "313_2", 688, 279, 70, 44, 2], [2, "207_2", 394, 165, 38, 27, 2], [2, "208_3", 339, 129, 78, 40, 3], [2, "152_3", 479, 104, 76, 40, 2], [2, "208_3", 303, 64, 78, 40, 1], [2, "208_3", 187, 140, 78, 40, 3], [2, "208_3", 461, 211, 78, 40, 2], [2, "208_3", 734, 106, 78, 40, 0], [2, "208_3", 815, 104, 78, 40, 2], [2, "208_3", 645, 32, 78, 40, 3], [2, "208_3", 617, 217, 78, 40, 2], [2, "152_3", 644, 146, 76, 40, 0], [2, "208_3", 880, 117, 78, 40, 0], [2, "207_2", 801, 121, 38, 27, 0], [2, "208_3", 890, 143, 78, 40, 3], [2, "205_3", 913, 177, 54, 40, 2], [2, "207_2", 654, 49, 38, 27, 2], [2, "208_3", 594, 11, 78, 40, 0], [2, "207_2", 667, 65, 38, 27, 0], [2, "208_3", 555, 11, 78, 40, 2], [2, "207_2", 542, 22, 38, 27, 0], [2, "208_3", 372, 36, 78, 40, 2], [2, "207_2", 362, 51, 38, 27, 2], [2, "208_3", 453, 10, 78, 40, 0], [2, "205_3", 514, 13, 54, 40, 0], [2, "152_3", 391, 3, 76, 40, 0], [2, "206", 664, 182, 66, 40, 0], [2, "208_3", 600, 238, 78, 40, 1], [2, "205_3", 145, 135, 54, 40, 0], [2, "208_3", 56, 199, 78, 40, 1], [2, "207_2", 492, 164, 38, 27, 2], [2, "205_3", 498, 180, 54, 40, 2], [2, "207_2", 484, 141, 38, 27, 2], [2, "208_3", 349, 189, 78, 40, 2], [2, "208_3", 327, 205, 78, 40, 0], [2, "207_2", 293, 206, 38, 27, 0], [2, "208_3", 449, 279, 78, 40, 2], [2, "205_3", 472, 249, 54, 40, 0], [2, "313_2", 133, 231, 70, 44, 0], [2, "313_2", 154, 243, 70, 44, 2], [2, "1151", 884, 161, 38, 33, 0], [2, "1151", 662, 64, 38, 33, 2], [2, "426_2", 548, 308, 26, 22, 0], [2, "208_3", 518, 599, 78, 40, 2], [2, "206", 628, 586, 66, 40, 0], [2, "1310_2", 717, 19, 18, 29, 0], [2, "1310_2", 839, 23, 18, 29, 0], [2, "1310_2", 641, -2, 18, 29, 0], [2, "1308_2", 728, 23, 22, 37, 0], [2, "207_2", 592, 600, 38, 27, 2], [2, "313_2", 651, 248, 70, 44, 2], [2, "313_2", 650, 290, 70, 44, 2], [2, "313_2", 114, 271, 70, 44, 2], [2, "313_2", 518, 494, 70, 44, 2], [2, "1305_2", 724, 222, 20, 14, 0], [2, "1302_3", 694, 201, 40, 29, 0], [2, "1303_2", 694, 232, 34, 20, 0], [2, "1302_3", 928, 202, 40, 29, 2], [2, "1303_2", 184, 289, 34, 20, 0], [2, "955_4", 827, 278, 20, 18, 0], [2, "955_4", 822, 468, 20, 18, 0], [2, "955_4", 49, 526, 20, 18, 0], [2, "955_4", 475, 211, 20, 18, 0], [2, "1303_2", 587, 430, 34, 20, 0], [2, "1303_2", 379, 99, 34, 20, 2], [2, "1303_2", 621, 51, 34, 20, 2], [2, "426_2", 565, 205, 26, 22, 0], [2, "1151", 492, 163, 38, 33, 2], [2, "1153", 19, 198, 34, 54, 0], [2, "1153", 331, 1, 34, 54, 0], [2, "207_2", 932, 532, 38, 27, 2], [2, "152_3", 288, 96, 76, 40, 2], [2, "208_3", 89, 160, 78, 40, 1], [2, "208_3", 88, 417, 78, 40, 2], [2, "214_3", 40, 590, 54, 40, 0], [2, "208_3", 696, 72, 78, 40, 0], [2, "206", 806, 327, 66, 40, 2], [2, "208_3", 421, 316, 78, 40, 2], [2, "206", 595, 275, 66, 40, 0], [2, "213_3", 860, 331, 64, 45, 2], [2, "152_3", 232, 168, 76, 40, 2], [2, "1151", 262, 203, 38, 33, 0], [2, "207_2", 51, 238, 38, 27, 0], [2, "166_2", 305, 91, 30, 35, 0], [2, "1150", 71, 19, 48, 85, 0], [2, "1150", 102, 11, 48, 85, 0], [2, "1150", 122, 8, 48, 85, 2], [2, "1152", 248, 76, 38, 26, 0], [2, "208_3", 194, 553, 78, 40, 2], [2, "208_3", 142, 571, 78, 40, 2], [2, "208_3", 79, 570, 78, 40, 0], [2, "208_3", 878, 414, 78, 40, 1], [2, "208_3", 919, 411, 78, 40, 0], [2, "313_2", 830, 420, 70, 44, 2], [2, "1150", 690, 376, 48, 85, 2], [2, "1149", 734, 379, 40, 82, 0], [2, "208_3", 694, 441, 78, 40, 2], [2, "214_3", 644, 418, 54, 40, 2], [2, "152_3", 629, 395, 76, 40, 2], [2, "208_3", 632, 433, 78, 40, 0], [2, "208_3", 668, 446, 78, 40, 0], [2, "1154", 707, 409, 28, 51, 2], [2, "1149", 334, 437, 40, 82, 0], [2, "1149", 308, 458, 40, 82, 0], [2, "1148", 360, 431, 46, 108, 0], [2, "216", 345, 416, 46, 46, 0], [2, "205_3", 346, 387, 54, 40, 2], [2, "1301_2", 887, 395, 24, 49, 2], [2, "426_2", 343, 497, 26, 22, 0], [2, "1152", 323, 599, 38, 26, 0], [2, "214_3", 396, 469, 54, 40, 0], [2, "208_3", 398, 439, 78, 40, 1], [2, "1147", 348, 546, 50, 42, 0], [2, "208_3", 415, 469, 78, 40, 0], [2, "166_2", 395, 439, 30, 35, 0], [2, "208_3", 467, 381, 78, 40, 1], [2, "205_3", 240, 510, 54, 40, 0], [2, "214_3", 274, 447, 54, 40, 2], [2, "214_3", 241, 454, 54, 40, 2], [2, "214_3", 309, 421, 54, 40, 2], [2, "208_3", 307, 396, 78, 40, 0], [2, "206", 227, 426, 66, 40, 0], [2, "208_3", 212, 479, 78, 40, 2], [2, "214_3", 410, 547, 54, 40, 0], [2, "208_3", 406, 529, 78, 40, 1], [2, "313_2", 500, 472, 70, 44, 2], [2, "213_3", 438, 575, 64, 45, 0], [2, "1302_3", 520, 565, 40, 29, 0], [2, "208_3", 438, 553, 78, 40, 0], [2, "214_3", 476, 609, 54, 40, 0], [2, "206", 470, 585, 66, 40, 0], [2, "208_3", -1, 259, 78, 40, 1], [2, "214_3", 75, 434, 54, 40, 0], [2, "208_3", 69, 417, 78, 40, 0], [2, "208_3", 71, 467, 78, 40, 1], [2, "208_3", -7, 479, 78, 40, 1], [2, "166_2", 254, 506, 30, 35, 2], [2, "166_2", 474, 579, 30, 35, 0], [2, "165_1", 397, 523, 42, 37, 0], [2, "216", 172, 614, 46, 46, 2], [2, "1147", 260, 614, 50, 42, 0], [2, "1154", 523, 663, 28, 51, 0], [2, "214_3", 409, 365, 54, 40, 2], [2, "208_3", 386, 347, 78, 40, 2], [2, "214_3", 372, 386, 54, 40, 2], [2, "206", 361, 367, 66, 40, 0], [2, "1308_2", 342, 429, 22, 37, 0], [2, "1308_2", 569, 134, 22, 37, 0], [2, "1303_2", 927, 227, 34, 20, 0], [2, "1151", 922, 251, 38, 33, 2], [2, "1154", 328, 472, 28, 51, 0], [2, "1308_2", 354, 428, 22, 37, 0], [2, "1144", 783, 388, 114, 70, 0], [2, "1301_2", 784, 415, 24, 49, 0], [2, "208_3", 734, 338, 78, 40, 2], [2, "1144", 755, 346, 114, 70, 0], [2, "214_3", 626, 335, 54, 40, 0], [2, "152_3", 619, 315, 76, 40, 0], [2, "208_3", 664, 337, 78, 40, 0], [2, "205_3", 589, 377, 54, 40, 2], [2, "1151", 573, 376, 38, 33, 0], [2, "213_3", 847, 562, 64, 45, 0], [2, "213_3", 789, 567, 64, 45, 0], [2, "208_3", 821, 545, 78, 40, 2], [2, "165_1", 187, 564, 42, 37, 2], [2, "214_3", 738, 587, 54, 40, 2], [2, "152_3", 687, 568, 76, 40, 2], [2, "214_3", -9, 589, 54, 40, 0], [2, "207_2", -9, 580, 38, 27, 0], [2, "208_3", 11, 555, 78, 40, 2], [2, "1308_2", 705, 689, 22, 37, 0], [2, "1308_2", 685, 666, 22, 37, 0], [2, "1308_2", 768, 674, 22, 37, 0], [2, "1310_2", 749, 694, 18, 29, 2], [2, "1308_2", 725, 698, 22, 37, 0], [2, "1308_2", 392, 648, 22, 37, 2], [2, "1152", 415, 655, 38, 26, 0], [2, "425_2", 300, 558, 30, 36, 0], [2, "1302_3", -8, 507, 40, 29, 2], [2, "1303_2", -9, 532, 34, 20, 0], [2, "1151", -9, 554, 38, 33, 2], [2, "313_2", 541, 472, 70, 44, 2], [2, "1303_2", 426, 254, 34, 20, 0], [2, "955_4", 475, 45, 20, 18, 0], [2, "1151", 633, 249, 38, 33, 0], [2, "1302_3", 61, 249, 40, 29, 0], [2, "1302_3", 188, 161, 40, 29, 2], [2, "1305_2", 211, 197, 20, 14, 0], [2, "1151", 99, 207, 38, 33, 0], [2, "955_4", 95, 275, 20, 18, 0], [2, "1303_2", 32, 351, 34, 20, 0], [2, "955_4", 229, 368, 20, 18, 0], [2, "955_4", 640, 553, 20, 18, 0], [2, "1303_2", 556, 577, 34, 20, 0], [2, "1152", 565, 340, 38, 26, 0], [2, "1152", 90, 135, 38, 26, 0], [2, "206", 696, 349, 66, 40, 0], [2, "1151", 749, 346, 38, 33, 2], [2, "166_2", 605, 267, 30, 35, 0], [2, "166_2", 501, 94, 30, 35, 0], [2, "208_3", 551, 106, 78, 40, 0], [2, "1308_2", 725, 597, 22, 37, 0], [2, "1310_2", 844, 581, 18, 29, 2], [2, "1308_2", 856, 568, 22, 37, 0], [2, "208_3", 431, 405, 78, 40, 0], [2, "1308_2", 815, 677, 22, 37, 0], [2, "1308_2", 836, 664, 22, 37, 0], [2, "1308_2", 852, 662, 22, 37, 0], [2, "1308_2", 885, 672, 22, 37, 0], [2, "1308_2", 897, 687, 22, 37, 0], [2, "216", 902, 570, 46, 46, 2], [2, "208_3", 892, 538, 78, 40, 0], [2, "1151", 227, 475, 38, 33, 0], [2, "1308_2", 348, 448, 22, 37, 0], [2, "1301_2", 74, 433, 24, 49, 2], [2, "181_3", -7, 420, 104, 100, 2], [2, "955_4", 473, 442, 20, 18, 2], [2, "955_4", 108, 480, 20, 18, 0], [2, "1303_2", 107, 423, 34, 20, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 64, 70, 69, 66, 63, 64, 50, 51, 52, 69, 68, 70, 69, 73, 72, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 60, -1, -1, -1, -1, 66, 55, 57, 57, -1, -1, 63, 64, 53, 54, 55, 56, 68, 57, 56, 70, 69, 68, 66, 66, 72, 66, 72, 72, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 60, -1, -1, -1, -1, 62, 63, 69, 68, -1, -1, 74, 75, 75, 75, 81, 80, -1, -1, -1, -1, -1, 70, 69, -1, -1, 63, 69, 69, -1, -1, -1, -1, -1, -1, 72, 71, -1, -1, 74, 75, 76, -1, -1, -1, -1, -1, -1, -1, 74, 75, 85, 78, 78, 91, 93, 92, -1, 58, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 67, 68, -1, -1, 77, 78, 79, 81, 50, 51, 52, -1, -1, 74, 85, 78, 90, 91, 93, 92, -1, -1, 50, 61, 60, 59, 70, 69, -1, -1, -1, -1, -1, -1, -1, 58, 57, -1, -1, 63, 51, 52, -1, -1, 86, 93, 87, 87, 62, 73, 66, -1, -1, 86, 97, 90, 91, 88, -1, -1, -1, -1, -1, 66, 60, 71, -1, 70, 69, -1, -1, -1, -1, -1, 66, 61, 60, -1, -1, 51, 54, 55, 51, 52, -1, -1, -1, -1, -1, 58, 66, 66, -1, -1, 86, 97, 95, -1, 72, 72, 72, 66, 67, -1, 60, 55, -1, 74, 75, 75, 76, -1, -1, -1, -1, -1, 66, -1, 66, 54, 55, 73, 72, 66, 66, 66, 72, -1, -1, 61, 67, 73, 72, -1, -1, 86, 92, -1, 72, 67, 69, 73, 64, -1, -1, -1, -1, 86, 97, 78, 79, 81, 80, 80, -1, -1, 66, -1, 66, 63, 64, 70, 73, 66, 66, 67, 73, 66, 67, 63, 64, 70, 66, -1, -1, -1, -1, -1, -1, 71, -1, 50, 72, 60, 60, -1, -1, -1, 89, 78, 78, 78, 84, 95, -1, 73, 72, -1, 63, 64, -1, -1, 70, 69, 69, 68, 70, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, -1, 70, 69, 73, 72, 71, -1, 74, 85, 78, 78, 90, 91, 92, -1, 70, 69, 66, 71, -1, -1, -1, -1, -1, -1, 82, 81, 81, 81, 81, 80, -1, -1, 82, 81, 80, -1, -1, 57, 71, 64, -1, -1, 70, 69, 68, -1, 86, 97, 84, 91, 87, 88, -1, 50, 51, 52, 63, 68, 58, -1, -1, 74, 81, 80, 85, 84, 84, 84, 84, 79, 75, 75, 85, 84, 83, -1, -1, 66, 59, -1, 50, 61, 66, -1, -1, -1, 74, 85, 78, 95, -1, -1, -1, 53, 54, 55, 57, 56, -1, -1, -1, 77, 84, 84, 84, 84, 84, 84, 84, 90, 91, 97, 78, 96, 95, -1, -1, 66, 71, -1, 53, 54, -1, -1, -1, 89, 90, 84, 84, 79, 80, -1, -1, -1, -1, -1, 60, 59, -1, -1, 74, 85, 84, 84, 84, 84, 90, 91, 93, 93, 88, 94, 93, 93, 92, -1, 66, 66, 71, -1, 53, 66, -1, 50, 51, 52, 87, 94, 97, 84, 83, -1, -1, -1, 66, -1, 60, 55, 56, -1, 86, 87, 97, 84, 84, 91, 87, 88, -1, -1, -1, -1, -1, -1, -1, 66, 66, 67, 68, -1, 65, 73, 66, 53, 54, 55, 56, -1, 86, 87, 84, 9, 10, 66, 66, -1, -1, 60, 59, -1, -1, -1, 94, 97, 84, 83, -1, 50, 51, 51, 52, 13, 14, -1, 66, 60, 67, 64, -1, -1, -1, 62, 73, 72, 54, 97, 90, 91, -1, -1, -1, 12, 13, 73, 72, 66, 54, 66, 66, 57, 57, 56, -1, 89, 84, 79, 80, 53, 54, 54, 55, 16, 17, 1, 69, 69, 68, 14, -1, -1, -1, -1, 70, 69, 66, 54, 87, 92, -1, -1, 66, 67, 72, 67, 73, 72, 54, 66, 66, 60, 60, 59, 51, 86, 93, 91, 92, 52, -1, -1, -1, -1, 67, 68, -1, -1, 16, 17, 82, 81, 80, 58, 57, -1, -1, -1, -1, -1, -1, -1, 63, 64, 69, 68, 70, 69, 66, 67, 64, -1, 51, 60, 59, 68, 50, 53, 54, 55, -1, -1, -1, -1, 6, -1, -1, 9, 74, 75, 85, 84, 79, 76, 65, -1, -1, -1, -1, -1, -1, 67, 9, 9, 10, -1, 70, 69, 63, 64, 66, -1, -1, -1, -1, -1, 53, 50, 51, -1, -1, -1, -1, -1, 66, 55, 71, 12, 77, 78, 78, 78, 78, 79, 80, 73, 54, -1, 74, 66, 66, 71, 12, 74, 75, 75, 81, 80, -1, -1, 66, -1, -1, 82, 81, 80, -1, 53, 54, -1, 66, 66, 66, 66, 66, 66, 69, 68, 94, 93, 97, 78, 78, 78, 83, 70, 69, 68, 62, 73, 72, 71, 74, 85, 84, 84, 90, 83, 66, 67, -1, 74, 75, 85, 84, 79, 80, -1, -1, -1, 66, 67, 69, 69, 68, 68, -1, -1, -1, -1, 89, 78, 78, 78, 79, 81, 81, 80, -1, 70, 69, 68, 86, 97, 96, 91, 93, 92, 51, 64, -1, 89, 90, 90, 96, 91, 92, 50, 51, 66, 66, 71, -1, -1, -1, -1, 57, 56, -1, 89, 90, 90, 78, 78, 78, 84, 90, 79, 81, 80, -1, -1, -1, 94, 93, 92, 57, 56, 54, -1, 54, 86, 87, 87, 93, 92, 51, 53, 54, 66, 66, 71, 50, 51, 50, 51, 66, 55, 68, 86, 87, 96, 96, 91, 93, 97, 90, 96, 93, 92, 50, 58, 57, 52, 66, 66, -1, -1, 66, -1, -1, -1, -1, -1, -1, 77, 78, 79, 66, 66, 67, 68, 53, 54, 61, 60, 66, 66, -1, -1, -1, 94, 93, 92, -1, 86, 87, 93, 92, -1, 53, 61, 60, 55, -1, 66, 66, 66, 66, -1, -1, 66, -1, 82, 81, 85, 96, 66, 67, 63, 64, 80, 66, 62, 63, 73, 72, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, -1, 66, 66, 66, 66, 66, 66, 66, -1, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 68, 97, 78, 79, 85, 78, 84, 70, 73, 72, 66, 66, 66, 53, 66, -1, 66, 66, 66, 66, 66, 66, -1, -1, -1, -1, 66, -1, 66, -1, 66, -1, 66, -1, 66, 58, 57, 56, 69, 68, -1, 86, 87, 93, 93, 93, 93, 93, 70, 69, 73, 66, 66, 66, 66, 66, 65, 66, 67, 73, 72, 66, -1, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 61, 60, 55, 57, 57, 56, 57, 56, -1, -1, -1, -1, 58, 57, 56, -1, -1, 66, 67, 73, 72, -1, 67, 69, 68, -1, -1, -1, -1, 73, 72, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 61, 60, 60, 59, 60, 59, -1, -1, -1, -1, 61, 60, 59, -1, -1, 63, 64, 70, 69, 69, 68, -1, -1, 73, 72, 71, -1, 70, 69, 68, 58, 57, -1]}, {"type": 2, "data": [-1, -1, -1, 0, 1, 2, 0, 1, 2, 2, -1, 0, 1, 2, 0, 1, 0, 1, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, 3, 4, 5, 3, 4, 5, 5, 0, 3, 4, 5, 3, 4, 3, 4, 13, 14, 8, 6, 7, 8, 6, 7, 8, 6, 3, 4, 0, 1, 2, 0, 1, 0, 1, 2, -1, -1, -1, -1, -1, 6, 7, 8, 6, 7, 8, 8, 3, 6, 7, 8, 6, 7, 6, 7, 16, 17, 17, 10, 9, 10, 11, 9, 10, 11, 6, 7, 3, 4, 5, 3, 4, 3, 4, 5, 0, 1, -1, -1, -1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 8, 10, 11, 11, 9, 10, 9, 9, 12, 13, 12, 13, 14, 12, 13, 14, 9, 10, 6, 7, 8, 1, 2, 6, 7, 8, 3, 4, -1, -1, -1, 3, 4, 0, 1, 2, 5, 3, 0, 1, 2, 13, 14, 14, 12, 13, 9, 10, 11, 16, 15, 9, 10, 11, 9, 10, 11, 9, 10, 11, 3, 4, 5, 2, 0, 1, 6, 3, 0, 1, 2, 6, 7, 3, 4, 5, 0, 1, 2, 4, 5, 16, 17, 17, 15, 16, 9, 10, 9, 0, 1, 2, 13, 14, 12, 13, 9, 12, 13, 14, 6, 7, 8, 10, 11, 11, 5, 6, 3, 0, 1, 2, 0, 6, 7, 9, 10, 11, 5, 7, 8, 0, 1, 1, 2, 9, 12, 13, 12, 3, 4, 0, 1, 2, 15, 16, 12, 15, 16, 17, 13, 14, 15, 16, 15, 16, 17, 1, 6, 3, 4, 5, 3, 9, 10, 11, 13, 14, 11, 0, 0, 1, 2, 2, 5, 12, 15, 16, 15, 6, 7, 3, 4, 0, 1, 2, 15, 16, 17, 15, 16, 17, 12, 9, 10, 11, 11, 1, 3, 6, 7, 8, 6, 12, 13, 14, 16, 9, 10, 11, 3, 4, 5, 5, 9, 10, 11, 17, 9, 12, 0, 6, 7, 3, 4, 5, 9, 10, 11, 11, 11, 12, 15, 9, 10, 11, 11, 9, 6, 7, 8, 9, 10, 11, 16, 17, 15, 12, 13, 14, 9, 10, 11, 8, 12, 13, 14, 10, 9, 10, 3, 4, 5, 6, 7, 8, 12, 13, 14, 14, 14, 9, 10, 11, 13, 14, 14, 12, 0, 1, 2, 12, 13, 14, 14, 14, 10, 15, 16, 17, 12, 13, 14, 11, 15, 16, 17, 13, 12, 13, 6, 7, 8, 6, 7, 8, 15, 16, 17, 17, 11, 12, 13, 9, 10, 9, 10, 15, 11, 10, 11, 15, 16, 17, 17, 17, 9, 10, 15, 16, 15, 16, 17, 14, 12, 13, 14, 16, 15, 0, 1, 0, 1, 2, 13, 14, 11, 16, 17, 17, 9, 9, 10, 11, 9, 12, 13, 9, 14, 10, 9, 10, 9, 10, 11, 17, 12, 9, 10, 11, 14, 9, 10, 11, 10, 11, 10, 9, 10, 3, 4, 3, 4, 5, 16, 17, 14, 10, 11, 9, 10, 9, 10, 9, 10, 15, 16, 12, 10, 11, 12, 13, 12, 13, 14, 17, 17, 12, 13, 9, 10, 12, 13, 14, 9, 10, 11, 12, 0, 1, 2, 0, 1, 2, 15, 16, 17, 13, 14, 10, 11, 12, 13, 12, 9, 10, 11, 15, 11, 14, 9, 10, 11, 16, 17, 16, 17, 15, 16, 12, 13, 15, 16, 17, 12, 9, 10, 15, 3, 4, 5, 3, 4, 0, 1, 2, 9, 10, 11, 11, 14, 15, 16, 15, 12, 0, 1, 0, 9, 10, 11, 9, 10, 11, 10, 11, 14, 17, 17, 15, 16, 17, 12, 15, 9, 10, 11, 2, 6, 7, 8, 6, 7, 3, 4, 0, 1, 2, 14, 14, 9, 10, 11, 9, 0, 1, 2, 3, 9, 10, 9, 10, 11, 14, 13, 14, 17, 9, 10, 11, 16, 9, 10, 0, 1, 2, 4, 5, 15, 16, 17, 15, 16, 6, 7, 3, 4, 5, 11, 11, 0, 1, 2, 12, 3, 4, 5, 6, 0, 1, 12, 9, 10, 11, 16, 17, 13, 12, 13, 14, 10, 12, 13, 3, 4, 4, 5, 8, 17, 12, 12, 13, 14, 10, 12, 0, 1, 2, 14, 14, 8, 4, 5, 15, 6, 7, 8, 7, 3, 4, 0, 1, 11, 14, 14, 16, 9, 15, 16, 17, 13, 15, 0, 6, 7, 7, 8, 17, 10, 11, 15, 16, 17, 13, 15, 3, 4, 5, 10, 8, 8, 7, 8, 8, 8, 9, 9, 10, 1, 3, 3, 4, 14, 17, 17, 14, 12, 13, 14, 17, 16, 0, 3, 4, 15, 13, 14, 12, 13, 14, 9, 10, 11, 16, 9, 13, 14, 8, 8, 14, 12, 13, 14, 11, 10, 12, 12, 13, 4, 10, 9, 10, 11, 10, 11, 10, 15, 16, 17, 2, 0, 3, 0, 1, 2, 16, 17, 15, 16, 17, 12, 13, 14, 10, 12, 16, 17, 15, 16, 17, 15, 16, 17, 14, 10, 12, 13, 11, 12, 13, 12, 13, 9, 10, 11, 9, 9, 10, 11, 2, 3, 4, 3, 4, 5, 2, 17, 15, 16, 15, 15, 16, 17, 13, 15, 16, 17, 12, 13, 14, 14, 15, 16, 17, 13, 12, 13, 14, 15, 16, 15, 16, 12, 13, 14, 12, 12, 13, 14, 5, 6, 7, 6, 7, 8, 5, 9, 10, 13, 9, 15, 16, 15, 16, 17, 14, 12, 9, 10, 9, 10, 11, 9, 10, 11, 15, 16, 17, 15, 16, 17, 13, 15, 16, 17, 15, 15, 16, 17, 8, 1, 2, 7, 6, 7, 8, 9, 10, 10, 12, 13, 14, 17, 15, 16, 17, 15, 12, 13, 12, 13, 14, 12, 13, 14, 15, 16, 17, 15, 16, 17, 16, 17, 15, 16, 17, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 12, 13, 13, 15, 16, 17, 13, 12, 13, 14, 17, 15, 16, 15, 16, 17, 15, 0, 1, 2, 0, 1, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 6, 15, 16, 17, 9, 10, 16, 15, 16, 17, 2, 3, 6, 7, 8, 0, 1, 2, 0, 1, 2, 4, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 1, 2, 2, 17, 12, 13, 16, 16, 0, 1, 0, 1, 2, 0, 0, 3, 4, 5, 3, 4, 5, 7, -1, -1, -1, 6, 7, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 1, 2, 0, 1, 2, 0, 1, 6, 7, 8, 6, 7, 8, 1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}