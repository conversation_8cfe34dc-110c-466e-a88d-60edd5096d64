{"mW": 864, "mH": 720, "tW": 24, "tH": 24, "tiles": [["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["315_6", 0, 3, 3], ["1314", 0, 3, 2], ["1317", 0, 3, 2], ["1317", 2, 3, 2], ["1317", 1, 3, 2], ["1317", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 2, "data": [29, 29, 29, 29, 29, 22, 31, 31, 31, 25, 25, 19, 29, 29, 29, 22, 31, 31, 30, 29, 29, 22, 25, 25, 31, 31, 31, 25, 24, -1, 26, 25, 30, 29, 21, 21, 29, 29, 22, 23, 17, 18, -1, -1, -1, -1, -1, 26, 25, 25, 17, 18, -1, -1, 26, 25, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 30, 29, 21, 22, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 3, 13, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 29, 29, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 25, 30, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 6, 9, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 13, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 21, 6, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 17, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 15, 15, 9, 8, -1, -1, -1, -1, -1, -1, -1, 6, 1, 2, -1, 10, 9, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, -1, 4, 5, 13, 13, 29, 6, 9, 15, 9, 1, 2, -1, -1, 13, 13, 6, 15, 14, 29, 29, 6, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 15, 15, 14, 13, 6, 29, 29, 29, 29, 29, 29, 29, 29, 29, 6, -1, -1]}, {"type": 4, "obj": [[2, "1174", 551, -46, 104, 88, 0], [2, "1174", 768, -17, 104, 88, 0], [2, "1174", 140, -9, 104, 88, 2], [2, "1174", 225, -3, 104, 88, 0], [2, "1174", 436, 4, 104, 88, 0], [2, "1174", 809, 111, 104, 88, 0], [2, "1174", -26, 238, 104, 88, 2], [2, "1177", 605, 389, 44, 64, 0]]}, {"type": 3, "obj": [[2, "1185", 590, -16, 76, 68, 0], [2, "1182", 330, 29, 94, 46, 0], [2, "1172", 796, 225, 90, 96, 0], [2, "1172", 58, -5, 90, 96, 0], [2, "1182", 680, 334, 94, 46, 2], [2, "1182", 195, 60, 94, 46, 2], [2, "1172", 738, 340, 90, 96, 0], [2, "1172", 718, 527, 90, 96, 0], [2, "1172", -1, 553, 90, 96, 2], [2, "1172", 63, 570, 90, 96, 2], [2, "1186", 48, 195, 48, 54, 0], [2, "1172", 619, 355, 90, 96, 0], [2, "1182", 463, 14, 94, 46, 0], [2, "1170", -2, 12, 68, 83, 0], [2, "1184", 263, 27, 50, 36, 2], [2, "1183", 545, 26, 58, 31, 2], [2, "1172", 122, 590, 90, 96, 2], [2, "1186", 166, 593, 48, 54, 0], [2, "1172", 138, 639, 90, 96, 2], [2, "1186", 458, 606, 48, 54, 0], [2, "1172", 538, 552, 90, 96, 0], [2, "1172", 621, 547, 90, 96, 2], [2, "1172", 672, 551, 90, 96, 2], [2, "1186", 537, 551, 48, 54, 2], [2, "1187", 710, 550, 38, 23, 0], [2, "1187", 662, 543, 38, 23, 0], [2, "1186", 757, 445, 48, 54, 0], [2, "1183", 702, 458, 58, 31, 0], [2, "1187", 9, 388, 38, 23, 0], [2, "1186", 768, 476, 48, 54, 0], [2, "1187", 725, 434, 38, 23, 0], [2, "1171", 679, 361, 74, 78, 2], [2, "1186", 42, 552, 48, 54, 0], [2, "1186", 110, 571, 48, 54, 0], [2, "165_2", 143, 575, 42, 37, 0], [2, "1187", -13, 530, 38, 23, 2], [2, "1180", -26, 43, 88, 117, 0], [2, "166_3", -16, 515, 30, 35, 0], [2, "1170", 782, 152, 68, 83, 0], [2, "1180", 813, 125, 88, 117, 0], [2, "1171", 830, 187, 74, 78, 0], [2, "1177", 118, 11, 44, 64, 2], [2, "1183", 266, 94, 58, 31, 2], [2, "1182", 38, 112, 94, 46, 0], [2, "1182", 634, 12, 94, 46, 2], [2, "165_2", 779, 462, 42, 37, 2], [2, "166_3", 788, 493, 30, 35, 2], [2, "1183", 76, 148, 58, 31, 0], [2, "1182", 633, 429, 94, 46, 0], [2, "1183", 770, 120, 58, 31, 0], [2, "1185", 95, 483, 76, 68, 2], [2, "1183", 155, 537, 58, 31, 0], [2, "1183", 465, 572, 58, 31, 0], [2, "1177", 498, 566, 44, 64, 0], [2, "1172", 491, 611, 90, 96, 0], [2, "1172", 424, 652, 90, 96, 0], [2, "1172", -4, 300, 90, 96, 2], [2, "1186", 47, 307, 48, 54, 0], [2, "1186", 44, 361, 48, 54, 2], [2, "1186", 777, 259, 48, 54, 0], [2, "1186", 795, 291, 48, 54, 0], [2, "165_2", 807, 274, 42, 37, 2], [2, "166_3", 818, 306, 30, 35, 2], [2, "1177", 795, 329, 44, 64, 0], [2, "1186", 191, 663, 48, 54, 0], [2, "1187", 434, 655, 38, 23, 0], [2, "1187", 778, 41, 38, 23, 0], [2, "1187", 417, 674, 38, 23, 0], [2, "1187", 416, 695, 38, 23, 0], [2, "1182", 722, 229, 94, 46, 2], [2, "1183", -4, 400, 58, 31, 0], [2, "1182", 14, 515, 94, 46, 0], [2, "1182", 204, 627, 94, 46, 0], [2, "1183", 160, 249, 58, 31, 0], [2, "1172", 575, 184, 90, 96, 0], [2, "1172", 524, 225, 90, 96, 2], [2, "1187", 560, 205, 38, 23, 0], [2, "1186", 617, 75, 48, 54, 0], [2, "1183", 562, 78, 58, 31, 0], [2, "1186", 628, 106, 48, 54, 0], [2, "1187", 585, 64, 38, 23, 0], [2, "165_2", 639, 92, 42, 37, 2], [2, "1182", 488, 50, 94, 46, 0], [2, "1172", 193, 193, 90, 96, 2], [2, "1172", 230, 217, 90, 96, 0], [2, "1172", 260, 220, 90, 96, 2], [2, "1186", 627, 158, 48, 54, 2], [2, "166_3", 652, 141, 30, 35, 2], [2, "1186", 171, 95, 48, 54, 2], [2, "1186", 167, 151, 48, 54, 0], [2, "165_2", 172, 76, 42, 37, 0], [2, "166_3", 171, 141, 30, 35, 0], [2, "1186", 312, 229, 48, 54, 0], [2, "1172", 499, 232, 90, 96, 0], [2, "1186", 492, 238, 48, 54, 2], [2, "1182", 265, 296, 94, 46, 2], [2, "1182", 484, 294, 94, 46, 0], [2, "1186", 488, 331, 48, 54, 0], [2, "1186", 302, 333, 48, 54, 2], [2, "1183", 261, 472, 58, 31, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, 87, 87, 87, 87, 87, 87, 87, 87, 75, -1, 87, 87, 87, 87, 87, 75, 87, 87, 87, 87, 87, 93, 93, 93, 93, 93, 93, 93, 93, -1, -1, -1, -1, -1, -1, -1, -1, 87, 87, 87, 87, 87, 87, 87, 75, 75, -1, 94, 93, 87, 87, 87, 88, 87, 87, 87, 87, -1, -1, -1, -1, 94, 93, 93, 93, 93, -1, -1, -1, 51, 51, -1, 87, 88, 88, 84, 84, 84, 94, 88, 90, 89, -1, 91, 90, 83, 84, 84, 85, 89, -1, -1, -1, -1, -1, -1, -1, 91, 90, 94, 93, 93, 93, -1, -1, -1, 87, 88, 84, 85, -1, 71, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 78, 77, -1, -1, -1, 91, 90, 94, 93, 93, 87, 88, 84, 85, -1, -1, -1, 74, 75, -1, -1, -1, 91, -1, -1, 55, 54, 54, 48, 49, -1, -1, -1, -1, -1, -1, -1, 80, -1, -1, -1, -1, -1, 91, 90, -1, 87, 92, -1, -1, 47, -1, 71, 72, -1, -1, -1, -1, -1, -1, 47, 58, 57, 57, 51, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 92, -1, -1, -1, -1, 74, 75, -1, -1, -1, -1, -1, -1, 50, 63, 63, 63, 63, 63, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, 72, -1, 84, 76, 78, 77, -1, -1, 86, 87, -1, -1, -1, -1, -1, -1, 62, 63, 63, 63, 63, 63, 68, -1, -1, -1, -1, -1, -1, 78, 77, -1, -1, -1, -1, 91, 90, -1, 90, 89, -1, 80, -1, -1, 83, 94, 93, -1, -1, -1, -1, -1, 59, 60, 70, 69, 64, 66, 65, -1, -1, -1, -1, -1, -1, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, -1, -1, -1, -1, -1, -1, -1, 67, 66, 61, -1, -1, -1, -1, -1, -1, -1, -1, 93, 92, -1, -1, 71, 72, 78, 77, -1, -1, 88, -1, -1, -1, -1, -1, 83, 94, 93, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 87, 88, 89, -1, -1, -1, -1, 81, -1, -1, 81, 85, 78, 77, -1, -1, -1, -1, 91, 90, -1, 94, 93, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 88, 85, -1, -1, -1, 83, 94, 93, -1, -1, 81, 81, 81, 80, -1, -1, -1, -1, -1, -1, 94, 93, 93, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 88, 84, 85, -1, -1, -1, -1, -1, 91, 90, -1, -1, 81, 81, 93, 92, -1, -1, -1, -1, -1, -1, 91, 90, 94, 93, -1, -1, 47, 54, 53, -1, -1, 87, 87, 88, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 75, 81, 90, 89, -1, -1, -1, -1, -1, -1, -1, -1, 91, 90, -1, -1, 50, 57, 56, -1, -1, 84, 84, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 58, 63, 68, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 60, 60, 70, -1, -1, 81, 81, 89, -1, 55, 54, 54, 53, -1, -1, -1, -1, -1, -1, -1, 59, 60, 60, 65, -1, -1, -1, -1, -1, -1, -1, 64, 60, 61, -1, -1, 72, 73, 67, -1, -1, -1, -1, -1, 47, 58, 57, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 81, 79, 78, 75, 76, -1, -1, -1, -1, -1, -1, 50, 51, 57, 57, 52, 54, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 48, 54, 53, -1, -1, 84, 81, 81, 81, -1, -1, -1, -1, -1, -1, -1, -1, 59, 70, 51, 51, 51, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 57, 56, -1, -1, 47, 57, 57, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 66, 70, 69, 68, -1, 47, 48, 48, 49, -1, -1, -1, -1, 47, 48, 58, 57, 57, 52, 48, 48, 58, 57, 52, 54, 53, -1, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 66, 65, -1, 50, 51, 51, 52, 54, 48, 49, -1, 62, 63, 57, 63, 64, 66, 70, 63, 57, 63, 57, 64, 61, -1, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 69, 69, 69, 51, 51, 52, 49, 50, 69, 64, 60, 61, -1, 67, 66, 66, 66, 60, 61, -1, 54, 29, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 70, 57, 51, 51, 51, 68, 67, 66, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 29, 29, -1, 84, 85, 93, 55, -1, 93, -1, -1, -1, -1, -1, -1, -1, 62, 57, 57, 57, 57, 68, -1, -1, -1, -1, -1, 53, -1, -1, -1, 59, 70, 69, 69, 29, 29, 29, -1, 29, 29, 93, 93, 93, 93, 93, -1, -1, -1, -1, -1, 55, 58, 57, 57, 64, 66, 65, -1, -1, -1, -1, 57, 68, -1, -1, -1, -1, 67, 66, 29, 29, -1, -1, -1, 29, 93, 93, 93, 93, 93, 93, -1, -1, -1, -1, -1, 67, 66, 66, 66, 65, -1, -1, -1, -1, -1, 60, 60, 61, -1, -1, 29, 29, 29, 29, 29, 29, 29, -1, -1, 93, 93, 93, 93, 93, 93, 93, 93, -1, -1, -1, -1, 47, 48, 54, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 81, 81, 81, 81, -1, -1, 73, 93, 93, 93, 93, 93, 93, 93, 93, 77, -1, -1, 62, 57, 57, 68, -1, -1, -1, -1, -1, 81, 81, -1, -1, 72, -1, -1, -1, 81, 81, 81, 81, 81, -1, -1, 93, 74, 93, 93, 93, 93, 93, 93, 93, 80, -1, 47, 58, 57, 57, 52, 53, -1, 81, 81, 81, 81, 81, 81, 74, 81, 81, 81, -1, -1, -1, -1, 81, 81, -1, -1]}, {"type": 2, "data": [33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, -1, 32, 38, 32, 33, 34, 40, 38, 39, 38, 39, 40, 40, 38, 39, 40, 38, 39, 32, 33, 34, 32, 33, 34, 32, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 35, 36, 35, 36, 37, 39, 40, -1, 38, 39, 40, 38, 39, 40, 35, 32, 33, 34, 36, 37, 32, 33, 34, 35, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 38, 39, 38, 39, 40, 33, 34, 40, 38, 39, 40, 38, 39, 40, 38, 35, 36, 37, 39, 40, 35, 36, 37, 38, 33, 34, 32, 33, 34, 32, 33, 35, 36, 37, 35, 36, 37, 35, 38, 39, 40, 36, 37, 35, 36, 37, 35, 36, 37, 35, 32, 38, 39, 40, 37, 32, 38, 39, 40, 36, 36, 37, 35, 36, 37, 35, 36, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 32, 33, 34, 34, 32, 35, 35, 36, 37, 33, 35, 36, 37, 38, 39, 39, 40, 38, 39, 40, 38, 39, 40, 35, 36, 38, 39, 40, 37, 38, 39, 40, 35, 36, 37, 32, 35, 36, 37, 37, 35, 38, 38, 39, 40, 36, 38, 39, 40, 32, 33, 37, 32, 33, 34, 32, 33, 34, 32, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 35, 38, 39, 40, 32, 33, 34, 35, 36, 37, 39, 35, 36, 37, 35, 36, 40, 35, 32, 33, 34, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 32, 33, 34, 32, 33, 34, 40, 38, 35, 36, 37, 38, 39, 40, 34, 38, 39, 40, 38, 39, 32, 33, 35, 36, 37, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 36, 37, 35, 36, 37, 35, 36, 38, 39, 40, 37, 35, 36, 37, 38, 35, 36, 32, -1, 35, 32, 38, 39, 40, 35, 36, 37, 35, 36, 37, 35, 36, 32, 33, 34, 37, 34, 40, 32, 33, 34, 32, 33, 34, 32, 32, 33, 34, 39, 40, 32, 38, 39, 35, -1, 38, 35, 36, 37, 39, 38, 39, 32, 33, 34, 40, 38, 39, 35, 36, 37, 40, 37, 32, 33, 34, 37, 35, 36, 37, 35, 35, 36, 37, 32, 33, 34, 36, 32, 38, -1, 38, 32, 33, 34, 39, 38, 32, 33, 34, 37, 32, 33, 34, 38, 39, 40, 39, 40, 35, 36, 37, 32, 33, 34, 40, 38, 38, 39, 40, 35, 36, 32, 33, 34, 32, -1, 32, 35, 36, 37, 32, 33, 34, 32, 33, 34, 35, 36, 37, 39, 40, 38, 39, 40, 38, 39, 40, 35, 36, 37, 34, 32, 35, 36, 37, 38, 39, 35, 36, 37, 35, -1, 35, 38, 39, 40, 35, 36, 37, 35, 36, 32, 33, 34, 40, 32, 33, 34, 36, 37, 35, 36, 37, 38, 39, 40, 34, 32, 38, 39, 40, 33, 34, 38, 39, 40, 38, 29, 13, 13, 40, 34, 38, 39, 40, 38, 39, 35, 36, 37, 39, 35, 36, 37, 39, 40, 38, 39, 40, 40, 35, 36, 37, 35, 36, 37, 35, 36, 37, 38, 39, 40, 29, 29, 13, 13, 13, 37, 37, 32, 38, 39, 40, 38, 39, 40, 32, 33, 34, 32, 33, 34, 37, 34, 32, 33, 32, 33, 34, 32, 33, 34, 38, 32, 33, 34, 32, 33, 29, 29, 13, 13, 39, 40, 40, 35, 36, 37, 35, 36, 38, 39, 35, 36, 37, 35, 36, 37, 40, 37, 35, 36, 35, 36, 37, 35, 36, 37, 37, 35, 36, 37, 29, 29, 29, 29, 39, 40, 40, 39, 40, 38, 39, 40, 38, 39, 40, 38, 38, 39, 40, 38, 39, 40, 33, 32, 33, 34, 38, 39, 40, 38, 39, 40, 40, 38, 39, 29, 29, 29, 29, 29, 35, 36, 37, 32, 32, 33, 34, 35, 38, 39, 40, 32, 33, 34, 35, 36, 37, 35, 36, 35, 36, 37, 32, 33, 32, 33, 34, 33, 34, 35, 36, 29, 29, 29, 29, 29, 38, 39, 40, 35, 35, 36, 37, 32, 33, 34, 34, 35, 36, 37, 38, 39, 40, 38, 39, 38, 39, 40, 35, 36, 35, 36, 37, 36, 37, 38, 39, 40, 35, 29, 29, 29, 32, 32, 33, 34, 38, 39, 40, 35, 36, 37, 37, 38, 39, 40, 35, 32, 32, 33, 34, 33, 32, 33, 34, 32, 38, 39, 40, 39, 40, 38, 39, 40, 38, 29, 29, 29, 35, 35, 36, 37, 37, 33, 34, 38, 39, 40, 40, 35, 36, 37, 38, 35, 35, 36, 37, 36, 35, 36, 37, 35, 36, 38, 39, 40, 40, 34, 38, 39, 40, 29, 29, 29, 38, 38, 39, 40, 40, 34, 37, 38, 39, 40, 40, 38, 39, 40, 34, 38, 38, 39, 40, 39, 38, 39, 40, 38, 39, 38, 39, 40, 36, 37, 32, 33, 34, 29, 29, 29, 32, 33, 32, 33, 34, 32, 32, 33, 34, 32, 33, 34, 35, 36, 37, 35, 36, 37, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 35, 36, 29, 35, 29, 29, 29, 36, 35, 36, 37, 32, 33, 34, 37, 35, 36, 37, 38, 39, 40, 38, 39, 40, 35, 36, 37, 35, 36, 37, 38, 29, 29, 29, 39, 40, 38, 29, 29, 29, 29, 29, 29, 29, 32, 33, 34, 32, 33, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 29, 29, 29, 29, 29, 33, 34, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 35, 32, 33, 34, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 35, 36, 37, 40, 34, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 38, 39, 40, 36, 37, 37, 35, 36, 37, 35, 32, 33, 34, 36, 32, 33, 29, 29, 29, 36, 32, 33, 29, 29, 29, 29, 29, 29, 29, 29, 38, 29, 29, 29, 29, 39, 40, 36, 37, 39, 40, 40, 38, 39, 40, 38, 35, 36, 37, 39, 29, 29, 29, 29, 34, 39, 35, 36, 37, 35, 36, 37, 35, 29, 29, 29]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}