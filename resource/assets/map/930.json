{"mW": 960, "mH": 960, "tW": 24, "tH": 24, "tiles": [["302_5", 0, 2, 2], ["1246", 0, 3, 2], ["1246", 2, 3, 2], ["1246", 1, 3, 2], ["1246", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 0, 4, 2]], "layers": [{"type": 3, "obj": [[2, "1244", 871, 333, 30, 67, 0], [2, "1254", 889, 344, 54, 32, 0], [2, "1141_2", 888, 352, 54, 67, 2], [2, "1244", 424, 486, 30, 67, 0], [2, "1254", 444, 498, 54, 32, 0], [2, "1141_2", 443, 506, 54, 67, 2], [2, "1244", 780, 849, 30, 67, 0], [2, "1244", 949, 875, 30, 67, 2], [2, "1141_2", 909, 894, 54, 67, 0], [2, "1254", 910, 887, 54, 32, 2], [2, "1141_2", 798, 869, 54, 67, 2], [2, "1254", 797, 861, 54, 32, 0], [2, "1244", 838, 877, 30, 67, 0], [2, "1141_2", 856, 898, 54, 67, 2], [2, "1254", 855, 890, 54, 32, 0], [2, "1244", 895, 904, 30, 67, 0], [2, "1141_2", 912, 925, 54, 67, 2], [2, "1254", 914, 918, 54, 32, 0], [2, "1244", 951, 933, 30, 67, 0], [2, "1254", 932, 344, 54, 32, 2], [2, "1141_2", 931, 351, 54, 67, 0], [2, "1244", 916, 359, 30, 67, 2], [2, "1141_2", 875, 379, 54, 67, 0], [2, "1254", 875, 371, 54, 32, 2], [2, "1244", 865, 384, 30, 67, 2], [2, "1254", 824, 395, 54, 32, 2], [2, "1141_2", 823, 402, 54, 67, 0], [2, "1244", 808, 410, 30, 67, 2], [2, "1141_2", 767, 430, 54, 67, 0], [2, "1254", 767, 422, 54, 32, 2], [2, "1244", 757, 435, 30, 67, 2], [2, "1254", 714, 448, 54, 32, 2], [2, "1141_2", 713, 455, 54, 67, 0], [2, "1244", 698, 463, 30, 67, 2], [2, "1141_2", 657, 483, 54, 67, 0], [2, "1254", 657, 475, 54, 32, 2], [2, "1244", 647, 488, 30, 67, 2], [2, "1244", 476, 511, 30, 67, 0], [2, "1141_2", 495, 531, 54, 67, 2], [2, "1254", 496, 523, 54, 32, 0], [2, "1254", 604, 500, 54, 32, 2], [2, "1141_2", 603, 507, 54, 67, 0], [2, "1244", 588, 515, 30, 67, 2], [2, "1141_2", 547, 535, 54, 67, 0], [2, "1254", 547, 527, 54, 32, 2], [2, "1254", 798, 890, 54, 32, 2], [2, "1141_2", 797, 897, 54, 67, 0], [2, "1244", 782, 905, 30, 67, 2], [2, "1141_2", 741, 925, 54, 67, 0], [2, "1254", 741, 917, 54, 32, 2], [2, "1244", 731, 930, 30, 67, 2], [2, "1254", 692, 942, 54, 32, 2], [2, "1141_2", 691, 949, 54, 67, 0], [2, "1244", 677, 957, 30, 67, 2], [2, "1244", 445, 865, 30, 67, 0], [2, "1141_2", 463, 885, 54, 67, 2], [2, "1254", 462, 877, 54, 32, 0], [2, "1244", 503, 893, 30, 67, 0], [2, "1141_2", 521, 914, 54, 67, 2], [2, "1254", 520, 906, 54, 32, 0], [2, "1244", 560, 920, 30, 67, 0], [2, "1141_2", 577, 941, 54, 67, 2], [2, "1254", 579, 934, 54, 32, 0], [2, "1244", 537, 540, 30, 67, 2], [2, "1244", 616, 949, 30, 67, 0]]}, {"type": 4, "obj": []}, {"type": 3, "obj": [[2, "1244", 472, 285, 30, 67, 2], [2, "1141_1", 429, 305, 54, 67, 0], [2, "1254", 430, 297, 54, 32, 2], [2, "1244", 414, 314, 30, 67, 2], [2, "1141_1", 371, 334, 54, 67, 0], [2, "1254", 372, 326, 54, 32, 2], [2, "1251", 946, 553, 22, 52, 0], [2, "1251", 924, 564, 22, 52, 0], [2, "1251", -3, 507, 22, 52, 2], [2, "1251", 18, 518, 22, 52, 2], [2, "1248", 552, 594, 22, 52, 0], [2, "1249", 552, 634, 22, 53, 0], [2, "1248", 574, 583, 22, 52, 0], [2, "1249", 574, 623, 22, 53, 0], [2, "1250", 574, 666, 22, 52, 0], [2, "1248", 618, 561, 22, 52, 0], [2, "1249", 618, 601, 22, 53, 0], [2, "1250", 618, 644, 22, 52, 0], [2, "1248", 596, 572, 22, 52, 0], [2, "1249", 596, 612, 22, 53, 0], [2, "1250", 596, 655, 22, 52, 0], [2, "1248", 640, 550, 22, 52, 0], [2, "1249", 640, 590, 22, 53, 0], [2, "1250", 640, 633, 22, 52, 0], [2, "1248", 662, 539, 22, 52, 0], [2, "1249", 662, 579, 22, 53, 0], [2, "1250", 662, 622, 22, 52, 0], [2, "1248", 706, 517, 22, 52, 0], [2, "1249", 706, 557, 22, 53, 0], [2, "1250", 706, 600, 22, 52, 0], [2, "1248", 684, 528, 22, 52, 0], [2, "1249", 684, 568, 22, 53, 0], [2, "1250", 684, 611, 22, 52, 0], [2, "1248", 728, 506, 22, 52, 0], [2, "1249", 728, 546, 22, 53, 0], [2, "1250", 728, 589, 22, 52, 0], [2, "1248", 750, 495, 22, 52, 0], [2, "1249", 750, 535, 22, 53, 0], [2, "1250", 750, 578, 22, 52, 0], [2, "1248", 794, 473, 22, 52, 0], [2, "1249", 794, 513, 22, 53, 0], [2, "1250", 794, 556, 22, 52, 0], [2, "1248", 772, 484, 22, 52, 0], [2, "1249", 772, 524, 22, 53, 0], [2, "1250", 772, 567, 22, 52, 0], [2, "1248", 816, 462, 22, 52, 0], [2, "1249", 816, 502, 22, 53, 0], [2, "1250", 816, 545, 22, 52, 0], [2, "1248", 838, 451, 22, 52, 0], [2, "1249", 838, 491, 22, 53, 0], [2, "1250", 838, 534, 22, 52, 0], [2, "1248", 882, 429, 22, 52, 0], [2, "1249", 882, 469, 22, 53, 0], [2, "1250", 882, 512, 22, 52, 0], [2, "1251", 882, 553, 22, 52, 0], [2, "1248", 860, 440, 22, 52, 0], [2, "1249", 860, 480, 22, 53, 0], [2, "1250", 860, 523, 22, 52, 0], [2, "1248", 903, 418, 22, 52, 0], [2, "1249", 903, 458, 22, 53, 0], [2, "1250", 903, 501, 22, 52, 0], [2, "1251", 903, 542, 22, 52, 0], [2, "1248", 925, 407, 22, 52, 0], [2, "1249", 925, 447, 22, 53, 0], [2, "1250", 925, 490, 22, 52, 0], [2, "1251", 925, 531, 22, 52, 0], [2, "1248", 947, 396, 22, 52, 0], [2, "1249", 947, 436, 22, 53, 0], [2, "1250", 947, 479, 22, 52, 0], [2, "1251", 947, 520, 22, 52, 0], [2, "1248", 533, 594, 22, 52, 2], [2, "1249", 533, 634, 22, 53, 2], [2, "1250", 533, 677, 22, 52, 2], [2, "1248", 512, 583, 22, 52, 2], [2, "1249", 512, 623, 22, 53, 2], [2, "1248", 490, 572, 22, 52, 2], [2, "1249", 490, 612, 22, 53, 2], [2, "1248", 469, 561, 22, 52, 2], [2, "1249", 469, 601, 22, 53, 2], [2, "1248", 448, 551, 22, 52, 2], [2, "1249", 448, 591, 22, 53, 2], [2, "1248", 427, 540, 22, 52, 2], [2, "1249", 427, 580, 22, 53, 2], [2, "1248", 406, 540, 22, 52, 0], [2, "1249", 406, 580, 22, 53, 0], [2, "1248", 384, 552, 22, 52, 0], [2, "1248", 330, 865, 22, 52, 2], [2, "1249", 330, 905, 22, 53, 2], [2, "1248", 309, 855, 22, 52, 2], [2, "1249", 309, 895, 22, 53, 2], [2, "1248", 288, 844, 22, 52, 2], [2, "1249", 288, 884, 22, 53, 2], [2, "1250", 288, 927, 22, 52, 2], [2, "1248", 267, 844, 22, 52, 0], [2, "1249", 267, 884, 22, 53, 0], [2, "1250", 267, 927, 22, 52, 0], [2, "1248", 245, 856, 22, 52, 0], [2, "1249", 245, 896, 22, 53, 0], [2, "1250", 245, 939, 22, 52, 0], [2, "1248", 223, 868, 22, 52, 0], [2, "1249", 223, 908, 22, 53, 0], [2, "1248", 201, 878, 22, 52, 0], [2, "1249", 201, 918, 22, 53, 0], [2, "1248", 181, 887, 22, 52, 0], [2, "1249", 181, 927, 22, 53, 0], [2, "1248", 159, 899, 22, 52, 0], [2, "1249", 159, 939, 22, 53, 0], [2, "1248", 137, 909, 22, 52, 0], [2, "1248", 116, 919, 22, 52, 0], [2, "1249", 116, 959, 22, 53, 0], [2, "1248", 95, 930, 22, 52, 0], [2, "1248", 74, 940, 22, 52, 0], [2, "1248", 52, 950, 22, 52, 0], [2, "1248", 502, 948, 22, 52, 2], [2, "1248", 481, 938, 22, 52, 2], [2, "1248", 460, 928, 22, 52, 2], [2, "1248", 439, 925, 22, 52, 0], [2, "1248", 854, 943, 22, 52, 2], [2, "1248", 833, 941, 22, 52, 0], [2, "1248", 875, 953, 22, 52, 2], [2, "1248", 812, 951, 22, 52, 0], [2, "1248", 294, 489, 22, 52, 2], [2, "1249", 294, 529, 22, 53, 2], [2, "1248", 272, 478, 22, 52, 2], [2, "1249", 272, 518, 22, 53, 2], [2, "1248", 251, 467, 22, 52, 2], [2, "1249", 251, 507, 22, 53, 2], [2, "1250", 252, 550, 22, 52, 2], [2, "1248", 230, 457, 22, 52, 2], [2, "1249", 230, 497, 22, 53, 2], [2, "1248", 208, 448, 22, 52, 2], [2, "1249", 208, 488, 22, 53, 2], [2, "1248", 186, 437, 22, 52, 2], [2, "1249", 186, 477, 22, 53, 2], [2, "1248", 165, 426, 22, 52, 2], [2, "1249", 165, 466, 22, 53, 2], [2, "1248", 144, 416, 22, 52, 2], [2, "1249", 144, 456, 22, 53, 2], [2, "1248", 124, 406, 22, 52, 2], [2, "1249", 124, 446, 22, 53, 2], [2, "1250", 124, 489, 22, 52, 2], [2, "1248", 102, 395, 22, 52, 2], [2, "1249", 102, 435, 22, 53, 2], [2, "1250", 102, 478, 22, 52, 2], [2, "1248", 81, 384, 22, 52, 2], [2, "1249", 81, 424, 22, 53, 2], [2, "1250", 81, 467, 22, 52, 2], [2, "1248", 60, 374, 22, 52, 2], [2, "1249", 60, 414, 22, 53, 2], [2, "1250", 60, 457, 22, 52, 2], [2, "1251", 60, 498, 22, 52, 2], [2, "1248", 39, 363, 22, 52, 2], [2, "1249", 39, 403, 22, 53, 2], [2, "1250", 39, 446, 22, 52, 2], [2, "1251", 39, 487, 22, 52, 2], [2, "1248", 17, 352, 22, 52, 2], [2, "1249", 17, 392, 22, 53, 2], [2, "1250", 17, 435, 22, 52, 2], [2, "1251", 17, 476, 22, 52, 2], [2, "1248", -4, 341, 22, 52, 2], [2, "1249", -4, 381, 22, 53, 2], [2, "1250", -4, 424, 22, 52, 2], [2, "1251", -4, 465, 22, 52, 2], [2, "1249", 137, 949, 22, 53, 0], [2, "1250", 223, 951, 22, 52, 0], [2, "1141_2", -32, 285, 54, 67, 2], [2, "1254", -32, 277, 54, 32, 0], [2, "1244", 8, 293, 30, 67, 0], [2, "1141_2", 25, 314, 54, 67, 2], [2, "1254", 25, 305, 54, 32, 0], [2, "1244", 65, 320, 30, 67, 0], [2, "1141_2", 82, 341, 54, 67, 2], [2, "1254", 84, 333, 54, 32, 0], [2, "1244", 295, 318, 30, 67, 0], [2, "1254", 313, 329, 54, 32, 0], [2, "1141_1", 313, 336, 54, 67, 2], [2, "1244", 353, 345, 30, 67, 0], [2, "1141_1", 315, 366, 54, 67, 0], [2, "1254", 315, 358, 54, 32, 2], [2, "1244", 297, 376, 30, 67, 2], [2, "1141_1", 256, 395, 54, 67, 0], [2, "1254", 256, 387, 54, 32, 2], [2, "1244", 125, 350, 30, 67, 0], [2, "1141_2", 142, 370, 54, 67, 2], [2, "1254", 142, 362, 54, 32, 0], [2, "1244", 182, 378, 30, 67, 0], [2, "1141_2", 199, 399, 54, 67, 2], [2, "1254", 199, 390, 54, 32, 0], [2, "1244", 239, 405, 30, 67, 0], [2, "1141_2", 256, 426, 54, 67, 2], [2, "1254", 258, 418, 54, 32, 0], [2, "1244", 294, 434, 30, 67, 0], [2, "1244", 1, 858, 30, 67, 0], [2, "1141_1", 19, 878, 54, 67, 2], [2, "1254", 20, 871, 54, 32, 0], [2, "1244", 114, 643, 30, 67, 2], [2, "1141_1", 71, 663, 54, 67, 0], [2, "1254", 72, 655, 54, 32, 2], [2, "1244", 564, -73, 30, 67, 2], [2, "1141_1", 522, -53, 54, 67, 0], [2, "1254", 523, -62, 54, 32, 2], [2, "1141_1", 580, -53, 54, 67, 2], [2, "1254", 582, -60, 54, 32, 0], [2, "1244", 618, -45, 30, 67, 0], [2, "1141_1", 634, -25, 54, 67, 2], [2, "1254", 635, -32, 54, 32, 0], [2, "1141_1", 306, -47, 54, 67, 0], [2, "1244", 288, -43, 30, 67, 2], [2, "1141_1", 244, -22, 54, 67, 0], [2, "1254", 249, -30, 54, 32, 2], [2, "1244", 225, -13, 30, 67, 2], [2, "1141_1", 183, 9, 54, 67, 0], [2, "1254", 184, 1, 54, 32, 2], [2, "1244", 173, 15, 30, 67, 2], [2, "1141_1", 131, 35, 54, 67, 0], [2, "1254", 132, 27, 54, 32, 2], [2, "1244", 121, 41, 30, 67, 2], [2, "1141_1", 78, 61, 54, 67, 0], [2, "1254", 79, 53, 54, 32, 2], [2, "1244", 68, 67, 30, 67, 2], [2, "1141_1", 26, 87, 54, 67, 0], [2, "1254", 27, 79, 54, 32, 2], [2, "1244", 15, 93, 30, 67, 2], [2, "1141_1", -27, 113, 54, 67, 0], [2, "1254", -26, 105, 54, 32, 2], [2, "1244", 668, -20, 30, 67, 0], [2, "1244", 56, 672, 30, 67, 2], [2, "1141_1", 13, 692, 54, 67, 0], [2, "1254", 14, 684, 54, 32, 2], [2, "1141_1", -37, 692, 54, 67, 2], [2, "1254", -36, 685, 54, 32, 0], [2, "1244", 4, 700, 30, 67, 0], [2, "1141_1", 22, 720, 54, 67, 2], [2, "1254", 23, 713, 54, 32, 0], [2, "1244", 55, 726, 30, 67, 0], [2, "958_1", 574, 299, 90, 68, 0], [2, "965_1", 105, 719, 40, 33, 0], [2, "969_1", 183, 692, 36, 30, 0], [2, "90_2", 143, 678, 28, 36, 0], [2, "955_5", 791, -146, 20, 18, 0], [2, "90_2", 563, -23, 28, 36, 0], [2, "90_2", 540, -6, 36, 28, 5], [2, "90_2", -3, 748, 28, 36, 0], [2, "965_1", 320, 435, 40, 33, 2], [2, "967_1", 327, 406, 24, 41, 2], [2, "958_1", 150, 299, 90, 68, 0], [2, "11_4", 361, 388, 32, 29, 0], [2, "955_5", 394, 382, 20, 18, 2], [2, "1255", 321, 486, 26, 27, 2], [2, "92_2", 395, 533, 40, 45, 2], [2, "1241", 294, 496, 38, 50, 2], [2, "1241", 269, 538, 38, 50, 2], [2, "1240", 286, 575, 84, 50, 2], [2, "1240", 295, 560, 84, 50, 2], [2, "1240", 304, 545, 84, 50, 2], [2, "1240", 313, 530, 84, 50, 2], [2, "1240", 323, 516, 84, 50, 2], [2, "1240", 332, 501, 84, 50, 2], [2, "1255", 405, 527, 26, 27, 2], [2, "1241", 379, 536, 38, 50, 2], [2, "1241", 353, 579, 38, 50, 2], [2, "700_2", 400, 496, 22, 48, 2], [2, "955_5", 301, 540, 20, 18, 0], [2, "700_2", 307, 455, 22, 48, 2], [2, "700_2", 330, 456, 22, 48, 0], [2, "700_2", 423, 497, 22, 48, 0], [2, "958_1", 696, 698, 90, 68, 2], [2, "955_5", 678, 713, 20, 18, 0], [2, "972_1", 909, 699, 66, 54, 0], [2, "971_1", 852, 724, 58, 50, 0], [2, "972_1", 865, 752, 66, 54, 2], [2, "971_1", 913, 767, 58, 50, 2], [2, "973_2", 918, 708, 42, 70, 2], [2, "1244", 139, 469, 30, 67, 2], [2, "1141_1", 97, 489, 54, 67, 0], [2, "1254", 98, 480, 54, 32, 2], [2, "1141_1", 155, 489, 54, 67, 2], [2, "1254", 157, 482, 54, 32, 0], [2, "1244", 193, 497, 30, 67, 0], [2, "1141_1", 209, 517, 54, 67, 2], [2, "1254", 210, 510, 54, 32, 0], [2, "1244", 79, 496, 30, 67, 2], [2, "1141_1", 39, 517, 54, 67, 0], [2, "1254", 37, 509, 54, 32, 2], [2, "1244", 21, 522, 30, 67, 2], [2, "1141_1", -21, 544, 54, 67, 0], [2, "1254", -19, 535, 54, 32, 2], [2, "1244", 243, 522, 30, 67, 0], [2, "1244", 360, 569, 30, 67, 2], [2, "1141_1", 376, 589, 54, 67, 2], [2, "1254", 377, 582, 54, 32, 0], [2, "1244", 418, 598, 30, 67, 2], [2, "1141_1", 434, 618, 54, 67, 2], [2, "1254", 435, 611, 54, 32, 0], [2, "959_2", 825, 772, 40, 22, 2], [2, "703_2", 948, 779, 16, 23, 0], [2, "703_2", 855, 740, 16, 23, 0], [2, "703_2", 950, 689, 16, 23, 0], [2, "90_2", 138, 519, 28, 36, 0], [2, "90_2", 115, 536, 36, 28, 5], [2, "1244", 477, 625, 30, 67, 2], [2, "1141_1", 493, 645, 54, 67, 2], [2, "1254", 494, 638, 54, 32, 0], [2, "1244", 534, 649, 30, 67, 2], [2, "1141_1", 550, 669, 54, 67, 2], [2, "1254", 551, 662, 54, 32, 0], [2, "1244", 851, 544, 30, 67, 2], [2, "1141_1", 809, 564, 54, 67, 0], [2, "1254", 810, 556, 54, 32, 2], [2, "1244", 799, 570, 30, 67, 2], [2, "1141_1", 756, 590, 54, 67, 0], [2, "1254", 757, 582, 54, 32, 2], [2, "1244", 746, 596, 30, 67, 2], [2, "1141_1", 704, 616, 54, 67, 0], [2, "1254", 705, 608, 54, 32, 2], [2, "1244", 693, 622, 30, 67, 2], [2, "1141_1", 651, 642, 54, 67, 0], [2, "1254", 652, 634, 54, 32, 2], [2, "1244", 642, 648, 30, 67, 2], [2, "1141_1", 603, 667, 54, 67, 0], [2, "1254", 601, 659, 54, 32, 2], [2, "1244", 588, 675, 30, 67, 2], [2, "1244", 271, 786, 30, 67, 2], [2, "92_2", 420, 914, 40, 45, 2], [2, "1255", 346, 867, 26, 27, 2], [2, "1241", 319, 876, 38, 50, 2], [2, "1241", 294, 918, 38, 50, 2], [2, "1240", 320, 941, 84, 50, 2], [2, "1240", 329, 926, 84, 50, 2], [2, "1240", 338, 911, 84, 50, 2], [2, "1240", 348, 897, 84, 50, 2], [2, "1240", 357, 882, 84, 50, 2], [2, "1255", 430, 908, 26, 27, 2], [2, "1241", 404, 917, 38, 50, 2], [2, "700_2", 424, 877, 22, 48, 2], [2, "700_2", 447, 877, 22, 48, 0], [2, "955_5", 326, 921, 20, 18, 0], [2, "1141_1", 288, 801, 54, 67, 2], [2, "1254", 288, 796, 54, 32, 0], [2, "1244", 324, 811, 30, 67, 0], [2, "700_2", 336, 837, 22, 48, 2], [2, "700_2", 359, 837, 22, 48, 0], [2, "1141_1", 866, 565, 54, 67, 2], [2, "1254", 867, 558, 54, 32, 0], [2, "1244", 900, 570, 30, 67, 0], [2, "1141_1", 916, 589, 54, 67, 2], [2, "1254", 917, 582, 54, 32, 0], [2, "1244", 950, 594, 30, 67, 0], [2, "1141_1", 230, 805, 54, 67, 0], [2, "1254", 231, 797, 54, 32, 2], [2, "1244", 219, 811, 30, 67, 2], [2, "1141_1", 177, 831, 54, 67, 0], [2, "1254", 178, 823, 54, 32, 2], [2, "1244", 168, 837, 30, 67, 2], [2, "1141_1", 129, 858, 54, 67, 0], [2, "1254", 127, 848, 54, 32, 2], [2, "1244", 114, 864, 30, 67, 2], [2, "1141_1", 72, 883, 54, 67, 0], [2, "1254", 73, 875, 54, 32, 2], [2, "1244", 61, 889, 30, 67, 2], [2, "1141_1", 19, 909, 54, 67, 0], [2, "1254", 20, 901, 54, 32, 2], [2, "1244", 10, 915, 30, 67, 2], [2, "1254", -30, 926, 54, 32, 2], [2, "1141_1", 685, -1, 54, 67, 2], [2, "1254", 686, -8, 54, 32, 0], [2, "1244", 728, 5, 30, 67, 2], [2, "1141_1", 744, 25, 54, 67, 2], [2, "1254", 746, 19, 54, 32, 0], [2, "1244", 787, 34, 30, 67, 2], [2, "1141_1", 803, 54, 54, 67, 2], [2, "1254", 804, 47, 54, 32, 0], [2, "90_2", 721, 53, 28, 36, 0], [2, "90_2", 742, 63, 28, 36, 0], [2, "954_4", 772, 90, 24, 25, 0], [2, "1244", 846, 62, 30, 67, 2], [2, "1141_1", 862, 82, 54, 67, 2], [2, "1254", 863, 75, 54, 32, 0], [2, "1244", 904, 90, 30, 67, 2], [2, "1141_1", 920, 110, 54, 67, 2], [2, "1254", 921, 103, 54, 32, 0], [2, "961_1", 877, 458, 28, 36, 2], [2, "1253", 933, 432, 22, 35, 0], [2, "541_3", 899, 487, 38, 42, 0], [2, "541_3", 927, 516, 38, 42, 0], [2, "962_1", 891, 475, 18, 19, 2], [2, "1253", 819, 480, 22, 35, 0], [2, "1253", 712, 530, 22, 35, 0], [2, "1253", 591, 582, 22, 35, 0], [2, "961_1", 50, 400, 28, 36, 0], [2, "962_1", 47, 418, 18, 19, 0], [2, "541_3", 24, 423, 38, 42, 2], [2, "541_3", -7, 453, 38, 42, 2], [2, "700_2", 240, 562, 22, 48, 2], [2, "700_2", 263, 562, 22, 48, 0], [2, "700_2", 331, 599, 22, 48, 2], [2, "700_2", 354, 599, 22, 48, 0], [2, "1253", 7, 379, 22, 35, 2], [2, "1253", 105, 421, 22, 35, 2], [2, "700_2", 157, 89, 22, 48, 2], [2, "700_2", 180, 89, 22, 48, 0], [2, "700_2", 331, 175, 22, 48, 2], [2, "700_2", 354, 175, 22, 48, 0], [2, "700_2", 330, 6, 22, 48, 2], [2, "700_2", 353, 6, 22, 48, 0], [2, "700_2", 496, 89, 22, 48, 2], [2, "700_2", 519, 89, 22, 48, 0], [2, "965_1", 854, 619, 40, 33, 0], [2, "967_1", 863, 588, 24, 41, 0], [2, "969_1", 886, 647, 36, 30, 2], [2, "965_1", 923, 653, 40, 33, 0], [2, "90_2", 912, 137, 28, 36, 0], [2, "90_2", 803, 622, 28, 36, 0], [2, "90_2", 555, 707, 28, 36, 0], [2, "965_1", 622, 717, 40, 33, 0], [2, "1141_1", -29, 933, 54, 67, 0], [2, "11_4", 523, 699, 32, 29, 0], [2, "955_5", 466, 825, 20, 18, 0], [2, "955_5", 216, 620, 20, 18, 0], [2, "11_4", 864, 167, 32, 29, 0], [2, "11_4", 766, 645, 32, 29, 0], [2, "541_3", -37, 483, 38, 42, 2], [2, "541_3", 957, 547, 38, 42, 0], [2, "1141_1", 862, 110, 54, 67, 0], [2, "1254", 862, 102, 54, 32, 2], [2, "1244", 845, 118, 30, 67, 2], [2, "1141_1", 802, 139, 54, 67, 0], [2, "1254", 802, 131, 54, 32, 2], [2, "1244", 785, 147, 30, 67, 2], [2, "1141_1", 743, 168, 54, 67, 0], [2, "1254", 743, 160, 54, 32, 2], [2, "1244", 726, 176, 30, 67, 2], [2, "700_2", 729, 209, 22, 48, 2], [2, "700_2", 752, 209, 22, 48, 0], [2, "700_2", 481, 327, 22, 48, 2], [2, "700_2", 504, 327, 22, 48, 0], [2, "1242", 564, -6, 96, 56, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, -1, 38, 38, 32, 32, 32, 44, 45, 51, 50, 38, 38, 51, 50, 38, 50, 50, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, -1, -1, -1, -1, -1, 44, 45, 41, 42, 38, 44, 32, 45, 51, 50, 45, 41, 42, 12, 43, 44, 50, 49, 47, 50, 38, 50, 42, 48, 47, 51, 50, -1, -1, -1, -1, -1, -1, 50, 50, 50, 50, -1, -1, 44, 41, 42, 44, 45, 47, 41, 42, -1, -1, 47, 51, 42, -1, -1, 40, 41, 47, 46, 48, 47, 51, 50, 51, -1, 50, 48, 47, 51, 50, -1, 51, 50, 50, 50, 45, 47, 47, 44, 45, 38, 38, 38, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 40, 41, 41, 45, -1, 48, 47, -1, 48, 47, 51, 47, 46, -1, -1, -1, 42, 38, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, 51, 50, -1, -1, -1, -1, 48, -1, -1, 38, 38, 38, 38, 38, 35, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 44, 44, 51, 50, -1, -1, -1, 50, 38, 44, 45, 51, 50, 38, 38, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 39, 33, -1, -1, 30, -1, -1, -1, -1, -1, -1, 40, 38, 44, 44, 51, 50, 51, 50, 33, 45, 41, 42, 48, 47, 38, 45, 46, -1, -1, -1, -1, -1, -1, -1, 28, -1, -1, 36, 35, 45, 42, -1, -1, -1, -1, -1, -1, -1, 29, 38, 38, 38, 38, 38, 50, 50, 38, 38, 50, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 34, -1, -1, 35, 35, 34, 40, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, -1, 31, 50, 50, 44, 45, 38, 33, 35, 35, 35, 34, -1, 4, 5, 5, 10, 48, 33, 35, 47, 47, 41, 43, 44, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 44, 45, 42, -1, 40, 41, 38, 41, 42, 38, 38, 38, 38, 50, 49, 4, 15, 8, 14, 9, 11, 10, -1, -1, -1, -1, 40, 41, 42, -1, -1, 4, 5, 11, 10, -1, -1, -1, -1, 40, 41, 42, -1, -1, 42, -1, -1, -1, 48, 38, 38, 50, 45, 47, 46, 7, 14, 14, 21, 27, 26, 13, -1, -1, -1, -1, -1, -1, -1, 48, -1, 7, 8, 14, 13, -1, -1, -1, -1, -1, -1, 12, 11, 11, 10, -1, -1, 28, 29, 38, 38, 50, 49, -1, -1, 16, 23, 23, 22, 24, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 17, 18, -1, -1, -1, 4, 5, 5, 15, 14, 14, 13, -1, -1, 31, 32, 38, 38, 38, 33, -1, -1, -1, -1, -1, -1, -1, 43, 35, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 15, 8, 14, 21, 27, 26, 25, -1, 36, 31, 32, 38, 12, 38, 38, 38, 38, 50, 38, 38, -1, 38, 38, 38, 41, 42, -1, -1, 24, 44, 45, -1, 9, -1, -1, -1, -1, -1, -1, 15, 14, 14, 21, 22, 24, 23, 22, -1, 39, 38, 44, -1, 19, 20, 38, 38, 38, 38, 38, -1, 38, 38, 38, 44, 45, -1, -1, -1, -1, 41, 42, -1, 12, 11, 11, 11, 10, -1, -1, 16, 17, 23, 18, -1, -1, 36, -1, -1, 48, 51, 44, -1, 16, 17, 23, 41, 42, 48, 43, 38, 38, 44, 45, 41, 42, -1, 44, 45, -1, -1, 12, 11, 15, 14, 14, 21, 22, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, -1, 4, 5, 6, 41, 27, 44, 45, 46, 44, 45, 41, 42, 12, 11, 15, 14, 14, 14, 21, 22, 28, 29, -1, -1, -1, -1, -1, -1, -1, -1, 12, 11, 10, 48, 47, -1, -1, -1, -1, -1, 24, 23, 22, -1, 24, 41, 42, 44, 41, 42, -1, -1, 7, 26, 21, 17, 23, 22, -1, -1, 39, 33, 34, -1, -1, -1, -1, -1, -1, 11, 15, 14, 9, 11, 10, 29, 30, -1, -1, -1, -1, 30, -1, -1, -1, 38, 38, 41, 42, -1, -1, -1, 16, 23, 22, -1, 36, 35, 44, 45, 44, 50, 37, -1, -1, 16, -1, 20, 14, 14, 14, 14, 14, 14, 13, 32, 33, 30, -1, -1, 28, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 39, 38, 41, 40, 44, 44, 46, -1, -1, 4, 15, 20, 21, 23, 27, 26, 14, 14, 25, -1, 32, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, -1, -1, 23, 22, 31, 32, 35, 34, -1, -1, -1, -1, -1, 41, 42, 19, 20, 26, 25, -1, 24, 23, 23, 23, 22, -1, -1, -1, -1, 38, 38, 38, 38, 38, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 40, 41, 42, -1, -1, -1, -1, 14, 35, 34, 16, 17, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 51, 38, 38, 51, 38, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, -1, -1, 28, 29, 29, 30, -1, -1, -1, 36, 38, 44, 45, 42, -1, 48, 51, 50, 45, 47, 51, 44, 45, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, 36, 35, 14, 45, 45, 41, 42, -1, -1, -1, -1, 32, 33, 29, 30, 36, 39, 38, 41, 42, -1, -1, -1, 48, 47, 46, -1, 12, 11, 6, 42, -1, -1, -1, -1, -1, -1, -1, 51, 50, 39, 38, 50, 35, 34, -1, -1, -1, -1, -1, -1, 48, 44, 45, 33, 39, 38, 38, 49, 36, 35, 34, -1, -1, -1, 4, 5, 15, 14, 9, 11, 10, 51, 50, -1, -1, -1, -1, 48, 47, 51, 50, 47, -1, 35, 34, -1, -1, -1, -1, 44, 45, 38, 42, 38, 38, 50, 38, 33, 39, 38, 37, -1, -1, -1, 7, 8, 14, 14, 14, 14, 25, 48, 47, 51, 50, 5, 6, -1, -1, 48, 47, -1, 51, 50, 42, -1, -1, 38, 38, 44, 45, 38, 38, 38, 38, 38, 38, 38, 39, 38, 37, -1, -1, -1, 16, 27, 14, 14, 14, 14, 25, -1, 42, 48, 47, 51, 38, -1, 11, 5, 6, -1, 48, 47, 51, 44, 38, 44, 45, 41, 42, 40, 41, 51, 50, 38, 39, 38, 37, 38, 38, 44, 45, -1, -1, 19, 14, 14, 14, 14, 9, 11, 10, -1, -1, 48, 43, 44, 45, 38, 9, 11, 10, 44, 45, 41, 41, 41, 42, -1, -1, -1, -1, 48, 47, 38, -1, -1, -1, 44, 45, 41, 42, 4, 5, 15, 14, 21, 23, 27, 14, 14, 9, 11, 10, -1, 40, 41, 51, 38, 38, 38, 50, 41, 42, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, 44, 43, 44, 35, 34, 19, 14, 14, 26, 25, -1, 7, 8, 14, 14, 14, 13, -1, -1, -1, -1, 43, 44, 45, 46, -1, 4, 5, 11, 11, 11, 11, 10, 28, 29, 13, -1, -1, 44, 45, 51, 50, 44, 38, 37, 16, 17, 17, 23, 22, -1, 16, 17, 27, 26, 26, 25, -1, -1, -1, -1, 40, 41, 42, -1, -1, 7, 8, 14, 14, 14, 14, 13, 47, 51, 38, 38, -1, 41, 42, 48, 47, 51, 45, 49, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 14, 14, 14, 14, 14, 9, 10, 48, 38, 36, 38, 49, -1, -1, -1, 43, 50, 49, -1, -1, -1, -1, -1, 28, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 15, 14, 14, 14, 14, 14, 14, 25, 36, 38, 38, 38, 29, 30, -1, 36, 35, 38, 38, -1, -1, -1, -1, -1, 31, 32, -1, -1, -1, 34, -1, -1, -1, -1, 4, 5, 11, 11, 11, 15, 14, 14, 21, 23, 27, 14, 14, 25, 48, 51, 50, 38, 32, 33, 38, 39, 38, 38, 38, -1, -1, -1, 36, 35, 39, 38, -1, 44, 45, 37, -1, -1, -1, -1, 7, 8, 14, 14, 14, 14, 21, 23, 22, -1, 16, 17, 23, 22, -1, 48, 47, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, -1, 44, 45, 44, 44, 45, -1, -1, -1, -1, 24, 23, 27, 20, 20, 21, 18, -1, -1, 36, 35, -1, 35, 34, -1, 38, 38, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 44, 45, 44, 41, 42, -1, -1, -1, -1, -1, -1, 24, 23, 23, 18, 36, 35, 35, 39, 38, -1, 38, 37, 38, 38, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 41, 42, -1, 36, 35, 34, -1, -1, -1, 36, 35, 34, -1, -1, 28, 39, 38, 38, -1, -1, -1, 36, 35, 39, 38, -1, -1]}, {"type": 3, "obj": [[2, "959_2", 425, 94, 40, 22, 0], [2, "972_1", 299, 28, 66, 54, 0], [2, "971_1", 242, 53, 58, 50, 0], [2, "972_1", 349, 35, 66, 54, 2], [2, "971_1", 199, 75, 58, 50, 0], [2, "971_1", 395, 51, 58, 50, 2], [2, "971_1", 157, 97, 58, 50, 0], [2, "971_1", 173, 119, 58, 50, 2], [2, "971_1", 216, 140, 58, 50, 2], [2, "971_1", 259, 163, 58, 50, 2], [2, "971_1", 305, 187, 58, 50, 2], [2, "971_1", 440, 74, 58, 50, 2], [2, "971_1", 482, 96, 58, 50, 2], [2, "971_1", 467, 118, 58, 50, 0], [2, "971_1", 423, 141, 58, 50, 0], [2, "971_1", 381, 163, 58, 50, 0], [2, "971_1", 337, 186, 58, 50, 0], [2, "43_8", 189, 154, 82, 58, 0], [2, "43_8", 210, 165, 82, 58, 0], [2, "43_8", 445, 144, 82, 58, 2], [2, "43_8", 410, 162, 82, 58, 2], [2, "10_1", 326, 41, 50, 26, 2], [2, "10_1", 300, 53, 50, 26, 2], [2, "10_1", 274, 66, 50, 26, 0], [2, "10_1", 248, 78, 50, 26, 1], [2, "10_1", 224, 91, 50, 26, 0], [2, "10_1", 198, 103, 50, 26, 0], [2, "10_1", 184, 112, 50, 26, 0], [2, "10_1", 352, 53, 50, 26, 0], [2, "10_1", 326, 65, 50, 26, 0], [2, "10_1", 300, 78, 50, 26, 2], [2, "10_1", 274, 90, 50, 26, 2], [2, "10_1", 250, 103, 50, 26, 2], [2, "10_1", 224, 115, 50, 26, 0], [2, "10_1", 210, 125, 50, 26, 2], [2, "10_1", 378, 66, 50, 26, 0], [2, "10_1", 353, 77, 50, 26, 0], [2, "10_1", 327, 90, 50, 26, 0], [2, "10_1", 301, 102, 50, 26, 2], [2, "10_1", 277, 115, 50, 26, 0], [2, "10_1", 251, 127, 50, 26, 2], [2, "10_1", 237, 138, 50, 26, 0], [2, "10_1", 404, 77, 50, 26, 0], [2, "10_1", 379, 89, 50, 26, 0], [2, "10_1", 352, 102, 50, 26, 0], [2, "10_1", 327, 114, 50, 26, 2], [2, "10_1", 303, 127, 50, 26, 0], [2, "10_1", 277, 139, 50, 26, 0], [2, "10_1", 263, 150, 50, 26, 2], [2, "10_1", 428, 90, 50, 26, 0], [2, "10_1", 403, 102, 50, 26, 0], [2, "10_1", 377, 115, 50, 26, 2], [2, "10_1", 351, 127, 50, 26, 0], [2, "10_1", 327, 140, 50, 26, 0], [2, "10_1", 301, 152, 50, 26, 2], [2, "10_1", 287, 163, 50, 26, 0], [2, "10_1", 453, 103, 50, 26, 0], [2, "10_1", 429, 114, 50, 26, 1], [2, "10_1", 402, 127, 50, 26, 0], [2, "10_1", 377, 139, 50, 26, 0], [2, "10_1", 353, 152, 50, 26, 0], [2, "10_1", 327, 164, 50, 26, 0], [2, "10_1", 312, 176, 50, 26, 0], [2, "10_1", 469, 112, 50, 26, 0], [2, "10_1", 444, 124, 50, 26, 2], [2, "10_1", 419, 136, 50, 26, 0], [2, "10_1", 392, 147, 50, 26, 0], [2, "10_1", 369, 161, 50, 26, 0], [2, "10_1", 346, 174, 50, 26, 2], [2, "10_1", 328, 184, 50, 26, 2]]}, {"type": 2, "data": [57, 57, 57, 57, 57, 57, 57, 57, -1, -1, -1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, -1, 0, 1, 0, 1, 0, 0, 57, 57, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, -1, 0, 1, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, -1, -1, -1, -1, 2, 3, 1, 1, 1, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 2, -1, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 1, 1, 3, 0, 1, 2, 3, 0, 1, 1, 2, 3, 2, 0, 1, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 3, 2, 3, 57, 57, 57, 57, 57, 57, 57, -1, 2, 3, 3, 3, 2, 2, 3, 0, 1, 2, 3, 3, 0, 1, 0, 2, 3, 1, 2, 3, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 57, 57, 57, -1, 2, 3, 2, 0, 1, 1, 3, 2, 3, 0, 1, 0, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 3, 2, 3, 0, 1, 0, 0, 1, 0, 1, 0, 1, 2, 3, 3, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 0, 1, 2, 0, 1, 2, 3, 0, 1, 1, 0, 0, 1, 2, 0, 1, 0, 0, 1, 3, 0, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, 0, 1, 2, 3, 2, 3, 2, 3, 3, 2, 2, 0, 1, 2, 3, 2, 2, 3, 3, 2, 3, 2, 0, 2, 3, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 2, 2, 3, 0, 1, 3, 2, 2, 3, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 2, 3, 0, 0, 1, 1, 2, 3, 0, 1, 0, 1, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 2, 3, 2, 0, 2, 3, 0, 1, 0, 1, 1, 0, 1, 3, 0, 1, 0, 0, 1, 2, 2, 3, 3, 0, 1, 2, 3, 0, 1, 3, 2, 3, 2, 3, 1, 2, 2, 3, 0, 1, 0, 1, 0, 2, 3, 1, 2, 3, 2, 3, 3, 2, 3, 2, 2, 3, 2, 2, 3, 0, 0, 1, 3, 2, 3, 0, 1, 2, 0, 1, 2, 3, 1, 0, 1, 0, 1, 3, 2, 3, 2, 3, 2, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 2, 2, 0, 1, 0, 1, 2, 0, 2, 2, 3, 1, 0, 1, 2, 3, 2, 3, 1, 1, 0, 1, 0, 1, 3, 2, 0, 1, 0, 1, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 2, 0, 2, 3, 2, 3, 0, 2, 3, 0, 1, 3, 2, 3, 1, 2, 3, 2, 3, 3, 2, 3, 2, 3, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 2, 3, 2, 3, 0, 1, 3, 1, 1, 2, 3, 1, 2, 2, 3, 0, 1, 3, 2, 3, 1, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 0, 1, 3, 0, 0, 1, 2, 3, 2, 3, 3, 0, 1, 0, 1, 3, 2, 2, 3, 3, 2, 3, 3, 2, 3, 0, 1, 0, 0, 1, 3, 0, 1, 0, 1, 2, 3, 2, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 1, 1, 2, 0, 1, 3, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 2, 3, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 3, 0, 2, 3, 0, 1, 0, 1, 0, 1, 1, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 2, 2, 3, 2, 3, 2, 0, 1, 3, 0, 0, 0, 1, 0, 1, 3, 2, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 0, 1, 0, 0, 0, 1, 2, 3, 1, 2, 2, 2, 0, 2, 3, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 0, 1, 1, 0, 1, 2, 3, 2, 3, 2, 0, 1, 0, 1, 2, 3, 2, 2, 2, 3, 1, 0, 1, 3, 0, 1, 1, 0, 0, 2, 3, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 3, 3, 2, 3, 3, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 2, 2, 3, 2, 0, 1, 2, 3, 3, 2, 2, 2, 3, 2, 3, 3, 2, 3, 2, 3, 3, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 2, 3, 0, 2, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 2, 0, 1, 3, 2, 2, 0, 1, 1, 0, 1, 2, 3, 0, 1, 2, 3, 2, 3, 2, 0, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 0, 1, 0, 2, 3, 0, 2, 3, 1, 3, 0, 2, 3, 3, 2, 3, 0, 0, 1, 3, 2, 3, 0, 1, 0, 1, 2, 2, 3, 0, 2, 3, 0, 1, 0, 1, 0, 0, 1, 0, 2, 3, 2, 3, 0, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 2, 3, 3, 0, 1, 2, 0, 0, 0, 2, 3, 2, 3, 2, 3, 2, 0, 1, 2, 3, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 0, 2, 2, 2, 3, 2, 3, 2, 0, 1, 2, 2, 3, 3, 0, 1, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 1, 2, 0, 1, 0, 0, 1, 2, 3, 0, 1, 1, 0, 2, 3, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 3, 1, 0, 3, 0, 2, 3, 2, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 0, 2, 3, 0, 2, 3, 0, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 2, 3, 3, 3, 2, 0, 1, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 1, 0, 1, 1, 0, 1, 1, 2, 2, 3, 2, 0, 0, 1, 2, 3, 1, 0, 2, 3, 2, 3, 1, 0, 0, 0, 2, 0, 1, 2, 3, 2, 0, 1, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 2, 3, 3, 2, 3, 3, 2, 2, 2, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 2, 0, 2, 3, 1, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 2, 3, 2, 3, 0, 1, 0, 1, 1, 1, 2, 3, 0, 0, 1, 1, 2, 3, 1, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 3, 2, 2, 2, 2, 3, 3, 3, 2, 3, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 2, 3, 1, 0, 0, 1, 0, 1, 3, 1, 2, 3, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 2, 3, 0, 1, 2, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 1, 2, 2, 0, 1, 1, 0, 1, 0, 1, 3, 0, 0, 1, 3, 0, 0, 0, 1, 2, 3, 0, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 1, 0, 2, 3, 1, 2, 3, 1, 1, 0, 1, 0, 1, 0, 2, 2, 2, 3, 2, 3, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 2, 3, 3, 2, 3, 2, 3, 2]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}