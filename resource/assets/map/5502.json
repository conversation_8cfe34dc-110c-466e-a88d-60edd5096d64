{"mW": 960, "mH": 960, "tW": 24, "tH": 24, "tiles": [["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["369", 0, 3, 3], ["369", 2, 3, 3], ["369", 1, 3, 3], ["369", 3, 3, 3], ["315", 0, 3, 3], ["111", 0, 3, 2], ["111", 2, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["937", 0, 2, 1], ["937", 1, 2, 1]], "layers": [{"type": 3, "obj": [[2, "1230", 444, 753, 58, 34, 2], [2, "1224", 666, 701, 66, 59, 2], [2, "1224", 674, 712, 66, 59, 2], [2, "1230", 604, 719, 58, 34, 0], [2, "892", 364, 670, 44, 62, 0], [2, "1225", 330, 82, 48, 44, 2], [2, "1223", 358, 148, 56, 37, 0], [2, "1220", 382, 159, 48, 39, 0], [2, "1230_1", 800, 335, 58, 34, 2], [2, "1224", 650, 709, 66, 59, 2], [2, "1224", 653, 723, 66, 59, 2], [2, "1223", 658, 690, 56, 37, 2], [2, "1223", 640, 697, 56, 37, 2], [2, "1225", 321, 686, 48, 44, 0], [2, "1229", 362, 711, 48, 39, 0], [2, "1226", 394, 729, 70, 47, 0], [2, "1229", 300, 99, 48, 39, 2], [2, "1225_1", 473, 354, 48, 44, 2], [2, "1229_1", 434, 377, 48, 39, 2], [2, "1226_1", 381, 392, 70, 47, 2], [2, "1226", 247, 115, 70, 47, 2], [2, "1205", 244, 126, 42, 62, 0], [2, "1205", 275, 167, 42, 62, 0], [2, "1225", 167, 164, 48, 44, 2], [2, "1223", 195, 230, 56, 37, 0], [2, "1220", 219, 241, 48, 39, 0], [2, "1229", 137, 181, 48, 39, 2], [2, "1226", 84, 199, 70, 47, 2], [2, "1205", 81, 208, 42, 62, 0], [2, "1205", 112, 249, 42, 62, 0], [2, "1225", 7, 245, 48, 44, 2], [2, "1223", 31, 312, 56, 37, 0], [2, "1220", 55, 323, 48, 39, 0], [2, "1229", -25, 263, 48, 39, 2], [2, "1189", 855, 596, 142, 78, 2], [2, "1225_1", 683, 260, 48, 44, 0], [2, "1229_1", 725, 284, 48, 39, 0], [2, "1226_1", 758, 301, 70, 47, 0], [2, "1225_1", 861, 345, 48, 44, 0], [2, "1229_1", 903, 369, 48, 39, 0], [2, "1226_1", 936, 386, 70, 47, 0], [2, "1188", 864, 925, 80, 90, 0], [2, "1188", 843, 935, 80, 90, 0], [2, "1188", 822, 945, 80, 90, 0], [2, "1189", 753, 907, 142, 78, 0], [2, "1225_1", 319, 432, 48, 44, 2], [2, "1229_1", 277, 456, 48, 39, 2], [2, "1226_1", 224, 471, 70, 47, 2], [2, "1274", 633, 736, 24, 38, 0]]}, {"type": 4, "obj": [[2, "922", 658, -97, 30, 108, 0], [2, "829", 130, 32, 42, 54, 2], [2, "1272", 0, -18, 62, 109, 0], [2, "1272", 63, -17, 62, 109, 2], [2, "829", 153, 44, 42, 54, 2], [2, "1270", 67, 61, 46, 40, 0], [2, "829", 178, 59, 42, 54, 2], [2, "922", 861, 5, 30, 108, 0], [2, "1205", 368, 74, 42, 62, 0], [2, "922", 936, 41, 30, 108, 0], [2, "1205", 397, 113, 42, 62, 0], [2, "1222", 357, 99, 64, 80, 0], [2, "1222", 336, 111, 64, 80, 0], [2, "1222", 315, 122, 64, 80, 0], [2, "1222", 294, 133, 64, 80, 0], [2, "1205", 207, 157, 42, 62, 0], [2, "1224", 337, 160, 66, 59, 0], [2, "1222", 272, 142, 64, 80, 0], [2, "1219", 373, 178, 78, 46, 0], [2, "1205", 236, 196, 42, 62, 0], [2, "1222", 196, 182, 64, 80, 0], [2, "1214", 315, 215, 34, 55, 0], [2, "1222", 175, 194, 64, 80, 0], [2, "1222", 154, 205, 64, 80, 0], [2, "1222", 133, 216, 64, 80, 0], [2, "1205", 43, 239, 42, 62, 0], [2, "1224", 176, 243, 66, 59, 0], [2, "1222", 111, 225, 64, 80, 0], [2, "1219", 212, 261, 78, 46, 0], [2, "1205", 72, 278, 42, 62, 0], [2, "1222", 32, 264, 64, 80, 0], [2, "1214", 153, 299, 34, 55, 0], [2, "1222", 11, 276, 64, 80, 0], [2, "1228", 653, 285, 60, 75, 0], [2, "1222", -10, 287, 64, 80, 0], [2, "1228", 677, 297, 60, 75, 0], [2, "1222", -31, 298, 64, 80, 0], [2, "1228", 702, 308, 60, 75, 0], [2, "1224", 12, 325, 66, 59, 0], [2, "1222", -53, 307, 64, 80, 0], [2, "1219", 48, 343, 78, 46, 0], [2, "1228", 727, 319, 60, 75, 0], [2, "1228", 751, 330, 60, 75, 0], [2, "1214", -13, 380, 34, 55, 0], [2, "1228", 829, 369, 60, 75, 0], [2, "1228", 489, 375, 60, 75, 2], [2, "1228", 476, 381, 60, 75, 2], [2, "1228", 853, 381, 60, 75, 0], [2, "1230_1", 340, 425, 58, 34, 0], [2, "1228", 878, 392, 60, 75, 0], [2, "1228", 451, 393, 60, 75, 2], [2, "1228", 903, 403, 60, 75, 0], [2, "1228", 426, 405, 60, 75, 2], [2, "1228", 927, 414, 60, 75, 0], [2, "1228", 401, 416, 60, 75, 2], [2, "1228", 334, 454, 60, 75, 2], [2, "1228", 321, 460, 60, 75, 2], [2, "1230_1", 186, 504, 58, 34, 0], [2, "1228", 296, 472, 60, 75, 2], [2, "1228", 271, 484, 60, 75, 2], [2, "1228", 246, 495, 60, 75, 2], [2, "1188", 809, 614, 80, 90, 2], [2, "1188", 830, 625, 80, 90, 2], [2, "1283", 77, 652, 84, 72, 2], [2, "1188", 852, 635, 80, 90, 2], [2, "1282", 88, 656, 62, 73, 2], [2, "1282", 78, 661, 62, 73, 2], [2, "1188", 874, 645, 80, 90, 2], [2, "1282", 68, 667, 62, 73, 2], [2, "1188", 886, 652, 80, 90, 2], [2, "1282", 57, 673, 62, 73, 2], [2, "1282", 46, 679, 62, 73, 2], [2, "1188", 908, 662, 80, 90, 2], [2, "1190", 931, 667, 100, 86, 2], [2, "1282", 34, 684, 62, 73, 2], [2, "1279", 111, 702, 42, 58, 2], [2, "1282", 24, 691, 62, 73, 2], [2, "1282", 12, 696, 62, 73, 2], [2, "1283", -19, 705, 84, 72, 2], [2, "1279", 65, 721, 42, 58, 2], [2, "1197", 623, 735, 54, 44, 0], [2, "1222", 286, 710, 64, 80, 2], [2, "116", 562, 753, 46, 39, 0], [2, "1222", 305, 721, 64, 80, 2], [2, "116", 547, 762, 46, 39, 2], [2, "1208", 621, 751, 52, 56, 0], [2, "1208", 673, 751, 52, 56, 2], [2, "1222", 325, 731, 64, 80, 2], [2, "1197", 427, 770, 54, 44, 2], [2, "1222", 344, 741, 64, 80, 2], [2, "1222", 364, 751, 64, 80, 2], [2, "1222", 385, 761, 64, 80, 2]]}, {"type": 3, "obj": [[2, "1112", 349, 813, 46, 83, 0], [2, "1112_1", 430, 772, 46, 83, 2], [2, "1207", 467, 782, 22, 81, 2], [2, "1112_1", 430, 793, 46, 83, 2], [2, "1112_1", 410, 802, 46, 83, 2], [2, "1112", 634, 811, 46, 83, 0], [2, "1112_1", 680, 808, 46, 83, 2], [2, "1207", 731, 792, 22, 81, 0], [2, "1112_1", 697, 802, 46, 83, 2], [2, "1112", 535, 796, 46, 83, 0], [2, "1112_1", 572, 799, 46, 83, 2], [2, "1112", -4, 373, 46, 83, 2], [2, "1207", 76, 329, 22, 81, 2], [2, "1112", 40, 352, 46, 83, 2], [2, "1112", 161, 290, 46, 83, 2], [2, "1207", 241, 248, 22, 81, 2], [2, "1112", 204, 268, 46, 83, 2], [2, "1112_1", 263, 203, 46, 83, 0], [2, "1112", 323, 200, 46, 83, 2], [2, "1207", 401, 166, 22, 81, 2], [2, "1112", 365, 188, 46, 83, 2], [2, "1207", 352, 449, 22, 81, 2], [2, "1112_1", 373, 438, 46, 83, 0], [2, "1112_1", 363, 460, 46, 83, 0], [2, "1112_1", 387, 472, 46, 83, 0], [2, "1207", 84, 261, 22, 81, 0], [2, "1112_1", 779, 340, 46, 83, 2], [2, "1207", 822, 355, 22, 81, 0], [2, "1112_1", 789, 353, 46, 83, 2], [2, "479", 843, 771, 36, 18, 2], [2, "1207", 816, 690, 22, 81, 0], [2, "1112", 829, 706, 46, 83, 0], [2, "1112", 875, 729, 46, 83, 0], [2, "1112_1", 934, 733, 46, 83, 2], [2, "1197", 950, 706, 54, 44, 2], [2, "1275", 961, 702, 16, 19, 0], [2, "1192", 829, 698, 24, 25, 2], [2, "1192", 848, 707, 24, 25, 2], [2, "1192", 866, 715, 24, 25, 2], [2, "1192", 885, 724, 24, 25, 2], [2, "1192", 904, 734, 24, 25, 2], [2, "1207", 919, 738, 22, 81, 0], [2, "1280", 929, 753, 18, 15, 0], [2, "1274", 893, 753, 24, 38, 0], [2, "1274", 828, 723, 24, 38, 0], [2, "1194", 847, 741, 46, 60, 2], [2, "1207", 244, 179, 22, 81, 0], [2, "1192", 245, 167, 24, 25, 2], [2, "1207", 294, 781, 22, 81, 0], [2, "325", 28, 156, 50, 37, 2], [2, "910", 295, 818, 50, 68, 0], [2, "880", 315, 804, 66, 94, 2], [2, "890", 276, 803, 94, 105, 2], [2, "879", 630, 816, 26, 56, 2], [2, "898", 735, 780, 16, 25, 2], [2, "898", 558, 802, 16, 25, 0], [2, "899", 531, 779, 44, 22, 0], [2, "898", 545, 796, 16, 25, 0], [2, "898", 573, 802, 16, 25, 2], [2, "899", 550, 788, 44, 22, 0], [2, "898", 587, 795, 16, 25, 2], [2, "899", 552, 769, 44, 22, 0], [2, "899", 570, 778, 44, 22, 0], [2, "898", 673, 810, 16, 25, 2], [2, "898", 688, 803, 16, 25, 2], [2, "898", 703, 796, 16, 25, 2], [2, "898", 718, 789, 16, 25, 2], [2, "898", 733, 782, 16, 25, 2], [2, "829", 293, -41, 42, 54, 2], [2, "829", 449, -35, 42, 54, 0], [2, "829", 421, -22, 42, 54, 0], [2, "829", 397, -10, 42, 54, 0], [2, "829", 373, 0, 42, 54, 0], [2, "829", 316, -29, 42, 54, 2], [2, "829", 341, -14, 42, 54, 2], [2, "116", 713, 842, 46, 39, 0], [2, "116", 686, 855, 46, 39, 0], [2, "116", 726, 861, 46, 39, 2], [2, "763", 241, 847, 32, 31, 0], [2, "328", 410, 28, 32, 29, 0], [2, "328", 118, 563, 32, 29, 0], [2, "422", 439, 886, 16, 14, 0], [2, "422", 571, 479, 16, 14, 0], [2, "422", 280, 639, 16, 14, 2], [2, "328", 543, 869, 32, 29, 0], [2, "898", 532, 789, 16, 25, 0], [2, "422", 268, 860, 16, 14, 0], [2, "328", 932, 934, 32, 29, 0], [2, "328", 31, 940, 32, 29, 0], [2, "325", 157, 94, 50, 37, 0], [2, "327", 155, 100, 30, 22, 0], [2, "1194_1", 351, 208, 46, 60, 0], [2, "1207", 304, 212, 22, 81, 2], [2, "1192", 357, 189, 24, 25, 0], [2, "1192", 338, 198, 24, 25, 0], [2, "1192", 320, 207, 24, 25, 0], [2, "1217", 342, 258, 30, 30, 0], [2, "1213", 324, 244, 26, 62, 0], [2, "1192", 352, 202, 24, 25, 2], [2, "1217", 402, 232, 30, 30, 0], [2, "1218", 416, 187, 28, 82, 0], [2, "1218", 365, 212, 28, 82, 0], [2, "872", 703, 819, 26, 39, 2], [2, "325", 915, 836, 50, 37, 0], [2, "328", 936, 861, 32, 29, 0], [2, "327", 936, 819, 30, 22, 0], [2, "116", 512, 837, 46, 39, 2], [2, "325", 79, 918, 50, 37, 0], [2, "328", 100, 939, 32, 29, 0], [2, "327", 100, 901, 30, 22, 0], [2, "422", 3, 910, 16, 14, 0], [2, "325", 243, 932, 50, 37, 2], [2, "328", 551, 503, 32, 29, 0], [2, "422", 435, 251, 16, 14, 0], [2, "1207", 607, 790, 22, 81, 0], [2, "898", 602, 788, 16, 25, 2], [2, "898", 615, 789, 16, 25, 0], [2, "900", 617, 815, 52, 77, 0], [2, "898", 629, 796, 16, 25, 0], [2, "898", 644, 803, 16, 25, 0], [2, "898", 658, 810, 16, 25, 0], [2, "1192", 256, 184, 24, 25, 2], [2, "1192", 255, 172, 24, 25, 2], [2, "1192", 85, 249, 24, 25, 2], [2, "1194_1", 191, 290, 46, 60, 0], [2, "1192", 197, 271, 24, 25, 0], [2, "1192", 178, 280, 24, 25, 0], [2, "1192", 160, 289, 24, 25, 0], [2, "1217", 182, 340, 30, 30, 0], [2, "1213", 164, 326, 26, 62, 0], [2, "1192", 192, 284, 24, 25, 2], [2, "1217", 242, 314, 30, 30, 0], [2, "1218", 256, 269, 28, 82, 0], [2, "1218", 205, 294, 28, 82, 0], [2, "1192", 96, 266, 24, 25, 2], [2, "1192", 95, 254, 24, 25, 2], [2, "1194_1", 26, 371, 46, 60, 0], [2, "1192", 32, 352, 24, 25, 0], [2, "1192", 13, 361, 24, 25, 0], [2, "1192", -5, 370, 24, 25, 0], [2, "1217", 17, 421, 30, 30, 0], [2, "1213", -1, 407, 26, 62, 0], [2, "1192", 27, 365, 24, 25, 2], [2, "1217", 77, 395, 30, 30, 0], [2, "1218", 91, 350, 28, 82, 0], [2, "1218", 40, 375, 28, 82, 0], [2, "422", 113, 413, 16, 14, 0], [2, "328", 486, 224, 32, 29, 0], [2, "14_1", 138, 374, 32, 30, 0], [2, "124", 396, 785, 142, 70, 2], [2, "125", 524, 809, 18, 70, 0], [2, "125", 444, 836, 18, 70, 2], [2, "328", 445, 890, 32, 29, 0], [2, "327", 456, 217, 30, 22, 0], [2, "422", 776, 723, 16, 14, 0], [2, "1213", 667, 833, 26, 62, 0], [2, "422", 675, 885, 16, 14, 0], [2, "328", 774, 740, 32, 29, 0], [2, "327", 709, 894, 30, 22, 0], [2, "328", 207, -9, 32, 29, 0], [2, "327", 247, -3, 30, 22, 0], [2, "422", 891, 804, 16, 14, 0], [2, "325", 522, 16, 50, 37, 2], [2, "325", 829, 165, 50, 37, 0], [2, "926", 916, 61, 32, 25, 2], [2, "926", 887, 76, 32, 25, 2], [2, "927", 910, 53, 42, 24, 2], [2, "927", 878, 69, 42, 24, 2], [2, "927", 763, 0, 42, 24, 2], [2, "927", 731, 16, 42, 24, 2], [2, "925", 608, -14, 52, 34, 0], [2, "925", 632, -2, 52, 34, 0], [2, "925", 658, 10, 52, 34, 0], [2, "925", 681, 23, 52, 34, 0], [2, "925", 874, 120, 52, 34, 0], [2, "927", 592, -24, 42, 24, 2], [2, "925", 556, -4, 52, 34, 0], [2, "927", 850, 81, 42, 24, 2], [2, "925", 825, 105, 52, 34, 0], [2, "925", 836, 132, 52, 34, 0], [2, "925", 854, 142, 52, 34, 0], [2, "925", 877, 153, 52, 34, 0], [2, "925", 901, 165, 52, 34, 0], [2, "925", 926, 178, 52, 34, 0], [2, "925", 797, 138, 52, 34, 0], [2, "925", 577, 5, 52, 34, 0], [2, "925", 601, 17, 52, 34, 0], [2, "925", 625, 30, 52, 34, 0], [2, "925", 649, 42, 52, 34, 0], [2, "925", 638, 57, 52, 34, 0], [2, "925", 614, 70, 52, 34, 0], [2, "925", 638, 82, 52, 34, 0], [2, "925", 637, 61, 52, 34, 0], [2, "925", 662, 94, 52, 34, 0], [2, "925", 685, 106, 52, 34, 0], [2, "925", 709, 118, 52, 34, 0], [2, "925", 733, 131, 52, 34, 0], [2, "925", 660, 73, 52, 34, 0], [2, "925", 684, 85, 52, 34, 0], [2, "925", 708, 97, 52, 34, 0], [2, "925", 732, 110, 52, 34, 0], [2, "925", 797, 118, 52, 34, 0], [2, "927", 669, 48, 42, 24, 2], [2, "927", 660, 62, 42, 24, 0], [2, "927", 691, 78, 42, 24, 0], [2, "927", 723, 94, 42, 24, 0], [2, "926", 660, 67, 32, 25, 0], [2, "925", 757, 143, 52, 34, 0], [2, "925", 755, 122, 52, 34, 0], [2, "926", 740, 108, 32, 25, 0], [2, "927", 819, 97, 42, 24, 2], [2, "926", 826, 105, 32, 25, 2], [2, "926", 854, 91, 32, 25, 2], [2, "926", 683, 79, 32, 25, 0], [2, "926", 712, 94, 32, 25, 0], [2, "925", 772, 151, 52, 34, 0], [2, "925", 772, 131, 52, 34, 0], [2, "926", 764, 121, 32, 25, 0], [2, "927", 787, 114, 42, 24, 2], [2, "926", 797, 120, 32, 25, 2], [2, "921", 847, 97, 56, 68, 0], [2, "921", 643, -5, 56, 68, 0], [2, "921", 564, -46, 56, 68, 0], [2, "479", 612, 91, 36, 18, 2], [2, "479", 760, 168, 36, 18, 2], [2, "927", 700, 32, 42, 24, 2], [2, "925", 899, 132, 52, 34, 0], [2, "925", 923, 144, 52, 34, 0], [2, "921", 922, 134, 56, 68, 0], [2, "927", 795, -16, 42, 24, 2], [2, "329", 839, 129, 42, 37, 0], [2, "328", 547, 10, 32, 29, 0], [2, "328", 612, 44, 32, 29, 0], [2, "328", 845, 151, 32, 29, 0], [2, "328", 922, 190, 32, 29, 0], [2, "325", 596, 379, 50, 37, 0], [2, "422", 277, 334, 16, 14, 0], [2, "479", 682, 418, 36, 18, 2], [2, "1207", 655, 337, 22, 81, 0], [2, "1112", 668, 353, 46, 83, 0], [2, "1112", 714, 376, 46, 83, 0], [2, "1112_1", 787, 376, 46, 83, 2], [2, "1112_1", 773, 381, 46, 83, 2], [2, "1275", 802, 352, 16, 19, 0], [2, "1192", 668, 345, 24, 25, 2], [2, "1192", 687, 354, 24, 25, 2], [2, "1192", 705, 362, 24, 25, 2], [2, "1192", 724, 371, 24, 25, 2], [2, "1192", 743, 381, 24, 25, 2], [2, "1207", 758, 385, 22, 81, 0], [2, "1274", 732, 400, 24, 38, 0], [2, "1274", 667, 370, 24, 38, 0], [2, "1194", 686, 388, 46, 60, 2], [2, "872", 795, 394, 26, 39, 2], [2, "1197", 783, 362, 54, 44, 2], [2, "1112", 439, 471, 46, 83, 2], [2, "1207", 520, 434, 22, 81, 2], [2, "1112", 484, 449, 46, 83, 2], [2, "1112_1", 386, 427, 46, 83, 0], [2, "1192", 505, 440, 24, 25, 0], [2, "1192", 486, 450, 24, 25, 0], [2, "1192", 467, 459, 24, 25, 0], [2, "1192", 449, 469, 24, 25, 0], [2, "1192", 431, 478, 24, 25, 0], [2, "1207", 423, 478, 22, 81, 2], [2, "1284", 503, 500, 34, 26, 2], [2, "1274", 507, 461, 24, 38, 2], [2, "872", 379, 481, 26, 39, 0], [2, "1275", 389, 439, 16, 19, 2], [2, "1197", 355, 440, 54, 44, 0], [2, "1197", 378, 451, 54, 44, 0], [2, "1194", 468, 479, 46, 60, 0], [2, "1274", 444, 490, 24, 38, 2], [2, "1207", 198, 527, 22, 81, 2], [2, "1112_1", 219, 516, 46, 83, 0], [2, "1112_1", 209, 543, 46, 83, 0], [2, "1112_1", 234, 552, 46, 83, 0], [2, "1112", 285, 550, 46, 83, 2], [2, "1207", 366, 512, 22, 81, 2], [2, "1112", 330, 529, 46, 83, 2], [2, "1112_1", 232, 505, 46, 83, 0], [2, "1192", 351, 518, 24, 25, 0], [2, "1192", 332, 528, 24, 25, 0], [2, "1192", 313, 537, 24, 25, 0], [2, "1192", 295, 547, 24, 25, 0], [2, "1192", 277, 556, 24, 25, 0], [2, "1207", 269, 556, 22, 81, 2], [2, "1274", 353, 539, 24, 38, 2], [2, "872", 225, 559, 26, 39, 0], [2, "1275", 235, 517, 16, 19, 2], [2, "1197", 201, 521, 54, 44, 0], [2, "1197", 225, 533, 54, 44, 0], [2, "1194", 314, 557, 46, 60, 0], [2, "1274", 290, 568, 24, 38, 2], [2, "14_1", 775, 463, 32, 30, 2], [2, "328", 788, 478, 32, 29, 0], [2, "422", 946, 558, 16, 14, 0], [2, "325", 132, 736, 50, 37, 0], [2, "1207", 141, 720, 22, 81, 2], [2, "1112", 106, 733, 46, 83, 2], [2, "1112", 61, 756, 46, 83, 2], [2, "1112_1", -8, 751, 46, 83, 0], [2, "1112_1", -8, 711, 46, 83, 0], [2, "1112_1", 7, 760, 46, 83, 0], [2, "1112_1", 7, 719, 46, 83, 0], [2, "1207", 45, 765, 22, 81, 2], [2, "872", 5, 769, 26, 39, 0], [2, "1197", -7, 746, 54, 44, 0], [2, "1275", 15, 731, 16, 19, 2], [2, "1275", 70, 780, 16, 19, 0], [2, "1275", 136, 753, 16, 19, 0], [2, "1194", 90, 763, 46, 60, 0], [2, "14_1", -21, 824, 32, 30, 0], [2, "14_1", 6, 826, 32, 30, 0], [2, "328", 29, 834, 32, 29, 0], [2, "14_1", -8, 805, 32, 30, 0], [2, "328", 164, 779, 32, 29, 0], [2, "14_1", 395, 564, 32, 30, 0], [2, "14_1", 421, 567, 32, 30, 0], [2, "14_1", 400, 548, 32, 30, 0], [2, "872", 271, 219, 26, 39, 0], [2, "1112_1", 102, 282, 46, 83, 0], [2, "1207", 143, 293, 22, 81, 2], [2, "872", 108, 297, 26, 39, 0], [2, "99", 94, 86, 20, 32, 2], [2, "1271", 3, 67, 16, 54, 0], [2, "1271", 11, 76, 16, 54, 0], [2, "1271", 22, 84, 16, 54, 0], [2, "1271", 35, 87, 16, 54, 0], [2, "1271", 49, 90, 16, 54, 0], [2, "1271", 107, 65, 16, 54, 0], [2, "1271", 103, 75, 16, 54, 0], [2, "1268", 62, 118, 48, 27, 0], [2, "99", 77, 93, 20, 32, 2], [2, "1271", 63, 93, 16, 54, 0], [2, "1273", 68, 98, 14, 27, 0], [2, "1269", 13, 85, 34, 39, 0], [2, "927", 756, 110, 42, 24, 0], [2, "1207", 391, 813, 22, 81, 2], [2, "763", 365, 878, 32, 31, 0], [2, "763", 396, 881, 32, 31, 0], [2, "763", 350, 897, 32, 31, 0], [2, "763", 383, 894, 32, 31, 0], [2, "422", 369, 914, 16, 14, 0], [2, "763", 417, 895, 32, 31, 0], [2, "479", 860, 503, 36, 18, 2], [2, "1207", 833, 422, 22, 81, 0], [2, "1112", 846, 438, 46, 83, 0], [2, "1112", 893, 461, 46, 83, 0], [2, "1192", 846, 430, 24, 25, 2], [2, "1192", 865, 439, 24, 25, 2], [2, "1192", 883, 447, 24, 25, 2], [2, "1192", 902, 456, 24, 25, 2], [2, "1192", 921, 466, 24, 25, 2], [2, "1207", 938, 470, 22, 81, 0], [2, "1274", 910, 485, 24, 38, 0], [2, "1274", 845, 455, 24, 38, 0], [2, "1194", 864, 473, 46, 60, 2], [2, "1275", 583, 826, 16, 19, 0], [2, "1275", 542, 819, 16, 19, 2], [2, "73", 121, 65, 46, 72, 0], [2, "73", 28, 103, 46, 72, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 30, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 90, 96, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 30, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 101, 102, 112, 94, 96, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 30, -1, -1, 29, -1, -1, -1, -1, -1, 104, 105, 111, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 15, -1, -1, 11, 12, -1, -1, -1, -1, -1, -1, 16, 0, 1, 30, 30, -1, 24, 29, -1, -1, -1, -1, -1, 101, 102, 108, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, 5, 10, -1, -1, -1, -1, -1, 0, 1, 2, -1, 30, 30, -1, 42, 37, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, 96, 96, 96, 95, -1, 7, 6, 5, 24, 24, 5, 10, -1, -1, -1, -1, 30, -1, -1, 6, 5, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 99, 99, 99, 94, 96, 95, -1, 7, 6, 5, 24, 24, 7, 36, 36, 5, 30, 30, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 111, 99, 99, 99, 99, 94, 90, 91, -1, 7, 6, 5, 0, 1, 2, 6, 5, 42, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, 101, 102, 112, 99, 99, 99, 93, 94, 91, -1, -1, 7, 2, -1, -1, -1, 7, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 99, 99, 99, 99, 99, 98, -1, -1, -1, 97, 96, 95, -1, -1, -1, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, -1, -1, -1, 2, -1, 104, 99, 99, 105, 111, 99, 94, 96, 95, 97, 100, 99, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 43, 42, 39, -1, -1, -1, 97, 96, 100, 99, 105, 106, 108, 107, -1, 99, 98, 104, 105, 99, 94, 90, 91, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 0, 1, 2, -1, 2, -1, 97, 96, 100, 99, 99, 99, 106, 103, -1, -1, -1, -1, -1, 101, 102, 99, 105, 105, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, 89, 100, 99, 99, 99, 99, 99, 110, -1, -1, -1, -1, -1, -1, -1, -1, 109, 108, 102, 103, -1, -1, -1, -1, -1, -1, 42, 42, 42, -1, -1, -1, -1, -1, -1, -1, 97, 96, 95, -1, 92, 111, 111, 111, 99, 99, 106, 107, -1, -1, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 42, -1, 24, 0, 1, 2, -1, -1, -1, 2, 97, 96, 100, 99, 98, -1, 109, 108, 111, 111, 106, 108, 107, -1, -1, -1, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 2, 2, -1, -1, -1, 97, 100, 99, 99, 106, 103, -1, -1, -1, -1, 102, 103, -1, -1, 17, 16, 15, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 100, 99, 106, 108, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, 36, 37, -1, -1, -1, -1, -1, -1, -1, -1, 101, 102, 108, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 5, 24, 24, 24, 24, 24, 24, -1, -1, -1, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 15, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 43, 42, 24, 24, 24, -1, -1, -1, -1, -1, 97, 96, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, 97, 96, 90, 91, -1, 7, 6, 24, 24, 24, 24, 24, 24, 24, -1, 97, 96, 100, 111, 110, -1, -1, -1, -1, -1, -1, -1, 17, 16, -1, 15, -1, 24, 24, 24, 24, 0, 1, 2, -1, 101, 112, 111, 94, 95, 97, 91, 7, 6, 5, 5, 24, 24, 24, 89, 100, 99, 99, 106, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 0, 0, 1, 2, -1, -1, -1, -1, 104, 105, 99, 94, 100, 96, 95, -1, 7, 6, 5, 24, 24, 92, 99, 99, 99, 110, -1, -1, -1, -1, 15, -1, -1, -1, 24, 24, 24, 24, 0, 0, 1, 2, -1, -1, -1, -1, -1, -1, 101, 112, 111, 111, 111, 99, 98, -1, -1, -1, 7, 6, 5, 109, 112, 99, 99, 110, 6, -1, 17, 16, 15, -1, -1, -1, -1, 24, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, 97, 96, 100, 99, 99, 99, 111, 110, -1, -1, -1, -1, -1, 7, -1, 104, 105, 111, 110, -1, -1, 7, 6, 5, 24, -1, 24, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 89, 100, 93, 100, 99, 99, 106, 108, 107, -1, -1, -1, -1, -1, -1, -1, 101, 102, 108, 107, -1, -1, -1, -1, 7, 6, 5, 0, 1, 2, -1, -1, -1, -1, -1, 97, 96, 90, 91, -1, 92, 111, 111, 93, 106, 108, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 90, 100, 99, 93, 94, 95, 109, 112, 111, 99, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 90, 90, 91, -1, -1, -1, -1, -1, -1, 92, 93, 99, 99, 99, 99, 98, -1, 109, 108, 108, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 92, 93, 93, 94, 96, 95, -1, -1, -1, -1, 101, 112, 99, 105, 106, 108, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, 11, -1, -1, -1, -1, -1, -1, 11, -1, 104, 99, 99, 99, 99, 99, 99, -1, -1, -1, -1, 101, 102, 102, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 15, -1, -1, -1, 24, -1, -1, -1, -1, -1, 10, -1, -1, -1, 101, 112, 111, 99, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, -1, -1, -1, -1, -1, -1, 36, 5, -1, -1, -1, -1, 10, -1, -1, -1, -1, 24, 24, 10, 11, 12, -1, 109, 108, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, -1, -1, 10, 11, -1, -1, 7, 6, 5, 5, 24, 42, 0, 24, -1, 24, 24, 24, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 1, 2, -1, -1, -1, -1, 7, 6, 5, 36, 36, 24, 5, 24, 24, 0, 1, 2, -1, -1, -1, -1, 17, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 90, 91, -1, 7, 6, 5, 6, 5, 0, 1, 2, -1, -1, -1, -1, 17, 16, 15, -1, -1, -1, -1, -1, -1, -1, 30, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 92, 93, 94, 96, 95, -1, -1, -1, 7, 2, -1, -1, 97, 96, 96, 95, 7, 6, 5, 24, 24, 24, 24, 24, 24, 30, 30, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, 30, 29, 104, 105, 99, 99, 94, 95, -1, -1, -1, -1, -1, 89, 100, 99, 106, 107, -1, -1, 7, 6, 5, 24, 24, 24, 24, 24, 24, 24, 0, 1, 7, 6, 5, 24, 24, 24, 24, 0, 1, 2, 101, 102, 99, 99, 99, 98, -1, -1, -1, -1, -1, 101, 102, 102, 103, -1, -1, -1, -1, -1, 7, 6, 1, 7, 6, 5, 0, 1, 2, -1, -1, -1, 7, 6, 5, 0, 1, 2, -1, -1, -1, -1, 101, 102, 108, 107, -1, -1, -1, -1, -1, -1, -1, 20, 21, 22, 28, 27, 27, 26, -1, -1, -1, -1, -1, 7, 2, -1, -1, -1, -1, -1, -1, -1, 7, 2, -1, -1, -1, -1, 20, 21, 22, 20, 21, 22, -1]}, {"type": 3, "obj": [[2, "899", 690, 778, 44, 22, 0], [2, "899", 628, 787, 44, 22, 0], [2, "899", 631, 730, 44, 22, 0], [2, "899", 649, 739, 44, 22, 0], [2, "899", 669, 749, 44, 22, 0], [2, "899", 686, 758, 44, 22, 0], [2, "899", 708, 769, 44, 22, 0], [2, "899", 573, 759, 44, 22, 0], [2, "899", 591, 768, 44, 22, 0], [2, "899", 593, 749, 44, 22, 0], [2, "899", 611, 758, 44, 22, 0], [2, "899", 631, 768, 44, 22, 0], [2, "899", 613, 739, 44, 22, 0], [2, "899", 631, 748, 44, 22, 0], [2, "899", 651, 758, 44, 22, 0], [2, "899", 668, 767, 44, 22, 0], [2, "899", 611, 778, 44, 22, 0], [2, "899", 648, 777, 44, 22, 0], [2, "899", 670, 788, 44, 22, 0], [2, "899", 650, 798, 44, 22, 0], [2, "253_2", 358, 252, 92, 53, 0], [2, "253_2", 192, 333, 92, 53, 0], [2, "253_2", 27, 418, 92, 53, 0], [2, "253_2", 633, 433, 92, 53, 0], [2, "253_2", 813, 521, 92, 53, 0], [2, "253_2", 468, 523, 92, 53, 2], [2, "253_2", 318, 599, 92, 53, 2], [2, "253_2", 803, 781, 92, 53, 0], [2, "253_2", 86, 806, 92, 53, 2]]}, {"type": 2, "data": [75, 74, 85, 83, 83, 84, 85, 62, 63, 72, 76, 75, 66, 75, 66, 66, 66, 66, 75, 66, 67, 72, 76, 75, 81, 113, 114, 113, 114, 113, 114, 113, 114, 113, 114, 113, 114, 113, 114, 113, 67, 71, 88, 86, 55, 54, 53, 84, 85, 83, 73, 76, 75, 75, 66, 75, 66, 67, 72, 63, 64, 83, 73, 76, 75, 66, 81, 82, -1, 115, 116, 115, 116, 115, 116, 115, 116, 115, 116, 115, 56, -1, -1, -1, 58, 57, 56, 87, 88, 86, 87, 73, 72, 76, 66, 66, 67, 71, 80, 81, 80, 86, 87, 73, 72, 76, 75, 85, -1, 113, 114, 113, 114, 113, 114, 113, 114, 113, 114, 113, 66, -1, -1, -1, 57, 56, -1, 55, 54, 53, 83, 86, 87, 73, 72, 72, 71, 80, 81, 82, 83, 84, 85, 85, 83, 73, 72, 88, -1, 115, 116, 115, 116, 115, 116, 115, 116, 115, 116, 115, -1, -1, -1, -1, -1, 75, 66, 66, 66, 74, 86, 87, 86, 87, 88, -1, 80, 81, 82, 85, 86, 87, 88, 88, 80, 81, 80, 76, 75, 113, 114, 113, 114, 113, 114, 113, 114, 113, 114, 113, 75, 75, -1, -1, 67, 76, 75, 66, 67, 71, 55, 54, -1, -1, -1, -1, 83, 84, 85, 88, 80, 86, 87, 88, 83, 84, 83, 73, 72, 76, 75, -1, -1, -1, -1, -1, 115, 115, 116, 115, 76, 75, 67, 72, 71, 73, 72, 63, 64, 45, 46, -1, -1, -1, -1, -1, 86, 80, 81, 82, 83, 84, 85, 87, 86, 87, 86, 87, 88, 73, 72, 76, 80, 81, 82, 80, 81, 82, -1, -1, 73, 72, 71, 80, 81, 83, 84, 85, 47, 48, 49, -1, -1, -1, -1, -1, -1, 45, 46, 82, 86, 87, 88, 83, 84, 86, 87, 88, 86, 87, 88, 62, 76, 75, 66, 67, 76, 75, 81, 82, 80, 81, 82, 83, 84, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 45, 46, 82, 88, 86, 87, 88, 80, 81, 82, 86, 87, 88, 73, 72, 63, 64, 73, 72, 76, 75, 55, 54, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, 66, 48, 49, 53, 87, 88, 83, 84, 83, 84, 85, 81, 82, 80, 81, 82, 83, 84, 85, 84, 73, 76, 58, 66, 48, 49, -1, -1, -1, -1, -1, -1, 80, 81, 66, 66, 66, -1, 48, 48, 66, 67, 63, 64, 88, 86, 86, 87, 86, 87, 88, 84, 85, 81, 82, 85, 86, 87, 88, 87, 88, 73, 49, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 66, 81, 66, 48, 48, 48, 67, 64, 87, 80, 81, 82, 88, 84, 85, 87, 86, 87, 88, 84, 85, 88, 84, 85, 80, 81, 83, 84, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 66, 66, 67, 76, 66, 67, 72, 71, 88, 80, 83, 84, 85, 85, 87, 88, 87, 88, 84, 86, 87, 88, 88, 87, 88, 83, 84, 86, 87, -1, -1, -1, -1, 48, 48, 48, -1, -1, 48, 48, 48, 66, 74, 62, 63, 64, 80, 81, 82, 83, 86, 87, 88, 88, 86, 87, 88, 81, 82, 88, 83, 84, 85, 86, 87, 86, 87, 86, 87, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 48, 67, 63, 64, 86, 87, 88, 83, 84, 85, 86, 87, 88, 88, 81, 86, 87, 88, 84, 85, -1, 86, 87, 88, 80, 81, 82, 86, 87, 88, -1, 48, 48, 48, 48, 48, 48, 48, 67, 81, 82, 80, 86, 87, 88, 81, 83, 86, 87, 88, 86, 87, 88, 83, 84, 85, 85, 55, 54, 88, -1, -1, -1, -1, 83, 84, 85, 84, 85, 85, 66, 80, 81, 66, 66, 67, 72, 72, 71, 84, 85, 83, 84, 85, 83, 84, 86, 87, 88, 85, 83, 84, 85, 86, 87, 88, 44, 58, 57, -1, -1, -1, -1, -1, 86, 87, 88, 87, 88, 88, 66, 48, 48, 48, 75, 74, 84, 86, 86, 87, 88, 86, 87, 88, 80, 81, 82, 86, 87, 88, 86, 87, 88, 81, 82, 82, 65, 66, 75, -1, 80, 81, 82, -1, -1, 83, 84, 85, 86, 87, 66, 48, 48, 48, 67, 71, 87, 80, 80, 81, 80, 83, 84, 85, 83, 84, 85, 82, -1, 80, 81, 82, 45, 46, 85, 85, 62, 76, 75, 66, 75, 66, 66, 66, 67, 86, 87, 88, -1, -1, 48, 66, 67, 72, 71, 86, 87, 83, 83, 84, 83, 86, 87, 88, 86, 87, 88, 85, -1, 83, 84, 66, 48, 49, 54, 53, 86, 62, 63, 63, 72, 76, 75, 75, 75, 75, 84, 85, -1, -1, 67, 63, 64, 87, 86, 87, 88, 86, 86, 87, 86, 87, 88, -1, 80, 86, 87, 88, 75, 86, 80, 66, 66, 66, 57, 56, 82, 81, 80, 81, 82, 73, 72, 76, 75, 75, 75, 88, -1, -1, 74, 80, 81, 82, 81, 80, 80, 81, 80, 81, 82, 82, -1, 66, 80, 66, 66, 75, 66, 66, 66, 75, 75, 66, 75, 74, 85, 84, 83, 84, 85, 85, 80, 73, 76, 75, 75, 75, 75, 75, 71, 83, 84, 85, 84, 80, 81, 55, 54, 82, 85, 85, -1, 66, 66, 66, 75, 66, 66, 75, 75, 75, 67, 72, 72, 71, 80, 80, 81, 82, 80, 81, 82, 84, 73, 72, 76, 75, 75, 75, 86, 86, 87, 88, 87, 55, 54, 58, 57, 66, 88, 80, 81, 66, 66, 75, 75, 75, 75, 66, 67, 72, 71, 86, 87, 88, 80, 83, 84, 85, 83, 84, 85, 87, 88, 88, 73, 72, 76, 75, 86, 87, 88, 84, 85, 62, 76, 75, 66, 66, 66, 66, 84, 86, 75, 75, 57, 75, 75, 75, 74, 80, 81, 86, 80, 81, 82, 86, 87, 88, 86, 87, 88, 85, 80, 81, 82, 81, 65, 66, 83, 83, 86, 87, 88, 80, 73, 72, 76, 75, 66, 66, 66, 75, 75, 66, 67, 72, 72, 72, 71, 81, 82, 85, 83, 84, 85, 83, 80, 81, 82, 81, 82, 88, 83, 84, 85, 44, 58, 57, 86, 86, 87, 88, 86, 83, 84, 85, 73, 72, 76, 75, 66, 66, 67, 63, 64, 88, 86, 87, 80, 80, 81, 82, 80, 80, 81, 82, 83, 84, 85, 84, 85, 84, 86, 87, 88, 47, 57, 57, 55, 45, 54, 53, 88, 86, 87, 88, 83, 84, 73, 72, 72, 63, 64, 80, 80, 81, 80, 81, 83, 83, 84, 85, 83, 83, 84, 85, 86, 87, 88, 87, 88, 87, 86, 87, 88, 83, 84, 57, 58, 48, 57, 56, 86, 87, 84, 85, 86, 87, 86, 87, 88, 86, 87, 83, 83, 84, 83, 80, 81, 86, 87, 88, 86, 86, 87, 88, 88, 88, 81, 83, 84, 80, 86, 87, 88, 86, 57, 88, 75, -1, -1, -1, -1, -1, 87, 88, 84, 85, 80, 81, 82, 80, 81, 86, 86, 80, 86, 83, 84, 85, 84, 85, 88, 88, 84, 85, 81, 82, 80, 86, 87, 55, 54, 85, -1, 86, 87, 88, 75, -1, -1, -1, -1, 45, 46, 86, 87, 88, 83, 84, 85, 83, 84, 85, -1, 80, 86, 86, 87, 88, 87, 88, 84, 86, 87, 88, 84, 80, 83, 44, 45, 58, 57, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 54, 53, 88, 86, 87, 88, 86, 87, 88, -1, 83, 84, 85, 80, 83, 84, 86, 87, 88, -1, 86, 87, 83, 86, 65, 66, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 75, 49, 53, 86, 87, 87, -1, -1, -1, -1, 86, 87, 88, 83, 86, 87, 88, -1, -1, -1, -1, -1, 86, 87, 62, 63, 76, 75, 75, -1, -1, 57, 57, 66, 66, 66, 66, 75, 48, 66, 67, 72, 71, 86, 87, 88, -1, -1, -1, -1, -1, -1, -1, 86, 87, 88, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 73, 72, 76, 75, 75, 57, 57, 66, 75, 75, 75, 66, 75, 75, 74, 87, 88, 44, 45, 46, -1, -1, -1, -1, -1, -1, 45, 46, 87, 88, -1, -1, -1, -1, -1, -1, -1, -1, 54, 53, 83, 84, 73, 72, 76, 75, 75, 57, 57, 75, 66, 75, 67, 72, 71, 44, 45, 58, 75, -1, 75, -1, -1, -1, -1, -1, 48, 49, 54, -1, -1, -1, -1, 75, -1, -1, 75, -1, 75, 56, 88, 83, 84, 44, 58, 66, 75, 57, 57, 66, 67, 72, 71, 81, 82, 62, 63, 72, 72, 76, 75, -1, 75, -1, 75, 75, 75, 67, 72, 76, -1, 67, 76, 75, 75, 75, -1, -1, 75, 49, 53, 86, 87, 62, 63, 76, 75, 57, 57, 57, 74, 55, 45, 46, 80, 81, 82, 81, 82, 73, 72, 76, 75, -1, 75, 75, 63, 64, 80, 73, 72, 71, 73, 72, 72, 76, 75, 75, 75, 66, 56, 87, 86, 87, 88, 73, 72, 57, 57, 57, 49, 58, 48, 49, 46, 55, 54, 54, 53, 87, 88, 73, 72, 63, 72, 71, 80, 81, 86, 86, 86, 87, 88, 81, 82, 73, 72, 72, 72, 72, 59, 87, 87, 44, 45, 54, 53, 57, 57, 57, 57, 57, 57, 57, 49, 58, 57, 57, 56, 80, 81, 80, 81, 82, 81, 82, 83, 44, 45, 45, 46, 85, 86, 87, 88, 80, 80, 81, 82, 44, 45, 58, -1, 47, 48, 57, 56]}], "blocks": [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}