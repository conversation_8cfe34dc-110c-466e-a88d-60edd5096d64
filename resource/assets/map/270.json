{"mW": 840, "mH": 600, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["997", 0, 2, 2], ["998", 0, 3, 2], ["998", 2, 3, 2], ["998", 1, 3, 2], ["998", 3, 3, 2], ["444_3", 0, 2, 2], ["709", 0, 2, 1], ["999", 0, 3, 2], ["999", 2, 3, 2], ["999", 1, 3, 2], ["999", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "2", 622, 8, 90, 66, 2], [2, "2", 529, 6, 90, 66, 2], [2, "2", 582, -39, 90, 66, 2], [2, "224", 657, 465, 124, 194, 0], [2, "224", 368, 522, 124, 194, 2], [2, "1272", 46, 384, 62, 109, 0], [2, "1272", 109, 385, 62, 109, 2], [2, "1270", 113, 463, 46, 40, 0], [2, "713", 462, 94, 18, 27, 2]]}, {"type": 4, "obj": [[4, 5, 390, 33, 1, 4020], [4, 0, 444, 47, 0, 4020], [4, 2, 372, 71, 0, 4024], [2, "4", 220, -46, 122, 119, 2], [2, "3", 573, 8, 74, 116, 0], [2, "488", 339, 104, 32, 51, 2], [2, "1367", 294, 118, 52, 40, 2], [2, "73", 586, 91, 46, 72, 0], [2, "714", 435, 55, 54, 132, 0], [2, "488", 270, 137, 32, 51, 2], [2, "73", 708, 158, 46, 72, 0], [4, 3, 130, 242, 1, 4024], [2, "224", -52, 64, 124, 194, 0], [2, "219", 3, 232, 36, 30, 2], [2, "224", 757, 97, 124, 194, 0], [2, "219", 61, 262, 36, 30, 2], [2, "488", 686, 250, 32, 51, 0], [2, "1367", 708, 265, 52, 40, 0], [2, "224", -13, 120, 124, 194, 2], [2, "220", 35, 291, 40, 29, 0], [2, "488", 751, 282, 32, 51, 0], [2, "429_1", -17, 279, 64, 63, 2], [2, "224", 195, 261, 124, 194, 2], [4, 1, 228, 479, 0, 4024], [2, "99", 140, 482, 20, 32, 2], [2, "1271", 153, 461, 16, 54, 0], [2, "1271", 49, 463, 16, 54, 0], [2, "1269", 59, 481, 34, 39, 0], [2, "1273", 114, 494, 14, 27, 0], [2, "99", 123, 489, 20, 32, 2], [2, "1271", 149, 471, 16, 54, 0], [2, "1271", 57, 472, 16, 54, 0], [2, "1271", 68, 480, 16, 54, 0], [2, "1271", 81, 483, 16, 54, 0], [2, "1271", 95, 486, 16, 54, 0], [2, "1268", 108, 514, 48, 27, 0], [2, "1271", 109, 489, 16, 54, 0], [2, "73", 158, 481, 46, 72, 0], [4, 3, 633, 596, 1, 4024], [2, "73", 88, 526, 46, 72, 0]]}, {"type": 3, "obj": [[2, "173_1", 679, 263, 70, 45, 2], [2, "325_1", 189, 402, 50, 37, 0], [2, "262_1", 181, 423, 48, 39, 0], [2, "325_1", 122, 489, 50, 37, 2], [2, "174", 749, 186, 68, 33, 1], [2, "21", 751, 182, 28, 24, 0], [2, "173_1", 469, 557, 70, 45, 2], [2, "328_1", 531, 570, 32, 29, 0], [2, "174", 566, 574, 68, 33, 1], [2, "421_1", 31, 938, 14, 11, 0], [2, "21", 54, 557, 28, 24, 0], [2, "420_1", 49, 572, 16, 13, 0], [2, "173_1", 311, 392, 70, 45, 0], [2, "174", 179, 525, 68, 33, 1], [2, "174", 353, 396, 68, 33, 1], [2, "426", 428, 112, 26, 22, 0], [2, "325_1", 22, 381, 50, 37, 0], [2, "220", 245, 423, 40, 29, 0], [2, "325_1", 639, 121, 50, 37, 2], [2, "174", 722, 283, 68, 33, 1], [2, "1325_1", 438, 86, 80, 36, 2], [2, "173_1", 463, 59, 70, 45, 2], [2, "1324_2", 481, 111, 40, 43, 0], [2, "174", 495, 96, 68, 33, 1], [2, "173_1", 473, 137, 70, 45, 2], [2, "173_1", 352, 143, 70, 45, 2], [2, "173_1", 316, 71, 70, 45, 2], [2, "1322_2", 365, 115, 64, 38, 0], [2, "1325_1", 359, 86, 80, 36, 0], [2, "1323_2", 411, 140, 94, 24, 0], [2, "21", 410, 149, 28, 24, 0], [2, "420_1", 405, 164, 16, 13, 0], [2, "328_1", 429, 148, 32, 29, 0], [2, "422_1", 376, 131, 16, 14, 0], [2, "220", 452, 144, 40, 29, 0], [2, "174", 318, 95, 68, 33, 1], [2, "426", 692, 401, 26, 22, 0], [2, "1325_1", 702, 375, 80, 36, 2], [2, "173_1", 753, 328, 70, 45, 2], [2, "1324_2", 745, 400, 40, 43, 0], [2, "174", 759, 385, 68, 33, 1], [2, "173_1", 737, 426, 70, 45, 2], [2, "173_1", 623, 427, 70, 45, 2], [2, "173_1", 580, 360, 70, 45, 2], [2, "1322_2", 629, 404, 64, 38, 0], [2, "1325_1", 623, 375, 80, 36, 0], [2, "1323_2", 675, 429, 94, 24, 0], [2, "21", 674, 436, 28, 24, 0], [2, "420_1", 669, 451, 16, 13, 0], [2, "328_1", 695, 432, 32, 29, 0], [2, "422_1", 640, 420, 16, 14, 0], [2, "220", 716, 433, 40, 29, 0], [2, "174", 582, 384, 68, 33, 1], [2, "219", 702, 75, 36, 30, 0], [2, "262_1", 327, 537, 48, 39, 2], [2, "325_1", 50, 16, 50, 37, 2], [2, "328_1", 34, 46, 32, 29, 0], [2, "262_1", 21, 12, 48, 39, 2], [2, "422_1", 654, 384, 16, 14, 0], [2, "422_1", 324, 389, 16, 14, 0], [2, "174", 491, 264, 68, 33, 1], [2, "220", 226, 34, 40, 29, 0], [2, "705", 708, -23, 74, 157, 0], [2, "705", 781, -23, 74, 157, 2], [2, "396", 741, 59, 34, 59, 0], [2, "1269", 793, 64, 34, 39, 2], [2, "1268", 733, 50, 48, 27, 2], [2, "328_1", 824, 91, 32, 29, 0], [2, "113", 754, 161, 26, 33, 0], [2, "420_1", 126, 259, 16, 13, 0], [2, "116_2", 783, 163, 46, 39, 0], [2, "220", 804, 467, 40, 29, 0], [2, "220", 468, -13, 40, 29, 0], [2, "219", 772, 110, 36, 30, 2], [2, "435_1", 1796, 377, 50, 77, 2], [2, "594", 646, 95, 52, 46, 0], [2, "594", 297, 421, 52, 46, 2], [2, "1366", 703, 291, 52, 39, 0], [2, "710", 712, 250, 38, 62, 0], [2, "173_1", 631, 294, 70, 45, 2], [2, "173_1", 362, 371, 70, 45, 0], [2, "173_1", 585, 528, 70, 45, 0], [2, "339", 696, 329, 22, 22, 0], [2, "339", 565, 562, 22, 22, 0], [2, "1366", 294, 146, 52, 39, 0], [2, "710", 303, 105, 38, 62, 0], [2, "339", 748, 275, 22, 22, 0], [2, "173_1", 243, 127, 70, 45, 0], [2, "339", 331, 181, 22, 22, 0], [2, "90", 787, 111, 28, 36, 0], [2, "13", 777, 328, 22, 24, 0], [2, "13", 751, 346, 22, 24, 0], [2, "13", 379, 143, 22, 24, 0], [2, "13", 368, 160, 22, 24, 0], [2, "13", 668, 272, 22, 24, 0], [2, "429_1", 501, -20, 64, 63, 2], [2, "488", 566, 495, 32, 51, 2], [2, "488", 543, 506, 32, 51, 2], [2, "488", 517, 518, 32, 51, 2], [2, "488", 395, 399, 32, 51, 2], [2, "488", 372, 410, 32, 51, 2], [2, "488", 345, 423, 32, 51, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, -1, 58, 59, 60, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 58, 69, 68, 63, 65, 64, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, -1, 7, -1, -1, 61, 62, 80, 80, 80, 79, -1, -1, -1, -1, -1, -1, -1, 28, 28, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 11, 10, -1, -1, 10, -1, -1, 70, 71, 81, 80, 80, 79, -1, -1, -1, -1, -1, -1, -1, -1, 17, 19, 19, 23, 22, -1, -1, -1, 0, 1, 7, 6, -1, 12, 23, 22, 16, 16, 17, -1, -1, -1, -1, 78, 77, 77, 76, -1, -1, -1, -1, -1, -1, -1, -1, 21, -1, -1, 20, 19, -1, -1, -1, 12, 16, 16, 18, -1, -1, 20, 19, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, 7, 2, 0, 1, 2, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 59, 59, 65, 64, -1, -1, -1, -1, 58, 65, 65, 60, -1, -1, -1, -1, -1, 0, 1, 2, 20, 19, 19, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 81, 62, 80, 63, 65, 64, -1, -1, 61, 62, 62, 63, 65, 65, 60, -1, -1, 20, 19, 18, -1, -1, -1, 22, 52, 7, 6, -1, -1, -1, -1, -1, -1, -1, 78, 81, 80, 80, 74, 75, 76, -1, -1, 70, 81, 62, 62, 62, 62, 67, -1, -1, -1, -1, -1, -1, -1, -1, 19, 23, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, 70, 71, 71, 77, 76, -1, -1, -1, -1, 73, 62, 62, 74, 75, 76, -1, -1, -1, -1, -1, -1, 0, 1, 11, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 59, 69, 62, 62, 80, 79, -1, -1, -1, 0, 1, 2, -1, 12, 13, 22, 21, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 62, 62, 75, 77, 76, -1, -1, -1, 12, 13, 14, -1, -1, -1, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 80, 80, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 71, 77, 77, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 19, 19, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 6, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 8, 21, -1, -1, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 7, 11, 16, 6, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 19, 19, 19, 18, -1, 58, 59, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 74, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, -1, 15, 16, 1, 2, 22, 21, 8, 7, 6, -1, -1, 8, 6, 66, 65, 69, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 19, 18, 19, 18, 20, 19, 18, -1, -1, 12, 14, -1, 71, 71, 77, 76, -1, -1, -1, 0, 1, 2, 58, 59, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, 20, 19, 18, 69, 68, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 71, 71, 81, 63, 65, 64, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "597", 626, 115, 34, 26, 2], [2, "173_1", 766, 115, 70, 45, 2], [2, "325_1", 406, 326, 50, 37, 2], [2, "173_1", 185, 274, 70, 45, 2], [2, "173_1", 238, 257, 70, 45, 0], [2, "173_1", 292, 303, 70, 45, 2], [2, "173_1", 84, 408, 70, 45, 0], [2, "174", 86, 444, 68, 33, 3], [2, "173_1", 72, 450, 70, 45, 0], [2, "173_1", 20, 469, 70, 45, 2], [2, "173_1", 765, 85, 70, 45, 2], [2, "173_1", 279, 494, 70, 45, 2], [2, "173_1", 174, 501, 70, 45, 0], [2, "173_1", 101, 542, 70, 45, 2], [2, "173_1", 316, 466, 70, 45, 2], [2, "173_1", 322, 515, 70, 45, 0], [2, "173_1", 236, 513, 70, 45, 0], [2, "173_1", 280, 533, 70, 45, 0], [2, "173_1", 718, 103, 70, 45, 0], [2, "597", 278, 445, 34, 26, 2], [2, "597", 293, 421, 34, 26, 0], [2, "598", -1, 342, 18, 22, 0]]}, {"type": 2, "data": [52, 53, 52, 53, 54, 55, 52, 53, 52, 53, 53, 53, 53, 53, 53, 52, 53, 53, 52, 53, 52, 54, 55, 41, 41, 51, 52, 53, 52, 53, 52, 53, 53, 52, 53, 52, 52, 53, 45, 47, 47, 51, 55, 54, 55, 45, 51, 53, 45, 42, 25, 48, 47, 47, 51, 54, 45, 25, 24, 25, 40, 51, 50, 54, 55, 54, 55, 55, 45, 47, 52, 54, 55, 42, 25, 24, 40, 41, 41, 41, 42, 51, 55, 33, 27, 27, 26, 26, 27, 48, 47, 46, 27, 26, 27, 27, 48, 47, 53, 52, 53, 52, 53, 50, 49, 54, 37, 27, 24, 28, 53, 34, 25, 24, 36, 35, 39, 52, 54, 55, 29, 29, 35, 34, 26, 27, 27, 26, 28, 29, 25, 26, 26, 27, 48, 47, 54, 55, 47, 51, 52, 49, 24, 26, 31, 55, 33, 30, 26, 31, 52, 53, 53, 52, 53, 53, 56, 57, 56, 57, 56, 33, 33, 39, 52, 53, 50, 49, 25, 26, 27, 24, 25, 25, 31, 54, 55, 26, 24, 31, 51, 52, 33, 29, 39, 44, 45, 47, 47, 51, 53, 57, 56, 57, 56, 57, 52, 52, 52, 54, 55, 50, 49, 27, 24, 25, 26, 27, 27, 43, 52, 53, 35, 34, 24, 48, 51, 55, 54, 52, 45, 42, 26, 27, 48, 53, 53, 57, 56, 57, 56, 52, 47, 51, 54, 45, 47, 46, 28, 30, 27, 28, 29, 35, 39, 52, 53, 38, 37, 24, 36, 35, 52, 53, 54, 33, 35, 34, 25, 40, 41, 47, 51, 50, 53, 44, 45, 24, 48, 41, 42, 24, 28, 44, 33, 29, 54, 54, 55, 44, 54, 55, 38, 37, 36, 39, 38, 54, 55, 54, 54, 38, 37, 24, 25, 24, 25, 48, 47, 41, 41, 42, 26, 27, 26, 27, 24, 31, 53, 52, 52, 52, 52, 52, 52, 52, 53, 27, 26, 39, 37, 27, 40, 41, 41, 47, 46, 27, 26, 27, 24, 24, 25, 24, 25, 25, 25, 24, 24, 25, 25, 26, 31, 51, 54, 52, 52, 53, 52, 54, 52, 52, 53, 52, 52, 33, 35, 34, 36, 35, 35, 34, 27, 26, 27, 26, 27, 36, 35, 35, 29, 30, 24, 26, 24, 27, 27, 27, 31, 52, 52, 53, 55, 45, 47, 52, 53, 55, 54, 54, 55, 55, 33, 39, 52, 53, 33, 35, 35, 29, 29, 29, 39, 52, 53, 52, 33, 30, 27, 26, 28, 30, 27, 43, 53, 54, 55, 53, 49, 25, 52, 53, 53, 54, 52, 53, 52, 53, 55, 54, 55, 54, 55, 54, 55, 54, 55, 54, 55, 54, 55, 53, 33, 34, 28, 39, 33, 35, 39, 52, 53, 54, 55, 30, 25, 52, 53, 55, 52, 53, 53, 53, 52, 53, 52, 45, 40, 41, 51, 50, 53, 53, 54, 55, 52, 54, 55, 52, 33, 39, 52, 54, 52, 54, 54, 55, 54, 55, 33, 29, 54, 55, 52, 52, 53, 52, 52, 53, 55, 54, 49, 26, 27, 48, 51, 55, 52, 53, 53, 53, 54, 55, 54, 55, 55, 52, 53, 54, 52, 53, 54, 55, 52, 53, 52, 52, 53, 52, 53, 55, 54, 54, 55, 54, 53, 53, 35, 34, 27, 43, 53, 54, 55, 55, 55, 52, 53, 53, 52, 53, 54, 52, 53, 54, 52, 53, 55, 54, 55, 52, 52, 53, 54, 55, 53, 53, 52, 53, 52, 53, 35, 52, 52, 35, 39, 52, 53, 52, 53, 53, 54, 52, 53, 54, 55, 54, 52, 56, 57, 56, 57, 56, 52, 53, 54, 54, 55, 52, 54, 55, 55, 45, 51, 54, 55, 52, 52, 54, 52, 52, 53, 55, 45, 51, 55, 52, 54, 52, 52, 53, 52, 52, 57, 56, 57, 56, 57, 54, 52, 52, 55, 53, 54, 45, 41, 47, 46, 48, 47, 51, 50, 52, 53, 52, 54, 55, 45, 25, 48, 54, 54, 52, 52, 52, 52, 54, 55, 56, 57, 56, 57, 56, 52, 54, 54, 54, 55, 50, 49, 27, 27, 26, 26, 43, 44, 52, 53, 52, 52, 45, 41, 42, 27, 25, 40, 51, 52, 53, 52, 53, 52, 53, 52, 53, 52, 53, 52, 53, 52, 53, 45, 47, 47, 46, 25, 24, 25, 25, 40, 41, 52, 52, 52, 41, 42, 24, 25, 25, 28, 29, 39, 53, 55, 54, 55, 54, 55, 54, 55, 54, 55, 52, 53, 54, 55, 45, 24, 25, 27, 27, 26, 27, 27, 26, 27, 39, 53, 54, 33, 34, 26, 27, 27, 31, 32, 54, 55, 55, 53, 54, 54, 55, 54, 55, 54, 46, 54, 55, 54, 45, 33, 35, 35, 34, 25, 25, 25, 26, 27, 36, 39, 55, 54, 55, 49, 26, 27, 25, 40, 52, 53, 52, 52, 53, 54, 54, 45, 47, 46, 26, 27, 51, 54, 45, 42, 54, 55, 52, 33, 35, 27, 27, 36, 35, 39, 52, 52, 52, 52, 50, 36, 35, 35, 39, 54, 55, 54, 54, 55, 47, 47, 24, 25, 24, 24, 25, 40, 51, 33, 34, 53, 52, 54, 52, 52, 50, 50, 39, 52, 54, 52, 45, 41, 41, 51, 52, 54, 55, 52, 53, 52, 53, 55, 33, 35, 34, 26, 27, 26, 26, 27, 27, 43, 54, 33]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}