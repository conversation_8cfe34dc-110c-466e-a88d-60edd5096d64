{"mW": 1200, "mH": 960, "tW": 24, "tH": 24, "tiles": [["417", 0, 1, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["3153", 0, 1, 1], ["3117", 0, 3, 3], ["691", 0, 2, 2], ["1233", 0, 3, 2], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1]], "layers": [{"type": 3, "obj": [[2, "348", 929, 650, 70, 61, 2], [2, "348", 415, 723, 70, 61, 2], [2, "1423", 935, 450, 20, 52, 0], [2, "1423", 969, 462, 20, 52, 0], [2, "3439", 1097, 8, 36, 48, 0], [2, "3439", 1038, -13, 36, 48, 0], [2, "1421", 1023, -16, 10, 26, 0], [2, "1421", 1143, 30, 10, 26, 0], [2, "1421", 1179, 51, 10, 26, 0], [2, "1421", 979, -36, 10, 26, 0], [2, "333", 531, -10, 38, 35, 0], [2, "333", 557, -2, 38, 35, 0], [2, "333", 603, 38, 38, 35, 0], [2, "333", 609, 59, 38, 35, 0], [2, "333", 626, 48, 38, 35, 0], [2, "333", 577, -8, 38, 35, 0], [2, "333", 550, 24, 38, 35, 0], [2, "333", 525, 10, 38, 35, 0], [2, "333", 585, 16, 38, 35, 0], [2, "333", 578, 58, 38, 35, 0], [2, "333", 518, 32, 38, 35, 0], [2, "332", 492, 19, 36, 38, 0], [2, "332", 535, 2, 36, 38, 0], [2, "332", 516, 13, 36, 38, 0], [2, "332", 576, 28, 36, 38, 0], [2, "332", 610, 46, 36, 38, 0], [2, "332", 609, 12, 36, 38, 0], [2, "3148", 1094, -37, 70, 55, 2], [2, "3148", 1129, -19, 70, 55, 2], [2, "3148", 1165, 0, 70, 55, 2], [2, "3148", 1201, 16, 70, 55, 2], [2, "346", 49, 410, 72, 71, 0], [2, "346", 13, 428, 72, 71, 0], [2, "346", -22, 446, 72, 71, 0], [2, "1225", 47, 390, 48, 44, 2], [2, "1226", -16, 414, 70, 47, 2], [2, "346", 111, 243, 72, 71, 2], [2, "346", 148, 262, 72, 71, 2], [2, "346", 182, 279, 72, 71, 2], [2, "348", 254, 300, 70, 61, 2], [2, "346", 214, 294, 72, 71, 2], [2, "736", 240, 274, 56, 42, 0], [2, "739", 133, 218, 40, 39, 0], [2, "738", 165, 238, 46, 37, 0], [2, "738", 203, 256, 46, 37, 0], [2, "1421", 109, 468, 10, 26, 0], [2, "1421", 29, 507, 10, 26, 0], [2, "1423", 516, 241, 20, 52, 0], [2, "1423", 550, 253, 20, 52, 0], [2, "1423", 347, 317, 20, 52, 0], [2, "1423", 381, 332, 20, 52, 0], [2, "346", -55, 466, 72, 71, 0], [2, "3226", 782, 609, 48, 45, 0], [2, "3226", 805, 622, 48, 45, 0], [2, "3226", 828, 635, 48, 45, 0], [2, "3226", 851, 647, 48, 45, 0], [2, "3226", 873, 656, 48, 45, 0], [2, "3226", 895, 668, 48, 45, 0], [2, "3226", 807, 580, 48, 45, 0], [2, "3226", 830, 593, 48, 45, 0], [2, "3226", 853, 606, 48, 45, 0], [2, "3226", 876, 618, 48, 45, 0], [2, "3226", 898, 627, 48, 45, 0], [2, "3226", 920, 639, 48, 45, 0], [2, "739", 816, 556, 40, 39, 0], [2, "738", 847, 577, 46, 37, 0], [2, "738", 882, 597, 46, 37, 0], [2, "738", 917, 616, 46, 37, 0], [2, "1403", 974, 646, 18, 22, 2], [2, "736", 928, 621, 56, 42, 0], [2, "1403", 983, 661, 18, 22, 2], [2, "332", 1042, 549, 36, 38, 0], [2, "333", 984, 613, 38, 35, 0], [2, "333", 1010, 600, 38, 35, 0], [2, "333", 1010, 600, 38, 35, 0], [2, "333", 1045, 589, 38, 35, 0], [2, "333", 1091, 659, 38, 35, 0], [2, "333", 1077, 643, 38, 35, 0], [2, "333", 1096, 637, 38, 35, 0], [2, "333", 1089, 616, 38, 35, 0], [2, "333", 977, 592, 38, 35, 0], [2, "333", 959, 596, 38, 35, 0], [2, "333", 959, 596, 38, 35, 0], [2, "333", 993, 572, 38, 35, 0], [2, "333", 1022, 579, 38, 35, 0], [2, "333", 1022, 579, 38, 35, 0], [2, "333", 1025, 561, 38, 35, 0], [2, "333", 1025, 561, 38, 35, 0], [2, "333", 1057, 614, 38, 35, 0], [2, "333", 1072, 590, 38, 35, 0], [2, "333", 1046, 568, 38, 35, 0], [2, "333", 1068, 554, 38, 35, 0], [2, "333", 1068, 554, 38, 35, 0], [2, "333", 1092, 577, 38, 35, 0], [2, "332", 1096, 595, 36, 38, 0], [2, "332", 1005, 577, 36, 38, 0], [2, "332", 976, 581, 36, 38, 0], [2, "332", 1007, 627, 36, 38, 0], [2, "332", 1059, 647, 36, 38, 0], [2, "332", 1114, 629, 36, 38, 0], [2, "332", 1074, 569, 36, 38, 0], [2, "332", 1046, 553, 36, 38, 0], [2, "332", 1042, 584, 36, 38, 0], [2, "332", 1037, 610, 36, 38, 0], [2, "3196", 1032, 690, 76, 55, 0], [2, "333", 839, 540, 38, 35, 0], [2, "333", 862, 514, 38, 35, 0], [2, "333", 862, 514, 38, 35, 0], [2, "333", 873, 491, 38, 35, 0], [2, "333", 873, 491, 38, 35, 0], [2, "333", 893, 512, 38, 35, 0], [2, "333", 836, 515, 38, 35, 0], [2, "333", 816, 531, 38, 35, 0], [2, "333", 944, 572, 38, 35, 0], [2, "333", 964, 548, 38, 35, 0], [2, "333", 964, 548, 38, 35, 0], [2, "333", 928, 524, 38, 35, 0], [2, "333", 928, 524, 38, 35, 0], [2, "333", 918, 483, 38, 35, 0], [2, "333", 918, 483, 38, 35, 0], [2, "333", 907, 505, 38, 35, 0], [2, "333", 907, 505, 38, 35, 0], [2, "333", 946, 527, 38, 35, 0], [2, "332", 856, 525, 36, 38, 0], [2, "332", 871, 493, 36, 38, 0], [2, "332", 897, 467, 36, 38, 0], [2, "332", 897, 467, 36, 38, 0], [2, "332", 906, 547, 36, 38, 0], [2, "332", 948, 513, 36, 38, 0], [2, "332", 981, 529, 36, 38, 0], [2, "332", 837, 547, 36, 38, 0], [2, "332", 894, 485, 36, 38, 0], [2, "332", 894, 485, 36, 38, 0], [2, "332", 914, 521, 36, 38, 0], [2, "332", 934, 494, 36, 38, 0], [2, "332", 954, 554, 36, 38, 0], [2, "332", 998, 547, 36, 38, 0], [2, "332", 1018, 524, 36, 38, 0], [2, "3226", 268, 683, 48, 45, 0], [2, "3226", 291, 696, 48, 45, 0], [2, "3226", 314, 709, 48, 45, 0], [2, "3226", 337, 721, 48, 45, 0], [2, "3226", 359, 730, 48, 45, 0], [2, "3226", 381, 742, 48, 45, 0], [2, "3226", 293, 654, 48, 45, 0], [2, "3226", 316, 667, 48, 45, 0], [2, "3226", 339, 680, 48, 45, 0], [2, "3226", 362, 692, 48, 45, 0], [2, "3226", 384, 701, 48, 45, 0], [2, "3226", 406, 713, 48, 45, 0], [2, "739", 302, 630, 40, 39, 0], [2, "738", 333, 651, 46, 37, 0], [2, "738", 368, 671, 46, 37, 0], [2, "738", 403, 690, 46, 37, 0], [2, "1403", 460, 720, 18, 22, 2], [2, "736", 414, 695, 56, 42, 0], [2, "1403", 469, 735, 18, 22, 2], [2, "3226", 78, 752, 48, 45, 2], [2, "3226", 56, 763, 48, 45, 2], [2, "3226", 33, 774, 48, 45, 2], [2, "3226", 11, 785, 48, 45, 2], [2, "3226", -12, 796, 48, 45, 2], [2, "3226", -33, 805, 48, 45, 2], [2, "3226", 53, 722, 48, 45, 2], [2, "3226", 31, 733, 48, 45, 2], [2, "3226", 8, 744, 48, 45, 2], [2, "3226", -14, 755, 48, 45, 2], [2, "3226", -37, 766, 48, 45, 2], [2, "3226", -58, 775, 48, 45, 2], [2, "739", 53, 701, 40, 39, 2], [2, "738", 18, 721, 46, 37, 2], [2, "738", -17, 740, 46, 37, 2], [2, "333", -3, 408, 38, 35, 0], [2, "332", 13, 395, 36, 38, 0], [2, "333", 1062, 438, 38, 35, 0], [2, "333", 1065, 457, 38, 35, 0], [2, "333", 1034, 445, 38, 35, 0], [2, "333", 1096, 401, 38, 35, 0], [2, "333", 1118, 414, 38, 35, 0], [2, "333", 1118, 414, 38, 35, 0], [2, "333", 1116, 386, 38, 35, 0], [2, "333", 1116, 386, 38, 35, 0], [2, "333", 1144, 398, 38, 35, 0], [2, "333", 1165, 426, 38, 35, 0], [2, "333", 1186, 417, 38, 35, 0], [2, "333", 1186, 417, 38, 35, 0], [2, "333", 1095, 371, 38, 35, 0], [2, "333", 1095, 371, 38, 35, 0], [2, "333", 1047, 418, 38, 35, 0], [2, "333", 1122, 367, 38, 35, 0], [2, "333", 1122, 367, 38, 35, 0], [2, "333", 1147, 376, 38, 35, 0], [2, "332", 1048, 428, 36, 38, 0], [2, "332", 1110, 382, 36, 38, 0], [2, "332", 1135, 392, 36, 38, 0], [2, "332", 1135, 392, 36, 38, 0], [2, "332", 1165, 402, 36, 38, 0], [2, "332", 1165, 402, 36, 38, 0], [2, "332", 1076, 406, 36, 38, 0], [2, "332", 1176, 444, 36, 38, 0], [2, "1422", 276, 319, 28, 30, 1], [2, "1422", 949, 663, 28, 30, 1], [2, "1421", 450, 736, 10, 26, 0], [2, "1421", 87, 719, 10, 26, 0], [2, "1421", 294, 647, 10, 26, 0], [2, "1421", 810, 572, 10, 26, 0], [2, "1421", 969, 657, 10, 26, 0], [2, "333", 316, 916, 38, 35, 0], [2, "333", 291, 924, 38, 35, 0], [2, "333", 274, 940, 38, 35, 0], [2, "333", 316, 940, 38, 35, 0], [2, "333", 316, 940, 38, 35, 0], [2, "333", 393, 937, 38, 35, 0], [2, "333", 340, 934, 38, 35, 0], [2, "333", 365, 932, 38, 35, 0], [2, "333", 345, 919, 38, 35, 0], [2, "333", 178, 941, 38, 35, 0], [2, "333", 200, 922, 38, 35, 0], [2, "332", 207, 930, 36, 38, 0], [2, "332", 239, 925, 36, 38, 0], [2, "332", 268, 902, 36, 38, 0], [2, "332", 260, 931, 36, 38, 0], [2, "332", 298, 910, 36, 38, 0], [2, "332", 342, 900, 36, 38, 0], [2, "332", 340, 930, 36, 38, 0], [2, "332", 381, 918, 36, 38, 0], [2, "332", 422, 933, 36, 38, 0], [2, "332", -6, 931, 36, 38, 0], [2, "332", 22, 936, 36, 38, 0], [2, "332", 1102, 899, 36, 38, 0], [2, "333", 1100, 924, 38, 35, 0], [2, "333", 1080, 938, 38, 35, 0], [2, "333", 1128, 933, 38, 35, 0], [2, "333", 1160, 927, 38, 35, 0], [2, "333", 1134, 909, 38, 35, 0], [2, "333", 1178, 939, 38, 35, 0], [2, "333", 1152, 941, 38, 35, 0], [2, "333", 1054, 937, 38, 35, 0], [2, "332", 1077, 917, 36, 38, 0], [2, "332", 1116, 914, 36, 38, 0], [2, "332", 1160, 900, 36, 38, 0], [2, "332", 1175, 907, 36, 38, 0], [2, "332", 1033, 940, 36, 38, 0], [2, "332", 1113, 934, 36, 38, 0], [2, "332", 949, 907, 36, 38, 0], [2, "332", 925, 917, 36, 38, 0], [2, "332", 925, 917, 36, 38, 0], [2, "332", 955, 928, 36, 38, 0], [2, "332", 925, 942, 36, 38, 0], [2, "332", 898, 940, 36, 38, 0], [2, "332", 981, 940, 36, 38, 0], [2, "332", 740, 932, 36, 38, 0], [2, "332", 771, 943, 36, 38, 0], [2, "332", 713, 936, 36, 38, 0], [2, "332", -12, 386, 36, 38, 0], [2, "333", 333, 619, 38, 35, 0], [2, "333", 351, 625, 38, 35, 0], [2, "333", 369, 599, 38, 35, 0], [2, "333", 379, 581, 38, 35, 0], [2, "333", 399, 595, 38, 35, 0], [2, "333", 399, 595, 38, 35, 0], [2, "333", 398, 575, 38, 35, 0], [2, "333", 423, 626, 38, 35, 0], [2, "333", 449, 631, 38, 35, 0], [2, "333", 438, 649, 38, 35, 0], [2, "333", 469, 650, 38, 35, 0], [2, "333", 431, 597, 38, 35, 0], [2, "333", 350, 595, 38, 35, 0], [2, "333", 308, 618, 38, 35, 0], [2, "333", 363, 563, 38, 35, 0], [2, "333", 385, 557, 38, 35, 0], [2, "333", 418, 563, 38, 35, 0], [2, "332", 366, 577, 36, 38, 0], [2, "332", 408, 569, 36, 38, 0], [2, "332", 414, 608, 36, 38, 0], [2, "332", 459, 610, 36, 38, 0], [2, "332", 485, 637, 36, 38, 0], [2, "332", 447, 578, 36, 38, 0], [2, "332", 350, 610, 36, 38, 0], [2, "332", 321, 595, 36, 38, 0], [2, "332", 408, 655, 36, 38, 0], [2, "332", 456, 666, 36, 38, 0], [2, "332", 483, 665, 36, 38, 0], [2, "1423", 180, 462, 20, 52, 0], [2, "1423", 214, 474, 20, 52, 0], [2, "1423", 320, 528, 20, 52, 0], [2, "1423", 354, 540, 20, 52, 0], [2, "1423", 531, 734, 20, 52, 0], [2, "1423", 565, 746, 20, 52, 0], [2, "1423", 668, 669, 20, 52, 0], [2, "1423", 702, 681, 20, 52, 0], [2, "1423", 766, 851, 20, 52, 0], [2, "1423", 800, 863, 20, 52, 0], [2, "1423", 877, 772, 20, 52, 0], [2, "1423", 911, 784, 20, 52, 0], [2, "1423", 791, 388, 20, 52, 0], [2, "1423", 825, 400, 20, 52, 0], [2, "1421", 421, 591, 10, 26, 0], [2, "1421", 462, 626, 10, 26, 0], [2, "1421", 340, 608, 10, 26, 0], [2, "1421", 297, 923, 10, 26, 0], [2, "1421", 841, 565, 10, 26, 0], [2, "1421", 886, 514, 10, 26, 0], [2, "1421", 886, 514, 10, 26, 0], [2, "1421", 939, 549, 10, 26, 0], [2, "1421", 920, 582, 10, 26, 0], [2, "1421", 920, 582, 10, 26, 0], [2, "1421", 1021, 654, 10, 26, 0], [2, "1421", 1054, 587, 10, 26, 0], [2, "1421", 1054, 587, 10, 26, 0], [2, "1421", 1114, 683, 10, 26, 0], [2, "1421", 1143, 656, 10, 26, 0], [2, "1421", 1087, 475, 10, 26, 0], [2, "1421", 1052, 443, 10, 26, 0], [2, "1421", 1052, 443, 10, 26, 0], [2, "1421", 1101, 419, 10, 26, 0], [2, "1421", 1140, 444, 10, 26, 0], [2, "1421", 1177, 477, 10, 26, 0], [2, "1421", 1177, 477, 10, 26, 0], [2, "1421", 1159, 387, 10, 26, 0], [2, "1421", 998, 562, 10, 26, 0], [2, "1421", 493, 35, 10, 26, 0], [2, "1421", 493, 35, 10, 26, 0], [2, "1421", 535, 4, 10, 26, 0], [2, "1421", 529, 63, 10, 26, 0], [2, "1421", 584, 30, 10, 26, 0], [2, "1421", 609, 75, 10, 26, 0], [2, "1421", 629, 25, 10, 26, 0]]}, {"type": 4, "obj": [[2, "3030", 238, -11, 38, 48, 0], [2, "3030", 353, 3, 38, 48, 2], [2, "3030", 751, 7, 38, 48, 0], [2, "3030", 751, 7, 38, 48, 0], [2, "3030", 200, 8, 38, 48, 0], [2, "3450", 201, -7, 30, 74, 2], [2, "3030", 129, 25, 38, 48, 2], [2, "3030", 163, 27, 38, 48, 0], [2, "3030", 744, 27, 38, 48, 2], [2, "3030", 766, 44, 38, 48, 2], [2, "3048", 808, 40, 30, 52, 0], [2, "3152", 79, 48, 32, 45, 0], [2, "3021", 355, 11, 88, 95, 0], [2, "3030", 760, 65, 38, 48, 0], [2, "331", 517, 10, 104, 108, 0], [2, "3021", 313, 32, 88, 95, 0], [2, "3030", 28, 83, 38, 48, 2], [2, "3030", 28, 83, 38, 48, 2], [2, "3030", 716, 88, 38, 48, 0], [4, 6, 827, 142, 0, 4001], [2, "3021", 272, 54, 88, 95, 0], [2, "3030", 23, 103, 38, 48, 0], [2, "3039", 728, 87, 76, 65, 0], [2, "330", 464, 110, 60, 49, 0], [2, "3030", 674, 111, 38, 48, 0], [2, "3450", 18, 91, 30, 74, 2], [2, "3021", 230, 75, 88, 95, 0], [2, "3030", 884, 126, 38, 48, 0], [2, "3030", 669, 129, 38, 48, 2], [2, "3030", 669, 129, 38, 48, 2], [2, "3450", 252, 116, 30, 74, 0], [2, "3030", 704, 146, 38, 48, 2], [2, "3030", 842, 148, 38, 48, 0], [2, "3030", 728, 157, 38, 48, 2], [4, 1, 477, 206, 0, 4005], [2, "3030", 800, 169, 38, 48, 0], [2, "3030", 763, 174, 38, 48, 2], [2, "3030", 991, 191, 38, 48, 0], [2, "3450", 365, 169, 30, 74, 0], [2, "3021", 55, 161, 88, 95, 0], [2, "3030", 953, 212, 38, 48, 0], [2, "3048", 1145, 208, 30, 52, 0], [2, "3450", 143, 190, 30, 74, 2], [2, "3450", 143, 190, 30, 74, 2], [2, "12", 81, 241, 26, 28, 0], [2, "395", 681, 204, 42, 72, 2], [2, "3021", 13, 182, 88, 95, 0], [2, "3030", 915, 232, 38, 48, 0], [2, "3170", 370, 200, 110, 85, 0], [4, 4, 663, 286, 0, 4001], [2, "3450", 465, 218, 30, 74, 0], [2, "3030", 921, 251, 38, 48, 2], [2, "3030", 1113, 252, 38, 48, 0], [2, "3030", 1152, 255, 38, 48, 2], [2, "3355", 17, 257, 54, 53, 0], [2, "3450", 248, 237, 30, 74, 2], [2, "3030", 960, 270, 38, 48, 2], [2, "3030", 1077, 270, 38, 48, 0], [2, "3030", 1039, 288, 38, 48, 0], [2, "3030", 1000, 291, 38, 48, 2], [2, "3439", 98, 300, 36, 48, 0], [2, "1421", 154, 323, 10, 26, 0], [2, "1420", 526, 226, 44, 124, 2], [2, "3452", 173, 330, 26, 25, 0], [4, 10, 66, 361, 0, 4019], [2, "1421", 202, 345, 10, 26, 0], [2, "3450", 347, 305, 30, 74, 2], [2, "395", 946, 324, 42, 72, 2], [2, "3032", 573, 331, 62, 68, 2], [2, "3439", 228, 361, 36, 48, 0], [2, "1420", 356, 304, 44, 124, 2], [2, "3032", 697, 392, 62, 68, 2], [2, "3455", 255, 413, 84, 62, 0], [2, "1420", 800, 371, 44, 124, 2], [2, "3421", 508, 420, 82, 98, 0], [2, "3421", 501, 422, 82, 98, 0], [2, "331", 1082, 419, 104, 108, 2], [2, "220_1", 1112, 500, 40, 29, 2], [4, 11, 716, 530, 0, 4021], [2, "3437", 460, 495, 50, 38, 0], [2, "220_1", 1142, 506, 40, 29, 0], [2, "220_1", 1112, 514, 40, 29, 0], [2, "3438", 579, 510, 50, 38, 0], [2, "3438", 485, 515, 50, 38, 0], [2, "1420", 943, 432, 44, 124, 2], [2, "3438", 521, 528, 50, 38, 0], [2, "1420", 188, 446, 44, 124, 2], [2, "13", 700, 594, 22, 24, 0], [2, "13", 740, 599, 22, 24, 0], [2, "1420", 329, 511, 44, 124, 2], [2, "679", 711, 604, 36, 32, 0], [2, "331", 861, 530, 104, 108, 0], [2, "3171", 607, 604, 48, 80, 0], [2, "1421", 815, 662, 10, 26, 0], [2, "1421", 815, 662, 10, 26, 0], [2, "3439", 773, 646, 36, 48, 0], [2, "331", 1001, 607, 104, 108, 0], [2, "220_1", 996, 690, 40, 29, 0], [2, "220_1", 1027, 690, 40, 29, 0], [2, "331", 361, 612, 104, 108, 0], [2, "1421", 886, 695, 10, 26, 0], [4, 9, 1157, 731, 0, 4010], [4, 12, 744, 744, 0, 4020], [2, "3439", 900, 708, 36, 48, 0], [2, "1417", 265, 720, 26, 41, 0], [2, "1421", 302, 736, 10, 26, 0], [2, "125", 1083, 701, 18, 70, 0], [2, "1420", 676, 651, 44, 124, 2], [2, "1420", 676, 651, 44, 124, 2], [2, "3083", 97, 674, 118, 106, 2], [2, "125", 1047, 724, 18, 70, 0], [2, "1421", 369, 772, 10, 26, 0], [4, 2, 190, 801, 0, 4010], [2, "1417", 385, 780, 26, 41, 0], [2, "1421", 101, 796, 10, 26, 0], [4, 3, 223, 825, 0, 4011], [2, "1420", 540, 716, 44, 124, 2], [2, "1421", 14, 838, 10, 26, 0], [2, "1420", 887, 755, 44, 124, 2], [2, "12", 552, 859, 26, 28, 0], [2, "1369", 44, 909, 28, 36, 0], [2, "1420", 774, 833, 44, 124, 2]]}, {"type": 3, "obj": [[2, "174_3", 1004, 709, 68, 33, 0], [2, "1260", 938, -77, 46, 142, 0], [2, "1260", 938, -77, 46, 142, 0], [2, "1260", 964, -63, 46, 142, 0], [2, "1260", 989, -51, 46, 142, 0], [2, "1260", 1015, -37, 46, 142, 0], [2, "1260", 1093, 4, 46, 142, 0], [2, "1260", 1119, 18, 46, 142, 0], [2, "1260", 1144, 31, 46, 142, 0], [2, "1260", 1170, 45, 46, 142, 0], [2, "1264", 966, 34, 26, 28, 2], [2, "1264", 1146, 127, 26, 28, 2], [2, "3030", 826, -31, 38, 48, 0], [2, "3030", 789, -12, 38, 48, 0], [2, "3030", 1189, 275, 38, 48, 2], [2, "3392", 620, 240, 34, 33, 0], [2, "220_1", 564, 179, 40, 29, 0], [2, "220_1", 579, 272, 40, 29, 0], [2, "220_1", 579, 204, 40, 29, 2], [2, "220_1", 563, 282, 40, 29, 0], [2, "220_1", 957, 348, 40, 29, 2], [2, "220_1", 1043, 365, 40, 29, 0], [2, "220_1", 647, 200, 40, 29, 2], [2, "220_1", 673, 212, 40, 29, 2], [2, "220_1", 726, 241, 40, 29, 2], [2, "220_1", 749, 251, 40, 29, 2], [2, "220_1", 927, 334, 40, 29, 2], [2, "220_1", 599, 166, 40, 29, 0], [2, "220_1", 986, 361, 40, 29, 2], [2, "220_1", 1022, 375, 40, 29, 0], [2, "220_1", 1127, 326, 40, 29, 0], [2, "220_1", 1127, 326, 40, 29, 0], [2, "220_1", 1082, 348, 40, 29, 0], [2, "220_1", 1162, 344, 40, 29, 2], [2, "220_1", 1146, 368, 40, 29, 2], [2, "220_1", 1086, 394, 40, 29, 0], [2, "220_1", 870, 430, 40, 29, 0], [2, "3428", 679, 276, 44, 31, 0], [2, "3429", 622, 304, 44, 31, 0], [2, "3430", 883, 381, 44, 31, 0], [2, "3428", 820, 411, 44, 31, 0], [2, "220_1", 787, 383, 40, 29, 0], [2, "220_1", 850, 358, 40, 29, 0], [2, "220_1", 888, 334, 40, 29, 0], [2, "220_1", 901, 325, 40, 29, 2], [2, "3432", 615, 208, 28, 21, 0], [2, "403", 903, 354, 40, 28, 0], [2, "3432", 934, 366, 28, 21, 0], [2, "403", 687, 14, 40, 28, 0], [2, "410", 767, 393, 14, 35, 0], [2, "410", 627, 314, 14, 35, 0], [2, "410", 672, 291, 14, 35, 0], [2, "410", 733, 265, 14, 35, 0], [2, "410", 833, 367, 14, 35, 0], [2, "410", 833, 367, 14, 35, 0], [2, "410", 887, 335, 14, 35, 0], [2, "220_1", 608, 95, 40, 29, 2], [2, "1368", 590, 53, 50, 42, 0], [2, "1368", 650, 102, 50, 42, 0], [2, "1368", 575, 171, 50, 42, 0], [2, "1368", 792, 414, 50, 42, 0], [2, "1368", 1021, 387, 50, 42, 2], [2, "1457", 958, 261, 22, 30, 0], [2, "1457", 1028, 287, 22, 30, 0], [2, "3127", 1048, 275, 30, 30, 0], [2, "220_1", 1180, 245, 40, 29, 2], [2, "220_1", 771, 24, 40, 29, 0], [2, "220_1", 803, 12, 40, 29, 0], [2, "220_1", 852, -11, 40, 29, 0], [2, "220_1", 702, 128, 40, 29, 0], [2, "220_1", 774, 89, 40, 29, 0], [2, "220_1", 780, 170, 40, 29, 0], [2, "3030", 282, -30, 38, 48, 2], [2, "3030", 317, -14, 38, 48, 2], [2, "3030", -14, 121, 38, 48, 0], [2, "410", 623, 14, 14, 35, 0], [2, "3432", 686, 93, 28, 21, 0], [2, "3392", 768, 155, 34, 33, 0], [2, "3392", 828, 2, 34, 33, 2], [2, "3392", 1085, 233, 34, 33, 0], [2, "220_1", 984, 228, 40, 29, 0], [2, "220_1", 1016, 209, 40, 29, 0], [2, "21", 448, 113, 28, 24, 0], [2, "21", 509, 146, 28, 24, 0], [2, "174_3", 510, 85, 68, 33, 0], [2, "174_3", 540, 104, 68, 33, 0], [2, "3392", 497, 65, 34, 33, 0], [2, "220_1", 563, 80, 40, 29, 2], [2, "220_1", 366, -8, 40, 29, 2], [2, "220_1", 384, 7, 40, 29, 2], [2, "220_1", 403, -7, 40, 29, 2], [2, "220_1", 467, 3, 40, 29, 0], [2, "220_1", 354, 112, 40, 29, 0], [2, "220_1", 417, 212, 40, 29, 2], [2, "220_1", 373, 194, 40, 29, 2], [2, "220_1", 321, 167, 40, 29, 2], [2, "1457", 409, 199, 22, 30, 0], [2, "1457", 521, 264, 22, 30, 0], [2, "1456", 412, 1, 24, 32, 0], [2, "3127", 534, 184, 30, 30, 0], [2, "220_1", 632, 111, 40, 29, 2], [2, "344", 83, 467, 30, 74, 2], [2, "345", 59, 481, 24, 73, 0], [2, "344", 29, 494, 30, 74, 2], [2, "344", -1, 509, 30, 74, 2], [2, "818", 84, 524, 30, 37, 0], [2, "818", 56, 538, 30, 37, 0], [2, "818", 26, 553, 30, 37, 0], [2, "818", -3, 568, 30, 37, 0], [2, "3408", 67, 550, 42, 26, 0], [2, "3408", 58, 538, 42, 26, 0], [2, "756", 17, 511, 28, 40, 0], [2, "3376", 39, 476, 58, 82, 0], [2, "818", 129, 362, 30, 37, 2], [2, "818", 159, 377, 30, 37, 2], [2, "818", 189, 392, 30, 37, 2], [2, "818", 218, 406, 30, 37, 2], [2, "818", 248, 406, 30, 37, 0], [2, "818", 278, 391, 30, 37, 0], [2, "344", 128, 304, 30, 74, 0], [2, "344", 157, 319, 30, 74, 0], [2, "344", 187, 334, 30, 74, 0], [2, "344", 217, 349, 30, 74, 0], [2, "344", 246, 349, 30, 74, 2], [2, "344", 276, 334, 30, 74, 2], [2, "345", 173, 346, 24, 73, 2], [2, "756", 135, 318, 28, 40, 2], [2, "756", 215, 359, 28, 40, 2], [2, "3376", 156, 345, 58, 82, 2], [2, "756", 302, 332, 28, 40, 0], [2, "3066", 242, 385, 14, 60, 0], [2, "3066", 242, 360, 14, 60, 0], [2, "3066", 119, 328, 14, 60, 0], [2, "3066", 119, 303, 14, 60, 0], [2, "3066", 316, 349, 14, 60, 0], [2, "3066", 316, 324, 14, 60, 0], [2, "818", 294, 382, 30, 37, 0], [2, "344", 292, 325, 30, 74, 2], [2, "756", 274, 345, 28, 40, 0], [2, "8", 315, 413, 38, 29, 2], [2, "8", 241, 458, 38, 29, 2], [2, "8", 312, 460, 38, 29, 0], [2, "14", 106, 375, 32, 30, 0], [2, "12", 109, 358, 26, 28, 0], [2, "13", 129, 384, 22, 24, 0], [2, "15", 216, 423, 22, 27, 0], [2, "818", 798, 706, 30, 37, 2], [2, "818", 828, 721, 30, 37, 2], [2, "818", 858, 736, 30, 37, 2], [2, "818", 887, 750, 30, 37, 2], [2, "818", 917, 750, 30, 37, 0], [2, "818", 947, 735, 30, 37, 0], [2, "344", 797, 648, 30, 74, 0], [2, "344", 826, 663, 30, 74, 0], [2, "344", 856, 678, 30, 74, 0], [2, "344", 886, 693, 30, 74, 0], [2, "344", 915, 693, 30, 74, 2], [2, "344", 945, 678, 30, 74, 2], [2, "345", 842, 690, 24, 73, 2], [2, "3376", 825, 688, 58, 82, 2], [2, "3066", 911, 729, 14, 60, 0], [2, "3066", 911, 704, 14, 60, 0], [2, "3066", 788, 672, 14, 60, 0], [2, "3066", 788, 647, 14, 60, 0], [2, "818", 958, 730, 30, 37, 0], [2, "344", 956, 673, 30, 74, 2], [2, "3066", 983, 673, 14, 60, 0], [2, "3066", 983, 698, 14, 60, 0], [2, "3373", 75, 244, 52, 36, 0], [2, "3066", 108, 491, 14, 60, 0], [2, "3066", 108, 466, 14, 60, 0], [2, "3066", -2, 548, 14, 60, 0], [2, "3066", -2, 523, 14, 60, 0], [2, "744", 943, 695, 30, 42, 2], [2, "220_1", 996, 663, 40, 29, 2], [2, "174_3", 964, 647, 68, 33, 0], [2, "818", 282, 779, 30, 37, 2], [2, "818", 312, 794, 30, 37, 2], [2, "818", 342, 809, 30, 37, 2], [2, "818", 371, 823, 30, 37, 2], [2, "818", 401, 823, 30, 37, 0], [2, "818", 431, 808, 30, 37, 0], [2, "344", 281, 721, 30, 74, 0], [2, "344", 310, 736, 30, 74, 0], [2, "344", 340, 751, 30, 74, 0], [2, "344", 370, 766, 30, 74, 0], [2, "344", 399, 766, 30, 74, 2], [2, "344", 429, 751, 30, 74, 2], [2, "345", 326, 763, 24, 73, 2], [2, "3376", 309, 761, 58, 82, 2], [2, "3066", 395, 802, 14, 60, 0], [2, "3066", 395, 777, 14, 60, 0], [2, "3066", 272, 745, 14, 60, 0], [2, "3066", 272, 720, 14, 60, 0], [2, "818", 442, 803, 30, 37, 0], [2, "344", 440, 746, 30, 74, 2], [2, "3066", 467, 746, 14, 60, 0], [2, "3066", 467, 771, 14, 60, 0], [2, "744", 427, 768, 30, 42, 2], [2, "344", 85, 787, 30, 74, 2], [2, "345", 61, 801, 24, 73, 0], [2, "344", 31, 814, 30, 74, 2], [2, "344", 1, 829, 30, 74, 2], [2, "818", 86, 844, 30, 37, 0], [2, "818", 58, 858, 30, 37, 0], [2, "818", 28, 873, 30, 37, 0], [2, "818", -1, 888, 30, 37, 0], [2, "3408", 69, 870, 42, 26, 0], [2, "3408", 60, 858, 42, 26, 0], [2, "756", 19, 831, 28, 40, 0], [2, "3376", 41, 796, 58, 82, 0], [2, "3066", 110, 811, 14, 60, 0], [2, "3066", 110, 786, 14, 60, 0], [2, "3066", 0, 868, 14, 60, 0], [2, "3066", 0, 843, 14, 60, 0], [2, "3373", 668, 596, 52, 36, 0], [2, "3373", 736, 638, 52, 36, 0], [2, "3373", 671, 638, 52, 36, 2], [2, "3373", 742, 595, 52, 36, 2], [2, "3373", 486, 862, 52, 36, 0], [2, "3373", 554, 904, 52, 36, 0], [2, "3373", 489, 904, 52, 36, 2], [2, "3373", 560, 861, 52, 36, 2], [2, "3455", 688, 596, 84, 62, 0], [2, "3455", 503, 859, 84, 62, 0], [2, "12", 713, 651, 26, 28, 0], [2, "12", 229, 434, 26, 28, 0], [2, "12", 32, 566, 26, 28, 0], [2, "15", 13, 577, 22, 27, 0], [2, "13", 274, 791, 22, 24, 0], [2, "361", 494, 482, 104, 58, 2], [2, "3052", 419, 811, 68, 57, 0], [2, "220_1", 1148, 475, 40, 29, 0], [2, "220_1", 1116, 486, 40, 29, 0], [2, "220_1", 1148, 449, 40, 29, 0], [2, "220_1", 1120, 465, 40, 29, 0], [2, "220_1", 1097, 477, 40, 29, 0], [2, "220_1", 1151, 465, 40, 29, 0], [2, "220_1", 1171, 498, 40, 29, 2], [2, "220_1", 1164, 523, 40, 29, 2], [2, "220_1", 1148, 533, 40, 29, 2], [2, "220_1", 912, 595, 40, 29, 2], [2, "220_1", 912, 595, 40, 29, 2], [2, "220_1", 935, 610, 40, 29, 2], [2, "220_1", 868, 573, 40, 29, 2], [2, "220_1", 1016, 708, 40, 29, 2], [2, "220_1", 1064, 678, 40, 29, 2], [2, "220_1", 1023, 645, 40, 29, 2], [2, "220_1", 923, 554, 40, 29, 2], [2, "220_1", 1035, 763, 40, 29, 2], [2, "1422", 364, 806, 28, 30, 0], [2, "3185", 134, 841, 66, 36, 2], [2, "763", 18, 898, 32, 31, 0], [2, "1108", 387, 840, 26, 31, 0], [2, "1108", 410, 843, 26, 31, 0], [2, "174_3", 469, 718, 68, 33, 0], [2, "174_3", 293, 882, 68, 33, 0], [2, "174_3", 640, 928, 68, 33, 0], [2, "174_3", 1058, 843, 68, 33, 0], [2, "174_3", 1106, 787, 68, 33, 0], [2, "1452", 626, 743, 30, 22, 0], [2, "1452", 632, 829, 30, 22, 0], [2, "1452", 551, 623, 30, 22, 0], [2, "1452", 421, 489, 30, 22, 0], [2, "1452", 427, 340, 30, 22, 0], [2, "1452", 246, 489, 30, 22, 0], [2, "1452", 166, 633, 30, 22, 0], [2, "1452", 257, 605, 30, 22, 0], [2, "1452", 227, 745, 30, 22, 0], [2, "1452", 494, 802, 30, 22, 0], [2, "1452", 648, 856, 30, 22, 0], [2, "1452", 1019, 890, 30, 22, 0], [2, "1452", 1102, 803, 30, 22, 0], [2, "1452", 1158, 633, 30, 22, 0], [2, "1452", 1158, 633, 30, 22, 0], [2, "1452", 1078, 534, 30, 22, 0], [2, "1452", 888, 470, 30, 22, 0], [2, "1452", 745, 463, 30, 22, 0], [2, "1452", 745, 463, 30, 22, 0], [2, "1452", 745, 463, 30, 22, 0], [2, "1452", 627, 423, 30, 22, 0], [2, "1452", 486, 370, 30, 22, 0], [2, "1452", 270, 215, 30, 22, 0], [2, "363", -4, -30, 54, 75, 0], [2, "363", 46, -40, 54, 75, 0], [2, "363", 15, -9, 54, 75, 0], [2, "363", 92, -31, 54, 75, 0], [2, "363", 92, -31, 54, 75, 0], [2, "363", 136, -38, 54, 75, 0], [2, "363", 50, -5, 54, 75, 0], [2, "3027", 36, -7, 50, 83, 0], [2, "3428", 657, 39, 44, 31, 0], [2, "14", 956, 748, 32, 30, 0], [2, "12", 959, 731, 26, 28, 0], [2, "8", 929, 763, 38, 29, 0]]}, {"type": 2, "data": [23, 23, 23, 23, 23, 23, 23, 23, 17, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 23, 23, -1, -1, -1, 23, 23, 23, 23, 23, 23, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 23, -1, -1, -1, -1, 23, 23, 23, 23, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 2, 2, 3, -1, -1, -1, -1, -1, -1, 4, 2, 3, -1, -1, -1, 80, 79, 78, 23, 23, -1, -1, 23, 23, 23, -1, -1, -1, 23, 23, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 75, -1, 16, 5, 5, 5, 6, 3, 2, 3, -1, -1, -1, 4, 5, 6, 7, -1, -1, 70, 69, 68, 23, 23, 23, 23, 23, 23, 23, -1, -1, -1, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 63, 64, 65, -1, 16, 23, 5, 5, 17, 18, 5, 6, 2, 3, -1, -1, 17, 18, 19, -1, -1, -1, -1, 70, 69, 68, 23, 23, 23, 23, 23, -1, -1, -1, 14, 15, -1, -1, -1, -1, -1, -1, -1, 80, 79, 78, -1, 23, 63, 64, 65, -1, -1, -1, 21, 20, 20, 20, 14, 15, -1, -1, 5, 6, 17, 18, 14, 15, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 23, 23, 23, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 15, -1, -1, -1, -1, -1, 73, 74, 75, -1, -1, -1, 70, 69, 68, 23, 23, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 17, 17, 17, 73, 74, 75, -1, -1, -1, 70, 69, 68, 23, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 17, 17, 17, 17, 17, 63, 64, 65, -1, -1, -1, -1, -1, 70, 69, 68, -1, -1, -1, -1, 73, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 17, 17, 17, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 23, 23, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, 16, 23, 5, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 17, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 8, 8, 80, 16, 78, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 20, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 11, 11, 70, 21, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 11, 11, 11, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 11, 11, 11, 11, 11, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 23, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 11, 11, 11, 11, 23, 11, 2, 3, -1, 80, 79, 78, -1, -1, -1, -1, -1, -1, -1, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 11, 63, 64, 70, 69, 20, 5, 6, 7, 70, 69, 68, 23, -1, -1, -1, -1, -1, -1, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, 65, -1, -1, -1, -1, -1, -1, 10, -1, -1, 70, 69, 68, 23, -1, -1, -1, 23, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 23, 23, 23, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 63, 64, 65, -1, -1, -1, -1, -1, 9, 8, -1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, -1, -1, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 65, -1, -1, -1, -1, -1, -1, 1, 12, 11, -1, 5, 6, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 23, -1, 23, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 23, -1, -1, -1, -1, -1, -1, 73, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 17, 18, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 20, 20, 20, -1, -1, 23, 23, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 23, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 8, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 8, 8, -1, 8, 8, 3, -1, -1, 4, 5, 11, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 24, 23, 23, 17, 18, 19, -1, -1, -1, -1, -1, -1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 8, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 20, 23, 14, 15, -1, -1, -1, -1, -1, -1, -1, 5, 6, 8, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 11, 5, 6, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, -1, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 7, -1, -1, -1, -1, -1, -1, -1, -1, 23, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 78, -1, -1, -1, -1, -1, -1, -1, 23, 17, 18, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 78, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 23, -1, -1, -1, -1, -1, 73, 23, 23, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 78, 63, 64, 65, -1, 80, 79, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, -1, -1, -1, -1, 23, 23, 23, 23, 17, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, 65, -1, 80, 79, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 23, 63, 64, 65, 69, 64, 14, 15, -1, -1, -1, -1, -1, -1, -1, 63, 64, 65, -1, -1, -1, 70, 69, 68, 23, -1, -1, -1, -1, -1, 73, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, -1, -1, -1, -1, -1, -1, 73, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 75, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 23, 23, 23, 23, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, 65, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 63, 64, 65, 8, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 63, 64, 65, -1, -1, -1, -1, 9, 8, 2, 3, -1, -1, -1, -1, 70, 65, -1, 16, 23, 23, 23, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, 65, -1, -1, -1, -1, 9, 8, 12, 11, 5, 6, -1, -1, 2, 3, -1, -1, -1, 13, 14, 20, 20, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 8, -1, 8, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 12, 11, -1, -1, -1, -1, -1, -1, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 12, 11, -1, -1, 5, 6, 2, 3, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "498", 508, 289, 56, 32, 2], [2, "3177", 42, 57, 60, 29, 2], [2, "3034", 803, 275, 50, 26, 0], [2, "3034", 851, 299, 50, 26, 0], [2, "3034", 829, 287, 50, 26, 0], [2, "3034", 877, 311, 50, 26, 0], [2, "3034", 779, 289, 50, 26, 0], [2, "3034", 805, 301, 50, 26, 0], [2, "3034", 827, 313, 50, 26, 0], [2, "3034", 853, 325, 50, 26, 0], [2, "3034", 754, 302, 50, 26, 0], [2, "3034", 802, 326, 50, 26, 0], [2, "3034", 780, 314, 50, 26, 0], [2, "3034", 828, 338, 50, 26, 0], [2, "3034", 728, 315, 50, 26, 0], [2, "3034", 776, 339, 50, 26, 0], [2, "3034", 754, 327, 50, 26, 0], [2, "3034", 802, 351, 50, 26, 0], [2, "3034", 674, 340, 50, 26, 0], [2, "3034", 722, 364, 50, 26, 0], [2, "3034", 700, 352, 50, 26, 0], [2, "3034", 748, 376, 50, 26, 0], [2, "3034", 623, 367, 50, 26, 0], [2, "3034", 671, 391, 50, 26, 0], [2, "3034", 649, 379, 50, 26, 0], [2, "3034", 697, 403, 50, 26, 0], [2, "3034", 650, 354, 50, 26, 0], [2, "3034", 698, 378, 50, 26, 0], [2, "3034", 676, 366, 50, 26, 0], [2, "3034", 724, 390, 50, 26, 0], [2, "1102", 603, 138, 114, 63, 2], [2, "1112", 743, 54, 46, 83, 0], [2, "531", 1035, 62, 74, 59, 2], [2, "1500", 766, 195, 50, 26, 0], [2, "1500", 740, 182, 50, 26, 0], [2, "1500", 766, 169, 50, 26, 0], [2, "1500", 792, 182, 50, 26, 0], [2, "1500", 740, 156, 50, 26, 0], [2, "1500", 714, 143, 50, 26, 0], [2, "1500", 688, 156, 50, 26, 0], [2, "1500", 714, 169, 50, 26, 0], [2, "1500", 667, 145, 50, 26, 0], [2, "1500", 818, 169, 50, 26, 0], [2, "1500", 792, 156, 50, 26, 0], [2, "1500", 766, 143, 50, 26, 0], [2, "1500", 740, 130, 50, 26, 0], [2, "1500", 719, 119, 50, 26, 0], [2, "1500", 843, 156, 50, 26, 0], [2, "1500", 817, 143, 50, 26, 0], [2, "1500", 791, 130, 50, 26, 0], [2, "1500", 765, 117, 50, 26, 0], [2, "1500", 744, 106, 50, 26, 0], [2, "1500", 869, 143, 50, 26, 0], [2, "1500", 843, 130, 50, 26, 0], [2, "1500", 817, 117, 50, 26, 0], [2, "1500", 791, 104, 50, 26, 0], [2, "1500", 770, 93, 50, 26, 0], [2, "1500", 1112, 262, 50, 26, 0], [2, "1500", 1144, 271, 50, 26, 0], [2, "1500", 1165, 282, 50, 26, 0], [2, "1500", 1191, 295, 50, 26, 0], [2, "326", 915, 439, 18, 14, 0], [2, "1500", 741, 41, 50, 26, 0], [2, "1500", 767, 30, 50, 26, 0], [2, "1500", 790, 18, 50, 26, 0], [2, "1500", 816, 5, 50, 26, 0], [2, "1500", 841, -8, 50, 26, 0], [2, "1500", 837, 18, 50, 26, 0], [2, "1500", 812, 31, 50, 26, 0], [2, "1500", 786, 44, 50, 26, 0], [2, "1500", 765, 53, 50, 26, 0], [2, "1500", 864, 6, 50, 26, 0], [2, "1500", 864, -19, 50, 26, 0], [2, "1500", 890, -7, 50, 26, 0], [2, "1500", 916, -20, 50, 26, 0], [2, "1500", 1168, 260, 50, 26, 0], [2, "220_1", 748, 98, 40, 29, 0], [2, "1456", 752, 173, 24, 32, 0], [2, "1456", 702, 146, 24, 32, 0], [2, "1456", 726, 158, 24, 32, 0], [2, "1456", 736, 162, 24, 32, 0], [2, "1457", 742, 169, 22, 30, 0], [2, "1457", 753, 85, 22, 30, 0], [2, "326", 874, 154, 18, 14, 0], [2, "1456", 833, 159, 24, 32, 0], [2, "1457", 848, 156, 22, 30, 0], [2, "11", 1059, 729, 32, 29, 0], [2, "14", 1009, 752, 32, 30, 0], [2, "15", 1014, 736, 22, 27, 0], [2, "1256", 1061, 32, 52, 63, 2], [2, "1256", 1060, -5, 52, 63, 2], [2, "1256", 1060, -23, 52, 63, 2], [2, "531", 931, 45, 74, 59, 0], [2, "531", 977, 69, 74, 59, 0], [2, "531", 1087, 127, 74, 59, 0], [2, "531", 1133, 151, 74, 59, 0], [2, "1102", 748, 427, 114, 63, 0], [2, "1102", 539, 311, 114, 63, 0], [2, "1112", 829, 178, 46, 83, 2], [2, "1112", 791, 198, 46, 83, 2], [2, "1112", 668, 157, 46, 83, 0], [2, "1112", 714, 179, 46, 83, 0], [2, "1112", 747, 196, 46, 83, 0], [2, "1102", 645, 211, 114, 63, 0], [2, "1102", 691, 232, 114, 63, 0], [2, "1112", 1101, 283, 46, 83, 2], [2, "1112", 1147, 283, 46, 83, 0], [2, "1112", 1193, 307, 46, 83, 0], [2, "1500", 1011, 314, 50, 26, 0], [2, "1500", 985, 301, 50, 26, 0], [2, "1500", 959, 288, 50, 26, 0], [2, "1500", 933, 275, 50, 26, 0], [2, "1500", 912, 264, 50, 26, 0], [2, "1500", 1036, 301, 50, 26, 0], [2, "1500", 1010, 288, 50, 26, 0], [2, "1500", 984, 275, 50, 26, 0], [2, "1500", 958, 262, 50, 26, 0], [2, "1500", 1062, 288, 50, 26, 0], [2, "1500", 1036, 275, 50, 26, 0], [2, "1500", 1010, 262, 50, 26, 0], [2, "1500", 984, 249, 50, 26, 0], [2, "1500", 963, 238, 50, 26, 0], [2, "1500", 1087, 275, 50, 26, 0], [2, "1500", 1061, 262, 50, 26, 0], [2, "1500", 1035, 249, 50, 26, 0], [2, "1500", 1009, 236, 50, 26, 0], [2, "1500", 988, 225, 50, 26, 0], [2, "1500", 1086, 249, 50, 26, 0], [2, "1500", 1060, 236, 50, 26, 0], [2, "1500", 1034, 223, 50, 26, 0], [2, "1457", 953, 405, 22, 30, 0], [2, "220_1", 997, 225, 40, 29, 0], [2, "1457", 997, 293, 22, 30, 0], [2, "1456", 948, 269, 24, 32, 0], [2, "1456", 980, 281, 24, 32, 0], [2, "1456", 965, 276, 24, 32, 0], [2, "1112", 874, 155, 46, 83, 2], [2, "1500", 937, 251, 50, 26, 0], [2, "1112", 957, 300, 46, 83, 0], [2, "1112", 990, 316, 46, 83, 0], [2, "1112", 1057, 305, 46, 83, 2], [2, "1112", 1034, 316, 46, 83, 2], [2, "43_1", 834, 211, 82, 58, 0], [2, "43_1", 885, 238, 82, 58, 0], [2, "43_1", 861, 182, 82, 58, 0], [2, "43_1", 913, 207, 82, 58, 0], [2, "43_1", 893, 157, 82, 58, 0], [2, "43_1", 947, 184, 82, 58, 0], [2, "1500", 911, 264, 50, 26, 0], [2, "1500", 937, 277, 50, 26, 0], [2, "1500", 937, 252, 50, 26, 0], [2, "1500", 963, 265, 50, 26, 0], [2, "1500", 962, 239, 50, 26, 0], [2, "1500", 988, 252, 50, 26, 0], [2, "1500", 986, 226, 50, 26, 0], [2, "1500", 1012, 239, 50, 26, 0], [2, "1500", 995, 220, 50, 26, 0], [2, "1500", 1013, 212, 50, 26, 0], [2, "1102", 897, 327, 114, 63, 0], [2, "1102", 1051, 334, 114, 63, 2], [2, "1102", 1022, 351, 114, 63, 2], [2, "1102", 1141, 337, 114, 63, 0], [2, "1102", 826, 423, 114, 63, 2], [2, "1102", 875, 398, 114, 63, 2], [2, "1102", 1148, 375, 114, 63, 0], [2, "1102", 1046, 384, 114, 63, 2], [2, "1102", 970, 395, 114, 63, 0], [2, "1102", 944, 351, 114, 63, 0], [2, "1500", 697, 132, 50, 26, 0], [2, "1102", 569, 257, 114, 63, 2], [2, "1102", 543, 271, 114, 63, 2], [2, "1102", 554, 161, 114, 63, 2], [2, "1102", 570, 211, 114, 63, 0], [2, "938", 915, 2, 38, 22, 2], [2, "938", 887, 16, 38, 22, 2], [2, "938", 858, 31, 38, 22, 2], [2, "938", 830, 45, 38, 22, 2], [2, "938", 823, 49, 38, 22, 2], [2, "938", 795, 63, 38, 22, 2], [2, "938", 795, 86, 38, 22, 0], [2, "938", 823, 101, 38, 22, 0], [2, "938", 852, 116, 38, 22, 0], [2, "938", 882, 130, 38, 22, 0], [2, "938", 911, 145, 38, 22, 0], [2, "938", 938, 159, 38, 22, 0], [2, "938", 967, 174, 38, 22, 0], [2, "938", 994, 188, 38, 22, 0], [2, "938", 1051, 215, 38, 22, 0], [2, "938", 1024, 201, 38, 22, 0], [2, "938", 1080, 230, 38, 22, 0], [2, "938", 1107, 244, 38, 22, 0], [2, "938", 1134, 257, 38, 22, 0], [2, "938", 1161, 271, 38, 22, 0], [2, "938", 1151, 254, 38, 22, 2], [2, "938", 1179, 241, 38, 22, 2], [2, "43_1", 1062, 83, 82, 58, 0], [2, "43_1", 1089, 56, 82, 58, 0], [2, "1102", 687, 275, 114, 63, 2], [2, "1102", 638, 300, 114, 63, 2], [2, "1102", 815, 331, 114, 63, 2], [2, "1102", 736, 366, 114, 63, 2], [2, "3034", 701, 327, 50, 26, 0], [2, "3034", 749, 351, 50, 26, 0], [2, "3034", 727, 339, 50, 26, 0], [2, "3034", 775, 363, 50, 26, 0], [2, "43_1", 804, 240, 82, 58, 0], [2, "43_1", 856, 265, 82, 58, 0], [2, "1112", 911, 277, 46, 83, 0], [2, "435", 600, -19, 50, 77, 2], [2, "433", 651, -39, 62, 61, 2], [2, "438", 604, 57, 26, 43, 2], [2, "439", 612, 87, 64, 42, 2], [2, "363", -12, 20, 54, 75, 0], [2, "3177", 60, 67, 60, 29, 2], [2, "3177", 78, 76, 60, 29, 2], [2, "3176", 97, 86, 66, 34, 2], [2, "43_1", 336, 47, 82, 58, 2], [2, "43_1", 284, 73, 82, 58, 2], [2, "43_1", 232, 99, 82, 58, 2], [2, "43_1", 180, 125, 82, 58, 2], [2, "43_1", 128, 151, 82, 58, 2], [2, "43_1", 76, 177, 82, 58, 2], [2, "43_1", 24, 203, 82, 58, 2], [2, "43_1", -28, 229, 82, 58, 2], [2, "498", 237, 157, 56, 32, 2], [2, "498", 276, 176, 56, 32, 2], [2, "498", 317, 196, 56, 32, 2], [2, "498", 356, 214, 56, 32, 2], [2, "498", 396, 233, 56, 32, 2], [2, "498", 474, 272, 56, 32, 2], [2, "498", 436, 252, 56, 32, 2], [2, "498", 91, 230, 56, 32, 2], [2, "498", 130, 249, 56, 32, 2], [2, "498", 169, 268, 56, 32, 2], [2, "498", 208, 287, 56, 32, 2], [2, "498", 247, 307, 56, 32, 2], [2, "498", 286, 326, 56, 32, 2], [2, "513", 360, 357, 20, 40, 2], [2, "513", 360, 386, 20, 40, 2], [2, "513", 360, 413, 20, 40, 2], [2, "513", 360, 438, 20, 40, 2], [2, "3151", 904, 96, 96, 49, 0], [2, "3151", 1061, 176, 96, 49, 0], [2, "3151", 11, 153, 96, 49, 0], [2, "3151", 194, 59, 96, 49, 0], [2, "468", 323, 469, 58, 31, 0], [2, "468", 282, 490, 58, 31, 0], [2, "468", 241, 510, 58, 31, 0], [2, "468", 200, 531, 58, 31, 0], [2, "468", 159, 552, 58, 31, 0], [2, "468", 118, 573, 58, 31, 0], [2, "468", 78, 593, 58, 31, 0], [2, "468", 37, 614, 58, 31, 0], [2, "513", 25, 624, 40, 21, 7], [2, "513", -5, 624, 40, 21, 7], [2, "3025", 68, 555, 92, 53, 2], [2, "498", 326, 345, 56, 32, 2], [2, "3025", 108, 410, 92, 53, 0], [2, "468", 297, 626, 58, 31, 0], [2, "468", 256, 647, 58, 31, 0], [2, "468", 215, 667, 58, 31, 0], [2, "468", 174, 688, 58, 31, 0], [2, "468", 133, 709, 58, 31, 0], [2, "468", 92, 729, 58, 31, 0], [2, "468", 51, 750, 58, 31, 0], [2, "468", 10, 770, 58, 31, 0], [2, "468", -31, 791, 58, 31, 0], [2, "468", -72, 812, 58, 31, 0], [2, "468", 385, 628, 58, 31, 2], [2, "513", 334, 626, 40, 21, 7], [2, "513", 364, 626, 40, 21, 7], [2, "468", 426, 649, 58, 31, 2], [2, "513", 465, 667, 20, 40, 0], [2, "513", 465, 696, 20, 40, 0], [2, "468", 464, 727, 58, 31, 2], [2, "468", 505, 747, 58, 31, 2], [2, "513", 543, 794, 20, 40, 0], [2, "513", 543, 765, 20, 40, 0], [2, "468", 543, 826, 58, 31, 2], [2, "468", 584, 846, 58, 31, 2], [2, "468", 625, 866, 58, 31, 2], [2, "468", 666, 886, 58, 31, 2], [2, "468", 706, 906, 58, 31, 2], [2, "468", 747, 926, 58, 31, 2], [2, "468", 786, 945, 58, 31, 2], [2, "468", 827, 965, 58, 31, 2], [2, "498", 676, 757, 56, 32, 2], [2, "498", 715, 777, 56, 32, 2], [2, "498", 755, 796, 56, 32, 2], [2, "498", 794, 816, 56, 32, 2], [2, "498", 834, 836, 56, 32, 2], [2, "498", 873, 856, 56, 32, 2], [2, "513", 911, 870, 40, 21, 4], [2, "513", 936, 870, 40, 21, 4], [2, "513", 962, 870, 40, 21, 4], [2, "498", 1069, 817, 56, 32, 0], [2, "498", 1027, 837, 56, 32, 0], [2, "498", 986, 857, 56, 32, 0], [2, "513", 1108, 786, 20, 40, 0], [2, "513", 1108, 759, 20, 40, 0], [2, "513", 1108, 738, 20, 40, 0], [2, "513", 1108, 711, 20, 40, 0], [2, "513", 1108, 691, 20, 40, 0], [2, "513", 1108, 664, 20, 40, 0], [2, "513", 671, 732, 20, 40, 2], [2, "498", 596, 675, 56, 32, 2], [2, "498", 636, 694, 56, 32, 2], [2, "513", 597, 648, 20, 40, 2], [2, "498", 636, 694, 56, 32, 2], [2, "513", 671, 706, 20, 40, 2], [2, "513", 597, 624, 20, 40, 2], [2, "468", 639, 583, 58, 31, 0], [2, "468", 598, 603, 58, 31, 0], [2, "468", 722, 542, 58, 31, 0], [2, "468", 681, 562, 58, 31, 0], [2, "513", 794, 538, 40, 21, 7], [2, "513", 764, 538, 40, 21, 7], [2, "513", 904, 542, 40, 21, 7], [2, "513", 874, 542, 40, 21, 7], [2, "513", 955, 543, 40, 21, 7], [2, "513", 925, 543, 40, 21, 7], [2, "498", 1080, 529, 56, 32, 2], [2, "498", 1120, 548, 56, 32, 2], [2, "498", 1159, 568, 56, 32, 2], [2, "513", 1082, 503, 20, 40, 2], [2, "513", 1082, 479, 20, 40, 2], [2, "468", 1083, 458, 58, 31, 0], [2, "468", 1124, 438, 58, 31, 0], [2, "468", 1175, 441, 58, 31, 2], [2, "468", 1216, 461, 58, 31, 2], [2, "3025", 775, 755, 92, 53, 0], [2, "3025", 257, 821, 92, 53, 0], [2, "3025", 69, 880, 92, 53, 2], [2, "791", 785, 741, 104, 53, 0], [2, "791", 268, 814, 104, 53, 0], [2, "791", 53, 868, 104, 53, 2], [2, "791", 50, 548, 104, 53, 2], [2, "791", 116, 399, 104, 53, 0]]}, {"type": 2, "data": [26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 49, 49, 49, 49, 49, 49, 49, 49, 43, 43, 43, 43, 43, 0, 0, 0, 0, 0, 0, 0, 36, 25, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 49, 49, 49, 49, 49, 55, 43, 43, 43, 43, 0, 0, 0, 0, 0, 0, 38, 37, 38, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 29, 49, 49, 55, 55, 55, 55, 55, 55, 55, 43, 0, 0, 0, 0, 0, 0, 25, 28, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 26, 27, 55, 55, 55, 55, 55, 55, 55, 55, 55, 43, 0, 0, 0, 0, 0, 0, 0, 31, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 0, 0, 0, 0, 0, 37, 38, 34, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 32, 33, 34, 32, 33, 34, 32, 33, 34, 26, 27, 61, 61, 61, 61, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 0, 0, 36, 25, 27, 28, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 26, 27, 28, 26, 27, 28, 26, 27, 28, 31, 61, 61, 61, 61, 61, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 38, 37, 38, 29, 30, 31, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 29, 30, 31, 29, 30, 31, 29, 30, 25, 25, 25, 25, 61, 61, 61, 61, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 55, 0, 26, 27, 28, 26, 27, 34, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 32, 33, 34, 32, 33, 34, 31, 25, 25, 25, 25, 25, 25, 25, 61, 61, 55, 55, 55, 55, 55, 55, 55, 55, 0, 0, 0, 0, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 26, 27, 28, 26, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 61, 55, 55, 55, 55, 55, 55, 55, 55, 0, 0, 0, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 29, 30, 36, 35, 36, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 55, 55, 55, 55, 55, 55, 55, 55, 0, 0, 0, 0, 26, 27, 28, 26, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 36, 35, 36, 37, 38, 38, 35, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 55, 55, 55, 55, 55, 55, 55, 0, 0, 0, 0, 0, 31, 25, 25, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 35, 36, 38, 37, 38, 35, 37, 35, 36, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 55, 55, 38, 0, 0, 0, 0, 0, 0, 0, 25, 25, 25, 25, 25, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 37, 38, 38, 35, 35, 36, 37, 37, 38, 36, 36, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 0, 0, 0, 0, 0, 0, 25, 25, 25, 25, 25, 25, 25, 25, 27, 28, 26, 27, 28, 26, 27, 28, 26, 27, 28, 26, 37, 35, 36, 35, 37, 38, 35, 35, 36, 38, 38, 35, 36, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 0, 0, 0, 25, 25, 25, 25, 25, 25, 25, 25, 25, 29, 0, 31, 29, 30, 31, 29, 30, 31, 0, 0, 29, 30, 35, 36, 35, 36, 38, -1, -1, 37, 38, 36, 35, 36, 36, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 33, 0, 0, 0, 0, 0, 27, 28, 32, 33, 34, 0, 0, 0, 0, 37, 38, 35, 36, 36, 35, 36, 35, 36, 36, 37, 35, 35, 36, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 25, 0, 37, 35, 36, 38, 38, 37, 38, 37, 38, 38, -1, 37, 35, 36, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 0, 0, 0, 0, 0, 0, 0, 0, 25, 25, 0, 0, 0, 0, 25, 25, 25, 25, 35, 37, 38, 35, 35, 36, 35, 35, 36, 36, 35, 36, 37, 38, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 0, 0, 0, 0, 0, 0, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 38, 37, 37, 35, 36, 37, 38, 38, 35, 36, 36, 37, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 0, 0, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 35, 36, 37, 38, 35, 36, 35, 37, 38, 35, 36, 38, 37, 38, 38, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 37, 35, 36, 37, 38, 37, 38, 37, 38, 35, 37, 38, 37, 38, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 35, 36, 35, 36, 35, 35, 35, 36, 35, 36, 38, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 35, 36, 37, 37, 25, 25, 25, 25, 25, 37, 38, 35, 36, 37, 38, 35, 37, 35, 36, 37, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 37, 38, 35, 37, 37, 38, 37, 37, 38, 37, 38, 25, 25, 25, 25, 37, 38, 35, 35, 35, 36, 37, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 38, 37, 38, 37, 37, 38, 37, 38, 35, 35, 36, 37, 38, 38, 37, 25, 25, 25, 25, 35, 37, 37, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 36, 37, 38, 35, 36, 35, 36, 35, 36, 35, 36, 37, 37, 38, 35, 37, 38, 35, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 35, 37, 38, 38, 37, 38, 37, 38, 37, 38, 35, 36, 37, 38, 35, 35, 36, 37, 38, 35, 37, 37, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 37, 37, 38, 35, 36, 25, 25, 25, 25, 25, 25, 37, 38, 38, 38, 37, 38, 37, 35, 35, 36, 35, 36, 35, 36, 37, 37, 38, 35, 36, 37, 35, 35, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 37, 38, 36, 37, 37, 37, 35, 36, 25, 25, 25, 25, 25, 37, 38, 37, 38, 37, 38, 35, 37, 37, 38, 37, 38, 37, 38, 37, 35, 36, 37, 38, 37, 37, 37, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 36, 38, 35, 36, 38, 35, 36, 37, 37, 38, 25, 25, 25, 25, 25, 25, 25, 36, 38, 38, 38, 37, 35, 36, 35, 36, 35, 36, 35, 35, 37, 38, 35, 36, 37, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 35, 37, 38, 38, 36, 37, 38, 35, 36, 36, 35, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 35, 36, 37, 38, 37, 38, 35, 36, 35, 36, 35, 35, 37, 38, 35, 36, 35, 25, 25, 25, 25, 25, 25, 25, 35, 36, 35, 36, 38, 35, 37, 38, 37, 38, 37, 38, 38, 36, 35, 35, 35, 36, 25, 25, 25, 25, 25, 25, 37, 38, 35, 35, 36, 35, 37, 38, 37, 38, 37, 35, 36, 37, 35, 36, 35, 36, 35, 25, 25, 25, 25, 25, 35, 36, 37, 38, 37, 38, 37, 35, 36, 35, 36, 36, 35, 35, 36, 36, 37, 37, 37, 35, 36, 25, 25, 25, 25, 25, 25, 25, 37, 37, 35, 36, 36, 35, 35, 36, 35, 37, 38, 35, 37, 38, 35, 35, 36, 25, 25, 25, 35, 36, 37, 38, 36, 38, 37, 38, 36, 37, 38, 37, 35, 36, 37, 37, 38, 36, 37, 37, 37, 37, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 35, 36, 36, 35, 36, 35, 36, 37, 35, 36, 35, 37, 38, 25, 25, 25, 37, 38, 37, 38, 38, 36, 35, 37, 35, 36, 35, 36, 35, 36, 37, 35, 36, 38, 36, 35, 35, 37, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 35, 36, 36, 35, 36, 36, 35, 36, 37, 38, 25, 25, 25, 25, 37, 35, 36, 38, 37, 35, 36, 35, 37, 38, 37, 38, 37, 35, 36, 37, 38, 35, 36, 35, 37, 35, 35, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 35, 36, 35, 36, 37, 38, 25, 25, 25, 25, 25, 25, 37, 35, 35, 36, 36, 37, 35, 36, 35, 35, 36, 38, 35, 37, 38, 35, 35, 36, 36, 37, 38, 37, 37, 37, 35, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 37, 38, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 37, 38, 38, 37, 35, 36, 37, 37, 38, 36, 37, 37, 38, 37, 37, 38, 38, 37, 38, 37, 37, 37, 37, 37, 35, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 35, 36, 35, 36, 35, 36, 35, 36, 35, 36, 37, 35, 36, 35, 36, 35, 36, 35, 36, 37, 38, 37, 35, 35, 35, 35, 37, 35, 35, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 37, 38, 37, 38, 37, 38, 37, 38, 37, 38, 37, 37, 38, 37, 38, 37, 38, 37, 38, 38, 37, 37, 37, 37, 37, 37, 35, 37, 37, 37, 35, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}