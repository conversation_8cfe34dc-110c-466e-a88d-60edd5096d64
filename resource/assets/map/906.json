{"mW": 744, "mH": 720, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["3153", 0, 1, 1], ["302", 0, 2, 2], ["692", 0, 1, 1], ["369", 0, 3, 3]], "layers": [{"type": 3, "obj": [[2, "514", 9, 388, 32, 33, 2], [2, "514", 590, 510, 32, 33, 0], [2, "3204", 717, 137, 24, 37, 0], [2, "3226", 707, 167, 48, 45, 2], [2, "3226", 732, 194, 48, 45, 2], [2, "3226", 685, 178, 48, 45, 2], [2, "3226", 710, 205, 48, 45, 2], [2, "3226", 667, 190, 48, 45, 2], [2, "3226", 692, 217, 48, 45, 2], [2, "458", 714, 252, 54, 55, 0], [2, "3226", 572, 179, 48, 45, 0], [2, "3226", 553, 200, 48, 45, 0], [2, "3226", 594, 191, 48, 45, 0], [2, "3226", 575, 212, 48, 45, 0], [2, "3226", 617, 202, 48, 45, 0], [2, "3226", 598, 223, 48, 45, 0], [2, "3226", 640, 213, 48, 45, 0], [2, "3226", 621, 234, 48, 45, 0], [2, "3226", 663, 226, 48, 45, 0], [2, "3226", 644, 247, 48, 45, 0], [2, "3226", 687, 238, 48, 45, 0], [2, "3226", 668, 259, 48, 45, 0], [2, "3226", 706, 247, 48, 45, 0], [2, "3226", 687, 268, 48, 45, 0], [2, "3204", 592, 154, 24, 37, 2], [2, "3202", 605, 179, 30, 24, 2], [2, "3202", 628, 190, 30, 24, 2], [2, "3202", 652, 201, 30, 24, 2], [2, "3202", 675, 212, 30, 24, 2], [2, "3202", 696, 222, 30, 24, 2], [2, "3202", 719, 233, 30, 24, 2], [2, "3201", 739, 226, 28, 38, 2], [2, "458", 170, -32, 54, 55, 0], [2, "3226", 113, -62, 48, 45, 0], [2, "3226", 94, -41, 48, 45, 0], [2, "3226", 136, -49, 48, 45, 0], [2, "3226", 117, -28, 48, 45, 0], [2, "3226", 160, -37, 48, 45, 0], [2, "3226", 141, -16, 48, 45, 0], [2, "1230", 187, -28, 58, 34, 2], [2, "352", 559, 176, 44, 54, 2], [2, "352", 712, 249, 44, 54, 2], [2, "352", 156, -30, 44, 54, 2], [2, "458", 92, 709, 54, 55, 0], [2, "3226", -50, 636, 48, 45, 0], [2, "3226", -69, 657, 48, 45, 0], [2, "3226", -28, 648, 48, 45, 0], [2, "3226", -47, 669, 48, 45, 0], [2, "3226", -5, 659, 48, 45, 0], [2, "3226", -24, 680, 48, 45, 0], [2, "3226", 18, 670, 48, 45, 0], [2, "3226", -1, 691, 48, 45, 0], [2, "3226", 41, 683, 48, 45, 0], [2, "3226", 22, 704, 48, 45, 0], [2, "3226", 65, 695, 48, 45, 0], [2, "3226", 46, 716, 48, 45, 0], [2, "3226", 84, 704, 48, 45, 0], [2, "3226", 65, 725, 48, 45, 0], [2, "3202", 30, 658, 30, 24, 2], [2, "3202", 53, 669, 30, 24, 2], [2, "3202", 74, 679, 30, 24, 2], [2, "3202", 97, 690, 30, 24, 2], [2, "3201", 117, 683, 28, 38, 2], [2, "352", -63, 633, 44, 54, 2], [2, "352", 86, 707, 44, 54, 2], [2, "3202", -11, 639, 30, 24, 2], [2, "3202", 10, 649, 30, 24, 2], [2, "3431", 16, 629, 66, 66, 0], [2, "3184", 593, 204, 24, 43, 0], [2, "3184", 670, 243, 24, 43, 0], [2, "3184", 630, 223, 24, 43, 0], [2, "3202", 698, 160, 30, 24, 0], [2, "3202", 674, 171, 30, 24, 0], [2, "3202", 652, 182, 30, 24, 0], [2, "3431", 640, 163, 66, 66, 0], [2, "3226", -31, 144, 48, 45, 2], [2, "3226", -6, 171, 48, 45, 2], [2, "3226", -53, 155, 48, 45, 2], [2, "3226", -28, 182, 48, 45, 2], [2, "516", 560, 462, 58, 52, 0], [2, "516", 611, 488, 58, 52, 0], [2, "516", 554, 465, 58, 52, 0], [2, "516", 605, 491, 58, 52, 0], [2, "516", 10, 342, 58, 52, 2], [2, "516", 16, 345, 58, 52, 2], [2, "516", -40, 369, 58, 52, 2], [2, "516", -34, 372, 58, 52, 2], [2, "3449", 250, 668, 44, 94, 0], [2, "3226", 707, 668, 48, 45, 2], [2, "3226", 732, 695, 48, 45, 2], [2, "3226", 729, 657, 48, 45, 2], [2, "3226", 754, 684, 48, 45, 2], [2, "3226", 707, 668, 48, 45, 2], [2, "3226", 732, 695, 48, 45, 2], [2, "3226", 729, 657, 48, 45, 2], [2, "3226", 754, 684, 48, 45, 2], [2, "3226", 671, 686, 48, 45, 2], [2, "3226", 696, 713, 48, 45, 2], [2, "3226", 693, 675, 48, 45, 2], [2, "3226", 718, 702, 48, 45, 2], [2, "3226", 628, 710, 48, 45, 2], [2, "3226", 653, 737, 48, 45, 2], [2, "3226", 650, 699, 48, 45, 2], [2, "3226", 675, 726, 48, 45, 2], [2, "3202", 723, 650, 30, 24, 0], [2, "3202", 702, 660, 30, 24, 0], [2, "3202", 681, 671, 30, 24, 0], [2, "3202", 660, 681, 30, 24, 0], [2, "3202", 637, 692, 30, 24, 0], [2, "3202", 616, 702, 30, 24, 0], [2, "3201", 595, 695, 28, 38, 0], [2, "3431", 663, 637, 66, 66, 2], [2, "1372", 724, 514, 88, 128, 0], [2, "1372", 673, 698, 88, 128, 0], [2, "1372", -59, 541, 88, 128, 0], [2, "1372", 129, 676, 88, 128, 0], [2, "1372", -51, 695, 88, 128, 0], [2, "3439", 58, 352, 36, 48, 0], [2, "3439", 538, 483, 36, 48, 0], [2, "3439", 632, 534, 36, 48, 0], [2, "1421", 4, 403, 10, 26, 0], [2, "1421", 21, 394, 10, 26, 0], [2, "1421", 43, 383, 10, 26, 0], [2, "1421", 579, 506, 10, 26, 0], [2, "1421", 602, 516, 10, 26, 0], [2, "1421", 625, 528, 10, 26, 0], [2, "1421", 294, 298, 10, 26, 0], [2, "1421", 205, 286, 10, 26, 0], [2, "1421", 205, 286, 10, 26, 0], [2, "1421", 194, 328, 10, 26, 0], [2, "1421", 429, 405, 10, 26, 0], [2, "1421", 437, 468, 10, 26, 0], [2, "1421", 499, 440, 10, 26, 0], [2, "1421", 16, 564, 10, 26, 0], [2, "1421", 625, 235, 10, 26, 0], [2, "1421", 643, 242, 10, 26, 0], [2, "3148", 523, 446, 70, 55, 2], [2, "3148", 556, 461, 70, 55, 2], [2, "3148", 588, 475, 70, 55, 2], [2, "3148", 609, 486, 70, 55, 2], [2, "3204", 553, 416, 24, 37, 2], [2, "3202", 566, 441, 30, 24, 2], [2, "3202", 589, 452, 30, 24, 2], [2, "3202", 613, 464, 30, 24, 2], [2, "3202", 636, 475, 30, 24, 2], [2, "3202", 655, 485, 30, 24, 2], [2, "3201", 664, 472, 28, 38, 2], [2, "3148", 20, 313, 70, 55, 0], [2, "3148", -16, 330, 70, 55, 0], [2, "3148", -54, 348, 70, 55, 0], [2, "3204", 39, 286, 24, 37, 0], [2, "3202", 18, 310, 30, 24, 0], [2, "3202", -4, 321, 30, 24, 0], [2, "3202", -28, 333, 30, 24, 0]]}, {"type": 4, "obj": [[2, "1421", 40, -8, 10, 26, 0], [2, "13", 311, -2, 22, 24, 0], [2, "1421", 229, 5, 10, 26, 0], [2, "12", 271, 12, 26, 28, 0], [2, "1421", 162, 26, 10, 26, 0], [2, "1421", 371, 54, 10, 26, 0], [2, "3449", 668, 3, 44, 94, 0], [2, "3083", 331, 1, 118, 106, 2], [2, "3449", 310, 22, 44, 94, 0], [2, "3171", 200, 46, 48, 80, 0], [2, "3112", 647, 55, 90, 84, 0], [2, "3449", 568, 46, 44, 94, 0], [2, "1423", 288, 94, 20, 52, 0], [2, "1421", 524, 125, 10, 26, 0], [2, "3449", 258, 63, 44, 94, 2], [2, "1423", 257, 106, 20, 52, 0], [2, "3356", 700, 113, 44, 49, 0], [2, "3356", 663, 125, 44, 49, 2], [2, "3037", 286, 109, 44, 69, 2], [2, "3142", 251, 129, 36, 51, 0], [2, "3142", 287, 130, 36, 51, 2], [2, "3083", 519, 86, 118, 106, 2], [2, "3449", 458, 103, 44, 94, 0], [2, "3171", 23, 139, 48, 80, 2], [2, "1423", 154, 169, 20, 52, 0], [2, "3142", 167, 172, 36, 51, 0], [2, "3037", 383, 157, 44, 69, 2], [2, "3142", 375, 176, 36, 51, 2], [2, "3174", 482, 159, 70, 68, 2], [2, "3449", 123, 139, 44, 94, 2], [2, "1423", 122, 186, 20, 52, 0], [2, "3142", 163, 188, 36, 51, 2], [2, "1421", 14, 217, 10, 26, 0], [2, "3142", 371, 193, 36, 51, 0], [2, "3037", 139, 185, 44, 69, 2], [2, "3441", 386, 209, 52, 59, 2], [2, "1421", 576, 245, 10, 26, 0], [2, "3439", 540, 236, 36, 48, 0], [2, "1421", 597, 258, 10, 26, 0], [2, "3142", 293, 234, 36, 51, 0], [2, "3142", 260, 237, 36, 51, 2], [2, "3449", 26, 201, 44, 94, 2], [2, "3037", 250, 242, 44, 69, 2], [2, "1421", 661, 288, 10, 26, 0], [2, "3441", 294, 259, 52, 59, 2], [2, "1421", 691, 303, 10, 26, 0], [2, "1421", 691, 303, 10, 26, 0], [2, "3439", 690, 310, 36, 48, 0], [2, "3449", 206, 272, 44, 94, 0], [2, "3030", 176, 338, 38, 48, 0], [2, "3030", 297, 338, 38, 48, 2], [2, "3030", 297, 338, 38, 48, 2], [2, "3183", 239, 314, 44, 75, 0], [2, "3161", 343, 322, 68, 75, 0], [2, "1372", 183, 278, 88, 128, 0], [2, "3030", 341, 362, 38, 48, 2], [2, "3449", 92, 320, 44, 94, 0], [2, "1372", 275, 288, 88, 128, 0], [2, "3433", 192, 388, 30, 30, 0], [2, "3449", 376, 331, 44, 94, 2], [2, "3433", 255, 403, 30, 30, 0], [2, "518", 53, 330, 16, 108, 0], [2, "3030", 335, 390, 38, 48, 0], [2, "1372", 116, 313, 88, 128, 0], [2, "3161", 514, 387, 68, 75, 0], [2, "3449", 460, 370, 44, 94, 2], [2, "3030", 496, 437, 38, 48, 2], [2, "3030", 424, 441, 38, 48, 0], [2, "3421", 108, 392, 82, 98, 0], [2, "3032", 63, 429, 62, 68, 0], [2, "3161", 569, 433, 68, 75, 0], [2, "3449", 562, 416, 44, 94, 2], [2, "3419", 199, 428, 76, 84, 0], [2, "1372", 410, 392, 88, 128, 0], [2, "3449", 689, 432, 44, 94, 0], [2, "3433", 503, 508, 30, 30, 0], [2, "1372", 366, 423, 88, 128, 0], [2, "1372", 485, 424, 88, 128, 0], [2, "3420", 275, 441, 72, 119, 0], [2, "3171", 692, 483, 48, 80, 0], [2, "3183", 452, 505, 44, 75, 0], [2, "3433", 519, 550, 30, 30, 0], [2, "518", 555, 487, 16, 108, 2], [2, "3423", 107, 541, 88, 71, 2], [2, "3421", 380, 530, 82, 98, 0], [2, "3440", 429, 591, 74, 47, 2], [2, "518", 652, 538, 16, 108, 2], [2, "3032", 493, 585, 62, 68, 2], [2, "3427", 248, 626, 66, 39, 0], [2, "3030", 451, 617, 38, 48, 0], [2, "3030", 451, 617, 38, 48, 0], [2, "3449", 686, 587, 44, 94, 0], [2, "3440", 318, 644, 74, 47, 2], [2, "3032", 599, 638, 62, 68, 2], [2, "3030", 338, 670, 38, 48, 0]]}, {"type": 3, "obj": [[2, "791", 157, 153, 104, 53, 1], [2, "791", 320, 159, 104, 53, 3], [2, "3372", 722, 203, 44, 81, 0], [2, "470", 226, -43, 18, 62, 2], [2, "470", 244, -52, 18, 62, 2], [2, "470", 262, -62, 18, 62, 2], [2, "818", 253, -9, 30, 37, 0], [2, "818", 224, 5, 30, 37, 0], [2, "818", 571, 295, 30, 37, 2], [2, "818", 601, 311, 30, 37, 2], [2, "818", 631, 326, 30, 37, 2], [2, "818", 661, 340, 30, 37, 2], [2, "818", 678, 349, 30, 37, 2], [2, "818", 708, 350, 30, 37, 0], [2, "818", 737, 335, 30, 37, 0], [2, "3372", 707, 284, 44, 81, 0], [2, "3372", 573, 238, 44, 81, 2], [2, "3372", 618, 261, 44, 81, 2], [2, "3372", 662, 283, 44, 81, 2], [2, "345", 624, 284, 24, 73, 2], [2, "1192", 625, 270, 24, 25, 2], [2, "3453", 612, 269, 8, 49, 0], [2, "3454", 650, 286, 8, 49, 0], [2, "524", 598, 314, 26, 39, 2], [2, "524", 630, 330, 26, 39, 2], [2, "827", 568, 230, 8, 48, 0], [2, "827", 568, 271, 8, 48, 0], [2, "827", 703, 297, 8, 48, 0], [2, "827", 703, 338, 8, 48, 0], [2, "756", 580, 261, 28, 40, 2], [2, "756", 668, 303, 28, 40, 2], [2, "3166", 727, 306, 28, 33, 0], [2, "1373", 540, 520, 16, 32, 0], [2, "1373", 278, 369, 16, 32, 0], [2, "1373", 169, 405, 16, 32, 0], [2, "1373", 472, 481, 16, 32, 0], [2, "1373", 449, 562, 16, 32, 0], [2, "362", 104, 565, 64, 42, 0], [2, "362", 139, 585, 64, 42, 0], [2, "3434", 330, 553, 48, 32, 0], [2, "3428", 81, 543, 44, 31, 0], [2, "3430", 176, 559, 44, 31, 0], [2, "3429", 75, 579, 44, 31, 0], [2, "3427", 97, 591, 66, 39, 0], [2, "3428", 192, 591, 44, 31, 0], [2, "3430", 152, 610, 44, 31, 0], [2, "3434", 453, 602, 48, 32, 0], [2, "3434", 81, 413, 48, 32, 0], [2, "3392", 467, 460, 34, 33, 0], [2, "3392", 297, 419, 34, 33, 0], [2, "220_1", 322, 392, 40, 29, 2], [2, "219_1", 190, 367, 36, 30, 0], [2, "219_1", 241, 387, 36, 30, 0], [2, "219_1", 180, 442, 36, 30, 0], [2, "220_1", 340, 526, 40, 29, 2], [2, "220_1", 417, 486, 40, 29, 2], [2, "220_1", 484, 533, 40, 29, 2], [2, "219_1", 453, 490, 36, 30, 0], [2, "219_1", 456, 533, 36, 30, 0], [2, "219_1", 513, 591, 36, 30, 2], [2, "818", 69, 22, 30, 37, 2], [2, "818", 52, 13, 30, 37, 2], [2, "818", 113, 45, 30, 37, 2], [2, "818", 96, 36, 30, 37, 2], [2, "818", 143, 60, 30, 37, 2], [2, "470", 54, -40, 18, 62, 0], [2, "470", 72, -30, 18, 62, 0], [2, "470", 89, -20, 18, 62, 0], [2, "470", 107, -12, 18, 62, 0], [2, "470", 120, -4, 18, 62, 0], [2, "470", 138, 5, 18, 62, 0], [2, "470", 155, 13, 18, 62, 0], [2, "818", 173, 60, 30, 37, 0], [2, "818", 202, 46, 30, 37, 0], [2, "470", 173, 13, 18, 62, 2], [2, "470", 191, 4, 18, 62, 2], [2, "470", 209, -6, 18, 62, 2], [2, "3373", 240, 12, 52, 36, 0], [2, "3373", 306, 10, 52, 36, 2], [2, "3373", 237, 56, 52, 36, 2], [2, "3373", 307, 58, 52, 36, 0], [2, "345", 98, -1, 24, 73, 2], [2, "744", 187, 12, 30, 42, 2], [2, "14", 198, 65, 32, 30, 0], [2, "15", 202, 50, 22, 27, 0], [2, "3455", 256, 17, 84, 62, 0], [2, "14", 380, -7, 32, 30, 0], [2, "14", 351, -4, 32, 30, 0], [2, "14", 368, 7, 32, 30, 0], [2, "14", 364, -11, 32, 30, 0], [2, "11", 430, 32, 32, 29, 0], [2, "3166", 55, -18, 28, 33, 2], [2, "3166", 134, 19, 28, 33, 2], [2, "3453", 88, -2, 8, 49, 0], [2, "3453", -438, -275, 8, 49, 0], [2, "3454", 124, 13, 8, 49, 0], [2, "827", 173, 27, 8, 48, 2], [2, "827", 173, 6, 8, 48, 2], [2, "11", 175, 79, 32, 29, 0], [2, "827", 223, 2, 8, 48, 2], [2, "827", 223, -19, 8, 48, 2], [2, "3212", 712, 70, 14, 27, 0], [2, "3213", 728, 60, 20, 25, 0], [2, "3211", 722, 78, 24, 28, 0], [2, "3213", 621, 117, 20, 25, 0], [2, "3212", 639, 137, 14, 27, 0], [2, "3212", 712, 70, 14, 27, 0], [2, "3185", 487, 268, 66, 36, 2], [2, "524", 31, 6, 26, 39, 2], [2, "524", 147, 63, 26, 39, 2], [2, "3213", 51, 22, 20, 25, 0], [2, "3212", 67, 27, 14, 27, 0], [2, "1434", 43, -14, 34, 43, 0], [2, "119", 131, 57, 18, 31, 0], [2, "818", -28, 274, 30, 37, 0], [2, "818", 1, 259, 30, 37, 0], [2, "470", -3, 214, 18, 62, 2], [2, "470", 12, 205, 18, 62, 2], [2, "827", 24, 214, 8, 48, 2], [2, "827", 24, 207, 8, 48, 2], [2, "1422", -12, 229, 28, 30, 1], [2, "3373", 157, 213, 52, 36, 2], [2, "3373", 360, 217, 52, 36, 0], [2, "3373", 359, 221, 52, 36, 0], [2, "3373", 197, 233, 52, 36, 2], [2, "3373", 318, 241, 52, 36, 0], [2, "3373", 238, 254, 52, 36, 2], [2, "3373", 278, 261, 52, 36, 0], [2, "1504", 158, 207, 50, 26, 0], [2, "1504", 184, 194, 50, 26, 0], [2, "1504", 209, 206, 50, 26, 0], [2, "1504", 183, 219, 50, 26, 0], [2, "1504", 260, 180, 50, 26, 0], [2, "1504", 234, 193, 50, 26, 0], [2, "1504", 235, 168, 50, 26, 0], [2, "1504", 209, 181, 50, 26, 0], [2, "1504", 306, 203, 50, 26, 0], [2, "1504", 280, 216, 50, 26, 0], [2, "1504", 281, 191, 50, 26, 0], [2, "1504", 255, 204, 50, 26, 0], [2, "1504", 255, 229, 50, 26, 0], [2, "1504", 229, 242, 50, 26, 0], [2, "1504", 230, 217, 50, 26, 0], [2, "1504", 204, 230, 50, 26, 0], [2, "1504", 344, 223, 50, 26, 0], [2, "1504", 318, 236, 50, 26, 0], [2, "1504", 319, 211, 50, 26, 0], [2, "1504", 293, 224, 50, 26, 0], [2, "1504", 293, 249, 50, 26, 0], [2, "1504", 267, 262, 50, 26, 0], [2, "1504", 268, 237, 50, 26, 0], [2, "1504", 242, 250, 50, 26, 0], [2, "1504", 368, 210, 50, 26, 0], [2, "1504", 343, 197, 50, 26, 0], [2, "1504", 318, 185, 50, 26, 0], [2, "1504", 294, 173, 50, 26, 0], [2, "1504", 270, 161, 50, 26, 0], [2, "1504", 260, 155, 50, 26, 0], [2, "3373", 359, 234, 52, 36, 0], [2, "3373", 176, 236, 52, 36, 2], [2, "3373", 215, 255, 52, 36, 2], [2, "932", 184, 240, 76, 48, 0], [2, "3373", 318, 254, 52, 36, 0], [2, "932", 326, 237, 76, 48, 2], [2, "3228", 232, 188, 106, 58, 0], [2, "14", 724, 362, 32, 30, 0], [2, "3160", 693, 367, 62, 52, 0], [2, "3213", 732, 346, 20, 25, 0], [2, "11", 557, 308, 32, 29, 0], [2, "3408", 650, 359, 42, 26, 2], [2, "43_1", -40, 12, 82, 58, 2], [2, "43_1", -66, -16, 82, 58, 2], [2, "219_1", 223, 359, 36, 30, 0], [2, "219_1", 216, 417, 36, 30, 0], [2, "219_1", 477, 574, 36, 30, 0], [2, "3425", 352, 563, 38, 39, 0], [2, "3426", 124, 606, 38, 39, 0], [2, "3426", 329, 548, 38, 39, 0], [2, "3425", 179, 463, 38, 39, 0], [2, "362", 252, 636, 64, 42, 0], [2, "1442", 148, 628, 38, 31, 0], [2, "1442", 226, 588, 38, 31, 0], [2, "1442", 46, 562, 38, 31, 0], [2, "791", 137, 255, 104, 53, 0], [2, "791", 340, 255, 104, 53, 2], [2, "3025", 242, 488, 92, 53, 0], [2, "3025", 307, 455, 92, 53, 0], [2, "3025", 355, 427, 92, 53, 0], [2, "1452", 389, 283, 30, 22, 0], [2, "1452", 418, 273, 30, 22, 0], [2, "1452", 110, 233, 30, 22, 0], [2, "1452", 266, 165, 30, 22, 0], [2, "1452", 266, 165, 30, 22, 0], [2, "1452", 143, 253, 30, 22, 0], [2, "1452", 93, 283, 30, 22, 0], [2, "1452", 27, 416, 30, 22, 0], [2, "1452", 12, 455, 30, 22, 0], [2, "1452", 485, 645, 30, 22, 0], [2, "1452", 332, 600, 30, 22, 0], [2, "1452", 542, 382, 30, 22, 0], [2, "1452", 657, 421, 30, 22, 0], [2, "1452", 660, 453, 30, 22, 0], [2, "1452", 116, 126, 30, 22, 0], [2, "1452", 191, 153, 30, 22, 0], [2, "1452", 401, 120, 30, 22, 0], [2, "1452", 401, 120, 30, 22, 0], [2, "1452", 391, 139, 30, 22, 0], [2, "1452", 391, 139, 30, 22, 0], [2, "1452", 418, 146, 30, 22, 0], [2, "1452", 414, 165, 30, 22, 0], [2, "1452", 414, 165, 30, 22, 0], [2, "1452", 482, 133, 30, 22, 0], [2, "1452", 499, 86, 30, 22, 0], [2, "1452", 145, 287, 30, 22, 0], [2, "1452", 401, 320, 30, 22, 0], [2, "1452", 401, 320, 30, 22, 0], [2, "1452", 363, 299, 30, 22, 0], [2, "1452", 458, 316, 30, 22, 0], [2, "1452", 336, 236, 30, 22, 0], [2, "1452", 205, 235, 30, 22, 0], [2, "3083", 449, -62, 118, 106, 2], [2, "519", 548, 594, 28, 35, 2], [2, "519", 645, 645, 28, 35, 2], [2, "519", 46, 438, 28, 35, 0]]}, {"type": 2, "data": [10, 10, 10, 22, 22, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, 10, 22, 25, 26, -1, 22, -1, -1, 22, 22, -1, -1, 24, 25, 26, -1, 41, 40, 39, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, 22, -1, 22, 22, 24, 25, 26, -1, 41, 40, 39, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, -1, 24, 25, 26, -1, 41, 40, 39, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 22, -1, -1, -1, -1, 31, 30, 29, 24, 25, 26, -1, -1, -1, 31, 19, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 22, 22, -1, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 22, 24, 25, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, 41, 40, 39, 22, 24, 25, 26, -1, -1, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, 20, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 24, 25, 26, -1, -1, -1, -1, -1, 41, 40, 39, 16, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 16, 16, 22, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 16, 16, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 16, 16, 22, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 19, 23, 23, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 16, 16, -1, -1, -1, -1, -1, -1, -1, 13, 14, -1, -1, 20, 19, 19, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, 22, 22, -1, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 41, 40, 39, 24, 30, 29, 22, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 24, 25, 26, -1, 31, 30, 29, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, 31, 30, 29, 34, 35, 36, -1, -1, 8, 7, 16, 16, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 34, 35, 36, 11, 10, 23, 22, 22, 22, 22, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, 4, 5, 20, 19, 23, 22, 22, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 10, -1, -1, -1, 7, 6, -1, -1, -1, 31, 30, 29, 22, -1, 6, 20, 19, 19, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, -1, -1, 16, 21, -1, -1, -1, -1, -1, 31, 30, 29, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 16, 17, 14, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 13, 14, -1, 8, -1, 7, 6, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1]}, {"type": 3, "obj": [[2, "468", 274, 357, 58, 31, 2], [2, "468", 177, 355, 58, 31, 0], [2, "468", 299, 370, 58, 31, 2], [2, "498", -32, 304, 56, 32, 0], [2, "468", 339, 391, 58, 31, 2], [2, "468", 381, 411, 58, 31, 2], [2, "468", 423, 432, 58, 31, 2], [2, "468", 465, 453, 58, 31, 2], [2, "468", 506, 473, 58, 31, 2], [2, "468", 535, 487, 58, 31, 2], [2, "513", 574, 506, 20, 40, 0], [2, "498", 8, 23, 56, 32, 2], [2, "498", 49, 43, 56, 32, 2], [2, "498", 89, 63, 56, 32, 2], [2, "498", 130, 83, 56, 32, 2], [2, "468", 105, 390, 58, 31, 0], [2, "498", 536, 602, 56, 32, 0], [2, "498", 495, 622, 56, 32, 0], [2, "498", 454, 642, 56, 32, 0], [2, "468", 701, 498, 58, 31, 0], [2, "513", 700, 517, 20, 40, 2], [2, "498", 702, 547, 56, 32, 2], [2, "513", 37, 223, 20, 40, 0], [2, "513", 37, 250, 20, 40, 0], [2, "513", 37, 191, 20, 40, 0], [2, "468", 63, 411, 58, 31, 0], [2, "498", 413, 662, 56, 32, 0], [2, "498", 373, 682, 56, 32, 0], [2, "513", 220, 351, 40, 21, 7], [2, "513", 251, 351, 40, 21, 7], [2, "468", 339, 391, 58, 31, 2], [2, "498", 307, 526, 56, 32, 0], [2, "498", 347, 506, 56, 32, 0], [2, "498", 388, 486, 56, 32, 0], [2, "498", 429, 467, 56, 32, 0], [2, "468", 308, 539, 58, 31, 2], [2, "468", 349, 560, 58, 31, 2], [2, "468", 390, 580, 58, 31, 2], [2, "468", 432, 602, 58, 31, 2], [2, "468", 454, 612, 58, 31, 2], [2, "468", 100, 430, 58, 31, 2], [2, "468", 141, 451, 58, 31, 2], [2, "468", 182, 471, 58, 31, 2], [2, "468", 323, 414, 58, 31, 0], [2, "468", 281, 434, 58, 31, 0], [2, "468", 248, 451, 58, 31, 0], [2, "468", 206, 471, 58, 31, 0], [2, "498", 522, 144, 56, 32, 0], [2, "498", 562, 124, 56, 32, 0], [2, "498", 603, 104, 56, 32, 0], [2, "498", 644, 84, 56, 32, 0], [2, "498", 684, 64, 56, 32, 0], [2, "498", 724, 45, 56, 32, 0], [2, "513", 574, 538, 20, 40, 0], [2, "513", 574, 574, 20, 40, 1], [2, "498", 484, 163, 56, 32, 0], [2, "498", 469, 170, 56, 32, 0], [2, "513", 469, 180, 20, 40, 0], [2, "513", 469, 209, 20, 40, 0], [2, "513", 469, 252, 20, 40, 0], [2, "513", 469, 236, 20, 40, 0], [2, "468", 466, 286, 58, 31, 2], [2, "468", 509, 306, 58, 31, 2], [2, "468", 550, 326, 58, 31, 2], [2, "468", 593, 346, 58, 31, 2], [2, "468", 635, 366, 58, 31, 2], [2, "468", 22, 432, 58, 31, 0], [2, "468", -20, 453, 58, 31, 0], [2, "468", 147, 369, 58, 31, 0], [2, "498", 149, 92, 56, 32, 2], [2, "498", 8, 284, 56, 32, 0], [2, "468", 90, 426, 58, 31, 2], [2, "513", 222, 106, 40, 21, 5], [2, "513", 251, 106, 40, 21, 5], [2, "513", 281, 106, 40, 21, 5], [2, "513", 310, 106, 40, 21, 5], [2, "498", 588, -33, 56, 32, 0], [2, "498", 548, -14, 56, 32, 0], [2, "498", 508, 6, 56, 32, 0], [2, "498", 467, 26, 56, 32, 0], [2, "498", 426, 46, 56, 32, 0], [2, "498", 386, 66, 56, 32, 0], [2, "498", 333, 92, 56, 32, 0], [2, "498", 365, 77, 56, 32, 0], [2, "468", -43, 151, 58, 31, 2], [2, "468", -2, 171, 58, 31, 2], [2, "468", 669, 382, 58, 31, 2], [2, "468", 710, 402, 58, 31, 2], [2, "498", 332, 702, 56, 32, 0], [2, "513", 193, 106, 40, 21, 5], [2, "498", -32, 4, 56, 32, 2], [2, "1102", 305, 533, 114, 63, 0], [2, "1102", 403, 579, 114, 63, 0], [2, "1102", 375, 452, 114, 63, 2], [2, "1102", 304, 486, 114, 63, 2], [2, "1102", 489, 584, 114, 63, 2], [2, "1102", 478, 458, 114, 63, 0], [2, "1102", 286, 361, 114, 63, 0], [2, "1102", 95, 365, 114, 63, 2], [2, "1102", 282, 403, 114, 63, 2], [2, "1102", 282, 403, 114, 63, 2], [2, "1102", 210, 438, 114, 63, 2], [2, "1102", 81, 411, 114, 63, 0], [2, "1102", 134, 437, 114, 63, 0]]}, {"type": 2, "data": [44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, -1, -1, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 46, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 45, 46, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 45, 46, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 45, 46, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 45, 46, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 47, 48, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 54, 54, 54, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 54, 54, 54, 54, 54, 54, 54, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 49, 49, 49, 49, 45, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 54, 54, 54, 54, 54, 54, 54, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 54, 54, 54, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 45, 54, 54, 54, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 54, 54, 54, 54, 54, 54, 54, 54, 49, 49, 49, 49, 49, 49, 45, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 49, 49, 49, 49, 49, 49, 45, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 54, 54, 54, 54, 54, 54, 54, 54, 54, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 54, 54, 54, 54, 54, 54, 54, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 54, 54, 54, 54, 54, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 54, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}