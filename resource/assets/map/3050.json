{"mW": 984, "mH": 648, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1233", 0, 3, 2], ["315", 0, 3, 3], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["937", 0, 2, 1], ["937", 1, 2, 1]], "layers": [{"type": 3, "obj": [[2, "3988", -40, 232, 148, 179, 2], [2, "3988", 197, 151, 148, 179, 0], [2, "3988", 500, 290, 148, 179, 0], [2, "3036", 774, 475, 70, 54, 2], [2, "3036", 802, 445, 70, 54, 2], [2, "3036", 833, 413, 70, 54, 2], [2, "1501", 936, 448, 50, 26, 0], [2, "1501", 911, 461, 50, 26, 0], [2, "1501", 783, 526, 50, 26, 0], [2, "1501", 809, 513, 50, 26, 0], [2, "1501", 758, 539, 50, 26, 0], [2, "1501", 783, 505, 50, 26, 0], [2, "1501", 809, 492, 50, 26, 0], [2, "1501", 758, 518, 50, 26, 0], [2, "1501", 815, 533, 50, 26, 0], [2, "1501", 841, 520, 50, 26, 0], [2, "1501", 790, 546, 50, 26, 0], [2, "1501", 829, 543, 50, 26, 0], [2, "1501", 855, 530, 50, 26, 0], [2, "1501", 804, 556, 50, 26, 0], [2, "1501", 709, 543, 50, 26, 0], [2, "1501", 735, 530, 50, 26, 0], [2, "1501", 684, 556, 50, 26, 0], [2, "1501", 632, 582, 50, 26, 0], [2, "1501", 658, 569, 50, 26, 0], [2, "1501", 607, 595, 50, 26, 0], [2, "1501", 555, 621, 50, 26, 0], [2, "1501", 581, 608, 50, 26, 0], [2, "1501", 530, 634, 50, 26, 0], [2, "1501", 651, 598, 50, 26, 0], [2, "1501", 677, 585, 50, 26, 0], [2, "1501", 626, 611, 50, 26, 0], [2, "1501", 785, 555, 50, 26, 0], [2, "1501", 811, 542, 50, 26, 0], [2, "1501", 760, 568, 50, 26, 0], [2, "1501", 708, 594, 50, 26, 0], [2, "1501", 734, 581, 50, 26, 0], [2, "1501", 683, 607, 50, 26, 0], [2, "1501", 728, 559, 50, 26, 0], [2, "1501", 754, 546, 50, 26, 0], [2, "1501", 703, 572, 50, 26, 0], [2, "1501", 860, 487, 50, 26, 0], [2, "1501", 886, 474, 50, 26, 0], [2, "1501", 835, 500, 50, 26, 0], [2, "1501", 895, 494, 50, 26, 0], [2, "1501", 921, 481, 50, 26, 0], [2, "1501", 870, 507, 50, 26, 0], [2, "1501", 906, 509, 50, 26, 0], [2, "1501", 932, 496, 50, 26, 0], [2, "1501", 881, 522, 50, 26, 0], [2, "1501", 931, 439, 50, 26, 0], [2, "1501", 957, 452, 50, 26, 0], [2, "1501", 983, 465, 50, 26, 0], [2, "1501", 1009, 478, 50, 26, 0], [2, "1501", 860, 466, 50, 26, 0], [2, "1501", 886, 453, 50, 26, 0], [2, "1501", 835, 479, 50, 26, 0], [2, "1501", 896, 448, 50, 26, 0], [2, "1501", 922, 435, 50, 26, 0], [2, "1501", 871, 461, 50, 26, 0], [2, "1501", 898, 462, 50, 26, 0], [2, "1501", 924, 449, 50, 26, 0], [2, "1501", 873, 475, 50, 26, 0], [2, "1501", 395, 195, 50, 26, 0], [2, "1501", 369, 208, 50, 26, 0], [2, "1501", 344, 221, 50, 26, 0], [2, "1501", 325, 230, 50, 26, 0], [2, "1501", 299, 243, 50, 26, 0], [2, "1501", 274, 256, 50, 26, 0], [2, "1501", 908, 479, 50, 26, 0], [2, "1501", 934, 466, 50, 26, 0], [2, "1501", 883, 492, 50, 26, 0], [2, "1501", 908, 501, 50, 26, 0], [2, "1501", 934, 488, 50, 26, 0], [2, "1501", 960, 475, 50, 26, 0], [2, "1501", 986, 488, 50, 26, 0], [2, "1501", 960, 501, 50, 26, 0], [2, "1501", 934, 514, 50, 26, 0], [2, "1501", 960, 527, 50, 26, 0], [2, "1501", 986, 514, 50, 26, 0], [2, "1501", 1012, 501, 50, 26, 0], [2, "1501", 874, 499, 50, 26, 0], [2, "1501", 900, 486, 50, 26, 0], [2, "1501", 926, 473, 50, 26, 0], [2, "1501", 952, 486, 50, 26, 0], [2, "1501", 926, 499, 50, 26, 0], [2, "1501", 900, 512, 50, 26, 0], [2, "1501", 926, 525, 50, 26, 0], [2, "1501", 952, 512, 50, 26, 0], [2, "1501", 978, 499, 50, 26, 0], [2, "1501", 800, 535, 50, 26, 0], [2, "1501", 826, 522, 50, 26, 0], [2, "1501", 852, 509, 50, 26, 0], [2, "1501", 878, 522, 50, 26, 0], [2, "1501", 852, 535, 50, 26, 0], [2, "1501", 826, 548, 50, 26, 0], [2, "1501", 852, 561, 50, 26, 0], [2, "1501", 878, 548, 50, 26, 0], [2, "1501", 904, 535, 50, 26, 0], [2, "1501", 693, 617, 50, 26, 0], [2, "1501", 719, 604, 50, 26, 0], [2, "1501", 745, 591, 50, 26, 0], [2, "1501", 771, 604, 50, 26, 0], [2, "1501", 745, 617, 50, 26, 0], [2, "1501", 719, 630, 50, 26, 0], [2, "1501", 745, 643, 50, 26, 0], [2, "1501", 771, 630, 50, 26, 0], [2, "1501", 797, 617, 50, 26, 0], [2, "1501", 767, 581, 50, 26, 0], [2, "1501", 793, 568, 50, 26, 0], [2, "1501", 819, 555, 50, 26, 0], [2, "1501", 845, 568, 50, 26, 0], [2, "1501", 819, 581, 50, 26, 0], [2, "1501", 793, 594, 50, 26, 0], [2, "1501", 819, 607, 50, 26, 0], [2, "1501", 845, 594, 50, 26, 0], [2, "1501", 871, 581, 50, 26, 0], [2, "1501", 838, 540, 50, 26, 0], [2, "1501", 864, 527, 50, 26, 0], [2, "1501", 890, 514, 50, 26, 0], [2, "1501", 916, 527, 50, 26, 0], [2, "1501", 890, 540, 50, 26, 0], [2, "1501", 864, 553, 50, 26, 0], [2, "1501", 890, 566, 50, 26, 0], [2, "1501", 916, 553, 50, 26, 0], [2, "1501", 942, 540, 50, 26, 0], [2, "1501", 825, 597, 50, 26, 0], [2, "1501", 851, 584, 50, 26, 0], [2, "1501", 877, 571, 50, 26, 0], [2, "1501", 903, 584, 50, 26, 0], [2, "1501", 877, 597, 50, 26, 0], [2, "1501", 851, 610, 50, 26, 0], [2, "1501", 877, 623, 50, 26, 0], [2, "1501", 903, 610, 50, 26, 0], [2, "1501", 929, 597, 50, 26, 0], [2, "1501", 899, 561, 50, 26, 0], [2, "1501", 925, 548, 50, 26, 0], [2, "1501", 951, 535, 50, 26, 0], [2, "1501", 977, 548, 50, 26, 0], [2, "1501", 951, 561, 50, 26, 0], [2, "1501", 925, 574, 50, 26, 0], [2, "1501", 951, 587, 50, 26, 0], [2, "1501", 977, 574, 50, 26, 0], [2, "1501", 1003, 561, 50, 26, 0], [2, "1501", 970, 520, 50, 26, 0], [2, "1501", 996, 507, 50, 26, 0], [2, "1501", 1022, 494, 50, 26, 0], [2, "1501", 1048, 507, 50, 26, 0], [2, "1501", 1022, 520, 50, 26, 0], [2, "1501", 996, 533, 50, 26, 0], [2, "1501", 1022, 546, 50, 26, 0], [2, "1501", 1048, 533, 50, 26, 0], [2, "1501", 1074, 520, 50, 26, 0], [2, "1501", 825, 597, 50, 26, 0], [2, "1501", 851, 584, 50, 26, 0], [2, "1501", 877, 571, 50, 26, 0], [2, "1501", 903, 584, 50, 26, 0], [2, "1501", 877, 597, 50, 26, 0], [2, "1501", 851, 610, 50, 26, 0], [2, "1501", 877, 623, 50, 26, 0], [2, "1501", 903, 610, 50, 26, 0], [2, "1501", 929, 597, 50, 26, 0], [2, "1501", 899, 561, 50, 26, 0], [2, "1501", 925, 548, 50, 26, 0], [2, "1501", 951, 535, 50, 26, 0], [2, "1501", 977, 548, 50, 26, 0], [2, "1501", 951, 561, 50, 26, 0], [2, "1501", 925, 574, 50, 26, 0], [2, "1501", 951, 587, 50, 26, 0], [2, "1501", 977, 574, 50, 26, 0], [2, "1501", 1003, 561, 50, 26, 0], [2, "1501", 932, 588, 50, 26, 0], [2, "1501", 958, 575, 50, 26, 0], [2, "1501", 984, 562, 50, 26, 0], [2, "1501", 1010, 575, 50, 26, 0], [2, "1501", 984, 588, 50, 26, 0], [2, "1501", 958, 601, 50, 26, 0], [2, "1501", 984, 614, 50, 26, 0], [2, "1501", 1010, 601, 50, 26, 0], [2, "1501", 1036, 588, 50, 26, 0], [2, "1501", 191, 71, 50, 26, 0], [2, "1501", 216, 83, 50, 26, 0], [2, "1501", 242, 96, 50, 26, 0], [2, "1501", 268, 109, 50, 26, 0], [2, "1501", 548, 646, 50, 26, 0], [2, "1501", 574, 633, 50, 26, 0], [2, "1501", 600, 620, 50, 26, 0], [2, "1501", 626, 633, 50, 26, 0], [2, "1501", 600, 646, 50, 26, 0], [2, "1501", 574, 659, 50, 26, 0], [2, "1501", 600, 672, 50, 26, 0], [2, "1501", 626, 659, 50, 26, 0], [2, "1501", 652, 646, 50, 26, 0], [2, "1501", 619, 635, 50, 26, 0], [2, "1501", 645, 622, 50, 26, 0], [2, "1501", 671, 609, 50, 26, 0], [2, "1501", 697, 622, 50, 26, 0], [2, "1501", 671, 635, 50, 26, 0], [2, "1501", 645, 648, 50, 26, 0], [2, "1501", 671, 661, 50, 26, 0], [2, "1501", 697, 648, 50, 26, 0], [2, "1501", 723, 635, 50, 26, 0], [2, "1501", 771, 632, 50, 26, 0], [2, "1501", 797, 619, 50, 26, 0], [2, "1501", 823, 606, 50, 26, 0], [2, "1501", 849, 619, 50, 26, 0], [2, "1501", 823, 632, 50, 26, 0], [2, "1501", 797, 645, 50, 26, 0], [2, "1501", 823, 658, 50, 26, 0], [2, "1501", 849, 645, 50, 26, 0], [2, "1501", 875, 632, 50, 26, 0], [2, "1501", 513, 642, 50, 26, 0], [2, "1501", 539, 629, 50, 26, 0], [2, "1501", 488, 655, 50, 26, 0], [2, "1501", 89, 88, 50, 26, 0], [2, "1501", 115, 75, 50, 26, 0], [2, "1501", 141, 62, 50, 26, 0], [2, "1501", 167, 75, 50, 26, 0], [2, "1501", 141, 88, 50, 26, 0], [2, "1501", 115, 101, 50, 26, 0], [2, "1501", 141, 114, 50, 26, 0], [2, "1501", 167, 101, 50, 26, 0], [2, "1501", 193, 88, 50, 26, 0], [2, "894", 57, 392, 24, 20, 2], [2, "894", 77, 382, 24, 20, 2], [2, "894", 128, 357, 24, 20, 2], [2, "894", 106, 368, 24, 20, 2], [2, "894", 99, 371, 24, 20, 2], [2, "1501", 163, 52, 50, 26, 0], [2, "1501", 189, 39, 50, 26, 0], [2, "1501", 215, 26, 50, 26, 0], [2, "1501", 241, 39, 50, 26, 0], [2, "1501", 215, 52, 50, 26, 0], [2, "1501", 189, 65, 50, 26, 0], [2, "1501", 215, 78, 50, 26, 0], [2, "1501", 241, 65, 50, 26, 0], [2, "1501", 267, 52, 50, 26, 0], [2, "1501", 241, 13, 50, 26, 0], [2, "1501", 267, 0, 50, 26, 0], [2, "1501", 293, -13, 50, 26, 0], [2, "1501", 319, 0, 50, 26, 0], [2, "1501", 293, 13, 50, 26, 0], [2, "1501", 267, 26, 50, 26, 0], [2, "1501", 293, 39, 50, 26, 0], [2, "1501", 319, 26, 50, 26, 0], [2, "1501", 345, 13, 50, 26, 0], [2, "1501", 241, 13, 50, 26, 0], [2, "1501", 267, 0, 50, 26, 0], [2, "1501", 293, -13, 50, 26, 0], [2, "1501", 319, 0, 50, 26, 0], [2, "1501", 293, 13, 50, 26, 0], [2, "1501", 267, 26, 50, 26, 0], [2, "1501", 293, 39, 50, 26, 0], [2, "1501", 319, 26, 50, 26, 0], [2, "1501", 345, 13, 50, 26, 0], [2, "1501", -57, 162, 50, 26, 0], [2, "1501", -31, 149, 50, 26, 0], [2, "1501", -5, 136, 50, 26, 0], [2, "1501", 21, 149, 50, 26, 0], [2, "1501", -5, 162, 50, 26, 0], [2, "1501", -31, 175, 50, 26, 0], [2, "1501", -5, 188, 50, 26, 0], [2, "1501", 21, 175, 50, 26, 0], [2, "1501", 47, 162, 50, 26, 0], [2, "3978", 182, 93, 50, 26, 0], [2, "3978", 157, 106, 50, 26, 0], [2, "3978", 134, 118, 50, 26, 0], [2, "3978", 109, 131, 50, 26, 0], [2, "3978", 84, 143, 50, 26, 0], [2, "3978", 59, 156, 50, 26, 0], [2, "3978", 42, 166, 50, 26, 0], [2, "3978", 17, 179, 50, 26, 0], [2, "3978", -2, 188, 50, 26, 0], [2, "3978", -27, 201, 50, 26, 0], [2, "3978", -33, 203, 50, 26, 0], [2, "3978", 393, 195, 50, 26, 0], [2, "3978", 368, 208, 50, 26, 0], [2, "3978", 345, 220, 50, 26, 0], [2, "3978", 320, 233, 50, 26, 0], [2, "3978", 295, 245, 50, 26, 0], [2, "3978", 171, 308, 50, 26, 0], [2, "3978", 196, 296, 50, 26, 0], [2, "3978", 221, 283, 50, 26, 0], [2, "3978", 244, 271, 50, 26, 0], [2, "3978", 269, 258, 50, 26, 0], [2, "3978", 50, 370, 50, 26, 0], [2, "3978", 75, 358, 50, 26, 0], [2, "3978", 100, 345, 50, 26, 0], [2, "3978", 123, 333, 50, 26, 0], [2, "3978", 148, 320, 50, 26, 0], [2, "3978", -35, 414, 50, 26, 0], [2, "3978", -10, 401, 50, 26, 0], [2, "3978", 13, 389, 50, 26, 0], [2, "3978", 38, 376, 50, 26, 0], [2, "3978", 800, 497, 50, 26, 0], [2, "3978", 825, 485, 50, 26, 0], [2, "3978", 850, 472, 50, 26, 0], [2, "3978", 873, 460, 50, 26, 0], [2, "3978", 898, 447, 50, 26, 0], [2, "3978", 677, 560, 50, 26, 0], [2, "3978", 702, 548, 50, 26, 0], [2, "3978", 727, 535, 50, 26, 0], [2, "3978", 750, 523, 50, 26, 0], [2, "3978", 775, 510, 50, 26, 0], [2, "3978", 560, 619, 50, 26, 0], [2, "3978", 585, 607, 50, 26, 0], [2, "3978", 610, 594, 50, 26, 0], [2, "3978", 633, 582, 50, 26, 0], [2, "3978", 658, 569, 50, 26, 0], [2, "3978", 481, 658, 50, 26, 0], [2, "3978", 506, 646, 50, 26, 0], [2, "3978", 531, 633, 50, 26, 0], [2, "3978", 554, 621, 50, 26, 0], [2, "3978", 579, 608, 50, 26, 0], [2, "3978", 208, 80, 50, 26, 2], [2, "3978", 232, 92, 50, 26, 2], [2, "3978", 259, 104, 50, 26, 2], [2, "3978", 285, 117, 50, 26, 2], [2, "3978", 337, 142, 50, 26, 2], [2, "3978", 311, 129, 50, 26, 2], [2, "3978", 387, 168, 50, 26, 2], [2, "3978", 361, 155, 50, 26, 2], [2, "3978", 436, 192, 50, 26, 2], [2, "3978", 410, 179, 50, 26, 2], [2, "3978", 486, 218, 50, 26, 2], [2, "3978", 460, 205, 50, 26, 2], [2, "3978", 531, 240, 50, 26, 2], [2, "3978", 505, 227, 50, 26, 2], [2, "3978", 581, 266, 50, 26, 2], [2, "3978", 555, 253, 50, 26, 2], [2, "3978", 631, 290, 50, 26, 2], [2, "3978", 605, 277, 50, 26, 2], [2, "3978", 678, 313, 50, 26, 2], [2, "3978", 652, 300, 50, 26, 2], [2, "3978", 724, 336, 50, 26, 2], [2, "3978", 698, 323, 50, 26, 2], [2, "3978", 768, 358, 50, 26, 2], [2, "3978", 742, 345, 50, 26, 2], [2, "3978", 816, 382, 50, 26, 2], [2, "3978", 790, 369, 50, 26, 2], [2, "3978", 864, 406, 50, 26, 2], [2, "3978", 838, 393, 50, 26, 2], [2, "3978", 911, 428, 50, 26, 2], [2, "3978", 885, 416, 50, 26, 2], [2, "3978", 924, 435, 50, 26, 2], [2, "3984", -24, 625, 24, 24, 0], [2, "3984", -48, 625, 24, 24, 0], [2, "3984", -48, 601, 24, 24, 0], [2, "3984", -24, 601, 24, 24, 0], [2, "3984", -48, 553, 24, 24, 0], [2, "3984", -24, 553, 24, 24, 0], [2, "3984", -24, 577, 24, 24, 0], [2, "3984", -48, 577, 24, 24, 0], [2, "3984", -48, 457, 24, 24, 0], [2, "3984", -24, 457, 24, 24, 0], [2, "3984", -24, 481, 24, 24, 0], [2, "3984", -48, 481, 24, 24, 0], [2, "3984", -48, 505, 24, 24, 0], [2, "3984", -24, 505, 24, 24, 0], [2, "3984", -24, 529, 24, 24, 0], [2, "3984", -48, 529, 24, 24, 0], [2, "3984", -48, 361, 24, 24, 0], [2, "3984", -24, 361, 24, 24, 0], [2, "3984", -24, 385, 24, 24, 0], [2, "3984", -48, 385, 24, 24, 0], [2, "3984", -48, 409, 24, 24, 0], [2, "3984", -24, 409, 24, 24, 0], [2, "3984", -24, 433, 24, 24, 0], [2, "3984", -48, 433, 24, 24, 0], [2, "3984", -48, 265, 24, 24, 0], [2, "3984", -24, 265, 24, 24, 0], [2, "3984", -24, 289, 24, 24, 0], [2, "3984", -48, 289, 24, 24, 0], [2, "3984", -48, 313, 24, 24, 0], [2, "3984", -24, 313, 24, 24, 0], [2, "3984", -24, 337, 24, 24, 0], [2, "3984", -48, 337, 24, 24, 0], [2, "3984", -48, 169, 24, 24, 0], [2, "3984", -24, 169, 24, 24, 0], [2, "3984", -24, 193, 24, 24, 0], [2, "3984", -48, 193, 24, 24, 0], [2, "3984", -48, 217, 24, 24, 0], [2, "3984", -24, 217, 24, 24, 0], [2, "3984", -24, 241, 24, 24, 0], [2, "3984", -48, 241, 24, 24, 0], [2, "3984", -48, 649, 24, 24, 0], [2, "3984", -24, 649, 24, 24, 0], [2, "3984", -24, 673, 24, 24, 0], [2, "3984", -48, 673, 24, 24, 0], [2, "3984", 0, 649, 24, 24, 0], [2, "3984", 24, 649, 24, 24, 0], [2, "3984", 24, 673, 24, 24, 0], [2, "3984", 0, 673, 24, 24, 0], [2, "3984", 96, 649, 24, 24, 0], [2, "3984", 120, 649, 24, 24, 0], [2, "3984", 120, 673, 24, 24, 0], [2, "3984", 96, 673, 24, 24, 0], [2, "3984", 48, 649, 24, 24, 0], [2, "3984", 72, 649, 24, 24, 0], [2, "3984", 72, 673, 24, 24, 0], [2, "3984", 48, 673, 24, 24, 0], [2, "3984", 288, 649, 24, 24, 0], [2, "3984", 312, 649, 24, 24, 0], [2, "3984", 312, 673, 24, 24, 0], [2, "3984", 288, 673, 24, 24, 0], [2, "3984", 240, 649, 24, 24, 0], [2, "3984", 264, 649, 24, 24, 0], [2, "3984", 264, 673, 24, 24, 0], [2, "3984", 240, 673, 24, 24, 0], [2, "3984", 192, 649, 24, 24, 0], [2, "3984", 216, 649, 24, 24, 0], [2, "3984", 216, 673, 24, 24, 0], [2, "3984", 192, 673, 24, 24, 0], [2, "3984", 144, 649, 24, 24, 0], [2, "3984", 168, 649, 24, 24, 0], [2, "3984", 168, 673, 24, 24, 0], [2, "3984", 144, 673, 24, 24, 0], [2, "3984", 480, 649, 24, 24, 0], [2, "3984", 504, 649, 24, 24, 0], [2, "3984", 504, 673, 24, 24, 0], [2, "3984", 480, 673, 24, 24, 0], [2, "3984", 432, 649, 24, 24, 0], [2, "3984", 456, 649, 24, 24, 0], [2, "3984", 456, 673, 24, 24, 0], [2, "3984", 432, 673, 24, 24, 0], [2, "3984", 384, 649, 24, 24, 0], [2, "3984", 408, 649, 24, 24, 0], [2, "3984", 408, 673, 24, 24, 0], [2, "3984", 384, 673, 24, 24, 0], [2, "3984", 336, 649, 24, 24, 0], [2, "3984", 360, 649, 24, 24, 0], [2, "3984", 360, 673, 24, 24, 0], [2, "3984", 336, 673, 24, 24, 0], [2, "3984", 672, 649, 24, 24, 0], [2, "3984", 696, 649, 24, 24, 0], [2, "3984", 696, 673, 24, 24, 0], [2, "3984", 672, 673, 24, 24, 0], [2, "3984", 624, 649, 24, 24, 0], [2, "3984", 648, 649, 24, 24, 0], [2, "3984", 648, 673, 24, 24, 0], [2, "3984", 624, 673, 24, 24, 0], [2, "3984", 576, 649, 24, 24, 0], [2, "3984", 600, 649, 24, 24, 0], [2, "3984", 600, 673, 24, 24, 0], [2, "3984", 576, 673, 24, 24, 0], [2, "3984", 528, 649, 24, 24, 0], [2, "3984", 552, 649, 24, 24, 0], [2, "3984", 552, 673, 24, 24, 0], [2, "3984", 528, 673, 24, 24, 0], [2, "894", 580, 278, 24, 20, 0], [2, "894", 560, 267, 24, 20, 0], [2, "894", 597, 287, 24, 20, 0], [2, "894", 621, 299, 24, 20, 0]]}, {"type": 4, "obj": [[2, "3567", 491, 27, 50, 39, 0], [4, 7, 442, 106, 1, 4005], [2, "3559", 462, 77, 52, 43, 0], [2, "3568", 544, 82, 48, 38, 0], [2, "3565", 352, 88, 52, 43, 0], [4, 2, 873, 146, 0, 4022], [2, "1231", 608, -14, 114, 162, 0], [2, "1456", 678, 136, 24, 32, 0], [2, "3987", 239, 133, 42, 42, 0], [4, 3, 913, 179, 0, 4022], [2, "3565", 400, 146, 52, 43, 0], [2, "1457", 739, 165, 22, 30, 0], [2, "1231", 710, 37, 114, 162, 0], [2, "3550", 189, 155, 26, 47, 0], [2, "3127", 778, 175, 30, 30, 0], [2, "3987", 346, 185, 42, 42, 0], [2, "3562", 284, 198, 26, 35, 0], [2, "3562", 115, 201, 26, 35, 0], [4, 1, 804, 237, 0, 4020], [4, 6, 850, 256, 1, 4020], [4, 6, 850, 256, 1, 4020], [4, 6, 850, 256, 1, 4020], [4, 6, 850, 256, 1, 4020], [2, "1456", 927, 227, 24, 32, 0], [2, "24", 948, 232, 28, 38, 0], [4, 5, 727, 276, 0, 4011], [2, "1457", 906, 246, 22, 30, 0], [2, "3550", 34, 231, 26, 47, 0], [2, "24", 920, 247, 28, 38, 0], [4, 8, 780, 287, 1, 4011], [2, "24", 891, 263, 28, 38, 0], [2, "1457", 829, 285, 22, 30, 0], [2, "1457", 829, 285, 22, 30, 0], [2, "3501", 851, 249, 54, 73, 2], [2, "3987", 296, 288, 42, 42, 0], [2, "1456", 804, 299, 24, 32, 0], [2, "24", 834, 294, 28, 38, 0], [2, "1457", 784, 304, 22, 30, 0], [2, "879", 572, 290, 26, 56, 2], [2, "24", 806, 309, 28, 38, 0], [2, "3559", 729, 305, 52, 43, 0], [2, "24", 777, 325, 28, 38, 0], [2, "3566", 511, 289, 38, 91, 0], [2, "3987", 181, 348, 42, 42, 0], [2, "3987", 181, 348, 42, 42, 0], [2, "3536", 7, 363, 40, 48, 0], [2, "3573", 253, 394, 40, 33, 0], [2, "3566", 619, 343, 38, 91, 0], [2, "72", 338, 405, 42, 44, 0], [2, "3987", 802, 415, 42, 42, 0], [2, "3566", 167, 371, 38, 91, 0], [2, "72", 302, 419, 42, 44, 0], [2, "3987", 3, 435, 42, 42, 0], [2, "3578", 580, 437, 56, 66, 0], [2, "3578", 481, 444, 56, 66, 2], [2, "3566", 34, 431, 38, 91, 0], [2, "3583", 615, 478, 58, 44, 0], [2, "3583", 445, 487, 58, 44, 0], [2, "3578", 650, 476, 56, 66, 0], [2, "3578", 409, 488, 56, 66, 2], [2, "3536", 373, 516, 40, 48, 0], [2, "3188", 526, 527, 60, 40, 0], [2, "3536", 401, 536, 40, 48, 0], [4, 4, 314, 605, 0, 4023], [2, "3536", 7, 562, 40, 48, 0], [2, "3536", 36, 579, 40, 48, 0], [2, "3536", 477, 579, 40, 48, 0], [2, "3536", 506, 595, 40, 48, 0]]}, {"type": 3, "obj": [[2, "3490", 291, 141, 46, 61, 0], [2, "3490", 470, 229, 46, 61, 0], [2, "894", 478, 228, 24, 20, 0], [2, "894", 417, 210, 24, 20, 2], [2, "894", 440, 209, 24, 20, 0], [2, "1207", 434, 229, 22, 81, 0], [2, "1207", 434, 194, 22, 81, 0], [2, "884", 306, 335, 24, 25, 2], [2, "884", 286, 345, 24, 25, 2], [2, "884", 267, 354, 24, 25, 2], [2, "884", 247, 364, 24, 25, 2], [2, "884", 231, 373, 24, 25, 2], [2, "884", 211, 383, 24, 25, 2], [2, "884", 192, 393, 24, 25, 2], [2, "884", 172, 403, 24, 25, 2], [2, "884", 16, 480, 24, 25, 2], [2, "884", -4, 490, 24, 25, 2], [2, "894", 190, 192, 24, 20, 2], [2, "894", 119, 228, 24, 20, 2], [2, "894", 96, 240, 24, 20, 2], [2, "894", 71, 252, 24, 20, 2], [2, "884", 206, 177, 24, 25, 2], [2, "884", 186, 187, 24, 25, 2], [2, "884", 168, 195, 24, 25, 2], [2, "884", 148, 205, 24, 25, 2], [2, "884", 128, 215, 24, 25, 2], [2, "884", 108, 225, 24, 25, 2], [2, "884", 90, 233, 24, 25, 2], [2, "884", 70, 243, 24, 25, 2], [2, "884", 52, 252, 24, 25, 2], [2, "884", 32, 262, 24, 25, 2], [2, "884", 14, 270, 24, 25, 2], [2, "884", -6, 280, 24, 25, 2], [2, "895", 525, 301, 8, 31, 0], [2, "895", 525, 274, 8, 31, 0], [2, "895", 525, 247, 8, 31, 0], [2, "895", 569, 321, 8, 31, 0], [2, "895", 569, 294, 8, 31, 0], [2, "895", 569, 268, 8, 31, 0], [2, "894", 525, 247, 24, 20, 0], [2, "894", 548, 259, 24, 20, 0], [2, "894", 523, 323, 24, 20, 0], [2, "894", 546, 334, 24, 20, 0], [2, "894", 500, 236, 24, 20, 0], [2, "894", 476, 300, 24, 20, 0], [2, "894", 498, 311, 24, 20, 0], [2, "895", 433, 265, 8, 31, 2], [2, "895", 417, 219, 8, 31, 2], [2, "895", 417, 246, 8, 31, 2], [2, "895", 417, 273, 8, 31, 2], [2, "894", 393, 223, 24, 20, 2], [2, "894", 417, 285, 24, 20, 2], [2, "894", 394, 297, 24, 20, 2], [2, "894", 368, 236, 24, 20, 2], [2, "895", 289, 285, 8, 31, 2], [2, "895", 289, 312, 8, 31, 2], [2, "895", 289, 323, 8, 31, 2], [2, "894", 272, 285, 24, 20, 2], [2, "894", 249, 297, 24, 20, 2], [2, "894", 273, 359, 24, 20, 2], [2, "895", 181, 130, 8, 31, 2], [2, "895", 181, 157, 8, 31, 2], [2, "895", 181, 184, 8, 31, 2], [2, "894", 164, 130, 24, 20, 2], [2, "894", 141, 142, 24, 20, 2], [2, "895", 437, 258, 8, 31, 0], [2, "894", 452, 212, 24, 20, 0], [2, "894", 442, 284, 24, 20, 0], [2, "894", 453, 289, 24, 20, 0], [2, "895", 341, 210, 8, 31, 0], [2, "895", 341, 183, 8, 31, 0], [2, "895", 341, 156, 8, 31, 0], [2, "894", 333, 152, 24, 20, 0], [2, "894", 356, 164, 24, 20, 0], [2, "894", 332, 227, 24, 20, 0], [2, "895", 345, 256, 8, 31, 2], [2, "895", 345, 283, 8, 31, 2], [2, "895", 345, 310, 8, 31, 2], [2, "894", 345, 248, 24, 20, 2], [2, "894", 346, 322, 24, 20, 2], [2, "895", 337, 260, 8, 31, 2], [2, "895", 337, 287, 8, 31, 2], [2, "895", 337, 314, 8, 31, 2], [2, "895", 329, 264, 8, 31, 2], [2, "895", 329, 291, 8, 31, 2], [2, "895", 329, 318, 8, 31, 2], [2, "895", 321, 268, 8, 31, 2], [2, "895", 321, 295, 8, 31, 2], [2, "895", 321, 322, 8, 31, 2], [2, "894", 320, 260, 24, 20, 2], [2, "894", 297, 272, 24, 20, 2], [2, "894", 321, 334, 24, 20, 2], [2, "894", 298, 346, 24, 20, 2], [2, "895", 215, 324, 8, 31, 2], [2, "895", 215, 351, 8, 31, 2], [2, "895", 215, 361, 8, 31, 2], [2, "894", 224, 310, 24, 20, 2], [2, "894", 201, 322, 24, 20, 2], [2, "895", 169, 347, 8, 31, 2], [2, "895", 169, 374, 8, 31, 2], [2, "895", 169, 401, 8, 31, 2], [2, "895", 161, 351, 8, 31, 2], [2, "895", 161, 378, 8, 31, 2], [2, "895", 161, 405, 8, 31, 2], [2, "895", 153, 355, 8, 31, 2], [2, "895", 153, 382, 8, 31, 2], [2, "895", 153, 409, 8, 31, 2], [2, "894", 176, 335, 24, 20, 2], [2, "894", 153, 347, 24, 20, 2], [2, "894", 154, 421, 24, 20, 2], [2, "895", 45, 407, 8, 31, 2], [2, "895", 45, 434, 8, 31, 2], [2, "895", 45, 461, 8, 31, 2], [2, "895", 37, 411, 8, 31, 2], [2, "895", 37, 438, 8, 31, 2], [2, "895", 37, 465, 8, 31, 2], [2, "894", 28, 407, 24, 20, 2], [2, "894", 5, 419, 24, 20, 2], [2, "895", -11, 436, 8, 31, 2], [2, "895", -11, 463, 8, 31, 2], [2, "895", -11, 490, 8, 31, 2], [2, "894", -20, 432, 24, 20, 2], [2, "895", 245, 163, 8, 31, 0], [2, "895", 245, 136, 8, 31, 0], [2, "895", 245, 109, 8, 31, 0], [2, "894", 237, 105, 24, 20, 0], [2, "894", 260, 117, 24, 20, 0], [2, "894", 236, 179, 24, 20, 0], [2, "894", 259, 190, 24, 20, 0], [2, "894", 644, 310, 24, 20, 0], [2, "894", 643, 385, 24, 20, 0], [2, "895", 645, 369, 8, 31, 0], [2, "895", 645, 342, 8, 31, 0], [2, "895", 645, 315, 8, 31, 0], [2, "895", 670, 357, 8, 31, 0], [2, "895", 670, 330, 8, 31, 0], [2, "894", 669, 322, 24, 20, 0], [2, "894", 692, 334, 24, 20, 0], [2, "894", 668, 397, 24, 20, 0], [2, "894", 690, 407, 24, 20, 0], [2, "894", 717, 345, 24, 20, 0], [2, "894", 740, 357, 24, 20, 0], [2, "894", 716, 420, 24, 20, 0], [2, "894", 738, 431, 24, 20, 0], [2, "895", 805, 443, 8, 31, 0], [2, "895", 805, 416, 8, 31, 0], [2, "895", 805, 389, 8, 31, 0], [2, "894", 765, 369, 24, 20, 0], [2, "894", 788, 381, 24, 20, 0], [2, "894", 786, 456, 24, 20, 0], [2, "895", 829, 455, 8, 31, 0], [2, "895", 837, 459, 8, 31, 0], [2, "895", 845, 463, 8, 31, 0], [2, "895", 845, 436, 8, 31, 0], [2, "895", 845, 409, 8, 31, 0], [2, "895", 853, 467, 8, 31, 0], [2, "895", 853, 440, 8, 31, 0], [2, "895", 853, 413, 8, 31, 0], [2, "894", 813, 393, 24, 20, 0], [2, "894", 835, 444, 24, 20, 0], [2, "894", 812, 468, 24, 20, 0], [2, "894", 834, 479, 24, 20, 0], [2, "895", 861, 471, 8, 31, 0], [2, "895", 861, 444, 8, 31, 0], [2, "895", 861, 417, 8, 31, 0], [2, "895", 869, 475, 8, 31, 0], [2, "895", 869, 448, 8, 31, 0], [2, "895", 869, 421, 8, 31, 0], [2, "895", 877, 479, 8, 31, 0], [2, "895", 877, 452, 8, 31, 0], [2, "895", 877, 425, 8, 31, 0], [2, "895", 885, 483, 8, 31, 0], [2, "895", 893, 487, 8, 31, 0], [2, "895", 893, 460, 8, 31, 0], [2, "895", 901, 491, 8, 31, 0], [2, "895", 901, 464, 8, 31, 0], [2, "894", 861, 417, 24, 20, 0], [2, "894", 884, 429, 24, 20, 0], [2, "894", 860, 456, 24, 20, 0], [2, "894", 883, 468, 24, 20, 0], [2, "894", 860, 492, 24, 20, 0], [2, "894", 882, 503, 24, 20, 0], [2, "894", 212, 106, 24, 20, 2], [2, "894", 189, 118, 24, 20, 2], [2, "894", 118, 154, 24, 20, 2], [2, "894", 95, 166, 24, 20, 2], [2, "895", 115, 167, 8, 31, 2], [2, "895", 115, 194, 8, 31, 2], [2, "894", 70, 178, 24, 20, 2], [2, "894", 47, 190, 24, 20, 2], [2, "894", 23, 201, 24, 20, 2], [2, "894", 0, 213, 24, 20, 2], [2, "894", 454, 216, 24, 20, 0], [2, "894", 503, 240, 24, 20, 0], [2, "894", 527, 252, 24, 20, 0], [2, "894", 536, 257, 24, 20, 0], [2, "894", 244, 111, 24, 20, 0], [2, "894", 268, 123, 24, 20, 0], [2, "894", 293, 135, 24, 20, 0], [2, "894", 318, 148, 24, 20, 0], [2, "894", 343, 160, 24, 20, 0], [2, "894", 389, 183, 24, 20, 0], [2, "894", 646, 311, 24, 20, 0], [2, "894", 670, 323, 24, 20, 0], [2, "894", 793, 384, 24, 20, 0], [2, "894", 817, 396, 24, 20, 0], [2, "894", 842, 409, 24, 20, 0], [2, "894", 866, 421, 24, 20, 0], [2, "894", 916, 446, 24, 20, 0], [2, "894", 203, 109, 24, 20, 2], [2, "894", 157, 134, 24, 20, 2], [2, "894", 133, 146, 24, 20, 2], [2, "894", 109, 158, 24, 20, 2], [2, "894", 85, 170, 24, 20, 2], [2, "894", 63, 181, 24, 20, 2], [2, "894", 39, 193, 24, 20, 2], [2, "894", 16, 205, 24, 20, 2], [2, "894", -6, 216, 24, 20, 2], [2, "894", -30, 228, 24, 20, 2], [2, "894", 391, 223, 24, 20, 2], [2, "894", 366, 236, 24, 20, 2], [2, "894", 345, 246, 24, 20, 2], [2, "894", 321, 259, 24, 20, 2], [2, "894", 297, 271, 24, 20, 2], [2, "894", 275, 282, 24, 20, 2], [2, "894", 273, 284, 24, 20, 2], [2, "894", 232, 304, 24, 20, 2], [2, "894", 208, 316, 24, 20, 2], [2, "894", 186, 327, 24, 20, 2], [2, "894", 193, 324, 24, 20, 2], [2, "894", 169, 336, 24, 20, 2], [2, "894", 146, 347, 24, 20, 2], [2, "894", 20, 412, 24, 20, 2], [2, "894", -4, 424, 24, 20, 2], [2, "894", -26, 435, 24, 20, 2], [2, "895", 181, 158, 8, 31, 2], [2, "895", 181, 132, 8, 31, 2], [2, "894", 236, 107, 24, 20, 0], [2, "894", 252, 294, 24, 20, 2], [2, "895", 909, 494, 8, 31, 0], [2, "895", 909, 467, 8, 31, 0], [2, "895", 909, 440, 8, 31, 0], [2, "895", 917, 498, 8, 31, 0], [2, "895", 917, 471, 8, 31, 0], [2, "895", 917, 444, 8, 31, 0], [2, "895", 925, 502, 8, 31, 0], [2, "895", 925, 475, 8, 31, 0], [2, "895", 925, 448, 8, 31, 0], [2, "895", 933, 506, 8, 31, 0], [2, "895", 933, 479, 8, 31, 0], [2, "895", 933, 452, 8, 31, 0], [2, "895", 941, 510, 8, 31, 0], [2, "895", 941, 483, 8, 31, 0], [2, "895", 941, 456, 8, 31, 0], [2, "895", 949, 514, 8, 31, 0], [2, "895", 949, 487, 8, 31, 0], [2, "895", 949, 460, 8, 31, 0], [2, "894", 909, 440, 24, 20, 0], [2, "894", 932, 452, 24, 20, 0], [2, "894", 908, 479, 24, 20, 0], [2, "894", 931, 491, 24, 20, 0], [2, "894", 908, 515, 24, 20, 0], [2, "894", 930, 526, 24, 20, 0], [2, "895", 957, 518, 8, 31, 0], [2, "895", 957, 491, 8, 31, 0], [2, "895", 957, 464, 8, 31, 0], [2, "895", 965, 522, 8, 31, 0], [2, "895", 965, 495, 8, 31, 0], [2, "895", 965, 468, 8, 31, 0], [2, "895", 973, 526, 8, 31, 0], [2, "895", 973, 499, 8, 31, 0], [2, "895", 973, 472, 8, 31, 0], [2, "895", 981, 530, 8, 31, 0], [2, "895", 981, 503, 8, 31, 0], [2, "895", 981, 476, 8, 31, 0], [2, "895", 989, 534, 8, 31, 0], [2, "895", 989, 507, 8, 31, 0], [2, "895", 989, 480, 8, 31, 0], [2, "895", 997, 538, 8, 31, 0], [2, "895", 997, 511, 8, 31, 0], [2, "895", 997, 484, 8, 31, 0], [2, "894", 957, 464, 24, 20, 0], [2, "894", 980, 476, 24, 20, 0], [2, "894", 956, 503, 24, 20, 0], [2, "894", 979, 515, 24, 20, 0], [2, "894", 956, 539, 24, 20, 0], [2, "894", 978, 550, 24, 20, 0], [2, "895", 285, 183, 8, 31, 0], [2, "895", 285, 156, 8, 31, 0], [2, "895", 285, 129, 8, 31, 0], [2, "894", 285, 129, 24, 20, 0], [2, "894", 308, 141, 24, 20, 0], [2, "894", 365, 171, 24, 20, 0], [2, "3036", 744, 507, 70, 54, 2], [2, "894", 762, 444, 24, 20, 0], [2, "884", 447, 277, 24, 25, 0], [2, "884", 467, 287, 24, 25, 0], [2, "884", 486, 295, 24, 25, 0], [2, "884", 506, 305, 24, 25, 0], [2, "884", 447, 277, 24, 25, 0], [2, "884", 467, 287, 24, 25, 0], [2, "884", 529, 317, 24, 25, 0], [2, "884", 549, 327, 24, 25, 0], [2, "884", 652, 378, 24, 25, 0], [2, "884", 672, 388, 24, 25, 0], [2, "884", 732, 418, 24, 25, 0], [2, "884", 752, 428, 24, 25, 0], [2, "884", 690, 398, 24, 25, 0], [2, "884", 710, 408, 24, 25, 0], [2, "884", 808, 454, 24, 25, 0], [2, "884", 828, 464, 24, 25, 0], [2, "884", 772, 439, 24, 25, 0], [2, "884", 792, 449, 24, 25, 0], [2, "884", 247, 175, 24, 25, 0], [2, "884", 267, 185, 24, 25, 0], [2, "884", 327, 214, 24, 25, 0], [2, "884", 347, 224, 24, 25, 0], [2, "673", 270, 357, 80, 63, 0], [2, "894", 179, 122, 24, 20, 2], [2, "894", 717, 346, 24, 20, 0], [2, "894", 410, 194, 24, 20, 0], [2, "22", 608, 184, 62, 38, 0], [2, "21", 582, 167, 28, 24, 0], [2, "21", 669, 213, 28, 24, 0], [2, "884", 286, 194, 24, 25, 0], [2, "884", 306, 204, 24, 25, 0], [2, "3528", 292, 343, 14, 31, 0], [2, "673", 282, 393, 80, 63, 2], [2, "3359", 431, 270, 32, 43, 0], [2, "123", 826, 186, 58, 42, 2], [2, "895", 115, 202, 8, 31, 2], [2, "3527", 307, 359, 20, 28, 0], [2, "3520", 329, 383, 24, 20, 0], [2, "679", 430, 253, 36, 32, 0], [2, "3554", 188, 346, 20, 31, 2], [2, "3554", 303, 286, 20, 31, 2], [2, "3554", 255, 131, 20, 31, 0], [2, "3554", 361, 183, 20, 31, 0], [2, "3554", 10, 434, 20, 31, 2], [2, "123", 868, 208, 58, 42, 2], [2, "3094", 636, 130, 58, 34, 0], [2, "1457", 709, 119, 22, 30, 0], [2, "3525", 627, 178, 24, 23, 0], [2, "3745", 368, 290, 78, 60, 2], [2, "3521", 392, 287, 32, 30, 0], [2, "3359", 456, 285, 32, 43, 0], [2, "884", 223, 168, 24, 25, 2], [2, "884", 203, 178, 24, 25, 2], [2, "884", 238, 169, 24, 25, 0], [2, "884", 258, 179, 24, 25, 0], [2, "1207", 228, 124, 22, 81, 0], [2, "1207", 228, 53, 22, 81, 0], [2, "1207", 325, 281, 22, 81, 0], [2, "1207", 325, 246, 22, 81, 0], [2, "1207", 156, 370, 22, 81, 0], [2, "1207", 156, 335, 22, 81, 0], [2, "1207", 37, 428, 22, 81, 0], [2, "1207", 37, 393, 22, 81, 0], [2, "1207", 541, 282, 22, 81, 0], [2, "1207", 541, 247, 22, 81, 0], [2, "3036", 543, 341, 70, 54, 2], [2, "3036", 584, 363, 70, 54, 2], [2, "1207", 646, 335, 22, 81, 0], [2, "1207", 646, 300, 22, 81, 0], [2, "3474", 182, 185, 44, 47, 2], [2, "3592", 139, 187, 88, 80, 2], [2, "3474", 108, 219, 44, 47, 2], [2, "3594", 63, 230, 94, 79, 2], [2, "3474", 27, 262, 44, 47, 2], [2, "3643", 280, 123, 56, 101, 0], [2, "895", 15, 249, 8, 31, 2], [2, "895", 15, 241, 8, 31, 2], [2, "895", 15, 214, 8, 31, 2], [2, "3601", 61, 196, 26, 39, 2], [2, "3602", 92, 186, 22, 29, 2], [2, "3604", 138, 161, 26, 26, 2], [2, "3603", 238, 301, 50, 54, 2], [2, "3650", 727, 369, 50, 94, 0], [2, "3650", 764, 387, 50, 94, 0], [2, "3554", 816, 413, 20, 31, 0], [2, "3574", 249, 178, 40, 45, 0], [2, "3574", 312, 207, 40, 45, 0], [2, "3361", 464, 279, 18, 17, 0], [2, "3485", 327, 319, 58, 54, 2], [2, "3574", 663, 393, 40, 45, 0], [2, "3562", 701, 410, 26, 35, 0], [2, "3490", 682, 335, 46, 61, 0], [2, "3490", -35, 230, 46, 61, 2], [2, "3530", 480, 303, 40, 47, 0]]}, {"type": 3, "obj": [[2, "24", 283, 101, 28, 38, 0], [2, "3735", 573, 340, 82, 45, 0], [2, "3025", 604, 304, 92, 53, 0], [2, "1501", -77, 182, 50, 26, 0], [2, "1501", -51, 169, 50, 26, 0], [2, "1501", -25, 156, 50, 26, 0], [2, "1501", 1, 169, 50, 26, 0], [2, "1501", -25, 182, 50, 26, 0], [2, "1501", -51, 195, 50, 26, 0], [2, "1501", -25, 208, 50, 26, 0], [2, "1501", 1, 195, 50, 26, 0], [2, "1501", 27, 182, 50, 26, 0], [2, "1501", -8, 141, 50, 26, 0], [2, "1501", 18, 128, 50, 26, 0], [2, "1501", 44, 115, 50, 26, 0], [2, "1501", 70, 128, 50, 26, 0], [2, "1501", 44, 141, 50, 26, 0], [2, "1501", 18, 154, 50, 26, 0], [2, "1501", 44, 167, 50, 26, 0], [2, "1501", 70, 154, 50, 26, 0], [2, "1501", 96, 141, 50, 26, 0], [2, "1501", -49, 130, 50, 26, 0], [2, "1501", -23, 117, 50, 26, 0], [2, "1501", 3, 104, 50, 26, 0], [2, "1501", 29, 117, 50, 26, 0], [2, "1501", 3, 130, 50, 26, 0], [2, "1501", -23, 143, 50, 26, 0], [2, "1501", 3, 156, 50, 26, 0], [2, "1501", 29, 143, 50, 26, 0], [2, "1501", 55, 130, 50, 26, 0], [2, "1501", 63, 114, 50, 26, 0], [2, "1501", 89, 101, 50, 26, 0], [2, "1501", 115, 88, 50, 26, 0], [2, "1501", 141, 101, 50, 26, 0], [2, "1501", 115, 114, 50, 26, 0], [2, "1501", 89, 127, 50, 26, 0], [2, "1501", 115, 140, 50, 26, 0], [2, "1501", 141, 127, 50, 26, 0], [2, "1501", 167, 114, 50, 26, 0], [2, "1501", 880, 620, 50, 26, 0], [2, "1501", 906, 607, 50, 26, 0], [2, "1501", 932, 594, 50, 26, 0], [2, "1501", 958, 607, 50, 26, 0], [2, "1501", 932, 620, 50, 26, 0], [2, "1501", 906, 633, 50, 26, 0], [2, "1501", 932, 646, 50, 26, 0], [2, "1501", 958, 633, 50, 26, 0], [2, "1501", 984, 620, 50, 26, 0], [2, "1501", 180, -3, 50, 26, 0], [2, "1501", 206, -16, 50, 26, 0], [2, "1501", 232, -29, 50, 26, 0], [2, "1501", 258, -16, 50, 26, 0], [2, "1501", 232, -3, 50, 26, 0], [2, "1501", 206, 10, 50, 26, 0], [2, "1501", 232, 23, 50, 26, 0], [2, "1501", 258, 10, 50, 26, 0], [2, "1501", 284, -3, 50, 26, 0], [2, "1501", 112, 19, 50, 26, 0], [2, "1501", 138, 6, 50, 26, 0], [2, "1501", 164, -7, 50, 26, 0], [2, "1501", 190, 6, 50, 26, 0], [2, "1501", 164, 19, 50, 26, 0], [2, "1501", 138, 32, 50, 26, 0], [2, "1501", 164, 45, 50, 26, 0], [2, "1501", 190, 32, 50, 26, 0], [2, "1501", 216, 19, 50, 26, 0], [2, "1501", 29, 51, 50, 26, 0], [2, "1501", 55, 38, 50, 26, 0], [2, "1501", 81, 25, 50, 26, 0], [2, "1501", 107, 38, 50, 26, 0], [2, "1501", 81, 51, 50, 26, 0], [2, "1501", 55, 64, 50, 26, 0], [2, "1501", 81, 77, 50, 26, 0], [2, "1501", 107, 64, 50, 26, 0], [2, "1501", 133, 51, 50, 26, 0], [2, "1501", -36, 90, 50, 26, 0], [2, "1501", -10, 77, 50, 26, 0], [2, "1501", 16, 64, 50, 26, 0], [2, "1501", 42, 77, 50, 26, 0], [2, "1501", 16, 90, 50, 26, 0], [2, "1501", -10, 103, 50, 26, 0], [2, "1501", 16, 116, 50, 26, 0], [2, "1501", 42, 103, 50, 26, 0], [2, "1501", 68, 90, 50, 26, 0], [2, "1501", -56, 43, 50, 26, 0], [2, "1501", -30, 30, 50, 26, 0], [2, "1501", -4, 17, 50, 26, 0], [2, "1501", 22, 30, 50, 26, 0], [2, "1501", -4, 43, 50, 26, 0], [2, "1501", -30, 56, 50, 26, 0], [2, "1501", -4, 69, 50, 26, 0], [2, "1501", 22, 56, 50, 26, 0], [2, "1501", 48, 43, 50, 26, 0], [2, "1501", -65, 21, 50, 26, 0], [2, "1501", -39, 8, 50, 26, 0], [2, "1501", -13, -5, 50, 26, 0], [2, "1501", 13, 8, 50, 26, 0], [2, "1501", -13, 21, 50, 26, 0], [2, "1501", -39, 34, 50, 26, 0], [2, "1501", -13, 47, 50, 26, 0], [2, "1501", 13, 34, 50, 26, 0], [2, "1501", 39, 21, 50, 26, 0], [2, "1501", 23, -11, 50, 26, 0], [2, "1501", 49, -24, 50, 26, 0], [2, "1501", 75, -37, 50, 26, 0], [2, "1501", 101, -24, 50, 26, 0], [2, "1501", 75, -11, 50, 26, 0], [2, "1501", 49, 2, 50, 26, 0], [2, "1501", 75, 15, 50, 26, 0], [2, "1501", 101, 2, 50, 26, 0], [2, "1501", 127, -11, 50, 26, 0], [2, "1501", -3, -11, 50, 26, 0], [2, "1501", 23, -24, 50, 26, 0], [2, "1501", 49, -37, 50, 26, 0], [2, "1501", 75, -24, 50, 26, 0], [2, "1501", 49, -11, 50, 26, 0], [2, "1501", 23, 2, 50, 26, 0], [2, "1501", 49, 15, 50, 26, 0], [2, "1501", 75, 2, 50, 26, 0], [2, "1501", 101, -11, 50, 26, 0], [2, "1501", 839, 60, 50, 26, 0], [2, "1501", 865, 47, 50, 26, 0], [2, "1501", 891, 34, 50, 26, 0], [2, "1501", 917, 47, 50, 26, 0], [2, "1501", 891, 60, 50, 26, 0], [2, "1501", 865, 73, 50, 26, 0], [2, "1501", 891, 86, 50, 26, 0], [2, "1501", 917, 73, 50, 26, 0], [2, "1501", 943, 60, 50, 26, 0], [2, "1501", 910, 96, 50, 26, 0], [2, "1501", 936, 83, 50, 26, 0], [2, "1501", 962, 70, 50, 26, 0], [2, "1501", 988, 83, 50, 26, 0], [2, "1501", 962, 96, 50, 26, 0], [2, "1501", 936, 109, 50, 26, 0], [2, "1501", 962, 122, 50, 26, 0], [2, "1501", 988, 109, 50, 26, 0], [2, "1501", 1014, 96, 50, 26, 0], [2, "1501", 829, -18, 50, 26, 0], [2, "1501", 855, -31, 50, 26, 0], [2, "1501", 881, -44, 50, 26, 0], [2, "1501", 907, -31, 50, 26, 0], [2, "1501", 881, -18, 50, 26, 0], [2, "1501", 855, -5, 50, 26, 0], [2, "1501", 881, 8, 50, 26, 0], [2, "1501", 907, -5, 50, 26, 0], [2, "1501", 933, -18, 50, 26, 0], [2, "1501", 902, 20, 50, 26, 0], [2, "1501", 928, 7, 50, 26, 0], [2, "1501", 954, -6, 50, 26, 0], [2, "1501", 980, 7, 50, 26, 0], [2, "1501", 954, 20, 50, 26, 0], [2, "1501", 928, 33, 50, 26, 0], [2, "1501", 954, 46, 50, 26, 0], [2, "1501", 980, 33, 50, 26, 0], [2, "1501", 1006, 20, 50, 26, 0], [2, "1501", 935, 41, 50, 26, 0], [2, "1501", 961, 28, 50, 26, 0], [2, "1501", 987, 15, 50, 26, 0], [2, "1501", 1013, 28, 50, 26, 0], [2, "1501", 987, 41, 50, 26, 0], [2, "1501", 961, 54, 50, 26, 0], [2, "1501", 987, 67, 50, 26, 0], [2, "1501", 1013, 54, 50, 26, 0], [2, "1501", 1039, 41, 50, 26, 0], [2, "1501", 694, -13, 50, 26, 0], [2, "1501", 720, -26, 50, 26, 0], [2, "1501", 746, -39, 50, 26, 0], [2, "1501", 772, -26, 50, 26, 0], [2, "1501", 746, -13, 50, 26, 0], [2, "1501", 720, 0, 50, 26, 0], [2, "1501", 746, 13, 50, 26, 0], [2, "1501", 772, 0, 50, 26, 0], [2, "1501", 798, -13, 50, 26, 0], [2, "1501", 767, 24, 50, 26, 0], [2, "1501", 793, 11, 50, 26, 0], [2, "1501", 819, -2, 50, 26, 0], [2, "1501", 845, 11, 50, 26, 0], [2, "1501", 819, 24, 50, 26, 0], [2, "1501", 793, 37, 50, 26, 0], [2, "1501", 819, 50, 50, 26, 0], [2, "1501", 845, 37, 50, 26, 0], [2, "1501", 871, 24, 50, 26, 0], [2, "1501", 313, -23, 50, 26, 0], [2, "1501", 339, -36, 50, 26, 0], [2, "1501", 365, -49, 50, 26, 0], [2, "1501", 391, -36, 50, 26, 0], [2, "1501", 365, -23, 50, 26, 0], [2, "1501", 339, -10, 50, 26, 0], [2, "1501", 365, 3, 50, 26, 0], [2, "1501", 391, -10, 50, 26, 0], [2, "1501", 417, -23, 50, 26, 0], [2, "3744", 510, 386, 96, 49, 0], [2, "3367", 188, 114, 44, 81, 2], [2, "3367", 188, 131, 44, 81, 2], [2, "3367", 140, 138, 44, 81, 2], [2, "3367", 140, 155, 44, 81, 2], [2, "3367", 99, 159, 44, 81, 2], [2, "3367", 64, 172, 44, 81, 2], [2, "3367", 64, 191, 44, 81, 2], [2, "3367", 15, 201, 44, 81, 2], [2, "3367", -14, 215, 44, 81, 2], [2, "3367", -14, 228, 44, 81, 2], [2, "3367", -13, 423, 44, 81, 2], [2, "3367", -13, 440, 44, 81, 2], [2, "3367", 3, 416, 44, 81, 2], [2, "3367", 3, 422, 44, 81, 2], [2, "3367", 251, 119, 44, 81, 0], [2, "3367", 249, 132, 44, 81, 0], [2, "3367", 296, 139, 44, 81, 0], [2, "3367", 342, 165, 44, 81, 0], [2, "3367", 386, 186, 44, 81, 0], [2, "3367", 431, 207, 44, 81, 0], [2, "3367", 308, 161, 44, 81, 0], [2, "3367", 387, 218, 44, 81, 2], [2, "3367", 390, 234, 44, 81, 2], [2, "3367", 343, 243, 44, 81, 2], [2, "3367", 347, 264, 44, 81, 2], [2, "3367", 288, 271, 44, 81, 2], [2, "3367", 288, 290, 44, 81, 2], [2, "3367", 245, 292, 44, 81, 2], [2, "3367", 247, 310, 44, 81, 2], [2, "3367", 206, 314, 44, 81, 2], [2, "3367", 206, 330, 44, 81, 2], [2, "3367", 170, 332, 44, 81, 2], [2, "3367", 170, 349, 44, 81, 2], [2, "3367", 475, 232, 44, 81, 0], [2, "3367", 519, 250, 44, 81, 0], [2, "3367", 517, 264, 44, 81, 0], [2, "3367", 662, 320, 44, 81, 0], [2, "3367", 660, 332, 44, 81, 0], [2, "3367", 707, 343, 44, 81, 0], [2, "3367", 707, 343, 44, 81, 0], [2, "3367", 705, 361, 44, 81, 0], [2, "3367", 748, 367, 44, 81, 0], [2, "3367", 789, 385, 44, 81, 0], [2, "3367", 815, 398, 44, 81, 0], [2, "3367", 878, 427, 44, 81, 0], [2, "3367", 788, 394, 44, 81, 0], [2, "24", 482, 4, 28, 38, 0], [2, "24", 454, 19, 28, 38, 0], [2, "24", 425, 33, 28, 38, 0], [2, "24", 397, 47, 28, 38, 0], [2, "24", 368, 60, 28, 38, 0], [2, "24", 340, 74, 28, 38, 0], [2, "24", 311, 87, 28, 38, 0], [2, "24", 511, 4, 28, 38, 2], [2, "24", 540, 18, 28, 38, 2], [2, "24", 569, 32, 28, 38, 2], [2, "24", 598, 46, 28, 38, 2], [2, "24", 627, 61, 28, 38, 2], [2, "24", 656, 75, 28, 38, 2], [2, "24", 685, 89, 28, 38, 2], [2, "24", 714, 103, 28, 38, 2], [2, "24", 715, 103, 28, 38, 2], [2, "24", 744, 117, 28, 38, 2], [2, "24", 773, 131, 28, 38, 2], [2, "24", 802, 145, 28, 38, 2], [2, "24", 831, 160, 28, 38, 2], [2, "24", 860, 174, 28, 38, 2], [2, "24", 889, 188, 28, 38, 2], [2, "24", 918, 202, 28, 38, 2], [2, "24", 947, 217, 28, 38, 2], [2, "3367", 531, 260, 44, 81, 0], [2, "3367", 648, 316, 44, 81, 0], [2, "3367", 436, 224, 44, 81, 0], [2, "3367", 236, 113, 44, 81, 0], [2, "3367", 237, 123, 44, 81, 0], [2, "3367", 191, 113, 44, 81, 2], [2, "3367", 193, 129, 44, 81, 2], [2, "3367", 286, 150, 44, 81, 0], [2, "3367", 43, 187, 44, 81, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 16, 1, 2, -1, -1, -1, -1, -1, -1, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 22, 24, 25, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 29, 24, 25, 26, -1, 20, 19, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, 31, 26, -1, -1, -1, -1, 12, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 24, 24, 25, 26, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, 10, 10, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, 31, 30, 29, 34, 35, 36, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 24, 25, 26, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "3736", 394, 290, 76, 41, 0], [2, "3736", 346, 314, 76, 41, 0], [2, "3736", 424, 305, 76, 41, 0], [2, "3736", 376, 329, 76, 41, 0], [2, "3736", 453, 320, 76, 41, 0], [2, "3736", 405, 344, 76, 41, 0], [2, "3736", 482, 335, 76, 41, 0], [2, "3736", 434, 359, 76, 41, 0], [2, "3736", 400, 287, 76, 41, 0], [2, "3736", 352, 311, 76, 41, 0], [2, "3736", 430, 302, 76, 41, 0], [2, "3736", 382, 326, 76, 41, 0], [2, "3736", 459, 317, 76, 41, 0], [2, "3736", 411, 341, 76, 41, 0], [2, "3736", 488, 332, 76, 41, 0], [2, "3736", 440, 356, 76, 41, 0], [2, "3736", 305, 335, 76, 41, 0], [2, "3736", 257, 359, 76, 41, 0], [2, "3736", 335, 350, 76, 41, 0], [2, "3736", 287, 374, 76, 41, 0], [2, "3736", 364, 365, 76, 41, 0], [2, "3736", 316, 389, 76, 41, 0], [2, "3736", 393, 380, 76, 41, 0], [2, "3736", 345, 404, 76, 41, 0], [2, "3736", 237, 369, 76, 41, 0], [2, "3736", 189, 393, 76, 41, 0], [2, "3736", 267, 384, 76, 41, 0], [2, "3736", 219, 408, 76, 41, 0], [2, "3736", 296, 399, 76, 41, 0], [2, "3736", 248, 423, 76, 41, 0], [2, "3736", 325, 414, 76, 41, 0], [2, "3736", 277, 438, 76, 41, 0], [2, "3747", 472, 31, 80, 54, 0], [2, "3747", 514, 51, 80, 54, 0], [2, "3747", 556, 72, 80, 54, 0], [2, "3747", 438, 47, 80, 54, 0], [2, "3747", 480, 67, 80, 54, 0], [2, "3747", 522, 88, 80, 54, 0], [2, "3747", 406, 63, 80, 54, 0], [2, "3747", 448, 83, 80, 54, 0], [2, "3747", 490, 104, 80, 54, 0], [2, "3747", 372, 79, 80, 54, 0], [2, "3747", 414, 99, 80, 54, 0], [2, "3747", 456, 120, 80, 54, 0], [2, "3747", 338, 95, 80, 54, 0], [2, "3747", 380, 115, 80, 54, 0], [2, "3747", 422, 136, 80, 54, 0], [2, "3747", 305, 111, 80, 54, 0], [2, "3747", 347, 131, 80, 54, 0], [2, "3747", 389, 152, 80, 54, 0], [2, "3607", 52, 283, 62, 33, 0], [2, "3607", 126, 245, 62, 33, 0], [2, "3607", 192, 212, 62, 33, 0], [2, "3744", 49, 425, 96, 49, 2]]}, {"type": 2, "data": [77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 50, 51, 52, 73, 72, 48, 48, 48, 48, 48, 48, 69, 70, 67, 51, 52, 75, 75, 75, 75, 75, 75, 75, 75, 75, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 75, 74, 53, 54, 55, 54, 55, 68, 48, 48, 48, 48, 70, 66, 67, 53, 54, 55, 51, 52, 75, 75, 75, 75, 75, 75, 75, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 75, 70, 71, 56, 57, 58, 57, 58, 68, 48, 48, 48, 48, 74, 54, 55, 56, 50, 51, 52, 50, 51, 52, 75, 75, 75, 75, 75, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 48, 48, 48, 49, 45, 45, 46, 54, 61, 60, 64, 48, 48, 48, 48, 49, 59, 50, 51, 52, 54, 55, 50, 51, 52, 51, 52, 75, 75, 75, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 48, 48, 48, 48, 48, 48, 49, 45, 64, 63, 48, 48, 48, 48, 48, 48, 62, 53, 54, 55, 57, 58, 53, 54, 55, 50, 51, 61, 60, 75, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 69, 70, 72, 76, 48, 48, 48, 49, 45, 46, 58, 51, 52, 50, 51, 52, 61, 60, 64, 63, 48, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 63, 74, 54, 68, 48, 48, 48, 48, 48, 49, 46, 54, 55, 53, 61, 60, 64, 63, 69, 48, 48, 77, 77, 77, 77, 77, 77, 77, 77, 79, 80, 79, 80, 48, 48, 48, 48, 48, 48, 48, 48, 48, 49, 60, 64, 48, 48, 48, 48, 48, 48, 49, 45, 45, 64, 64, 63, 48, 48, 48, 48, 48, 77, 77, 77, 77, 77, 77, 77, 77, 79, 80, 79, 80, 79, 80, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 77, 77, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 77, 77, 77, 78, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 77, 77, 77, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 69, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 48, 48, 48, 48, 72, 72, 76, 75, 48, 48, 48, 48, 48, 48, 75, 70, 66, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 48, 48, 48, 48, 48, 48, 51, 73, 72, 76, 48, 48, 48, 48, 70, 72, 71, 54, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 79, 80, 48, 48, 48, 48, 48, 48, 48, 54, 68, 48, 48, 48, 48, 62, 53, 56, 57, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 78, 77, 78, 77, 77, 48, 77, 77, 77, 48, 48, 68, 48, 48, 48, 48, 49, 46, 57, 61, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 48, 48, 48, 48, 48, 48, 48, 48, 49, 45, 64, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 77, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 48, 48, 48, 48, 75, 48, 48, 48, 48, 77, 78, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 48, 48, 48, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 48, 48, 48, 77, 77, 78, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 77, 77, 77, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 77, 77, 77, 77, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77, 77]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}