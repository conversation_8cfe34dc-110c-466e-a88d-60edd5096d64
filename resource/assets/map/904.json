{"mW": 768, "mH": 672, "tW": 24, "tH": 24, "tiles": [["135", 0, 1, 1], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["106_2", 0, 3, 3], ["302_1", 0, 2, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["203_14", 0, 2, 1], ["203_14", 1, 2, 1], ["415_2", 0, 4, 1], ["415_2", 2, 4, 1], ["415_2", 1, 4, 1], ["415_2", 3, 4, 1], ["709", 0, 2, 1], ["709_1", 0, 2, 1]], "layers": [{"type": 4, "obj": [[2, "700", 559, -30, 22, 48, 2], [2, "700", 582, -30, 22, 48, 0], [2, "700", 441, 25, 22, 48, 2], [2, "700", 464, 25, 22, 48, 0], [2, "699", 597, 14, 96, 87, 0], [2, "700", 736, 57, 22, 48, 2], [2, "700", 759, 57, 22, 48, 0], [2, "571", 187, 89, 34, 18, 0], [2, "701", 125, 53, 16, 59, 0], [2, "570", 187, 107, 34, 21, 0], [2, "569", 186, 128, 34, 13, 0], [2, "571", 92, 140, 34, 18, 0], [2, "701", 29, 103, 16, 59, 0], [2, "700", 620, 119, 22, 48, 2], [2, "700", 643, 119, 22, 48, 0], [2, "710", 322, 111, 38, 62, 0], [2, "570", 92, 158, 34, 21, 0], [2, "569", 91, 179, 34, 13, 0], [2, "571", 366, 180, 34, 18, 0], [2, "570", 366, 198, 34, 21, 0], [2, "569", 365, 219, 34, 13, 0], [2, "571", 445, 219, 34, 18, 0], [2, "700", 362, 203, 22, 48, 2], [2, "700", 385, 203, 22, 48, 0], [2, "570", 445, 237, 34, 21, 0], [2, "710", 493, 198, 38, 62, 0], [2, "569", 444, 258, 34, 13, 0], [2, "701", 211, 215, 16, 59, 0], [2, "700", 442, 244, 22, 48, 2], [2, "700", 465, 244, 22, 48, 0], [2, "571", 640, 307, 34, 18, 0], [2, "570", 640, 325, 34, 21, 0], [2, "569", 639, 346, 34, 13, 0], [2, "701", 42, 306, 16, 59, 0], [2, "701", 406, 314, 16, 59, 0], [2, "571", 543, 360, 34, 18, 0], [2, "571", 135, 367, 34, 18, 0], [2, "701", 693, 328, 16, 59, 0], [2, "701", 107, 340, 16, 59, 0], [2, "570", 543, 378, 34, 21, 0], [2, "570", 135, 385, 34, 21, 0], [2, "569", 542, 399, 34, 13, 0], [2, "569", 134, 406, 34, 13, 0], [2, "571", 239, 417, 34, 18, 0], [2, "570", 239, 435, 34, 21, 0], [2, "569", 238, 456, 34, 13, 0], [2, "701", 630, 424, 16, 59, 0], [2, "701", 332, 453, 16, 59, 0], [2, "701", 750, 474, 16, 59, 0], [2, "701", 448, 514, 16, 59, 0], [2, "706", 258, 507, 98, 112, 0], [2, "701", 585, 582, 16, 59, 0]]}, {"type": 3, "obj": [[2, "586_1", 26, 30, 40, 22, 2], [2, "587_1", 342, 550, 24, 24, 3], [2, "587_1", 386, 573, 24, 24, 3], [2, "587_1", 192, 561, 24, 24, 0], [2, "587_1", 92, 5, 24, 24, 2], [2, "587_1", 50, 29, 24, 24, 2], [2, "508_1", 298, 533, 60, 37, 2], [2, "508_1", 345, 556, 60, 37, 2], [2, "508_1", 393, 580, 60, 37, 2], [2, "508_1", 403, 603, 60, 37, 0], [2, "587_1", 167, 409, 24, 24, 0], [2, "586_1", 128, 391, 40, 22, 0], [2, "586_1", 157, 406, 40, 22, 0], [2, "572_1", 125, 402, 56, 42, 0], [2, "427_1", 306, 427, 24, 24, 0], [2, "427_1", 282, 438, 24, 24, 0], [2, "427_1", 336, 435, 24, 24, 2], [2, "427_1", 374, 454, 24, 24, 2], [2, "427_1", 384, 459, 24, 24, 2], [2, "427_1", 409, 460, 24, 24, 0], [2, "427_1", 432, 449, 24, 24, 0], [2, "427_1", 455, 437, 24, 24, 0], [2, "427_1", 478, 425, 24, 24, 0], [2, "427_1", 501, 413, 24, 24, 0], [2, "427_1", 358, 446, 24, 24, 2], [2, "427_1", 503, 511, 24, 24, 2], [2, "427_1", 541, 530, 24, 24, 2], [2, "427_1", 550, 535, 24, 24, 2], [2, "427_1", 525, 522, 24, 24, 2], [2, "427_1", 573, 547, 24, 24, 2], [2, "427_1", 189, 358, 24, 24, 2], [2, "427_1", 207, 367, 24, 24, 2], [2, "427_1", 328, 430, 24, 24, 2], [2, "427_1", 35, 279, 24, 24, 2], [2, "427_1", 73, 298, 24, 24, 2], [2, "427_1", 83, 303, 24, 24, 2], [2, "427_1", 108, 304, 24, 24, 0], [2, "427_1", 131, 293, 24, 24, 0], [2, "427_1", 154, 281, 24, 24, 0], [2, "427_1", 177, 269, 24, 24, 0], [2, "427_1", 200, 250, 24, 24, 0], [2, "427_1", 57, 290, 24, 24, 2], [2, "427_1", 27, 274, 24, 24, 2], [2, "427_1", 3, 262, 24, 24, 2], [2, "427_1", -18, 252, 24, 24, 2], [2, "427_1", 616, 570, 24, 24, 2], [2, "427_1", 640, 572, 24, 24, 0], [2, "427_1", 723, 614, 24, 24, 2], [2, "427_1", 701, 602, 24, 24, 2], [2, "427_1", 678, 590, 24, 24, 2], [2, "427_1", 654, 577, 24, 24, 2], [2, "427_1", 594, 558, 24, 24, 2], [2, "427_1", 268, 445, 24, 24, 0], [2, "587_1", 552, 600, 24, 24, 0], [2, "587_1", 503, 577, 24, 24, 0], [2, "587_1", 455, 552, 24, 24, 0], [2, "587_1", 408, 530, 24, 24, 0], [2, "587_1", 359, 507, 24, 24, 0], [2, "587_1", 312, 480, 24, 24, 0], [2, "587_1", 264, 456, 24, 24, 0], [2, "587_1", 120, 386, 24, 24, 0], [2, "587_1", 72, 360, 24, 24, 0], [2, "587_1", 24, 338, 24, 24, 0], [2, "587_1", 645, 339, 24, 24, 0], [2, "587_1", 695, 362, 24, 24, 0], [2, "587_1", 432, 242, 24, 24, 0], [2, "587_1", 192, 120, 24, 24, 0], [2, "587_1", 144, 96, 24, 24, 0], [2, "587_1", 72, 72, 24, 24, 0], [2, "587_1", 480, 26, 24, 24, 2], [2, "587_1", 436, 48, 24, 24, 2], [2, "587_1", 529, 2, 24, 24, 2], [2, "587_1", 624, 340, 24, 24, 2], [2, "427_1", 224, 82, 24, 24, 2], [2, "427_1", 262, 101, 24, 24, 2], [2, "427_1", 272, 106, 24, 24, 2], [2, "427_1", 297, 107, 24, 24, 0], [2, "427_1", 320, 96, 24, 24, 0], [2, "427_1", 343, 84, 24, 24, 0], [2, "427_1", 366, 72, 24, 24, 0], [2, "427_1", 246, 93, 24, 24, 2], [2, "427_1", 216, 77, 24, 24, 2], [2, "427_1", 192, 65, 24, 24, 2], [2, "427_1", 390, 60, 24, 24, 0], [2, "427_1", 413, 48, 24, 24, 0], [2, "427_1", 437, 36, 24, 24, 0], [2, "427_1", 461, 23, 24, 24, 0], [2, "427_1", 484, 11, 24, 24, 0], [2, "427_1", 507, 0, 24, 24, 0], [2, "427_1", 747, 626, 24, 24, 2], [2, "587_1", 744, 385, 24, 24, 0], [2, "92_1", 214, 47, 40, 45, 0], [2, "586_1", 111, 9, 40, 22, 0], [2, "586_1", 139, 23, 40, 22, 0], [2, "586_1", 168, 37, 40, 22, 0], [2, "92_1", 180, 95, 40, 45, 2], [2, "92_1", 180, 73, 40, 45, 2], [2, "92_1", 165, 76, 40, 45, 2], [2, "586_1", 37, 56, 40, 22, 0], [2, "586_1", 66, 70, 40, 22, 0], [2, "586_1", 176, 115, 40, 22, 0], [2, "586_1", 205, 130, 40, 22, 0], [2, "92_1", 217, 189, 40, 45, 2], [2, "92_1", 217, 167, 40, 45, 2], [2, "92_1", 217, 145, 40, 45, 2], [2, "92_1", 189, 225, 40, 45, 2], [2, "92_1", 161, 240, 40, 45, 2], [2, "92_1", 135, 253, 40, 45, 2], [2, "92_1", 108, 267, 40, 45, 2], [2, "92_1", 189, 203, 40, 45, 2], [2, "92_1", 161, 218, 40, 45, 2], [2, "92_1", 135, 232, 40, 45, 2], [2, "92_1", 108, 247, 40, 45, 2], [2, "92_1", 189, 181, 40, 45, 2], [2, "92_1", 161, 196, 40, 45, 2], [2, "92_1", 135, 210, 40, 45, 2], [2, "92_1", 108, 225, 40, 45, 2], [2, "92_1", 189, 159, 40, 45, 2], [2, "92_1", 161, 174, 40, 45, 2], [2, "92_1", 135, 188, 40, 45, 2], [2, "92_1", 108, 203, 40, 45, 2], [2, "92_1", -19, 208, 40, 45, 0], [2, "92_1", -19, 189, 40, 45, 0], [2, "92_1", -19, 167, 40, 45, 0], [2, "92_1", -19, 145, 40, 45, 0], [2, "92_1", -4, 229, 40, 45, 0], [2, "92_1", 25, 243, 40, 45, 0], [2, "92_1", -3, 210, 40, 45, 0], [2, "92_1", 25, 224, 40, 45, 0], [2, "92_1", -3, 188, 40, 45, 0], [2, "92_1", 25, 202, 40, 45, 0], [2, "92_1", -3, 166, 40, 45, 0], [2, "92_1", 25, 180, 40, 45, 0], [2, "92_1", 53, 258, 40, 45, 0], [2, "92_1", 80, 273, 40, 45, 0], [2, "92_1", 53, 239, 40, 45, 0], [2, "92_1", 80, 251, 40, 45, 0], [2, "92_1", 53, 217, 40, 45, 0], [2, "92_1", 79, 231, 40, 45, 0], [2, "92_1", 53, 195, 40, 45, 0], [2, "92_1", 80, 209, 40, 45, 0], [2, "586_1", 94, 84, 40, 22, 0], [2, "586_1", -3, 153, 40, 22, 0], [2, "92_1", 180, 51, 40, 45, 2], [2, "92_1", 152, 65, 40, 45, 2], [2, "92_1", 136, 73, 40, 45, 2], [2, "586_1", 148, 100, 40, 22, 0], [2, "586_1", 123, 87, 40, 22, 0], [2, "586_1", 83, 3, 40, 22, 2], [2, "586_1", 54, 17, 40, 22, 2], [2, "586_1", 8, 40, 40, 22, 0], [2, "306_1", 115, 100, 46, 25, 0], [2, "306_1", 158, 121, 46, 25, 0], [2, "92_1", 489, -28, 40, 45, 2], [2, "92_1", 463, -16, 40, 45, 2], [2, "92_1", 462, -39, 40, 45, 2], [2, "92_1", 436, -1, 40, 45, 2], [2, "92_1", 436, -23, 40, 45, 2], [2, "92_1", 436, -45, 40, 45, 2], [2, "92_1", 408, 14, 40, 45, 2], [2, "92_1", 408, -8, 40, 45, 2], [2, "92_1", 408, -30, 40, 45, 2], [2, "92_1", 380, 28, 40, 45, 2], [2, "92_1", 380, 6, 40, 45, 2], [2, "92_1", 380, -16, 40, 45, 2], [2, "92_1", 380, -38, 40, 45, 2], [2, "92_1", 352, 42, 40, 45, 2], [2, "92_1", 324, 57, 40, 45, 2], [2, "92_1", 298, 70, 40, 45, 2], [2, "92_1", 352, 20, 40, 45, 2], [2, "92_1", 324, 34, 40, 45, 2], [2, "92_1", 298, 47, 40, 45, 2], [2, "92_1", 352, -2, 40, 45, 2], [2, "92_1", 324, 12, 40, 45, 2], [2, "92_1", 298, 25, 40, 45, 2], [2, "92_1", 352, -24, 40, 45, 2], [2, "92_1", 324, -10, 40, 45, 2], [2, "92_1", 298, 3, 40, 45, 2], [2, "586_1", -13, 318, 40, 22, 0], [2, "586_1", 15, 332, 40, 22, 0], [2, "586_1", 44, 347, 40, 22, 0], [2, "586_1", 72, 362, 40, 22, 0], [2, "586_1", 400, 529, 40, 22, 0], [2, "92_1", 583, 631, 40, 45, 2], [2, "92_1", 752, 161, 40, 45, 2], [2, "92_1", 752, 139, 40, 45, 2], [2, "92_1", 752, 117, 40, 45, 2], [2, "92_1", 752, 95, 40, 45, 2], [2, "586_1", 549, -13, 40, 22, 2], [2, "586_1", 520, 1, 40, 22, 2], [2, "92_1", 724, 175, 40, 45, 2], [2, "92_1", 696, 190, 40, 45, 2], [2, "92_1", 670, 204, 40, 45, 2], [2, "92_1", 643, 219, 40, 45, 2], [2, "92_1", 724, 153, 40, 45, 2], [2, "92_1", 696, 168, 40, 45, 2], [2, "92_1", 670, 182, 40, 45, 2], [2, "92_1", 643, 197, 40, 45, 2], [2, "92_1", 724, 131, 40, 45, 2], [2, "92_1", 696, 146, 40, 45, 2], [2, "92_1", 670, 160, 40, 45, 2], [2, "92_1", 643, 175, 40, 45, 2], [2, "92_1", 724, 109, 40, 45, 2], [2, "92_1", 696, 124, 40, 45, 2], [2, "92_1", 670, 138, 40, 45, 2], [2, "92_1", 643, 153, 40, 45, 2], [2, "586_1", 491, 15, 40, 22, 2], [2, "92_1", 242, 61, 40, 45, 0], [2, "92_1", 269, 75, 40, 45, 0], [2, "92_1", 187, 10, 40, 45, 0], [2, "92_1", 214, 24, 40, 45, 0], [2, "92_1", 242, 39, 40, 45, 0], [2, "92_1", 269, 53, 40, 45, 0], [2, "92_1", 187, -12, 40, 45, 0], [2, "92_1", 214, 2, 40, 45, 0], [2, "92_1", 242, 17, 40, 45, 0], [2, "92_1", 269, 31, 40, 45, 0], [2, "92_1", 187, -34, 40, 45, 0], [2, "92_1", 214, -20, 40, 45, 0], [2, "92_1", 242, -5, 40, 45, 0], [2, "92_1", 269, 9, 40, 45, 0], [2, "586_1", 462, 29, 40, 22, 2], [2, "586_1", 434, 43, 40, 22, 2], [2, "92_1", 423, 124, 40, 45, 0], [2, "92_1", 478, 149, 40, 45, 0], [2, "92_1", 505, 163, 40, 45, 0], [2, "92_1", 423, 102, 40, 45, 0], [2, "92_1", 450, 116, 40, 45, 0], [2, "92_1", 478, 131, 40, 45, 0], [2, "92_1", 505, 145, 40, 45, 0], [2, "92_1", 429, 83, 40, 45, 0], [2, "92_1", 450, 94, 40, 45, 0], [2, "92_1", 478, 109, 40, 45, 0], [2, "92_1", 505, 123, 40, 45, 0], [2, "92_1", 423, 58, 40, 45, 0], [2, "92_1", 450, 72, 40, 45, 0], [2, "92_1", 478, 87, 40, 45, 0], [2, "92_1", 505, 101, 40, 45, 0], [2, "92_1", 533, 178, 40, 45, 0], [2, "92_1", 572, 222, 40, 45, 0], [2, "92_1", 533, 160, 40, 45, 0], [2, "92_1", 560, 174, 40, 45, 0], [2, "92_1", 533, 138, 40, 45, 0], [2, "92_1", 560, 152, 40, 45, 0], [2, "92_1", 533, 116, 40, 45, 0], [2, "92_1", 560, 130, 40, 45, 0], [2, "92_1", 588, 207, 40, 45, 0], [2, "92_1", 615, 224, 40, 45, 0], [2, "92_1", 588, 189, 40, 45, 0], [2, "92_1", 615, 203, 40, 45, 0], [2, "92_1", 588, 167, 40, 45, 0], [2, "92_1", 614, 173, 40, 45, 0], [2, "92_1", 588, 145, 40, 45, 0], [2, "92_1", 615, 159, 40, 45, 0], [2, "540_1", 479, 110, 42, 36, 2], [2, "541_1", 457, 133, 38, 42, 2], [2, "540_1", 545, 146, 42, 36, 2], [2, "541_1", 523, 169, 38, 42, 2], [2, "541_1", 426, 165, 38, 42, 2], [2, "541_1", 492, 201, 38, 42, 2], [2, "541_1", 462, 232, 38, 42, 2], [2, "541_1", 397, 196, 38, 42, 2], [2, "92_1", 518, 366, 40, 45, 2], [2, "92_1", 518, 344, 40, 45, 2], [2, "92_1", 518, 322, 40, 45, 2], [2, "92_1", 518, 300, 40, 45, 2], [2, "586_1", 315, 192, 40, 22, 2], [2, "586_1", 286, 206, 40, 22, 2], [2, "92_1", 490, 380, 40, 45, 2], [2, "92_1", 462, 396, 40, 45, 2], [2, "92_1", 436, 410, 40, 45, 2], [2, "92_1", 409, 424, 40, 45, 2], [2, "92_1", 490, 358, 40, 45, 2], [2, "92_1", 462, 373, 40, 45, 2], [2, "92_1", 436, 387, 40, 45, 2], [2, "92_1", 409, 402, 40, 45, 2], [2, "92_1", 490, 336, 40, 45, 2], [2, "92_1", 462, 351, 40, 45, 2], [2, "92_1", 436, 365, 40, 45, 2], [2, "92_1", 409, 380, 40, 45, 2], [2, "92_1", 490, 314, 40, 45, 2], [2, "92_1", 462, 329, 40, 45, 2], [2, "92_1", 436, 343, 40, 45, 2], [2, "92_1", 409, 358, 40, 45, 2], [2, "586_1", 257, 220, 40, 22, 2], [2, "586_1", 228, 234, 40, 22, 2], [2, "586_1", 200, 248, 40, 22, 2], [2, "92_1", 189, 329, 40, 45, 0], [2, "92_1", 244, 354, 40, 45, 0], [2, "92_1", 271, 368, 40, 45, 0], [2, "92_1", 189, 307, 40, 45, 0], [2, "92_1", 216, 321, 40, 45, 0], [2, "92_1", 244, 336, 40, 45, 0], [2, "92_1", 271, 350, 40, 45, 0], [2, "92_1", 195, 288, 40, 45, 0], [2, "92_1", 216, 299, 40, 45, 0], [2, "92_1", 244, 314, 40, 45, 0], [2, "92_1", 271, 328, 40, 45, 0], [2, "92_1", 189, 263, 40, 45, 0], [2, "92_1", 216, 277, 40, 45, 0], [2, "92_1", 244, 292, 40, 45, 0], [2, "92_1", 271, 306, 40, 45, 0], [2, "92_1", 299, 383, 40, 45, 0], [2, "92_1", 328, 401, 40, 45, 0], [2, "92_1", 299, 365, 40, 45, 0], [2, "92_1", 326, 379, 40, 45, 0], [2, "92_1", 299, 343, 40, 45, 0], [2, "92_1", 326, 357, 40, 45, 0], [2, "92_1", 299, 321, 40, 45, 0], [2, "92_1", 326, 335, 40, 45, 0], [2, "92_1", 354, 414, 40, 45, 0], [2, "92_1", 381, 429, 40, 45, 0], [2, "92_1", 354, 394, 40, 45, 0], [2, "92_1", 381, 408, 40, 45, 0], [2, "92_1", 354, 372, 40, 45, 0], [2, "92_1", 380, 378, 40, 45, 0], [2, "92_1", 354, 350, 40, 45, 0], [2, "92_1", 381, 364, 40, 45, 0], [2, "586_1", 332, 193, 40, 22, 0], [2, "586_1", 361, 208, 40, 22, 0], [2, "586_1", 390, 223, 40, 22, 0], [2, "586_1", 418, 238, 40, 22, 0], [2, "586_1", 447, 253, 40, 22, 0], [2, "586_1", 476, 268, 40, 22, 0], [2, "586_1", 504, 282, 40, 22, 0], [2, "586_1", 519, 290, 40, 22, 0], [2, "586_1", 444, 268, 40, 22, 2], [2, "540_1", 692, 165, 42, 36, 0], [2, "541_1", 719, 188, 38, 42, 0], [2, "541_1", 750, 219, 38, 42, 0], [2, "586_1", 426, 542, 40, 22, 0], [2, "586_1", 455, 557, 40, 22, 0], [2, "586_1", 484, 572, 40, 22, 0], [2, "586_1", 513, 586, 40, 22, 0], [2, "586_1", 542, 601, 40, 22, 0], [2, "586_1", 258, 455, 40, 22, 0], [2, "586_1", 287, 470, 40, 22, 0], [2, "586_1", 316, 485, 40, 22, 0], [2, "586_1", 344, 500, 40, 22, 0], [2, "586_1", 373, 515, 40, 22, 0], [2, "586_1", 100, 376, 40, 22, 0], [2, "586_1", 571, 616, 40, 22, 0], [2, "92_1", 554, 646, 40, 45, 2], [2, "92_1", 525, 660, 40, 45, 2], [2, "427_1", 578, 661, 24, 24, 0], [2, "427_1", 599, 651, 24, 24, 0], [2, "427_1", 557, 671, 24, 24, 0], [2, "586_1", 496, 675, 40, 22, 2], [2, "586_1", 127, 442, 40, 22, 2], [2, "586_1", 98, 456, 40, 22, 2], [2, "586_1", 69, 470, 40, 22, 2], [2, "586_1", 41, 484, 40, 22, 2], [2, "586_1", 186, 473, 40, 22, 2], [2, "586_1", 157, 487, 40, 22, 2], [2, "586_1", 128, 501, 40, 22, 2], [2, "586_1", 99, 515, 40, 22, 2], [2, "506_1", 253, 310, 20, 45, 2], [2, "507_1", 213, 316, 48, 58, 2], [2, "507_1", 176, 365, 48, 58, 2], [2, "507_1", 158, 389, 48, 58, 2], [2, "43_4", 172, 410, 82, 58, 0], [2, "43_4", 180, 397, 82, 58, 0], [2, "43_4", 204, 367, 82, 58, 0], [2, "43_4", 221, 346, 82, 58, 0], [2, "43_4", 238, 324, 82, 58, 0], [2, "586_1", 542, 387, 40, 22, 2], [2, "586_1", 515, 401, 40, 22, 2], [2, "92_1", 503, 482, 40, 45, 0], [2, "92_1", 531, 496, 40, 45, 0], [2, "92_1", 558, 510, 40, 45, 0], [2, "92_1", 585, 525, 40, 45, 0], [2, "92_1", 503, 460, 40, 45, 0], [2, "92_1", 530, 474, 40, 45, 0], [2, "92_1", 558, 489, 40, 45, 0], [2, "92_1", 585, 503, 40, 45, 0], [2, "92_1", 503, 438, 40, 45, 0], [2, "92_1", 530, 452, 40, 45, 0], [2, "92_1", 558, 467, 40, 45, 0], [2, "92_1", 585, 481, 40, 45, 0], [2, "92_1", 503, 416, 40, 45, 0], [2, "92_1", 530, 430, 40, 45, 0], [2, "92_1", 558, 445, 40, 45, 0], [2, "92_1", 585, 459, 40, 45, 0], [2, "586_1", 605, 346, 40, 22, 2], [2, "506_1", 310, 390, 20, 45, 2], [2, "506_1", 300, 394, 20, 45, 2], [2, "506_1", 288, 399, 20, 45, 2], [2, "506_1", 277, 405, 20, 45, 2], [2, "506_1", 267, 408, 20, 45, 2], [2, "506_1", 255, 414, 20, 45, 2], [2, "506_1", 310, 372, 20, 45, 2], [2, "506_1", 304, 373, 20, 45, 2], [2, "506_1", 292, 379, 20, 45, 2], [2, "506_1", 280, 382, 20, 45, 2], [2, "507_1", 242, 384, 48, 58, 2], [2, "507_1", 216, 419, 48, 58, 2], [2, "508_1", 262, 314, 60, 37, 2], [2, "506_1", 312, 338, 20, 45, 2], [2, "507_1", 273, 344, 48, 58, 2], [2, "92_1", 640, 541, 40, 45, 0], [2, "92_1", 640, 519, 40, 45, 0], [2, "92_1", 640, 497, 40, 45, 0], [2, "92_1", 640, 475, 40, 45, 0], [2, "92_1", 613, 540, 40, 45, 0], [2, "92_1", 613, 518, 40, 45, 0], [2, "92_1", 613, 496, 40, 45, 0], [2, "92_1", 613, 474, 40, 45, 0], [2, "92_1", 668, 556, 40, 45, 0], [2, "92_1", 668, 534, 40, 45, 0], [2, "92_1", 668, 512, 40, 45, 0], [2, "92_1", 668, 490, 40, 45, 0], [2, "586_1", 635, 339, 40, 22, 0], [2, "586_1", 665, 353, 40, 22, 0], [2, "586_1", 695, 368, 40, 22, 0], [2, "586_1", 724, 383, 40, 22, 0], [2, "586_1", 745, 389, 40, 22, 2], [2, "92_1", 696, 571, 40, 45, 0], [2, "92_1", 696, 549, 40, 45, 0], [2, "92_1", 696, 527, 40, 45, 0], [2, "92_1", 696, 505, 40, 45, 0], [2, "92_1", 724, 586, 40, 45, 0], [2, "92_1", 724, 564, 40, 45, 0], [2, "92_1", 724, 542, 40, 45, 0], [2, "92_1", 724, 520, 40, 45, 0], [2, "92_1", 753, 601, 40, 45, 0], [2, "92_1", 753, 579, 40, 45, 0], [2, "92_1", 753, 557, 40, 45, 0], [2, "92_1", 753, 535, 40, 45, 0], [2, "585", 319, 193, 46, 34, 0], [2, "567", 307, 137, 68, 67, 0], [2, "585", 490, 280, 46, 34, 0], [2, "272_1", 195, 146, 72, 54, 2], [2, "271_1", 256, 174, 64, 50, 2], [2, "269_1", 130, 165, 110, 58, 2], [2, "269_1", 170, 186, 110, 58, 2], [2, "269_1", 197, 199, 110, 58, 2], [2, "272_1", 122, 183, 72, 54, 2], [2, "271_1", 183, 211, 64, 50, 2], [2, "272_1", 504, 300, 72, 54, 2], [2, "271_1", 565, 328, 64, 50, 2], [2, "269_1", 439, 319, 110, 58, 2], [2, "269_1", 479, 340, 110, 58, 2], [2, "269_1", 506, 353, 110, 58, 2], [2, "272_1", 431, 337, 72, 54, 2], [2, "271_1", 492, 365, 64, 50, 2], [2, "567", 478, 224, 68, 67, 0], [2, "272_1", 147, -42, 72, 54, 0], [2, "271_1", 112, -23, 64, 50, 0], [2, "269_1", 179, -25, 110, 58, 0], [2, "269_1", 165, -18, 110, 58, 0], [2, "269_1", 123, 2, 110, 58, 0], [2, "272_1", 219, -9, 72, 54, 0], [2, "271_1", 180, 12, 64, 50, 0], [2, "540_1", 396, -28, 42, 36, 0], [2, "541_1", 423, -5, 38, 42, 0], [2, "572_1", 80, 177, 56, 42, 0], [2, "572_1", 176, 124, 56, 42, 0], [2, "508_1", 251, 533, 60, 37, 0], [2, "508_1", 208, 554, 60, 37, 0], [2, "508_1", 161, 577, 60, 37, 0], [2, "508_1", 152, 600, 60, 37, 2], [2, "508_1", 197, 622, 60, 37, 2], [2, "508_1", 244, 646, 60, 37, 2], [2, "508_1", 358, 626, 60, 37, 0], [2, "508_1", 312, 648, 60, 37, 0], [2, "508_1", -16, 381, 60, 37, 2], [2, "508_1", -6, 404, 60, 37, 0], [2, "508_1", -53, 428, 60, 37, 0], [2, "586_1", 70, 529, 40, 22, 2], [2, "586_1", 42, 543, 40, 22, 2], [2, "586_1", 13, 557, 40, 22, 2], [2, "586_1", 11, 499, 40, 22, 2], [2, "586_1", -18, 513, 40, 22, 2], [2, "586_1", -16, 571, 40, 22, 2], [2, "587_1", 240, 548, 24, 24, 1], [2, "388", 595, 57, 60, 51, 0], [2, "711", 347, 580, 92, 34, 0], [2, "704", 474, 130, 82, 76, 0], [2, "704", 428, 173, 82, 76, 0], [2, "702", 561, 467, 18, 25, 0], [2, "702", 356, 377, 18, 25, 0], [2, "702", 208, 305, 18, 25, 0], [2, "702", 18, 201, 18, 25, 0], [2, "702", 600, 170, 18, 25, 0], [2, "702", 443, 95, 18, 25, 0], [2, "702", 312, 32, 18, 25, 2], [2, "702", 382, -3, 18, 25, 2], [2, "572_1", 228, 451, 56, 42, 0], [2, "572_1", 530, 398, 56, 42, 2], [2, "572_1", 628, 345, 56, 42, 2], [2, "700", 286, 561, 22, 48, 2], [2, "700", 309, 561, 22, 48, 0], [2, "700", 306, 570, 22, 48, 2], [2, "700", 329, 570, 22, 48, 0], [2, "700", 267, 571, 22, 48, 2], [2, "700", 290, 571, 22, 48, 0], [2, "700", 287, 580, 22, 48, 2], [2, "700", 310, 580, 22, 48, 0], [2, "703", 550, 394, 16, 23, 2], [2, "703", 647, 341, 16, 23, 2], [2, "703", 196, 119, 16, 23, 2], [2, "703", 99, 173, 16, 23, 2], [2, "703", 142, 399, 16, 23, 0], [2, "703", 247, 447, 16, 23, 0], [2, "703", 159, 589, 16, 23, 0], [2, "703", 296, 522, 16, 23, 0], [2, "703", 439, 592, 16, 23, 0], [2, "707", 305, 609, 6, 25, 0], [2, "707", 306, 632, 6, 25, 0], [2, "707", 306, 653, 6, 25, 0], [2, "703", 299, 659, 16, 23, 0], [2, "707", 301, 544, 6, 25, 2], [2, "703", 31, 393, 16, 23, 0], [2, "711", 176, 577, 92, 34, 2], [2, "702", 715, 537, 18, 25, 0], [2, "711", -62, 380, 92, 34, 0], [2, "166_1", 221, 291, 30, 35, 0], [2, "166_1", 325, 342, 30, 35, 0], [2, "586_1", -12, 38, 40, 22, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, -1, -1, 17, 12, 13, 13, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 12, 13, -1, -1, 34, 35, -1, 50, 51, 52, -1, -1, 1, 2, 3, -1, -1, -1, 8, 7, 6, 6, -1, -1, -1, -1, -1, 38, 1, 2, 3, 2, 3, -1, -1, -1, -1, 46, 47, 47, 48, -1, 18, 17, 3, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 38, -1, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, 18, 17, 11, 12, 8, 7, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, 11, 12, 13, -1, -1, -1, -1, 18, 17, 16, -1, -1, -1, -1, -1, -1, -1, 38, 38, -1, -1, -1, -1, -1, 18, 17, 12, -1, -1, -1, -1, -1, 1, 2, 3, -1, -1, 18, 17, 16, 44, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 1, 2, 7, 57, 44, -1, -1, 8, 7, 6, 44, 44, 44, 44, -1, -1, -1, 44, 44, 44, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 3, -1, -1, 8, 7, 2, 3, -1, -1, 8, 7, 6, 44, 44, 44, -1, 44, 44, 44, 1, 2, 6, -1, -1, -1, -1, -1, -1, -1, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, -1, 8, 7, 6, 44, 44, 6, 1, 2, 3, -1, 8, 7, 6, -1, -1, -1, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 57, 3, -1, -1, 8, 7, 6, 2, 3, -1, -1, -1, -1, -1, 8, 7, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, 12, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 13, -1, 13, -1, -1, -1, -1, 8, 7, 6, 6, -1, -1, -1, -1, -1, -1, 18, 17, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 41, 50, 38, 11, 12, 11, 12, 13, -1, -1, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 50, 38, 38, 38, -1, 1, 48, -1, 18, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 12, 13, -1, 8, 7, 6, 38, 38, 2, 3, -1, -1, 8, 7, 2, 6, -1, -1, -1, 8, 7, 6, 38, -1, -1, -1, 44, 44, 44, -1, -1, 2, 3, -1, -1, -1, -1, 8, 7, 2, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, 38, -1, 8, 7, 6, 44, 44, 44, 44, 44, -1, -1, -1, -1, 18, 18, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 2, 3, -1, -1, 8, 3, 8, 7, 6, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, 17, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, 16, 16, 11, 12, 13, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, -1, -1, 8, 7, 6, 38, 44, 1, 2, 3, -1, 12, 13, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, 8, 7, 2, 3, -1, -1, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, 11, 12, 13, -1, -1, -1, 8]}, {"type": 2, "data": [80, 81, 80, 81, 80, 81, -1, -1, -1, -1, -1, 30, 30, 30, -1, -1, -1, -1, -1, -1, -1, -1, 80, 29, 25, 26, 30, 30, 31, 31, 29, 21, 80, 81, 81, 30, 30, 30, 30, 30, -1, -1, -1, -1, 32, 32, -1, -1, -1, -1, -1, -1, -1, 23, 21, 22, 30, 30, 31, 32, 33, 30, 31, 24, 32, 33, 30, 32, 32, 32, 32, 32, -1, -1, -1, -1, -1, -1, -1, -1, 81, 81, -1, 24, 25, 26, 24, 32, 30, 31, 33, 33, 32, 32, 33, 27, 30, 30, 32, 33, 32, 33, 33, 32, -1, 80, -1, -1, -1, -1, 80, 81, 81, 81, 21, 27, 28, 29, 27, 30, 32, 33, 33, 30, 31, 33, 21, 24, 32, 32, 33, 23, 32, 32, -1, -1, -1, 80, 81, 80, -1, 80, 81, 80, 81, 80, 81, 25, 26, 21, 21, 22, 33, 32, 33, 32, 33, 22, 23, 27, 30, 31, 30, 30, 31, 32, 33, 32, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 25, 28, 31, 30, 31, 21, 22, 32, 33, 21, 22, 23, 26, -1, 30, 31, 30, 32, 33, 31, 31, 30, 32, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, -1, 31, 32, 33, 30, 31, 30, 31, 24, 25, 21, 22, -1, 32, 33, 30, 30, 32, 33, 33, 32, 33, -1, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 31, 32, 33, 33, 31, 27, 28, 24, 25, -1, -1, -1, 32, 32, 32, 33, -1, -1, -1, -1, -1, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 80, 80, 32, 33, 33, 33, 27, -1, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, 32, 30, 31, 80, 80, 81, 80, 81, -1, 80, 81, 80, 81, 33, 33, 80, 81, 80, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, 32, 33, 33, 32, 33, 30, 31, 80, 80, -1, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 80, 80, 81, -1, -1, -1, -1, -1, -1, 32, 32, 33, 32, 33, 32, 33, 32, 32, 33, 30, 31, 32, -1, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 81, 80, 81, 80, -1, 80, 81, 80, 81, 21, 22, 23, 31, 31, 33, 32, 30, 32, 32, 33, 30, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 80, 80, 81, 80, 81, 80, 81, 80, 81, -1, 24, 25, 26, 33, 33, 30, 31, 30, 31, 30, 30, 32, 33, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 31, 80, 81, 81, 81, 80, 81, 80, 81, 27, 21, 22, 23, 23, 32, 33, 32, 30, 31, 32, 30, -1, -1, -1, 80, 80, 81, 80, 81, 80, 81, 80, 21, 22, 23, 81, 80, 81, 80, 81, 80, 81, -1, 25, 26, 26, 25, 32, 33, 32, 30, 31, 32, -1, -1, -1, -1, 30, 22, 23, 31, 80, 80, 81, 24, 25, 26, 24, 24, 80, 81, 80, 81, -1, -1, -1, -1, 21, 22, 23, 30, -1, -1, -1, -1, -1, -1, 30, 31, 32, 32, 22, 23, 33, 25, 80, 78, 28, 29, 21, 21, 22, 24, 80, -1, -1, -1, -1, -1, -1, -1, 26, 32, 33, -1, -1, -1, -1, 30, 32, 33, 30, 31, 32, 33, 32, 21, 23, 29, 25, 26, 24, 24, 25, 30, 30, 31, -1, -1, -1, 80, 81, 81, 79, -1, -1, -1, 80, 81, -1, -1, 22, 23, 31, 30, 31, 30, 32, 21, 26, 27, 28, 29, 21, 30, 31, 32, 32, 33, 31, 29, 80, 81, 80, 81, 80, 81, 80, 81, 80, 81, 81, -1, 32, 32, 22, 23, 22, 23, 32, 32, 33, 28, 29, 26, 30, 30, 31, 30, 30, 31, 24, 25, 29, 29, 80, 80, 81, 80, 81, 80, 81, 80, 81, -1, -1, -1, -1, -1, 28, 29, 24, 25, 32, 27, 30, 31, 30, 32, 33, 31, 24, 25, 27, 28, 22, 23, 30, 31, 80, 81, 80, 81, 80, 81, 81, -1, -1, -1, -1, -1, -1, -1, 21, 22, 26, 31, 32, 33, 32, 24, 25, 25, 27, 28, 24, 24, 25, 25, 32, 33, 29, 21, 81, 80, 81, 80, 81, 81, 80, 81, -1, -1, -1, -1, -1, 25, 29, 31, 30, 31, 24, 25, 28, 28, 25, 26, 27, 79, 79, 78, 79, 78, 29, 24, 25, 26, 80, 81, 80, 81, 80, 81, 80, -1, -1, -1, -1, -1, -1, 25, 21, 22, 27, 28, 29, 27, 28, 79, 78, 79, 78, 79, 78, 79, 78, 79, 28, 29, 25, 21, 81, 80, 81, 80, 81, 80, 81, 80, -1, -1, -1, 28, 24, 25, 27, 28, 22, 23, 79, 78, 79, 78, 79, 78, 79, 78, 79, 78, 79, 27, 28, 24, 25, 26, 81, 81, 80, 81, 80, 81, 80, 81, -1, 21, 22, 23, 24, 24, 25, 26, 21, 22, 78, 79, 78, 79, 78, 79, 78, 79, 29, 29, 32, 27, 28, 29, 24, 25, 81, 80, 81, 80, 81, 80, 81, 24, 25, 26, 27, 27, 28, 29, 24, 25, 26, 28, 79, 78, 79, 78, 25, 27, 27, 28, 29, 30, 31, 30, 24, 25, 80, 81, 80, 81, 80, 81, 80]}], "blocks": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]}