{"mW": 1200, "mH": 960, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["304", 0, 3, 2], ["304", 2, 3, 2], ["304", 1, 3, 2], ["304", 3, 3, 2], ["709", 0, 2, 1], ["450", 0, 1, 1], ["490", 0, 3, 2], ["490", 2, 3, 2], ["490", 1, 3, 2], ["490", 3, 3, 2], ["491", 0, 3, 2], ["491", 2, 3, 2], ["491", 1, 3, 2], ["491", 3, 3, 2], ["492", 0, 1, 1], ["106_1", 0, 3, 3], ["302", 0, 2, 2], ["303", 0, 2, 1]], "layers": [{"type": 2, "data": [4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 13, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 14, -1, 15, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 5, 6, -1, 12, 13, 13, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 21, -1, -1, -1, -1, 15, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 21, -1, -1, -1, -1, 15, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 5, 2, -1, -1, 0, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 5, 7, 7, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 5, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 13, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 14, -1, 12, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 9, -1, -1, -1, 3, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 9, -1, -1, -1, 3, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 5, 1, 1, 1, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 13, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 22, 22, 22, 22, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 19, 19, 14, -1, 20, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 9, -1, -1, -1, -1, -1, 15, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 13, 5, 2, -1, -1, -1, -1, 3, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 23, 16, 21, -1, 15, 5, 7, 7, 2, -1, 15, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 5, 11, 16, 21, -1, 11, 16, 16, 16, 5, 7, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 7, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 16, 16, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 13, 13, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 17, 14, -1, -1, 15, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 4, 16, 16, 16, 16, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 9, -1, -1, 8, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 4, 16, 16, 4, 16, 16, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 9, 1, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 4, 4, 4, 16, 16, 16, 16, 16, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 4, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 17, 23, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 5, 11, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}, {"type": 3, "obj": [[2, "219_1", 30, 690, 36, 30, 0], [2, "334", 129, 574, 40, 32, 0], [2, "333", 34, 530, 38, 35, 0], [2, "1231", 59, 805, 114, 162, 0], [2, "355", 69, -62, 32, 47, 0], [2, "260", 1029, 531, 26, 34, 2], [2, "260", 1013, 505, 26, 34, 2], [2, "232", 906, 516, 46, 45, 2], [2, "232", 930, 527, 46, 45, 2], [2, "233", 927, 477, 44, 54, 2], [2, "149", 938, 789, 58, 51, 0], [2, "328", 547, 715, 32, 29, 0], [2, "219_1", 520, 723, 36, 30, 0], [2, "219_1", 502, 719, 36, 30, 0], [2, "1231", 389, 520, 114, 162, 0], [2, "1231", 425, 510, 114, 162, 0], [2, "1231_1", 232, 758, 114, 162, 0], [2, "1231", 543, 880, 114, 162, 0], [2, "1232", 309, 611, 100, 158, 0], [2, "1231", 316, 515, 114, 162, 0], [2, "1456", 241, 730, 24, 32, 0], [2, "2_1", 756, 472, 90, 66, 2], [2, "1231_1", 343, 553, 114, 162, 0], [2, "1231_1", 870, 891, 114, 162, 0], [2, "1231_1", 478, 528, 114, 162, 0], [2, "2_1", 206, 304, 90, 66, 0], [2, "1384", -120, 25, 46, 66, 0], [2, "1384", -78, 24, 46, 66, 2], [2, "2_1", 104, 391, 90, 66, 0], [2, "1231", 286, 594, 114, 162, 0], [2, "1231_1", -7, 824, 114, 162, 0], [2, "1232", 359, 900, 100, 158, 0], [2, "1231_1", 87, 225, 114, 162, 0], [2, "1231", 32, 246, 114, 162, 0], [2, "219_1", 74, 380, 36, 30, 0], [2, "219_1", 136, 361, 36, 30, 0], [2, "1232", 295, 859, 100, 158, 0], [2, "1231_1", 216, 837, 114, 162, 0], [2, "1232", 173, 825, 100, 158, 0], [2, "1232", 127, 828, 100, 158, 0], [2, "1231_1", 218, 667, 114, 162, 0], [2, "1231", 507, 857, 114, 162, 0], [2, "1231_1", 256, 620, 114, 162, 0], [2, "1231_1", -56, 770, 114, 162, 0], [2, "1231", 528, 588, 114, 162, 0], [2, "1231_1", 566, 515, 114, 162, 0], [2, "1231_1", 826, 872, 114, 162, 0], [2, "1231", 785, 890, 114, 162, 0], [2, "219_1", 320, 733, 36, 30, 0], [2, "219_1", 562, 760, 36, 30, 0], [2, "219_1", 723, 437, 36, 30, 0], [2, "219_1", 699, 434, 36, 30, 0], [2, "219_1", 736, 453, 36, 30, 0], [2, "1231_1", 738, 375, 114, 162, 0], [2, "1231", 479, 677, 114, 162, 0], [2, "1231_1", 407, 882, 114, 162, 0], [2, "1231_1", 460, 857, 114, 162, 0], [2, "2_1", 1018, 380, 90, 66, 2], [2, "475", 948, 757, 30, 42, 0], [2, "471", 967, 788, 52, 47, 0], [2, "150", 887, 818, 36, 37, 0], [2, "149", 913, 803, 58, 51, 0], [2, "471", 905, 816, 52, 47, 0], [2, "151", 918, 791, 30, 25, 0], [2, "472", 864, 810, 38, 27, 0], [2, "473", 888, 789, 28, 38, 2], [2, "233", 951, 488, 44, 54, 2], [2, "239", 940, 472, 80, 36, 2], [2, "237", 920, 509, 36, 22, 2], [2, "237", 943, 521, 36, 22, 2], [2, "233", 973, 498, 44, 54, 2], [2, "232", 957, 539, 46, 45, 2], [2, "237", 972, 535, 36, 22, 2], [2, "236", 949, 511, 26, 22, 2], [2, "260", 354, -19, 26, 34, 2], [2, "260", 320, -18, 26, 34, 0], [2, "238", 368, 134, 30, 42, 0], [2, "1354", 409, -47, 46, 75, 2], [2, "1354", 379, -30, 46, 75, 2], [2, "1354", 343, -20, 46, 75, 2], [2, "237", 378, -52, 36, 22, 0], [2, "237", 358, -43, 36, 22, 0], [2, "237", 339, -34, 36, 22, 0], [2, "237", 397, -11, 36, 22, 0], [2, "237", 365, 5, 36, 22, 0], [2, "457", 366, -49, 70, 59, 2], [2, "169", 360, 388, 36, 49, 0], [2, "169", 395, 388, 36, 49, 2], [2, "348", -23, 10, 70, 61, 0], [2, "346", 22, -6, 72, 71, 0], [2, "346", 59, -25, 72, 71, 0], [2, "347", 39, 37, 60, 45, 0], [2, "356", 48, -33, 30, 30, 0], [2, "356", 26, -22, 30, 30, 0], [2, "354", 3, -27, 30, 44, 0], [2, "349", 39, 30, 22, 62, 2], [2, "352", 14, 0, 44, 54, 0], [2, "347", 96, 5, 60, 45, 0], [2, "350", 111, 5, 58, 24, 2], [2, "352", 87, -35, 44, 54, 0], [2, "347", 68, 20, 60, 45, 0], [2, "980", 42, 19, 58, 41, 2], [2, "334", -56, 663, 40, 32, 0], [2, "334", -19, 619, 40, 32, 0], [2, "334", 3, 592, 40, 32, 0], [2, "334", 37, 646, 40, 32, 0], [2, "334", 128, 539, 40, 32, 0], [2, "334", 32, 570, 40, 32, 0], [2, "333", 15, 618, 38, 35, 0], [2, "333", 96, 543, 38, 35, 0], [2, "333", 83, 522, 38, 35, 0], [2, "332", 17, 553, 36, 38, 0], [2, "332", 58, 511, 36, 38, 0], [2, "332", 53, 538, 36, 38, 0], [2, "332", -44, 630, 36, 38, 2], [2, "333", 42, 615, 38, 35, 0], [2, "334", 122, 556, 40, 32, 0], [2, "333", -41, 600, 38, 35, 0], [2, "332", -16, 591, 36, 38, 0], [2, "332", 23, 595, 36, 38, 0], [2, "332", 123, 518, 36, 38, 0], [2, "332", 92, 504, 36, 38, 0], [2, "332", 148, 546, 36, 38, 0], [2, "334", 57, 596, 40, 32, 0], [2, "219_1", 74, 646, 36, 30, 0], [2, "219_1", 111, 642, 36, 30, 0], [2, "219_1", 23, 702, 36, 30, 0], [2, "219_1", 89, 645, 36, 30, 0], [2, "219_1", 579, 742, 36, 30, 0], [2, "10412", 0, 0, 1200, 960, 0], [2, "10412", 1, 1, 1200, 960, 0]]}, {"type": 4, "obj": [[2, "1231_1", 105, -121, 114, 162, 0], [2, "1231", 829, -98, 114, 162, 0], [2, "1231", -2, -89, 114, 162, 0], [2, "1231_1", 893, -88, 114, 162, 0], [2, "1231", 1100, -47, 114, 162, 0], [2, "1231", 956, -40, 114, 162, 0], [2, "1231_1", 1032, -38, 114, 162, 0], [2, "1231", 985, 9, 114, 162, 0], [2, "429_2", 673, 130, 64, 63, 0], [2, "1231_1", 1089, 33, 114, 162, 0], [2, "1231_1", -53, 40, 114, 162, 0], [2, "1231", 1016, 56, 114, 162, 0], [2, "219", 3, 232, 36, 30, 2], [2, "1231", 1122, 104, 114, 162, 0], [2, "1231_1", 1063, 124, 114, 162, 0], [2, "1231_1", 736, 128, 114, 162, 0], [2, "1231_1", -27, 138, 114, 162, 0], [2, "1239", 913, 217, 60, 94, 0], [4, 4, 307, 322, 1, 4005], [2, "1231", 756, 208, 114, 162, 0], [2, "1231", 1112, 230, 114, 162, 0], [2, "219", 827, 395, 36, 30, 0], [2, "329", 591, 391, 42, 37, 2], [2, "1231_1", 567, 268, 114, 162, 0], [2, "1231_1", 645, 293, 114, 162, 0], [2, "329", 677, 422, 42, 37, 0], [2, "327_1", 426, 450, 30, 22, 2], [2, "1231_1", -50, 334, 114, 162, 0], [2, "429_2", 12, 439, 64, 63, 2], [2, "328_1", 669, 487, 32, 29, 0], [2, "327_1", 712, 538, 30, 22, 2], [2, "1231_1", 813, 412, 114, 162, 0], [2, "219_1", 873, 549, 36, 30, 0], [2, "219_1", 65, 633, 36, 30, 0], [2, "331", 37, 568, 104, 108, 2], [2, "328_1", 722, 676, 32, 29, 0], [2, "331", -44, 628, 104, 108, 2], [2, "1231", 721, 582, 114, 162, 0], [2, "1231", 800, 594, 114, 162, 0], [2, "1231", 800, 594, 114, 162, 0], [2, "328_1", 621, 729, 32, 29, 0], [2, "429_2", -24, 710, 64, 63, 0], [2, "2_1", 1035, 726, 90, 66, 2], [2, "1231_1", 746, 636, 114, 162, 0], [2, "1231_1", 1058, 636, 114, 162, 0], [2, "1231", 1106, 653, 114, 162, 0], [2, "357", 948, 802, 48, 91, 0], [2, "357", 883, 836, 48, 91, 0]]}, {"type": 3, "obj": [[2, "219_1", 841, 78, 36, 30, 2], [2, "328", 853, 92, 32, 29, 0], [2, "219_1", 119, 627, 36, 30, 0], [2, "219_1", 61, 669, 36, 30, 0], [2, "219_1", 112, 607, 36, 30, 0], [2, "329", 659, 597, 42, 37, 0], [2, "309", 22, 134, 46, 33, 0], [2, "309", 80, 88, 46, 33, 0], [2, "308", 77, 123, 52, 22, 0], [2, "470", 114, 25, 18, 62, 2], [2, "470", 104, 30, 18, 62, 2], [2, "345", 81, 39, 24, 73, 0], [2, "470", 58, 52, 18, 62, 2], [2, "329", 491, 99, 42, 37, 2], [2, "123", 419, 27, 58, 42, 0], [2, "257", 362, -20, 14, 66, 2], [2, "257", 355, -16, 14, 66, 2], [2, "257", 348, -12, 14, 66, 2], [2, "257", 341, -8, 14, 66, 2], [2, "257", 334, -4, 14, 66, 2], [2, "257", 327, 0, 14, 66, 2], [2, "257", 372, -22, 14, 66, 0], [2, "257", 378, -18, 14, 66, 0], [2, "257", 384, -15, 14, 66, 0], [2, "257", 390, -12, 14, 66, 0], [2, "257", 396, -9, 14, 66, 0], [2, "257", 403, -5, 14, 66, 0], [2, "257", 409, -1, 14, 66, 0], [2, "257", 415, 2, 14, 66, 0], [2, "257", 421, 0, 14, 66, 0], [2, "704", 319, 0, 82, 76, 0], [2, "1231_1", 726, -110, 114, 162, 0], [2, "125", 766, -14, 18, 70, 0], [2, "263", 691, -11, 34, 34, 2], [2, "257", 1021, 527, 14, 66, 2], [2, "257", 1014, 516, 14, 66, 2], [2, "257", 1007, 507, 14, 66, 2], [2, "257", 1000, 519, 14, 66, 2], [2, "435_1", 755, 837, 50, 77, 2], [2, "439_1", 977, 768, 64, 42, 0], [2, "391_1", 1057, 586, 86, 55, 2], [2, "439_1", 833, 519, 64, 42, 2], [2, "398_1", 561, 246, 58, 78, 0], [2, "319_2", 523, 238, 62, 65, 0], [2, "1387", 525, 350, 50, 38, 2], [2, "434_1", 933, 421, 70, 54, 2], [2, "325_1", 448, 774, 50, 37, 2], [2, "391_1", 386, 286, 86, 55, 0], [2, "433_1", 833, 780, 62, 61, 2], [2, "435_1", 729, 300, 50, 77, 0], [2, "434_1", 756, 369, 70, 54, 0], [2, "329", 783, 363, 42, 37, 0], [2, "325_1", 299, 190, 50, 37, 0], [2, "325_1", 210, 615, 50, 37, 2], [2, "174", 995, 149, 68, 33, 1], [2, "174", 756, 124, 68, 33, 1], [2, "325_1", 251, 54, 50, 37, 2], [2, "437_1", 379, 331, 20, 19, 0], [2, "391_1", 372, 220, 86, 55, 0], [2, "434_1", 70, 270, 70, 54, 2], [2, "434_1", 620, 161, 70, 54, 0], [2, "439_1", 676, 411, 64, 42, 2], [2, "399_1", 188, 225, 58, 72, 2], [2, "391_1", 1054, 611, 86, 55, 0], [2, "392_1", 230, 272, 118, 69, 0], [2, "398_1", 231, 220, 58, 78, 2], [2, "435_1", 625, 324, 50, 77, 2], [2, "328_1", 285, 305, 32, 29, 0], [2, "398_1", 338, 213, 58, 78, 2], [2, "399_1", 302, 256, 58, 72, 0], [2, "399_1", 301, 209, 58, 72, 0], [2, "399_1", 258, 210, 58, 72, 2], [2, "398_1", 84, 241, 58, 78, 2], [2, "420_1", 402, 307, 16, 13, 0], [2, "421_1", 406, 334, 14, 11, 2], [2, "420_1", 329, 277, 16, 13, 0], [2, "325_1", 418, 221, 50, 37, 0], [2, "420_1", 322, 200, 16, 13, 0], [2, "420_1", 258, 300, 16, 13, 0], [2, "439_1", 614, 171, 64, 42, 2], [2, "436_1", 658, 237, 34, 28, 2], [2, "319_2", 634, 249, 62, 65, 0], [2, "440_1", 662, 203, 34, 34, 2], [2, "398_1", 526, 263, 58, 78, 2], [2, "399_1", 485, 235, 58, 72, 0], [2, "399_1", 477, 254, 58, 72, 0], [2, "398_1", 541, 296, 58, 78, 0], [2, "319_2", 589, 312, 62, 65, 0], [2, "421_1", 495, 377, 14, 11, 0], [2, "436_1", 608, 258, 34, 28, 2], [2, "319_2", 590, 266, 62, 65, 0], [2, "435_1", 780, 375, 50, 77, 0], [2, "436_1", 456, 316, 34, 28, 2], [2, "319_2", 578, 282, 62, 65, 0], [2, "319_2", 523, 238, 62, 65, 0], [2, "412_1", 243, 221, 64, 100, 2], [2, "325_1", 577, 125, 50, 37, 0], [2, "421_1", 567, 332, 14, 11, 2], [2, "421_1", 506, 377, 14, 11, 2], [2, "421_1", 853, 400, 14, 11, 0], [2, "391_1", 369, 234, 86, 55, 2], [2, "434_1", 865, 420, 70, 54, 2], [2, "436_1", 805, 507, 34, 28, 0], [2, "433_1", 685, 177, 62, 61, 0], [2, "440_1", 744, 447, 34, 34, 2], [2, "441_1", 663, 398, 28, 21, 2], [2, "420_1", 621, 264, 16, 13, 0], [2, "420_1", 655, 246, 16, 13, 0], [2, "422_1", 770, 579, 16, 14, 0], [2, "421_1", 830, 539, 14, 11, 2], [2, "421_1", 923, 457, 14, 11, 0], [2, "420_1", 576, 176, 16, 13, 2], [2, "21", 671, 402, 28, 24, 2], [2, "420_1", 497, 387, 16, 13, 0], [2, "421_1", 669, 258, 14, 11, 2], [2, "420_1", 663, 241, 16, 13, 0], [2, "422_1", 371, 457, 16, 14, 0], [2, "440_1", 721, 280, 34, 34, 0], [2, "420_1", 512, 431, 16, 13, 0], [2, "426", 1165, 621, 26, 22, 0], [2, "398_1", 112, 239, 58, 78, 0], [2, "398_1", 185, 259, 58, 78, 2], [2, "319_2", 129, 239, 62, 65, 2], [2, "440_1", 1068, 575, 34, 34, 2], [2, "319_2", 136, 270, 62, 65, 2], [2, "412_1", 499, 257, 64, 100, 2], [2, "325_1", 540, 375, 50, 37, 2], [2, "412_1", 218, 237, 64, 100, 2], [2, "399_1", 140, 233, 58, 72, 2], [2, "319_2", 909, 394, 62, 65, 0], [2, "422_1", 25, 334, 16, 14, 0], [2, "437_1", 643, 402, 20, 19, 2], [2, "391_1", 49, 344, 86, 55, 3], [2, "439_1", 865, 835, 64, 42, 0], [2, "436_1", 802, 931, 34, 28, 0], [2, "441_1", 900, 836, 28, 21, 0], [2, "435_1", 743, 197, 50, 77, 0], [2, "439_1", 729, 267, 64, 42, 0], [2, "435_1", 1035, 632, 50, 77, 2], [2, "434_1", 914, 695, 70, 54, 2], [2, "435_1", 877, 719, 50, 77, 2], [2, "391_1", 802, 797, 86, 55, 2], [2, "441_1", 961, 801, 28, 21, 0], [2, "434_1", 657, 889, 70, 54, 2], [2, "435_1", 620, 899, 50, 77, 2], [2, "439_1", 918, 806, 64, 42, 0], [2, "440_1", 844, 868, 34, 34, 0], [2, "325_1", 548, 669, 50, 37, 2], [2, "325_1", 668, 638, 50, 37, 2], [2, "441_1", 1022, 559, 28, 21, 2], [2, "422_1", 250, 399, 16, 14, 2], [2, "21", 489, 387, 28, 24, 2], [2, "439_1", 821, 866, 64, 42, 0], [2, "325_1", -23, 443, 50, 37, 2], [2, "329_1", -24, 423, 42, 37, 2], [2, "325_1", 858, 5, 50, 37, 2], [2, "21", 797, 37, 28, 24, 0], [2, "420_1", 776, 56, 16, 13, 0], [2, "174", 953, 75, 68, 33, 1], [2, "422_1", 832, 527, 16, 14, 0], [2, "392_1", -37, 298, 118, 69, 2], [2, "263", 301, 653, 34, 34, 0], [2, "219_1", 606, 169, 36, 30, 0], [2, "219_1", 65, 633, 36, 30, 0], [2, "263", 587, 159, 34, 34, 0], [2, "263", 3, 627, 34, 34, 0], [2, "263", 745, 265, 34, 34, 0], [2, "263", 811, 28, 34, 34, 0], [2, "219_1", 155, 394, 36, 30, 0], [2, "263", 191, 21, 34, 34, 0], [2, "263", 961, 153, 34, 34, 0], [2, "328", 235, 11, 32, 29, 0], [2, "328", 777, 224, 32, 29, 0], [2, "420_1", 444, 218, 16, 13, 0], [2, "263", 216, 522, 34, 34, 0], [2, "219_1", 269, 350, 36, 30, 2], [2, "219_1", 858, 106, 36, 30, 2], [2, "328", 275, 89, 32, 29, 0], [2, "328", 604, -14, 32, 29, 0], [2, "420_1", 644, -1, 16, 13, 0], [2, "325_1", 454, -12, 50, 37, 0], [2, "328_1", 221, 124, 32, 29, 0], [2, "174", 34, 217, 68, 33, 1], [2, "420_1", 282, 125, 16, 13, 0], [2, "420_1", 545, 267, 16, 13, 0], [2, "420_1", 650, 201, 16, 13, 0], [2, "328", 451, 267, 32, 29, 0], [2, "439_1", 1034, 723, 64, 42, 0], [2, "329", 224, 418, 42, 37, 2], [2, "441_1", 855, 767, 28, 21, 0], [2, "328", 616, 382, 32, 29, 0], [2, "263", 623, 235, 34, 34, 0], [2, "420_1", 598, 193, 16, 13, 2], [2, "1235", 645, 321, 12, 18, 1], [2, "1235", 658, 302, 12, 18, 1], [2, "329", 659, 597, 42, 37, 0], [2, "1235", 569, 322, 12, 18, 0], [2, "1236", 596, 345, 42, 43, 0], [2, "1236", 634, 375, 42, 43, 2], [2, "1235", 840, 526, 12, 18, 1], [2, "328", 472, 671, 32, 29, 0], [2, "1236", 651, 271, 42, 43, 0], [2, "1235", 589, 295, 12, 18, 0], [2, "1235", 207, 344, 12, 18, 0], [2, "1235", 682, 302, 12, 18, 1], [2, "429_2", 548, 336, 64, 63, 0], [2, "328", 506, 345, 32, 29, 0], [2, "420_1", 89, 418, 16, 13, 0], [2, "1238", 134, 607, 56, 50, 2], [2, "599", 301, 765, 40, 34, 0], [2, "1236", 147, 621, 42, 43, 0], [2, "420_1", 50, 641, 16, 13, 2], [2, "1236", 805, 487, 42, 43, 0], [2, "21", 965, 402, 28, 24, 0], [2, "85_1", 880, 389, 48, 53, 0], [2, "1236", 646, 289, 42, 43, 0], [2, "1236", 616, 292, 42, 43, 0], [2, "1236", 131, 312, 42, 43, 0], [2, "50", 832, 445, 8, 23, 2], [2, "439_1", 1135, 707, 64, 42, 2], [2, "329", 991, 762, 42, 37, 2], [2, "440_1", 799, 898, 34, 34, 0], [2, "399_1", -11, 250, 58, 72, 0], [2, "399_1", 38, 250, 58, 72, 2], [2, "391_1", 46, 316, 86, 55, 2], [2, "391_1", 354, 258, 86, 55, 2], [2, "391_1", 337, 289, 86, 55, 0], [2, "329", -25, 289, 42, 37, 2], [2, "412_1", 128, 254, 64, 100, 2], [2, "412_1", 98, 266, 64, 100, 0], [2, "328", 132, 258, 32, 29, 0], [2, "263", 132, 318, 34, 34, 0], [2, "1387", 180, 326, 50, 38, 2], [2, "399_1", 442, 228, 58, 72, 0], [2, "440_1", 405, 275, 34, 34, 0], [2, "411_1", 452, 273, 44, 40, 0], [2, "413", 429, 246, 44, 72, 0], [2, "319_2", 417, 282, 62, 65, 0], [2, "325_1", 115, 349, 50, 37, 2], [2, "329", -20, 179, 42, 37, 2], [2, "328", 767, 346, 32, 29, 0], [2, "1385", 637, 300, 68, 62, 2], [2, "441_1", 866, 854, 28, 21, 0], [2, "437_1", 815, 740, 20, 19, 0], [2, "437_1", 1105, 698, 20, 19, 0], [2, "441_1", 1123, 696, 28, 21, 2], [2, "436_1", 86, 344, 34, 28, 2], [2, "429_2", -27, 314, 64, 63, 0], [2, "329", 11, 328, 42, 37, 2], [2, "1387", 31, 352, 50, 38, 2], [2, "1236", 65, 331, 42, 43, 0], [2, "440_1", 782, 479, 34, 34, 2], [2, "437_1", 765, 472, 20, 19, 0], [2, "328", 470, 358, 32, 29, 0], [2, "325_1", 687, 446, 50, 37, 2], [2, "329", 469, 298, 42, 37, 1], [2, "327_1", 456, 322, 30, 22, 2], [2, "328", 853, 414, 32, 29, 0], [2, "599", 780, 248, 40, 34, 0], [2, "328", 814, 383, 32, 29, 0], [2, "173_1", 305, 316, 70, 45, 0], [2, "328", 200, 367, 32, 29, 0], [2, "325_1", 1097, 291, 50, 37, 2], [2, "328", 451, 210, 32, 29, 0], [2, "329", 945, 686, 42, 37, 2], [2, "1236", 853, 734, 42, 43, 0], [2, "1235", 751, 476, 12, 18, 1], [2, "391_1", 772, 815, 86, 55, 2], [2, "434_1", 705, 876, 70, 54, 2], [2, "440_1", 1075, 703, 34, 34, 0], [2, "440_1", 835, 869, 34, 34, 0], [2, "440_1", 1013, 754, 34, 34, 0], [2, "219_1", 949, 812, 36, 30, 0], [2, "329", 1102, 705, 42, 37, 0], [2, "325_1", 891, 862, 50, 37, 2], [2, "411_1", 1038, 746, 44, 40, 0], [2, "219_1", 642, 603, 36, 30, 2], [2, "328", 603, 865, 32, 29, 0], [2, "219_1", 782, 488, 36, 30, 2], [2, "219_1", 686, 873, 36, 30, 2], [2, "219_1", 756, 831, 36, 30, 2], [2, "219_1", 1015, 764, 36, 30, 2], [2, "329", 715, 712, 42, 37, 2], [2, "219_1", 982, 791, 36, 30, 2], [2, "420_1", 929, 838, 16, 13, 0], [2, "392_1", 939, 679, 118, 69, 2], [2, "437_1", 892, 547, 20, 19, 0], [2, "437_1", 912, 549, 20, 19, 0], [2, "437_1", 932, 552, 20, 19, 0], [2, "437_1", 951, 557, 20, 19, 0], [2, "437_1", 972, 559, 20, 19, 0], [2, "437_1", 992, 560, 20, 19, 0], [2, "437_1", 1047, 570, 20, 19, 0], [2, "434_1", 1142, 411, 70, 54, 2], [2, "434_1", 809, 421, 70, 54, 0], [2, "434_1", 984, 422, 70, 54, 0], [2, "434_1", 1054, 422, 70, 54, 2], [2, "319_2", 1106, 400, 62, 65, 0], [2, "328", 974, 415, 32, 29, 0], [2, "437_1", 1007, 559, 20, 19, 0], [2, "1231", 706, 90, 114, 162, 0], [2, "329", 738, 746, 42, 37, 2], [2, "1236", 726, 687, 42, 43, 0], [2, "549", 345, 418, 34, 28, 0], [2, "552", 337, 444, 22, 15, 2], [2, "552", 398, 487, 22, 15, 0], [2, "325_1", 461, 336, 50, 37, 2], [2, "553", 337, 459, 14, 8, 0], [2, "257", 1030, 545, 14, 66, 2], [2, "257", 1019, 549, 14, 66, 2], [2, "257", 1012, 553, 14, 66, 2], [2, "257", 1005, 557, 14, 66, 2], [2, "257", 913, 541, 14, 66, 2], [2, "257", 920, 544, 14, 66, 2], [2, "257", 927, 548, 14, 66, 2], [2, "257", 934, 552, 14, 66, 2], [2, "257", 941, 555, 14, 66, 2], [2, "257", 948, 558, 14, 66, 2], [2, "257", 955, 561, 14, 66, 2], [2, "257", 993, 529, 14, 66, 2], [2, "257", 962, 564, 14, 66, 2], [2, "257", 969, 568, 14, 66, 2], [2, "257", 976, 571, 14, 66, 2], [2, "257", 998, 561, 14, 66, 2], [2, "257", 991, 566, 14, 66, 2], [2, "240", 1026, 544, 12, 13, 2], [2, "240", 1000, 558, 12, 13, 2], [2, "240", 1013, 551, 12, 13, 2], [2, "257", 983, 574, 14, 66, 2], [2, "240", 987, 565, 12, 13, 2], [2, "234", 997, 568, 34, 63, 2], [2, "33", 924, 564, 22, 35, 0], [2, "34", 948, 583, 30, 53, 2], [2, "108", 935, 592, 6, 46, 0], [2, "108", 961, 603, 6, 46, 0], [2, "229", 930, 568, 60, 42, 2], [2, "231", 935, 589, 6, 6, 0], [2, "231", 960, 599, 6, 6, 0], [2, "437_1", 592, -35, 20, 19, 0], [2, "437_1", 667, -28, 20, 19, 0], [2, "257", 690, -42, 14, 66, 2], [2, "257", 679, -38, 14, 66, 2], [2, "257", 672, -34, 14, 66, 2], [2, "257", 665, -30, 14, 66, 2], [2, "257", 580, -43, 14, 66, 2], [2, "257", 587, -39, 14, 66, 2], [2, "257", 594, -35, 14, 66, 2], [2, "257", 601, -32, 14, 66, 2], [2, "257", 608, -29, 14, 66, 2], [2, "257", 615, -26, 14, 66, 2], [2, "257", 622, -23, 14, 66, 2], [2, "257", 629, -19, 14, 66, 2], [2, "257", 636, -16, 14, 66, 2], [2, "257", 658, -26, 14, 66, 2], [2, "257", 651, -21, 14, 66, 2], [2, "240", 686, -43, 12, 13, 2], [2, "240", 660, -29, 12, 13, 2], [2, "240", 673, -36, 12, 13, 2], [2, "257", 643, -13, 14, 66, 2], [2, "240", 647, -22, 12, 13, 2], [2, "234", 657, -19, 34, 63, 2], [2, "47", 475, 92, 54, 63, 0], [2, "328", 555, 217, 32, 29, 0], [2, "34", 598, -8, 30, 53, 2], [2, "34", 598, -20, 30, 53, 2], [2, "116", 679, 7, 46, 39, 0], [2, "114", 630, 21, 18, 32, 0], [2, "124", 637, -49, 142, 70, 0], [2, "125", 717, 8, 18, 70, 0], [2, "1351", 594, 27, 34, 26, 0], [2, "437_1", 389, 17, 20, 19, 0], [2, "437_1", 404, 16, 20, 19, 0], [2, "257", 428, 5, 14, 66, 2], [2, "257", 416, 7, 14, 66, 2], [2, "257", 409, 11, 14, 66, 2], [2, "257", 402, 15, 14, 66, 2], [2, "257", 325, 9, 14, 66, 2], [2, "257", 331, 13, 14, 66, 2], [2, "257", 337, 16, 14, 66, 2], [2, "257", 343, 19, 14, 66, 2], [2, "257", 349, 22, 14, 66, 2], [2, "257", 356, 26, 14, 66, 2], [2, "257", 362, 30, 14, 66, 2], [2, "257", 368, 33, 14, 66, 2], [2, "257", 395, 19, 14, 66, 2], [2, "257", 388, 23, 14, 66, 2], [2, "240", 423, 1, 12, 13, 2], [2, "240", 397, 15, 12, 13, 2], [2, "240", 410, 8, 12, 13, 2], [2, "240", 384, 22, 12, 13, 2], [2, "33", 343, 35, 22, 35, 0], [2, "34", 387, 41, 30, 53, 0], [2, "108", 378, 168, 6, 46, 0], [2, "257", 381, 27, 14, 66, 2], [2, "257", 374, 31, 14, 66, 2], [2, "257", 375, 37, 14, 66, 2], [2, "420_1", 378, 202, 16, 13, 0], [2, "329", 342, 174, 42, 37, 2], [2, "231", 564, 187, 6, 6, 0], [2, "959", 344, 67, 40, 22, 0], [2, "980", 383, 12, 58, 41, 0], [2, "959", 323, 26, 40, 22, 2], [2, "420_1", 476, 144, 16, 13, 0], [2, "117", 566, 2, 22, 27, 0], [2, "3063", 318, 64, 22, 19, 0], [2, "3356", 649, 21, 44, 49, 2], [2, "3408", 364, 12, 42, 26, 0], [2, "3408", 394, -4, 42, 26, 0], [2, "328_1", 362, 78, 32, 29, 0], [2, "168", 382, 420, 28, 51, 0], [2, "549", 331, 469, 34, 28, 2], [2, "550", 418, 467, 42, 28, 0], [2, "552", 437, 452, 22, 15, 0], [2, "552", 398, 487, 22, 15, 0], [2, "552", 363, 490, 22, 15, 0], [2, "552", 363, 490, 22, 15, 0], [2, "552", 380, 494, 22, 15, 0], [2, "329", 486, 392, 42, 37, 2], [2, "420_1", 390, 487, 16, 13, 2], [2, "420_1", 351, 436, 16, 13, 0], [2, "422_1", 351, 484, 16, 14, 0], [2, "420_1", 330, 458, 16, 13, 2], [2, "553", 418, 496, 14, 8, 0], [2, "553", 443, 487, 14, 8, 0], [2, "173_1", 356, 320, 70, 45, 0], [2, "3025", 355, 346, 92, 53, 0], [2, "3025", 307, 348, 92, 53, 0], [2, "3025", 262, 332, 92, 53, 2], [2, "263", 1114, 299, 34, 34, 0], [2, "512", 937, 159, 36, 62, 0], [2, "512", 827, 168, 36, 62, 2], [2, "1238", 918, 293, 56, 50, 0], [2, "219_1", 925, 192, 36, 30, 0], [2, "328", 906, 311, 32, 29, 0], [2, "219_1", 928, 325, 36, 30, 0], [2, "420_1", 959, 208, 16, 13, 0], [2, "329", 958, 304, 42, 37, 0], [2, "1235", 926, 302, 12, 18, 1], [2, "1236", 829, 190, 42, 43, 2], [2, "978", 1032, 221, 66, 56, 2], [2, "329", 1003, 213, 42, 37, 2], [2, "219_1", 1065, 250, 36, 30, 0], [2, "1236", 1043, 223, 42, 43, 0], [2, "976", 998, 353, 34, 37, 0], [2, "1236", 1000, 352, 42, 43, 0], [2, "977", 879, 357, 18, 36, 0], [2, "219_1", 866, 363, 36, 30, 0], [2, "1236", 1005, 577, 42, 43, 0], [2, "470", 4, 48, 18, 62, 0], [2, "470", 21, 57, 18, 62, 0], [2, "818", 10, 94, 30, 37, 2], [2, "470", 46, 59, 18, 62, 2], [2, "818", 46, 95, 30, 37, 0], [2, "814", 74, 58, 16, 61, 2], [2, "818", 103, 65, 30, 37, 0], [2, "814", 102, 44, 16, 61, 2], [2, "841", 39, 66, 16, 26, 0], [2, "770", 33, 113, 28, 25, 0], [2, "841", 39, 79, 16, 26, 0], [2, "841", 39, 97, 16, 26, 0], [2, "470", 1, 48, 18, 62, 0], [2, "959", 53, 82, 22, 41, 5], [2, "980", 33, 56, 58, 41, 2], [2, "959", 117, 45, 22, 41, 6], [2, "841", 130, 27, 16, 26, 0], [2, "770", 124, 74, 28, 25, 0], [2, "841", 130, 40, 16, 26, 0], [2, "841", 130, 58, 16, 26, 0], [2, "306", 55, 134, 46, 25, 0], [2, "512", 54, 90, 36, 62, 2], [2, "306", 127, 97, 46, 25, 0], [2, "219_1", 99, 77, 36, 30, 0], [2, "512", 125, 53, 36, 62, 2], [2, "420_1", 132, 108, 16, 13, 0], [2, "1236", 52, 112, 42, 43, 0], [2, "422_1", 92, 109, 16, 14, 0], [2, "8", 121, 669, 38, 29, 2], [2, "330", 79, 676, 60, 49, 0], [2, "8", 60, 703, 38, 29, 2], [2, "219_1", 95, 619, 36, 30, 0], [2, "219_1", 119, 627, 36, 30, 0], [2, "219_1", 14, 670, 36, 30, 0], [2, "219_1", 39, 675, 36, 30, 0], [2, "328", 229, 538, 32, 29, 0], [2, "328", 634, 670, 32, 29, 0], [2, "329", 654, 673, 42, 37, 0], [2, "328_1", 159, 183, 32, 29, 0], [2, "174", 852, 52, 68, 33, 1], [2, "328", 827, 55, 32, 29, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, -1, -1, 75, -1, -1, -1, -1, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, -1, -1, 68, 74, -1, -1, -1, -1, 75, 76, -1, -1, 76, -1, -1, -1, -1, -1, 82, 81, -1, -1, -1, 8, 7, 6, -1, 15, 22, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 82, 76, 76, 68, 70, 70, 70, 65, 71, 74, 81, -1, -1, 83, 68, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 77, 20, 19, 13, 82, 15, 22, 22, 16, 10, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 75, 68, 70, 64, 64, 64, 65, 99, 99, 99, 99, 99, 66, 84, -1, -1, 95, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 95, 93, -1, 75, 68, 70, 20, 19, 19, 19, 23, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, 78, 72, 99, 99, 99, 99, 99, 59, 52, 53, 59, 58, 62, 56, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 72, 99, 99, -1, -1, -1, 20, 23, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 99, 99, 59, 52, 52, 62, 85, 56, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 72, 99, 99, 74, 76, -1, -1, 15, 10, 10, -1, -1, -1, -1, -1, -1, -1, -1, 58, 58, 58, 62, 96, 87, 88, 93, 75, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 23, 16, -1, -1, 53, 99, 63, 74, -1, -1, 20, 23, 16, 16, -1, -1, -1, -1, -1, -1, -1, 87, 88, 88, 88, 93, -1, -1, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, -1, 17, 14, 20, 19, 19, 14, -1, 58, 57, 71, 74, 91, 81, 20, 23, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, -1, 15, -1, -1, -1, 75, 68, 69, 99, 66, 91, 96, -1, 20, 23, 16, -1, -1, -1, -1, -1, 7, 6, -1, -1, -1, 15, 10, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 17, -1, -1, -1, -1, 78, 60, 99, 99, 66, 81, 93, -1, -1, -1, 16, 16, -1, -1, -1, -1, 22, -1, 7, 6, -1, 12, 16, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 93, 75, 72, 99, 99, 85, -1, -1, -1, -1, 15, 14, -1, 0, -1, -1, 75, 56, 58, 52, 62, 84, 76, 76, 76, 77, 91, 91, 10, -1, -1, -1, 23, 22, 22, 21, 8, 7, 6, 7, 6, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, 78, 56, 57, 99, 99, -1, -1, -1, -1, -1, -1, -1, 3, -1, -1, 78, 67, 68, 64, 74, 96, 79, 68, 64, 74, 80, 81, -1, 10, -1, -1, 12, 13, 19, -1, -1, 13, 14, -1, 5, -1, -1, -1, -1, -1, -1, -1, -1, 10, -1, -1, -1, -1, 52, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 68, 65, 99, 66, 96, 79, 72, 99, 63, 74, 84, -1, -1, -1, -1, -1, 8, -1, 17, 21, 0, 1, 7, 6, 22, -1, 22, 22, 10, 10, 10, -1, -1, -1, 22, 22, 22, -1, -1, -1, -1, -1, 7, 6, -1, 8, 7, 19, -1, 90, 60, 99, 59, 62, 96, -1, 56, 53, 59, 62, 93, -1, -1, -1, -1, 16, 17, 19, 19, 18, 3, 10, 16, 16, 22, 22, 22, 10, 10, 10, 10, 17, 13, 13, 13, 13, 16, 16, 16, -1, 8, 7, 17, 18, -1, -1, 19, 19, -1, 78, 72, 99, 66, 92, 93, -1, 90, 56, 62, 96, -1, -1, -1, -1, -1, 13, 14, -1, 22, 22, 11, 16, 17, 23, 16, 16, 17, 13, 13, 13, 13, 18, -1, -1, -1, -1, 20, 23, 16, 23, 11, 10, 5, 6, -1, -1, -1, -1, -1, 78, 72, 59, 62, 96, -1, -1, 87, 88, 88, 89, -1, -1, -1, -1, -1, -1, 22, 22, 16, 16, 17, 13, 6, 12, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, 16, 1, 6, 22, 18, -1, -1, -1, -1, -1, 61, 60, 59, 61, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 22, 16, 16, 16, 18, -1, -1, 21, -1, -1, -1, -1, -1, -1, -1, 99, -1, -1, -1, -1, -1, 12, 19, 19, 23, 22, 21, -1, 2, -1, -1, 15, 19, -1, 56, 62, 8, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, -1, 16, 16, 16, 16, 16, 17, 14, -1, -1, -1, -1, -1, -1, -1, -1, 76, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 16, 19, -1, -1, 20, 23, -1, 16, 17, -1, -1, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 16, 17, 13, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 16, 16, -1, -1, 20, 23, 17, 19, 23, 22, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 18, 17, 14, -1, -1, -1, -1, -1, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 16, 16, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 16, 16, 5, 6, -1, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 16, 16, 16, -1, -1, 8, 11, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 98, 88, 56, 58, 62, 91, 93, -1, -1, -1, -1, -1, 90, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 19, 23, 16, 22, 22, 11, 16, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, 64, 65, 66, -1, -1, -1, -1, 87, 88, -1, -1, -1, -1, -1, 83, 82, 81, -1, -1, 12, 19, 23, 22, 10, 22, 22, 16, 16, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, 75, 3, 16, 16, 5, 6, -1, -1, -1, -1, 83, 65, 99, 59, 62, 0, 1, 2, 0, 1, 6, -1, 94, 93, -1, -1, 90, 97, 96, -1, -1, -1, -1, 20, 23, 22, 22, 22, 16, 16, 16, 9, -1, -1, -1, -1, -1, -1, -1, -1, 68, 70, 69, 71, 16, 9, -1, -1, -1, 75, 85, 51, 52, 62, 61, 12, 13, 22, 16, 16, 16, 16, 16, -1, 16, 21, 95, 94, 93, -1, -1, -1, -1, -1, 20, 19, 19, 23, 16, 17, 13, 18, -1, -1, -1, -1, -1, -1, 75, 76, 16, 16, 99, 16, 16, 9, -1, -1, -1, 78, 22, 66, 92, 94, 93, -1, 16, 16, 16, 16, 16, 16, 16, 16, 17, -1, -1, -1, -1, -1, -1, -1, 85, 85, -1, -1, -1, 20, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 16, 16, 16, 16, 17, 14, -1, -1, 75, 85, 58, 22, 22, 22, 22, -1, -1, 16, 16, 16, 16, 16, 16, 9, -1, -1, -1, -1, -1, -1, -1, 55, 85, -1, -1, -1, 85, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 16, 16, 16, 16, 16, 17, 14, -1, -1, 83, 16, 16, 16, 16, 16, 16, 17, 14, -1, 15, 17, 16, 8, 7, 1, 5, 6, -1, -1, -1, -1, -1, -1, 94, -1, 22, 85, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 16, 16, 16, 17, 18, -1, -1, 83, 85, 16, 16, 16, 91, 22, 17, 14, -1, -1, 15, 16, 7, 11, 22, 22, 16, 5, 6, -1, -1, -1, -1, -1, -1, 20, 23, 22, 22, 54, 96, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 16, 16, 17, 19, 18, 74, 77, -1, 78, 85, 16, 17, 16, 91, 19, 14, -1, -1, 0, 11, 16, 16, 22, 22, 23, 17, 19, 18, -1, -1, -1, -1, -1, -1, -1, 20, 19, -1, 48, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 17, 13, 14, -1, 60, 66, 80, 76, 77, 20, 19, 19, 91, 91, 76, 76, 77, 75, 3, 16, 16, 16, 16, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 49, -1, -1, 16, 16, 16, -1, -1, -1, -1, 17, 18, -1, 90, 68, 69, 71, 74, 94, 93, -1, -1, 79, 91, 79, 79, 67, 84, 78, 12, 23, 16, 16, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, 88, 34, -1, -1, 48, 48, 49, -1, 12, 13, 13, 13, 23, 16, 16, 18, -1, -1, 87, 72, 99, 59, 62, -1, 79, 79, 79, 91, 91, 79, 79, 92, 89, -1, -1, 20, 23, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 91, 91, 88, 89, -1, -1, -1, -1, 12, 13, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 16, 91, 80, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 16, 16, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 91, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 16, 16, 17, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, 85, 85, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "306", 923, 605, 46, 25, 0], [2, "325_1", 538, 565, 50, 37, 2], [2, "173_1", 735, 271, 70, 45, 2], [2, "173_1", -6, 348, 70, 45, 2], [2, "325_1", 572, 436, 50, 37, 0], [2, "325_1", 213, 538, 50, 37, 2], [2, "325_1", 725, 720, 50, 37, 2], [2, "325_1", 229, 354, 50, 37, 0], [2, "325_1", 908, 117, 50, 37, 0], [2, "325_1", 320, 361, 50, 37, 0], [2, "550", 415, 424, 42, 28, 2], [2, "306", 924, 629, 46, 25, 0], [2, "309", 849, 619, 46, 33, 0], [2, "307", 875, 623, 42, 19, 0], [2, "306", 898, 638, 46, 25, 0], [2, "307", 874, 608, 42, 19, 0], [2, "306", 899, 594, 46, 25, 0], [2, "306", 945, 616, 46, 25, 0], [2, "306", 971, 625, 46, 25, 0], [2, "308", 922, 656, 52, 22, 0], [2, "307", 946, 642, 42, 19, 0], [2, "309", 1020, 593, 46, 33, 2], [2, "308", 988, 617, 52, 22, 0], [2, "309", 302, 58, 46, 33, 0], [2, "306", 417, 54, 46, 25, 0], [2, "308", 354, 92, 52, 22, 0], [2, "308", 383, 76, 52, 22, 0], [2, "306", 323, 79, 46, 25, 0], [2, "309", 557, 6, 46, 33, 0], [2, "308", 621, 50, 52, 22, 0], [2, "307", 649, 38, 42, 19, 0], [2, "306", 682, 21, 46, 25, 0], [2, "309", 573, 27, 46, 33, 0], [2, "305", 602, 49, 30, 24, 0], [2, "174_1", 138, 651, 68, 33, 0], [2, "174_1", 48, 719, 68, 33, 0], [2, "174_1", 93, 612, 68, 33, 0], [2, "174_1", 5, 663, 68, 33, 0], [2, "174_1", 51, 667, 68, 33, 0], [2, "174_1", 113, 695, 68, 33, 2], [2, "174_1", 141, 603, 68, 33, 0], [2, "328", 599, 719, 32, 29, 0], [2, "328", 139, 203, 32, 29, 0]]}, {"type": 2, "data": [28, 46, 46, 46, 46, 46, 46, 46, 28, 28, 28, 46, 46, 46, 40, 40, 46, 46, 46, 46, 46, 41, 37, 37, 47, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 40, 41, 43, 47, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 47, 41, 43, 47, 28, 28, 40, 41, 43, 47, 46, 46, 40, 34, 34, 34, 34, 46, 45, 103, 103, 44, 43, 47, 46, 46, 41, 43, 47, 46, 46, 41, 47, 29, 30, 39, 46, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 45, 102, 39, 40, 46, 41, 38, 106, 44, 43, 47, 40, 41, 47, 41, 37, 43, 38, 106, 106, 107, 104, 39, 40, 46, 45, 100, 44, 47, 46, 45, 39, 40, 29, 35, 28, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 41, 43, 38, 105, 36, 37, 43, 38, 32, 31, 30, 103, 39, 40, 29, 35, 29, 31, 30, 101, 102, 32, 26, 107, 44, 43, 43, 42, 104, 105, 44, 43, 42, 36, 47, 40, 46, 28, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 37, 37, 38, 105, 24, 25, 31, 26, 32, 31, 35, 34, 29, 35, 35, 34, 46, 40, 46, 41, 42, 100, 101, 27, 29, 30, 106, 107, 108, 106, 107, 108, 103, 104, 105, 100, 36, 47, 46, 46, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 106, 107, 107, 32, 35, 40, 40, 29, 35, 40, 40, 34, 41, 37, 43, 37, 43, 37, 43, 42, 100, 101, 104, 27, 46, 29, 31, 25, 25, 31, 30, 100, 106, 107, 108, 103, 104, 39, 40, 40, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, -1, 103, 104, 39, 40, 40, 40, 40, 46, 46, 40, 34, 45, 100, 100, 100, 100, 100, 100, 100, 103, 104, 32, 35, 46, 46, 34, 46, 40, 40, 29, 25, 25, 31, 30, 106, 107, 36, 37, 47, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 103, 103, 104, 36, 47, 40, 40, 46, 46, 46, 40, 34, 29, 31, 31, 25, 26, 105, 100, 103, 100, 32, 35, 34, 46, -1, -1, -1, 40, 46, 40, 40, 40, 40, 29, 30, 100, 101, 32, 35, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 32, 31, 30, 104, 39, 40, 40, 35, 34, 41, 47, 28, 28, 28, 40, 40, 45, 100, 101, 100, 24, 35, 41, 37, 37, 47, 46, 46, -1, 39, 40, 40, 40, 40, 40, 29, 30, 104, 39, 40, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 35, 34, 29, 31, 35, 34, 34, 40, 41, 38, 44, 43, 47, 28, 28, 28, 45, 103, 104, 35, 35, 41, 38, 104, 105, 44, 47, 40, 48, 49, 48, 43, 40, 40, 40, 34, 33, 107, 36, 47, 37, 43, 43, 47, 46, 46, 46, 46, 46, 46, 40, 40, 40, 40, 40, 40, 31, 40, 29, 31, 31, 30, 44, 43, 43, 43, 47, 40, 46, 40, 45, 106, 101, 107, 108, 32, 35, 34, 49, 48, 49, 48, -1, 46, 46, 41, 42, 100, 101, 44, 100, 100, 101, 36, 47, 46, 46, 46, 46, 46, 40, 28, 40, 40, 40, 40, -1, 29, 40, 46, 46, 29, 106, 107, 103, 32, 100, 101, 102, 34, 29, 25, 26, 106, 107, 44, 43, 42, 48, 49, 48, 49, 31, 46, 46, 45, 100, 103, 104, 101, 103, 103, 104, 101, 36, 47, 46, 46, 46, 46, 40, 28, -1, -1, -1, 48, 49, 46, 46, 46, 46, -1, -1, -1, 106, 107, 103, 104, 105, 41, 103, 104, 101, 106, 107, -1, 24, 48, 49, 48, 49, -1, 40, 46, 46, 42, 103, 106, 24, 31, 30, 103, 104, 100, 101, 27, 46, 46, 46, 46, -1, -1, -1, -1, 48, 100, 101, 102, 100, 101, 102, 100, 101, 102, 101, 102, 106, 107, 108, 103, 106, 107, 104, 26, 32, 25, 48, 49, 48, 49, 48, 40, 40, 45, 106, 107, 106, 107, 27, 46, 45, 104, 100, 103, 104, 39, 46, 46, 46, 46, 100, 101, 102, 48, 49, 103, 100, 101, 103, 104, 105, 103, 104, 105, 104, 105, 100, 101, 102, 106, 105, 32, 31, 28, -1, 48, 49, 48, 49, 48, 49, -1, 40, 29, 31, 30, 102, 101, 44, 37, 38, 103, 103, 104, 32, 35, 46, 46, 46, 46, 103, 104, 105, 100, 101, 106, 103, 104, 106, 107, 108, 106, 102, 102, 102, 30, 103, 101, 102, 100, 32, 35, 46, 28, 28, 28, 49, 49, 48, 49, 48, 49, 40, 40, 34, 33, 24, 30, 103, 104, 104, 24, 31, 31, 35, 46, 46, 46, 46, 46, 106, 107, 108, 100, 101, 102, 106, 107, 108, 106, 107, 100, 105, 105, 105, 102, 102, 104, 100, 100, 39, 34, 46, 28, 28, 28, 49, 48, 49, 48, 49, 48, 49, -1, 34, 45, 27, 29, 31, 31, 31, 35, 46, 46, 46, 46, 46, 46, 46, 46, 100, 101, 102, 103, 104, 105, 32, 31, 31, 31, 31, 30, 104, 100, 102, 105, 105, 105, 101, 100, 36, 47, 46, 46, 46, 46, 46, 40, 40, 49, 48, 49, 48, 48, 34, 30, 35, 28, 40, 40, 48, 46, 46, 46, 46, 46, 46, 46, 46, 46, 26, 100, 100, 32, 31, 41, 37, 37, 47, 46, 46, 45, 102, 100, 24, 35, 28, 29, 30, 100, 100, 36, 47, 46, 46, 46, 46, 41, 47, 46, 49, 48, 49, 48, 49, 48, 49, -1, 40, 40, -1, -1, 48, 49, 48, 49, 48, 49, 48, 49, 29, 31, 31, 35, 41, 38, 101, 102, 39, 40, 41, 42, 100, 105, 27, 28, 40, 41, 42, 105, 100, 102, 44, 43, 43, 43, 43, 42, 39, 40, 43, 49, 48, 49, 48, 49, 48, 49, 48, 49, 49, 48, 48, 49, 48, 49, 49, 48, 49, 48, 40, 41, 43, 37, 38, 103, 104, 105, 36, 37, 42, 102, 101, 102, 44, 43, 43, 42, 104, 101, 101, 104, 100, 101, 102, 101, 102, 24, 35, 41, 38, 44, 27, 48, 49, 48, 49, 48, 49, 48, 48, 48, 48, 49, 48, 49, 49, 49, 49, 48, 41, 38, 107, 108, 106, 106, 107, 106, 107, 108, 104, 105, 104, 100, 101, 102, 100, 101, 100, 104, 104, 106, 100, 104, 100, 104, 101, 36, 43, 42, 104, 24, 35, 34, 48, 49, 48, 49, 48, 49, 49, 48, 49, 48, 48, 49, 49, 48, 49, 49, 29, 31, 30, 107, 103, 103, 103, 104, 105, 24, 24, 31, 30, 101, 102, 100, 100, 101, 100, 104, 100, 104, 106, 104, 101, 104, 101, 102, 106, 32, 31, 35, 34, 34, 34, 28, 49, 48, 49, 48, 49, 48, 49, 48, 48, 48, 49, 49, 48, 49, 46, 46, 29, 31, 31, 31, 30, 32, 31, 35, 40, 40, 45, 104, 32, 30, 106, 104, 105, 100, 104, 103, 104, 107, 103, 104, 104, 106, 101, 44, 43, 47, 34, 34, 34, 28, 28, 28, 28, 49, 48, 48, 49, 48, 49, 49, 48, 49, 49, 49, 46, 46, 46, 46, 46, 46, 29, 35, 46, 34, 34, 41, 42, 101, 44, 42, 24, 25, 31, 31, 25, 26, 32, 25, 25, 31, 31, 30, 106, 101, 106, 36, 43, 43, 43, 37, 47, 28, 28, 28, 28, 28, 28, 28, 28, 48, 49, 48, 49, 48, 41, 43, 47, 28, 46, 41, 43, 47, 46, 34, 28, 33, 100, 101, 100, 101, 39, 40, 34, 34, 34, 29, 35, 40, 28, 28, 28, 29, 30, 106, 100, 100, 100, 100, 100, 100, 44, 34, 34, 34, 34, 34, 34, 28, 28, 48, 49, 48, 49, 48, 42, 32, 35, 34, 41, 42, 102, 44, 47, 46, 40, 45, 103, 104, 103, 104, 36, 47, 34, 34, 34, 40, 40, 40, 34, 34, 34, 34, 45, 106, 100, 100, 100, 100, 100, 106, 34, 100, 101, 100, 101, 34, 34, 28, 28, 46, 48, 49, 48, 49, 24, 35, 34, 41, 42, 106, 105, 108, 44, 43, 43, 38, 101, 32, 31, 30, 101, 39, 34, 34, 40, 40, 40, 34, 34, 34, 34, 28, 29, 26, 106, 32, 31, 31, 31, 31, 30, 103, 104, 103, 24, 34, 34, 34, 46, 48, 49, 48, 49, 48, 39, 40, 34, 45, 100, 101, 108, 101, 100, 101, 100, 101, 24, 35, 34, 29, 25, 35, 41, 43, 34, 34, 34, 34, 34, 34, 34, 28, 28, 29, 31, 35, 28, 28, 40, 40, 29, 31, 31, 31, 35, 46, 46, 46, 48, 48, 49, 49, 48, 48, 44, 43, 43, 42, 103, 104, 103, 104, 32, 31, 31, 25, 35, 40, 46, 46, 40, 41, 42, 101, 36, 47, 34, 34, 34, 34, 34, 34, 34, 28, 28, 40, 28, 28, 40, 28, 34, 28, 28, 34, 34, 46, 46, 48, 48, 49, 48, 49, 48, 48, 24, 25, 26, 32, 31, 30, 102, 102, 44, 43, 43, 47, 46, 40, 46, 34, 41, 38, 100, 104, 27, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 40, 28, 28, 34, 34, 28, 28, 34, 48, 49, 48, 49, 48, 49, 46, 46, 46, 48, 49, 40, 40, 29, 35, 34, 45, 105, 105, 105, 102, 101, 44, 47, 40, 34, 41, 38, 103, 103, 104, 27, 34, 34, 34, 34, 41, 37, 37, 37, 47, 34, 40, 28, 28, 28, 46, 46, 28, 48, 49, 48, 49, 48, 49, 46, 46, 46, 46, 46, 46, 40, 40, 40, 40, 41, 42, 108, 108, 108, 32, 31, 30, 32, 40, 41, 38, 100, 101, 32, 31, 35, 34, 34, 34, 41, 42, 100, 100, 100, 36, 37, 47, 28, 46, 35, 46, 34, 48, 49, 49, 49, 48, 49, 46, 46, 46, 46, 46, 46, 46, 40, 40, 40, 41, 38, 100, 108, 32, 31, 35, 34, 29, 35, 40, 29, 25, 30, 104, 36, 37, 37, 47, 34, 41, 38, 100, 100, 100, 100, 100, 106, 36, 43, 47, 34, 34, 46, 48, 49, 48, 49, 46, 46, 46, 46, 46, 46, 46, 46, 46, 40, 40, 40, 29, 26, 103, 32, 35, 34, 34, 34, 34, 46, 46, 46, 46, 33, 103, 104, 100, 101, 44, 43, 42, 100, 100, 100, 100, 106, 100, 100, 106, 106, 44, 37, 37, 48, 49, 48, 49, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 29, 25, 35, 46, 46, 46, 46, 46, 46, 46, 41, 37, 42, 100, 103, 103, 100, 100, 106, 106, 100, 100, 106, 106, 100, 103, 104, 100, 101, 108, 48, 49, 48, 49, 37, 37, 28, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 29, 26, 104, 103, 103, 100, 100, 32, 25, 25, 30, 100, 100, 101, 106, 106, 106, 103, 104, 48, 48, 48, 49, 46, 100, 101, 36, 43, 47, 46, 46, 41, 37, 47, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 29, 31, 31, 31, 26, 32, 35, 46, 41, 42, 103, 103, 104, 105, 103, 104, 105, 49, 48, 49, 104, 27, 46, 103, 100, 100, 101, 36, 37, 37, 38, 101, 36, 47, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 29, 35, 46, 46, 45, 103, 104, 106, 107, 108, 48, 49, 49, 48, 49, 31, 31, 35, 46, 28, 103, 103, 104, 100, 100, 103, 104, 104, 100, 44, 47, 47, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 29, 26, 106, 107, 48, 48, 49, 48, 49, 48, 49, 34, 46, 46, 46, 46, 100, 101, 101, 103, 103, 104, 100, 101, 101, 101, 44]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}