{"mW": 960, "mH": 960, "tW": 24, "tH": 24, "tiles": [["302_5", 0, 2, 2], ["1246", 0, 3, 2], ["1246", 2, 3, 2], ["1246", 1, 3, 2], ["1246", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "1244", 782, 560, 30, 67, 0], [2, "1244", 942, 171, 30, 67, 2], [2, "1244", 946, 810, 30, 67, 2], [2, "541_3", 85, 497, 38, 42, 0], [2, "541_3", 113, 526, 38, 42, 0], [2, "541_3", 141, 554, 38, 42, 0], [2, "541_3", 167, 580, 38, 42, 0], [2, "541_3", 196, 610, 38, 42, 0], [2, "1244", 314, 302, 30, 67, 2], [2, "1141_2", 904, 829, 54, 67, 0], [2, "1254", 905, 821, 54, 32, 2], [2, "1244", 887, 837, 30, 67, 2], [2, "1141_2", 845, 859, 54, 67, 0], [2, "1254", 847, 849, 54, 32, 2], [2, "1244", 834, 864, 30, 67, 2], [2, "1244", -3, 784, 30, 67, 0], [2, "1141_2", 15, 804, 54, 67, 2], [2, "1254", 15, 796, 54, 32, 0], [2, "1244", 55, 812, 30, 67, 0], [2, "1141_2", 73, 833, 54, 67, 2], [2, "1254", 74, 825, 54, 32, 0], [2, "1141_2", 792, 882, 54, 67, 0], [2, "1254", 793, 874, 54, 32, 2], [2, "1244", 775, 890, 30, 67, 2], [2, "1141_2", 733, 912, 54, 67, 0], [2, "1254", 735, 902, 54, 32, 2], [2, "1244", 722, 917, 30, 67, 2], [2, "1141_2", 680, 935, 54, 67, 0], [2, "1254", 681, 927, 54, 32, 2], [2, "1244", 663, 943, 30, 67, 2], [2, "1244", 111, 841, 30, 67, 0], [2, "1141_2", 129, 861, 54, 67, 2], [2, "1254", 129, 853, 54, 32, 0], [2, "1244", 169, 869, 30, 67, 0], [2, "1141_2", 187, 890, 54, 67, 2], [2, "1254", 188, 882, 54, 32, 0], [2, "1244", 223, 898, 30, 67, 0], [2, "1141_2", 241, 918, 54, 67, 2], [2, "1254", 241, 910, 54, 32, 0], [2, "1244", 281, 926, 30, 67, 0], [2, "1141_2", 299, 947, 54, 67, 2], [2, "1254", 300, 939, 54, 32, 0], [2, "1141_2", 54, 306, 54, 67, 0], [2, "1254", 55, 298, 54, 32, 2], [2, "1244", 37, 314, 30, 67, 2], [2, "1141_2", -5, 336, 54, 67, 0], [2, "1254", -3, 326, 54, 32, 2], [2, "1244", -16, 341, 30, 67, 2], [2, "1141_2", 902, 190, 54, 67, 0], [2, "1254", 903, 183, 54, 32, 2], [2, "1141_2", 332, 324, 54, 67, 2], [2, "1254", 333, 315, 54, 32, 0], [2, "1141_2", 381, 330, 54, 67, 0], [2, "1254", 381, 320, 54, 32, 2], [2, "1244", 372, 330, 30, 67, 0], [2, "1141_2", 788, 167, 54, 67, 2], [2, "1254", 788, 158, 54, 32, 0], [2, "1244", 828, 174, 30, 67, 0], [2, "1141_2", 848, 195, 54, 67, 2], [2, "1254", 846, 187, 54, 32, 0], [2, "1244", 888, 201, 30, 67, 0], [2, "1141_2", 906, 222, 54, 67, 2], [2, "1254", 906, 215, 54, 32, 0], [2, "1244", 945, 228, 30, 67, 0], [2, "1244", 951, 586, 30, 67, 2], [2, "1141_2", 911, 605, 54, 67, 0], [2, "1254", 912, 598, 54, 32, 2], [2, "1141_2", 800, 580, 54, 67, 2], [2, "1254", 799, 572, 54, 32, 0], [2, "1244", 840, 588, 30, 67, 0], [2, "1141_2", 858, 609, 54, 67, 2], [2, "1254", 857, 601, 54, 32, 0], [2, "1244", 897, 615, 30, 67, 0], [2, "1141_2", 914, 636, 54, 67, 2], [2, "1254", 916, 629, 54, 32, 0], [2, "1244", 953, 644, 30, 67, 0]]}, {"type": 4, "obj": [[2, "1243", 542, 232, 50, 49, 2], [2, "1243", 412, 282, 50, 49, 2]]}, {"type": 3, "obj": [[2, "1141_1", 749, 13, 54, 67, 0], [2, "1141_1", 749, -31, 54, 67, 0], [2, "1141_1", 706, 34, 54, 67, 0], [2, "1141_1", 706, -10, 54, 67, 0], [2, "1141_1", 705, -53, 54, 67, 0], [2, "1141_1", 663, 55, 54, 67, 0], [2, "1141_1", 663, 11, 54, 67, 0], [2, "1141_1", 662, -32, 54, 67, 0], [2, "1244", 349, -30, 30, 67, 0], [2, "1141_1", 367, -10, 54, 67, 2], [2, "1254", 368, -17, 54, 32, 0], [2, "1244", 406, 0, 30, 67, 2], [2, "1244", 404, -50, 30, 67, 2], [2, "1141_1", 620, 74, 54, 67, 0], [2, "1141_1", 620, 30, 54, 67, 0], [2, "1141_1", 619, -13, 54, 67, 0], [2, "1141_1", 619, -58, 54, 67, 0], [2, "1141_1", 427, 18, 54, 67, 2], [2, "1141_1", 427, -26, 54, 67, 2], [2, "1141_1", 471, 40, 54, 67, 2], [2, "1141_1", 471, -4, 54, 67, 2], [2, "1141_1", 471, -47, 54, 67, 2], [2, "1141_1", 514, 62, 54, 67, 2], [2, "1141_1", 514, 18, 54, 67, 2], [2, "1141_1", 514, -25, 54, 67, 2], [2, "1141_1", 557, 83, 54, 67, 2], [2, "1141_1", 557, 39, 54, 67, 2], [2, "1141_1", 557, -4, 54, 67, 2], [2, "1141_1", 562, -47, 54, 67, 2], [2, "1244", 124, 214, 30, 67, 2], [2, "1141_2", 78, 231, 54, 67, 0], [2, "1141_2", 51, 243, 54, 67, 0], [2, "1141_2", 142, 230, 54, 67, 2], [2, "1244", 196, -45, 30, 67, 2], [2, "1141_1", 153, -25, 54, 67, 0], [2, "1254", 154, -33, 54, 32, 2], [2, "1141_1", -13, -25, 54, 67, 2], [2, "1254", -12, -32, 54, 32, 0], [2, "1249", 515, 30, 22, 53, 0], [2, "1249", 496, 36, 22, 53, 0], [2, "43_9", 492, 58, 82, 58, 0], [2, "1248", 799, 217, 22, 52, 2], [2, "1248", 799, 241, 22, 52, 2], [2, "1248", 779, 208, 22, 52, 2], [2, "1248", 779, 232, 22, 52, 2], [2, "1248", 735, 220, 22, 52, 0], [2, "1248", 735, 244, 22, 52, 0], [2, "1248", 715, 231, 22, 52, 0], [2, "1248", 715, 255, 22, 52, 0], [2, "1248", 739, 623, 22, 52, 0], [2, "1249", 953, 737, 22, 53, 2], [2, "1249", 867, 695, 22, 53, 2], [2, "1249", 888, 704, 22, 53, 2], [2, "1249", 910, 715, 22, 53, 2], [2, "1249", 931, 724, 22, 53, 2], [2, "1249", 781, 654, 22, 53, 2], [2, "1251", 346, 610, 22, 52, 2], [2, "1249", 544, 546, 22, 53, 2], [2, "1249", 610, 577, 22, 53, 2], [2, "1249", 589, 567, 22, 53, 2], [2, "1249", 566, 556, 22, 53, 2], [2, "1251", 240, 642, 22, 52, 0], [2, "1251", 218, 654, 22, 52, 0], [2, "1251", 196, 665, 22, 52, 0], [2, "1251", 174, 677, 22, 52, 0], [2, "1251", 153, 686, 22, 52, 0], [2, "1251", 131, 698, 22, 52, 0], [2, "1251", 109, 709, 22, 52, 0], [2, "1251", 87, 721, 22, 52, 0], [2, "1251", 66, 731, 22, 52, 0], [2, "1251", 44, 743, 22, 52, 0], [2, "1251", 22, 754, 22, 52, 0], [2, "1251", 0, 766, 22, 52, 0], [2, "1251", 282, 622, 22, 52, 0], [2, "1251", 260, 634, 22, 52, 0], [2, "1251", 303, 612, 22, 52, 0], [2, "1251", 325, 600, 22, 52, 0], [2, "1251", 332, 565, 22, 52, 2], [2, "1251", 353, 575, 22, 52, 2], [2, "1251", 375, 585, 22, 52, 2], [2, "1251", 375, 546, 22, 52, 2], [2, "1251", 373, 511, 22, 52, 2], [2, "1251", 353, 500, 22, 52, 2], [2, "1251", 333, 488, 22, 52, 2], [2, "1251", 311, 478, 22, 52, 2], [2, "1251", 122, 541, 22, 52, 2], [2, "1250", 142, 529, 22, 52, 2], [2, "1250", 122, 503, 22, 52, 2], [2, "1249", 118, 314, 22, 53, 0], [2, "1248", 117, 274, 22, 52, 0], [2, "1254", 82, 225, 54, 32, 2], [2, "1254", 54, 238, 54, 32, 2], [2, "1244", 40, 253, 30, 67, 2], [2, "1141_1", 57, 274, 54, 67, 2], [2, "1254", 58, 266, 54, 32, 0], [2, "1244", 98, 285, 30, 67, 2], [2, "1250", 270, 448, 22, 52, 0], [2, "1250", 230, 424, 22, 52, 0], [2, "1250", 249, 459, 22, 52, 0], [2, "1249", 269, 384, 22, 53, 0], [2, "1251", 270, 486, 22, 52, 0], [2, "1249", 309, 353, 22, 53, 2], [2, "1250", 309, 396, 22, 52, 2], [2, "1250", 309, 437, 22, 52, 2], [2, "1249", 290, 355, 22, 53, 0], [2, "1250", 290, 398, 22, 52, 0], [2, "1250", 290, 439, 22, 52, 0], [2, "1251", 290, 477, 22, 52, 0], [2, "1251", 290, 513, 22, 52, 0], [2, "1250", 438, 457, 22, 52, 2], [2, "1250", 457, 466, 22, 52, 2], [2, "1250", 478, 478, 22, 52, 2], [2, "1249", 158, 322, 22, 53, 2], [2, "1248", 158, 282, 22, 52, 2], [2, "1248", 179, 295, 22, 52, 2], [2, "92_2", 273, 342, 40, 45, 2], [2, "1255", 199, 295, 26, 27, 2], [2, "1241", 172, 304, 38, 50, 2], [2, "1241", 147, 346, 38, 50, 2], [2, "1240", 164, 384, 84, 50, 2], [2, "1240", 173, 369, 84, 50, 2], [2, "1240", 182, 354, 84, 50, 2], [2, "1240", 191, 339, 84, 50, 2], [2, "1240", 201, 325, 84, 50, 2], [2, "1240", 210, 310, 84, 50, 2], [2, "1255", 283, 336, 26, 27, 2], [2, "1241", 257, 345, 38, 50, 2], [2, "1241", 231, 388, 38, 50, 2], [2, "700_2", 277, 305, 22, 48, 2], [2, "700_2", 300, 305, 22, 48, 0], [2, "955_5", 179, 349, 20, 18, 0], [2, "1249", 371, 385, 22, 53, 2], [2, "1249", 350, 375, 22, 53, 2], [2, "1249", 328, 364, 22, 53, 2], [2, "269_5", 224, 547, 110, 58, 0], [2, "272_2", 274, 511, 72, 54, 2], [2, "269_5", 245, 539, 110, 58, 0], [2, "272_2", 308, 527, 72, 54, 2], [2, "1255", 212, 443, 26, 27, 0], [2, "1241", 227, 452, 38, 50, 0], [2, "1241", 253, 495, 38, 50, 0], [2, "1240", 138, 453, 84, 50, 0], [2, "1240", 149, 465, 84, 50, 0], [2, "1240", 159, 479, 84, 50, 0], [2, "1240", 168, 494, 84, 50, 0], [2, "1240", 177, 508, 84, 50, 0], [2, "1240", 186, 522, 84, 50, 0], [2, "1255", 123, 483, 26, 27, 0], [2, "1241", 138, 492, 38, 50, 0], [2, "269_5", 183, 569, 110, 58, 0], [2, "92_2", 437, 399, 40, 45, 0], [2, "92_2", 437, 376, 40, 45, 0], [2, "1248", 796, 937, 22, 52, 0], [2, "1248", 818, 925, 22, 52, 0], [2, "1248", 839, 898, 22, 52, 0], [2, "1248", 858, 889, 22, 52, 0], [2, "1248", 879, 879, 22, 52, 0], [2, "1248", 900, 870, 22, 52, 0], [2, "1248", 922, 860, 22, 52, 0], [2, "1248", 922, 900, 22, 52, 0], [2, "1248", 943, 851, 22, 52, 0], [2, "1248", 943, 891, 22, 52, 0], [2, "1249", 942, 931, 22, 53, 0], [2, "1248", -7, 398, 22, 52, 0], [2, "1249", -7, 478, 22, 53, 0], [2, "1248", -7, 438, 22, 52, 0], [2, "1250", -7, 520, 22, 52, 0], [2, "1250", -7, 561, 22, 52, 0], [2, "1251", -7, 602, 22, 52, 0], [2, "1248", 15, 387, 22, 52, 0], [2, "1249", 15, 467, 22, 53, 0], [2, "1248", 15, 427, 22, 52, 0], [2, "1250", 15, 509, 22, 52, 0], [2, "1250", 15, 550, 22, 52, 0], [2, "1251", 15, 591, 22, 52, 0], [2, "1248", 37, 376, 22, 52, 0], [2, "1249", 37, 456, 22, 53, 0], [2, "1248", 37, 416, 22, 52, 0], [2, "1250", 37, 498, 22, 52, 0], [2, "1250", 37, 539, 22, 52, 0], [2, "1251", 37, 580, 22, 52, 0], [2, "1248", 59, 365, 22, 52, 0], [2, "1249", 59, 445, 22, 53, 0], [2, "1248", 59, 405, 22, 52, 0], [2, "1250", 59, 487, 22, 52, 0], [2, "1250", 59, 528, 22, 52, 0], [2, "1251", 59, 569, 22, 52, 0], [2, "1248", 81, 354, 22, 52, 0], [2, "1249", 81, 434, 22, 53, 0], [2, "1248", 81, 394, 22, 52, 0], [2, "1250", 81, 476, 22, 52, 0], [2, "1250", 81, 517, 22, 52, 0], [2, "1251", 81, 558, 22, 52, 0], [2, "1248", 102, 343, 22, 52, 0], [2, "1249", 102, 423, 22, 53, 0], [2, "1248", 102, 383, 22, 52, 0], [2, "1250", 102, 465, 22, 52, 0], [2, "1250", 102, 506, 22, 52, 0], [2, "1251", 102, 547, 22, 52, 0], [2, "1244", 854, -35, 30, 67, 2], [2, "1141_1", 812, -13, 54, 67, 0], [2, "1254", 812, -21, 54, 32, 2], [2, "1244", 797, -5, 30, 67, 2], [2, "958_1", 771, 782, 90, 68, 2], [2, "1253", -6, 469, 22, 35, 0], [2, "1253", 94, 426, 22, 35, 0], [2, "1248", 901, 911, 22, 52, 0], [2, "1248", 881, 920, 22, 52, 0], [2, "1248", 860, 929, 22, 52, 0], [2, "955_5", 459, 107, 20, 18, 0], [2, "958_1", 802, 380, 90, 68, 0], [2, "955_5", 644, 833, 20, 18, 0], [2, "1250", 498, 447, 22, 52, 2], [2, "965_1", 615, 376, 40, 33, 2], [2, "967_1", 624, 344, 24, 41, 2], [2, "1249", 415, 375, 22, 53, 0], [2, "1249", 393, 386, 22, 53, 0], [2, "92_2", 437, 353, 40, 45, 0], [2, "92_2", 437, 422, 40, 45, 0], [2, "92_2", 465, 414, 40, 45, 0], [2, "92_2", 465, 391, 40, 45, 0], [2, "92_2", 491, 432, 40, 45, 0], [2, "1249", 521, 535, 22, 53, 2], [2, "1248", 518, 493, 22, 52, 2], [2, "1248", 539, 503, 22, 52, 2], [2, "1248", 560, 513, 22, 52, 2], [2, "1248", 566, 306, 22, 52, 0], [2, "1248", 587, 295, 22, 52, 0], [2, "1248", 673, 251, 22, 52, 0], [2, "1248", 652, 262, 22, 52, 0], [2, "1248", 630, 273, 22, 52, 0], [2, "1248", 608, 284, 22, 52, 0], [2, "1248", 673, 275, 22, 52, 0], [2, "1248", 651, 286, 22, 52, 0], [2, "1248", 629, 297, 22, 52, 0], [2, "1248", 607, 308, 22, 52, 0], [2, "1248", 588, 318, 22, 52, 0], [2, "1248", 568, 328, 22, 52, 0], [2, "92_2", 538, 320, 40, 45, 0], [2, "92_2", 574, 384, 40, 45, 0], [2, "92_2", 554, 347, 40, 45, 0], [2, "1255", 540, 309, 26, 27, 0], [2, "1241", 555, 318, 38, 50, 0], [2, "1241", 581, 362, 38, 50, 0], [2, "1255", 607, 407, 26, 27, 0], [2, "1240", 466, 321, 84, 50, 0], [2, "1240", 476, 335, 84, 50, 0], [2, "1240", 486, 349, 84, 50, 0], [2, "1240", 496, 363, 84, 50, 0], [2, "1240", 505, 378, 84, 50, 0], [2, "1240", 514, 392, 84, 50, 0], [2, "1240", 524, 406, 84, 50, 0], [2, "1240", 536, 419, 84, 50, 0], [2, "1255", 450, 352, 26, 27, 0], [2, "1241", 465, 361, 38, 50, 0], [2, "1241", 491, 404, 38, 50, 0], [2, "1255", 519, 450, 26, 27, 0], [2, "249_1", 416, 303, 48, 72, 2], [2, "1248", 498, 483, 22, 52, 2], [2, "954_4", 580, 137, 24, 25, 0], [2, "1250", 416, 418, 22, 52, 0], [2, "1250", 394, 429, 22, 52, 0], [2, "1253", 623, 284, 22, 35, 0], [2, "1250", 437, 417, 22, 52, 2], [2, "1250", 456, 426, 22, 52, 2], [2, "1250", 477, 438, 22, 52, 2], [2, "959_2", 576, 446, 40, 22, 0], [2, "955_5", 510, 263, 20, 18, 0], [2, "269_5", 143, 590, 110, 58, 0], [2, "269_5", 102, 610, 110, 58, 0], [2, "269_5", 63, 630, 110, 58, 0], [2, "269_5", 19, 648, 110, 58, 0], [2, "269_5", -22, 668, 110, 58, 0], [2, "269_5", -61, 688, 110, 58, 0], [2, "272_2", 322, 554, 72, 54, 0], [2, "272_2", 275, 578, 72, 54, 0], [2, "272_2", 221, 605, 72, 54, 0], [2, "272_2", 174, 629, 72, 54, 0], [2, "541_3", 223, 638, 38, 42, 0], [2, "272_2", 118, 657, 72, 54, 0], [2, "272_2", 71, 681, 72, 54, 0], [2, "272_2", 16, 708, 72, 54, 0], [2, "272_2", -31, 732, 72, 54, 0], [2, "972_1", 493, 726, 66, 54, 0], [2, "971_1", 436, 751, 58, 50, 0], [2, "972_1", 543, 733, 66, 54, 2], [2, "971_1", 589, 749, 58, 50, 2], [2, "972_1", 449, 779, 66, 54, 2], [2, "971_1", 497, 794, 58, 50, 2], [2, "971_1", 573, 771, 58, 50, 0], [2, "971_1", 529, 793, 58, 50, 0], [2, "973_2", 500, 735, 42, 70, 2], [2, "973_2", 542, 735, 42, 70, 0], [2, "1249", 632, 587, 22, 53, 2], [2, "1248", 601, 533, 22, 52, 2], [2, "1248", 623, 545, 22, 52, 2], [2, "1254", 629, 536, 54, 32, 0], [2, "1255", 673, 555, 26, 27, 2], [2, "1249", 437, 374, 22, 53, 2], [2, "1249", 458, 384, 22, 53, 2], [2, "1250", 417, 459, 22, 52, 0], [2, "1250", 395, 470, 22, 52, 0], [2, "1251", 439, 498, 22, 52, 2], [2, "1251", 460, 507, 22, 52, 2], [2, "1251", 481, 517, 22, 52, 2], [2, "1251", 417, 498, 22, 52, 0], [2, "1251", 395, 508, 22, 52, 0], [2, "1251", 419, 535, 22, 52, 0], [2, "1251", 397, 545, 22, 52, 0], [2, "1249", 499, 525, 22, 53, 2], [2, "1251", 441, 536, 22, 52, 2], [2, "1251", 463, 546, 22, 52, 2], [2, "1250", 329, 407, 22, 52, 2], [2, "1250", 350, 417, 22, 52, 2], [2, "1250", 372, 429, 22, 52, 2], [2, "1250", 331, 449, 22, 52, 2], [2, "1250", 352, 458, 22, 52, 2], [2, "1250", 374, 471, 22, 52, 2], [2, "1250", 270, 409, 22, 52, 0], [2, "1250", 249, 418, 22, 52, 0], [2, "955_5", 220, 432, 20, 18, 0], [2, "1254", 141, 224, 54, 32, 0], [2, "1244", 177, 239, 30, 67, 0], [2, "700_2", 189, 265, 22, 48, 2], [2, "700_2", 212, 265, 22, 48, 0], [2, "1248", 139, 274, 22, 52, 2], [2, "1249", 138, 314, 22, 53, 2], [2, "700_2", 210, 412, 22, 48, 2], [2, "700_2", 233, 412, 22, 48, 0], [2, "700_2", 110, 449, 22, 48, 2], [2, "700_2", 133, 449, 22, 48, 0], [2, "1251", 397, 584, 22, 52, 0], [2, "1251", 419, 572, 22, 52, 0], [2, "1253", 313, 393, 22, 35, 2], [2, "1253", 357, 413, 22, 35, 2], [2, "961_1", 454, 442, 28, 36, 0], [2, "962_1", 453, 458, 18, 19, 0], [2, "541_3", 425, 466, 38, 42, 2], [2, "541_3", 396, 496, 38, 42, 2], [2, "541_3", 367, 526, 38, 42, 2], [2, "541_3", 338, 556, 38, 42, 2], [2, "541_3", 309, 586, 38, 42, 2], [2, "1244", 498, 540, 30, 67, 2], [2, "1141_1", 456, 560, 54, 67, 0], [2, "1254", 457, 551, 54, 32, 2], [2, "1141_1", 514, 560, 54, 67, 2], [2, "1254", 516, 553, 54, 32, 0], [2, "1244", 552, 568, 30, 67, 0], [2, "1141_1", 568, 588, 54, 67, 2], [2, "1254", 569, 581, 54, 32, 0], [2, "1244", 438, 567, 30, 67, 2], [2, "1141_1", 398, 588, 54, 67, 0], [2, "1254", 395, 581, 54, 32, 2], [2, "1244", 380, 592, 30, 67, 2], [2, "1141_1", 336, 613, 54, 67, 0], [2, "1254", 338, 607, 54, 32, 2], [2, "541_3", 278, 617, 38, 42, 2], [2, "1244", 317, 622, 30, 67, 2], [2, "1141_1", 275, 644, 54, 67, 0], [2, "1254", 276, 636, 54, 32, 2], [2, "1244", 265, 650, 30, 67, 2], [2, "1141_1", 223, 670, 54, 67, 0], [2, "1254", 224, 662, 54, 32, 2], [2, "1244", 213, 676, 30, 67, 2], [2, "1141_1", 170, 696, 54, 67, 0], [2, "1254", 171, 688, 54, 32, 2], [2, "1244", 160, 702, 30, 67, 2], [2, "1141_1", 118, 722, 54, 67, 0], [2, "1254", 119, 714, 54, 32, 2], [2, "1244", 107, 728, 30, 67, 2], [2, "1141_1", 65, 748, 54, 67, 0], [2, "1254", 66, 740, 54, 32, 2], [2, "1244", 56, 754, 30, 67, 2], [2, "1141_1", 14, 774, 54, 67, 0], [2, "1254", 15, 765, 54, 32, 2], [2, "1248", 243, 957, 22, 52, 2], [2, "1248", 221, 945, 22, 52, 2], [2, "1248", 199, 935, 22, 52, 2], [2, "1248", 178, 925, 22, 52, 2], [2, "1249", 178, 966, 22, 53, 2], [2, "1248", 156, 914, 22, 52, 2], [2, "1249", 156, 955, 22, 53, 2], [2, "1248", 134, 903, 22, 52, 2], [2, "1249", 134, 944, 22, 53, 2], [2, "1248", 113, 893, 22, 52, 2], [2, "1249", 113, 934, 22, 53, 2], [2, "1248", 91, 882, 22, 52, 2], [2, "1249", 91, 923, 22, 53, 2], [2, "1250", 91, 966, 22, 52, 2], [2, "1248", 69, 871, 22, 52, 2], [2, "1249", 69, 912, 22, 53, 2], [2, "1250", 69, 955, 22, 52, 2], [2, "1248", 47, 860, 22, 52, 2], [2, "1249", 47, 901, 22, 53, 2], [2, "1250", 47, 944, 22, 52, 2], [2, "1248", 25, 849, 22, 52, 2], [2, "1249", 25, 890, 22, 53, 2], [2, "1250", 25, 933, 22, 52, 2], [2, "1248", 3, 838, 22, 52, 2], [2, "1249", 3, 879, 22, 53, 2], [2, "1250", 3, 922, 22, 52, 2], [2, "1244", 326, 792, 30, 67, 0], [2, "1141_1", 344, 810, 54, 67, 2], [2, "1254", 344, 803, 54, 32, 0], [2, "1244", 384, 819, 30, 67, 0], [2, "1141_1", 346, 840, 54, 67, 0], [2, "1254", 346, 832, 54, 32, 2], [2, "1244", 328, 850, 30, 67, 2], [2, "1141_1", 287, 869, 54, 67, 0], [2, "1254", 287, 861, 54, 32, 2], [2, "1244", 269, 879, 30, 67, 2], [2, "1141_1", 230, 898, 54, 67, 0], [2, "1254", 230, 890, 54, 32, 2], [2, "965_1", 356, 927, 40, 33, 2], [2, "961_1", 82, 887, 28, 36, 0], [2, "541_3", 58, 906, 38, 42, 2], [2, "962_1", 80, 904, 18, 19, 0], [2, "541_3", 32, 932, 38, 42, 2], [2, "967_1", 365, 896, 24, 41, 2], [2, "1248", 580, 524, 22, 52, 2], [2, "1244", 602, 593, 30, 67, 0], [2, "1248", 644, 555, 22, 52, 2], [2, "92_2", 747, 602, 40, 45, 2], [2, "1241", 646, 565, 38, 50, 2], [2, "1241", 621, 607, 38, 50, 2], [2, "1240", 638, 644, 84, 50, 2], [2, "1240", 647, 629, 84, 50, 2], [2, "1240", 656, 614, 84, 50, 2], [2, "1240", 665, 599, 84, 50, 2], [2, "1240", 675, 585, 84, 50, 2], [2, "1240", 684, 570, 84, 50, 2], [2, "1255", 757, 596, 26, 27, 2], [2, "1241", 731, 605, 38, 50, 2], [2, "1241", 705, 648, 38, 50, 2], [2, "700_2", 752, 565, 22, 48, 2], [2, "700_2", 775, 565, 22, 48, 0], [2, "955_5", 653, 609, 20, 18, 0], [2, "1248", 781, 615, 22, 52, 2], [2, "1248", 801, 625, 22, 52, 2], [2, "1248", 822, 635, 22, 52, 2], [2, "1248", 843, 645, 22, 52, 2], [2, "1248", 863, 655, 22, 52, 2], [2, "1248", 884, 664, 22, 52, 2], [2, "1248", 906, 675, 22, 52, 2], [2, "1248", 928, 686, 22, 52, 2], [2, "1248", 949, 696, 22, 52, 2], [2, "1249", 802, 663, 22, 53, 2], [2, "1249", 824, 674, 22, 53, 2], [2, "1249", 845, 683, 22, 53, 2], [2, "1248", 761, 615, 22, 52, 0], [2, "1249", 760, 654, 22, 53, 0], [2, "1244", 719, 640, 30, 67, 2], [2, "1141_1", 735, 660, 54, 67, 2], [2, "1254", 736, 653, 54, 32, 0], [2, "1244", 778, 669, 30, 67, 2], [2, "1141_1", 794, 689, 54, 67, 2], [2, "1254", 795, 682, 54, 32, 0], [2, "1244", 836, 698, 30, 67, 2], [2, "1141_1", 852, 718, 54, 67, 2], [2, "1254", 853, 711, 54, 32, 0], [2, "1244", 894, 726, 30, 67, 2], [2, "1141_1", 910, 746, 54, 67, 2], [2, "1254", 911, 739, 54, 32, 0], [2, "1244", 951, 756, 30, 67, 2], [2, "1244", 601, 85, 30, 67, 2], [2, "1248", 694, 241, 22, 52, 0], [2, "1248", 694, 265, 22, 52, 0], [2, "1248", 757, 210, 22, 52, 0], [2, "1248", 757, 234, 22, 52, 0], [2, "1248", 820, 226, 22, 52, 2], [2, "1248", 820, 250, 22, 52, 2], [2, "1248", 842, 237, 22, 52, 2], [2, "1248", 842, 261, 22, 52, 2], [2, "1248", 864, 247, 22, 52, 2], [2, "1248", 864, 271, 22, 52, 2], [2, "1248", 886, 258, 22, 52, 2], [2, "1248", 886, 282, 22, 52, 2], [2, "1248", 905, 267, 22, 52, 2], [2, "1248", 905, 291, 22, 52, 2], [2, "1248", 927, 277, 22, 52, 2], [2, "1248", 927, 301, 22, 52, 2], [2, "1248", 948, 286, 22, 52, 2], [2, "1248", 948, 310, 22, 52, 2], [2, "1248", 496, -3, 22, 52, 0], [2, "1253", 447, -11, 22, 35, 2], [2, "1244", 601, 35, 30, 67, 2], [2, "1244", 600, -15, 30, 67, 2], [2, "1253", 566, 38, 22, 35, 2], [2, "1252", 368, 18, 96, 71, 2], [2, "1141_1", 874, -14, 54, 67, 2], [2, "1254", 874, -21, 54, 32, 0], [2, "1244", 912, -7, 30, 67, 0], [2, "1141_1", 931, 14, 54, 67, 2], [2, "1254", 931, 7, 54, 32, 0], [2, "1244", 969, 21, 30, 67, 0], [2, "1242", 826, 26, 96, 56, 2], [2, "1244", 138, -16, 30, 67, 2], [2, "1141_1", 95, 4, 54, 67, 0], [2, "1254", 96, -4, 54, 32, 2], [2, "1244", 27, -16, 30, 67, 0], [2, "1141_1", 45, 4, 54, 67, 2], [2, "1254", 46, -3, 54, 32, 0], [2, "1244", 86, 12, 30, 67, 0], [2, "1141_1", 104, 32, 54, 67, 2], [2, "1254", 105, 25, 54, 32, 0], [2, "1244", 137, 38, 30, 67, 0], [2, "90_2", 712, 688, 28, 36, 0], [2, "90_2", 733, 698, 28, 36, 0], [2, "1253", 690, 253, 22, 35, 0], [2, "1253", 757, 222, 22, 35, 0], [2, "1253", 830, 242, 22, 35, 2], [2, "1253", 917, 281, 22, 35, 2], [2, "1253", 740, 4, 22, 35, 0], [2, "1253", 655, 36, 22, 35, 0], [2, "955_5", 815, 117, 20, 18, 0], [2, "958_1", 257, 68, 90, 68, 0], [2, "965_1", 187, 31, 40, 33, 0], [2, "969_1", 265, 4, 36, 30, 0], [2, "90_2", 764, 258, 28, 36, 0], [2, "90_2", 225, -10, 28, 36, 0], [2, "90_2", 781, 41, 28, 36, 0], [2, "90_2", 624, 114, 28, 36, 0], [2, "959_2", 654, 122, 40, 22, 0], [2, "11_4", 755, 61, 32, 29, 0], [2, "969_1", 748, 80, 36, 30, 0], [2, "11_4", -1, 29, 32, 29, 0], [2, "965_1", 784, 303, 40, 33, 0], [2, "967_1", 793, 272, 24, 41, 0], [2, "965_1", 923, 372, 40, 33, 0], [2, "967_1", 932, 341, 24, 41, 0], [2, "965_1", 851, 334, 40, 33, 0], [2, "969_1", 819, 336, 36, 30, 2], [2, "700_2", 372, 73, 22, 48, 2], [2, "700_2", 395, 73, 22, 48, 0], [2, "955_5", 841, 366, 20, 18, 0], [2, "1240", 196, 536, 84, 50, 0], [2, "1241", 164, 536, 38, 50, 0], [2, "272_2", 120, 563, 72, 54, 0], [2, "272_2", 74, 586, 72, 54, 0], [2, "272_2", 28, 609, 72, 54, 0], [2, "272_2", -10, 628, 72, 54, 0], [2, "961_1", 36, 444, 28, 36, 2], [2, "962_1", 50, 459, 18, 19, 2], [2, "541_3", 58, 468, 38, 42, 0], [2, "959_2", 622, 796, 40, 22, 0], [2, "1249", 921, 941, 22, 53, 0], [2, "1248", 838, 940, 22, 52, 0], [2, "11_4", 896, 789, 32, 29, 0], [2, "703_2", 532, 806, 16, 23, 0], [2, "703_2", 439, 767, 16, 23, 0], [2, "703_2", 536, 716, 16, 23, 0], [2, "703_2", 626, 762, 16, 23, 0], [2, "958_1", 185, 790, 90, 68, 0], [2, "954_4", 763, 725, 24, 25, 0], [2, "11_4", 396, 879, 32, 29, 0], [2, "90_2", 497, 590, 28, 36, 0], [2, "90_2", 474, 607, 36, 28, 5], [2, "90_2", 675, 305, 28, 36, 0], [2, "90_2", 652, 322, 36, 28, 5], [2, "90_2", 40, 33, 28, 36, 0], [2, "90_2", 929, 47, 28, 36, 0], [2, "955_5", 312, 225, 20, 18, 0], [2, "955_5", 429, 873, 20, 18, 2], [2, "1244", 769, 145, 30, 67, 2], [2, "1141_2", 727, 166, 54, 67, 0], [2, "1254", 729, 156, 54, 32, 2], [2, "1244", 720, 170, 30, 67, 2], [2, "1141_2", 676, 190, 54, 67, 0], [2, "1254", 678, 180, 54, 32, 2], [2, "1244", 669, 194, 30, 67, 2], [2, "1141_2", 626, 214, 54, 67, 0], [2, "1254", 628, 205, 54, 32, 2], [2, "1244", 619, 218, 30, 67, 2], [2, "1254", 577, 229, 54, 32, 2], [2, "1141_2", 577, 237, 54, 67, 0], [2, "249_1", 542, 248, 48, 72, 2], [2, "1244", 498, 430, 30, 67, 0], [2, "1141_2", 516, 450, 54, 67, 2], [2, "1254", 515, 442, 54, 32, 0], [2, "1244", 549, 455, 30, 67, 0], [2, "1141_2", 567, 478, 54, 67, 2], [2, "1254", 568, 469, 54, 32, 0], [2, "1244", 605, 481, 30, 67, 0], [2, "1141_2", 622, 502, 54, 67, 2], [2, "1254", 620, 493, 54, 32, 0], [2, "1244", 654, 504, 30, 67, 0], [2, "700_2", 661, 527, 22, 48, 2], [2, "700_2", 684, 528, 22, 48, 0], [2, "1245", 483, -13, 76, 130, 2], [2, "1252", 487, 70, 96, 71, 2], [2, "700_2", 480, 123, 22, 48, 2], [2, "700_2", 503, 123, 22, 48, 0]]}, {"type": 2, "data": [-1, -1, 50, 50, 50, 50, -1, -1, -1, 44, 45, -1, -1, 40, 41, 51, 50, -1, 44, 45, 51, 50, -1, -1, -1, -1, 51, 50, 50, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, 50, 50, 50, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, 48, 47, 45, 41, 42, 48, 47, -1, -1, -1, -1, 48, 47, 47, 42, 48, 47, 51, 50, -1, -1, -1, -1, -1, -1, 50, 50, 50, 50, -1, -1, 44, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, -1, -1, -1, 12, 11, 5, 6, -1, 50, 48, 47, 51, 50, -1, 51, 50, 50, 50, 45, 47, 47, 44, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 51, 50, 50, 11, 11, 5, 5, 15, 20, 20, 50, 50, 44, 45, -1, 48, 47, -1, 48, 47, 51, 47, 46, -1, -1, -1, 42, 4, 5, 6, -1, -1, -1, -1, -1, -1, 48, 47, 46, -1, 48, 47, 47, 50, 50, 20, 20, -1, 44, 50, 50, 45, 41, 42, -1, -1, -1, -1, -1, -1, 48, -1, -1, -1, -1, 4, 5, 15, 8, 9, 5, 6, -1, -1, -1, 4, 5, 11, 11, 10, -1, -1, 48, 47, 47, 51, 50, 44, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 42, -1, -1, 7, 8, 14, 20, 8, 26, 25, -1, -1, 48, 7, 8, 14, 14, 25, 36, 35, 34, -1, -1, 48, 47, 41, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 30, -1, -1, 16, 17, 17, 27, 8, 20, 9, 5, 11, 15, 14, 14, 14, 14, 13, 48, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 30, 36, 35, 50, 49, -1, -1, -1, -1, -1, 24, 27, 8, 8, 8, 14, 26, 26, 26, 26, 14, 9, 11, 10, -1, -1, -1, -1, 36, 35, 35, 34, -1, -1, -1, -1, -1, -1, 31, 32, 33, 39, 38, 47, 46, -1, -1, -1, -1, -1, -1, 19, 20, 26, 26, 26, 26, 26, 26, 26, 26, 14, 14, 9, 10, -1, 36, 35, 39, 38, 38, -1, -1, -1, -1, -1, -1, 44, 45, 40, 31, 32, 33, -1, -1, -1, 44, 45, -1, 4, 5, -1, 19, 20, 26, 26, 21, 17, 27, 26, 14, 14, 14, 14, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 45, 42, -1, -1, -1, 48, -1, -1, 45, 41, 42, -1, 7, 8, -1, 16, 27, 26, 21, 22, -1, 24, 27, 14, 14, 14, 14, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, 41, 42, -1, -1, 12, 11, 10, -1, 36, 35, 4, 5, 5, 15, 8, 21, 22, 24, 23, 22, -1, -1, -1, 24, 23, 16, 17, 23, 22, 36, 35, -1, -1, -1, -1, -1, 44, 45, 47, 47, 51, 50, 50, 4, 15, 14, 13, 35, 39, 38, 7, 8, 8, 8, 8, 9, 10, -1, 43, 44, 44, -1, -1, -1, 47, 51, 35, 34, -1, 48, 47, -1, -1, -1, 44, 45, 41, 42, -1, -1, 48, 47, 51, 50, 50, 20, 9, 38, 12, 11, 15, 8, 50, 50, 8, 9, 13, -1, 40, 41, 41, 42, -1, -1, -1, 48, 38, 37, 50, -1, -1, -1, 44, 45, 41, 42, 42, 12, 11, 10, -1, -1, 48, 47, 51, 50, -1, -1, 19, 20, 8, 44, 45, 51, 50, 8, 9, 6, 12, 11, 11, 10, -1, -1, -1, 36, 39, 44, 47, -1, -1, 43, 41, 42, 42, -1, 12, 15, 14, 13, -1, -1, -1, -1, 48, 51, 50, -1, 16, 17, 23, 41, 42, 48, 43, 44, 8, 9, 15, 14, 14, 13, -1, 36, 35, 39, 38, -1, -1, -1, -1, 40, 41, -1, -1, 4, 15, 14, 14, 9, 10, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, -1, 4, 5, 6, 41, 27, 8, 8, 8, 21, 18, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, 29, 30, 34, 19, 20, 14, 14, 14, 25, -1, 12, 11, 10, 48, 47, -1, -1, -1, -1, -1, 24, 23, 22, -1, 24, 23, 23, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 34, 16, 17, 27, 20, 14, 9, 11, 15, 14, 9, 11, 10, 29, 30, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, 44, 45, 44, 50, 37, -1, -1, 16, 27, 20, 14, 14, 14, 14, 14, 14, 13, 32, 33, 30, -1, -1, 28, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 47, 41, 40, 44, 44, 46, -1, -1, 4, 15, 20, 21, 23, 27, 26, 14, 14, 25, -1, 32, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 35, 34, -1, -1, -1, -1, -1, 41, 42, 19, 20, 26, 25, -1, 24, 23, 23, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 40, 41, 42, -1, -1, -1, -1, 14, 35, 34, 16, 17, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, -1, -1, 28, 29, 29, 30, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, 36, 35, 14, 45, 45, 41, 42, -1, -1, -1, -1, 32, 33, 29, 30, 36, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, -1, -1, -1, -1, -1, 51, 50, 39, 38, 50, 35, 34, -1, -1, -1, -1, -1, -1, 48, 51, 32, 33, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 51, 50, 47, -1, 35, 34, -1, -1, -1, -1, -1, -1, 48, 47, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, 4, 5, 6, -1, -1, 48, 47, -1, 51, 50, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, 7, 8, 9, 11, 11, 5, 6, -1, 48, 47, 51, 50, 29, 12, 11, 11, 10, -1, -1, -1, -1, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, 12, 11, 15, 14, 14, 14, 14, 8, 9, 11, 10, -1, 48, 47, 50, 50, 14, 14, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 45, 41, 42, 4, 5, 11, 15, 14, 14, 14, 26, 14, 14, 14, 14, 14, 25, -1, -1, 48, 47, 51, 50, 50, -1, 11, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 41, 42, -1, -1, 7, 8, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 25, -1, -1, -1, -1, 48, 47, 47, 51, 50, 14, 13, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, 16, 17, 27, 26, 14, 14, -1, -1, -1, -1, -1, -1, 14, 22, -1, -1, -1, -1, -1, -1, -1, 48, 47, 51, 50, -1, -1, -1, -1, 44, 45, 50, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 27, 26, 14, -1, -1, -1, -1, 21, 22, -1, -1, 4, 5, 11, 10, -1, -1, -1, -1, 48, 47, 36, 35, -1, -1, 41, 50, 50, 50, 34, -1, -1, -1, -1, -1, 28, 29, 36, 35, -1, 24, 23, 23, 27, 26, 14, 26, 25, -1, -1, 12, 15, 14, 14, 9, 10, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, 38, 37, -1, -1, -1, -1, -1, 31, 32, 39, 38, -1, 34, -1, -1, 19, 20, 14, 14, 9, 11, 11, 15, 14, 14, 14, 14, 13, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, 44, 45, 37, -1, -1, 16, 17, 27, 26, 14, 14, 14, 14, 14, 14, 14, 14, 13, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, -1, 44, 45, 44, 44, 45, -1, -1, -1, -1, 24, 23, 27, 20, 20, 21, 27, 26, 26, 21, 18, -1, -1, -1, -1, -1, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 44, 45, 44, 41, 42, -1, -1, -1, -1, -1, -1, 24, 23, 23, 18, 24, 23, 22, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 41, 42, -1, 36, 35, 34, -1, -1, -1, 36, 35, 34, -1, -1, 28, 29, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1]}, {"type": 2, "data": [0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, -1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 2, 3, 1, 1, 2, 0, 1, 0, 1, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 3, 2, 2, 3, 2, 3, 2, 2, 3, 2, 2, 3, 0, 2, 3, 3, 0, 2, 3, 2, 3, 1, 1, 1, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 1, 0, 1, 0, 1, 2, 0, 1, 2, 3, 1, 1, 1, 3, 0, 1, 2, 3, 0, 1, 1, 2, 3, 2, 0, 1, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 3, 2, 3, 2, 3, 2, 0, 1, 2, 3, 1, 2, 3, 3, 3, 2, 2, 3, 0, 1, 2, 3, 3, 0, 1, 0, 2, 3, 1, 2, 3, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 0, 2, 3, 2, 0, 1, 1, 3, 2, 3, 0, 1, 0, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 3, 2, 3, 0, 1, 0, 0, 1, 0, 1, 0, 1, 2, 3, 3, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 0, 1, 2, 0, 1, 2, 3, 0, 1, 1, 0, 0, 1, 2, 0, 1, 0, 0, 1, 3, 0, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 2, 3, 2, 3, 2, 3, 3, 2, 2, 3, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 0, 2, 3, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 2, 3, 2, 0, 1, 3, 2, 2, 3, 3, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 0, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 2, 3, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 2, 3, 2, 0, 2, 3, 0, 1, 0, 1, 1, 0, 1, 3, 0, 1, 0, 0, 1, 2, 3, 2, 3, 0, 1, 2, 3, 0, 1, -1, -1, 3, 2, 3, 1, 2, 2, 3, 0, 1, 0, 1, 0, 2, 3, 1, 2, 3, 2, 3, 3, 2, 3, 2, 2, 3, 2, 2, 3, 0, 0, 1, 3, 2, 3, 0, 1, 2, 3, -1, -1, 0, 1, 0, 1, 0, 1, 3, 2, 3, 2, 3, 2, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 2, 2, 0, 1, 0, 1, 2, 0, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 1, 1, 0, 1, 0, 1, 3, 2, 0, 1, 0, 1, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 2, 0, 2, 3, 2, 3, 0, 2, 3, 0, 1, 3, 2, 3, 1, 2, 3, 2, 3, 3, 2, 3, 2, 3, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 2, 3, 2, 3, -1, 2, 3, 1, 1, 2, 3, 1, 2, 2, 3, 0, 1, 3, 2, 3, 1, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, -1, -1, -1, 0, 2, 3, 3, 0, 1, 0, 1, 3, 2, 2, 3, 3, 2, 3, 3, 2, 3, 0, 1, 0, 0, 1, 3, 0, 1, 0, 1, 2, 3, 2, 0, 1, 3, -1, -1, -1, -1, -1, -1, 2, 0, 1, 1, 2, 0, 1, 3, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 2, 3, 0, 1, -1, -1, -1, -1, -1, -1, 2, 3, 3, 0, 2, 3, 0, 1, 0, 1, 0, 1, 1, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 2, 2, 3, 2, 3, -1, 0, 1, -1, 0, 0, 0, 1, 0, 1, 3, 2, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 0, 1, 0, 0, 1, 0, 2, 3, 1, 2, 2, 2, 0, 2, 3, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 0, 1, 1, 0, 1, 2, 3, 2, 3, 2, 0, 1, 0, 1, 2, 3, 2, 2, 0, 1, 3, 0, 1, 3, 0, 1, 1, 0, 0, 2, 3, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 3, 3, 2, 3, 3, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 2, 3, 1, 2, 0, 1, 2, 3, 3, 2, 2, 2, 3, 2, 3, 3, 2, 3, 2, 3, 3, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 2, 3, 0, 2, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 2, 0, 1, 3, 2, 2, 0, 1, 1, 0, 1, 2, 3, 0, 1, 2, 3, 2, 3, 2, 0, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 0, 1, 0, 2, 3, 0, 2, 3, 1, 3, 0, 2, 3, 3, 2, 3, 0, 0, 1, 3, 2, 3, 0, 1, 0, 1, 2, 2, 3, 0, 2, 3, 0, 1, 0, 1, 0, 0, 1, 0, 2, 3, 2, 3, 0, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 2, 3, 3, 0, 1, 2, 0, 0, 0, 2, 3, 2, 3, 2, 3, 2, 0, 1, 2, 3, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 0, 2, 2, 2, 3, 2, 3, 2, 0, 1, 2, 2, 3, 3, 0, 1, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 1, 2, 0, 1, 0, 0, 1, 2, 3, 0, 1, 1, 0, 2, 3, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 3, 1, 0, 3, 0, 2, 3, 2, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 0, 2, 3, 0, 2, 3, 0, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 2, 3, 3, 3, 2, 0, 1, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 1, 0, 1, 1, 0, 1, 1, 2, 2, 3, 2, 0, 0, 1, 2, 3, 1, 0, 2, 3, 2, 3, 1, 0, 0, 0, 2, 0, 1, 2, 3, 2, 0, 1, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 2, 3, 3, 2, 3, 3, 2, 2, 2, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 2, 0, 2, 3, 1, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 2, 3, 2, 3, 0, 1, 0, 1, 1, 1, 2, 3, 0, 0, 1, 1, 2, 3, 1, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 3, 2, 2, 2, 2, 3, 3, 3, 2, 3, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 2, 3, 1, 0, 0, 1, 0, 1, 3, 1, 2, 3, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 2, 3, 0, 1, 2, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 1, 2, 2, 0, 1, 1, 0, 1, 0, 1, 3, 0, 0, 1, 3, 0, 0, 0, 1, 2, 3, 0, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 1, 0, 2, 3, 1, 2, 3, 1, 1, 0, 1, 0, 1, 0, 2, 2, 2, 3, 2, 3, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 2, 3, 3, 2, 3, 2, 3, 2]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]}