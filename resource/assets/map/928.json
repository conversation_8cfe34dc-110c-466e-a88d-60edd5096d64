{"mW": 1128, "mH": 960, "tW": 24, "tH": 24, "tiles": [["302_5", 0, 2, 2], ["1246", 0, 3, 2], ["1246", 2, 3, 2], ["1246", 1, 3, 2], ["1246", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 0, 4, 2]], "layers": [{"type": 3, "obj": [[2, "1141_2", 1083, 306, 54, 67, 0], [2, "1254", 1084, 298, 54, 32, 2], [2, "1244", 1067, 314, 30, 67, 2], [2, "1141_2", 1024, 336, 54, 67, 0], [2, "1254", 1026, 326, 54, 32, 2], [2, "1244", 1012, 341, 30, 67, 2], [2, "1141_2", 1104, 819, 54, 67, 0], [2, "1244", 1145, 799, 30, 67, 2], [2, "967_1", 433, 935, 24, 41, 2], [2, "1254", 1105, 811, 54, 32, 2], [2, "1244", 1088, 827, 30, 67, 2], [2, "1141_2", 1045, 849, 54, 67, 0], [2, "1254", 1047, 839, 54, 32, 2], [2, "1244", 1033, 854, 30, 67, 2], [2, "1141_2", 992, 873, 54, 67, 0], [2, "1254", 993, 865, 54, 32, 2], [2, "1244", 976, 881, 30, 67, 2], [2, "1141_2", 933, 903, 54, 67, 0], [2, "1254", 935, 893, 54, 32, 2], [2, "1244", 921, 908, 30, 67, 2], [2, "1141_2", 879, 929, 54, 67, 0], [2, "1254", 880, 921, 54, 32, 2], [2, "1244", 863, 937, 30, 67, 2], [2, "1254", 822, 949, 54, 32, 2], [2, "1244", -2, 769, 30, 67, 0], [2, "1141_2", 17, 790, 54, 67, 2], [2, "1254", 17, 782, 54, 32, 0], [2, "1244", 58, 798, 30, 67, 0], [2, "1141_2", 77, 819, 54, 67, 2], [2, "1254", 77, 811, 54, 32, 0], [2, "1244", 119, 830, 30, 67, 0], [2, "1141_2", 138, 851, 54, 67, 2], [2, "1254", 138, 843, 54, 32, 0], [2, "1244", 179, 859, 30, 67, 0], [2, "1141_2", 198, 880, 54, 67, 2], [2, "1254", 198, 872, 54, 32, 0], [2, "1244", 240, 889, 30, 67, 0], [2, "1141_2", 259, 910, 54, 67, 2], [2, "1254", 259, 902, 54, 32, 0], [2, "1244", 300, 918, 30, 67, 0], [2, "1141_2", 319, 939, 54, 67, 2], [2, "1254", 319, 931, 54, 32, 0], [2, "1141_2", 968, 361, 54, 67, 0], [2, "1254", 969, 353, 54, 32, 2], [2, "1244", 952, 369, 30, 67, 2], [2, "1141_2", 909, 391, 54, 67, 0], [2, "1254", 911, 381, 54, 32, 2], [2, "1244", 897, 396, 30, 67, 2]]}, {"type": 4, "obj": [[2, "1243", 347, 86, 50, 49, 0], [2, "1243", 742, 108, 50, 49, 2], [2, "1243", 464, 140, 50, 49, 0], [2, "1243", 613, 163, 50, 49, 2], [2, "1243", 972, 593, 50, 49, 0], [2, "1243", 1070, 637, 50, 49, 0], [2, "973_2", 676, 738, 42, 70, 2], [2, "973_2", 718, 738, 42, 70, 0], [2, "967_1", 361, 882, 24, 41, 2]]}, {"type": 3, "obj": [[2, "1244", 166, 441, 30, 67, 0], [2, "1244", 182, 449, 30, 67, 0], [2, "1141_2", 200, 467, 54, 67, 2], [2, "1254", 198, 460, 54, 32, 0], [2, "1248", 1108, 768, 22, 52, 0], [2, "1244", 954, 31, 30, 67, 2], [2, "1249", 9, 718, 22, 53, 0], [2, "1249", -13, 730, 22, 53, 0], [2, "1250", 75, 713, 22, 52, 2], [2, "1250", 58, 666, 22, 52, 2], [2, "1250", 77, 674, 22, 52, 2], [2, "1250", 99, 687, 22, 52, 2], [2, "1251", 679, 360, 22, 52, 2], [2, "1251", 659, 350, 22, 52, 2], [2, "1251", 637, 338, 22, 52, 2], [2, "1250", 698, 328, 22, 52, 2], [2, "1244", 672, 505, 30, 67, 2], [2, "1141_1", 631, 524, 54, 67, 0], [2, "1254", 631, 516, 54, 32, 2], [2, "1244", 613, 534, 30, 67, 2], [2, "1141_1", 574, 553, 54, 67, 0], [2, "1254", 574, 545, 54, 32, 2], [2, "965_1", 815, 259, 40, 33, 2], [2, "967_1", 824, 228, 24, 41, 2], [2, "1248", 968, 944, 22, 52, 0], [2, "1248", 990, 932, 22, 52, 0], [2, "1248", 1011, 921, 22, 52, 0], [2, "1248", 1030, 912, 22, 52, 0], [2, "1248", 1051, 902, 22, 52, 0], [2, "1248", 1072, 893, 22, 52, 0], [2, "1248", 1094, 883, 22, 52, 0], [2, "1248", 1094, 923, 22, 52, 0], [2, "1248", 1115, 874, 22, 52, 0], [2, "1248", 1115, 913, 22, 52, 0], [2, "1248", 20, 218, 22, 52, 2], [2, "1248", -2, 208, 22, 52, 2], [2, "1249", -2, 249, 22, 53, 2], [2, "92_2", 1, 588, 40, 45, 0], [2, "92_2", 1, 565, 40, 45, 0], [2, "92_2", 1, 542, 40, 45, 0], [2, "92_2", 1, 519, 40, 45, 0], [2, "92_2", 41, 593, 40, 45, 0], [2, "92_2", 41, 570, 40, 45, 0], [2, "92_2", 41, 547, 40, 45, 0], [2, "92_2", 41, 524, 40, 45, 0], [2, "92_2", 70, 638, 40, 45, 0], [2, "92_2", 70, 615, 40, 45, 0], [2, "92_2", 70, 592, 40, 45, 0], [2, "92_2", 70, 569, 40, 45, 0], [2, "92_2", 96, 640, 40, 45, 0], [2, "92_2", 96, 617, 40, 45, 0], [2, "92_2", 122, 642, 40, 45, 0], [2, "92_2", 117, 684, 40, 45, 0], [2, "92_2", 117, 662, 40, 45, 0], [2, "92_2", 135, 692, 40, 45, 0], [2, "92_2", 135, 671, 40, 45, 0], [2, "1248", 197, 543, 22, 52, 2], [2, "1248", 218, 553, 22, 52, 2], [2, "1248", 156, 485, 22, 52, 2], [2, "1248", 178, 496, 22, 52, 2], [2, "1248", 200, 507, 22, 52, 2], [2, "1248", 221, 517, 22, 52, 2], [2, "1248", 243, 527, 22, 52, 2], [2, "1248", 306, 506, 22, 52, 0], [2, "1248", 284, 517, 22, 52, 0], [2, "1248", 262, 528, 22, 52, 0], [2, "1248", 394, 462, 22, 52, 0], [2, "1248", 372, 473, 22, 52, 0], [2, "1248", 350, 484, 22, 52, 0], [2, "1248", 328, 495, 22, 52, 0], [2, "1248", 481, 421, 22, 52, 0], [2, "1248", 459, 432, 22, 52, 0], [2, "1248", 437, 443, 22, 52, 0], [2, "1248", 415, 454, 22, 52, 0], [2, "1248", 570, 376, 22, 52, 0], [2, "1248", 548, 387, 22, 52, 0], [2, "1248", 526, 398, 22, 52, 0], [2, "1248", 504, 409, 22, 52, 0], [2, "1248", 569, 399, 22, 52, 0], [2, "1248", 547, 410, 22, 52, 0], [2, "1248", 525, 421, 22, 52, 0], [2, "1248", 503, 432, 22, 52, 0], [2, "1249", 572, 310, 22, 53, 2], [2, "1249", 561, 303, 22, 53, 2], [2, "1249", 539, 291, 22, 53, 2], [2, "1249", 517, 281, 22, 53, 2], [2, "1248", 496, 278, 22, 52, 0], [2, "1248", 572, 269, 22, 52, 2], [2, "1248", 558, 262, 22, 52, 2], [2, "1248", 539, 252, 22, 52, 2], [2, "1247", 454, 259, 22, 51, 0], [2, "1247", 476, 249, 22, 51, 0], [2, "1247", 496, 239, 22, 51, 0], [2, "1248", 517, 241, 22, 52, 2], [2, "1251", 593, 351, 22, 52, 0], [2, "1251", 615, 340, 22, 52, 0], [2, "1249", 615, 256, 22, 53, 0], [2, "1249", 593, 267, 22, 53, 0], [2, "92_2", 637, 303, 40, 45, 0], [2, "92_2", 637, 280, 40, 45, 0], [2, "92_2", 637, 257, 40, 45, 0], [2, "92_2", 637, 234, 40, 45, 0], [2, "92_2", 665, 295, 40, 45, 0], [2, "92_2", 665, 272, 40, 45, 0], [2, "92_2", 691, 313, 40, 45, 0], [2, "1249", 698, 405, 22, 53, 2], [2, "1249", 720, 416, 22, 53, 2], [2, "1249", 739, 424, 22, 53, 2], [2, "1248", 718, 374, 22, 52, 2], [2, "1248", 739, 384, 22, 52, 2], [2, "1248", 760, 392, 22, 52, 2], [2, "1254", 698, 354, 54, 32, 0], [2, "1254", 736, 373, 54, 32, 0], [2, "1248", 766, 187, 22, 52, 0], [2, "1248", 787, 176, 22, 52, 0], [2, "1248", 874, 132, 22, 52, 0], [2, "1248", 852, 143, 22, 52, 0], [2, "1248", 830, 154, 22, 52, 0], [2, "1248", 808, 165, 22, 52, 0], [2, "1248", 239, 943, 22, 52, 2], [2, "1248", 217, 931, 22, 52, 2], [2, "1248", 195, 921, 22, 52, 2], [2, "1248", 174, 911, 22, 52, 2], [2, "1249", 174, 952, 22, 53, 2], [2, "1248", 152, 900, 22, 52, 2], [2, "1249", 152, 941, 22, 53, 2], [2, "1248", 130, 889, 22, 52, 2], [2, "1249", 130, 930, 22, 53, 2], [2, "1248", 109, 879, 22, 52, 2], [2, "1249", 109, 920, 22, 53, 2], [2, "1248", 87, 868, 22, 52, 2], [2, "1249", 87, 909, 22, 53, 2], [2, "1250", 87, 952, 22, 52, 2], [2, "1248", 65, 857, 22, 52, 2], [2, "1249", 65, 898, 22, 53, 2], [2, "1250", 65, 941, 22, 52, 2], [2, "1248", 43, 846, 22, 52, 2], [2, "1249", 43, 887, 22, 53, 2], [2, "1250", 43, 930, 22, 52, 2], [2, "1248", 21, 835, 22, 52, 2], [2, "1249", 21, 876, 22, 53, 2], [2, "1250", 21, 919, 22, 52, 2], [2, "1248", -1, 824, 22, 52, 2], [2, "1249", -1, 865, 22, 53, 2], [2, "1250", -1, 908, 22, 52, 2], [2, "1251", -1, 949, 22, 52, 2], [2, "92_2", 856, 483, 40, 45, 2], [2, "92_2", 855, 461, 40, 45, 2], [2, "92_2", 854, 439, 40, 45, 2], [2, "1248", 893, 487, 22, 52, 2], [2, "1248", 893, 447, 22, 52, 2], [2, "1248", 915, 492, 22, 52, 0], [2, "1248", 915, 451, 22, 52, 0], [2, "1248", 1003, 407, 22, 52, 0], [2, "1249", 1003, 487, 22, 53, 0], [2, "1248", 1003, 447, 22, 52, 0], [2, "1250", 1003, 529, 22, 52, 0], [2, "1250", 1003, 570, 22, 52, 0], [2, "1251", 1003, 611, 22, 52, 0], [2, "1248", 1025, 396, 22, 52, 0], [2, "1249", 1025, 476, 22, 53, 0], [2, "1248", 1025, 436, 22, 52, 0], [2, "1250", 1025, 518, 22, 52, 0], [2, "1250", 1025, 559, 22, 52, 0], [2, "1251", 1025, 600, 22, 52, 0], [2, "1248", 1047, 385, 22, 52, 0], [2, "1249", 1047, 465, 22, 53, 0], [2, "1248", 1047, 425, 22, 52, 0], [2, "1250", 1047, 507, 22, 52, 0], [2, "1250", 1047, 548, 22, 52, 0], [2, "1251", 1047, 589, 22, 52, 0], [2, "1248", 1068, 374, 22, 52, 0], [2, "1249", 1068, 454, 22, 53, 0], [2, "1248", 1068, 414, 22, 52, 0], [2, "1250", 1068, 496, 22, 52, 0], [2, "1250", 1068, 537, 22, 52, 0], [2, "1251", 1068, 578, 22, 52, 0], [2, "1248", 1090, 363, 22, 52, 0], [2, "1249", 1090, 443, 22, 53, 0], [2, "1248", 1090, 403, 22, 52, 0], [2, "1250", 1090, 485, 22, 52, 0], [2, "1250", 1090, 526, 22, 52, 0], [2, "1251", 1090, 567, 22, 52, 0], [2, "1248", 1112, 352, 22, 52, 0], [2, "1249", 1112, 432, 22, 53, 0], [2, "1248", 1112, 392, 22, 52, 0], [2, "1250", 1112, 474, 22, 52, 0], [2, "1250", 1112, 515, 22, 52, 0], [2, "1251", 1112, 556, 22, 52, 0], [2, "1244", 322, 778, 30, 67, 0], [2, "1141_1", 340, 796, 54, 67, 2], [2, "1254", 340, 789, 54, 32, 0], [2, "1244", 380, 805, 30, 67, 0], [2, "1141_1", 342, 826, 54, 67, 0], [2, "1254", 342, 818, 54, 32, 2], [2, "1244", 324, 836, 30, 67, 2], [2, "1141_1", 283, 855, 54, 67, 0], [2, "1254", 283, 847, 54, 32, 2], [2, "1244", 265, 865, 30, 67, 2], [2, "1141_1", 226, 884, 54, 67, 0], [2, "1254", 226, 876, 54, 32, 2], [2, "1141_1", 596, -52, 54, 67, 0], [2, "1244", 585, -46, 30, 67, 2], [2, "1141_1", 543, -26, 54, 67, 0], [2, "1254", 544, -34, 54, 32, 2], [2, "1141_1", 438, -50, 54, 67, 2], [2, "1254", 440, -55, 54, 32, 0], [2, "1244", 476, -42, 30, 67, 0], [2, "1141_1", 492, -24, 54, 67, 2], [2, "1254", 493, -30, 54, 32, 0], [2, "1244", 524, -18, 30, 67, 0], [2, "1141_1", 401, -42, 54, 67, 0], [2, "1254", 402, -50, 54, 32, 2], [2, "1244", 391, -36, 30, 67, 2], [2, "1141_1", 348, -16, 54, 67, 0], [2, "1254", 349, -24, 54, 32, 2], [2, "1244", 338, -10, 30, 67, 2], [2, "1141_1", 296, 10, 54, 67, 0], [2, "1254", 297, 2, 54, 32, 2], [2, "1244", 285, 16, 30, 67, 2], [2, "1141_1", 243, 36, 54, 67, 0], [2, "1254", 244, 28, 54, 32, 2], [2, "1244", 611, 369, 30, 67, 2], [2, "1141_1", 570, 389, 54, 67, 0], [2, "1254", 571, 380, 54, 32, 2], [2, "1244", 554, 395, 30, 67, 2], [2, "1141_1", 512, 417, 54, 67, 0], [2, "1254", 513, 409, 54, 32, 2], [2, "1244", 502, 423, 30, 67, 2], [2, "1141_1", 460, 443, 54, 67, 0], [2, "1254", 461, 435, 54, 32, 2], [2, "1244", 450, 449, 30, 67, 2], [2, "1141_1", 407, 469, 54, 67, 0], [2, "1254", 408, 461, 54, 32, 2], [2, "1244", 397, 475, 30, 67, 2], [2, "1141_1", 355, 495, 54, 67, 0], [2, "1254", 356, 487, 54, 32, 2], [2, "1244", 344, 501, 30, 67, 2], [2, "1141_1", 302, 521, 54, 67, 0], [2, "1254", 303, 513, 54, 32, 2], [2, "1244", 293, 527, 30, 67, 2], [2, "1141_1", 251, 547, 54, 67, 0], [2, "1254", 252, 538, 54, 32, 2], [2, "1244", 239, 554, 30, 67, 2], [2, "1244", 237, 553, 30, 67, 0], [2, "1141_1", 255, 573, 54, 67, 2], [2, "1254", 256, 566, 54, 32, 0], [2, "1244", 297, 583, 30, 67, 0], [2, "1141_1", 314, 604, 54, 67, 2], [2, "1254", 315, 597, 54, 32, 0], [2, "92_2", 143, 502, 40, 45, 0], [2, "92_2", 179, 566, 40, 45, 0], [2, "92_2", 159, 529, 40, 45, 0], [2, "1255", 145, 491, 26, 27, 0], [2, "1244", 157, 2, 30, 67, 0], [2, "1141_1", 173, 23, 54, 67, 2], [2, "1254", 173, 14, 54, 32, 0], [2, "1244", 210, 29, 30, 67, 0], [2, "1141_1", 227, 50, 54, 67, 2], [2, "1254", 228, 43, 54, 32, 0], [2, "1245", 34, 7, 76, 130, 0], [2, "1253", 112, 6, 22, 35, 0], [2, "1253", -5, 44, 22, 35, 0], [2, "1252", 119, 32, 96, 71, 0], [2, "1252", 1, 80, 96, 71, 0], [2, "703_2", 204, 92, 16, 23, 2], [2, "703_2", 83, 138, 16, 23, 2], [2, "1244", -23, 141, 30, 67, 0], [2, "1244", 266, 58, 30, 67, 0], [2, "1141_1", 281, 78, 54, 67, 2], [2, "1254", 282, 70, 54, 32, 0], [2, "1244", 316, 84, 30, 67, 0], [2, "1141_1", 333, 106, 54, 67, 2], [2, "1254", 332, 98, 54, 32, 0], [2, "249_1", 347, 107, 48, 72, 0], [2, "43_8", 367, 147, 82, 58, 0], [2, "43_8", 405, 166, 82, 58, 0], [2, "249_1", 461, 162, 48, 72, 0], [2, "1244", 499, 175, 30, 67, 2], [2, "1141_1", 458, 196, 54, 67, 0], [2, "1254", 457, 189, 54, 32, 2], [2, "1244", 443, 203, 30, 67, 2], [2, "1141_1", 401, 224, 54, 67, 0], [2, "1254", 401, 216, 54, 32, 2], [2, "1244", 386, 232, 30, 67, 2], [2, "1141_1", 344, 251, 54, 67, 0], [2, "1254", 344, 243, 54, 32, 2], [2, "1244", 333, 258, 30, 67, 2], [2, "1141_1", 402, 252, 54, 67, 2], [2, "1254", 402, 245, 54, 32, 0], [2, "1244", 440, 259, 30, 67, 0], [2, "1141_1", 458, 280, 54, 67, 2], [2, "1254", 459, 273, 54, 32, 0], [2, "1244", 500, 290, 30, 67, 0], [2, "1141_1", 517, 311, 54, 67, 2], [2, "1254", 519, 303, 54, 32, 0], [2, "1244", 561, 319, 30, 67, 2], [2, "700_2", 153, 461, 22, 48, 0], [2, "700_2", 130, 461, 22, 48, 2], [2, "1241", 160, 500, 38, 50, 0], [2, "264_2", 225, 602, 66, 34, 2], [2, "264_2", 263, 620, 66, 34, 2], [2, "1241", 186, 544, 38, 50, 0], [2, "1255", 213, 589, 26, 27, 0], [2, "1240", 70, 502, 84, 50, 0], [2, "1240", 81, 517, 84, 50, 0], [2, "1240", 91, 531, 84, 50, 0], [2, "1240", 101, 545, 84, 50, 0], [2, "1240", 110, 560, 84, 50, 0], [2, "1240", 119, 574, 84, 50, 0], [2, "1240", 129, 588, 84, 50, 0], [2, "1240", 141, 601, 84, 50, 0], [2, "1255", 55, 534, 26, 27, 0], [2, "1241", 70, 543, 38, 50, 0], [2, "1241", 96, 587, 38, 50, 0], [2, "1255", 123, 632, 26, 27, 0], [2, "10_1", 200, 616, 50, 26, 0], [2, "10_1", 172, 628, 50, 26, 2], [2, "10_1", 147, 641, 50, 26, 2], [2, "264_2", 155, 672, 66, 34, 2], [2, "10_1", 226, 629, 50, 26, 0], [2, "10_1", 198, 641, 50, 26, 2], [2, "10_1", 172, 654, 50, 26, 2], [2, "10_1", 251, 641, 50, 26, 0], [2, "10_1", 224, 654, 50, 26, 2], [2, "10_1", 198, 667, 50, 26, 0], [2, "1244", 353, 614, 30, 67, 0], [2, "251_2", 300, 637, 68, 57, 2], [2, "1240", 252, 680, 84, 50, 0], [2, "1240", 239, 667, 84, 50, 0], [2, "1240", 226, 654, 84, 50, 0], [2, "1240", 232, 691, 84, 50, 0], [2, "1240", 217, 679, 84, 50, 0], [2, "1240", 206, 665, 84, 50, 0], [2, "251_2", 194, 690, 68, 57, 2], [2, "1244", 173, 678, 30, 67, 2], [2, "1141_1", 131, 700, 54, 67, 0], [2, "1254", 132, 691, 54, 32, 2], [2, "1244", 116, 706, 30, 67, 2], [2, "1141_1", 73, 728, 54, 67, 0], [2, "1254", 76, 718, 54, 32, 2], [2, "1244", 57, 737, 30, 67, 2], [2, "1141_1", 14, 759, 54, 67, 0], [2, "1254", 14, 751, 54, 32, 2], [2, "1141_1", 628, 389, 54, 67, 2], [2, "1254", 630, 382, 54, 32, 0], [2, "1244", 666, 397, 30, 67, 0], [2, "1141_1", 682, 417, 54, 67, 2], [2, "1254", 683, 410, 54, 32, 0], [2, "1244", 714, 423, 30, 67, 0], [2, "1141_1", 699, -44, 54, 67, 2], [2, "1244", 735, -37, 30, 67, 0], [2, "1141_1", 753, -16, 54, 67, 2], [2, "1254", 754, -24, 54, 32, 0], [2, "1244", 796, -7, 30, 67, 0], [2, "1141_1", 815, 14, 54, 67, 2], [2, "1254", 814, 6, 54, 32, 0], [2, "1244", 851, 21, 30, 67, 0], [2, "1141_1", 869, 42, 54, 67, 2], [2, "1254", 870, 34, 54, 32, 0], [2, "972_1", 669, 729, 66, 54, 0], [2, "971_1", 612, 754, 58, 50, 0], [2, "972_1", 719, 736, 66, 54, 2], [2, "971_1", 765, 752, 58, 50, 2], [2, "972_1", 625, 782, 66, 54, 2], [2, "971_1", 673, 797, 58, 50, 2], [2, "971_1", 749, 774, 58, 50, 0], [2, "971_1", 705, 796, 58, 50, 0], [2, "958_1", 640, 917, 90, 68, 2], [2, "958_1", 419, 520, 90, 68, 0], [2, "965_1", 352, 913, 40, 33, 2], [2, "965_1", 378, 561, 40, 33, 2], [2, "967_1", 387, 530, 24, 41, 2], [2, "965_1", 487, 505, 40, 33, 2], [2, "967_1", 496, 474, 24, 41, 2], [2, "1255", 780, 392, 26, 27, 2], [2, "1241", 753, 401, 38, 50, 2], [2, "1241", 728, 444, 38, 50, 2], [2, "1240", 745, 481, 84, 50, 2], [2, "1240", 754, 466, 84, 50, 2], [2, "1240", 763, 451, 84, 50, 2], [2, "1240", 772, 436, 84, 50, 2], [2, "1240", 782, 422, 84, 50, 2], [2, "1240", 791, 407, 84, 50, 2], [2, "1255", 864, 433, 26, 27, 2], [2, "1241", 837, 442, 38, 50, 2], [2, "1241", 812, 485, 38, 50, 2], [2, "700_2", 860, 402, 22, 48, 2], [2, "700_2", 883, 402, 22, 48, 0], [2, "700_2", 766, 360, 22, 48, 2], [2, "700_2", 789, 360, 22, 48, 0], [2, "1248", 937, 440, 22, 52, 0], [2, "1249", 937, 520, 22, 53, 0], [2, "1248", 937, 480, 22, 52, 0], [2, "1248", 959, 429, 22, 52, 0], [2, "1249", 959, 509, 22, 53, 0], [2, "1248", 959, 469, 22, 52, 0], [2, "1250", 959, 551, 22, 52, 0], [2, "1248", 981, 418, 22, 52, 0], [2, "1249", 981, 498, 22, 53, 0], [2, "1248", 981, 458, 22, 52, 0], [2, "1250", 981, 540, 22, 52, 0], [2, "1250", 981, 581, 22, 52, 0], [2, "1250", 937, 562, 22, 52, 0], [2, "1250", 959, 592, 22, 52, 0], [2, "961_1", 1048, 466, 28, 36, 2], [2, "962_1", 1062, 482, 18, 19, 2], [2, "541_3", 1066, 489, 38, 42, 0], [2, "541_3", 1094, 518, 38, 42, 0], [2, "541_3", 1122, 547, 38, 42, 0], [2, "541_3", 1150, 575, 38, 42, 0], [2, "272_2", 1083, 585, 72, 54, 0], [2, "272_2", 1036, 609, 72, 54, 0], [2, "272_2", 1000, 627, 72, 54, 0], [2, "1244", 824, 474, 30, 67, 0], [2, "1141_1", 841, 494, 54, 67, 2], [2, "1254", 842, 487, 54, 32, 0], [2, "1244", 870, 499, 30, 67, 0], [2, "1141_1", 887, 518, 54, 67, 2], [2, "1254", 888, 511, 54, 32, 0], [2, "1244", 927, 527, 30, 67, 0], [2, "1141_1", 888, 549, 54, 67, 0], [2, "1254", 888, 541, 54, 32, 2], [2, "1244", 870, 559, 30, 67, 2], [2, "1141_1", 885, 579, 54, 67, 2], [2, "1254", 886, 572, 54, 32, 0], [2, "1244", 925, 588, 30, 67, 0], [2, "1141_1", 941, 608, 54, 67, 2], [2, "1254", 942, 601, 54, 32, 0], [2, "1244", 981, 617, 30, 67, 0], [2, "269_5", 1091, 619, 110, 58, 0], [2, "269_5", 1050, 639, 110, 58, 0], [2, "269_5", 1011, 659, 110, 58, 0], [2, "272_2", 1122, 656, 72, 54, 0], [2, "1244", 1079, 662, 30, 67, 0], [2, "1141_1", 1098, 683, 54, 67, 2], [2, "1254", 1097, 675, 54, 32, 0], [2, "1244", 1138, 692, 30, 67, 0], [2, "961_1", 78, 873, 28, 36, 0], [2, "541_3", 54, 892, 38, 42, 2], [2, "962_1", 76, 890, 18, 19, 0], [2, "541_3", 28, 918, 38, 42, 2], [2, "1248", 961, 89, 22, 52, 0], [2, "1248", 939, 100, 22, 52, 0], [2, "1248", 917, 111, 22, 52, 0], [2, "1248", 895, 122, 22, 52, 0], [2, "1248", 961, 112, 22, 52, 0], [2, "1248", 939, 123, 22, 52, 0], [2, "1248", 917, 134, 22, 52, 0], [2, "1248", 895, 145, 22, 52, 0], [2, "1248", 873, 156, 22, 52, 0], [2, "1248", 851, 167, 22, 52, 0], [2, "1248", 829, 178, 22, 52, 0], [2, "1248", 807, 189, 22, 52, 0], [2, "1248", 788, 199, 22, 52, 0], [2, "1248", 768, 209, 22, 52, 0], [2, "92_2", 738, 201, 40, 45, 0], [2, "92_2", 774, 265, 40, 45, 0], [2, "92_2", 754, 228, 40, 45, 0], [2, "1255", 740, 190, 26, 27, 0], [2, "1241", 755, 199, 38, 50, 0], [2, "1241", 781, 243, 38, 50, 0], [2, "1255", 807, 288, 26, 27, 0], [2, "1240", 666, 202, 84, 50, 0], [2, "1240", 676, 216, 84, 50, 0], [2, "1240", 686, 230, 84, 50, 0], [2, "1240", 696, 244, 84, 50, 0], [2, "1240", 705, 259, 84, 50, 0], [2, "1240", 714, 273, 84, 50, 0], [2, "1240", 724, 287, 84, 50, 0], [2, "1240", 736, 300, 84, 50, 0], [2, "1255", 650, 233, 26, 27, 0], [2, "1241", 665, 242, 38, 50, 0], [2, "1241", 691, 286, 38, 50, 0], [2, "1255", 718, 331, 26, 27, 0], [2, "249_1", 615, 184, 48, 72, 2], [2, "1244", 518, 186, 30, 67, 2], [2, "1141_1", 980, 106, 54, 67, 2], [2, "1254", 979, 97, 54, 32, 0], [2, "1244", 1021, 115, 30, 67, 0], [2, "1141_1", 1039, 135, 54, 67, 2], [2, "1254", 1040, 128, 54, 32, 0], [2, "1244", 1079, 145, 30, 67, 0], [2, "1141_1", 1098, 165, 54, 67, 2], [2, "1254", 1099, 158, 54, 32, 0], [2, "1244", 1137, 174, 30, 67, 0], [2, "1248", 698, 364, 22, 52, 2], [2, "954_4", 708, 344, 24, 25, 0], [2, "1250", 616, 299, 22, 52, 0], [2, "1250", 594, 310, 22, 52, 0], [2, "1253", 994, 485, 22, 35, 0], [2, "1253", 1105, 434, 22, 35, 0], [2, "1248", 1072, 933, 22, 52, 0], [2, "1248", 1050, 943, 22, 52, 0], [2, "1248", 1028, 953, 22, 52, 0], [2, "1253", 929, 118, 22, 35, 0], [2, "1253", 818, 169, 22, 35, 0], [2, "90_2", 965, 137, 28, 36, 0], [2, "90_2", 941, 147, 28, 36, 0], [2, "90_2", 855, 188, 28, 36, 0], [2, "90_2", 1100, 201, 28, 36, 0], [2, "1242", 952, 216, 96, 56, 0], [2, "1244", 571, 555, 30, 67, 0], [2, "1141_1", 589, 575, 54, 67, 2], [2, "1254", 590, 568, 54, 32, 0], [2, "1244", 631, 585, 30, 67, 0], [2, "969_1", 869, 247, 36, 30, 0], [2, "969_1", 622, 454, 36, 30, 0], [2, "965_1", 577, 461, 40, 33, 0], [2, "955_5", 760, 446, 20, 18, 0], [2, "955_5", 810, 532, 20, 18, 0], [2, "90_2", 173, 731, 28, 36, 0], [2, "11_4", 814, 62, 32, 29, 0], [2, "1250", 637, 298, 22, 52, 2], [2, "1250", 656, 308, 22, 52, 2], [2, "1250", 677, 319, 22, 52, 2], [2, "1249", 22, 572, 22, 53, 0], [2, "1248", 1, 573, 22, 52, 2], [2, "1250", 64, 627, 22, 52, 2], [2, "1249", 42, 572, 22, 53, 2], [2, "1249", 64, 583, 22, 53, 2], [2, "1250", 42, 616, 22, 52, 2], [2, "1250", 86, 639, 22, 52, 2], [2, "1250", 95, 643, 22, 52, 2], [2, "264_2", 117, 654, 66, 34, 2], [2, "1249", 53, 699, 22, 53, 0], [2, "1249", 31, 710, 22, 53, 0], [2, "1250", 21, 616, 22, 52, 0], [2, "954_4", 951, 664, 24, 25, 0], [2, "955_5", 294, 228, 20, 18, 0], [2, "959_2", 776, 327, 40, 22, 0], [2, "958_1", 304, 177, 90, 68, 0], [2, "90_2", 867, 599, 28, 36, 0], [2, "969_1", 813, 533, 36, 30, 2], [2, "955_5", 358, 675, 20, 18, 0], [2, "955_5", 645, 848, 20, 18, 0], [2, "955_5", 1095, 248, 20, 18, 0], [2, "955_5", 710, 144, 20, 18, 0], [2, "11_4", 437, -6, 32, 29, 0], [2, "90_2", 551, 15, 28, 36, 0], [2, "955_5", 270, 759, 20, 18, 2], [2, "955_5", 249, 623, 20, 18, 2], [2, "955_5", 725, 621, 20, 18, 2], [2, "1242", 620, -20, 96, 56, 2], [2, "1141_2", 1100, 713, 54, 67, 0], [2, "1254", 1099, 705, 54, 32, 2], [2, "1244", 1088, 719, 30, 67, 2], [2, "1141_2", 1045, 738, 54, 67, 0], [2, "1254", 1048, 733, 54, 32, 2], [2, "1244", 1039, 747, 30, 67, 2], [2, "1141_1", 1055, 767, 54, 67, 2], [2, "1254", 1056, 760, 54, 32, 0], [2, "1244", 1095, 776, 30, 67, 0], [2, "1141_1", 1112, 792, 54, 67, 2], [2, "1254", 1113, 785, 54, 32, 0], [2, "90_2", 1049, 799, 28, 36, 0], [2, "90_2", 1028, 789, 28, 36, 0], [2, "1141_2", -17, 472, 54, 67, 2], [2, "1254", -19, 464, 54, 32, 0], [2, "1244", 22, 479, 30, 67, 0], [2, "700_2", 38, 501, 22, 48, 2], [2, "700_2", 61, 501, 22, 48, 0], [2, "1244", -18, 610, 30, 67, 0], [2, "1141_2", 1, 631, 54, 67, 2], [2, "1254", 1, 623, 54, 32, 0], [2, "1244", 44, 642, 30, 67, 2], [2, "1141_2", 2, 664, 54, 67, 0], [2, "1254", 4, 654, 54, 32, 2], [2, "1244", -10, 669, 30, 67, 2], [2, "1254", -51, 680, 54, 32, 2], [2, "1141_2", -51, 687, 54, 67, 0], [2, "1141_2", -7, 160, 54, 67, 2], [2, "1254", -6, 154, 54, 32, 0], [2, "1244", 35, 167, 30, 67, 0], [2, "1141_2", 51, 186, 54, 67, 2], [2, "1254", 52, 180, 54, 32, 0], [2, "1244", 92, 198, 30, 67, 2], [2, "1141_1", 51, 219, 54, 67, 0], [2, "1254", 51, 212, 54, 32, 2], [2, "1244", 36, 226, 30, 67, 2], [2, "1141_1", -6, 247, 54, 67, 0], [2, "1254", -6, 238, 54, 32, 2], [2, "1244", -21, 255, 30, 67, 2], [2, "11_4", 102, 254, 32, 29, 0], [2, "1141_2", 913, 51, 54, 67, 0], [2, "1254", 914, 43, 54, 32, 2], [2, "1244", 897, 59, 30, 67, 2], [2, "1141_2", 854, 81, 54, 67, 0], [2, "1254", 856, 71, 54, 32, 2], [2, "1244", 842, 86, 30, 67, 2], [2, "1141_2", 799, 105, 54, 67, 0], [2, "1254", 800, 97, 54, 32, 2], [2, "1244", 792, 109, 30, 67, 2], [2, "1141_2", 749, 131, 54, 67, 0], [2, "1254", 750, 122, 54, 32, 2], [2, "249_1", 742, 128, 48, 72, 2], [2, "1141_2", 520, 339, 54, 67, 0], [2, "1254", 521, 331, 54, 32, 2], [2, "1244", 507, 346, 30, 67, 2], [2, "1141_2", 463, 368, 54, 67, 0], [2, "1254", 465, 358, 54, 32, 2], [2, "1244", 454, 372, 30, 67, 2], [2, "1141_2", 413, 392, 54, 67, 0], [2, "1254", 414, 384, 54, 32, 2], [2, "1244", 400, 399, 30, 67, 2], [2, "1141_2", 356, 421, 54, 67, 0], [2, "1254", 358, 411, 54, 32, 2], [2, "1244", 347, 425, 30, 67, 2], [2, "1141_2", 306, 445, 54, 67, 0], [2, "1254", 307, 437, 54, 32, 2], [2, "1244", 293, 452, 30, 67, 2], [2, "1141_2", 249, 474, 54, 67, 0], [2, "1254", 251, 464, 54, 32, 2], [2, "1244", 240, 478, 30, 67, 2], [2, "1141_2", 536, 207, 54, 67, 2], [2, "1254", 535, 198, 54, 32, 0], [2, "1141_2", 579, 211, 54, 67, 0], [2, "1254", 583, 202, 54, 32, 2], [2, "1244", 571, 213, 30, 67, 0]]}, {"type": 3, "obj": [[2, "1141_1", 114, -36, 54, 67, 0], [2, "1141_1", 71, -63, 54, 67, 0], [2, "1141_1", 71, -18, 54, 67, 0], [2, "1141_1", 28, -56, 54, 67, 0], [2, "1141_1", 28, -11, 54, 67, 0], [2, "1249", 55, 42, 22, 53, 2], [2, "1248", 75, 11, 22, 52, 2], [2, "1249", 75, 51, 22, 53, 2], [2, "43_9", 15, 70, 82, 58, 2], [2, "1141_1", 109, 18, 54, 67, 0], [2, "1141_1", 92, 15, 54, 67, 0], [2, "1141_1", -14, 80, 54, 67, 0], [2, "1141_1", -15, 35, 54, 67, 0], [2, "1141_1", -15, -11, 54, 67, 0], [2, "1141_1", -15, -53, 54, 67, 0], [2, "1141_1", 94, 26, 54, 67, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, 12, 11, 5, 6, -1, -1, 48, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 51, 50, 51, 50, -1, 44, 45, 41, 42, -1, -1, -1, -1, 12, 11, 11, 11, 5, 5, 15, 20, 20, 9, 10, -1, -1, -1, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 4, 5, 6, 48, 47, 51, 41, 42, -1, -1, -1, -1, 12, 11, 15, 20, 20, 20, 20, 20, 20, 20, 20, 21, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 4, 5, 15, 8, 9, 5, 6, 48, 47, 51, 50, -1, -1, -1, 19, 20, 20, 21, 27, 26, 20, 20, 20, 20, 21, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, 7, 8, 14, 20, 8, 26, 25, -1, -1, 48, 51, 50, -1, -1, 16, 17, 20, 18, 24, 23, 23, 27, 26, 26, 9, 6, -1, -1, -1, -1, -1, -1, -1, -1, 44, -1, -1, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 17, 27, 8, 20, 9, 5, 11, 10, 48, 47, -1, 51, 50, -1, -1, -1, -1, -1, -1, 24, 23, 23, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 50, 44, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 27, 8, 8, 8, 14, 13, -1, -1, -1, 51, 50, 50, -1, -1, -1, -1, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 44, 44, 45, 47, 41, 42, 48, 51, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, 19, 20, 21, 17, 23, 22, -1, -1, -1, 48, 47, 47, -1, -1, -1, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 40, 41, 41, 42, 36, 35, 29, 30, -1, 51, 50, -1, -1, 43, 44, 45, -1, 4, 5, 15, 21, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 44, 45, 42, -1, -1, -1, 48, 47, 47, 41, 42, -1, 48, 47, 43, 44, 45, 41, 42, -1, 7, 8, 8, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 41, 42, -1, -1, 12, 11, 10, -1, -1, -1, -1, -1, -1, 36, 40, 41, 42, 4, 5, 5, 15, 8, 21, 22, -1, 36, 35, -1, -1, 44, 45, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 15, 14, 13, -1, -1, -1, -1, 36, 35, 39, -1, -1, -1, 7, 8, 8, 8, 8, 9, 10, -1, 43, 44, 44, 45, 41, 42, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 5, 5, 6, 19, 20, 20, 9, 10, -1, -1, -1, 39, 38, -1, -1, 12, 11, 15, 8, 8, 8, 8, 9, 13, -1, 40, 41, 41, 42, -1, -1, -1, 48, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 8, 8, 9, 15, 26, 26, 26, 13, -1, -1, -1, -1, -1, -1, -1, 19, 20, 8, 20, 21, 23, 27, 8, 9, 6, 12, 11, 11, 10, -1, -1, -1, 36, 35, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 8, 20, 20, 20, 21, 23, 22, -1, -1, -1, -1, -1, -1, -1, 16, 17, 23, 17, 18, -1, 19, 8, 8, 9, 15, 14, 14, 13, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 27, 20, 21, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 27, 8, 8, 8, 21, 18, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, -1, 24, 23, 23, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 30, -1, -1, -1, -1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 50, 44, 45, 41, 42, 48, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 30, -1, -1, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 47, 41, 40, 41, 42, -1, -1, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, -1, -1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 35, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, 40, 41, 42, -1, -1, -1, -1, 14, 35, 34, -1, -1, -1, -1, 28, 29, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, -1, -1, -1, -1, 31, 32, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, 36, 35, 14, 45, 45, 41, 42, -1, -1, -1, -1, 51, 50, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, 50, 35, 34, -1, -1, -1, -1, -1, -1, 48, 51, 50, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 47, -1, 35, 34, -1, -1, -1, -1, -1, -1, 48, 47, 47, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 5, 6, -1, -1, -1, -1, -1, 40, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 41, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 8, 9, 11, 11, 5, 6, -1, -1, -1, -1, -1, -1, 12, 11, 11, 10, -1, -1, -1, -1, 40, 51, 50, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 11, 15, 14, 14, 14, 14, 8, 9, 11, 10, -1, 12, 11, 11, 15, 14, 14, 13, -1, -1, -1, -1, -1, 48, 47, 46, 47, 51, 50, 35, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, -1, -1, 4, 5, 11, 15, 14, 14, 14, 26, 14, 14, 14, 14, 14, 25, -1, 19, 20, 14, 14, 14, 14, 9, 11, 11, 10, -1, -1, -1, -1, -1, 36, 48, 47, 38, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, 7, 8, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 25, -1, -1, 16, 17, 14, 14, 14, 14, 14, 14, 13, -1, -1, -1, -1, -1, 39, 38, 38, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, 16, 17, 27, 26, 14, 14, 14, 14, 14, 14, 14, 14, 14, 22, -1, -1, -1, -1, -1, -1, 14, 14, 14, 14, 9, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 27, 26, 14, 26, 14, 14, 14, 21, 22, -1, -1, -1, -1, -1, -1, 14, 14, 14, 14, 26, 26, 13, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, 41, 42, 33, 35, 34, -1, -1, -1, -1, -1, 36, 35, -1, -1, -1, 24, 23, 23, 27, 26, 14, 26, 25, -1, -1, 12, 11, 14, 14, 14, 14, 14, 14, 20, 21, 23, 22, -1, -1, -1, -1, 48, 47, 35, -1, -1, -1, -1, -1, -1, 38, 37, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, 19, 20, 14, 14, 9, 11, 11, 15, 14, 14, 14, 14, 14, 26, 21, 17, 18, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, -1, -1, -1, 44, 45, -1, -1, -1, 16, 17, 27, 26, 14, 14, 14, 14, 14, 14, 14, 14, 21, 23, 22, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 41, 42, -1, -1, -1, -1, -1, 24, 23, 23, 23, 23, 23, 27, 26, 25, -1, -1, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 23, 22, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, -1, -1, 0, 0, 1, 0, 1, -1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, -1, -1, -1, -1, -1, -1, -1, -1, 57, 57, 57, 57, 57, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 3, 2, 0, 1, -1, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, -1, -1, -1, -1, -1, -1, 0, 1, -1, -1, -1, -1, 0, 1, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 2, 3, 2, 3, 1, -1, -1, -1, 57, 57, 57, 57, 57, 57, 57, 57, 57, -1, -1, -1, 0, 1, 1, 1, 3, 0, 1, 1, -1, 2, 3, 2, 2, 3, 2, 0, 1, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 3, 2, 3, 0, 1, 0, 1, 57, 57, 57, 57, 57, 57, 57, 57, -1, 0, 1, 2, 3, 3, 3, 2, 2, 3, 0, 1, -1, -1, -1, 0, 1, 0, 2, 3, 1, 2, 3, 0, 1, 0, 1, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 57, 57, 57, 57, 57, 57, 57, 57, 0, 2, 3, 2, 0, 1, 1, 3, 2, 3, 0, 1, 0, 1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 3, 2, 3, 2, 3, -1, -1, -1, -1, -1, 57, -1, 57, 57, 57, 2, 2, 0, 1, 2, 3, 3, 0, 1, 0, 1, 0, 1, 3, 0, 0, 1, 0, 1, 2, 0, 1, 2, 3, 0, 1, 1, 0, 0, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, -1, 57, 57, 57, -1, -1, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 2, 3, 2, 3, 2, 3, 3, 2, 2, 3, 3, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 2, 3, 0, 1, 57, 57, -1, -1, -1, 2, 3, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 3, 2, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 0, 1, 0, 1, -1, -1, -1, -1, 0, 1, 2, 3, 0, 1, 0, 0, 0, 1, 3, 57, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 2, 3, 2, 3, 2, 3, -1, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, -1, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 2, 2, 3, 3, 0, -1, -1, -1, 0, 1, 0, 1, 1, 0, 1, 3, 0, 1, 0, 0, 1, 0, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, 3, -1, -1, -1, 2, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, -1, 0, 1, 2, 3, 2, 3, 3, 2, 3, 2, 2, 3, 2, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 3, 2, 0, 1, 0, 1, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 2, 0, 1, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 2, 3, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 2, 3, 0, 1, 3, 2, 3, 2, 3, 2, 3, 2, 3, -1, 1, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, -1, -1, -1, 3, 2, 3, 0, 1, 0, 0, 1, 3, 0, 1, 0, 1, 2, 3, 2, 0, 1, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 1, 0, 1, 0, 0, 1, 3, -1, 0, 1, -1, -1, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 2, 3, 0, 1, 1, 2, 3, -1, -1, 2, 3, -1, -1, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, 1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 3, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, -1, -1, -1, -1, -1, 0, 0, 1, 2, 2, 2, 0, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 0, 1, 3, 2, 0, 1, -1, 2, 3, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, 0, 2, 0, 1, 3, 0, 1, 1, 0, 0, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 0, 1, 2, 0, 1, 2, 3, 3, 2, 2, 2, 3, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 2, 2, 3, 0, 2, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 2, 3, 2, 0, 0, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 0, 1, 0, 1, 2, 2, 3, 0, 2, 3, 0, 1, 0, 1, 0, 0, 1, 0, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 2, 3, 2, 3, 2, 2, 3, 2, 3, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 2, 3, 2, 3, 3, 2, 3, 2, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 3, 1, 2, 3, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 2, 3, 0, 2, 3, 0, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 2, 3, 3, 3, 2, 3, 2, 3, 0, 1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 1, 0, 1, 1, 0, 1, 1, 2, 2, 3, 2, 0, 0, 1, 2, 3, 1, 0, 2, 3, 2, 3, 1, 0, 0, 1, 1, 0, 1, 2, 3, 0, 1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 2, 3, 3, 2, 3, 3, 2, 2, 2, 3, 0, 1, 3, 2, 3, 2, 0, 1, 3, 2, 2, 3, 0, 1, 3, 3, 3, 2, 3, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 2, 3, 0, 0, 1, 1, 2, 3, 1, 1, 0, 1, 2, 3, 0, 1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 3, 2, 2, 2, 2, 3, 3, 3, 2, 3, 0, 1, 0, 1, 0, 1, 3, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 2, 3, 1, 0, 0, 1, 0, 1, 3, 1, 2, 3, 2, 3, 2, 3, 2, 3, 1, 0, 0, 1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 3, 0, 1, 2, 3, 2, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 2, 3, 0, 1, 2, 3, 1, 3, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 1, 2, 2, 0, 1, 1, 0, 1, 0, 1, 3, 0, 0, 1, 3, 3, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 3, 1, 0, 2, 3, 1, 2, 3, 1, 1, 0, 1, 0, 1, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, -1, -1, -1, -1, -1, -1]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]}