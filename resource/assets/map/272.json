{"mW": 960, "mH": 768, "tW": 24, "tH": 24, "tiles": [["1331", 0, 3, 3], ["1333", 0, 3, 2], ["1333", 2, 3, 2], ["1333", 1, 3, 2], ["1333", 3, 3, 2], ["1314", 0, 3, 2], ["1314", 2, 3, 2], ["1314", 1, 3, 2], ["1314", 3, 3, 2], ["1382", 0, 3, 3]], "layers": [{"type": 4, "obj": [[2, "1231_2", 383, -93, 114, 162, 0], [2, "85_6", 465, 70, 48, 53, 2], [2, "1231_2", 861, 107, 114, 162, 0], [2, "1161_1", 911, 272, 54, 52, 2], [2, "1231_2", 384, 222, 114, 162, 0], [2, "1231_2", -54, 280, 114, 162, 0], [2, "1231_2", 883, 309, 114, 162, 0], [2, "85_6", 274, 469, 48, 53, 2], [2, "1231_2", 67, 390, 114, 162, 0], [2, "1231_2", 25, 599, 114, 162, 0], [2, "1231_2", 873, 603, 114, 162, 0], [2, "1368_1", 16, 724, 50, 42, 0], [2, "1368_1", 150, 726, 50, 42, 2], [2, "1368_1", 44, 727, 50, 42, 0], [2, "1371_1", 12, 744, 42, 35, 0], [2, "1368_1", 647, 737, 50, 42, 2], [2, "1371_1", 40, 747, 42, 35, 0], [2, "429_4", 207, 727, 64, 63, 0], [2, "1368_1", 586, 750, 50, 42, 2], [2, "1371_1", 658, 758, 42, 35, 2], [2, "1371_1", 597, 771, 42, 35, 2], [2, "1160_1", 496, 733, 88, 75, 0]]}, {"type": 3, "obj": [[2, "433_2", 881, 365, 62, 61, 0], [2, "392_2", -33, 681, 118, 69, 0], [2, "392_2", 109, 471, 118, 69, 0], [2, "392_2", 432, 54, 118, 69, 2], [2, "85_6", 641, -23, 48, 53, 2], [2, "392_2", 568, 723, 118, 69, 2], [2, "392_2", -70, 183, 118, 69, 2], [2, "435_2", 31, 204, 50, 77, 0], [2, "435_2", 728, 724, 50, 77, 0], [2, "433_2", 666, 724, 62, 61, 2], [2, "436_4", 644, 324, 34, 28, 2], [2, "433_2", 602, 334, 62, 61, 2], [2, "433_2", 540, 354, 62, 61, 2], [2, "435_2", 502, 375, 50, 77, 2], [2, "1161_1", 468, 417, 54, 52, 0], [2, "441_3", 565, 468, 28, 21, 2], [2, "433_2", 774, 271, 62, 61, 0], [2, "433_2", 721, 275, 62, 61, 2], [2, "439_3", 577, 454, 64, 42, 0], [2, "21_2", 807, 742, 28, 24, 0], [2, "1159_1", 707, 189, 36, 37, 0], [2, "1159_1", 436, 428, 36, 37, 0], [2, "1159_1", 187, 255, 36, 37, 0], [2, "22_3", 144, 265, 62, 38, 0], [2, "1160_1", 181, 495, 88, 75, 2], [2, "1159_1", 600, 631, 36, 37, 2], [2, "262_3", 427, -7, 48, 39, 2], [2, "1161_1", 487, -27, 54, 52, 2], [2, "1160_1", 456, -11, 88, 75, 0], [2, "1160_1", 444, 84, 88, 75, 0], [2, "390_2", 491, 117, 102, 80, 0], [2, "390_2", 554, 152, 102, 80, 0], [2, "390_2", 615, 187, 102, 80, 0], [2, "1160_1", 688, 211, 88, 75, 0], [2, "1161_1", 671, 245, 54, 52, 2], [2, "1163_1", 908, 239, 80, 54, 0], [2, "1334", 578, 321, 30, 30, 0], [2, "1161_1", 658, 282, 54, 52, 2], [2, "1160_1", 823, -22, 88, 75, 2], [2, "1159_1", 221, 258, 36, 37, 2], [2, "22_3", 255, 725, 62, 38, 0], [2, "1159_1", 295, 731, 36, 37, 0], [2, "1334", 754, 696, 30, 30, 0], [2, "1332", 573, -11, 106, 57, 0], [2, "1159_1", 565, -7, 36, 37, 2], [2, "262_3", 561, 327, 48, 39, 0], [2, "1160_1", 884, -18, 88, 75, 0], [2, "1160_1", 764, 2, 88, 75, 0], [2, "1159_1", 759, 45, 36, 37, 2], [2, "1161_1", 799, -9, 54, 52, 2], [2, "1161_1", 875, 30, 54, 52, 2], [2, "85_6", 820, 36, 48, 53, 2], [2, "429_4", 908, 27, 64, 63, 0], [2, "1161_1", 693, 284, 54, 52, 0], [2, "262_3", 749, 727, 48, 39, 0], [2, "1160_1", -17, -26, 88, 75, 2], [2, "1160_1", 898, 289, 88, 75, 0], [2, "1160_1", 825, 276, 88, 75, 0], [2, "1159_1", 855, 323, 36, 37, 2], [2, "1334", 404, 295, 30, 30, 0], [2, "1334", 39, 120, 30, 30, 2], [2, "1334", 860, 237, 30, 30, 0], [2, "1334", 39, 304, 30, 30, 2], [2, "262_3", 262, -16, 48, 39, 0], [2, "1159_1", 812, 294, 36, 37, 2], [2, "429_4", 520, -16, 64, 63, 0], [2, "1161_1", 880, 336, 54, 52, 2], [2, "1162_1", 915, 347, 36, 46, 0], [2, "1161_1", 832, 251, 54, 52, 2], [2, "1161_1", 863, 260, 54, 52, 0], [2, "1160_1", 11, 418, 88, 75, 2], [2, "1162_1", -8, 451, 36, 46, 0], [2, "1161_1", -11, 483, 54, 52, 0], [2, "1161_1", 58, 457, 54, 52, 0], [2, "1160_1", 10, 479, 88, 75, 0], [2, "63_3", 73, 647, 16, 31, 0], [2, "63_3", 45, 627, 16, 31, 0], [2, "63_3", 87, 679, 16, 31, 0], [2, "1159_1", 107, 686, 36, 37, 2], [2, "262_3", 113, 649, 48, 39, 0], [2, "262_3", 1, 530, 48, 39, 0], [2, "1332", 860, 521, 106, 57, 0], [2, "63_3", 1, 150, 16, 31, 0], [2, "1334", 696, 31, 30, 30, 0], [2, "1334", 695, 499, 30, 30, 0], [2, "1334", 195, 597, 30, 30, 0], [2, "1334", 353, 665, 30, 30, 0], [2, "1334", 338, 513, 30, 30, 0], [2, "1334", 686, 181, 30, 30, 1], [2, "1334", 14, 615, 30, 30, 0], [2, "1334", 363, 29, 30, 30, 0], [2, "1334", 333, 729, 30, 30, 0], [2, "1334", 625, 255, 30, 30, 0], [2, "1334", 823, 227, 30, 30, 0], [2, "1334", 564, 38, 30, 30, 0], [2, "1334", 101, 105, 30, 30, 0], [2, "1334", 531, 203, 30, 30, 0], [2, "1334", 856, 732, 30, 30, 0], [2, "1334", 907, 720, 30, 30, 0], [2, "1334", 548, 603, 30, 30, 1], [2, "1159_1", 60, 526, 36, 37, 0], [2, "21_2", 785, 475, 28, 24, 0], [2, "1334", 408, 122, 30, 30, 0], [2, "1334", 866, 128, 30, 30, 0], [2, "1231_2", 695, -113, 114, 162, 0], [2, "85_6", 69, 686, 48, 53, 2], [2, "1159_1", 935, 182, 36, 37, 2], [2, "429_4", 736, 235, 64, 63, 0], [2, "1332", 138, 707, 106, 57, 2], [2, "85_6", 636, 260, 48, 53, 0], [2, "1161_1", -1, 11, 54, 52, 0], [2, "1161_1", 447, 63, 54, 52, 2], [2, "262_3", 500, 16, 48, 39, 0], [2, "1159_1", 512, 80, 36, 37, 2], [2, "1334", 636, 658, 30, 30, 0], [2, "1334", 737, 599, 30, 30, 0], [2, "1334", 814, 662, 30, 30, 0], [2, "1334", 868, 598, 30, 30, 0], [2, "1334", 930, 607, 30, 30, 0], [2, "85_6", 50, 14, 48, 53, 2], [2, "1231_2", 41, -111, 114, 162, 0], [2, "1159_1", -13, 35, 36, 37, 2], [2, "1334", 132, 338, 30, 30, 0], [2, "1334", 325, 338, 30, 30, 2], [2, "1334", 208, 428, 30, 30, 0], [2, "1163_1", 388, 98, 80, 54, 2], [2, "1159_1", 426, 122, 36, 37, 2], [2, "429_4", 626, 703, 64, 63, 0], [2, "1160_1", 436, 447, 88, 75, 0], [2, "390_2", 373, 474, 102, 80, 2], [2, "1160_1", 300, 484, 88, 75, 0], [2, "1162_1", 289, 508, 36, 46, 0], [2, "1162_1", 381, 528, 36, 46, 0], [2, "1161_1", 352, 534, 54, 52, 0], [2, "1161_1", 393, 545, 54, 52, 0], [2, "1162_1", 374, 557, 36, 46, 0], [2, "1160_1", 243, 506, 88, 75, 0], [2, "1334", 502, 540, 30, 30, 0], [2, "1334", 264, 603, 30, 30, 0], [2, "1159_1", 419, 568, 36, 37, 0], [2, "1161_1", 309, 553, 54, 52, 2], [2, "429_4", 149, 506, 64, 63, 0], [2, "262_3", 227, 553, 48, 39, 0], [2, "63_3", 41, 161, 16, 31, 2], [2, "63_3", 70, 193, 16, 31, 0], [2, "1159_1", 45, 204, 36, 37, 2], [2, "262_3", 226, 231, 48, 39, 0], [2, "1332", 181, 271, 106, 57, 0], [2, "1334", 289, 394, 30, 30, 0], [2, "21_2", 390, 382, 28, 24, 0], [2, "1161_1", 650, 485, 54, 52, 0], [2, "262_3", 635, 506, 48, 39, 0], [2, "21_2", 366, 706, 28, 24, 0], [2, "1334", 468, 670, 30, 30, 1], [2, "262_3", 840, 527, 48, 39, 0], [2, "21_2", 27, 317, 28, 24, 0], [2, "63_3", 212, 36, 16, 31, 2], [2, "1334", 150, 148, 30, 30, 2], [2, "1334", 293, 185, 30, 30, 0], [2, "1334", 353, 163, 30, 30, 2], [2, "1371_1", 694, 262, 42, 35, 0], [2, "1368_1", 698, 242, 50, 42, 0], [2, "1371_1", 281, 547, 42, 35, 0], [2, "1368_1", 285, 527, 50, 42, 0], [2, "1371_1", 606, 445, 42, 35, 0], [2, "1368_1", 610, 425, 50, 42, 0], [2, "1371_1", 928, 549, 42, 35, 0], [2, "1368_1", 932, 529, 50, 42, 0], [2, "1371_1", 810, 309, 42, 35, 0], [2, "1368_1", 814, 289, 50, 42, 0], [2, "21_2", 596, 658, 28, 24, 0], [2, "437_3", 752, 450, 20, 19, 0], [2, "439_3", 507, 441, 64, 42, 2], [2, "437_3", 816, 423, 20, 19, 0], [2, "439_3", 758, 428, 64, 42, 0], [2, "437_3", 836, 423, 20, 19, 0], [2, "439_3", 849, 427, 64, 42, 2], [2, "1371_1", 786, 424, 42, 35, 0], [2, "1368_1", 790, 404, 50, 42, 0], [2, "1159_1", 514, 449, 36, 37, 2], [2, "1371_1", 547, 458, 42, 35, 2], [2, "1368_1", 536, 437, 50, 42, 2], [2, "1371_1", 655, 316, 42, 35, 0], [2, "1368_1", 659, 296, 50, 42, 0], [2, "1159_1", 463, 492, 36, 37, 2], [2, "1371_1", 534, 366, 42, 35, 0], [2, "1368_1", 837, 413, 50, 42, 2], [2, "445_1", 702, 449, 50, 22, 1], [2, "445_1", 653, 447, 50, 22, 3], [2, "1371_1", 705, 720, 42, 35, 0], [2, "1368_1", 709, 700, 50, 42, 0], [2, "1161_1", -23, 725, 54, 52, 0], [2, "445_1", 23, 269, 50, 22, 1], [2, "445_1", -21, 280, 50, 22, 1], [2, "1371_1", -19, 203, 42, 35, 0], [2, "1368_1", -15, 183, 50, 42, 0], [2, "1371_1", 621, 427, 42, 35, 0], [2, "85_6", 36, 250, 48, 53, 2], [2, "1159_1", -7, 258, 36, 37, 2], [2, "1368_1", 569, 733, 50, 42, 2], [2, "1371_1", 580, 754, 42, 35, 2], [2, "21_2", 192, 563, 28, 24, 0], [2, "262_3", 146, 565, 48, 39, 0], [2, "445_1", 900, 443, 50, 22, 1], [2, "85_6", 931, 414, 48, 53, 2], [2, "1231_2", 880, -6, 114, 162, 0], [2, "1160_1", 914, 135, 88, 75, 0], [2, "85_6", 500, 473, 48, 53, 2], [2, "1371_1", 520, 70, 42, 35, 0], [2, "1368_1", 524, 50, 50, 42, 0], [2, "63_3", 258, 32, 16, 31, 2], [2, "1371_1", 229, 56, 42, 35, 0], [2, "1368_1", 233, 36, 50, 42, 0], [2, "445_1", 24, 731, 50, 22, 1], [2, "445_1", 49, 735, 50, 22, 1]]}, {"type": 2, "data": [43, -1, 43, 43, 43, -1, -1, -1, -1, -1, -1, 45, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 55, 33, 34, 40, 39, 45, 46, 47, 37, 37, 37, 37, 37, -1, 37, 37, 49, 50, 43, 43, 38, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 49, 52, 52, 56, 55, 54, -1, 33, 34, 34, -1, -1, 37, 37, 37, 37, 37, 56, 49, 50, 46, 52, 51, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, 33, 34, 44, -1, 53, 52, 51, -1, -1, 53, 52, 51, -1, 45, 46, 37, -1, 37, 37, 37, 37, 37, 37, 45, 46, 47, -1, -1, -1, -1, -1, -1, 53, 52, 51, -1, -1, -1, -1, -1, 45, 46, 55, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 11, 45, 52, -1, 53, 52, 56, 55, -1, -1, -1, -1, 9, 10, 10, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 40, 39, -1, -1, -1, -1, -1, 9, 10, 20, 25, 14, 16, 16, 15, -1, -1, 53, 52, -1, -1, -1, -1, 12, 25, 25, 13, 14, 16, 15, -1, -1, -1, -1, -1, -1, -1, 33, 34, -1, -1, 40, 39, -1, -1, -1, -1, 12, 13, 25, 25, 19, 31, 19, 18, -1, -1, 33, 34, -1, -1, -1, -1, 21, 32, 31, 25, 25, 19, 14, 16, 10, 11, -1, -1, -1, -1, 45, 46, 46, -1, -1, -1, -1, -1, -1, -1, 29, 32, 31, 31, 25, 25, 25, 14, 15, -1, -1, -1, 33, 34, 35, -1, -1, 24, 25, 25, 25, 25, 25, 25, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 32, 31, 25, 25, 19, 18, -1, 33, 34, 45, 46, 54, -1, -1, 21, 32, 31, 25, 25, 25, 25, 25, 14, 16, 15, -1, 9, 10, 16, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 28, 28, 22, 23, -1, 45, 46, -1, 52, 51, -1, -1, -1, 29, 28, 28, 28, 32, 25, 31, 25, 31, 14, 16, 20, 13, 13, 13, 14, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, -1, -1, -1, -1, -1, 33, 34, 28, 32, 31, 31, 31, 31, 31, 19, 19, 19, 19, 14, 15, -1, -1, 45, 56, 55, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 35, -1, -1, 46, 51, -1, 33, 34, 34, -1, -1, -1, -1, 12, 31, 31, 31, 31, 31, 19, 19, 19, 19, 19, 18, -1, -1, -1, 53, 52, 55, 55, 55, -1, 33, 40, 39, -1, 53, 52, 43, 38, 34, 35, -1, -1, 53, 52, 52, 56, -1, -1, 9, 20, 19, 31, 31, 31, 25, 25, -1, 25, 26, 28, 27, -1, -1, -1, -1, 45, 46, -1, -1, -1, -1, -1, 17, 16, 15, 43, 43, -1, 51, -1, -1, -1, -1, -1, -1, 53, 51, 9, 20, 19, 31, 31, 31, 26, 22, 22, 46, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 19, 19, 19, 19, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 31, 31, 26, 28, 27, 33, 34, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 28, 22, 23, -1, -1, 53, 52, 52, 51, -1, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 13, 14, 15, -1, -1, -1, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 11, 56, -1, -1, -1, -1, -1, -1, -1, 41, -1, -1, -1, -1, 9, 20, 13, 13, 13, 14, 16, 15, 53, 53, 55, 54, -1, -1, 33, 34, 40, 39, -1, -1, -1, -1, 17, 16, 31, 31, 31, 16, 37, -1, 48, 49, -1, -1, -1, -1, -1, 41, 40, 39, 39, 13, 13, 13, 13, 19, 25, 14, 16, 15, 52, 51, -1, -1, 36, 37, 55, 54, -1, -1, 9, 10, 20, 31, 31, 31, 31, 19, 56, 55, 50, 50, 56, 38, 39, -1, -1, -1, -1, 9, 20, 13, 13, 13, 13, -1, 25, 25, 31, 18, -1, -1, -1, -1, -1, -1, 52, 51, -1, -1, 12, 13, 25, 25, 14, 31, 31, 30, 49, 50, 52, 51, 53, 52, 56, 49, 49, 49, 50, 55, 13, 13, 13, 13, 25, -1, 26, 22, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 32, 25, 25, 31, 30, -1, 27, 46, 47, -1, -1, -1, -1, -1, 45, 46, 47, 53, 52, 56, 49, 50, 56, 55, 50, 51, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 10, 10, 11, -1, 29, 32, 25, 31, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, 53, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 13, 13, 14, 16, 15, 21, 22, 28, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 31, 31, 31, 31, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 20, 19, 14, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 28, 28, 32, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 13, 25, 25, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 32, 31, 31, 31, 25, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 39, -1, 29, 28, 28, 28, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 39, -1, -1, -1, -1, -1, -1, -1, 40, 39, 45, 52, 51, -1, 45, 46, 52, 51, -1, 48, 55, 54, -1, -1, -1, -1, -1, 41, 40, 39, 41, 40, 39, -1, 33, 34, 39, -1, 9, 41, 40, 39, 11, -1, -1, 41, 40, 39]}, {"type": 2, "data": [0, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 2, 0, 1, 2, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 4, 0, 0, 1, 2, 2, 3, 3, 4, 5, 3, 4, 5, 3, 4, 0, 1, 2, 0, 1, 2, 0, 1, 2, 5, 5, 3, 4, 5, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 6, 7, 3, 3, 4, 5, 5, 6, 6, 7, 8, 6, 7, 0, 1, 2, 3, 4, 5, 3, 4, 5, 3, 4, 5, 8, 8, 6, 7, 8, 0, 6, 7, 8, 6, 7, 8, 0, 1, 2, 0, 1, 6, 6, 7, 8, 0, 1, 2, 0, 1, 2, 4, 0, 1, 2, 6, 7, 8, 6, 7, 8, 6, 7, 8, 1, 2, 0, 1, 2, 3, 4, 5, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, 3, 4, 5, 6, 7, 3, 0, 0, 1, 0, 1, 2, 2, 1, 2, 4, 0, 1, 2, 0, 1, 2, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 3, 4, 0, 1, 2, 0, 0, 1, 2, 3, 3, 4, 3, 4, 5, 5, 4, 5, 7, 3, 4, 5, 0, 1, 2, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 0, 1, 0, 1, 2, 6, 7, 3, 4, 5, 3, 0, 1, 2, 6, 6, 7, 6, 7, 8, 2, 0, 1, 2, 0, 1, 2, 3, 4, 5, 3, 4, 5, 3, 3, 4, 5, 3, 4, 5, 3, 4, 0, 1, 2, 3, 4, 6, 7, 8, 6, 3, 4, 5, 6, 3, 4, 6, 7, 8, 5, 3, 4, 5, 3, 4, 0, 1, 0, 1, 2, 7, 8, 6, 6, 7, 8, 6, 7, 0, 1, 2, 3, 4, 5, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 7, 8, 6, 7, 8, 6, 0, 1, 2, 1, 3, 4, 3, 4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 65, 65, 2, 2, 5, 3, 4, 5, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3, 4, 5, 4, 6, 7, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 57, 58, 5, 5, 1, 2, 7, 8, 6, 7, 8, 0, 1, 2, 0, 0, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 8, 6, 7, 8, 6, 7, 8, 6, 7, 60, 61, 65, 8, 4, 5, 4, 5, 3, 4, 5, 0, 1, 2, 3, 3, 4, 3, 4, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, 4, 5, 2, 0, 1, 2, 0, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 0, 1, 2, 1, 0, 1, 2, 5, 6, 6, 7, 6, 0, 6, 7, 8, 6, 7, 8, 6, 3, 4, 5, 7, 8, 5, 58, 58, 5, 3, 3, 4, 5, 3, 3, 4, 0, 1, 2, 0, 3, 4, 5, 0, 3, 4, 0, 1, 2, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 1, 6, 7, 57, 58, 59, 57, 58, 59, 57, 58, 61, 57, 58, 57, 0, 1, 2, 4, 5, 3, 6, 7, 8, 0, 6, 7, 3, 4, 5, 3, 4, 5, 6, 7, 0, 3, 4, 5, 3, 4, 4, 62, 60, 61, 62, 60, 61, 62, 60, 61, 59, 60, 61, 60, 3, 4, 5, 7, 8, 0, 1, 2, 0, 3, 4, 5, 6, 7, 0, 0, 1, 2, 0, 1, 3, 6, 7, 8, 0, 63, 64, 65, 63, 64, 65, 63, 64, 65, 63, 64, 62, 57, 58, 62, 6, 7, 8, 0, 1, 3, 4, 5, 3, 0, 1, 2, 1, 1, 3, 3, 4, 5, 3, 4, 6, 7, 57, 58, 59, 57, 58, 59, 57, 58, 59, 57, 58, 59, 57, 58, 59, 60, 61, 57, 3, 4, 0, 1, 2, 6, 0, 1, 0, 1, 2, 5, 4, 4, 6, 6, 7, 8, 6, 7, 8, 3, 60, 61, 62, 60, 61, 62, 60, 61, 62, 60, 61, 62, 60, 61, 62, 57, 58, 60, 4, 5, 3, 0, 1, 2, 3, 4, 3, 4, 0, 1, 2, 4, 5, 0, 1, 2, 7, 8, 1, 6, 63, 64, 65, 63, 64, 65, 63, 64, 65, 63, 64, 65, 4, 5, 65, 60, 61, 60, 7, 8, 6, 3, 4, 5, 6, 7, 6, 7, 3, 4, 5, 4, 5, 3, 4, 5, 5, 3, 4, 5, 65, 63, 64, 65, 6, 7, 8, 7, 8, 3, 3, 4, 5, 1, 2, 5, 8, 8, 3, 7, 8, 6, 7, 8, 0, 1, 2, 0, 6, 7, 8, 2, 8, 6, 7, 8, 8, 6, 7, 8, 6, 7, 8, 6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 8, 4, 5, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 6, 7, 8, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 4, 5, 3, 4, 5, 6, 7, 8, 4, 3, 6, 7, 8, 6, 7, 8, 0, 1, 2, 4, 5, 3, 6, 7, 8, 6, 7, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 6, 7, 8, 6, 7, 8, 0, 1, 6, 7, 6, 7, 8, 4, 5, 6, 7, 3, 4, 5, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 2, 0, 1, 2, 0, 1, 3, 4, 5, 3, 4, 5, 6, 7, 8, 2, 0, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 6, 7, 8, 6, 7, 8, 6, 7, 8, 5, 3, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 0, 1, 2, 0, 1, 2, 1, 0, 1, 2, 0, 1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 6, 7, 8, 6, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 4, 5, 3, 4, 5, 4, 3, 4, 0, 1, 2, 3, 4, 5, 3, 4, 5, 3, 4, 5, 0, 0, 1, 2, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 6, 7, 8, 6, 7, 8, 7, 6, 7, 3, 4, 5, 0, 1, 2, 6, 7, 8, 2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 6, 7, 0, 1, 2, 7, 8, 3, 4, 5, 5, 3, 4, 5, 3, 4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 3, 4, 5, 0, 1, 6, 7, 8, 8, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 6, 7, 8, 3, 4, 5, 0, 1, 2, 5, 2, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 0, 1, 2, 6, 7, 8, 6, 7, 8, 0, 6, 7, 8, 3, 64, 64, 8, 5, 3, 4, 5, 3, 4, 5, 8]}], "blocks": [1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1]}