{"mW": 960, "mH": 720, "tW": 24, "tH": 24, "tiles": [["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["315_6", 0, 3, 3], ["1314", 0, 3, 2], ["1317", 0, 3, 2], ["1317", 2, 3, 2], ["1317", 1, 3, 2], ["1317", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["709_3", 0, 2, 1]], "layers": [{"type": 2, "data": [29, 29, 29, 29, 29, 22, 31, 31, 31, 25, 25, 19, 29, 29, 29, 29, 29, 29, 29, 29, 29, 22, 25, 25, 31, 31, 31, 25, 24, -1, -1, -1, -1, -1, -1, 26, 30, 29, 21, 21, 29, 29, 22, 23, 17, 18, -1, -1, -1, -1, -1, 26, 30, 29, 29, 29, 29, 29, 29, 22, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 21, 21, 21, 22, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 19, 29, 29, 29, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 29, 29, 29, 29, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 9, 9, 9, 2, 3, 21, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 30, 29, 29, 29, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 21, 21, 21, 21, 21, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 29, 29, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 31, 31, 30, 29, 21, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 21, 22, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 30, 6, 9, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 14, 21, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 29, 13, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 29, 11, 15, 15, 9, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 21, 6, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 19, 21, 21, 21, 21, 13, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 17, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 29, 21, 21, 21, 22, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 3, 13, 29, 22, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 29, 29, 29, 29, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 17, -1, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 9, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 5, 21, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 25, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 15, 15, 9, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 1, 2, -1, 10, 9, 1, 2, -1, 10, 9, -1, 0, 9, 15, 15, 9, 8, -1, -1, -1, 10, -1, 4, 5, 13, 13, 29, 6, 9, 15, 9, 1, 2, -1, -1, -1, 10, 9, -1, 13, 13, 6, 15, 14, 29, 29, 6, 15, 14, 13, 6, 3, 29, 29, 29, 29, 6, 15, 15, 15, 14, 13, 6, 29, 29, 29, 29, 29, 29, 29, 29, 29, 6, 7, 7, 7, 14, 21, 21]}, {"type": 4, "obj": [[2, "1174", 855, -16, 104, 88, 0], [2, "1174", 140, -9, 104, 88, 2], [2, "1174", 225, -3, 104, 88, 0], [2, "1174", 436, 4, 104, 88, 0], [2, "1174", 809, 111, 104, 88, 0], [2, "1170", 561, 234, 68, 83, 0], [2, "1170", 519, 235, 68, 83, 2], [2, "1170", 647, 235, 68, 83, 0], [2, "1170", 602, 237, 68, 83, 0], [2, "1174", 391, 239, 104, 88, 2], [2, "1174", -51, 250, 104, 88, 2], [2, "1177", 239, 296, 44, 64, 0], [2, "1177", 427, 380, 44, 64, 0], [2, "1171", 473, 374, 74, 78, 0], [2, "1176", 716, 375, 84, 87, 0], [2, "1170", 517, 410, 68, 83, 2], [2, "1177", 555, 437, 44, 64, 0], [2, "1174", 877, 416, 104, 88, 0], [2, "1177", 620, 622, 44, 64, 0], [2, "1170", 377, 625, 68, 83, 0]]}, {"type": 3, "obj": [[2, "1186", 926, 231, 48, 54, 0], [2, "1187", 896, 233, 38, 23, 0], [2, "1172", 58, -5, 90, 96, 0], [2, "1182", 802, 390, 94, 46, 2], [2, "1182", 195, 60, 94, 46, 2], [2, "1182", 402, 290, 94, 46, 2], [2, "1180", 316, 58, 88, 117, 0], [2, "1187", 4, 421, 38, 23, 0], [2, "1172", 860, 396, 90, 96, 0], [2, "1172", 871, 626, 90, 96, 0], [2, "1172", 48, 248, 90, 96, 2], [2, "1172", 142, 541, 90, 96, 2], [2, "1172", 189, 603, 90, 96, 2], [2, "1186", 49, 195, 48, 54, 0], [2, "1172", 741, 411, 90, 96, 0], [2, "1172", 658, 421, 90, 96, 2], [2, "1172", 357, 316, 90, 96, 2], [2, "1182", 463, 14, 94, 46, 0], [2, "1170", -2, 12, 68, 83, 0], [2, "1184", 263, 27, 50, 36, 2], [2, "1185", 590, -16, 76, 68, 0], [2, "1183", 545, 26, 58, 31, 2], [2, "1176", 269, -3, 84, 87, 0], [2, "1176", 312, 652, 84, 87, 0], [2, "1186", 426, 360, 48, 54, 2], [2, "1187", 394, 319, 38, 23, 0], [2, "1187", 376, 320, 38, 23, 0], [2, "1187", 426, 349, 38, 23, 0], [2, "1187", 418, 333, 38, 23, 2], [2, "1172", 272, 317, 90, 96, 0], [2, "1182", 198, 352, 94, 46, 2], [2, "1183", 247, 389, 58, 31, 2], [2, "1184", 407, 406, 50, 36, 0], [2, "1187", 265, 330, 38, 23, 0], [2, "1187", 291, 319, 38, 23, 2], [2, "1172", 244, 646, 90, 96, 2], [2, "1186", 288, 649, 48, 54, 0], [2, "1172", 526, 655, 90, 96, 2], [2, "1172", 442, 652, 90, 96, 0], [2, "1172", 388, 657, 90, 96, 0], [2, "1186", 559, 651, 48, 54, 0], [2, "1187", 397, 659, 38, 23, 0], [2, "1185", 665, 380, 76, 68, 0], [2, "1172", 660, 608, 90, 96, 0], [2, "1172", 743, 603, 90, 96, 2], [2, "1172", 794, 607, 90, 96, 2], [2, "1186", 659, 607, 48, 54, 2], [2, "1187", 832, 606, 38, 23, 0], [2, "1187", 784, 599, 38, 23, 0], [2, "1187", 683, 423, 38, 23, 0], [2, "1186", 869, 500, 48, 54, 0], [2, "1187", 687, 484, 38, 23, 0], [2, "1180", 376, 47, 88, 117, 0], [2, "1183", 824, 514, 58, 31, 0], [2, "1180", 324, 295, 88, 117, 0], [2, "1177", 727, 446, 44, 64, 0], [2, "1174", 551, -46, 104, 88, 0], [2, "1186", 99, 255, 48, 54, 0], [2, "1172", 57, 526, 90, 96, 0], [2, "1172", -31, 257, 90, 96, 0], [2, "1186", 86, 314, 48, 54, 2], [2, "1187", 62, 337, 38, 23, 0], [2, "1186", 24, 349, 48, 54, 2], [2, "1187", 66, 359, 38, 23, 0], [2, "1186", 855, 617, 48, 54, 0], [2, "1187", 837, 491, 38, 23, 0], [2, "1171", 801, 417, 74, 78, 2], [2, "1186", 185, 555, 48, 54, 0], [2, "1186", 234, 606, 48, 54, 0], [2, "165_2", 265, 631, 42, 37, 0], [2, "1170", 652, 405, 68, 83, 2], [2, "1170", 443, 360, 68, 83, 0], [2, "1187", 124, 519, 38, 23, 0], [2, "1180", -26, 43, 88, 117, 0], [2, "166_3", 122, 501, 30, 35, 0], [2, "166_3", 26, 341, 30, 35, 0], [2, "1170", 782, 152, 68, 83, 0], [2, "1180", 813, 125, 88, 117, 0], [2, "1171", 829, 187, 74, 78, 0], [2, "1182", 536, 302, 94, 46, 0], [2, "1177", 118, 11, 44, 64, 2], [2, "1174", 307, 53, 104, 88, 0], [2, "1182", 311, 156, 94, 46, 0], [2, "1180", 384, 216, 88, 117, 0], [2, "1170", 474, 226, 68, 83, 0], [2, "1183", 491, 313, 58, 31, 2], [2, "1186", 803, -3, 48, 54, 2], [2, "1182", 798, 21, 94, 46, 0], [2, "1182", 270, 415, 94, 46, 0], [2, "1183", 358, 428, 58, 31, 2], [2, "1174", 331, 215, 104, 88, 2], [2, "1182", 407, 148, 94, 46, 0], [2, "1183", 266, 94, 58, 31, 2], [2, "1183", 880, 263, 58, 31, 0], [2, "1183", 518, 78, 58, 31, 0], [2, "1182", 38, 112, 94, 46, 0], [2, "1183", 76, 148, 58, 31, 0], [2, "1183", 462, 327, 58, 31, 0], [2, "1182", 755, 485, 94, 46, 0], [2, "1185", 916, 260, 76, 68, 2], [2, "1185", 409, 428, 76, 68, 2], [2, "1183", 828, 254, 58, 31, 2], [2, "1183", 770, 120, 58, 31, 0], [2, "1185", 479, 460, 76, 68, 2], [2, "1183", 438, 493, 58, 31, 0], [2, "1183", 277, 257, 58, 31, 0], [2, "1183", 555, 498, 58, 31, 0], [2, "1185", 217, 539, 76, 68, 2], [2, "1183", 277, 593, 58, 31, 0], [2, "1183", 587, 628, 58, 31, 0], [2, "1172", 30, 515, 90, 96, 2], [2, "1186", 5, 374, 48, 54, 2], [2, "1186", 12, 484, 48, 54, 0], [2, "1177", -1, 403, 44, 64, 0], [2, "1187", 7, 461, 38, 23, 0], [2, "1186", 915, 503, 48, 54, 2], [2, "1180", 900, 130, 88, 117, 0], [2, "1144_3", 710, 204, 114, 70, 0], [2, "1170", 683, 211, 68, 83, 0], [2, "1177", 696, 243, 44, 64, 0], [2, "1184", 734, 277, 50, 36, 0], [2, "1144_3", 566, 436, 114, 70, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, 87, 87, 87, 87, 87, 87, 87, 87, 75, -1, 87, 87, 87, 87, 87, 75, 87, 87, 87, 87, 87, 87, 87, -1, -1, -1, 62, 63, 68, 86, 87, 87, 81, 81, 81, -1, -1, -1, -1, -1, -1, 87, 87, 87, 87, 87, 87, 87, 75, 75, 87, 88, 89, 90, -1, 87, 87, 87, 87, 87, 87, -1, -1, -1, -1, 47, 58, 63, 68, 83, -1, -1, 88, 84, 94, 87, -1, 51, 51, -1, 87, 88, 88, 84, 84, 84, 94, 93, 87, -1, 84, 85, 79, 78, -1, 87, 88, 89, -1, -1, -1, -1, -1, -1, -1, 50, 51, 69, 52, 53, -1, -1, 94, 93, 87, 84, -1, -1, 87, 88, 84, 85, -1, -1, -1, -1, 91, 90, 94, 93, 93, 79, 78, -1, 87, 88, 85, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, 69, 57, 56, -1, -1, 91, 87, 87, -1, 87, 88, 84, 85, -1, -1, 47, 48, 49, -1, -1, -1, 91, 90, 90, -1, -1, 87, 88, 89, -1, -1, -1, -1, -1, -1, 47, 48, 49, 59, 70, 63, 69, 56, 78, -1, 82, 87, 87, -1, 87, 92, -1, -1, 47, 48, 58, 51, 52, 48, 49, -1, -1, -1, -1, -1, 93, 93, 92, -1, -1, -1, -1, -1, -1, 47, 58, 63, 52, 48, 58, 69, 64, 82, 81, -1, 87, 87, 87, -1, 87, 92, -1, -1, 50, 51, 51, 51, 51, 51, 68, -1, -1, -1, -1, -1, 93, 93, 89, -1, -1, -1, -1, -1, -1, 50, 51, 63, 57, 69, 69, 69, 56, -1, -1, -1, -1, 87, 87, 56, 84, 76, 78, 77, 62, 63, 51, 51, 51, 69, 68, -1, -1, -1, -1, -1, 93, 76, 72, 73, -1, -1, -1, -1, -1, 67, 70, 63, 57, 57, 69, 69, 52, -1, -1, -1, 87, 87, 60, 61, 90, 89, -1, 80, 59, 70, 63, 51, 64, 66, 65, -1, 71, 72, -1, -1, 75, 81, 75, 76, 72, 72, 78, 78, 77, -1, 59, 60, 60, 60, 70, 63, 65, -1, -1, -1, 87, 87, 87, 87, -1, -1, -1, -1, -1, 59, 60, 66, 65, -1, -1, -1, 74, 75, -1, -1, 47, 58, 75, 76, 81, 75, 81, 81, 80, -1, -1, -1, -1, -1, 62, 63, 72, 87, 87, 87, 87, 87, 87, -1, 87, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 94, 78, -1, 58, 51, 51, -1, 51, -1, 51, 84, 84, 85, -1, -1, -1, -1, 71, 87, 88, 90, 90, 90, 90, 90, 94, 93, 84, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 81, 50, -1, 51, 51, 51, 51, 51, 51, 51, 51, -1, -1, -1, -1, 87, 87, 88, 85, 47, 48, 49, -1, -1, 91, 94, 81, 81, 75, 75, 83, -1, -1, -1, -1, -1, -1, 79, 78, -1, -1, -1, -1, -1, -1, 51, 87, 87, 87, 87, 87, 87, 86, 87, 87, 88, 84, 85, -1, 50, 51, 52, 54, 54, 53, 91, 81, 81, 81, 81, -1, -1, -1, -1, -1, -1, 79, 82, 81, -1, -1, 55, -1, -1, -1, 84, 84, 84, 84, 84, 69, -1, 83, 84, 84, 85, -1, 55, 54, 58, 63, 63, 57, 57, 56, -1, 75, 81, 88, 89, -1, -1, -1, -1, -1, -1, 82, 81, -1, -1, 47, 58, 57, -1, 72, 73, -1, -1, -1, -1, -1, -1, 55, 54, 53, -1, 55, 58, 51, 57, 63, 63, 69, 69, 68, -1, 21, 84, -1, -1, -1, -1, -1, -1, -1, -1, 87, -1, -1, 87, 93, 93, 81, 93, 75, 76, 72, 73, -1, 47, 48, 54, 58, 57, 56, -1, 59, 60, 60, 70, 63, 63, 64, 66, 65, 71, -1, -1, -1, -1, 55, 54, 54, 53, -1, -1, -1, -1, 93, 87, 87, 87, 87, 93, -1, -1, 75, 76, -1, 50, 51, 69, 64, 60, 61, -1, -1, 72, 73, 67, 66, 66, 65, -1, 57, 75, 21, -1, 47, 48, 58, 57, 57, 56, -1, -1, -1, -1, 87, 87, 87, 87, 87, 87, 93, -1, -1, -1, -1, 62, 63, 69, 52, 53, -1, 79, 78, 75, 76, -1, -1, -1, -1, -1, 70, 57, 21, -1, 62, 51, 51, 57, 57, 52, 54, 53, -1, -1, -1, 84, 84, 84, 84, 87, 90, 94, 93, 87, 93, 59, 69, 69, 69, 56, -1, 82, 81, -1, 59, 70, 63, 64, 61, -1, 67, 81, 21, -1, 62, 63, 51, 51, 51, 51, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, 77, 91, 84, 94, 93, 93, 93, 90, 90, -1, -1, -1, -1, -1, -1, 67, 60, 61, 79, 78, 81, 81, 21, -1, 59, 60, 66, 66, 70, 69, 69, 68, -1, 47, 48, 48, 48, 49, -1, -1, -1, -1, -1, 86, 81, 87, 87, 94, 69, 68, 86, 87, 87, 87, 88, 94, 93, 92, 82, 81, 81, 81, 21, -1, -1, -1, -1, -1, 67, 66, 66, 65, -1, 50, 51, 57, 57, 52, 48, 49, -1, -1, -1, 83, 84, 84, 85, 62, 63, 68, 83, 84, 84, 84, 85, 91, 90, 89, -1, 81, 81, 75, 21, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 60, 70, 51, 51, 51, 52, 49, -1, -1, -1, -1, -1, -1, 59, 60, 61, 47, 48, 48, 48, 54, 48, 48, 54, 53, -1, -1, -1, 21, 88, 84, 84, 84, 93, 93, -1, -1, -1, -1, -1, -1, 62, 57, 51, 51, 51, 52, 54, 54, 48, 49, -1, -1, -1, -1, -1, 50, 51, 69, 51, 57, 63, 57, 57, 56, -1, -1, -1, 21, 21, 93, 55, 93, 93, 93, 48, -1, -1, -1, -1, -1, 62, 57, 57, 57, 57, 57, 57, 57, 51, 52, 54, 53, -1, -1, -1, 59, 70, 69, 69, 64, 60, 60, 60, 61, -1, -1, -1, 21, 21, 93, 93, 93, 93, 93, 93, -1, -1, -1, -1, -1, 59, 60, 66, 70, 57, 57, 57, 57, 57, 57, 57, 68, -1, -1, -1, -1, 67, 66, 66, 65, -1, -1, -1, -1, -1, -1, -1, 93, 93, 93, 93, 93, 93, 93, 93, 93, -1, -1, 72, 73, -1, -1, -1, 67, 66, 66, 60, 60, 60, 60, 60, 61, -1, -1, -1, 63, 63, 64, 60, 60, 61, -1, -1, -1, -1, -1, -1, 93, 93, 93, 93, 93, 93, 93, 93, 93, 93, -1, 75, 76, 72, 73, 78, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 81, 81, 81, 81, -1, 81, 81, 81, 81, 81, 73, 93, 93, 93, 93, 93, 93, 93, 93, 93, 93, -1, -1, 75, 76, 81, 80, -1, -1, -1, -1, 81, 81, -1, -1, 72, -1, -1, -1, 81, 81, 81, 81, 81, 81, 81, 81, 81, 81, 81, 93, 74, 93, 93, 93, 93, 93, 93, 93, 93, 93, 93, 93, -1, -1, -1, -1, -1, 81, 81, 81, 81, 81, 81, 74, 81, 81, 81, -1, -1, -1, -1, 81, 81, 81, 81, 81, 81, 81, 81]}, {"type": 2, "data": [33, 34, 32, 33, 34, 32, 33, 34, 35, 36, 37, 96, 35, 36, 37, 96, 35, 36, 37, 96, 35, 36, 37, 35, 36, 37, 32, 33, 32, 33, 34, 32, 33, 34, 34, 32, 33, 34, 32, 33, 36, 37, 35, 36, 37, 35, 36, 37, 38, 39, 40, 35, 38, 39, 40, 95, 38, 39, 40, 95, 38, 39, 40, 38, 39, 40, 35, 36, 35, 36, 37, 35, 36, 37, 37, 35, 36, 37, 35, 36, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 95, 96, 95, 96, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 38, 39, 40, 38, 39, 40, 40, 38, 39, 40, 38, 39, 33, 34, 32, 33, 34, 32, 33, 35, 36, 37, 35, 36, 37, 35, 36, 95, 96, 36, 37, 35, 36, 37, 35, 36, 37, 35, 32, 32, 33, 34, 37, 32, 33, 34, 35, 36, 37, 35, 36, 32, 36, 37, 35, 36, 37, 35, 36, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 32, 33, 34, 34, 32, 35, 35, 36, 37, 33, 35, 36, 37, 34, 32, 33, 34, 39, 35, 39, 40, 38, 39, 40, 38, 39, 40, 35, 36, 38, 39, 40, 37, 38, 39, 40, 35, 36, 37, 32, 35, 36, 37, 37, 35, 38, 38, 39, 40, 36, 38, 39, 40, 37, 35, 36, 37, 32, 38, 37, 32, 33, 34, 32, 33, 34, 32, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 35, 38, 39, 40, 32, 33, 34, 35, 36, 37, 39, 35, 36, 37, 40, 38, 39, 13, 35, 36, 40, 35, 32, 33, 34, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 32, 33, 34, 32, 33, 34, 40, 38, 35, 36, 37, 38, 39, 40, 34, 38, 39, 40, 34, 35, 13, 13, 13, 13, 32, 33, 35, 36, 37, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 36, 37, 35, 36, 37, 35, 36, 38, 39, 40, 37, 35, 36, 37, 38, 35, 36, 37, 38, 13, 13, 13, 13, 35, 32, 38, 39, 40, 35, 36, 37, 35, 36, 37, 35, 36, 32, 33, 34, 37, 34, 40, 32, 40, 34, 32, 33, 34, 32, 33, 34, 38, 39, 40, 32, 38, 39, 40, 33, 34, 21, 13, 13, 38, 35, 36, 37, 39, 38, 39, 32, 33, 34, 40, 38, 39, 35, 36, 37, 40, 37, 32, 33, 34, 37, 35, 36, 37, 35, 36, 37, 35, 32, 33, 34, 36, 32, 32, 33, 34, 32, 33, 34, 38, 38, 39, 40, 39, 38, 32, 33, 34, 37, 32, 33, 34, 38, 39, 40, 39, 40, 35, 36, 37, 32, 33, 34, 40, 38, 32, 33, 34, 35, 36, 32, 33, 34, 35, 36, 37, 35, 36, 37, 32, 33, 21, 21, 33, 38, 35, 36, 37, 40, 35, 36, 37, 39, 40, 38, 39, 40, 38, 39, 40, 35, 36, 37, 34, 32, 35, 36, 37, 38, 39, 35, 36, 37, 38, 39, 40, 38, 39, 40, 21, 21, 21, 21, 96, 35, 38, 39, 40, 34, 38, 39, 40, 32, 33, 34, 36, 37, 35, 36, 37, 38, 39, 40, 34, 32, 38, 39, 40, 33, 34, 38, 39, 40, 38, 39, 40, 35, 36, 37, 21, 21, 21, 21, 39, 38, 35, 36, 37, 37, 96, 38, 39, 35, 36, 37, 39, 40, 38, 39, 40, 40, 35, 36, 37, 35, 36, 37, 35, 36, 37, 38, 39, 40, 38, 39, 40, 38, 39, 40, 21, 21, 36, 37, 37, 32, 38, 39, 40, 40, 38, 39, 32, 33, 34, 32, 33, 34, 37, 34, 32, 33, 32, 33, 34, 32, 33, 34, 38, 39, 40, 34, 32, 33, 34, 32, 33, 34, 32, 33, 21, 37, 39, 40, 40, 35, 36, 37, 35, 36, 38, 39, 35, 36, 37, 35, 36, 37, 40, 37, 35, 36, 35, 36, 37, 35, 36, 37, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 21, 40, 40, 39, 40, 38, 39, 40, 38, 39, 40, 38, 38, 39, 40, 38, 39, 40, 33, 32, 33, 34, 38, 39, 40, 38, 37, 40, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 21, 36, 37, 32, 32, 33, 34, 35, 38, 39, 40, 32, 36, 34, 35, 36, 37, 35, 36, 35, 36, 37, 32, 33, 32, 33, 34, 37, 37, 35, 36, 37, 38, 39, 40, 32, 33, 34, 35, 13, 21, 39, 40, 35, 35, 36, 37, 32, 33, 34, 34, 36, 36, 37, 38, 39, 40, 38, 39, 38, 39, 40, 35, 36, 35, 36, 37, 36, 37, 38, 39, 40, 35, 36, 37, 35, 36, 13, 13, 13, 21, 32, 33, 34, 38, 39, 40, 35, 36, 37, 37, 38, 39, 40, 35, 32, 32, 33, 34, 33, 32, 33, 34, 32, 38, 39, 40, 39, 40, 38, 39, 40, 38, 39, 40, 13, 13, 13, 13, 13, 21, 21, 36, 37, 37, 33, 34, 38, 39, 40, 40, 35, 36, 37, 38, 35, 35, 36, 37, 36, 35, 36, 37, 35, 36, 38, 39, 40, 40, 34, 38, 39, 40, 38, 39, 40, 38, 13, 13, 13, 21, 21, 39, 40, 40, 34, 37, 38, 39, 40, 40, 38, 39, 40, 34, 38, 38, 39, 40, 39, 38, 39, 40, 38, 39, 38, 39, 40, 36, 37, 32, 33, 34, 32, 33, 34, 32, 33, 32, 33, 21, 21, 21, 33, 34, 13, 32, 33, 34, 32, 33, 34, 35, 36, 37, 35, 36, 37, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 35, 36, 37, 35, 36, 37, 35, 32, 33, 34, 21, 21, 21, 36, 13, 13, 13, 34, 37, 35, 36, 37, 38, 39, 40, 38, 39, 40, 35, 36, 37, 35, 36, 37, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 35, 36, 37, 21, 21, 21, 13, 13, 13, 13, 13, 96, 38, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 38, 39, 40, 21, 21, 13, 13, 13, 13, 13, 13, 13, 39, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 38, 39, 40, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 34, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 40, 38, 39, 13, 13, 13, 13, 13, 39, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 38, 95, 13, 13, 13, 35, 13, 13, 13, 13, 40, 40, 13, 13, 13, 13, 13, 13, 13, 13, 40, 38, 13, 13, 13, 39, 40, 13, 39, 40, 38, 13, 13, 13, 13, 13, 13, 13, 13, 13]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}