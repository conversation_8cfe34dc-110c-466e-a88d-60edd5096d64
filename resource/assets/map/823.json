{"mW": 960, "mH": 672, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2]], "layers": [{"type": 2, "data": [47, 47, 47, 47, 39, 47, 39, 40, 37, 47, 40, 41, 49, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 41, 42, 44, 41, 49, 48, 39, 31, 31, 47, 47, 31, 47, 47, 47, 47, 47, 47, 47, 47, 35, 43, 35, 36, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 49, 37, 39, 40, 43, 49, 43, 48, 47, 47, 47, 47, 31, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, 44, 43, 37, 47, 45, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 48, -1, -1, -1, -1, 18, 19, 20, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 21, -1, -1, 28, 27, 21, 31, 24, 32, 23, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 32, 31, -1, 28, 32, 31, 31, 31, 40, 43, 41, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 32, 31, 31, 31, 28, 32, 39, 39, 39, 40, 36, -1, -1, -1, -1, -1, -1, -1, 28, 27, 33, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 31, 31, 31, 31, 31, 44, 48, 39, 39, 39, 24, 20, -1, -1, 28, 27, 26, 28, 25, 32, 31, 31, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, 34, 35, 37, 47, 31, 31, 28, 21, 31, 31, 31, 31, 24, 25, 25, 32, 31, 29, 32, 31, 31, 31, 31, 31, 29, 27, 27, 33, 20, -1, -1, 28, 27, 32, 31, 30, -1, -1, -1, -1, -1, 28, 32, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 47, 47, 47, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 24, 33, 27, 32, 31, 31, 31, 24, 25, 25, 33, 33, 33, 32, 31, 31, 31, 31]}, {"type": 4, "obj": [[2, "1310_2", 60, 76, 18, 29, 2], [2, "1310_2", 167, 625, 18, 29, 0], [2, "1308_2", 178, 618, 22, 37, 0], [2, "1308_2", 149, 622, 22, 37, 0]]}, {"type": 3, "obj": [[2, "1152", 928, 160, 38, 26, 0], [2, "214_3", 896, 311, 54, 40, 2], [2, "1149", 485, 571, 40, 82, 0], [2, "1149", 453, 570, 40, 82, 2], [2, "1149", 391, 520, 40, 82, 0], [2, "1149", 362, 518, 40, 82, 2], [2, "1149", 129, 427, 40, 82, 2], [2, "1149", 158, 427, 40, 82, 0], [2, "1149", 338, 493, 40, 82, 2], [2, "1154", 346, 517, 28, 51, 2], [2, "216", 217, 352, 46, 46, 2], [2, "208_3", 190, 333, 78, 40, 2], [2, "214_3", 887, 189, 54, 40, 2], [2, "1150", 80, 425, 48, 85, 0], [2, "1150", 53, 424, 48, 85, 2], [2, "1147", 466, -11, 50, 42, 0], [2, "1150", 590, -51, 48, 85, 0], [2, "1150", 546, -46, 48, 85, 2], [2, "1154", 590, -21, 28, 51, 0], [2, "1149", 799, 564, 40, 82, 0], [2, "1154", 807, 588, 28, 51, 0], [2, "1149", 767, 565, 40, 82, 2], [2, "214_3", 855, 503, 54, 40, 2], [2, "214_3", 894, 366, 54, 40, 2], [2, "214_3", 885, 429, 54, 40, 2], [2, "216", 428, 545, 46, 46, 2], [2, "214_3", 177, 363, 54, 40, 2], [2, "214_3", 153, 406, 54, 40, 2], [2, "214_3", 17, 419, 54, 40, 0], [2, "213_3", 55, 420, 64, 45, 2], [2, "208_3", 874, 213, 78, 40, 3], [2, "214_3", 287, 125, 54, 40, 0], [2, "1150", 828, 0, 48, 85, 0], [2, "1150", 37, 115, 48, 85, 0], [2, "1150", 889, 22, 48, 85, 2], [2, "1149", 319, -14, 40, 82, 0], [2, "1150", 286, -6, 48, 85, 0], [2, "1149", 771, 30, 40, 82, 2], [2, "1149", 921, 54, 40, 82, 2], [2, "1150", 10, 174, 48, 85, 0], [2, "213_3", 894, 281, 64, 45, 2], [2, "1150", 798, 29, 48, 85, 0], [2, "214_3", 671, 61, 54, 40, 2], [2, "214_3", 386, 38, 54, 40, 0], [2, "1310_2", 48, 168, 18, 29, 0], [2, "1150", 147, 9, 48, 85, 0], [2, "1150", 253, -5, 48, 85, 2], [2, "1150", 234, -5, 48, 85, 0], [2, "1150", 190, 0, 48, 85, 2], [2, "1308_2", 182, 6, 22, 37, 0], [2, "1310_2", 361, -12, 18, 29, 0], [2, "1154", 234, 25, 28, 51, 0], [2, "1153", 897, 45, 34, 54, 2], [2, "1150", 855, 5, 48, 85, 2], [2, "214_3", 254, 362, 54, 40, 0], [2, "1150", 31, 55, 48, 85, 0], [2, "1147", 124, 99, 50, 42, 0], [2, "214_3", 670, 567, 54, 40, 2], [2, "213_3", 616, 560, 64, 45, 2], [2, "425_2", 213, 104, 30, 36, 0], [2, "426_2", 179, 96, 26, 22, 0], [2, "214_3", 575, 571, 54, 40, 2], [2, "213_3", 516, 562, 64, 45, 2], [2, "313_2", 662, 396, 70, 44, 0], [2, "152_3", 880, 272, 76, 40, 2], [2, "208_3", 311, 72, 78, 40, 1], [2, "208_3", 201, 142, 78, 40, 3], [2, "208_3", 838, 374, 78, 40, 2], [2, "208_3", 740, 106, 78, 40, 0], [2, "208_3", 645, 45, 78, 40, 3], [2, "205_3", 889, 167, 54, 40, 2], [2, "207_2", 654, 62, 38, 27, 2], [2, "208_3", 594, 24, 78, 40, 0], [2, "207_2", 667, 65, 38, 27, 0], [2, "208_3", 555, 24, 78, 40, 2], [2, "207_2", 542, 31, 38, 27, 0], [2, "208_3", 373, 47, 78, 40, 2], [2, "207_2", 362, 58, 38, 27, 2], [2, "208_3", 453, 25, 78, 40, 0], [2, "205_3", 512, 26, 54, 40, 0], [2, "152_3", 385, 17, 76, 40, 0], [2, "205_3", 145, 135, 54, 40, 0], [2, "208_3", 56, 199, 78, 40, 1], [2, "207_2", 887, 327, 38, 27, 2], [2, "205_3", 893, 343, 54, 40, 2], [2, "207_2", 879, 307, 38, 27, 2], [2, "313_2", 88, 226, 70, 44, 2], [2, "1151", 857, 192, 38, 33, 0], [2, "1151", 662, 64, 38, 33, 2], [2, "208_3", 510, 542, 78, 40, 2], [2, "1310_2", 712, 21, 18, 29, 0], [2, "1310_2", 839, 23, 18, 29, 0], [2, "1310_2", 640, -1, 18, 29, 0], [2, "1308_2", 732, 28, 22, 37, 0], [2, "313_2", 479, 441, 70, 44, 2], [2, "1305_2", 558, 323, 20, 14, 0], [2, "1302_3", 822, 131, 40, 29, 0], [2, "1303_2", 495, 294, 34, 20, 2], [2, "1302_3", 830, 204, 40, 29, 2], [2, "1303_2", 151, 176, 34, 20, 0], [2, "955_4", -11, 346, 20, 18, 0], [2, "955_4", 870, 374, 20, 18, 0], [2, "1303_2", 379, 99, 34, 20, 2], [2, "1303_2", 621, 64, 34, 20, 2], [2, "1151", 861, 329, 38, 33, 2], [2, "1153", 21, 197, 34, 54, 0], [2, "1153", 321, 10, 34, 54, 0], [2, "152_3", 288, 96, 76, 40, 2], [2, "214_3", 112, 420, 54, 40, 2], [2, "208_3", 89, 160, 78, 40, 1], [2, "208_3", 28, 237, 78, 40, 2], [2, "206", 251, 338, 66, 40, 0], [2, "208_3", 697, 72, 78, 40, 0], [2, "207_2", 51, 238, 38, 27, 0], [2, "166_2", 305, 91, 30, 35, 0], [2, "1150", 71, 19, 48, 85, 0], [2, "1150", 102, 11, 48, 85, 0], [2, "1150", 122, 8, 48, 85, 2], [2, "1152", 248, 76, 38, 26, 0], [2, "208_3", 133, 381, 78, 40, 2], [2, "214_3", 819, 133, 54, 40, 2], [2, "152_3", 804, 110, 76, 40, 2], [2, "208_3", 819, 146, 78, 40, 0], [2, "1152", 303, 575, 38, 26, 0], [2, "205_3", 175, 343, 54, 40, 0], [2, "214_3", 379, 506, 54, 40, 0], [2, "208_3", 375, 488, 78, 40, 1], [2, "213_3", 423, 525, 64, 45, 0], [2, "1302_3", 509, 513, 40, 29, 0], [2, "208_3", 414, 505, 78, 40, 0], [2, "214_3", 468, 562, 54, 40, 0], [2, "206", 463, 537, 66, 40, 0], [2, "208_3", -1, 259, 78, 40, 1], [2, "208_3", 11, 286, 78, 40, 1], [2, "166_2", 464, 530, 30, 35, 0], [2, "165_1", 915, 332, 42, 37, 2], [2, "1147", 207, 453, 50, 42, 0], [2, "1303_2", 866, 192, 34, 20, 0], [2, "1151", 861, 201, 38, 33, 2], [2, "214_3", 706, 563, 54, 40, 2], [2, "1308_2", 393, 634, 22, 37, 2], [2, "1152", 404, 603, 38, 26, 0], [2, "1302_3", 736, 229, 40, 29, 2], [2, "1303_2", 736, 261, 34, 20, 0], [2, "955_4", 483, 59, 20, 18, 0], [2, "1302_3", 188, 161, 40, 29, 2], [2, "1305_2", 223, 177, 20, 14, 0], [2, "1151", 99, 207, 38, 33, 0], [2, "955_4", 95, 275, 20, 18, 0], [2, "955_4", 630, 523, 20, 18, 0], [2, "1303_2", 545, 528, 34, 20, 0], [2, "1152", 90, 135, 38, 26, 0], [2, "208_3", 271, 134, 78, 40, 2], [2, "213_3", 862, 474, 64, 45, 2], [2, "205_3", 885, 405, 54, 40, 2], [2, "206", 863, 461, 66, 40, 2], [2, "208_3", 826, 438, 78, 40, 2], [2, "213_3", 817, 507, 64, 45, 2], [2, "152_3", 813, 485, 76, 40, 2], [2, "216", 797, 542, 46, 46, 2], [2, "214_3", 746, 563, 54, 40, 2], [2, "213_3", 745, 536, 64, 45, 2], [2, "152_3", 692, 539, 76, 40, 2], [2, "1147", 898, 604, 50, 42, 0], [2, "208_3", 761, 515, 78, 40, 3], [2, "207_2", 766, 537, 38, 27, 2], [2, "166_2", 896, 454, 30, 35, 2], [2, "166_2", 810, 514, 30, 35, 2], [2, "208_3", 346, 470, 78, 40, 0], [2, "205_3", -25, 289, 54, 40, 0], [2, "1153", 87, 453, 34, 54, 0], [2, "1150", -9, 435, 48, 85, 0], [2, "1154", 6, 459, 28, 51, 0], [2, "1303_2", 95, 423, 34, 20, 0], [2, "1149", 941, 76, 40, 82, 2], [2, "1152", 879, 108, 38, 26, 0], [2, "214_3", 265, 418, 54, 40, 0], [2, "208_3", 265, 400, 78, 40, 1], [2, "213_3", 292, 442, 64, 45, 0], [2, "208_3", 298, 420, 78, 40, 0], [2, "214_3", 302, 483, 54, 40, 0], [2, "206", 298, 456, 66, 40, 0], [2, "166_2", 301, 449, 30, 35, 0], [2, "208_3", 283, 366, 78, 40, 0], [2, "207_2", 913, 248, 38, 27, 2], [2, "1154", 141, 459, 28, 51, 2], [2, "1154", 167, 449, 28, 51, 0], [2, "1308_2", 833, 592, 22, 37, 0], [2, "1308_2", 931, 474, 22, 37, 0], [2, "1310_2", 920, 478, 18, 29, 0], [2, "1149", 844, 607, 40, 82, 2], [2, "1150", 175, 544, 48, 85, 0], [2, "1308_2", 206, 537, 22, 37, 0], [2, "1150", 134, 549, 48, 85, 2], [2, "1150", 123, 573, 48, 85, 0], [2, "1150", 2, 599, 48, 85, 2], [2, "1147", 253, 526, 50, 42, 0], [2, "1147", 5, 532, 50, 42, 0], [2, "1152", 257, 499, 38, 26, 0], [2, "1147", 840, 67, 50, 42, 0], [2, "1149", 647, 580, 40, 82, 0], [2, "1149", 615, 579, 40, 82, 2], [2, "206", 626, 546, 66, 40, 0], [2, "208_3", 560, 549, 78, 40, 2], [2, "1153", 609, 593, 34, 54, 2], [2, "1154", 488, 597, 28, 51, 0], [2, "1147", 722, 613, 50, 42, 0], [2, "313_2", 775, 376, 70, 44, 0], [2, "313_2", 805, 397, 70, 44, 2], [2, "313_2", 756, 425, 70, 44, 2], [2, "1303_2", 826, 434, 34, 20, 0], [2, "1305_2", 750, 145, 20, 14, 0], [2, "955_4", 817, 405, 20, 18, 0], [2, "955_4", 698, 305, 20, 18, 0], [2, "955_4", 608, 180, 20, 18, 0], [2, "1303_2", 327, 198, 34, 20, 0], [2, "216", 36, 429, 46, 46, 0], [2, "208_3", 84, 404, 78, 40, 2], [2, "208_3", 18, 405, 78, 40, 0], [2, "208_3", -33, 409, 78, 40, 2], [2, "1302_3", 71, 406, 40, 29, 2], [2, "1151", 364, 385, 38, 33, 0], [2, "1305_2", 740, 407, 20, 14, 0], [2, "1305_2", 792, 490, 20, 14, 0], [2, "1303_2", 881, 240, 34, 20, 0], [2, "955_4", 318, 242, 20, 18, 0], [2, "955_4", 145, 349, 20, 18, 0], [2, "955_4", 424, 405, 20, 18, 0], [2, "166_2", 908, 162, 30, 35, 2], [2, "165_1", 128, 400, 42, 37, 2], [2, "1150", -16, 192, 48, 85, 0], [2, "1303_2", 19, 271, 34, 20, 0], [2, "1154", -8, 222, 28, 51, 0], [2, "208_3", 15, 238, 78, 40, 0], [2, "1302_3", 61, 249, 40, 29, 0], [2, "1154", 455, 592, 28, 51, 2], [2, "1154", 776, 59, 28, 51, 2], [2, "1154", 941, 99, 28, 51, 2], [2, "1154", 102, 43, 28, 51, 0], [2, "313_2", 757, 148, 70, 44, 2], [2, "1301_2", 784, 131, 24, 49, 2], [2, "313_2", 550, 89, 70, 44, 2], [2, "1301_2", 577, 72, 24, 49, 0], [2, "313_2", 369, 124, 70, 44, 2], [2, "1301_2", 396, 107, 24, 49, 2], [2, "1303_2", 656, 518, 34, 20, 0], [2, "1302_3", 852, 427, 40, 29, 0], [2, "544_1", 634, 228, 86, 107, 0], [2, "544_1", 550, 228, 86, 107, 2], [2, "178_4", 631, 241, 70, 37, 0], [2, "178_4", 568, 241, 70, 37, 2], [2, "178_4", 631, 278, 70, 37, 1], [2, "178_4", 568, 278, 70, 37, 3], [2, "1303_2", 356, 424, 34, 20, 0], [2, "955_4", 154, 212, 20, 18, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 57, 57, 57, 61, 67, 69, 64, 50, 51, 52, 69, 68, 70, 69, 73, 72, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, -1, -1, -1, -1, 72, 69, 73, 60, 72, 55, 57, 57, 72, 54, 55, 56, 68, 57, 56, 70, 73, 66, 66, 66, 72, 66, 72, 72, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 63, -1, -1, -1, -1, -1, -1, 62, 63, -1, -1, -1, -1, -1, -1, -1, 80, -1, -1, -1, -1, 62, 63, 73, 66, -1, 63, 69, 69, -1, -1, -1, -1, -1, -1, 72, -1, -1, -1, -1, -1, 76, -1, -1, -1, 58, 69, -1, -1, -1, -1, 82, 81, 81, 80, -1, 62, 63, 50, -1, 56, -1, -1, 62, 73, -1, 66, 67, -1, 58, -1, -1, -1, -1, 66, 56, -1, -1, 57, 56, 50, 57, 56, -1, 58, 57, 72, 71, -1, 74, 75, 85, 84, 84, 79, 81, 80, 50, 51, 57, 54, 56, 69, 72, 62, 63, 63, 64, -1, 58, 61, 57, -1, -1, 63, 71, -1, 65, 72, 55, 61, 72, 55, 57, 57, 60, 69, 68, -1, 77, 78, 78, 84, 84, 84, 96, 83, 53, 54, 60, 54, 55, 56, 69, -1, 51, 51, 51, 52, 62, 63, 73, -1, -1, 51, 55, 55, 61, 72, -1, -1, 72, 66, 66, 60, 72, 71, 50, 56, 86, 97, 78, 78, 84, 84, 91, 92, 62, 63, 63, 73, 60, 55, 57, 56, -1, -1, 54, 55, 57, 56, 70, -1, 66, 54, 54, 73, 72, 66, 66, 67, 68, -1, 58, 61, 66, 55, 61, 59, -1, 86, 87, 87, 87, 93, 92, 82, 81, 81, 80, 70, 73, 72, 60, 56, 50, 51, 51, -1, 72, 72, 70, -1, 66, 63, 54, 66, 67, 69, 69, 68, 50, 51, 61, 60, 60, 54, 72, 55, 56, -1, -1, -1, 74, 75, 81, 85, 84, 84, 83, -1, 70, 69, 69, 68, 62, 73, 54, 54, 56, 73, 56, -1, 63, 64, -1, 63, 64, -1, -1, -1, 62, 63, 69, 69, 73, 72, 67, 69, 68, -1, -1, 74, 85, 90, 90, 90, 84, 84, 79, 80, -1, 74, 81, 81, 80, 62, 73, 54, 59, 70, 71, 66, 71, -1, -1, -1, -1, -1, -1, 82, 81, 81, 81, 80, 70, 69, 68, 82, 81, 80, -1, 77, 90, 90, 90, 90, 90, 78, 78, 95, -1, 86, 97, -1, 95, -1, 65, 54, 71, 51, 68, 63, 68, 58, -1, -1, 74, 81, 81, 85, 84, 84, 91, 92, 74, 75, 75, 85, 84, 83, -1, 89, 96, 90, 90, 90, 90, 90, 91, 92, -1, 74, 85, 78, 95, -1, 65, 66, 68, 54, 55, 57, -1, -1, -1, -1, 77, 84, 84, 84, 84, 84, 83, -1, 77, 96, 96, 78, 96, 95, -1, 86, 97, 96, 90, 90, 90, 96, 95, -1, 74, 85, 84, 84, 79, 80, 62, 63, -1, 57, 56, -1, 82, 81, 80, 74, 85, 84, 90, 91, 93, 93, 92, -1, 86, 87, 93, 97, 84, 79, 80, -1, 86, 87, 97, 96, 90, 90, 79, 75, 85, 84, 84, 84, 84, 83, -1, -1, -1, 67, 68, 75, 85, 84, 83, 86, 87, 87, 87, 88, -1, -1, -1, -1, -1, -1, -1, 86, 93, 93, 92, -1, -1, -1, 86, 87, 87, 87, 97, 90, 90, 90, 84, 84, 91, 92, 9, 10, 66, 68, -1, 85, 84, 91, 92, -1, -1, -1, -1, -1, 83, -1, 50, 51, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 74, 75, 85, 90, 90, 90, 91, 93, 92, -1, 12, 13, 73, 72, 71, 87, 87, 88, 50, 51, 52, -1, 89, 72, 72, 72, 72, 54, 54, 55, -1, 50, 51, 52, -1, 13, 14, -1, -1, -1, 77, 78, 90, 90, 91, 87, 92, -1, -1, -1, -1, -1, 70, 71, 68, 54, 66, 57, 61, 54, 55, 51, 86, 72, 72, 72, 72, 72, -1, -1, 50, 61, 54, 55, 56, 16, 17, 82, 81, 80, 86, 87, 87, -1, -1, -1, 58, 57, 72, 54, 56, -1, -1, 68, -1, 66, 67, 64, -1, 51, 72, 72, 66, 67, 63, 73, 72, -1, -1, 50, 51, 61, 66, 67, 68, 74, 75, 85, 84, 79, 76, -1, -1, 50, 51, 57, 61, 57, 56, 54, 59, -1, -1, 67, 65, 72, 72, 72, 72, -1, 72, 72, 63, 64, -1, 65, 72, 72, -1, -1, -1, 66, 63, 64, -1, 77, 78, 78, 78, 78, 79, 81, 80, 53, 72, 54, 72, 72, 72, 54, 74, 75, 75, 71, 65, 66, 67, 63, 63, 69, 69, 73, 67, 64, -1, 62, 73, 72, 72, 66, 66, 66, 55, 55, 68, 94, 93, 97, 90, 78, 78, 78, 95, 62, 63, 73, 72, 72, 69, 68, -1, 84, 84, 72, 83, 63, 64, -1, 74, 79, 85, -1, -1, -1, -1, -1, 70, 69, 73, 72, 72, 68, 68, -1, -1, -1, -1, 86, 93, 93, 93, 91, 92, -1, -1, 70, 69, 68, -1, 86, 97, 96, 72, 71, 92, -1, 64, -1, 89, 90, 90, -1, -1, -1, -1, -1, -1, -1, 70, 69, 72, 72, 72, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 72, 69, 68, -1, -1, -1, -1, 86, 87, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 72, 72, 55, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 72, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, -1, 58, 57, 56, -1, 50, 58, -1, 70, 69, 73, 72, 72, 72, 72, 72, 72, 72, 72, 86, 66, 67, 73, 72, -1, -1, 72, 72, -1, -1, 66, 66, 51, 51, -1, -1, -1, 82, 81, 85, 57, 61, 60, 59, 50, 51, 61, -1, 63, -1, 70, 69, -1, 72, 72, 72, 66, 67, 73, 66, 63, 64, 70, 69, 72, 72, -1, -1, -1, -1, 66, -1, 54, 54, 66, 66, -1, -1, -1, -1, 60, 59, 60, 59, 53, 54, 85, 78, 84, -1, -1, 72, 66, 69, 69, 69, 63, 64, 62, 63, 64, 58, 57, 56, -1, -1, 50, 51, 66, -1, 66, -1, 66, -1, 66, -1, 66, 58, 57, 56, 69, 68, -1, 86, 87, 93, 93, 93, 93, 93, 70, 69, 73, 66, 57, 56, 50, 51, 65, 66, 67, 73, 55, 55, 57, 57, 53, 54, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, 0, 1, 2, 0, 1, 2, 2, -1, 0, 1, 8, 8, 1, 0, 1, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, 3, 4, 5, 3, 4, 5, 5, 0, 3, 4, 5, 8, 4, 3, 7, 8, 7, 8, 6, 7, 7, 8, 7, 8, 6, 3, 4, 0, 1, 2, 0, 1, 0, 1, 2, -1, -1, -1, -1, -1, 6, 7, 8, 6, 7, 8, 8, 3, 6, 7, 8, 6, 7, 6, 7, 16, 17, 17, 10, 9, 10, 11, 9, 10, 11, 6, 7, 3, 4, 5, 3, 4, 3, 4, 5, 0, 1, -1, -1, -1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 8, 7, 17, 11, 9, 10, 9, 9, 12, 13, 12, 13, 14, 12, 13, 14, 9, 10, 6, 7, 8, 1, 2, 0, 1, 7, 8, 2, -1, -1, -1, 3, 4, 0, 1, 2, 5, 3, 0, 1, 2, 13, 14, 14, 12, 13, 9, 10, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 3, 4, 5, 0, 1, 2, 1, 2, 0, 1, 2, 6, 7, 3, 4, 5, 0, 1, 2, 4, 5, 16, 17, 17, 15, 16, 9, 10, 12, 13, 9, 10, 11, 14, 12, 13, 9, 12, 13, 14, 6, 7, 8, 0, 1, 2, 2, 5, 3, 0, 1, 2, 0, 6, 7, 9, 10, 11, 5, 7, 8, 9, 9, 10, 11, 9, 12, 13, 9, 10, 11, 13, 14, 9, 10, 11, 12, 15, 16, 17, 13, 14, 15, 3, 4, 5, 0, 1, 6, 3, 4, 5, 3, 9, 10, 11, 13, 14, 11, 9, 10, 12, 12, 13, 14, 12, 15, 16, 12, 13, 14, 16, 17, 12, 13, 14, 15, 16, 17, 15, 16, 17, 10, 10, 11, 8, 3, 4, 3, 6, 7, 8, 6, 12, 13, 14, 16, 9, 10, 12, 13, 15, 15, 16, 17, 10, 11, 17, 15, 16, 17, 11, 16, 15, 16, 17, 9, 10, 11, 11, 11, 12, 13, 13, 14, 11, 6, 7, 6, 7, 8, 9, 10, 11, 16, 17, 15, 12, 13, 15, 16, 15, 16, 17, 12, 13, 14, 10, 12, 13, 14, 14, 11, 9, 10, 11, 12, 13, 14, 14, 14, 9, 10, 11, 13, 14, 14, 3, 0, 1, 2, 12, 13, 14, 14, 14, 10, 15, 16, 17, 12, 13, 14, 11, 15, 16, 17, 13, 15, 16, 17, 17, 14, 12, 13, 14, 15, 16, 17, 17, 11, 12, 13, 9, 10, 9, 10, 0, 8, 11, 11, 15, 16, 17, 17, 17, 9, 10, 15, 16, 15, 16, 17, 14, 12, 13, 14, 16, 9, 10, 11, 16, 17, 15, 16, 17, 11, 16, 17, 17, 9, 9, 10, 11, 9, 12, 13, 3, 13, 14, 14, 10, 9, 10, 11, 17, 12, 9, 10, 11, 14, 9, 10, 11, 10, 11, 10, 9, 12, 13, 14, 9, 9, 10, 11, 17, 14, 10, 11, 9, 10, 9, 10, 9, 10, 15, 16, 6, 9, 9, 10, 11, 12, 13, 14, 17, 17, 12, 13, 9, 10, 12, 13, 14, 9, 10, 11, 12, 15, 16, 17, 12, 12, 13, 14, 10, 11, 13, 14, 10, 11, 12, 13, 12, 9, 10, 0, 1, 12, 12, 13, 14, 11, 16, 17, 16, 17, 15, 16, 17, 16, 17, 16, 17, 12, 9, 10, 9, 10, 9, 10, 11, 15, 16, 17, 13, 14, 10, 11, 11, 14, 9, 10, 11, 12, 9, 0, 1, 15, 15, 16, 17, 10, 11, 10, 11, 14, 0, 3, 4, 5, 17, 9, 10, 11, 10, 11, 9, 10, 12, 13, 14, 9, 10, 11, 11, 17, 11, 9, 10, 11, 12, 13, 14, 0, 12, 3, 4, 9, 10, 9, 10, 11, 14, 13, 14, 0, 1, 2, 0, 1, 9, 12, 13, 14, 13, 14, 12, 13, 15, 16, 17, 12, 13, 14, 14, 13, 14, 12, 9, 10, 11, 9, 10, 11, 15, 6, 7, 10, 10, 11, 9, 10, 11, 16, 17, 3, 4, 5, 3, 4, 12, 15, 16, 17, 10, 11, 15, 16, 17, 12, 15, 15, 16, 17, 17, 9, 10, 11, 12, 13, 14, 12, 9, 10, 11, 8, 3, 13, 13, 10, 10, 11, 10, 0, 1, 6, 7, 8, 6, 7, 15, 16, 17, 14, 13, 14, 11, 10, 11, 15, 16, 17, 13, 9, 10, 11, 13, 14, 15, 16, 17, 15, 12, 13, 14, 0, 1, 0, 1, 0, 0, 1, 0, 3, 4, 6, 7, 8, 0, 1, 2, 3, 16, 9, 10, 11, 12, 13, 14, 9, 10, 11, 16, 12, 13, 14, 16, 17, 14, 12, 13, 14, 9, 10, 11, 3, 4, 3, 4, 3, 3, 4, 3, 6, 7, 8, 8, 6, 3, 4, 5, 6, 7, 12, 13, 14, 15, 16, 17, 12, 13, 14, 10, 15, 16, 17, 15, 16, 17, 15, 16, 17, 12, 13, 14, 6, 7, 6, 7, 6, 6, 7, 6, 7, 8, 5, 6, 7, 6, 7, 8, 3, 4, 15, 16, 17, 15, 16, 15, 15, 16, 17, 13, 15, 16, 17, 12, 13, 14, 14, 15, 16, 15, 16, 0, 6, 7, 5, 0, 1, 6, 7, 8, 6, 7, 8, 5, 0, 1, 2, 7, 6, 7, 8, 0, 1, 2, 13, 9, 15, 16, 15, 16, 17, 14, 12, 9, 10, 9, 10, 11, 9, 10, 0, 3, 0, 1, 0, 1, 2, 0, 1, 2, 8, 6, 7, 8, 0, 1, 2, 2, 7, 6, 7, 3, 4, 5, 10, 12, 13, 14, 17, 15, 16, 17, 15, 12, 13, 12, 13, 14, 12, 0, 1, 2, 2, 4, 3, 4, 5, 3, 4, 5, 0, 1, 2, 8, 3, 4, 5, 1, 2, 0, 1, 6, 0, 1, 2, 15, 16, 17, 0, 1, 2, 14, 17, 15, 0, 1, 2, 0, 1, 3, 4, 5, 5, 7, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 6, 7, 8, 4, 5, 3, 4, 3, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 7, 8, 8, 1, 6, 7, 8, 0, 1, 2, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 6, 6, 7, 8, 3, 4, 5, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, 6, 7, 8, 4, 0, 1, 2, 3, 4, 5, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 6, 7, 6, 7, 8, 3, 4, 6, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 6, 7]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}