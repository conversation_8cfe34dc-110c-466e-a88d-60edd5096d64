{"mW": 912, "mH": 720, "tW": 24, "tH": 24, "tiles": [["135", 0, 1, 1], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["709", 0, 2, 1], ["709_1", 0, 2, 1], ["302_4", 0, 2, 2], ["1134", 0, 3, 2], ["1134", 2, 3, 2], ["1134", 1, 3, 2], ["1134", 3, 3, 2], ["1139", 0, 1, 1], ["1139", 2, 1, 1]], "layers": [{"type": 3, "obj": [[2, "703_2", 397, 351, 16, 23, 2], [2, "571", 67, 146, 34, 18, 0], [2, "570", 67, 164, 34, 21, 0], [2, "569", 66, 185, 34, 13, 0]]}, {"type": 4, "obj": [[2, "367_1", 368, -39, 14, 42, 0], [2, "367_1", 418, -14, 14, 42, 0], [2, "367_1", 466, 10, 14, 42, 0], [2, "367_1", 514, 34, 14, 42, 0], [2, "367_1", 796, 73, 14, 42, 0], [2, "367_1", 747, 95, 14, 42, 0], [2, "367_1", 849, 100, 14, 42, 0], [2, "367_1", 695, 119, 14, 42, 0], [2, "367_1", 898, 122, 14, 42, 0], [2, "973_2", 42, 178, 42, 70, 2], [2, "973_2", 84, 178, 42, 70, 0], [2, "571", 481, 528, 34, 18, 0], [2, "570", 481, 546, 34, 21, 0], [2, "569", 480, 567, 34, 13, 0], [2, "571", 566, 574, 34, 18, 0], [2, "570", 566, 592, 34, 21, 0], [2, "569", 565, 613, 34, 13, 0]]}, {"type": 3, "obj": [[2, "1303_1", 785, 376, 34, 20, 2], [2, "1303_1", 854, 410, 34, 20, 0], [2, "1303_1", 784, 346, 34, 20, 0], [2, "688_1", 324, 466, 46, 24, 2], [2, "929_1", -18, -21, 60, 31, 2], [2, "1141", 135, -53, 54, 67, 2], [2, "1141", 768, 165, 54, 67, 0], [2, "1141", 768, 141, 54, 67, 0], [2, "1141", 768, 101, 54, 67, 0], [2, "1141", 725, 187, 54, 67, 0], [2, "1141", 725, 163, 54, 67, 0], [2, "1141", 725, 123, 54, 67, 0], [2, "1141", 682, 209, 54, 67, 0], [2, "1141", 682, 185, 54, 67, 0], [2, "1141", 682, 145, 54, 67, 0], [2, "1141", 896, 511, 54, 67, 0], [2, "1141", 896, 487, 54, 67, 0], [2, "1141", 896, 447, 54, 67, 0], [2, "1141", 853, 533, 54, 67, 0], [2, "1141", 853, 509, 54, 67, 0], [2, "1141", 853, 469, 54, 67, 0], [2, "1141", 810, 555, 54, 67, 0], [2, "1141", 810, 531, 54, 67, 0], [2, "1141", 810, 491, 54, 67, 0], [2, "1141", 796, 666, 54, 67, 2], [2, "1141", 839, 687, 54, 67, 2], [2, "1141", 880, 707, 54, 67, 2], [2, "1141", 631, 655, 54, 67, 0], [2, "1141", 587, 677, 54, 67, 0], [2, "1141", 544, 699, 54, 67, 0], [2, "1141", 603, 314, 54, 67, 0], [2, "1141", 603, 290, 54, 67, 0], [2, "1141", 603, 250, 54, 67, 0], [2, "1141", 560, 336, 54, 67, 0], [2, "1141", 560, 312, 54, 67, 0], [2, "1141", 560, 272, 54, 67, 0], [2, "1141", 517, 358, 54, 67, 0], [2, "1141", 517, 334, 54, 67, 0], [2, "1141", 517, 294, 54, 67, 0], [2, "1141", 481, 375, 54, 67, 0], [2, "1141", 481, 351, 54, 67, 0], [2, "1141", 481, 311, 54, 67, 0], [2, "1141", 438, 397, 54, 67, 0], [2, "1141", 438, 373, 54, 67, 0], [2, "1141", 438, 333, 54, 67, 0], [2, "1141", 395, 419, 54, 67, 0], [2, "1141", 395, 395, 54, 67, 0], [2, "1141", 395, 355, 54, 67, 0], [2, "1141", 317, 397, 54, 67, 2], [2, "1141", 361, 419, 54, 67, 2], [2, "1141", 319, 366, 54, 67, 2], [2, "1141", 362, 388, 54, 67, 2], [2, "1141", 319, 332, 54, 67, 2], [2, "1141", 362, 353, 54, 67, 2], [2, "1141", 265, 395, 54, 67, 0], [2, "1141", 265, 371, 54, 67, 0], [2, "1141", 265, 331, 54, 67, 0], [2, "1141", 221, 391, 54, 67, 0], [2, "1141", 177, 413, 54, 67, 0], [2, "1141", 133, 435, 54, 67, 0], [2, "1141", 221, 351, 54, 67, 0], [2, "1141", 177, 373, 54, 67, 0], [2, "1141", 134, 395, 54, 67, 0], [2, "1141", 91, 457, 54, 67, 0], [2, "1141", 47, 479, 54, 67, 0], [2, "1141", 3, 501, 54, 67, 0], [2, "1141", 91, 417, 54, 67, 0], [2, "1141", 47, 439, 54, 67, 0], [2, "1141", 4, 461, 54, 67, 0], [2, "1141", -40, 522, 54, 67, 0], [2, "1141", -39, 482, 54, 67, 0], [2, "929_1", 476, 304, 60, 31, 0], [2, "929_1", 434, 325, 60, 31, 0], [2, "929_1", 392, 346, 60, 31, 0], [2, "929_1", 262, 320, 60, 31, 0], [2, "929_1", 220, 341, 60, 31, 0], [2, "929_1", 178, 362, 60, 31, 0], [2, "929_1", 136, 383, 60, 31, 0], [2, "929_1", 94, 404, 60, 31, 0], [2, "929_1", 52, 425, 60, 31, 0], [2, "929_1", 9, 447, 60, 31, 0], [2, "929_1", -33, 468, 60, 31, 0], [2, "929_1", -75, 489, 60, 31, 0], [2, "929_1", 311, 318, 60, 31, 2], [2, "929_1", 354, 339, 60, 31, 2], [2, "929_1", 300, 313, 60, 31, 2], [2, "929_1", 367, 508, 60, 31, 2], [2, "929_1", 409, 529, 60, 31, 2], [2, "929_1", 452, 550, 60, 31, 2], [2, "929_1", 493, 572, 60, 31, 2], [2, "929_1", 535, 593, 60, 31, 2], [2, "929_1", 578, 614, 60, 31, 2], [2, "929_1", 621, 636, 60, 31, 2], [2, "929_1", 627, 652, 60, 31, 0], [2, "929_1", 585, 673, 60, 31, 0], [2, "929_1", 543, 694, 60, 31, 0], [2, "929_1", 876, 599, 60, 31, 0], [2, "929_1", 834, 620, 60, 31, 0], [2, "929_1", 792, 641, 60, 31, 0], [2, "929_1", 792, 654, 60, 31, 2], [2, "929_1", 834, 675, 60, 31, 2], [2, "929_1", 877, 696, 60, 31, 2], [2, "1141", 525, 477, 54, 67, 2], [2, "1141", 568, 499, 54, 67, 2], [2, "1141", 525, 446, 54, 67, 2], [2, "1141", 569, 468, 54, 67, 2], [2, "1141", 526, 412, 54, 67, 2], [2, "1141", 569, 433, 54, 67, 2], [2, "1141", 611, 520, 54, 67, 2], [2, "1141", 654, 542, 54, 67, 2], [2, "1141", 611, 489, 54, 67, 2], [2, "1141", 655, 511, 54, 67, 2], [2, "1141", 612, 455, 54, 67, 2], [2, "1141", 655, 476, 54, 67, 2], [2, "1141", 775, 572, 54, 67, 0], [2, "1141", 775, 548, 54, 67, 0], [2, "1141", 775, 508, 54, 67, 0], [2, "1141", 698, 564, 54, 67, 2], [2, "1141", 699, 533, 54, 67, 2], [2, "1141", 699, 498, 54, 67, 2], [2, "427_2", 554, 527, 24, 24, 2], [2, "427_2", 531, 516, 24, 24, 2], [2, "427_2", 525, 513, 24, 24, 2], [2, "427_2", 577, 538, 24, 24, 2], [2, "427_2", 601, 550, 24, 24, 2], [2, "427_2", 625, 562, 24, 24, 2], [2, "427_2", 649, 574, 24, 24, 2], [2, "427_2", 673, 586, 24, 24, 2], [2, "929_1", 743, 286, 60, 31, 0], [2, "929_1", 701, 307, 60, 31, 0], [2, "929_1", 659, 328, 60, 31, 0], [2, "929_1", 617, 349, 60, 31, 0], [2, "929_1", 575, 370, 60, 31, 0], [2, "929_1", 533, 391, 60, 31, 0], [2, "929_1", 526, 406, 60, 31, 2], [2, "929_1", 568, 427, 60, 31, 2], [2, "929_1", 611, 448, 60, 31, 2], [2, "929_1", 653, 469, 60, 31, 2], [2, "1138", 168, 338, 46, 60, 2], [2, "1138", 70, 388, 46, 60, 2], [2, "702_1", 106, 405, 18, 25, 2], [2, "702_1", 205, 356, 18, 25, 2], [2, "43_6", 196, 449, 82, 58, 2], [2, "43_6", 154, 470, 82, 58, 2], [2, "43_6", 170, 420, 82, 58, 2], [2, "43_6", 128, 441, 82, 58, 2], [2, "43_6", 145, 391, 82, 58, 2], [2, "43_6", 103, 412, 82, 58, 2], [2, "961_1", 665, 497, 28, 36, 0], [2, "962_1", 662, 515, 18, 19, 0], [2, "541_3", 637, 520, 38, 42, 2], [2, "541_3", 608, 550, 38, 42, 2], [2, "541_3", 579, 581, 38, 42, 2], [2, "961_1", 605, 467, 28, 36, 0], [2, "962_1", 602, 485, 18, 19, 0], [2, "541_3", 577, 490, 38, 42, 2], [2, "541_3", 548, 520, 38, 42, 2], [2, "541_3", 519, 551, 38, 42, 2], [2, "704", 588, 489, 82, 76, 0], [2, "704", 542, 532, 82, 76, 0], [2, "1141", 733, 578, 54, 67, 2], [2, "1141", 734, 547, 54, 67, 2], [2, "1141", 734, 512, 54, 67, 2], [2, "427_2", 696, 597, 24, 24, 2], [2, "427_2", 718, 608, 24, 24, 2], [2, "929_1", 696, 490, 60, 31, 2], [2, "929_1", 733, 508, 60, 31, 2], [2, "929_1", 899, 437, 60, 31, 0], [2, "929_1", 857, 458, 60, 31, 0], [2, "929_1", 815, 479, 60, 31, 0], [2, "929_1", 773, 500, 60, 31, 0], [2, "929_1", 784, 278, 60, 31, 2], [2, "929_1", 826, 299, 60, 31, 2], [2, "929_1", 869, 320, 60, 31, 2], [2, "427_2", 798, 614, 24, 24, 0], [2, "427_2", 777, 625, 24, 24, 0], [2, "427_2", 882, 572, 24, 24, 0], [2, "427_2", 862, 582, 24, 24, 0], [2, "427_2", 839, 593, 24, 24, 0], [2, "427_2", 817, 604, 24, 24, 0], [2, "427_2", 927, 549, 24, 24, 0], [2, "427_2", 905, 560, 24, 24, 0], [2, "427_2", 754, 625, 24, 24, 2], [2, "427_2", 737, 617, 24, 24, 2], [2, "1142", 488, 355, 30, 37, 0], [2, "1142", 422, 387, 30, 37, 0], [2, "1142", 563, 321, 30, 37, 0], [2, "1142", 342, 377, 30, 37, 2], [2, "1142", 260, 378, 30, 37, 0], [2, "1142", 68, 475, 30, 37, 0], [2, "1142", 9, 500, 30, 37, 0], [2, "1141", 177, -33, 54, 67, 2], [2, "1141", 220, -11, 54, 67, 2], [2, "1141", 221, -42, 54, 67, 2], [2, "1141", 263, 11, 54, 67, 2], [2, "1141", 306, 33, 54, 67, 2], [2, "1141", 263, -20, 54, 67, 2], [2, "1141", 307, 2, 54, 67, 2], [2, "1141", 264, -54, 54, 67, 2], [2, "1141", 307, -33, 54, 67, 2], [2, "1141", 349, 54, 54, 67, 2], [2, "1141", 392, 76, 54, 67, 2], [2, "1141", 349, 23, 54, 67, 2], [2, "1141", 393, 45, 54, 67, 2], [2, "1141", 350, -11, 54, 67, 2], [2, "1141", 393, 10, 54, 67, 2], [2, "1141", 436, 98, 54, 67, 2], [2, "1141", 479, 120, 54, 67, 2], [2, "1141", 436, 67, 54, 67, 2], [2, "1141", 480, 89, 54, 67, 2], [2, "1141", 437, 33, 54, 67, 2], [2, "1141", 480, 54, 54, 67, 2], [2, "1141", 522, 142, 54, 67, 2], [2, "1141", 565, 164, 54, 67, 2], [2, "1141", 522, 111, 54, 67, 2], [2, "1141", 566, 133, 54, 67, 2], [2, "1141", 523, 77, 54, 67, 2], [2, "1141", 566, 98, 54, 67, 2], [2, "1141", 608, 187, 54, 67, 2], [2, "1141", 651, 209, 54, 67, 2], [2, "1141", 608, 156, 54, 67, 2], [2, "1141", 652, 178, 54, 67, 2], [2, "1141", 609, 122, 54, 67, 2], [2, "1141", 652, 143, 54, 67, 2], [2, "1141", 800, 178, 54, 67, 2], [2, "1141", 843, 200, 54, 67, 2], [2, "1141", 800, 147, 54, 67, 2], [2, "1141", 844, 169, 54, 67, 2], [2, "1141", 801, 113, 54, 67, 2], [2, "1141", 844, 134, 54, 67, 2], [2, "1141", 886, 221, 54, 67, 2], [2, "1141", 886, 190, 54, 67, 2], [2, "1141", 887, 156, 54, 67, 2], [2, "929_1", 24, -1, 60, 31, 2], [2, "929_1", 66, 20, 60, 31, 2], [2, "929_1", 109, 41, 60, 31, 2], [2, "929_1", 151, 62, 60, 31, 2], [2, "929_1", 193, 83, 60, 31, 2], [2, "929_1", 236, 104, 60, 31, 2], [2, "929_1", 300, 103, 60, 31, 0], [2, "929_1", 278, 113, 60, 31, 0], [2, "929_1", 342, 95, 60, 31, 2], [2, "929_1", 384, 116, 60, 31, 2], [2, "929_1", 427, 137, 60, 31, 2], [2, "929_1", 467, 159, 60, 31, 2], [2, "929_1", 509, 180, 60, 31, 2], [2, "929_1", 552, 201, 60, 31, 2], [2, "929_1", 602, 241, 60, 31, 0], [2, "929_1", 560, 262, 60, 31, 0], [2, "929_1", 518, 283, 60, 31, 0], [2, "929_1", 592, 222, 60, 31, 2], [2, "929_1", 353, -15, 60, 31, 2], [2, "929_1", 395, 6, 60, 31, 2], [2, "929_1", 438, 27, 60, 31, 2], [2, "929_1", 480, 48, 60, 31, 2], [2, "929_1", 522, 69, 60, 31, 2], [2, "929_1", 565, 90, 60, 31, 2], [2, "929_1", 569, 92, 60, 31, 2], [2, "929_1", 611, 113, 60, 31, 2], [2, "929_1", 654, 134, 60, 31, 2], [2, "929_1", 772, 91, 60, 31, 0], [2, "929_1", 730, 112, 60, 31, 0], [2, "929_1", 688, 133, 60, 31, 0], [2, "929_1", 799, 107, 60, 31, 2], [2, "929_1", 841, 128, 60, 31, 2], [2, "929_1", 884, 149, 60, 31, 2], [2, "1138", 524, 370, 46, 60, 0], [2, "702_1", 519, 389, 18, 25, 0], [2, "1138", 752, 484, 46, 60, 0], [2, "702_1", 746, 501, 18, 25, 0], [2, "1138", 533, 34, 46, 60, 0], [2, "702_1", 528, 53, 18, 25, 0], [2, "1138", 424, 97, 46, 60, 0], [2, "702_1", 419, 116, 18, 25, 0], [2, "272_2", 517, 72, 72, 54, 0], [2, "272_2", 500, 80, 72, 54, 0], [2, "269_5", 544, 94, 110, 58, 0], [2, "269_5", 503, 114, 110, 58, 0], [2, "272_2", 594, 116, 72, 54, 0], [2, "272_2", 577, 124, 72, 54, 0], [2, "271_2", 450, 106, 64, 50, 0], [2, "269_5", 464, 134, 110, 58, 0], [2, "271_2", 526, 151, 64, 50, 0], [2, "1138", 639, 87, 46, 60, 0], [2, "702_1", 634, 106, 18, 25, 0], [2, "1138", 532, 148, 46, 60, 0], [2, "702_1", 527, 167, 18, 25, 0], [2, "683_1", 83, 503, 22, 34, 0], [2, "683_1", 90, 507, 22, 34, 0], [2, "683_1", 98, 511, 22, 34, 0], [2, "683_1", 106, 515, 22, 34, 0], [2, "683_1", 114, 519, 22, 34, 0], [2, "683_1", 121, 522, 22, 34, 0], [2, "683_1", 128, 526, 22, 34, 0], [2, "683_1", 136, 530, 22, 34, 0], [2, "683_1", 144, 534, 22, 34, 0], [2, "683_1", 152, 538, 22, 34, 0], [2, "683_1", 160, 542, 22, 34, 0], [2, "683_1", 167, 546, 22, 34, 0], [2, "683_1", 175, 550, 22, 34, 0], [2, "683_1", 183, 554, 22, 34, 0], [2, "683_1", 191, 558, 22, 34, 0], [2, "683_1", 198, 561, 22, 34, 0], [2, "683_1", 205, 565, 22, 34, 0], [2, "683_1", 213, 569, 22, 34, 0], [2, "683_1", 221, 573, 22, 34, 0], [2, "683_1", 229, 577, 22, 34, 0], [2, "688_1", 363, 486, 46, 24, 2], [2, "683_1", 390, 501, 22, 34, 2], [2, "683_1", 381, 506, 22, 34, 2], [2, "683_1", 373, 510, 22, 34, 2], [2, "43_6", 316, 533, 82, 58, 2], [2, "43_6", 269, 557, 82, 58, 2], [2, "683_1", 359, 517, 22, 34, 2], [2, "683_1", 367, 521, 22, 34, 2], [2, "1138", 343, 478, 46, 60, 2], [2, "43_6", 305, 524, 82, 58, 2], [2, "43_6", 263, 545, 82, 58, 2], [2, "683_1", 261, 568, 22, 34, 2], [2, "683_1", 253, 572, 22, 34, 2], [2, "683_1", 244, 577, 22, 34, 2], [2, "683_1", 236, 581, 22, 34, 2], [2, "1138", 245, 528, 46, 60, 2], [2, "702_1", 281, 545, 18, 25, 2], [2, "702_1", 380, 496, 18, 25, 2], [2, "1137", 92, 466, 66, 59, 0], [2, "1142", 624, 292, 30, 37, 0], [2, "1133", 638, 285, 24, 34, 2], [2, "1133", 434, 381, 24, 34, 2], [2, "712_1", 797, 297, 114, 122, 2], [2, "1133", 273, 371, 24, 34, 2], [2, "1133", 22, 492, 24, 34, 2], [2, "84_1", 566, 198, 66, 65, 0], [2, "1142", 828, 166, 30, 37, 2], [2, "1142", 891, 194, 30, 37, 2], [2, "1142", 744, 170, 30, 37, 0], [2, "1142", 642, 175, 30, 37, 2], [2, "1142", 391, 48, 30, 37, 2], [2, "1142", 457, 80, 30, 37, 2], [2, "1142", 318, 14, 30, 37, 2], [2, "14_5", -6, 566, 32, 30, 0], [2, "1304_1", 21, 615, 22, 19, 2], [2, "1303_1", 15, 595, 34, 20, 0], [2, "1302_2", 42, 616, 40, 29, 2], [2, "11_2", 628, 635, 32, 29, 0], [2, "1304_1", 432, 507, 22, 19, 2], [2, "1303_1", 426, 487, 34, 20, 0], [2, "1302_2", 453, 508, 40, 29, 2], [2, "1304_1", 722, 647, 22, 19, 2], [2, "1303_1", 716, 627, 34, 20, 0], [2, "1302_2", 743, 648, 40, 29, 2], [2, "1304_1", 578, 419, 22, 19, 2], [2, "1303_1", 572, 399, 34, 20, 0], [2, "1302_2", 599, 420, 40, 29, 2], [2, "1302_2", 707, 676, 40, 29, 0], [2, "1303_1", 747, 688, 34, 20, 0], [2, "1303_1", 624, 609, 34, 20, 0], [2, "1303_1", 496, 539, 34, 20, 0], [2, "1302_2", 292, 457, 40, 29, 2], [2, "1302_2", 307, 458, 40, 29, 2], [2, "14_5", 58, 526, 32, 30, 0], [2, "14_5", 43, 534, 32, 30, 0], [2, "14_5", 25, 540, 32, 30, 0], [2, "14_5", 39, 521, 32, 30, 0], [2, "572_2", 470, 567, 56, 42, 2], [2, "572_2", 555, 611, 56, 42, 2], [2, "1303_1", 883, 421, 34, 20, 2], [2, "954_3", 772, 359, 24, 25, 0], [2, "954_3", 402, 537, 24, 25, 0], [2, "954_3", 94, 534, 24, 25, 0], [2, "954_3", 603, 633, 24, 25, 0], [2, "954_3", 797, 493, 24, 25, 0], [2, "954_3", 691, 124, 24, 25, 0], [2, "1304_1", 351, 329, 22, 19, 2], [2, "1303_1", 345, 309, 34, 20, 0], [2, "1302_2", 372, 330, 40, 29, 2], [2, "14_5", 412, 335, 32, 30, 0], [2, "954_3", 399, 130, 24, 25, 0], [2, "954_3", 249, 115, 24, 25, 0], [2, "954_3", 585, 258, 24, 25, 0], [2, "954_3", 3, 281, 24, 25, 0], [2, "954_3", 842, 393, 24, 25, 0], [2, "1303_1", 785, 294, 34, 20, 2], [2, "427_2", 501, 424, 24, 24, 0], [2, "427_2", 492, 429, 24, 24, 0], [2, "427_2", 469, 440, 24, 24, 0], [2, "427_2", 447, 451, 24, 24, 0], [2, "427_2", 428, 460, 24, 24, 0], [2, "427_2", 407, 471, 24, 24, 0], [2, "427_2", 315, 437, 24, 24, 2], [2, "427_2", 339, 449, 24, 24, 2], [2, "427_2", 362, 460, 24, 24, 2], [2, "427_2", 384, 471, 24, 24, 2], [2, "427_2", 293, 438, 24, 24, 0], [2, "688_1", 246, 427, 46, 24, 2], [2, "688_1", 284, 446, 46, 24, 2], [2, "954_3", 274, 444, 24, 25, 0], [2, "427_2", 779, 217, 24, 24, 0], [2, "427_2", 760, 226, 24, 24, 0], [2, "427_2", 737, 238, 24, 24, 0], [2, "427_2", 717, 248, 24, 24, 0], [2, "427_2", 694, 259, 24, 24, 0], [2, "427_2", 802, 218, 24, 24, 2], [2, "427_2", 826, 230, 24, 24, 2], [2, "427_2", 849, 241, 24, 24, 2], [2, "427_2", 871, 252, 24, 24, 2], [2, "427_2", 890, 261, 24, 24, 2], [2, "427_2", 907, 269, 24, 24, 2], [2, "427_2", 144, -7, 24, 24, 2], [2, "427_2", 168, 5, 24, 24, 2], [2, "427_2", 191, 16, 24, 24, 2], [2, "427_2", 213, 27, 24, 24, 2], [2, "427_2", 232, 36, 24, 24, 2], [2, "427_2", 249, 44, 24, 24, 2], [2, "427_2", 273, 56, 24, 24, 2], [2, "427_2", 292, 65, 24, 24, 2], [2, "427_2", 309, 73, 24, 24, 2], [2, "84_1", 326, 78, 66, 65, 0], [2, "427_2", 672, 260, 24, 24, 2], [2, "427_2", 659, 254, 24, 24, 2], [2, "427_2", 663, 698, 24, 24, 0], [2, "427_2", 644, 707, 24, 24, 0], [2, "1303_1", 107, 554, 34, 20, 0], [2, "1303_1", 267, 603, 34, 20, 0], [2, "954_3", 600, 385, 24, 25, 0], [2, "954_3", 250, 603, 24, 25, 0], [2, "954_3", 524, 197, 24, 25, 0], [2, "1142", 812, 541, 30, 37, 0], [2, "1142", 878, 509, 30, 37, 0], [2, "1142", 715, 540, 30, 37, 2], [2, "1142", 550, 462, 30, 37, 2]]}, {"type": 2, "data": [-1, -1, 11, 12, 13, 8, 7, 6, 6, -1, -1, -1, 12, 13, -1, -1, -1, -1, -1, -1, 33, 34, 8, 7, 2, 3, -1, -1, -1, -1, -1, 61, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 13, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 59, -1, 53, 54, 64, 63, 58, 55, -1, -1, -1, 17, 12, 13, 13, -1, -1, 11, 12, 13, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, 12, 12, 13, -1, -1, -1, -1, 65, 71, -1, 65, 66, 76, 75, 75, 74, -1, -1, -1, 1, 2, 3, -1, -1, -1, 8, 7, 11, 12, 13, 7, 6, 6, -1, 25, 1, 2, 3, 2, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 72, 72, 71, -1, -1, -1, 3, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 12, 13, 7, 6, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, 28, 12, 8, 7, 2, 21, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 40, 39, -1, -1, 33, 34, 44, 43, 43, 42, 16, -1, -1, -1, -1, -1, -1, -1, 25, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 11, 12, 13, -1, -1, -1, -1, -1, -1, 3, -1, -1, 41, 40, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 25, 8, 25, 22, 11, 12, 13, -1, -1, -1, -1, 53, 54, 55, -1, -1, -1, -1, 36, 37, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, 32, 31, 21, 25, 25, 25, 28, 11, 12, 13, -1, -1, 56, 63, 58, 54, 60, 59, -1, 33, 34, 31, 31, 31, 31, -1, -1, -1, 31, 31, 31, 31, 0, 1, 43, 3, -1, -1, 25, 31, 31, 31, 43, 37, 1, 1, 2, 3, 53, 54, 64, 63, 63, 75, 63, 62, -1, -1, 8, 7, 6, 31, 31, 31, -1, 31, 31, 31, 1, 1, 1, 2, 7, 6, 0, -1, 33, 6, 25, 25, 1, 1, 2, 3, -1, -1, 68, 69, 63, 63, 70, 72, 66, 67, -1, -1, -1, -1, 8, 7, 6, 31, 31, 6, 1, 1, 1, 2, 3, 17, 13, 8, 7, 6, 8, 8, 7, 6, 2, 3, -1, -1, -1, -1, 65, 66, 72, 66, 67, -1, -1, -1, 8, -1, -1, -1, -1, -1, 8, 7, 6, 8, 7, 2, 3, 17, 16, 17, 11, 12, 13, 8, -1, -1, -1, -1, 53, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, 29, 29, 28, 27, 18, 17, 6, -1, -1, -1, 44, 12, 13, 18, 17, 16, 17, 25, 25, 25, -1, 11, 12, -1, -1, -1, -1, 65, 76, 58, 59, -1, -1, -1, -1, -1, -1, 21, 32, 31, 31, 26, 16, -1, -1, -1, -1, -1, -1, -1, 2, 3, 17, 37, 38, 44, 25, 25, 25, 27, -1, 13, -1, -1, 53, 54, 64, 63, 62, 21, 22, -1, -1, -1, -1, -1, 18, 17, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, 18, 17, 16, 34, 34, 44, 37, 37, 38, 34, 44, 11, 12, 13, 56, 57, 66, 63, 27, -1, -1, -1, -1, -1, -1, 0, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, -1, 8, 7, 6, 6, 28, 27, 33, 34, 34, 35, -1, 32, 44, 1, 35, -1, -1, -1, 31, 31, -1, -1, -1, -1, -1, -1, -1, 0, 0, -1, -1, -1, -1, 31, 31, 2, 3, 53, 54, 59, 8, 7, 6, 6, 22, 27, -1, 36, 1, 1, 44, 3, -1, -1, -1, -1, -1, -1, 0, 0, 0, 8, 7, 6, 25, -1, -1, -1, 31, 31, 31, 0, 0, 2, -1, 65, 72, 71, -1, -1, 8, 7, 6, 26, 22, 32, 2, 3, -1, -1, -1, -1, -1, -1, -1, 8, 0, 1, 2, 3, 1, 2, 7, 6, 31, 31, 31, 1, 2, 3, 3, 16, -1, 18, 18, 13, 60, 59, -1, -1, 36, 43, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 61, 60, 59, -1, -1, 8, 7, 1, 2, 3, -1, 18, 17, 16, -1, -1, -1, -1, -1, 62, 22, 23, 32, 38, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 3, -1, 68, 69, 58, 59, -1, -1, -1, -1, -1, 18, 17, 16, 16, 0, 0, -1, -1, -1, -1, -1, 17, 12, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 25, 2, 3, -1, -1, -1, 65, 66, 72, 71, -1, -1, 1, 2, 3, -1, 8, 7, 6, 6, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 1, 2, 7, 6, 25, 25, 6, -1, -1, -1, -1, -1, -1, 1, 43, 38, -1, -1, -1, -1, -1, 8, 7, 6, 6, 0, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 1, 34, 3, -1, -1, 8, 7, 6, 25, 6, 25, 25, 25, 1, 2, 38, 40, 39, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 6, 0, -1, -1, -1, 0, 0, 0, 1, 25, 1, -1, -1, -1, -1, 53, 54, 55, 8, 7, 25, 1, 1, 2, 40, 39, -1, -1, 53, 54, 55, 33, 34, 39, -1, -1, -1, 8, 7, 6, 6, -1, 0, 0, 1, 25, 25, 2, 3, 28, 27, -1, -1, 56, 57, 58, 55, -1, -1, 3, -1, -1, -1, -1, 3, -1, 56, 57, 58, 60, 55, 21, 22, 37, -1, -1, -1, 8, 7, 6, 1, 25, 1, 2, 3, 3, 37, 31, 30, 61, 60, 64, 75, 75, 58, 54, 55, -1, -1, 8, 3, -1, 53, 54, 64, 75, 75, 75, 74, 33, 34, 34, -1, -1, 13, -1, -1, 8, 1, 2, 3, 3, -1, 33, 34, 40, 39, 73, 72, 76, 75, 69, 70, 72, 71, -1, -1, -1, -1, -1, 65, 66, 76, 75, 70, 72, 71, -1, -1, -1, -1, -1, 11, 12, 13, -1, -1, -1, -1, -1, 28, 27, -1, -1, -1, -1, -1, 73, 72, 66, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 66, 67, -1, -1, -1, -1, -1, -1, -1, 1, 2, 3, -1, -1, 18, 17, 16, -1, -1, 28]}, {"type": 2, "data": [52, 49, 48, 47, 48, 47, 48, 47, 48, -1, -1, -1, -1, -1, -1, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 49, 51, 49, 50, 47, 48, 47, 48, 47, 48, 47, 48, -1, -1, -1, 51, 52, 51, 49, 50, 49, 50, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 51, 52, 51, 52, 49, 49, 48, 47, 48, 47, 48, 47, 48, -1, -1, -1, -1, -1, 51, 52, 51, 52, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 50, 51, 51, 51, 52, 49, 47, 48, 47, 48, 47, 48, 47, 48, 48, 48, -1, -1, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 52, 51, 52, 51, 51, 51, 52, 49, 48, 47, 48, 47, 48, 49, 50, -1, 48, -1, -1, 49, 51, 52, 49, 49, 50, 49, 50, 49, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 51, 52, 49, 50, 49, 51, 51, 52, 49, 51, 49, 50, 50, 52, 49, 50, 49, 50, 51, 52, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 52, 51, 52, 51, 51, 52, 51, 52, 51, 52, 50, 51, 51, 52, 51, 52, 52, 49, 51, 52, 51, 52, 50, -1, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 49, 51, 51, 52, 49, 49, 51, 52, 50, 51, 52, 50, 49, 51, 52, 50, 51, 51, 52, 49, 50, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 49, 50, 51, 51, 51, 52, 51, 49, 49, 50, 49, 50, 50, 51, 49, 50, 49, 50, 49, 50, 50, 49, 50, 52, 47, -1, -1, -1, -1, -1, -1, 47, -1, 47, 48, -1, -1, -1, 51, 49, 50, 49, 49, 51, 52, 51, 51, 52, 51, 52, 52, 52, 51, 52, 51, 52, 51, 52, 50, 51, 52, 50, 49, 50, -1, -1, 47, 48, 47, 48, 47, 48, 47, 48, -1, -1, 49, 51, 52, 51, 51, 52, 51, 49, 50, 51, 49, 50, 52, 49, 50, 49, 50, 49, 51, 51, 52, 49, 50, 52, 51, 52, 50, 47, 48, 47, 48, 47, 48, 47, 48, 47, 48, 47, 51, 52, 50, 50, 49, 50, 49, 51, 52, 50, 51, 52, 49, 51, 52, 51, 52, 51, 49, 50, 49, 51, 52, 50, 51, 52, -1, 48, 47, 48, 47, 48, 47, 48, 47, 48, 47, 48, 49, 50, 52, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 49, 50, 49, 51, 52, 51, 52, 51, 52, 49, 50, -1, 47, 48, 47, 48, 47, 50, 52, 48, 47, 48, 47, 51, 52, 49, 50, 49, 50, 49, 50, 49, 50, 49, 50, 49, 49, 50, 49, 50, 49, 51, 52, 49, 50, 51, 52, 51, 52, 47, 48, 47, 48, 50, 49, 50, 49, 50, 49, 47, 48, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 52, 51, 51, 52, 49, 50, 51, 51, 52, 51, 49, 49, 50, 50, 52, 47, 48, 49, 50, 49, 51, 49, 50, 50, 49, 50, 49, 49, 50, 51, 52, 51, 52, 50, 49, 49, 50, 51, 52, 49, 50, 49, 50, 52, 49, 50, 49, 50, 51, 51, 52, 52, 51, 52, 50, 51, 52, 49, 50, 51, 52, 52, 51, 52, 50, 49, 50, 49, 50, 50, 51, 49, 50, 51, 52, 49, 50, 51, 52, 49, 50, 49, 51, 49, 49, 49, 50, -1, 49, 49, 50, 49, 50, 51, 51, 51, 52, 49, 50, 49, 50, 51, 52, 49, 50, 51, 52, 49, 50, 51, 52, 49, 50, 51, 52, 52, 50, 51, 49, 51, 49, 50, 51, 51, 47, 48, 51, 51, 52, 51, 52, 49, 50, 49, 50, 51, 52, 51, 52, 49, 49, 50, 51, 49, 50, 51, 52, 51, 52, 51, 77, 51, 77, 48, 47, 48, 51, 49, 51, 52, 47, 47, 48, 48, -1, 49, 50, 49, 50, 51, 52, 51, 52, 52, 50, 51, 52, 51, 51, 52, 50, 51, 52, 51, 52, 78, -1, 49, 50, 78, 77, 78, 47, 48, 47, 51, 52, 48, 47, 48, 47, 48, -1, 51, 52, 51, 52, 51, 49, 50, 49, 50, 52, 51, 52, 49, 50, 51, 52, 51, 52, 52, 50, 78, 77, 78, 77, 78, 77, 78, 77, 78, 48, 47, 48, 47, 48, 47, 48, 48, -1, -1, -1, -1, 49, 50, 51, 52, 51, 52, 51, 49, 50, 51, 52, 49, 50, 51, 52, 51, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 48, 47, 48, 47, 48, 48, 47, 48, -1, -1, -1, 51, 52, 51, 52, 51, 49, 50, 51, 52, -1, -1, 51, 52, 49, 50, 52, 49, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 49, 50, 47, 48, 47, 48, 48, 47, 47, 48, -1, -1, -1, -1, -1, -1, 51, 52, -1, -1, 47, 48, 49, 49, 51, 52, 50, 51, 49, 77, 78, 77, 78, 77, 78, 77, 78, 51, 51, 52, 52, 52, 48, 47, 48, 47, 48, 47, 47, 48, -1, -1, -1, -1, -1, -1, -1, 47, 48, 47, 51, 51, 52, 49, 50, 49, 51, 52, 49, 77, 78, 77, 49, 51, 52, 50, 51, 52, 52, 49, 49, 50, 48, 48, 47, 47, 47, 47, 47, -1, -1, -1, 47, 48, 47, 48, 47, 48, 49, 50, 50, 51, 52, 51, 51, 52, 51, 49, 50, 52, 51, 52, 51, 52, 51, 49, 50, 51, 51, 52, 52, 49, 48, 47, 48, 47, 48, 47, 48, 47, 48, 47, 48, 47, 47, 48, 51, 52, 52, 51, 49, 50, 50, 49, 50, 49, 50, 49, 50, 50, 49, 50, 50, 49, 50, 49, 50, 49, 50, 49, 49, 50, 48, 48, 47, 48, 47, 48, 47, 48, 47, 48, 51, 52, 49, 50, 49, 50, 51, 52, 50, 51, 52, 51, 52, 51, 52, 49, 50, 52, 52, 51, 52, 51, 52, 51, 52, 49, 51, 52, 50, 47, 48, 47, 48, 47, 48, 47, 50, 49, 50, 49, 49, 50, 51, 52, 49, 51, 52, 50, 49, 49, 50, 51, 52, 49, 50, 50, 49, 50, 49, 50, 52, 49, 50, 51, 52, 51, 52, -1, 47, 48, 47, 48, 47, 48, 51, 51, 52, 51, 51, 52, 49, 50, 51, 52, 51, 52, 51, 51, 52, 51, 52, 51, 52, 50, 51, 52, 51, 52, 50, 50, 52, 52, 51, 52, -1, 47, 48, 47, 48, 47, 48, 47, 48, 51, 52, 49]}], "blocks": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}