{"mW": 984, "mH": 648, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["3117", 0, 3, 3], ["1233", 0, 3, 2], ["315", 0, 3, 3], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["3984", 0, 1, 1]], "layers": [{"type": 3, "obj": [[2, "3988", 636, 365, 148, 179, 0], [2, "3988", 199, 149, 148, 179, 0], [2, "3988", 501, 285, 148, 179, 0], [2, "3036", 774, 475, 70, 54, 2], [2, "3036", 802, 445, 70, 54, 2], [2, "3036", 833, 413, 70, 54, 2], [2, "1501", 936, 448, 50, 26, 0], [2, "1501", 911, 461, 50, 26, 0], [2, "1501", 783, 526, 50, 26, 0], [2, "1501", 809, 513, 50, 26, 0], [2, "1501", 758, 539, 50, 26, 0], [2, "1501", 783, 505, 50, 26, 0], [2, "1501", 809, 492, 50, 26, 0], [2, "1501", 758, 518, 50, 26, 0], [2, "1501", 815, 533, 50, 26, 0], [2, "1501", 841, 520, 50, 26, 0], [2, "1501", 790, 546, 50, 26, 0], [2, "1501", 829, 543, 50, 26, 0], [2, "1501", 855, 530, 50, 26, 0], [2, "1501", 804, 556, 50, 26, 0], [2, "1501", 709, 543, 50, 26, 0], [2, "1501", 735, 530, 50, 26, 0], [2, "1501", 684, 556, 50, 26, 0], [2, "1501", 632, 582, 50, 26, 0], [2, "1501", 658, 569, 50, 26, 0], [2, "1501", 607, 595, 50, 26, 0], [2, "1501", 555, 621, 50, 26, 0], [2, "1501", 581, 608, 50, 26, 0], [2, "1501", 530, 634, 50, 26, 0], [2, "1501", 651, 598, 50, 26, 0], [2, "1501", 677, 585, 50, 26, 0], [2, "1501", 626, 611, 50, 26, 0], [2, "1501", 785, 555, 50, 26, 0], [2, "1501", 811, 542, 50, 26, 0], [2, "1501", 760, 568, 50, 26, 0], [2, "1501", 708, 594, 50, 26, 0], [2, "1501", 734, 581, 50, 26, 0], [2, "1501", 683, 607, 50, 26, 0], [2, "1501", 728, 559, 50, 26, 0], [2, "1501", 754, 546, 50, 26, 0], [2, "1501", 703, 572, 50, 26, 0], [2, "1501", 860, 487, 50, 26, 0], [2, "1501", 886, 474, 50, 26, 0], [2, "1501", 835, 500, 50, 26, 0], [2, "1501", 895, 494, 50, 26, 0], [2, "1501", 921, 481, 50, 26, 0], [2, "1501", 870, 507, 50, 26, 0], [2, "1501", 906, 509, 50, 26, 0], [2, "1501", 932, 496, 50, 26, 0], [2, "1501", 881, 522, 50, 26, 0], [2, "1501", 931, 439, 50, 26, 0], [2, "1501", 957, 452, 50, 26, 0], [2, "1501", 983, 465, 50, 26, 0], [2, "1501", 1009, 478, 50, 26, 0], [2, "1501", 860, 466, 50, 26, 0], [2, "1501", 886, 453, 50, 26, 0], [2, "1501", 835, 479, 50, 26, 0], [2, "1501", 896, 448, 50, 26, 0], [2, "1501", 922, 435, 50, 26, 0], [2, "1501", 871, 461, 50, 26, 0], [2, "1501", 898, 462, 50, 26, 0], [2, "1501", 924, 449, 50, 26, 0], [2, "1501", 873, 475, 50, 26, 0], [2, "1501", 395, 195, 50, 26, 0], [2, "1501", 369, 208, 50, 26, 0], [2, "1501", 344, 221, 50, 26, 0], [2, "1501", 908, 479, 50, 26, 0], [2, "1501", 934, 466, 50, 26, 0], [2, "1501", 883, 492, 50, 26, 0], [2, "1501", 293, 122, 50, 26, 0], [2, "1501", 318, 134, 50, 26, 0], [2, "1501", 344, 147, 50, 26, 0], [2, "1501", 370, 160, 50, 26, 0], [2, "1501", 908, 501, 50, 26, 0], [2, "1501", 934, 488, 50, 26, 0], [2, "1501", 960, 475, 50, 26, 0], [2, "1501", 986, 488, 50, 26, 0], [2, "1501", 960, 501, 50, 26, 0], [2, "1501", 934, 514, 50, 26, 0], [2, "1501", 960, 527, 50, 26, 0], [2, "1501", 986, 514, 50, 26, 0], [2, "1501", 1012, 501, 50, 26, 0], [2, "1501", 874, 499, 50, 26, 0], [2, "1501", 900, 486, 50, 26, 0], [2, "1501", 926, 473, 50, 26, 0], [2, "1501", 952, 486, 50, 26, 0], [2, "1501", 926, 499, 50, 26, 0], [2, "1501", 900, 512, 50, 26, 0], [2, "1501", 926, 525, 50, 26, 0], [2, "1501", 952, 512, 50, 26, 0], [2, "1501", 978, 499, 50, 26, 0], [2, "1501", 800, 535, 50, 26, 0], [2, "1501", 826, 522, 50, 26, 0], [2, "1501", 852, 509, 50, 26, 0], [2, "1501", 878, 522, 50, 26, 0], [2, "1501", 852, 535, 50, 26, 0], [2, "1501", 826, 548, 50, 26, 0], [2, "1501", 852, 561, 50, 26, 0], [2, "1501", 878, 548, 50, 26, 0], [2, "1501", 904, 535, 50, 26, 0], [2, "1501", 693, 617, 50, 26, 0], [2, "1501", 719, 604, 50, 26, 0], [2, "1501", 745, 591, 50, 26, 0], [2, "1501", 771, 604, 50, 26, 0], [2, "1501", 745, 617, 50, 26, 0], [2, "1501", 719, 630, 50, 26, 0], [2, "1501", 745, 643, 50, 26, 0], [2, "1501", 771, 630, 50, 26, 0], [2, "1501", 797, 617, 50, 26, 0], [2, "1501", 767, 581, 50, 26, 0], [2, "1501", 793, 568, 50, 26, 0], [2, "1501", 819, 555, 50, 26, 0], [2, "1501", 845, 568, 50, 26, 0], [2, "1501", 819, 581, 50, 26, 0], [2, "1501", 793, 594, 50, 26, 0], [2, "1501", 819, 607, 50, 26, 0], [2, "1501", 845, 594, 50, 26, 0], [2, "1501", 871, 581, 50, 26, 0], [2, "1501", 838, 540, 50, 26, 0], [2, "1501", 864, 527, 50, 26, 0], [2, "1501", 890, 514, 50, 26, 0], [2, "1501", 916, 527, 50, 26, 0], [2, "1501", 890, 540, 50, 26, 0], [2, "1501", 864, 553, 50, 26, 0], [2, "1501", 890, 566, 50, 26, 0], [2, "1501", 916, 553, 50, 26, 0], [2, "1501", 942, 540, 50, 26, 0], [2, "1501", 825, 597, 50, 26, 0], [2, "1501", 851, 584, 50, 26, 0], [2, "1501", 877, 571, 50, 26, 0], [2, "1501", 903, 584, 50, 26, 0], [2, "1501", 877, 597, 50, 26, 0], [2, "1501", 851, 610, 50, 26, 0], [2, "1501", 877, 623, 50, 26, 0], [2, "1501", 903, 610, 50, 26, 0], [2, "1501", 929, 597, 50, 26, 0], [2, "1501", 899, 561, 50, 26, 0], [2, "1501", 925, 548, 50, 26, 0], [2, "1501", 951, 535, 50, 26, 0], [2, "1501", 977, 548, 50, 26, 0], [2, "1501", 951, 561, 50, 26, 0], [2, "1501", 925, 574, 50, 26, 0], [2, "1501", 951, 587, 50, 26, 0], [2, "1501", 977, 574, 50, 26, 0], [2, "1501", 1003, 561, 50, 26, 0], [2, "1501", 970, 520, 50, 26, 0], [2, "1501", 996, 507, 50, 26, 0], [2, "1501", 1022, 494, 50, 26, 0], [2, "1501", 1048, 507, 50, 26, 0], [2, "1501", 1022, 520, 50, 26, 0], [2, "1501", 996, 533, 50, 26, 0], [2, "1501", 1022, 546, 50, 26, 0], [2, "1501", 1048, 533, 50, 26, 0], [2, "1501", 1074, 520, 50, 26, 0], [2, "1501", 825, 597, 50, 26, 0], [2, "1501", 851, 584, 50, 26, 0], [2, "1501", 877, 571, 50, 26, 0], [2, "1501", 903, 584, 50, 26, 0], [2, "1501", 877, 597, 50, 26, 0], [2, "1501", 851, 610, 50, 26, 0], [2, "1501", 877, 623, 50, 26, 0], [2, "1501", 903, 610, 50, 26, 0], [2, "1501", 929, 597, 50, 26, 0], [2, "1501", 899, 561, 50, 26, 0], [2, "1501", 925, 548, 50, 26, 0], [2, "1501", 951, 535, 50, 26, 0], [2, "1501", 977, 548, 50, 26, 0], [2, "1501", 951, 561, 50, 26, 0], [2, "1501", 925, 574, 50, 26, 0], [2, "1501", 951, 587, 50, 26, 0], [2, "1501", 977, 574, 50, 26, 0], [2, "1501", 1003, 561, 50, 26, 0], [2, "1501", 932, 588, 50, 26, 0], [2, "1501", 958, 575, 50, 26, 0], [2, "1501", 984, 562, 50, 26, 0], [2, "1501", 1010, 575, 50, 26, 0], [2, "1501", 984, 588, 50, 26, 0], [2, "1501", 958, 601, 50, 26, 0], [2, "1501", 984, 614, 50, 26, 0], [2, "1501", 1010, 601, 50, 26, 0], [2, "1501", 1036, 588, 50, 26, 0], [2, "1501", 191, 71, 50, 26, 0], [2, "1501", 216, 83, 50, 26, 0], [2, "1501", 242, 96, 50, 26, 0], [2, "1501", 268, 109, 50, 26, 0], [2, "1501", 548, 646, 50, 26, 0], [2, "1501", 574, 633, 50, 26, 0], [2, "1501", 600, 620, 50, 26, 0], [2, "1501", 626, 633, 50, 26, 0], [2, "1501", 600, 646, 50, 26, 0], [2, "1501", 574, 659, 50, 26, 0], [2, "1501", 600, 672, 50, 26, 0], [2, "1501", 626, 659, 50, 26, 0], [2, "1501", 652, 646, 50, 26, 0], [2, "1501", 619, 635, 50, 26, 0], [2, "1501", 645, 622, 50, 26, 0], [2, "1501", 671, 609, 50, 26, 0], [2, "1501", 697, 622, 50, 26, 0], [2, "1501", 671, 635, 50, 26, 0], [2, "1501", 645, 648, 50, 26, 0], [2, "1501", 671, 661, 50, 26, 0], [2, "1501", 697, 648, 50, 26, 0], [2, "1501", 723, 635, 50, 26, 0], [2, "1501", 771, 632, 50, 26, 0], [2, "1501", 797, 619, 50, 26, 0], [2, "1501", 823, 606, 50, 26, 0], [2, "1501", 849, 619, 50, 26, 0], [2, "1501", 823, 632, 50, 26, 0], [2, "1501", 797, 645, 50, 26, 0], [2, "1501", 823, 658, 50, 26, 0], [2, "1501", 849, 645, 50, 26, 0], [2, "1501", 875, 632, 50, 26, 0], [2, "1501", 89, 88, 50, 26, 0], [2, "1501", 115, 75, 50, 26, 0], [2, "1501", 141, 62, 50, 26, 0], [2, "1501", 167, 75, 50, 26, 0], [2, "1501", 141, 88, 50, 26, 0], [2, "1501", 115, 101, 50, 26, 0], [2, "1501", 141, 114, 50, 26, 0], [2, "1501", 167, 101, 50, 26, 0], [2, "1501", 193, 88, 50, 26, 0], [2, "894", 59, 391, 24, 20, 2], [2, "894", 77, 382, 24, 20, 2], [2, "894", 128, 357, 24, 20, 2], [2, "894", 106, 368, 24, 20, 2], [2, "894", 99, 372, 24, 20, 2], [2, "1501", 163, 52, 50, 26, 0], [2, "1501", 189, 39, 50, 26, 0], [2, "1501", 215, 26, 50, 26, 0], [2, "1501", 241, 39, 50, 26, 0], [2, "1501", 215, 52, 50, 26, 0], [2, "1501", 189, 65, 50, 26, 0], [2, "1501", 215, 78, 50, 26, 0], [2, "1501", 241, 65, 50, 26, 0], [2, "1501", 267, 52, 50, 26, 0], [2, "1501", 241, 13, 50, 26, 0], [2, "1501", 267, 0, 50, 26, 0], [2, "1501", 293, -13, 50, 26, 0], [2, "1501", 319, 0, 50, 26, 0], [2, "1501", 293, 13, 50, 26, 0], [2, "1501", 267, 26, 50, 26, 0], [2, "1501", 293, 39, 50, 26, 0], [2, "1501", 319, 26, 50, 26, 0], [2, "1501", 345, 13, 50, 26, 0], [2, "1501", 241, 13, 50, 26, 0], [2, "1501", 267, 0, 50, 26, 0], [2, "1501", 293, -13, 50, 26, 0], [2, "1501", 319, 0, 50, 26, 0], [2, "1501", 293, 13, 50, 26, 0], [2, "1501", 267, 26, 50, 26, 0], [2, "1501", 293, 39, 50, 26, 0], [2, "1501", 319, 26, 50, 26, 0], [2, "1501", 345, 13, 50, 26, 0], [2, "1501", -57, 162, 50, 26, 0], [2, "1501", -31, 149, 50, 26, 0], [2, "1501", -5, 136, 50, 26, 0], [2, "1501", 21, 149, 50, 26, 0], [2, "1501", -5, 162, 50, 26, 0], [2, "1501", -31, 175, 50, 26, 0], [2, "1501", -5, 188, 50, 26, 0], [2, "1501", 21, 175, 50, 26, 0], [2, "1501", 47, 162, 50, 26, 0], [2, "3978", 183, 93, 50, 26, 0], [2, "3978", 157, 106, 50, 26, 0], [2, "3978", 106, 132, 50, 26, 0], [2, "3978", 132, 119, 50, 26, 0], [2, "3978", 56, 157, 50, 26, 0], [2, "3978", 82, 144, 50, 26, 0], [2, "3978", 13, 179, 50, 26, 0], [2, "3978", 39, 166, 50, 26, 0], [2, "3978", -35, 204, 50, 26, 0], [2, "3978", -9, 191, 50, 26, 0], [2, "3978", 392, 196, 50, 26, 0], [2, "3978", 366, 209, 50, 26, 0], [2, "3978", 341, 222, 50, 26, 0], [2, "3978", 316, 235, 50, 26, 0], [2, "3978", 290, 248, 50, 26, 0], [2, "3978", 265, 261, 50, 26, 0], [2, "3978", 244, 272, 50, 26, 0], [2, "3978", 218, 285, 50, 26, 0], [2, "3978", 193, 298, 50, 26, 0], [2, "3978", 169, 310, 50, 26, 0], [2, "3978", 148, 321, 50, 26, 0], [2, "3978", 122, 334, 50, 26, 0], [2, "3978", 97, 347, 50, 26, 0], [2, "3978", 86, 352, 50, 26, 0], [2, "3978", 65, 363, 50, 26, 0], [2, "3978", 39, 376, 50, 26, 0], [2, "3978", 14, 389, 50, 26, 0], [2, "3978", 12, 391, 50, 26, 0], [2, "3978", -9, 402, 50, 26, 0], [2, "3978", -35, 415, 50, 26, 0], [2, "3978", 897, 446, 50, 26, 0], [2, "3978", 871, 459, 50, 26, 0], [2, "3978", 846, 472, 50, 26, 0], [2, "3978", 821, 485, 50, 26, 0], [2, "3978", 795, 498, 50, 26, 0], [2, "3978", 770, 511, 50, 26, 0], [2, "3978", 749, 521, 50, 26, 0], [2, "3978", 723, 534, 50, 26, 0], [2, "3978", 698, 547, 50, 26, 0], [2, "3978", 673, 560, 50, 26, 0], [2, "3978", 647, 573, 50, 26, 0], [2, "3978", 622, 586, 50, 26, 0], [2, "3978", 614, 590, 50, 26, 0], [2, "3978", 588, 603, 50, 26, 0], [2, "3978", 563, 616, 50, 26, 0], [2, "3978", 538, 629, 50, 26, 0], [2, "3978", 512, 642, 50, 26, 0], [2, "3978", 209, 80, 50, 26, 2], [2, "3978", 234, 92, 50, 26, 2], [2, "3978", 259, 105, 50, 26, 2], [2, "3978", 284, 117, 50, 26, 2], [2, "3978", 309, 130, 50, 26, 2], [2, "3978", 334, 142, 50, 26, 2], [2, "3978", 360, 155, 50, 26, 2], [2, "3978", 385, 167, 50, 26, 2], [2, "3978", 560, 254, 50, 26, 2], [2, "3978", 535, 242, 50, 26, 2], [2, "3978", 509, 229, 50, 26, 2], [2, "3978", 484, 217, 50, 26, 2], [2, "3978", 459, 204, 50, 26, 2], [2, "3978", 434, 192, 50, 26, 2], [2, "3978", 409, 179, 50, 26, 2], [2, "3978", 733, 341, 50, 26, 2], [2, "3978", 708, 329, 50, 26, 2], [2, "3978", 682, 316, 50, 26, 2], [2, "3978", 657, 304, 50, 26, 2], [2, "3978", 632, 291, 50, 26, 2], [2, "3978", 607, 279, 50, 26, 2], [2, "3978", 582, 266, 50, 26, 2], [2, "3978", 907, 427, 50, 26, 2], [2, "3978", 882, 415, 50, 26, 2], [2, "3978", 856, 402, 50, 26, 2], [2, "3978", 831, 390, 50, 26, 2], [2, "3978", 806, 377, 50, 26, 2], [2, "3978", 781, 365, 50, 26, 2], [2, "3978", 756, 352, 50, 26, 2], [2, "3978", 923, 434, 50, 26, 2], [2, "3984", -24, 624, 24, 24, 0], [2, "3984", -48, 624, 24, 24, 0], [2, "3984", -48, 600, 24, 24, 0], [2, "3984", -24, 600, 24, 24, 0], [2, "3984", -48, 552, 24, 24, 0], [2, "3984", -24, 552, 24, 24, 0], [2, "3984", -24, 576, 24, 24, 0], [2, "3984", -48, 576, 24, 24, 0], [2, "3984", -48, 456, 24, 24, 0], [2, "3984", -24, 456, 24, 24, 0], [2, "3984", -24, 480, 24, 24, 0], [2, "3984", -48, 480, 24, 24, 0], [2, "3984", -48, 504, 24, 24, 0], [2, "3984", -24, 504, 24, 24, 0], [2, "3984", -24, 528, 24, 24, 0], [2, "3984", -48, 528, 24, 24, 0], [2, "3984", -48, 264, 24, 24, 0], [2, "3984", -24, 264, 24, 24, 0], [2, "3984", -24, 288, 24, 24, 0], [2, "3984", -48, 288, 24, 24, 0], [2, "3984", -48, 312, 24, 24, 0], [2, "3984", -24, 312, 24, 24, 0], [2, "3984", -24, 336, 24, 24, 0], [2, "3984", -48, 336, 24, 24, 0], [2, "3984", -48, 360, 24, 24, 0], [2, "3984", -24, 360, 24, 24, 0], [2, "3984", -24, 384, 24, 24, 0], [2, "3984", -48, 384, 24, 24, 0], [2, "3984", -48, 408, 24, 24, 0], [2, "3984", -24, 408, 24, 24, 0], [2, "3984", -24, 432, 24, 24, 0], [2, "3984", -48, 432, 24, 24, 0], [2, "3984", -48, 72, 24, 24, 0], [2, "3984", -24, 72, 24, 24, 0], [2, "3984", -24, 96, 24, 24, 0], [2, "3984", -48, 96, 24, 24, 0], [2, "3984", -48, 120, 24, 24, 0], [2, "3984", -24, 120, 24, 24, 0], [2, "3984", -24, 144, 24, 24, 0], [2, "3984", -48, 144, 24, 24, 0], [2, "3984", -48, 168, 24, 24, 0], [2, "3984", -24, 168, 24, 24, 0], [2, "3984", -24, 192, 24, 24, 0], [2, "3984", -48, 192, 24, 24, 0], [2, "3984", -48, 216, 24, 24, 0], [2, "3984", -24, 216, 24, 24, 0], [2, "3984", -24, 240, 24, 24, 0], [2, "3984", -48, 240, 24, 24, 0], [2, "3984", -96, 408, 24, 24, 0], [2, "3984", -72, 408, 24, 24, 0], [2, "3984", -72, 432, 24, 24, 0], [2, "3984", -96, 432, 24, 24, 0], [2, "3984", -96, 360, 24, 24, 0], [2, "3984", -72, 360, 24, 24, 0], [2, "3984", -72, 384, 24, 24, 0], [2, "3984", -96, 384, 24, 24, 0], [2, "3984", -48, 648, 24, 24, 0], [2, "3984", -24, 648, 24, 24, 0], [2, "3984", -24, 672, 24, 24, 0], [2, "3984", -48, 672, 24, 24, 0], [2, "3984", 0, 648, 24, 24, 0], [2, "3984", 24, 648, 24, 24, 0], [2, "3984", 24, 672, 24, 24, 0], [2, "3984", 0, 672, 24, 24, 0], [2, "3984", 96, 648, 24, 24, 0], [2, "3984", 120, 648, 24, 24, 0], [2, "3984", 120, 672, 24, 24, 0], [2, "3984", 96, 672, 24, 24, 0], [2, "3984", 48, 648, 24, 24, 0], [2, "3984", 72, 648, 24, 24, 0], [2, "3984", 72, 672, 24, 24, 0], [2, "3984", 48, 672, 24, 24, 0], [2, "3984", 288, 648, 24, 24, 0], [2, "3984", 312, 648, 24, 24, 0], [2, "3984", 312, 672, 24, 24, 0], [2, "3984", 288, 672, 24, 24, 0], [2, "3984", 240, 648, 24, 24, 0], [2, "3984", 264, 648, 24, 24, 0], [2, "3984", 264, 672, 24, 24, 0], [2, "3984", 240, 672, 24, 24, 0], [2, "3984", 192, 648, 24, 24, 0], [2, "3984", 216, 648, 24, 24, 0], [2, "3984", 216, 672, 24, 24, 0], [2, "3984", 192, 672, 24, 24, 0], [2, "3984", 144, 648, 24, 24, 0], [2, "3984", 168, 648, 24, 24, 0], [2, "3984", 168, 672, 24, 24, 0], [2, "3984", 144, 672, 24, 24, 0], [2, "3984", 480, 648, 24, 24, 0], [2, "3984", 504, 648, 24, 24, 0], [2, "3984", 504, 672, 24, 24, 0], [2, "3984", 480, 672, 24, 24, 0], [2, "3984", 432, 648, 24, 24, 0], [2, "3984", 456, 648, 24, 24, 0], [2, "3984", 456, 672, 24, 24, 0], [2, "3984", 432, 672, 24, 24, 0], [2, "3984", 384, 648, 24, 24, 0], [2, "3984", 408, 648, 24, 24, 0], [2, "3984", 408, 672, 24, 24, 0], [2, "3984", 384, 672, 24, 24, 0], [2, "3984", 336, 648, 24, 24, 0], [2, "3984", 360, 648, 24, 24, 0], [2, "3984", 360, 672, 24, 24, 0], [2, "3984", 336, 672, 24, 24, 0], [2, "3984", 672, 648, 24, 24, 0], [2, "3984", 696, 648, 24, 24, 0], [2, "3984", 696, 672, 24, 24, 0], [2, "3984", 672, 672, 24, 24, 0], [2, "3984", 624, 648, 24, 24, 0], [2, "3984", 648, 648, 24, 24, 0], [2, "3984", 648, 672, 24, 24, 0], [2, "3984", 624, 672, 24, 24, 0], [2, "3984", 576, 648, 24, 24, 0], [2, "3984", 600, 648, 24, 24, 0], [2, "3984", 600, 672, 24, 24, 0], [2, "3984", 576, 672, 24, 24, 0], [2, "3984", 528, 648, 24, 24, 0], [2, "3984", 552, 648, 24, 24, 0], [2, "3984", 552, 672, 24, 24, 0], [2, "3984", 528, 672, 24, 24, 0], [2, "894", 580, 278, 24, 20, 0], [2, "894", 556, 266, 24, 20, 0], [2, "894", 597, 287, 24, 20, 0], [2, "894", 621, 299, 24, 20, 0]]}, {"type": 4, "obj": [[2, "1434", 491, 22, 34, 43, 0], [2, "1434", 434, 47, 34, 43, 0], [2, "3390", 479, 79, 44, 46, 0], [2, "1434", 549, 82, 34, 43, 0], [4, 2, 873, 146, 0, 4022], [4, 1, 641, 151, 0, 4018], [2, "1434", 438, 115, 34, 43, 0], [4, 3, 913, 179, 0, 4022], [2, "1232", 712, 51, 100, 158, 0], [2, "3987", 161, 175, 42, 42, 0], [2, "3987", 81, 214, 42, 42, 0], [2, "3571", 249, 222, 34, 34, 0], [2, "3385", 783, 197, 64, 86, 0], [2, "3050", 904, 248, 62, 59, 0], [2, "3050", 846, 277, 62, 59, 0], [2, "879", 572, 290, 26, 56, 2], [2, "3050", 787, 311, 62, 59, 0], [2, "3050", 773, 319, 62, 59, 0], [2, "3571", 250, 396, 34, 34, 0], [4, 4, 492, 444, 0, 4019], [2, "72", 338, 405, 42, 44, 0], [2, "3544", 153, 400, 46, 59, 0], [2, "72", 302, 419, 42, 44, 0], [2, "3544", 37, 459, 46, 59, 0], [2, "3571", 605, 489, 34, 34, 0], [2, "3571", 516, 490, 34, 34, 0], [2, "3571", 604, 540, 34, 34, 0], [2, "3571", 516, 541, 34, 34, 0], [2, "3544", 381, 530, 46, 59, 0], [2, "673", 411, 562, 80, 63, 0], [2, "3544", 475, 580, 46, 59, 0]]}, {"type": 3, "obj": [[2, "3606", 13, 300, 64, 35, 2], [2, "884", -4, 275, 24, 25, 2], [2, "884", -24, 285, 24, 25, 2], [2, "884", 306, 335, 24, 25, 2], [2, "884", 286, 345, 24, 25, 2], [2, "884", 267, 355, 24, 25, 2], [2, "884", 247, 365, 24, 25, 2], [2, "884", 231, 373, 24, 25, 2], [2, "884", 211, 383, 24, 25, 2], [2, "884", 192, 393, 24, 25, 2], [2, "884", 172, 403, 24, 25, 2], [2, "884", 16, 480, 24, 25, 2], [2, "884", -4, 490, 24, 25, 2], [2, "894", 190, 192, 24, 20, 2], [2, "894", 119, 228, 24, 20, 2], [2, "894", 96, 240, 24, 20, 2], [2, "894", 71, 252, 24, 20, 2], [2, "884", 206, 177, 24, 25, 2], [2, "884", 186, 187, 24, 25, 2], [2, "884", 168, 195, 24, 25, 2], [2, "884", 148, 205, 24, 25, 2], [2, "884", 128, 215, 24, 25, 2], [2, "884", 108, 225, 24, 25, 2], [2, "884", 90, 233, 24, 25, 2], [2, "884", 70, 243, 24, 25, 2], [2, "884", 52, 252, 24, 25, 2], [2, "884", 32, 262, 24, 25, 2], [2, "884", 14, 270, 24, 25, 2], [2, "884", -6, 280, 24, 25, 2], [2, "3744", 140, 248, 96, 49, 2], [2, "895", 525, 301, 8, 31, 0], [2, "895", 525, 274, 8, 31, 0], [2, "895", 525, 247, 8, 31, 0], [2, "895", 569, 321, 8, 31, 0], [2, "895", 569, 294, 8, 31, 0], [2, "895", 569, 268, 8, 31, 0], [2, "894", 525, 247, 24, 20, 0], [2, "894", 548, 259, 24, 20, 0], [2, "894", 523, 323, 24, 20, 0], [2, "894", 546, 334, 24, 20, 0], [2, "894", 477, 224, 24, 20, 0], [2, "894", 500, 236, 24, 20, 0], [2, "894", 476, 300, 24, 20, 0], [2, "894", 498, 311, 24, 20, 0], [2, "895", 433, 211, 8, 31, 2], [2, "895", 433, 238, 8, 31, 2], [2, "895", 433, 265, 8, 31, 2], [2, "895", 417, 219, 8, 31, 2], [2, "895", 417, 246, 8, 31, 2], [2, "895", 417, 273, 8, 31, 2], [2, "894", 416, 211, 24, 20, 2], [2, "894", 393, 223, 24, 20, 2], [2, "894", 417, 285, 24, 20, 2], [2, "894", 394, 297, 24, 20, 2], [2, "894", 368, 236, 24, 20, 2], [2, "895", 289, 285, 8, 31, 2], [2, "895", 289, 312, 8, 31, 2], [2, "895", 289, 323, 8, 31, 2], [2, "894", 272, 285, 24, 20, 2], [2, "894", 249, 297, 24, 20, 2], [2, "894", 273, 359, 24, 20, 2], [2, "895", 181, 130, 8, 31, 2], [2, "895", 181, 157, 8, 31, 2], [2, "895", 181, 184, 8, 31, 2], [2, "894", 164, 130, 24, 20, 2], [2, "894", 141, 142, 24, 20, 2], [2, "895", 437, 258, 8, 31, 0], [2, "895", 437, 231, 8, 31, 0], [2, "895", 437, 204, 8, 31, 0], [2, "894", 429, 200, 24, 20, 0], [2, "894", 452, 212, 24, 20, 0], [2, "894", 442, 284, 24, 20, 0], [2, "894", 453, 289, 24, 20, 0], [2, "895", 341, 210, 8, 31, 0], [2, "895", 341, 183, 8, 31, 0], [2, "895", 341, 156, 8, 31, 0], [2, "894", 333, 152, 24, 20, 0], [2, "894", 356, 164, 24, 20, 0], [2, "894", 332, 227, 24, 20, 0], [2, "895", 345, 256, 8, 31, 2], [2, "895", 345, 283, 8, 31, 2], [2, "895", 345, 310, 8, 31, 2], [2, "894", 345, 248, 24, 20, 2], [2, "894", 346, 322, 24, 20, 2], [2, "895", 337, 260, 8, 31, 2], [2, "895", 337, 287, 8, 31, 2], [2, "895", 337, 314, 8, 31, 2], [2, "895", 329, 264, 8, 31, 2], [2, "895", 329, 291, 8, 31, 2], [2, "895", 329, 318, 8, 31, 2], [2, "895", 321, 268, 8, 31, 2], [2, "895", 321, 295, 8, 31, 2], [2, "895", 321, 322, 8, 31, 2], [2, "894", 320, 260, 24, 20, 2], [2, "894", 297, 272, 24, 20, 2], [2, "894", 321, 334, 24, 20, 2], [2, "894", 298, 346, 24, 20, 2], [2, "895", 209, 326, 8, 31, 2], [2, "895", 209, 353, 8, 31, 2], [2, "895", 209, 363, 8, 31, 2], [2, "894", 224, 310, 24, 20, 2], [2, "894", 201, 322, 24, 20, 2], [2, "895", 169, 347, 8, 31, 2], [2, "895", 169, 374, 8, 31, 2], [2, "895", 169, 401, 8, 31, 2], [2, "895", 161, 351, 8, 31, 2], [2, "895", 161, 378, 8, 31, 2], [2, "895", 161, 405, 8, 31, 2], [2, "895", 153, 355, 8, 31, 2], [2, "895", 153, 382, 8, 31, 2], [2, "895", 153, 409, 8, 31, 2], [2, "894", 176, 335, 24, 20, 2], [2, "894", 153, 347, 24, 20, 2], [2, "894", 154, 421, 24, 20, 2], [2, "895", 45, 407, 8, 31, 2], [2, "895", 45, 434, 8, 31, 2], [2, "895", 45, 461, 8, 31, 2], [2, "895", 37, 411, 8, 31, 2], [2, "895", 37, 438, 8, 31, 2], [2, "895", 37, 465, 8, 31, 2], [2, "895", 29, 415, 8, 31, 2], [2, "895", 29, 442, 8, 31, 2], [2, "895", 29, 469, 8, 31, 2], [2, "894", 28, 407, 24, 20, 2], [2, "894", 5, 419, 24, 20, 2], [2, "894", 28, 444, 24, 20, 2], [2, "895", -11, 436, 8, 31, 2], [2, "895", -11, 463, 8, 31, 2], [2, "895", -11, 490, 8, 31, 2], [2, "895", -19, 440, 8, 31, 2], [2, "895", -19, 467, 8, 31, 2], [2, "895", -19, 494, 8, 31, 2], [2, "894", -20, 432, 24, 20, 2], [2, "895", 237, 159, 8, 31, 0], [2, "895", 237, 132, 8, 31, 0], [2, "895", 237, 105, 8, 31, 0], [2, "895", 245, 163, 8, 31, 0], [2, "895", 245, 136, 8, 31, 0], [2, "895", 245, 109, 8, 31, 0], [2, "894", 237, 105, 24, 20, 0], [2, "894", 260, 117, 24, 20, 0], [2, "894", 236, 179, 24, 20, 0], [2, "894", 259, 190, 24, 20, 0], [2, "894", 644, 310, 24, 20, 0], [2, "894", 643, 385, 24, 20, 0], [2, "895", 645, 369, 8, 31, 0], [2, "895", 645, 342, 8, 31, 0], [2, "895", 645, 315, 8, 31, 0], [2, "895", 709, 396, 8, 31, 0], [2, "895", 670, 357, 8, 31, 0], [2, "895", 670, 330, 8, 31, 0], [2, "894", 669, 322, 24, 20, 0], [2, "894", 692, 334, 24, 20, 0], [2, "894", 668, 397, 24, 20, 0], [2, "894", 690, 407, 24, 20, 0], [2, "894", 717, 345, 24, 20, 0], [2, "894", 740, 357, 24, 20, 0], [2, "894", 716, 420, 24, 20, 0], [2, "894", 738, 431, 24, 20, 0], [2, "895", 805, 443, 8, 31, 0], [2, "895", 805, 416, 8, 31, 0], [2, "895", 805, 389, 8, 31, 0], [2, "894", 765, 369, 24, 20, 0], [2, "894", 788, 381, 24, 20, 0], [2, "894", 786, 456, 24, 20, 0], [2, "895", 829, 455, 8, 31, 0], [2, "895", 837, 459, 8, 31, 0], [2, "895", 845, 463, 8, 31, 0], [2, "895", 845, 436, 8, 31, 0], [2, "895", 845, 409, 8, 31, 0], [2, "895", 853, 467, 8, 31, 0], [2, "895", 853, 440, 8, 31, 0], [2, "895", 853, 413, 8, 31, 0], [2, "894", 813, 393, 24, 20, 0], [2, "894", 835, 444, 24, 20, 0], [2, "894", 812, 468, 24, 20, 0], [2, "894", 834, 479, 24, 20, 0], [2, "895", 861, 471, 8, 31, 0], [2, "895", 861, 444, 8, 31, 0], [2, "895", 861, 417, 8, 31, 0], [2, "895", 869, 475, 8, 31, 0], [2, "895", 869, 448, 8, 31, 0], [2, "895", 869, 421, 8, 31, 0], [2, "895", 877, 479, 8, 31, 0], [2, "895", 877, 452, 8, 31, 0], [2, "895", 877, 425, 8, 31, 0], [2, "895", 885, 483, 8, 31, 0], [2, "895", 893, 487, 8, 31, 0], [2, "895", 893, 460, 8, 31, 0], [2, "895", 901, 491, 8, 31, 0], [2, "895", 901, 464, 8, 31, 0], [2, "894", 861, 417, 24, 20, 0], [2, "894", 884, 429, 24, 20, 0], [2, "894", 860, 456, 24, 20, 0], [2, "894", 883, 468, 24, 20, 0], [2, "894", 860, 492, 24, 20, 0], [2, "894", 882, 503, 24, 20, 0], [2, "895", 229, 106, 8, 31, 2], [2, "895", 229, 133, 8, 31, 2], [2, "895", 229, 160, 8, 31, 2], [2, "894", 212, 106, 24, 20, 2], [2, "894", 189, 118, 24, 20, 2], [2, "894", 118, 154, 24, 20, 2], [2, "894", 95, 166, 24, 20, 2], [2, "895", 63, 190, 8, 31, 2], [2, "895", 63, 217, 8, 31, 2], [2, "895", 63, 244, 8, 31, 2], [2, "895", 55, 194, 8, 31, 2], [2, "895", 55, 221, 8, 31, 2], [2, "895", 55, 248, 8, 31, 2], [2, "894", 70, 178, 24, 20, 2], [2, "894", 47, 190, 24, 20, 2], [2, "894", 48, 264, 24, 20, 2], [2, "895", 16, 213, 8, 31, 2], [2, "895", 16, 240, 8, 31, 2], [2, "895", 16, 267, 8, 31, 2], [2, "894", 23, 201, 24, 20, 2], [2, "894", 0, 213, 24, 20, 2], [2, "894", 24, 275, 24, 20, 2], [2, "894", 1, 287, 24, 20, 2], [2, "894", 454, 216, 24, 20, 0], [2, "894", 503, 240, 24, 20, 0], [2, "894", 527, 252, 24, 20, 0], [2, "894", 536, 257, 24, 20, 0], [2, "894", 244, 111, 24, 20, 0], [2, "894", 268, 123, 24, 20, 0], [2, "894", 293, 135, 24, 20, 0], [2, "894", 318, 148, 24, 20, 0], [2, "894", 343, 160, 24, 20, 0], [2, "894", 389, 183, 24, 20, 0], [2, "894", 646, 311, 24, 20, 0], [2, "894", 670, 323, 24, 20, 0], [2, "894", 744, 360, 24, 20, 0], [2, "894", 768, 372, 24, 20, 0], [2, "894", 793, 385, 24, 20, 0], [2, "894", 817, 397, 24, 20, 0], [2, "894", 842, 409, 24, 20, 0], [2, "894", 866, 421, 24, 20, 0], [2, "894", 916, 446, 24, 20, 0], [2, "894", 203, 109, 24, 20, 2], [2, "894", 157, 134, 24, 20, 2], [2, "894", 133, 146, 24, 20, 2], [2, "894", 109, 158, 24, 20, 2], [2, "894", 85, 170, 24, 20, 2], [2, "894", 63, 181, 24, 20, 2], [2, "894", 39, 193, 24, 20, 2], [2, "894", 16, 205, 24, 20, 2], [2, "894", -6, 216, 24, 20, 2], [2, "894", 391, 223, 24, 20, 2], [2, "894", 366, 236, 24, 20, 2], [2, "894", 345, 246, 24, 20, 2], [2, "894", 321, 259, 24, 20, 2], [2, "894", 297, 271, 24, 20, 2], [2, "894", 275, 282, 24, 20, 2], [2, "894", 273, 284, 24, 20, 2], [2, "894", 232, 304, 24, 20, 2], [2, "894", 208, 316, 24, 20, 2], [2, "894", 186, 327, 24, 20, 2], [2, "894", 193, 324, 24, 20, 2], [2, "894", 169, 336, 24, 20, 2], [2, "894", 146, 347, 24, 20, 2], [2, "894", 20, 412, 24, 20, 2], [2, "894", -4, 424, 24, 20, 2], [2, "894", -26, 435, 24, 20, 2], [2, "895", 181, 158, 8, 31, 2], [2, "895", 181, 132, 8, 31, 2], [2, "894", 440, 209, 24, 20, 0], [2, "894", 236, 107, 24, 20, 0], [2, "894", 252, 294, 24, 20, 2], [2, "895", 909, 494, 8, 31, 0], [2, "895", 909, 467, 8, 31, 0], [2, "895", 909, 440, 8, 31, 0], [2, "895", 917, 498, 8, 31, 0], [2, "895", 917, 471, 8, 31, 0], [2, "895", 917, 444, 8, 31, 0], [2, "895", 925, 502, 8, 31, 0], [2, "895", 925, 475, 8, 31, 0], [2, "895", 925, 448, 8, 31, 0], [2, "895", 933, 506, 8, 31, 0], [2, "895", 933, 479, 8, 31, 0], [2, "895", 933, 452, 8, 31, 0], [2, "895", 941, 510, 8, 31, 0], [2, "895", 941, 483, 8, 31, 0], [2, "895", 941, 456, 8, 31, 0], [2, "895", 949, 514, 8, 31, 0], [2, "895", 949, 487, 8, 31, 0], [2, "895", 949, 460, 8, 31, 0], [2, "894", 909, 440, 24, 20, 0], [2, "894", 932, 452, 24, 20, 0], [2, "894", 908, 479, 24, 20, 0], [2, "894", 931, 491, 24, 20, 0], [2, "894", 908, 515, 24, 20, 0], [2, "894", 930, 526, 24, 20, 0], [2, "895", 957, 518, 8, 31, 0], [2, "895", 957, 491, 8, 31, 0], [2, "895", 957, 464, 8, 31, 0], [2, "895", 965, 522, 8, 31, 0], [2, "895", 965, 495, 8, 31, 0], [2, "895", 965, 468, 8, 31, 0], [2, "895", 973, 526, 8, 31, 0], [2, "895", 973, 499, 8, 31, 0], [2, "895", 973, 472, 8, 31, 0], [2, "895", 981, 530, 8, 31, 0], [2, "895", 981, 503, 8, 31, 0], [2, "895", 981, 476, 8, 31, 0], [2, "895", 989, 534, 8, 31, 0], [2, "895", 989, 507, 8, 31, 0], [2, "895", 989, 480, 8, 31, 0], [2, "895", 997, 538, 8, 31, 0], [2, "895", 997, 511, 8, 31, 0], [2, "895", 997, 484, 8, 31, 0], [2, "894", 957, 464, 24, 20, 0], [2, "894", 980, 476, 24, 20, 0], [2, "894", 956, 503, 24, 20, 0], [2, "894", 979, 515, 24, 20, 0], [2, "894", 956, 539, 24, 20, 0], [2, "894", 978, 550, 24, 20, 0], [2, "897", 227, 141, 22, 62, 0], [2, "897", 229, 92, 22, 62, 0], [2, "897", 51, 227, 22, 62, 2], [2, "897", 52, 178, 22, 62, 2], [2, "897", 331, 294, 22, 62, 2], [2, "897", 330, 244, 22, 62, 2], [2, "897", 38, 443, 22, 62, 2], [2, "897", 38, 394, 22, 62, 2], [2, "897", 149, 389, 22, 62, 2], [2, "897", 149, 340, 22, 62, 2], [2, "895", 285, 183, 8, 31, 0], [2, "895", 285, 156, 8, 31, 0], [2, "895", 285, 129, 8, 31, 0], [2, "894", 285, 129, 24, 20, 0], [2, "894", 308, 141, 24, 20, 0], [2, "894", 365, 171, 24, 20, 0], [2, "894", 8, 455, 24, 20, 2], [2, "894", -14, 467, 24, 20, 2], [2, "894", 478, 228, 24, 20, 0], [2, "3036", 744, 507, 70, 54, 2], [2, "3036", 100, 427, 70, 54, 0], [2, "3036", 59, 448, 70, 54, 0], [2, "894", 762, 444, 24, 20, 0], [2, "894", 417, 210, 24, 20, 2], [2, "897", 429, 247, 22, 62, 2], [2, "897", 430, 198, 22, 62, 2], [2, "897", 882, 444, 22, 62, 2], [2, "884", 447, 277, 24, 25, 0], [2, "884", 467, 287, 24, 25, 0], [2, "884", 486, 295, 24, 25, 0], [2, "884", 506, 305, 24, 25, 0], [2, "884", 447, 277, 24, 25, 0], [2, "884", 467, 287, 24, 25, 0], [2, "884", 529, 317, 24, 25, 0], [2, "884", 549, 327, 24, 25, 0], [2, "897", 549, 299, 22, 62, 0], [2, "897", 551, 250, 22, 62, 0], [2, "884", 652, 378, 24, 25, 0], [2, "884", 672, 388, 24, 25, 0], [2, "884", 732, 418, 24, 25, 0], [2, "884", 752, 428, 24, 25, 0], [2, "884", 692, 398, 24, 25, 0], [2, "884", 712, 408, 24, 25, 0], [2, "884", 808, 454, 24, 25, 0], [2, "884", 828, 464, 24, 25, 0], [2, "884", 772, 439, 24, 25, 0], [2, "884", 792, 449, 24, 25, 0], [2, "897", 801, 424, 22, 62, 0], [2, "897", 803, 375, 22, 62, 0], [2, "647", -12, 445, 34, 39, 2], [2, "884", 247, 175, 24, 25, 0], [2, "884", 267, 185, 24, 25, 0], [2, "884", 327, 214, 24, 25, 0], [2, "884", 347, 224, 24, 25, 0], [2, "119", 313, 329, 18, 31, 0], [2, "673", 270, 357, 80, 63, 0], [2, "894", 179, 122, 24, 20, 2], [2, "894", 717, 346, 24, 20, 0], [2, "894", 421, 199, 24, 20, 0], [2, "894", 410, 194, 24, 20, 0], [2, "22", 608, 184, 62, 38, 0], [2, "21", 582, 167, 28, 24, 0], [2, "21", 669, 213, 28, 24, 0], [2, "116", 689, 133, 46, 39, 0], [2, "116", 689, 133, 46, 39, 0], [2, "116", 715, 150, 46, 39, 0], [2, "113", 686, 123, 26, 33, 2], [2, "903", 736, 375, 30, 40, 2], [2, "903", 484, 253, 30, 40, 2], [2, "903", 302, 163, 30, 40, 2], [2, "884", 286, 194, 24, 25, 0], [2, "884", 306, 204, 24, 25, 0], [2, "3036", 543, 341, 70, 54, 2], [2, "730", 420, 289, 36, 25, 0], [2, "730", 400, 299, 36, 25, 0], [2, "730", 379, 309, 36, 25, 0], [2, "730", 359, 319, 36, 25, 0], [2, "730", 438, 297, 36, 25, 0], [2, "730", 418, 308, 36, 25, 0], [2, "730", 397, 318, 36, 25, 0], [2, "730", 377, 327, 36, 25, 0], [2, "1366", 399, 294, 52, 39, 0], [2, "730", 420, 282, 36, 25, 0], [2, "730", 438, 291, 36, 25, 0], [2, "730", 421, 275, 36, 25, 0], [2, "730", 439, 284, 36, 25, 0], [2, "730", 421, 269, 36, 25, 0], [2, "730", 439, 278, 36, 25, 0], [2, "730", 421, 263, 36, 25, 0], [2, "730", 439, 272, 36, 25, 0], [2, "730", 421, 257, 36, 25, 0], [2, "730", 439, 266, 36, 25, 0], [2, "730", 400, 268, 36, 25, 0], [2, "730", 418, 277, 36, 25, 0], [2, "730", 379, 277, 36, 25, 0], [2, "730", 397, 286, 36, 25, 0], [2, "730", 360, 311, 36, 25, 0], [2, "730", 378, 320, 36, 25, 0], [2, "730", 360, 305, 36, 25, 0], [2, "730", 378, 314, 36, 25, 0], [2, "730", 360, 299, 36, 25, 0], [2, "730", 378, 308, 36, 25, 0], [2, "730", 360, 293, 36, 25, 0], [2, "730", 378, 302, 36, 25, 0], [2, "730", 360, 287, 36, 25, 0], [2, "730", 378, 296, 36, 25, 0], [2, "682", 363, 319, 34, 35, 0], [2, "682", 363, 301, 34, 35, 0], [2, "3474", 160, 197, 44, 47, 2], [2, "3594", 113, 203, 94, 79, 2], [2, "3474", 81, 235, 44, 47, 2], [2, "3545", 177, 183, 10, 28, 0], [2, "3545", 97, 222, 10, 28, 0], [2, "3602", 113, 176, 22, 29, 2], [2, "3646", 18, 223, 42, 87, 2], [2, "3646", -10, 236, 42, 87, 2], [2, "3036", 584, 363, 70, 54, 2], [2, "897", 642, 356, 22, 62, 0], [2, "897", 644, 307, 22, 62, 0], [2, "3587", 263, 194, 64, 50, 0], [2, "3562", 281, 178, 26, 35, 0], [2, "3584", 549, 510, 56, 43, 0], [2, "3360", 663, 379, 58, 57, 0], [2, "3360", 746, 422, 58, 57, 0], [2, "3359", 491, 298, 32, 43, 0], [2, "3359", 517, 311, 32, 43, 0], [2, "3570", 712, 421, 36, 34, 0], [2, "3557", 721, 395, 22, 38, 0], [2, "41", 571, 514, 12, 11, 0], [2, "7", 379, 278, 28, 27, 0], [2, "679", 419, 252, 36, 32, 0], [2, "3361", 498, 293, 18, 17, 0], [2, "637", 521, 293, 26, 32, 0], [2, "1108", 354, 330, 26, 31, 0], [2, "3528", 310, 352, 14, 31, 0], [2, "3528", 292, 343, 14, 31, 0], [2, "673", 282, 393, 80, 63, 2], [2, "1108", 333, 338, 26, 31, 0], [2, "3987", 406, 290, 42, 42, 0]]}, {"type": 3, "obj": [[2, "3050", 905, 212, 62, 59, 2], [2, "1430", 802, 213, 66, 35, 0], [2, "1430", 773, 228, 66, 35, 0], [2, "1430", 741, 242, 66, 35, 0], [2, "1430", 712, 257, 66, 35, 0], [2, "3735", 573, 340, 82, 45, 0], [2, "3736", 17, 448, 76, 41, 0], [2, "3025", 604, 304, 92, 53, 0], [2, "1501", -8, 141, 50, 26, 0], [2, "1501", 18, 128, 50, 26, 0], [2, "1501", 44, 115, 50, 26, 0], [2, "1501", 70, 128, 50, 26, 0], [2, "1501", 44, 141, 50, 26, 0], [2, "1501", 18, 154, 50, 26, 0], [2, "1501", 44, 167, 50, 26, 0], [2, "1501", 70, 154, 50, 26, 0], [2, "1501", 96, 141, 50, 26, 0], [2, "1501", 63, 114, 50, 26, 0], [2, "1501", 89, 101, 50, 26, 0], [2, "1501", 115, 88, 50, 26, 0], [2, "1501", 141, 101, 50, 26, 0], [2, "1501", 115, 114, 50, 26, 0], [2, "1501", 89, 127, 50, 26, 0], [2, "1501", 115, 140, 50, 26, 0], [2, "1501", 141, 127, 50, 26, 0], [2, "1501", 167, 114, 50, 26, 0], [2, "1501", 880, 620, 50, 26, 0], [2, "1501", 906, 607, 50, 26, 0], [2, "1501", 932, 594, 50, 26, 0], [2, "1501", 958, 607, 50, 26, 0], [2, "1501", 932, 620, 50, 26, 0], [2, "1501", 906, 633, 50, 26, 0], [2, "1501", 932, 646, 50, 26, 0], [2, "1501", 958, 633, 50, 26, 0], [2, "1501", 984, 620, 50, 26, 0], [2, "1501", 180, -3, 50, 26, 0], [2, "1501", 206, -16, 50, 26, 0], [2, "1501", 232, -29, 50, 26, 0], [2, "1501", 258, -16, 50, 26, 0], [2, "1501", 232, -3, 50, 26, 0], [2, "1501", 206, 10, 50, 26, 0], [2, "1501", 232, 23, 50, 26, 0], [2, "1501", 258, 10, 50, 26, 0], [2, "1501", 284, -3, 50, 26, 0], [2, "1501", 112, 19, 50, 26, 0], [2, "1501", 138, 6, 50, 26, 0], [2, "1501", 164, -7, 50, 26, 0], [2, "1501", 190, 6, 50, 26, 0], [2, "1501", 164, 19, 50, 26, 0], [2, "1501", 138, 32, 50, 26, 0], [2, "1501", 164, 45, 50, 26, 0], [2, "1501", 190, 32, 50, 26, 0], [2, "1501", 216, 19, 50, 26, 0], [2, "1501", 29, 51, 50, 26, 0], [2, "1501", 55, 38, 50, 26, 0], [2, "1501", 81, 25, 50, 26, 0], [2, "1501", 107, 38, 50, 26, 0], [2, "1501", 81, 51, 50, 26, 0], [2, "1501", 55, 64, 50, 26, 0], [2, "1501", 81, 77, 50, 26, 0], [2, "1501", 107, 64, 50, 26, 0], [2, "1501", 133, 51, 50, 26, 0], [2, "1501", -36, 90, 50, 26, 0], [2, "1501", -10, 77, 50, 26, 0], [2, "1501", 16, 64, 50, 26, 0], [2, "1501", 42, 77, 50, 26, 0], [2, "1501", 16, 90, 50, 26, 0], [2, "1501", -10, 103, 50, 26, 0], [2, "1501", 16, 116, 50, 26, 0], [2, "1501", 42, 103, 50, 26, 0], [2, "1501", 68, 90, 50, 26, 0], [2, "1501", 23, -11, 50, 26, 0], [2, "1501", 49, -24, 50, 26, 0], [2, "1501", 75, -37, 50, 26, 0], [2, "1501", 101, -24, 50, 26, 0], [2, "1501", 75, -11, 50, 26, 0], [2, "1501", 49, 2, 50, 26, 0], [2, "1501", 75, 15, 50, 26, 0], [2, "1501", 101, 2, 50, 26, 0], [2, "1501", 127, -11, 50, 26, 0], [2, "1501", -3, -11, 50, 26, 0], [2, "1501", 23, -24, 50, 26, 0], [2, "1501", 49, -37, 50, 26, 0], [2, "1501", 75, -24, 50, 26, 0], [2, "1501", 49, -11, 50, 26, 0], [2, "1501", 23, 2, 50, 26, 0], [2, "1501", 49, 15, 50, 26, 0], [2, "1501", 75, 2, 50, 26, 0], [2, "1501", 101, -11, 50, 26, 0], [2, "1501", 839, 60, 50, 26, 0], [2, "1501", 865, 47, 50, 26, 0], [2, "1501", 891, 34, 50, 26, 0], [2, "1501", 917, 47, 50, 26, 0], [2, "1501", 891, 60, 50, 26, 0], [2, "1501", 865, 73, 50, 26, 0], [2, "1501", 891, 86, 50, 26, 0], [2, "1501", 917, 73, 50, 26, 0], [2, "1501", 943, 60, 50, 26, 0], [2, "1501", 910, 96, 50, 26, 0], [2, "1501", 936, 83, 50, 26, 0], [2, "1501", 962, 70, 50, 26, 0], [2, "1501", 988, 83, 50, 26, 0], [2, "1501", 962, 96, 50, 26, 0], [2, "1501", 936, 109, 50, 26, 0], [2, "1501", 962, 122, 50, 26, 0], [2, "1501", 988, 109, 50, 26, 0], [2, "1501", 1014, 96, 50, 26, 0], [2, "1501", 829, -18, 50, 26, 0], [2, "1501", 855, -31, 50, 26, 0], [2, "1501", 881, -44, 50, 26, 0], [2, "1501", 907, -31, 50, 26, 0], [2, "1501", 881, -18, 50, 26, 0], [2, "1501", 855, -5, 50, 26, 0], [2, "1501", 881, 8, 50, 26, 0], [2, "1501", 907, -5, 50, 26, 0], [2, "1501", 933, -18, 50, 26, 0], [2, "1501", 902, 20, 50, 26, 0], [2, "1501", 928, 7, 50, 26, 0], [2, "1501", 954, -6, 50, 26, 0], [2, "1501", 980, 7, 50, 26, 0], [2, "1501", 954, 20, 50, 26, 0], [2, "1501", 928, 33, 50, 26, 0], [2, "1501", 954, 46, 50, 26, 0], [2, "1501", 980, 33, 50, 26, 0], [2, "1501", 1006, 20, 50, 26, 0], [2, "1501", 935, 41, 50, 26, 0], [2, "1501", 961, 28, 50, 26, 0], [2, "1501", 987, 15, 50, 26, 0], [2, "1501", 1013, 28, 50, 26, 0], [2, "1501", 987, 41, 50, 26, 0], [2, "1501", 961, 54, 50, 26, 0], [2, "1501", 987, 67, 50, 26, 0], [2, "1501", 1013, 54, 50, 26, 0], [2, "1501", 1039, 41, 50, 26, 0], [2, "1501", 694, -13, 50, 26, 0], [2, "1501", 720, -26, 50, 26, 0], [2, "1501", 746, -39, 50, 26, 0], [2, "1501", 772, -26, 50, 26, 0], [2, "1501", 746, -13, 50, 26, 0], [2, "1501", 720, 0, 50, 26, 0], [2, "1501", 746, 13, 50, 26, 0], [2, "1501", 772, 0, 50, 26, 0], [2, "1501", 798, -13, 50, 26, 0], [2, "1501", 767, 24, 50, 26, 0], [2, "1501", 793, 11, 50, 26, 0], [2, "1501", 819, -2, 50, 26, 0], [2, "1501", 845, 11, 50, 26, 0], [2, "1501", 819, 24, 50, 26, 0], [2, "1501", 793, 37, 50, 26, 0], [2, "1501", 819, 50, 50, 26, 0], [2, "1501", 845, 37, 50, 26, 0], [2, "1501", 871, 24, 50, 26, 0], [2, "1501", 313, -23, 50, 26, 0], [2, "1501", 339, -36, 50, 26, 0], [2, "1501", 365, -49, 50, 26, 0], [2, "1501", 391, -36, 50, 26, 0], [2, "1501", 365, -23, 50, 26, 0], [2, "1501", 339, -10, 50, 26, 0], [2, "1501", 365, 3, 50, 26, 0], [2, "1501", 391, -10, 50, 26, 0], [2, "1501", 417, -23, 50, 26, 0], [2, "3050", 446, 4, 62, 59, 0], [2, "3050", 388, 33, 62, 59, 0], [2, "3050", 329, 67, 62, 59, 0], [2, "3050", 271, 96, 62, 59, 0], [2, "3050", 233, 115, 62, 59, 0], [2, "3050", 507, 4, 62, 59, 2], [2, "3050", 567, 36, 62, 59, 2], [2, "3050", 627, 67, 62, 59, 2], [2, "3050", 687, 99, 62, 59, 2], [2, "3050", 747, 128, 62, 59, 2], [2, "3050", 807, 160, 62, 59, 2], [2, "3050", 869, 192, 62, 59, 2], [2, "3367", 177, 121, 44, 81, 2], [2, "3367", 133, 142, 44, 81, 2], [2, "3367", 89, 164, 44, 81, 2], [2, "3367", 58, 177, 44, 81, 2], [2, "3367", 4, 205, 44, 81, 2], [2, "3367", -16, 215, 44, 81, 2], [2, "3367", 7, 215, 44, 81, 2], [2, "3367", 69, 188, 44, 81, 2], [2, "3367", 111, 169, 44, 81, 2], [2, "3367", 149, 143, 44, 81, 2], [2, "3367", 179, 133, 44, 81, 2], [2, "3367", 247, 119, 44, 81, 0], [2, "3367", 247, 126, 44, 81, 0], [2, "3367", 292, 136, 44, 81, 0], [2, "3367", 292, 151, 44, 81, 0], [2, "3367", 292, 153, 44, 81, 0], [2, "3367", 292, 138, 44, 81, 0], [2, "3367", 335, 175, 44, 81, 0], [2, "3367", 335, 160, 44, 81, 0], [2, "3367", 380, 197, 44, 81, 0], [2, "3367", 380, 182, 44, 81, 0], [2, "3367", 426, 216, 44, 81, 0], [2, "3367", 426, 201, 44, 81, 0], [2, "3367", 469, 242, 44, 81, 0], [2, "3367", 469, 227, 44, 81, 0], [2, "3367", 514, 256, 44, 81, 0], [2, "3367", 514, 241, 44, 81, 0], [2, "3367", 648, 334, 44, 81, 0], [2, "3367", 648, 319, 44, 81, 0], [2, "3367", 690, 350, 44, 81, 0], [2, "3367", 690, 335, 44, 81, 0], [2, "3367", 735, 370, 44, 81, 0], [2, "3367", 735, 355, 44, 81, 0], [2, "3367", 779, 386, 44, 81, 0], [2, "3367", 779, 371, 44, 81, 0], [2, "3367", 819, 409, 44, 81, 0], [2, "3367", 819, 394, 44, 81, 0], [2, "3367", 871, 438, 44, 81, 0], [2, "3367", 871, 423, 44, 81, 0], [2, "3367", 528, 265, 44, 81, 0], [2, "3367", 528, 250, 44, 81, 0], [2, "3367", 649, 325, 44, 81, 0], [2, "3367", 649, 310, 44, 81, 0], [2, "1430", 477, 43, 66, 35, 0], [2, "1430", 448, 58, 66, 35, 0], [2, "1430", 417, 73, 66, 35, 0], [2, "1430", 388, 88, 66, 35, 0], [2, "1430", 507, 59, 66, 35, 0], [2, "1430", 478, 74, 66, 35, 0], [2, "1430", 447, 89, 66, 35, 0], [2, "1430", 418, 104, 66, 35, 0], [2, "1430", 537, 75, 66, 35, 0], [2, "1430", 508, 90, 66, 35, 0], [2, "1430", 477, 105, 66, 35, 0], [2, "1430", 448, 120, 66, 35, 0], [2, "1430", 565, 89, 66, 35, 0], [2, "1430", 536, 104, 66, 35, 0], [2, "1430", 505, 119, 66, 35, 0], [2, "1430", 476, 134, 66, 35, 0], [2, "1430", 359, 104, 66, 35, 0], [2, "1430", 389, 120, 66, 35, 0], [2, "1430", 419, 136, 66, 35, 0], [2, "1430", 447, 150, 66, 35, 0], [2, "1430", 833, 229, 66, 35, 0], [2, "1430", 804, 244, 66, 35, 0], [2, "1430", 861, 243, 66, 35, 0], [2, "1430", 832, 258, 66, 35, 0], [2, "1430", 772, 258, 66, 35, 0], [2, "1430", 743, 273, 66, 35, 0], [2, "1430", 800, 272, 66, 35, 0], [2, "1430", 771, 287, 66, 35, 0], [2, "3367", 197, 128, 44, 81, 2], [2, "3367", 197, 108, 44, 81, 2], [2, "3367", 24, 195, 44, 81, 2], [2, "3367", -18, 229, 44, 81, 2], [2, "3367", 173, 331, 44, 81, 2], [2, "3367", 173, 349, 44, 81, 2], [2, "3367", -6, 418, 44, 81, 2], [2, "3367", -6, 436, 44, 81, 2], [2, "3367", 214, 307, 44, 81, 2], [2, "3367", 214, 325, 44, 81, 2], [2, "3367", 255, 290, 44, 81, 2], [2, "3367", 255, 308, 44, 81, 2], [2, "3367", 297, 266, 44, 81, 2], [2, "3367", 297, 284, 44, 81, 2], [2, "3367", 358, 231, 44, 81, 2], [2, "3367", 358, 249, 44, 81, 2], [2, "3367", 388, 218, 44, 81, 2], [2, "3367", 388, 236, 44, 81, 2], [2, "3367", 138, 156, 44, 81, 2], [2, "3367", 343, 235, 44, 81, 2], [2, "3367", 343, 253, 44, 81, 2], [2, "3744", 510, 386, 96, 49, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 16, 1, 2, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 31, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 29, 24, 25, 26, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, 10, 10, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "3736", 111, 399, 76, 41, 0], [2, "3736", 195, 185, 76, 41, 0], [2, "3736", 147, 209, 76, 41, 0], [2, "3736", 225, 200, 76, 41, 0], [2, "3736", 177, 224, 76, 41, 0], [2, "3736", 254, 215, 76, 41, 0], [2, "3736", 206, 239, 76, 41, 0], [2, "3736", 283, 230, 76, 41, 0], [2, "3736", 235, 254, 76, 41, 0], [2, "3736", 99, 233, 76, 41, 0], [2, "3736", 51, 257, 76, 41, 0], [2, "3736", 129, 248, 76, 41, 0], [2, "3736", 81, 272, 76, 41, 0], [2, "3736", 158, 263, 76, 41, 0], [2, "3736", 110, 287, 76, 41, 0], [2, "3736", 187, 278, 76, 41, 0], [2, "3736", 139, 302, 76, 41, 0], [2, "3736", 3, 281, 76, 41, 0], [2, "3736", -45, 305, 76, 41, 0], [2, "3736", 33, 296, 76, 41, 0], [2, "3736", -15, 320, 76, 41, 0], [2, "3736", 62, 311, 76, 41, 0], [2, "3736", 14, 335, 76, 41, 0], [2, "3736", 91, 326, 76, 41, 0], [2, "3736", 43, 350, 76, 41, 0], [2, "3736", -44, 354, 76, 41, 0], [2, "3736", -15, 369, 76, 41, 0], [2, "3736", -63, 393, 76, 41, 0], [2, "3736", 105, 361, 76, 41, 0], [2, "3736", 135, 376, 76, 41, 0], [2, "3736", 57, 386, 76, 41, 0], [2, "3736", 87, 401, 76, 41, 0], [2, "3736", 10, 411, 76, 41, 0], [2, "3736", 40, 426, 76, 41, 0], [2, "3736", 394, 290, 76, 41, 0], [2, "3736", 346, 314, 76, 41, 0], [2, "3736", 424, 305, 76, 41, 0], [2, "3736", 376, 329, 76, 41, 0], [2, "3736", 453, 320, 76, 41, 0], [2, "3736", 405, 344, 76, 41, 0], [2, "3736", 482, 335, 76, 41, 0], [2, "3736", 434, 359, 76, 41, 0], [2, "3736", 299, 338, 76, 41, 0], [2, "3736", 251, 362, 76, 41, 0], [2, "3736", 329, 353, 76, 41, 0], [2, "3736", 281, 377, 76, 41, 0], [2, "3736", 358, 368, 76, 41, 0], [2, "3736", 310, 392, 76, 41, 0], [2, "3736", 387, 383, 76, 41, 0], [2, "3736", 339, 407, 76, 41, 0], [2, "3736", 231, 372, 76, 41, 0], [2, "3736", 183, 396, 76, 41, 0], [2, "3736", 261, 387, 76, 41, 0], [2, "3736", 213, 411, 76, 41, 0], [2, "3736", 290, 402, 76, 41, 0], [2, "3736", 242, 426, 76, 41, 0], [2, "3736", 319, 417, 76, 41, 0], [2, "3736", 271, 441, 76, 41, 0]]}, {"type": 2, "data": [86, 86, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 57, 59, 60, 61, 82, 81, 57, 57, 57, 57, 57, 57, 78, 79, 76, 60, 61, 57, 57, 57, 57, 57, 57, 57, 57, 78, 86, 86, 86, 86, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 84, 83, 62, 63, 64, 63, 64, 77, 57, 57, 57, 57, 79, 75, 76, 62, 63, 64, 60, 61, 57, 57, 57, 57, 57, 57, 57, 86, 86, 86, 86, 45, 45, 45, 45, 45, 45, 45, 45, 57, 84, 79, 80, 65, 66, 67, 66, 67, 77, 57, 57, 57, 57, 83, 63, 64, 65, 59, 60, 61, 59, 60, 61, 57, 57, 57, 57, 78, 86, 45, 86, 45, 45, 45, 45, 45, 45, 45, 57, 57, 57, 57, 58, 54, 54, 55, 63, 70, 69, 73, 57, 57, 57, 57, 58, 68, 59, 60, 61, 63, 64, 59, 60, 61, 60, 61, 78, 78, 57, 86, 86, 45, 45, 45, 45, 45, 45, 45, 45, 57, 57, 57, 57, 57, 57, 57, 58, 54, 73, 72, 57, 57, 57, 57, 57, 57, 71, 62, 63, 64, 66, 67, 62, 63, 64, 59, 60, 70, 69, 78, 86, 86, 45, 45, 45, 45, 45, 45, 45, 45, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 78, 79, 81, 85, 57, 57, 57, 58, 54, 55, 67, 60, 61, 59, 60, 61, 70, 69, 73, 72, 57, 86, 45, 45, 45, 45, 45, 45, 45, 45, 45, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 72, 83, 63, 77, 57, 57, 57, 57, 57, 58, 55, 63, 64, 62, 70, 69, 73, 72, 78, 57, 57, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 58, 69, 73, 57, 57, 57, 57, 57, 57, 58, 54, 54, 73, 73, 72, 57, 57, 57, 57, 57, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 44, 45, 46, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 44, 45, 47, 48, 49, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 57, 78, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 44, 45, 44, 45, 46, 50, 51, 52, 44, 45, 46, 44, 45, 57, 57, 57, 57, 57, 81, 81, 85, 84, 57, 57, 57, 57, 57, 57, 84, 79, 75, 45, 45, 45, 45, 45, 44, 45, 46, 44, 45, 46, 48, 47, 48, 49, 52, 48, 49, 47, 48, 49, 47, 48, 57, 57, 57, 57, 57, 57, 60, 82, 81, 85, 57, 57, 57, 57, 79, 81, 80, 63, 45, 45, 45, 45, 45, 45, 48, 49, 47, 48, 49, 51, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 57, 57, 57, 57, 57, 57, 57, 63, 77, 57, 57, 57, 57, 71, 62, 65, 66, 45, 45, 45, 45, 45, 50, 51, 52, 50, 51, 52, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 51, 57, 44, 45, 46, 57, 57, 77, 57, 57, 57, 57, 58, 55, 66, 70, 45, 45, 45, 45, 45, 45, 45, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 57, 57, 57, 57, 57, 57, 57, 57, 58, 54, 73, 45, 45, 45, 45, 45, 45, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 49, 57, 57, 57, 57, 57, 78, 57, 57, 57, 57, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 57, 57, 57, 57, 57, 57, 57, 57, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 44, 45, 46, 44, 57, 57, 57, 47, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 47, 48, 49, 47, 48, 49, 47, 47, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 50, 51, 52, 50, 51, 52, 47, 47, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 47, 47, 47, 47, 47, 47, 47, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 47, 47, 47, 47, 47, 47, 47, 47, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 44, 45, 46, 47, 47, 47, 47, 47, 47, 47, 47, 47, 47, 47, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 48, 49, 47, 47, 47, 47, 47, 47, 47, 47, 47, 47, 47, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 50, 51, 52, 47, 47, 47, 47, 47, 47, 47, 47, 47, 47, 47]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}