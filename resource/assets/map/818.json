{"mW": 960, "mH": 720, "tW": 24, "tH": 24, "tiles": [["302_4", 0, 2, 2], ["302_4", 2, 2, 2], ["1139", 0, 1, 1], ["1139", 2, 1, 1], ["1134", 0, 3, 2], ["1134", 2, 3, 2], ["1134", 1, 3, 2], ["1134", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1316", 0, 4, 2]], "layers": [{"type": 3, "obj": [[2, "1140", 570, 392, 58, 47, 2], [2, "1141", 929, 658, 54, 67, 0], [2, "1141", 886, 679, 54, 67, 0], [2, "1141", 842, 700, 54, 67, 0], [2, "1140", 929, 633, 58, 47, 0], [2, "1140", 886, 654, 58, 47, 0], [2, "1140", 842, 675, 58, 47, 0], [2, "1141", 494, 655, 54, 67, 0], [2, "1141", 451, 676, 54, 67, 0], [2, "1140", 492, 635, 58, 47, 0], [2, "1140", 450, 657, 58, 47, 0], [2, "1141", 540, 661, 54, 67, 2], [2, "1140", 538, 637, 58, 47, 2], [2, "1140", 580, 658, 58, 47, 2], [2, "1141", 582, 682, 54, 67, 2], [2, "1140", 406, 678, 58, 47, 0], [2, "1141", 407, 697, 54, 67, 0], [2, "1140", 363, 700, 58, 47, 0], [2, "1140", 798, 697, 58, 47, 0], [2, "1140", 913, 244, 58, 47, 0], [2, "1140", 871, 265, 58, 47, 0], [2, "1140", 828, 288, 58, 47, 0], [2, "1140", 785, 309, 58, 47, 0], [2, "1140", 741, 331, 58, 47, 0], [2, "1140", 698, 352, 58, 47, 0], [2, "1140", 655, 373, 58, 47, 0], [2, "1140", 613, 394, 58, 47, 0], [2, "1141", 623, 702, 54, 67, 2], [2, "1140", 623, 680, 58, 47, 2], [2, "1140", 667, 702, 58, 47, 2]]}, {"type": 4, "obj": [[2, "1143", 756, -17, 32, 36, 2], [2, "1143", 9, -1, 32, 36, 0], [2, "1143", 852, 23, 32, 36, 2], [2, "1135", 346, 2, 118, 114, 2], [2, "1143", 164, 94, 32, 36, 2], [2, "1143", 627, 105, 32, 36, 0], [2, "1143", 920, 109, 32, 36, 2], [2, "1143", 281, 146, 32, 36, 2], [2, "1143", 528, 151, 32, 36, 0], [2, "1141", 911, 270, 54, 67, 0], [2, "1141", 868, 292, 54, 67, 0], [2, "1136", 773, 267, 108, 95, 0], [2, "1143", 14, 331, 32, 36, 0], [2, "1141", 824, 315, 54, 67, 0], [2, "1143", 936, 352, 32, 36, 0], [2, "1141", 780, 337, 54, 67, 0], [2, "1143", 378, 385, 32, 36, 2], [2, "1141", 740, 356, 54, 67, 0], [2, "1143", 844, 397, 32, 36, 0], [2, "1143", 274, 400, 32, 36, 0], [2, "1141", 699, 374, 54, 67, 0], [2, "1141", 656, 395, 54, 67, 0], [2, "1143", 474, 429, 32, 36, 2], [2, "1143", 753, 438, 32, 36, 0], [2, "1141", 570, 411, 54, 67, 2], [2, "1141", 615, 418, 54, 67, 0], [2, "1143", 576, 474, 32, 36, 2], [2, "1143", 648, 481, 32, 36, 0], [2, "1143", 893, 512, 32, 36, 2], [2, "1143", 317, 595, 32, 36, 0], [2, "1143", 215, 640, 32, 36, 0], [2, "1136", 842, 619, 108, 95, 0], [2, "1143", 111, 680, 32, 36, 0]]}, {"type": 3, "obj": [[2, "1140", -45, 248, 58, 47, 2], [2, "1141", 720, -46, 54, 67, 2], [2, "1141", 763, -25, 54, 67, 2], [2, "1141", 75, -55, 54, 67, 0], [2, "1141", 912, 311, 54, 67, 0], [2, "1141", 912, 352, 54, 67, 0], [2, "1141", 876, 331, 54, 67, 0], [2, "1141", 876, 372, 54, 67, 0], [2, "1141", 833, 352, 54, 67, 0], [2, "1141", 833, 393, 54, 67, 0], [2, "1141", 790, 372, 54, 67, 0], [2, "1141", 790, 413, 54, 67, 0], [2, "1141", 747, 392, 54, 67, 0], [2, "1141", 747, 433, 54, 67, 0], [2, "1141", 368, 379, 54, 67, 2], [2, "1141", 366, 351, 54, 67, 2], [2, "1141", 313, 379, 54, 67, 0], [2, "683_1", 709, 91, 22, 34, 2], [2, "683_1", 701, 95, 22, 34, 2], [2, "683_1", 692, 100, 22, 34, 2], [2, "683_1", 684, 104, 22, 34, 2], [2, "683_1", 88, 70, 22, 34, 0], [2, "683_1", 95, 74, 22, 34, 0], [2, "683_1", 103, 78, 22, 34, 0], [2, "683_1", 111, 82, 22, 34, 0], [2, "683_1", 119, 86, 22, 34, 0], [2, "1141", 806, -4, 54, 67, 2], [2, "1141", 850, 16, 54, 67, 2], [2, "1141", 912, 88, 54, 67, 0], [2, "1142", 860, 29, 30, 37, 2], [2, "1142", 813, 7, 30, 37, 2], [2, "702_1", 861, 31, 18, 25, 0], [2, "1141", 891, 36, 54, 67, 2], [2, "1141", 914, 41, 54, 67, 2], [2, "1137", 866, 58, 66, 59, 2], [2, "1140", 804, -29, 58, 47, 2], [2, "1140", 847, -8, 58, 47, 2], [2, "1140", 891, 13, 58, 47, 2], [2, "1140", 934, 34, 58, 47, 2], [2, "1140", 940, 52, 58, 47, 0], [2, "1140", 912, 66, 58, 47, 0], [2, "683_1", 677, 108, 22, 34, 2], [2, "683_1", 669, 112, 22, 34, 2], [2, "683_1", 660, 117, 22, 34, 2], [2, "683_1", 652, 121, 22, 34, 2], [2, "683_1", 126, 89, 22, 34, 0], [2, "683_1", 133, 93, 22, 34, 0], [2, "683_1", 141, 97, 22, 34, 0], [2, "683_1", 149, 101, 22, 34, 0], [2, "683_1", 157, 105, 22, 34, 0], [2, "683_1", 645, 125, 22, 34, 2], [2, "683_1", 637, 129, 22, 34, 2], [2, "683_1", 628, 134, 22, 34, 2], [2, "683_1", 620, 138, 22, 34, 2], [2, "1141", 270, 399, 54, 67, 0], [2, "1141", 228, 419, 54, 67, 0], [2, "1141", 313, 348, 54, 67, 0], [2, "1141", 270, 369, 54, 67, 0], [2, "1141", 228, 390, 54, 67, 0], [2, "1141", 704, 454, 54, 67, 0], [2, "1141", 660, 476, 54, 67, 0], [2, "1141", 399, 397, 54, 67, 2], [2, "1141", 442, 418, 54, 67, 2], [2, "1141", 485, 441, 54, 67, 2], [2, "1141", 526, 461, 54, 67, 2], [2, "1141", 569, 483, 54, 67, 2], [2, "1141", 583, 491, 54, 67, 2], [2, "1141", 616, 497, 54, 67, 0], [2, "14_5", 585, 540, 32, 30, 2], [2, "1141", 399, 366, 54, 67, 2], [2, "1141", 442, 385, 54, 67, 2], [2, "1141", 485, 406, 54, 67, 2], [2, "1141", 528, 430, 54, 67, 2], [2, "1141", 572, 451, 54, 67, 2], [2, "1141", 704, 413, 54, 67, 0], [2, "1141", 659, 436, 54, 67, 0], [2, "1141", 615, 460, 54, 67, 0], [2, "929_1", 364, 343, 60, 31, 2], [2, "929_1", 408, 365, 60, 31, 2], [2, "929_1", 452, 386, 60, 31, 2], [2, "929_1", 496, 407, 60, 31, 2], [2, "929_1", 540, 429, 60, 31, 2], [2, "929_1", 583, 451, 60, 31, 2], [2, "929_1", 309, 342, 60, 31, 0], [2, "929_1", 266, 363, 60, 31, 0], [2, "1137", 370, 420, 66, 59, 2], [2, "14_5", 424, 450, 32, 30, 0], [2, "1142", 431, 411, 30, 37, 2], [2, "1142", 480, 434, 30, 37, 2], [2, "1142", 531, 459, 30, 37, 2], [2, "1142", 581, 480, 30, 37, 2], [2, "702_1", 486, 442, 18, 25, 0], [2, "702_1", 586, 487, 18, 25, 0], [2, "1142", 647, 488, 30, 37, 0], [2, "1142", 700, 465, 30, 37, 0], [2, "702_1", 656, 492, 18, 25, 2], [2, "1141", 182, 430, 54, 67, 0], [2, "1141", 188, 412, 54, 67, 0], [2, "1141", 146, 432, 54, 67, 0], [2, "929_1", 179, 407, 60, 31, 0], [2, "929_1", 138, 428, 60, 31, 0], [2, "929_1", 223, 384, 60, 31, 0], [2, "1142", 330, 381, 30, 37, 0], [2, "1142", 271, 406, 30, 37, 0], [2, "1142", 199, 440, 30, 37, 0], [2, "702_1", 279, 407, 18, 25, 2], [2, "11_2", 362, 332, 32, 29, 0], [2, "43_6", 568, 150, 82, 58, 2], [2, "43_6", 521, 174, 82, 58, 2], [2, "1138", 595, 95, 46, 60, 2], [2, "43_6", 557, 141, 82, 58, 2], [2, "43_6", 515, 162, 82, 58, 2], [2, "683_1", 513, 185, 22, 34, 2], [2, "683_1", 505, 189, 22, 34, 2], [2, "683_1", 496, 194, 22, 34, 2], [2, "683_1", 488, 198, 22, 34, 2], [2, "1138", 498, 146, 46, 60, 2], [2, "702_1", 533, 162, 18, 25, 2], [2, "702_1", 631, 112, 18, 25, 2], [2, "683_1", 479, 202, 22, 34, 2], [2, "683_1", 471, 206, 22, 34, 2], [2, "683_1", 462, 211, 22, 34, 2], [2, "683_1", 454, 215, 22, 34, 2], [2, "683_1", 165, 110, 22, 34, 0], [2, "683_1", 172, 114, 22, 34, 0], [2, "683_1", 180, 118, 22, 34, 0], [2, "683_1", 188, 122, 22, 34, 0], [2, "683_1", 196, 126, 22, 34, 0], [2, "43_6", 177, 138, 82, 58, 0], [2, "43_6", 225, 162, 82, 58, 0], [2, "43_6", 187, 127, 82, 58, 0], [2, "43_6", 235, 151, 82, 58, 0], [2, "683_1", 306, 180, 22, 34, 0], [2, "683_1", 313, 184, 22, 34, 0], [2, "683_1", 321, 188, 22, 34, 0], [2, "683_1", 329, 192, 22, 34, 0], [2, "683_1", 337, 196, 22, 34, 0], [2, "1138", 296, 140, 46, 60, 0], [2, "702_1", 290, 157, 18, 25, 0], [2, "1138", 179, 85, 46, 60, 0], [2, "702_1", 174, 104, 18, 25, 0], [2, "683_1", 346, 200, 22, 34, 0], [2, "683_1", 353, 204, 22, 34, 0], [2, "683_1", 361, 208, 22, 34, 0], [2, "683_1", 369, 212, 22, 34, 0], [2, "683_1", 377, 216, 22, 34, 0], [2, "683_1", 447, 219, 22, 34, 2], [2, "683_1", 439, 223, 22, 34, 2], [2, "683_1", 430, 228, 22, 34, 2], [2, "683_1", 422, 232, 22, 34, 2], [2, "683_1", 385, 220, 22, 34, 0], [2, "683_1", 392, 224, 22, 34, 0], [2, "683_1", 400, 228, 22, 34, 0], [2, "683_1", 408, 232, 22, 34, 0], [2, "683_1", 416, 236, 22, 34, 0], [2, "84_1", 642, 120, 66, 65, 2], [2, "84_1", 475, 200, 66, 65, 2], [2, "84_1", 299, 194, 66, 65, 0], [2, "84_1", 108, 96, 66, 65, 0], [2, "688_1", 636, 51, 46, 24, 2], [2, "688_1", 676, 72, 46, 24, 2], [2, "688_1", 599, 32, 46, 24, 2], [2, "688_1", 561, 13, 46, 24, 2], [2, "688_1", 137, 31, 46, 24, 0], [2, "688_1", 99, 50, 46, 24, 0], [2, "688_1", 175, 12, 46, 24, 0], [2, "367_1", 362, 173, 14, 42, 0], [2, "367_1", 417, 197, 14, 42, 2], [2, "367_1", 473, 173, 14, 42, 2], [2, "367_1", 660, 84, 14, 42, 2], [2, "367_1", 708, 56, 14, 42, 2], [2, "367_1", 642, 18, 14, 42, 2], [2, "367_1", 580, -13, 14, 42, 2], [2, "367_1", 94, 38, 14, 42, 0], [2, "367_1", 144, 62, 14, 42, 0], [2, "367_1", 156, 5, 14, 42, 2], [2, "1137", 25, 299, 66, 59, 0], [2, "11_2", 630, 373, 32, 29, 0], [2, "954_3", 883, 171, 24, 25, 2], [2, "43_7", 224, 478, 82, 58, 2], [2, "43_7", 197, 452, 82, 58, 2], [2, "43_7", 170, 424, 82, 58, 2], [2, "929_1", 121, 449, 60, 31, 2], [2, "929_1", 165, 471, 60, 31, 2], [2, "929_1", 210, 493, 60, 31, 2], [2, "929_1", 253, 514, 60, 31, 2], [2, "1141", 302, 598, 54, 67, 0], [2, "1141", 258, 618, 54, 67, 0], [2, "1141", 216, 637, 54, 67, 0], [2, "1141", 302, 560, 54, 67, 0], [2, "1141", 258, 582, 54, 67, 0], [2, "1141", 215, 605, 54, 67, 0], [2, "929_1", 297, 536, 60, 31, 2], [2, "929_1", 280, 559, 60, 31, 0], [2, "929_1", 236, 581, 60, 31, 0], [2, "1142", 261, 622, 30, 37, 0], [2, "1142", 315, 599, 30, 37, 0], [2, "702_1", 322, 604, 18, 25, 2], [2, "1141", 33, -34, 54, 67, 0], [2, "1141", -11, -12, 54, 67, 0], [2, "1142", 11, 1, 30, 37, 0], [2, "1141", 888, 501, 54, 67, 2], [2, "1141", 927, 522, 54, 67, 2], [2, "1140", 887, 478, 58, 47, 2], [2, "1140", 928, 499, 58, 47, 2], [2, "1138", 209, 366, 46, 60, 2], [2, "1138", 126, 405, 46, 60, 2], [2, "929_1", 192, 604, 60, 31, 0], [2, "929_1", 148, 626, 60, 31, 0], [2, "1141", 173, 658, 54, 67, 0], [2, "1141", 172, 627, 54, 67, 0], [2, "1141", 130, 679, 54, 67, 0], [2, "1141", 129, 648, 54, 67, 0], [2, "1141", 87, 669, 54, 67, 0], [2, "1141", 44, 690, 54, 67, 0], [2, "929_1", 104, 647, 60, 31, 0], [2, "929_1", 60, 669, 60, 31, 0], [2, "929_1", 15, 691, 60, 31, 0], [2, "1142", 214, 645, 30, 37, 0], [2, "1142", 160, 668, 30, 37, 0], [2, "1142", 107, 691, 30, 37, 0], [2, "702_1", 222, 649, 18, 25, 2], [2, "702_1", 116, 692, 18, 25, 2], [2, "1142", 749, 442, 30, 37, 0], [2, "1142", 796, 420, 30, 37, 0], [2, "1142", 843, 398, 30, 37, 0], [2, "1142", 889, 379, 30, 37, 0], [2, "1142", 933, 356, 30, 37, 0], [2, "702_1", 758, 446, 18, 25, 2], [2, "702_1", 851, 402, 18, 25, 2], [2, "702_1", 942, 359, 18, 25, 2], [2, "14_5", 694, 533, 32, 30, 0], [2, "14_5", 700, 511, 32, 30, 0], [2, "14_5", 684, 518, 32, 30, 0], [2, "1137", 630, 527, 66, 59, 0], [2, "1141", 9, 315, 54, 67, 0], [2, "1141", -35, 337, 54, 67, 0], [2, "1141", -16, 279, 54, 67, 2], [2, "1140", -2, 269, 58, 47, 2], [2, "1140", 9, 290, 58, 47, 0], [2, "1140", -35, 312, 58, 47, 0], [2, "1142", 13, 335, 30, 37, 0], [2, "1142", 902, 518, 30, 37, 2], [2, "84_1", 912, 549, 66, 65, 0], [2, "1140", -10, -37, 58, 47, 0], [2, "11_2", -6, 381, 32, 29, 0], [2, "11_2", 840, 221, 32, 29, 0], [2, "1137", 916, 393, 66, 59, 2], [2, "11_2", 889, 423, 32, 29, 0], [2, "11_2", 860, 436, 32, 29, 0], [2, "954_3", 564, 527, 24, 25, 0], [2, "954_3", 57, 349, 24, 25, 0], [2, "1142", 53, -22, 30, 37, 0], [2, "1142", 758, -17, 30, 37, 2], [2, "688_1", 523, -7, 46, 24, 2], [2, "688_1", 214, -8, 46, 24, 0], [2, "11_2", 235, 508, 32, 29, 0], [2, "1137", 106, 450, 66, 59, 2], [2, "14_5", 162, 481, 32, 30, 0], [2, "14_5", 14, 674, 32, 30, 0], [2, "14_5", 3, 684, 32, 30, 0], [2, "14_5", -4, 669, 32, 30, 0], [2, "1141", 908, 107, 54, 67, 2], [2, "1140", 908, 84, 58, 47, 2], [2, "1141", 953, 127, 54, 67, 2], [2, "1140", 950, 106, 58, 47, 2], [2, "1142", 927, 125, 30, 37, 2], [2, "702_1", 930, 127, 18, 25, 0], [2, "702_1", 761, -13, 18, 25, 0], [2, "702_1", 20, 336, 18, 25, 2], [2, "1142", 380, 387, 30, 37, 2], [2, "702_1", 382, 391, 18, 25, 0], [2, "702_1", 905, 522, 18, 25, 0], [2, "14_5", 823, 52, 32, 30, 2], [2, "14_5", 842, 62, 32, 30, 2], [2, "14_5", 828, 42, 32, 30, 2], [2, "954_3", 788, 34, 24, 25, 0]]}, {"type": 2, "data": [-1, -1, -1, 50, 51, -1, -1, 75, 74, 73, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 69, 70, -1, -1, 65, 64, 63, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 47, 48, 74, 73, 73, -1, -1, -1, -1, 10, 11, 11, 17, 16, -1, 10, 11, 11, 12, -1, -1, -1, 68, 69, 70, -1, -1, 65, 64, 63, 44, -1, -1, -1, -1, -1, -1, 50, 51, 48, 74, 73, 73, -1, -1, -1, -1, -1, 25, 26, 26, 27, 29, 28, -1, 22, 32, 14, 15, 16, -1, -1, -1, -1, 68, 69, 70, -1, -1, 65, 64, 63, 44, -1, -1, -1, -1, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 23, 27, 24, -1, -1, -1, -1, 32, 26, 26, 19, -1, -1, -1, -1, -1, -1, 68, 69, 70, 10, 11, 12, 64, 63, 63, -1, -1, -1, -1, -1, 64, 63, 47, 48, 10, 11, 18, -1, -1, -1, -1, -1, -1, -1, 25, 26, 21, 26, 17, 17, 31, -1, -1, -1, -1, -1, -1, 68, 69, 13, 14, 15, 16, 46, 47, -1, -1, -1, -1, -1, 65, 64, 63, -1, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, 22, 23, 28, 22, 32, 29, -1, -1, -1, 16, -1, -1, 58, 59, 60, 25, 26, 26, 32, 31, 74, 73, -1, -1, -1, -1, -1, 63, 65, 51, 63, 11, 12, -1, -1, -1, -1, 42, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 32, 58, 59, -1, -1, 22, 23, 23, 29, 28, 64, 63, -1, -1, -1, -1, -1, -1, -1, 52, 65, 64, 63, -1, -1, 50, -1, 45, 44, 43, -1, -1, -1, -1, 44, -1, -1, -1, 50, 56, 50, 51, 52, -1, -1, -1, -1, -1, 70, -1, 65, 64, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 57, 56, 54, 53, 52, -1, -1, -1, 28, 57, 50, 51, 51, 47, 57, 47, 48, 75, -1, -1, -1, -1, -1, -1, -1, 70, -1, 34, -1, -1, 52, -1, -1, 18, 17, 17, 16, -1, -1, 65, 64, 63, 57, 56, 20, -1, -1, -1, 50, 51, 47, 48, 48, -1, -1, -1, -1, 18, 17, 17, 12, -1, -1, -1, -1, 34, 69, 37, -1, -1, -1, -1, 10, 21, 26, 26, 15, 16, -1, -1, -1, 65, 64, 63, 56, 50, 50, 58, 59, 60, -1, -1, 10, 11, 12, -1, 10, 11, 21, 32, 15, 16, -1, -1, -1, 49, 50, 68, 35, 36, -1, 13, 26, 26, 26, 26, 26, 15, 17, 17, 17, 16, -1, 65, 64, 63, 59, 60, 34, -1, 18, 17, 21, 32, 15, 17, 21, 32, 32, 32, 32, 19, -1, -1, 16, 46, 47, 75, 38, 39, 35, 13, 26, 26, 26, 26, 26, 26, 26, 26, 26, 31, -1, -1, -1, -1, -1, -1, -1, -1, 25, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 15, -1, 14, 31, -1, 57, 56, -1, -1, 38, 22, 23, 33, 26, 26, 26, 26, 26, 32, 26, 28, 34, 35, -1, -1, -1, -1, -1, -1, 25, 32, 32, 32, 32, 32, 32, 27, 29, 29, 28, -1, 29, 28, -1, -1, 54, 34, 10, 11, 50, 51, -1, 25, 26, 26, 26, 27, 29, 29, 28, -1, -1, -1, 42, 42, -1, -1, -1, -1, 22, 29, 28, 29, 33, 32, 32, 31, -1, 45, 44, 36, 42, 41, 40, 34, 35, 37, 50, 51, 47, 48, -1, 22, 33, 26, 27, 28, -1, -1, -1, -1, -1, 42, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 29, 28, 45, -1, -1, 38, 39, 45, 44, 43, 37, 38, 39, 50, 51, -1, -1, -1, -1, 30, 29, 28, -1, 41, 40, 40, -1, 34, 45, 44, -1, -1, -1, -1, -1, -1, -1, 16, -1, 42, 41, 57, 56, -1, 56, 23, 37, 38, 39, 38, 39, -1, -1, 47, 48, -1, -1, -1, -1, -1, -1, -1, 50, 44, 44, 55, -1, 34, 35, 35, 58, 58, 59, 60, -1, -1, 34, 35, 37, 45, 44, 54, 53, 54, 53, 57, 56, 28, 39, 36, 56, 56, 41, -1, -1, 42, 41, 34, 35, 42, 57, 56, 50, 51, 53, 52, 50, 51, 38, 38, 55, 50, -1, 75, 74, 22, 45, 44, 39, 44, 44, 41, 40, 48, -1, 54, 53, -1, 38, 56, 56, 45, 44, -1, -1, 34, 38, 50, 35, 45, 50, 51, 47, 48, 50, 51, 47, 48, 34, 57, 56, 50, 50, 50, 16, -1, 42, 45, 44, 51, 47, 48, -1, 18, 17, 17, 50, 50, 56, 56, 56, 45, 44, -1, -1, 37, 38, 56, 57, 56, 47, 57, 56, 10, 47, 48, -1, -1, -1, 54, 53, 57, 57, 56, 50, 50, -1, 51, 47, 48, 18, 17, 11, 32, 50, 50, 51, 57, 57, 50, 16, 46, 57, 41, 18, 17, 11, 12, 54, 53, -1, 54, 57, 56, -1, -1, 10, 11, 12, 34, 35, 54, 54, 53, 57, 50, 50, 50, 16, -1, 25, 26, 14, 50, 50, 51, 48, 48, 46, 56, 31, 49, 56, 18, 21, 20, 14, 15, 16, 48, 42, 50, 50, 57, 50, 50, 13, 14, 15, 16, 47, 56, 50, 58, 59, 57, 50, 50, 19, -1, 22, 23, 50, 50, 51, 52, -1, -1, 54, 53, 50, 54, 53, 22, 20, 20, 20, 20, 31, 34, 45, 53, 52, 45, 50, 50, 30, 22, 29, 28, 18, 17, 17, 16, -1, 46, 50, 50, 50, -1, -1, 50, 51, 47, 48, 10, 17, 16, -1, 57, 56, 50, 50, -1, 25, 20, 20, 20, 15, 16, 53, 57, 73, 74, 73, -1, -1, 50, 50, 51, 25, 26, 26, 15, 17, 16, -1, -1, 54, 53, 52, 47, 48, 10, 11, 21, 26, 15, 16, 54, 53, 57, 56, -1, 22, 20, 27, 33, 29, 28, 73, 73, 56, 56, -1, -1, 50, 51, 47, 48, 25, 26, 26, 26, 20, 19, -1, -1, -1, -1, -1, 18, 17, 21, 32, 32, 32, 20, 19, 34, 41, 54, 53, 58, 59, 75, 75, 74, 73, 73, 48, -1, -1, -1, -1, 50, 50, 47, 48, 36, 22, 23, 33, 32, 31, -1, -1, -1, -1, -1, -1, 25, 32, 32, 33, 32, 32, 27, 24, 42, 42, 41, 41, 42, 41, 40, 73, 48, -1, -1, -1, -1, -1, -1, -1, 47, 48, 45, 38, 39, -1, -1, 30, 29, 28, -1, -1, -1, -1, -1, -1, 30, 29, 28, 30, 29, 29, 28, 45, 44, 45, 44, 44, 45, 44, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 44, 43, 42, 41, 40, -1, -1, -1, -1, -1, -1, 42, 45, 43, -1, -1, -1, -1, 46, 47, 48, 41, -1, -1, 73, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 45, 44, 43, -1, 34, 35, 36, -1, -1, 45, 44, 43, -1, -1, -1, -1, 37, 38, 45, 44, -1, -1]}, {"type": 3, "obj": [[2, "1304_1", 451, 499, 22, 19, 2], [2, "1303_1", 445, 479, 34, 20, 0], [2, "1302_2", 472, 500, 40, 29, 2], [2, "1303_1", 926, 479, 34, 20, 0], [2, "1302_2", 914, 451, 40, 29, 0], [2, "1303_1", 430, 847, 34, 20, 0], [2, "1303_1", 103, 45, 34, 20, 0], [2, "1302_2", 350, 323, 40, 29, 2], [2, "1303_1", 711, 403, 34, 20, 0], [2, "1303_1", 598, 19, 34, 20, 2], [2, "1303_1", 379, 165, 34, 20, 2], [2, "269_4", 319, 641, 110, 58, 0], [2, "269_4", 278, 660, 110, 58, 0], [2, "269_4", 236, 680, 110, 58, 0], [2, "269_4", 193, 701, 110, 58, 0], [2, "269_4", 356, 676, 110, 58, 2], [2, "1303_1", 339, 707, 34, 20, 0], [2, "1302_2", 197, -11, 40, 29, 0], [2, "1303_1", 157, 19, 34, 20, 0], [2, "1303_1", 9, 59, 34, 20, 0], [2, "1302_2", 74, 105, 40, 29, 2], [2, "1303_1", 430, 273, 34, 20, 0], [2, "1303_1", 303, 456, 34, 20, 0], [2, "1303_1", 776, 491, 34, 20, 0], [2, "1303_1", 180, 564, 34, 20, 0], [2, "1303_1", 728, 686, 34, 20, 0], [2, "955_3", 710, 336, 20, 18, 0], [2, "955_3", 871, 543, 20, 18, 0], [2, "955_3", 303, 496, 20, 18, 0], [2, "955_3", 154, 173, 20, 18, 0]]}, {"type": 2, "data": [83, 83, 0, 0, 1, 0, 1, 2, 3, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 3, 0, 1, 2, 3, 2, 3, 0, 1, 83, 83, 83, 83, 83, 83, 0, 1, 1, 1, 3, 2, 3, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 3, 0, 1, 0, 1, 2, 3, 0, 1, 83, 83, 83, 83, 2, 3, 3, 3, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 3, 2, 3, 2, 3, 2, 3, 83, -1, 83, 83, 0, 1, 0, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 0, 1, 2, 3, 0, 1, 0, 1, 8, 83, 2, 3, 2, 3, 0, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 3, 0, 1, 2, 3, 2, 3, 8, 9, 0, 1, 0, 1, 2, 0, 1, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 0, 1, 1, 2, 0, 0, 1, 2, 3, 0, 1, 0, 1, 2, 3, 0, 1, 3, 3, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 5, 4, 8, 2, 3, 3, 0, 2, 2, 3, 0, 1, 2, 3, 2, 3, 0, 1, 2, 3, 2, 3, 0, 0, 1, 0, 1, 7, 8, 9, 8, 9, 8, 9, 8, 7, 8, 9, 8, 9, 7, 6, 0, 1, 5, 4, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 2, 3, 2, 0, 1, 2, 2, 2, 3, 2, 3, 0, 0, 9, 8, 9, 8, 9, 5, 7, 2, 3, 2, 3, 7, 6, 2, 3, 5, 4, 5, 2, 3, 4, 0, 0, 1, 3, 2, 3, 0, 1, 0, 2, 3, 5, 4, 4, 0, 1, 0, 1, 2, 0, 1, 9, 8, 1, 7, 6, 5, 0, 1, 2, 3, 4, 6, 5, 7, 6, 5, 4, 7, 6, 2, 2, 3, 4, 0, 1, 2, 3, 2, 3, 4, 4, 6, 6, 2, 0, 2, 3, 3, 2, 3, 0, 1, 0, 1, 0, 7, 2, 3, 7, 7, 6, 7, 6, 5, 5, 4, 0, 1, 4, 0, 1, 7, 6, 2, 3, 0, 1, 0, 7, 6, 6, 2, 3, 1, 2, 3, 0, 1, 3, 0, 2, 3, 2, 3, 2, 0, 0, 1, 1, 5, 4, 5, 4, 7, 7, 6, 2, 3, 6, 2, 3, 7, -1, 5, 4, 2, 3, 2, 3, 2, 3, 1, 2, 0, 1, 1, 5, 4, 5, 4, 0, 1, 3, 1, 0, 2, 2, 3, 3, 7, 6, 7, 5, 4, 0, 1, 5, 4, 0, 1, 6, 5, 4, 7, 6, 0, 1, 5, 4, 2, 0, 1, 0, 2, 3, 3, 7, 6, 7, 6, 2, 3, 2, 3, 2, 3, 0, 1, 1, 7, 6, 6, 7, 6, 2, 3, 0, 1, 2, 3, 4, 7, 6, 5, 4, 4, 5, 7, 0, 1, 2, 3, 2, 0, 1, 5, 4, 1, 3, 2, 3, 0, 1, 1, 0, 1, 0, 1, 0, 1, 5, 4, 7, 6, 5, 4, 2, 3, 4, 7, 6, 4, 4, 7, 5, 4, 7, 6, 2, 3, 4, 0, 5, 4, 3, 7, 6, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, 7, 6, 5, 4, 7, 6, 7, 7, 6, 4, 5, 4, 6, 5, 7, 6, 7, 5, 4, 7, 6, 2, 7, 6, 5, 4, 2, 3, 0, 1, 0, 0, 0, 1, 2, 3, 0, 1, 2, 3, 1, 0, 0, 1, 5, 4, 5, 4, 7, 6, 7, 6, 4, 7, 6, 5, 4, 7, 0, 1, 0, 1, 0, 0, 8, 9, 8, 9, 2, 3, 2, 2, 2, 3, 0, 1, 2, 3, 0, 1, 3, 2, 2, 3, 7, 6, 6, 6, 5, 4, 5, 5, 4, 5, 4, 7, 6, 5, 2, 3, 8, 9, 8, 9, 8, 5, 4, 5, 4, 9, 2, 0, 1, 0, 2, 3, 0, 2, 2, 3, 5, 4, 5, 4, 5, 0, 0, 0, 1, 0, 1, 7, 6, 5, 4, 5, 4, 7, 5, 4, 8, 9, 8, 5, 4, 7, 6, 7, 6, 0, 1, 0, 1, 2, 0, 1, 2, 3, 5, 4, 7, 6, 5, 4, 7, 2, 2, 2, 3, 0, 1, 0, 1, 7, 6, 7, 6, 5, 7, 6, 4, 5, 4, 7, 6, 5, 4, 7, 6, 2, 3, 2, 3, 2, 2, 3, 5, 4, 5, 4, 7, 5, 4, 6, 7, 6, 4, 6, 4, 2, 3, 0, 1, 4, 6, 5, 5, 4, 6, 7, 6, 7, 5, 4, 4, 4, 6, 2, 3, 0, 1, 5, 4, 5, 4, 4, 7, 6, 7, 5, 4, 7, 6, 7, 6, 7, 6, 6, 6, 7, 6, 2, 3, 6, 4, 0, 7, 6, 6, 7, 6, 5, 7, 6, 6, 6, 3, 2, 3, 2, 3, 7, 6, 7, 6, 6, 7, 5, 4, 7, 6, 5, 5, 4, 4, 5, 4, 5, 5, 7, 6, 5, 4, 0, 1, 2, 5, 4, 4, 5, 4, 5, 4, 5, 4, 0, 0, 1, 5, 4, 5, 4, 7, 6, 5, 5, 4, 7, 5, 4, 5, 4, 7, 6, 4, 7, 6, 7, 7, 6, 5, 4, 6, 2, 3, 2, 7, 6, 6, 7, 6, 5, 4, 7, 6, 2, 2, 3, 7, 6, 7, 6, 6, 7, 7, 5, 4, 5, 7, 6, 7, 6, 5, 7, 6, 5, 5, 4, 5, 4, 7, 6, 5, 2, 3, 2, 5, 5, 4, 5, 4, 7, 6, 4, 0, 1, 4, 6, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 7, 6, 6, 6, 7, 6, 7, 7, 7, 6, 5, 4, 7, 6, 7, 0, 1, 0, 5, 7, 6, 7, 6, 2, 7, 6, 0, 1, 6, 5, 4, 5, 4, 7, 6, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 7, 6, 7, 6, 4, 5, 4, 2, 3, 2, 7, 6, 5, 5, 4, 83, 83, 7, 2, 3, 6, 7, 5, 4, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 5, 4, 5, 4, 5, 4, 7, 6, 0, 1, 0, 0, 1, 7, 83, 83, 83, 5, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 5, 4, 5, 5, 4, 5, 4, 7, 6, 5, 4, 7, 6, 5, 4, 2, 3, 2, 2, 83, 83, 83, 83, 5, 7, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 83, 83, 83, 7, 7, 6, 7, 7, 6, 7, 6, 7, 6, 7, 5, 4, -1, 7, 6]}], "blocks": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1]}