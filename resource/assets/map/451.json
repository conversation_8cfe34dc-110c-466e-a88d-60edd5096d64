{"mW": 600, "mH": 600, "tW": 24, "tH": 24, "tiles": [["696", 0, 2, 2], ["1300", 0, 3, 2]], "layers": [{"type": 4, "obj": []}, {"type": 3, "obj": [[2, "219_1", 47, 257, 36, 30, 0], [2, "219_1", 86, 83, 36, 30, 2], [2, "220_1", 113, 84, 40, 29, 0], [2, "219_1", 549, 285, 36, 30, 2], [2, "219_1", 493, 517, 36, 30, 2], [2, "174_2", 216, 171, 68, 33, 0], [2, "174_2", 426, 91, 68, 33, 0], [2, "219_1", 496, 48, 36, 30, 2], [2, "219_1", 547, 68, 36, 30, 0], [2, "219_1", 279, 349, 36, 30, 2], [2, "219_1", 87, 466, 36, 30, 0], [2, "174_2", 360, 485, 68, 33, 0], [2, "174_2", 86, 383, 68, 33, 0], [2, "655", 292, 329, 22, 16, 0], [2, "657", 314, 318, 48, 27, 0], [2, "659", 362, 296, 30, 41, 0], [2, "661", 393, 283, 30, 41, 0], [2, "661", 393, 242, 30, 41, 1], [2, "659", 362, 229, 30, 41, 1], [2, "657", 314, 221, 48, 27, 1], [2, "655", 292, 221, 22, 16, 1], [2, "657", 248, 221, 48, 27, 3], [2, "659", 217, 229, 30, 41, 3], [2, "661", 188, 241, 30, 41, 3], [2, "661", 188, 282, 30, 41, 2], [2, "659", 218, 295, 30, 41, 2], [2, "657", 248, 317, 48, 27, 2], [2, "654", 210, 281, 96, 45, 0], [2, "654", 210, 236, 96, 45, 1], [2, "654", 306, 281, 96, 45, 2], [2, "654", 306, 236, 96, 45, 3]]}, {"type": 2, "data": [3, 0, 1, 4, 5, 6, 4, 5, 6, 4, 0, 1, 0, 1, 0, 1, 6, 4, 4, 5, 4, 5, 6, 0, 1, 7, 2, 3, 0, 1, 9, 7, 8, 9, 7, 8, 3, 2, 3, 7, 8, 9, 4, 7, 8, 7, 8, 0, 1, 3, 4, 5, 0, 2, 3, 4, 7, 8, 9, 5, 6, 4, 5, 6, 6, 8, 9, 7, 8, 4, 5, 0, 1, 3, 0, 7, 8, 2, 2, 3, 7, 8, 9, 5, 6, 9, 7, 8, 9, 4, 5, 6, 6, 4, 5, 6, 2, 3, 6, 2, 4, 5, 2, 3, 4, 5, 6, 7, 4, 5, 6, 5, 4, 5, 6, 8, 9, 9, 7, 8, 9, 7, 8, 4, 5, 7, 8, 9, 9, 7, 4, 5, 4, 5, 6, 9, 8, 7, 8, 9, 5, 4, 5, 6, 4, 5, 6, 5, 7, 8, 4, 5, 6, 8, 9, 7, 8, 4, 5, 6, 4, 4, 5, 8, 4, 5, 7, 8, 9, 7, 8, 9, 8, 4, 5, 7, 8, 9, 6, 5, 4, 5, 6, 4, 5, 6, 5, 6, 0, 7, 4, 5, 6, 4, 4, 4, 4, 4, 5, 6, 0, 1, 6, 9, 8, 7, 8, 9, 7, 8, 9, 8, 0, 1, 3, 7, 8, 9, 0, 7, 4, 5, 4, 5, 6, 2, 0, 1, 6, 7, 8, 4, 5, 4, 5, 0, 1, 2, 3, 1, 1, 0, 1, 0, 0, 7, 4, 7, 4, 5, 7, 2, 3, 6, 4, 5, 6, 8, 7, 0, 1, 3, 3, 3, 3, 1, 0, 1, 0, 1, 4, 7, 4, 7, 8, 4, 7, 4, 5, 6, 8, 9, 7, 0, 2, 3, 3, 0, 1, 0, 0, 0, 1, 2, 3, 4, 5, 4, 5, 6, 7, 8, 7, 8, 9, 4, 4, 5, 2, 3, 0, 0, 0, 0, 0, 2, 2, 3, 7, 8, 7, 8, 7, 8, 0, 4, 5, 4, 5, 6, 7, 7, 8, 0, 1, 2, 0, 0, 2, 2, 3, 8, 9, 7, 4, 4, 5, 6, 0, 2, 7, 4, 7, 8, 9, 0, 1, 2, 2, 3, 0, 2, 2, 3, 5, 6, 4, 5, 6, 7, 4, 5, 4, 2, 3, 4, 5, 6, 9, 2, 2, 3, 1, 1, 2, 2, 3, 9, 7, 8, 4, 5, 6, 4, 4, 4, 5, 7, 8, 9, 7, 4, 5, 0, 1, 0, 0, 1, 1, 1, 8, 9, 5, 6, 5, 7, 4, 5, 6, 7, 7, 8, 9, 9, 9, 5, 7, 8, 2, 3, 2, 2, 3, 4, 5, 6, 6, 4, 5, 6, 9, 7, 1, 1, 7, 4, 5, 4, 5, 6, 4, 0, 1, 2, 3, 7, 8, 9, 7, 8, 9, 9, 7, 4, 5, 6, 4, 5, 0, 0, 1, 8, 7, 8, 9, 7, 2, 3, 3, 9, 7, 8, 9, 5, 4, 5, 8, 4, 5, 6, 9, 7, 8, 2, 2, 3, 1, 4, 5, 6, 4, 5, 6, 6, 4, 5, 6, 4, 5, 6, 4, 5, 6, 5, 6, 4, 5, 6, 5, 0, 2, 3, 6, 6, 9, 7, 8, 9, 9, 7, 8, 4, 5, 6, 9, 7, 8, 9, 8, 9, 7, 8, 9, 8, 0, 1, 3, 9, 4, 5, 4, 5, 6, 4, 5, 6, 4, 5, 6, 6, 7, 8, 9, 4, 5, 6, 4, 5, 0, 2, 3, 4, 5, 7, 8, 1, 8, 9, 7, 4, 5, 6, 5, 4, 5, 6, 4, 5, 6, 4, 5, 0, 1, 2, 3, 6, 4, 5, 6, 0, 3, 1, 7, 8, 7, 8, 9, 8, 7, 8, 9, 7, 8, 9, 4, 5, 2, 3, 4, 5, 6, 7, 8, 0, 2]}]}