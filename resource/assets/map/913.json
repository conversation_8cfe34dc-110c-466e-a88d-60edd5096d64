{"mW": 1080, "mH": 912, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1233", 0, 3, 2], ["315", 0, 3, 3], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["154", 0, 2, 1], ["335", 0, 1, 1], ["335", 2, 1, 1]], "layers": [{"type": 3, "obj": [[2, "3975", 882, 292, 22, 11, 0], [2, "3974", 710, 302, 172, 86, 0], [2, "3974", 548, 382, 172, 86, 0], [2, "3974", 217, 291, 172, 86, 2], [2, "3974", 377, 372, 172, 86, 2], [2, "894", 353, 365, 24, 20, 0], [2, "894", 377, 377, 24, 20, 0], [2, "894", 392, 385, 24, 20, 0], [2, "942", 607, 402, 68, 61, 2], [2, "942", 738, 335, 68, 61, 2], [2, "942", 457, 410, 68, 61, 0], [2, "942", 238, 300, 68, 61, 0], [2, "3974", 209, 210, 172, 86, 0], [2, "3974", 371, 130, 172, 86, 0], [2, "3974", 591, 107, 172, 86, 2], [2, "3974", 752, 187, 172, 86, 2], [2, "3975", 532, 125, 22, 11, 0], [2, "3975", 543, 120, 22, 11, 0], [2, "3975", 563, 110, 22, 11, 0], [2, "3975", 553, 115, 22, 11, 0], [2, "3975", 574, 105, 22, 11, 0], [2, "3975", 580, 102, 22, 11, 0], [2, "3975", 913, 267, 22, 11, 0], [2, "3975", 923, 272, 22, 11, 0], [2, "3975", 538, 452, 22, 11, 0], [2, "3975", 871, 297, 22, 11, 0], [2, "3975", 902, 282, 22, 11, 0], [2, "3975", 892, 287, 22, 11, 0], [2, "3975", 912, 276, 22, 11, 0], [2, "942", 845, 280, 68, 61, 2], [2, "3365", 59, 878, 66, 51, 2], [2, "3365", -4, 850, 66, 51, 2], [2, "3365", 1021, 851, 66, 51, 0], [2, "3365", 958, 879, 66, 51, 0], [2, "1232", 451, 821, 100, 158, 0], [2, "1232", 380, 854, 100, 158, 0], [2, "1232", 903, 858, 100, 158, 0]]}, {"type": 4, "obj": [[2, "3095", 211, 23, 62, 42, 0], [2, "14", 192, 40, 32, 30, 0], [2, "3095", 250, 37, 62, 42, 0], [2, "3095", 979, 38, 62, 42, 0], [2, "3095", 862, 39, 62, 42, 0], [2, "3095", 590, 41, 62, 42, 0], [4, 5, 496, 96, 0, 4005], [2, "3559", 966, 54, 52, 43, 0], [2, "3095", 699, 68, 62, 42, 0], [2, "3571", 763, 81, 34, 34, 0], [2, "3095", 950, 74, 62, 42, 0], [2, "3365", 292, 72, 66, 51, 2], [4, 4, 964, 133, 0, 4005], [2, "3585", 784, 86, 56, 53, 0], [2, "3365", 336, 94, 66, 51, 2], [2, "3095", 465, 103, 62, 42, 0], [2, "3095", 129, 108, 62, 42, 0], [2, "3384", 233, 95, 52, 56, 0], [2, "3571", 828, 119, 34, 34, 0], [2, "3095", 862, 113, 62, 42, 0], [2, "3095", 953, 122, 62, 42, 0], [2, "1232", 360, 8, 100, 158, 0], [2, "1434", 300, 126, 34, 43, 0], [2, "3365", 128, 119, 66, 51, 0], [2, "3095", 372, 132, 62, 42, 0], [2, "3568", 910, 142, 48, 38, 0], [2, "1232", 849, 24, 100, 158, 0], [2, "1232", 36, 25, 100, 158, 0], [2, "1434", 182, 152, 34, 43, 0], [2, "3095", 943, 153, 62, 42, 0], [2, "3095", 57, 157, 62, 42, 0], [2, "3095", 900, 157, 62, 42, 0], [2, "3095", 859, 160, 62, 42, 0], [2, "3095", 902, 177, 62, 42, 0], [2, "3095", 21, 180, 62, 42, 0], [4, 6, 162, 226, 0, 4011], [2, "3557", 503, 192, 22, 38, 0], [2, "3546", 556, 179, 44, 55, 0], [2, "3095", 232, 194, 62, 42, 0], [4, 10, 976, 239, 0, 4022], [2, "3527", 491, 217, 20, 28, 0], [2, "1232", 965, 87, 100, 158, 0], [2, "3538", 582, 210, 38, 39, 0], [4, 7, 223, 254, 0, 4018], [2, "3095", 998, 214, 62, 42, 0], [2, "3095", 120, 234, 62, 42, 0], [2, "3549", 395, 262, 26, 28, 2], [2, "3095", 158, 251, 62, 42, 0], [2, "3557", 376, 257, 22, 38, 0], [2, "3596", 425, 226, 96, 93, 2], [2, "3095", 68, 293, 62, 42, 0], [2, "3552", 610, 277, 40, 60, 0], [2, "3535", 326, 288, 44, 61, 2], [4, 9, 454, 367, 0, 4023], [2, "3576", 364, 340, 36, 36, 0], [2, "3581", 674, 307, 82, 71, 0], [2, "3582", 541, 310, 86, 76, 2], [2, "3552", 508, 332, 40, 60, 0], [2, "3559", 933, 358, 52, 43, 0], [2, "3588", 608, 360, 72, 56, 2], [2, "3095", 932, 376, 62, 42, 0], [2, "3536", 814, 394, 40, 48, 0], [2, "3095", 142, 409, 62, 42, 0], [2, "1232", 954, 299, 100, 158, 0], [2, "3568", 960, 420, 48, 38, 0], [4, 3, 105, 461, 0, 4005], [2, "3536", 527, 413, 40, 48, 0], [2, "673", 845, 398, 80, 63, 0], [2, "3568", 268, 427, 48, 38, 0], [2, "3485", 848, 416, 58, 54, 0], [2, "3095", 995, 429, 62, 42, 0], [2, "3095", 952, 438, 62, 42, 0], [2, "673", 912, 433, 80, 63, 0], [2, "3536", 699, 450, 40, 48, 0], [2, "3488", 906, 442, 62, 60, 0], [2, "3067", 991, 412, 18, 93, 0], [2, "3069", 171, 449, 98, 74, 2], [2, "3364", 775, 458, 94, 70, 0], [2, "3359", 675, 486, 32, 43, 0], [2, "3067", 140, 442, 18, 93, 0], [2, "3568", 4, 502, 48, 38, 0], [2, "3359", 699, 499, 32, 43, 0], [2, "3568", 414, 508, 48, 38, 0], [2, "3095", 11, 512, 62, 42, 0], [2, "3559", 602, 524, 52, 43, 0], [2, "673", 644, 506, 80, 63, 0], [2, "3095", 469, 560, 62, 42, 0], [2, "3095", 469, 560, 62, 42, 0], [2, "3069", 277, 529, 98, 74, 2], [2, "3067", 784, 522, 18, 93, 0], [2, "3095", 568, 574, 62, 42, 0], [2, "3067", 253, 529, 18, 93, 0], [2, "3107", 75, 525, 90, 110, 0], [2, "3357", 268, 591, 40, 53, 0], [2, "3095", 986, 607, 62, 42, 0], [4, 11, 567, 659, 0, 4019], [4, 2, 794, 675, 0, 4005], [2, "3095", 832, 639, 62, 42, 0], [2, "1232", 344, 556, 100, 158, 0], [2, "3067", 58, 627, 18, 93, 0], [2, "3095", 435, 680, 62, 42, 0], [2, "3095", 777, 690, 62, 42, 0], [2, "3095", 372, 691, 62, 42, 0], [2, "3384", 721, 679, 52, 56, 0], [2, "3559", 411, 696, 52, 43, 0], [2, "3360", 791, 688, 58, 57, 2], [2, "3360", 371, 690, 58, 57, 0], [2, "3095", 726, 714, 62, 42, 0], [2, "3095", 399, 717, 62, 42, 0], [2, "1232", 919, 605, 100, 158, 0], [2, "3360", 755, 707, 58, 57, 2], [2, "3095", 579, 730, 62, 42, 0], [2, "3502", 641, 687, 74, 111, 0], [2, "3095", 46, 769, 62, 42, 0], [4, 1, 312, 835, 0, 4005], [2, "3095", 841, 798, 62, 42, 0], [2, "3559", 861, 804, 52, 43, 0], [4, 8, 725, 853, 0, 4021], [2, "3095", 140, 818, 62, 42, 0], [2, "3095", 140, 818, 62, 42, 0], [2, "3095", 874, 820, 62, 42, 0], [2, "1232", 111, 709, 100, 158, 0], [2, "3568", 110, 832, 48, 38, 0], [2, "3559", 423, 828, 52, 43, 0], [2, "3360", 175, 817, 58, 57, 2], [2, "3095", 975, 836, 62, 42, 0], [2, "3095", 112, 841, 62, 42, 0], [2, "3095", 442, 844, 62, 42, 0], [2, "3095", 378, 854, 62, 42, 0], [2, "3095", 279, 859, 62, 42, 0], [2, "3095", 279, 859, 62, 42, 0], [2, "3568", 362, 871, 48, 38, 0]]}, {"type": 3, "obj": [[2, "3603", 663, 170, 50, 54, 0], [2, "3492", 814, 237, 58, 86, 0], [2, "1398", 612, 438, 28, 66, 2], [2, "1398", 650, 419, 28, 66, 2], [2, "1398", 849, 320, 28, 66, 2], [2, "1398", 887, 301, 28, 66, 2], [2, "1398", 741, 373, 28, 66, 2], [2, "1398", 779, 355, 28, 66, 2], [2, "1398", 459, 430, 28, 66, 0], [2, "1398", 496, 450, 28, 66, 0], [2, "1398", 238, 319, 28, 66, 0], [2, "1398", 277, 339, 28, 66, 0], [2, "1207", 549, 492, 22, 81, 0], [2, "1207", 549, 455, 22, 81, 0], [2, "1207", 418, 428, 22, 81, 0], [2, "1207", 418, 392, 22, 81, 0], [2, "1207", 939, 298, 22, 81, 0], [2, "1207", 939, 274, 22, 81, 0], [2, "1207", 188, 311, 22, 81, 0], [2, "1207", 188, 289, 22, 81, 0], [2, "1207", 323, 380, 22, 81, 0], [2, "1207", 323, 342, 22, 81, 0], [2, "3381", 612, 468, 70, 57, 0], [2, "3381", 850, 351, 70, 57, 0], [2, "3381", 451, 483, 70, 57, 2], [2, "3587", 365, 273, 64, 50, 2], [2, "3587", 477, 219, 64, 50, 2], [2, "3560", 371, 229, 28, 31, 2], [2, "3560", 483, 173, 28, 31, 2], [2, "3602", 441, 190, 22, 29, 2], [2, "3601", 410, 211, 26, 39, 2], [2, "3604", 441, 211, 26, 26, 2], [2, "3492", 290, 245, 58, 86, 2], [2, "3480", 581, 145, 64, 94, 0], [2, "3492", 522, 128, 58, 86, 2], [2, "3540", 796, 267, 34, 64, 0], [2, "3587", 633, 214, 64, 50, 0], [2, "3533", 646, 180, 36, 57, 0], [2, "3536", 673, 223, 40, 48, 0], [2, "3482", 700, 208, 68, 93, 0], [2, "3532", 748, 248, 48, 70, 0], [2, "3560", 776, 229, 28, 31, 0], [2, "3571", 841, 509, 34, 34, 0], [2, "3571", 760, 508, 34, 34, 0], [2, "3571", 846, 461, 34, 34, 0], [2, "3571", 766, 461, 34, 34, 0], [2, "14", 192, 53, 32, 30, 0], [2, "14", 173, 63, 32, 30, 0], [2, "3568", 764, 407, 48, 38, 0], [2, "3381", 741, 406, 70, 57, 0], [2, "3381", 230, 370, 70, 57, 2], [2, "3558", 453, 483, 46, 38, 0], [2, "3558", 235, 364, 46, 38, 0], [2, "3558", 631, 463, 46, 38, 2], [2, "3558", 869, 345, 46, 38, 2]]}, {"type": 3, "obj": [[2, "3367", 204, 271, 44, 81, 2], [2, "3367", 204, 309, 44, 81, 2], [2, "3625", 205, 357, 26, 34, 2], [2, "3625", 222, 348, 26, 34, 2], [2, "3372", 818, 325, 44, 81, 0], [2, "3367", 276, 236, 44, 81, 2], [2, "3367", 276, 274, 44, 81, 2], [2, "3625", 277, 322, 26, 34, 2], [2, "3625", 294, 313, 26, 34, 2], [2, "3367", 231, 258, 44, 81, 2], [2, "3367", 231, 296, 44, 81, 2], [2, "3625", 232, 344, 26, 34, 2], [2, "3625", 249, 335, 26, 34, 2], [2, "3372", 783, 343, 44, 81, 0], [2, "3372", 738, 366, 44, 81, 0], [2, "3372", 693, 388, 44, 81, 0], [2, "3372", 648, 411, 44, 81, 0], [2, "3372", 788, 340, 44, 81, 0], [2, "3372", 603, 434, 44, 81, 0], [2, "3372", 558, 457, 44, 81, 0], [2, "3371", 305, 349, 44, 81, 0], [2, "3371", 208, 300, 44, 81, 0], [2, "3371", 252, 322, 44, 81, 0], [2, "3371", 297, 345, 44, 81, 0], [2, "3371", 513, 456, 44, 81, 0], [2, "3371", 506, 452, 44, 81, 0], [2, "3371", 461, 429, 44, 81, 0], [2, "3371", 417, 407, 44, 81, 0], [2, "3146", 668, 385, 54, 42, 2], [2, "1208", 679, 449, 52, 56, 2], [2, "1208", 668, 454, 52, 56, 2], [2, "3146", 723, 359, 54, 42, 2], [2, "1208", 734, 423, 52, 56, 2], [2, "1208", 723, 428, 52, 56, 2], [2, "3146", 613, 413, 54, 42, 2], [2, "1208", 624, 477, 52, 56, 2], [2, "1208", 613, 482, 52, 56, 2], [2, "895", 413, 394, 8, 31, 0], [2, "895", 413, 422, 8, 31, 0], [2, "895", 413, 452, 8, 31, 0], [2, "895", 413, 438, 8, 31, 0], [2, "1208", 201, 356, 52, 56, 0], [2, "1208", 245, 378, 52, 56, 0], [2, "1208", 293, 402, 52, 56, 0], [2, "3146", 207, 290, 54, 42, 0], [2, "3146", 262, 318, 54, 42, 0], [2, "3146", 291, 333, 54, 42, 0], [2, "1208", 414, 464, 52, 56, 0], [2, "1208", 458, 486, 52, 56, 0], [2, "1208", 506, 510, 52, 56, 0], [2, "3146", 420, 398, 54, 42, 0], [2, "3146", 475, 426, 54, 42, 0], [2, "3146", 504, 441, 54, 42, 0], [2, "895", 345, 360, 8, 31, 0], [2, "895", 345, 388, 8, 31, 0], [2, "895", 345, 404, 8, 31, 0], [2, "3146", 558, 441, 54, 42, 2], [2, "1208", 569, 505, 52, 56, 2], [2, "1208", 558, 510, 52, 56, 2], [2, "3146", 778, 331, 54, 42, 2], [2, "1208", 789, 395, 52, 56, 2], [2, "1208", 778, 400, 52, 56, 2], [2, "3367", 366, 191, 44, 81, 2], [2, "3367", 366, 229, 44, 81, 2], [2, "3625", 367, 277, 26, 34, 2], [2, "3625", 384, 268, 26, 34, 2], [2, "3367", 321, 213, 44, 81, 2], [2, "3367", 321, 251, 44, 81, 2], [2, "3625", 322, 299, 26, 34, 2], [2, "3625", 339, 290, 26, 34, 2], [2, "3367", 456, 147, 44, 81, 2], [2, "3367", 456, 185, 44, 81, 2], [2, "3625", 457, 233, 26, 34, 2], [2, "3625", 474, 224, 26, 34, 2], [2, "3367", 411, 169, 44, 81, 2], [2, "3367", 411, 207, 44, 81, 2], [2, "3625", 412, 255, 26, 34, 2], [2, "3625", 429, 246, 26, 34, 2], [2, "3367", 591, 102, 44, 81, 0], [2, "3367", 591, 141, 44, 81, 0], [2, "3625", 591, 180, 26, 34, 0], [2, "3625", 609, 189, 26, 34, 0], [2, "3367", 546, 102, 44, 81, 2], [2, "3367", 546, 140, 44, 81, 2], [2, "3625", 547, 188, 26, 34, 2], [2, "3625", 564, 179, 26, 34, 2], [2, "3367", 501, 124, 44, 81, 2], [2, "3367", 501, 162, 44, 81, 2], [2, "3625", 502, 210, 26, 34, 2], [2, "3625", 519, 201, 26, 34, 2], [2, "3367", 636, 124, 44, 81, 0], [2, "3367", 636, 163, 44, 81, 0], [2, "3625", 636, 202, 26, 34, 0], [2, "3625", 654, 211, 26, 34, 0], [2, "3367", 681, 147, 44, 81, 0], [2, "3367", 681, 186, 44, 81, 0], [2, "3625", 681, 225, 26, 34, 0], [2, "3625", 699, 234, 26, 34, 0], [2, "3367", 726, 169, 44, 81, 0], [2, "3367", 726, 208, 44, 81, 0], [2, "3625", 726, 247, 26, 34, 0], [2, "3625", 744, 256, 26, 34, 0], [2, "3367", 771, 192, 44, 81, 0], [2, "3367", 771, 231, 44, 81, 0], [2, "3625", 771, 270, 26, 34, 0], [2, "3625", 789, 279, 26, 34, 0], [2, "3367", 816, 214, 44, 81, 0], [2, "3367", 816, 253, 44, 81, 0], [2, "3625", 816, 292, 26, 34, 0], [2, "3625", 834, 301, 26, 34, 0], [2, "3367", 861, 237, 44, 81, 0], [2, "3367", 861, 276, 44, 81, 0], [2, "3625", 861, 315, 26, 34, 0], [2, "3625", 879, 324, 26, 34, 0], [2, "3367", 897, 255, 44, 81, 0], [2, "3367", 897, 294, 44, 81, 0], [2, "3625", 897, 333, 26, 34, 0], [2, "3625", 915, 342, 26, 34, 0], [2, "3372", 893, 288, 44, 81, 0], [2, "3372", 848, 311, 44, 81, 0], [2, "3372", 898, 285, 44, 81, 0], [2, "3146", 833, 304, 54, 42, 2], [2, "1208", 844, 368, 52, 56, 2], [2, "1208", 833, 373, 52, 56, 2], [2, "3146", 888, 276, 54, 42, 2], [2, "1208", 899, 340, 52, 56, 2], [2, "1208", 888, 345, 52, 56, 2], [2, "895", 345, 418, 8, 31, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 4, 5, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 7, 1, 2, -1, -1, -1, -1, -1, 8, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 16, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 22, 22, 4, 5, -1, -1, -1, -1, -1, 15, 22, 16, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 22, 22, 22, 22, 1, 2, -1, -1, -1, 20, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 22, 22, 4, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 30, 29, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 22, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, 31, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 22, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, 22, 24, 25, 26, 8, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 24, 25, 26, -1, -1, -1, 23, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, 20, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, -1, 22, 22, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, -1, -1, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, 16, 16, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 21, -1, -1, -1, -1, -1, -1, 8, 7, 7, 7, 7, 7, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, 15, 10, 16, 16, 16, 16, 17, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 22, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 1, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 10, 10, 4, 16, 21, -1, -1, -1, -1, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 13, 13, 13, 13, 14, -1, -1, -1, -1, 8, 7, 11, 10, -1, -1, -1, -1, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 10, 22, 22, 22, 22, -1, -1, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "253", 932, 508, 92, 53, 0], [2, "253", 764, 581, 92, 53, 0], [2, "253", 647, 534, 92, 53, 2], [2, "253", 679, 581, 92, 53, 0], [2, "3408", 722, 563, 42, 26, 2], [2, "3408", 751, 577, 42, 26, 2], [2, "253", 878, 550, 92, 53, 0], [2, "3408", 905, 541, 42, 26, 0], [2, "3408", 877, 555, 42, 26, 0], [2, "163", 184, 381, 60, 33, 2], [2, "163", 232, 405, 60, 33, 2], [2, "163", 281, 429, 60, 33, 2], [2, "163", 329, 453, 60, 33, 2], [2, "163", 378, 477, 60, 33, 2], [2, "163", 426, 501, 60, 33, 2], [2, "163", 474, 524, 60, 33, 2], [2, "163", 514, 544, 60, 33, 2], [2, "163", 174, 359, 60, 33, 0], [2, "163", 910, 365, 60, 33, 0], [2, "163", 862, 389, 60, 33, 0], [2, "163", 814, 413, 60, 33, 0], [2, "163", 766, 437, 60, 33, 0], [2, "163", 718, 460, 60, 33, 0], [2, "163", 670, 484, 60, 33, 0], [2, "163", 621, 508, 60, 33, 0], [2, "163", 573, 532, 60, 33, 0], [2, "163", 547, 545, 60, 33, 0], [2, "163", 912, 347, 60, 33, 2], [2, "3145", 323, 442, 108, 72, 0], [2, "3608", 433, 275, 96, 53, 0], [2, "3609", 348, 421, 112, 59, 0], [2, "3608", 596, 366, 96, 53, 0], [2, "3607", 604, 249, 62, 33, 0], [2, "3736", 805, 432, 76, 41, 0], [2, "3736", 757, 456, 76, 41, 0], [2, "3736", 836, 447, 76, 41, 0], [2, "3736", 788, 471, 76, 41, 0], [2, "3736", 867, 461, 76, 41, 0], [2, "3736", 819, 485, 76, 41, 0], [2, "3736", 898, 476, 76, 41, 0], [2, "3736", 850, 500, 76, 41, 0], [2, "3736", 659, 504, 76, 41, 0], [2, "3736", 738, 495, 76, 41, 0], [2, "3736", 690, 519, 76, 41, 0], [2, "3736", 769, 509, 76, 41, 0], [2, "3736", 721, 533, 76, 41, 0], [2, "3736", 800, 524, 76, 41, 0], [2, "3736", 752, 548, 76, 41, 0], [2, "3736", 707, 480, 76, 41, 0], [2, "1430", 259, 97, 66, 35, 0], [2, "1430", 227, 113, 66, 35, 0], [2, "1430", 197, 129, 66, 35, 0], [2, "1430", 294, 113, 66, 35, 0], [2, "1430", 262, 129, 66, 35, 0], [2, "1430", 232, 145, 66, 35, 0], [2, "1430", 325, 130, 66, 35, 0], [2, "1430", 293, 146, 66, 35, 0], [2, "1430", 263, 162, 66, 35, 0], [2, "1430", 164, 145, 66, 35, 0], [2, "1430", 199, 161, 66, 35, 0], [2, "1430", 230, 178, 66, 35, 0], [2, "1430", 130, 160, 66, 35, 0], [2, "1430", 165, 176, 66, 35, 0], [2, "1430", 196, 193, 66, 35, 0], [2, "1456", 250, 105, 24, 32, 0], [2, "3736", 923, 494, 76, 41, 0], [2, "3736", 875, 518, 76, 41, 0], [2, "3736", 825, 542, 76, 41, 0], [2, "3736", 779, 563, 76, 41, 0], [2, "163", 265, 451, 60, 33, 0], [2, "163", 217, 475, 60, 33, 0], [2, "163", 209, 482, 60, 33, 0], [2, "163", 161, 506, 60, 33, 0], [2, "163", 209, 482, 60, 33, 0], [2, "163", 161, 506, 60, 33, 0], [2, "163", 115, 529, 60, 33, 0], [2, "163", 67, 553, 60, 33, 0], [2, "163", 20, 577, 60, 33, 0], [2, "163", -28, 601, 60, 33, 0], [2, "163", 384, 511, 60, 33, 0], [2, "163", 336, 535, 60, 33, 0], [2, "163", 338, 536, 60, 33, 0], [2, "163", 290, 560, 60, 33, 0], [2, "163", 338, 536, 60, 33, 0], [2, "163", 290, 560, 60, 33, 0], [2, "163", 242, 584, 60, 33, 0], [2, "163", 194, 608, 60, 33, 0], [2, "163", 146, 632, 60, 33, 0], [2, "163", 98, 656, 60, 33, 0], [2, "163", 48, 680, 60, 33, 0], [2, "163", 0, 704, 60, 33, 0], [2, "163", -47, 728, 60, 33, 0], [2, "688", -7, 728, 46, 24, 0], [2, "688", 29, 709, 46, 24, 0], [2, "688", 67, 690, 46, 24, 0], [2, "688", 180, 634, 46, 24, 0], [2, "688", 142, 653, 46, 24, 0], [2, "688", 106, 672, 46, 24, 0], [2, "688", 293, 577, 46, 24, 0], [2, "688", 255, 596, 46, 24, 0], [2, "688", 219, 615, 46, 24, 0], [2, "688", 388, 529, 46, 24, 0], [2, "688", 350, 548, 46, 24, 0], [2, "688", 314, 567, 46, 24, 0], [2, "688", 407, 519, 46, 24, 0], [2, "688", 369, 538, 46, 24, 0], [2, "688", 333, 557, 46, 24, 0], [2, "688", 263, 448, 46, 24, 0], [2, "688", 227, 467, 46, 24, 0], [2, "688", 188, 486, 46, 24, 0], [2, "688", 150, 506, 46, 24, 0], [2, "688", 114, 524, 46, 24, 0], [2, "688", 75, 542, 46, 24, 0], [2, "688", 37, 561, 46, 24, 0], [2, "688", 111, 525, 46, 24, 0], [2, "688", 75, 544, 46, 24, 0], [2, "688", 36, 563, 46, 24, 0], [2, "688", -2, 582, 46, 24, 0], [2, "688", -38, 601, 46, 24, 0], [2, "253", 392, 543, 92, 53, 2], [2, "253", 405, 579, 92, 53, 2], [2, "253", 470, 597, 92, 53, 2], [2, "253", 479, 635, 92, 53, 2], [2, "253", 556, 641, 92, 53, 2], [2, "253", 599, 674, 92, 53, 2], [2, "253", 646, 659, 92, 53, 2], [2, "253", 724, 653, 92, 53, 2], [2, "253", 175, 649, 92, 53, 2], [2, "253", 257, 665, 92, 53, 2], [2, "253", 323, 670, 92, 53, 0], [2, "253", 413, 662, 92, 53, 0], [2, "253", 790, 632, 92, 53, 0], [2, "253", 790, 632, 92, 53, 0], [2, "253", 809, 587, 92, 53, 0], [2, "253", 883, 584, 92, 53, 0], [2, "253", 955, 522, 92, 53, 0], [2, "253", 991, 559, 92, 53, 2], [2, "253", 35, 488, 92, 53, 2], [2, "253", 21, 451, 92, 53, 2], [2, "253", 53, 412, 92, 53, 0], [2, "253", 58, 373, 92, 53, 0], [2, "253", 33, 255, 92, 53, 0], [2, "253", 918, 208, 92, 53, 0], [2, "253", 948, 245, 92, 53, 2], [2, "253", 1026, 361, 92, 53, 2], [2, "253", 494, 29, 92, 53, 2], [2, "253", 665, 50, 92, 53, 0], [2, "253", 44, 49, 92, 53, 0], [2, "1457", 817, 682, 22, 30, 0], [2, "1457", 761, 718, 22, 30, 0], [2, "1457", 371, 691, 22, 30, 0], [2, "1457", 480, 565, 22, 30, 0], [2, "1457", 1032, 425, 22, 30, 0], [2, "1457", 981, 292, 22, 30, 0], [2, "1457", 979, 114, 22, 30, 0], [2, "1457", 646, 47, 22, 30, 0], [2, "1457", 513, 102, 22, 30, 0], [2, "1457", 257, 29, 22, 30, 0], [2, "1457", 162, 240, 22, 30, 0], [2, "1457", 53, 176, 22, 30, 0], [2, "1457", 155, 405, 22, 30, 0], [2, "1456", 386, 562, 24, 32, 0], [2, "1456", 39, 504, 24, 32, 0], [2, "1456", 768, 693, 24, 32, 0], [2, "1456", 1010, 416, 24, 32, 0], [2, "1456", 950, 149, 24, 32, 0], [2, "1456", 393, 96, 24, 32, 0], [2, "1456", 948, 726, 24, 32, 0], [2, "253", 562, 33, 92, 53, 0], [2, "253", 625, 65, 92, 53, 2], [2, "253", 808, 172, 92, 53, 2], [2, "253", 50, 207, 92, 53, 0], [2, "253", 615, 620, 92, 53, 0], [2, "253", 697, 109, 92, 53, 2], [2, "253", 734, 151, 92, 53, 2], [2, "22", 402, 797, 62, 38, 0], [2, "21", 461, 784, 28, 24, 0], [2, "21", 371, 826, 28, 24, 0], [2, "41", 436, 798, 12, 11, 0], [2, "41", 417, 802, 12, 11, 2]]}, {"type": 2, "data": [45, 46, 52, 50, 51, 65, 66, 76, 48, 48, 48, 49, 45, 46, 51, 52, 61, 60, 64, 48, 48, 48, 63, 48, 48, 48, 48, 48, 49, 59, 50, 51, 68, 70, 66, 76, 48, 63, 63, 63, 63, 63, 48, 48, 48, 48, 49, 59, 53, 54, 55, 51, 68, 48, 48, 48, 48, 48, 49, 60, 60, 64, 63, 48, 48, 48, 48, 63, 63, 48, 48, 48, 48, 63, 62, 53, 54, 73, 67, 50, 73, 72, 72, 72, 76, 63, 63, 48, 48, 48, 48, 48, 62, 56, 57, 58, 54, 68, 48, 63, 63, 63, 63, 63, 48, 48, 48, 48, 48, 48, 48, 48, 63, 63, 48, 63, 48, 48, 63, 49, 56, 57, 58, 51, 53, 54, 55, 50, 51, 68, 48, 63, 63, 63, 48, 48, 48, 62, 48, 48, 56, 61, 64, 48, 63, 63, 48, 63, 63, 48, 63, 63, 63, 48, 48, 48, 48, 48, 63, 63, 63, 48, 63, 63, 48, 60, 59, 61, 60, 45, 46, 46, 45, 46, 68, 48, 48, 63, 48, 48, 48, 63, 49, 60, 60, 60, 64, 48, 48, 63, 63, 63, 48, 48, 63, 48, 63, 63, 63, 63, 48, 48, 48, 48, 63, 63, 48, 63, 48, 48, 48, 49, 64, 63, 48, 49, 49, 48, 49, 64, 48, 48, 48, 48, 48, 48, 63, 63, 63, 48, 63, 63, 63, 48, 63, 63, 63, 63, 63, 48, 63, 48, 63, 63, 63, 48, 48, 48, 63, 63, 63, 48, 63, 63, 63, 63, 63, 48, 48, 48, 48, 69, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 63, 48, 63, 63, 48, 63, 48, 48, 48, 48, 48, 48, 48, 63, 63, 63, 48, 48, 48, 48, 63, 63, 63, 48, 48, 48, 63, 63, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 63, 48, 63, 63, 48, 63, 63, 63, 63, 48, 48, 48, 48, 48, 48, 48, 48, 63, 63, 54, 48, 48, 48, 75, 48, 48, 63, 48, 63, 48, 48, 48, 63, 48, 48, 48, 48, 48, 48, 48, 63, 48, 48, 48, 48, 63, 63, 63, 48, 48, 48, 48, 63, 48, 48, 48, 63, 48, 48, 48, 48, 63, 48, 49, 45, 48, 48, 48, 79, 80, 79, 48, 48, 48, 48, 48, 48, 63, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 63, 63, 48, 48, 63, 48, 48, 48, 48, 48, 48, 63, 48, 63, 63, 63, 48, 48, 48, 48, 49, 79, 80, 79, 80, 79, 80, 79, 48, 48, 48, 63, -1, 48, 48, 48, 48, 63, 48, 48, 48, 48, 48, 48, 48, 63, 70, 66, 66, 76, 48, 48, 48, 48, 48, 48, 48, 63, 48, 63, 63, 48, 75, 48, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 69, 48, -1, -1, -1, 63, 63, 63, 63, 48, 48, 48, 48, 48, 48, 63, 62, 54, 55, 47, 63, 48, 48, 48, 63, 48, 63, 63, 63, 63, 63, 63, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 48, 48, -1, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 66, 67, 57, 61, 64, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 63, 63, 63, 63, 63, 48, 48, 48, 48, 48, 53, 54, 55, 68, 48, 48, 48, 63, 63, 63, 63, 63, 63, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 46, 57, 61, 64, 63, 48, 63, 48, 63, 48, 63, 63, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 49, 60, 64, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 79, 79, 80, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 48, 63, 63, 48, 63, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 48, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 63, 63, 63, 63, 80, 79, 80, 79, 80, 63, 80, 79, 80, 79, 80, 79, 80, 48, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 63, 63, 63, 63, 63, 63, 48, 48, 48, 48, 63, 63, 77, 78, 77, 78, 77, 78, 80, 63, 63, 63, 80, 79, 80, 63, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 63, 63, 63, 63, 63, 48, 48, 48, 63, 63, 77, 78, 78, 77, 77, 78, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 63, 63, 63, 48, 48, 48, 77, 78, 78, 77, 77, 77, 78, 77, 78, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 50, 51, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 48, 63, 63, 63, 63, 63, 48, 77, 78, 77, 78, 77, 78, 77, 77, 77, 78, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 70, 53, 50, 51, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 63, 63, 77, 77, 78, 77, 78, 78, 78, 77, 77, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 62, 56, 53, 54, 55, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 77, 78, 77, 78, 78, 77, 78, 78, 77, 78, 77, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 49, 46, 56, 57, 61, 60, 63, 63, 63, 63, 63, 48, 63, 63, 48, 63, 48, 77, 78, 78, 77, 78, 77, 78, 77, 77, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 49, 60, 60, 64, 63, 63, 48, 63, 48, 63, 63, 48, 48, 63, 48, 48, 77, 78, 77, 78, 77, 77, 78, 77, 77, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 70, 76, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 48, 63, 63, 63, 63, 48, 48, 48, 48, 48, 77, 78, 77, 78, 77, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 70, 67, 54, 76, 63, 63, 63, 63, 63, 63, 63, 48, 48, 48, 63, 63, 63, 63, 48, 48, 48, 48, 48, 77, 78, 77, 77, 78, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63, 62, 54, 54, 68, 63, 63, 63, 63, 63, 48, 48, 48, 48, 48, 63, 48, 70, 66, 66, 66, 66, 76, 48, 77, 78, 77, 63, 63, 63, 63, 63, 63, 69, 70, 66, 66, 66, 76, 75, 63, 63, 63, 63, 69, 70, 67, 44, 45, 64, 63, 63, 63, 48, 48, 63, 48, 63, 63, 63, 70, 66, 67, 50, 51, 52, 50, 73, 76, 77, 63, 63, 63, 63, 63, 63, 63, 70, 66, 67, 50, 51, 52, 73, 72, 76, 75, 69, 70, 66, 67, 50, 47, 48, 48, 48, 48, 48, 63, 63, 48, 48, 63, 70, 66, 67, 50, 51, 44, 45, 46, 53, 54, 68, 63, 63, 63, 63, 63, 48, 48, 70, 67, 50, 51, 52, 54, 55, 50, 51, 73, 72, 66, 67, 51, 52, 53, 47, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 62, 54, 54, 50, 51, 68, 48, 62, 51, 52, 68, 48, 48, 48, 48, 48, 48, 48, 62, 51, 53, 54, 55, 50, 51, 52, 61, 60, 60, 45, 45, 45, 46, 53, 65, 66, 76, 75, 48, 48, 48, 48, 69, 70, 72, 67, 50, 51, 52, 54, 65, 66, 67, 54, 61, 64, 48, 48, 48, 48, 48, 48, 48, 49, 45, 46, 57, 50, 53, 54, 61, 64, 63, 48, 48, 48, 48, 49, 45, 45, 46, 73, 76, 75, 48, 69, 70, 66, 67, 54, 55, 61, 60, 45, 46, 53, 54, 61, 60, 64, 63, 48, 48, 48, 48, 48, 48, 48, 48, 48, 49, 45, 46, 56, 57, 47, 48, 48, 48, 48, 48, 48, 48, 48, 70, 67, 55, 73, 72, 66, 66, 67, 55, 56, 57, 61, 64, 63, 48, 49, 60, 60, 64, 63, 48, 48, 66, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 49, 60, 60, 64, 48, 48, 48, 48, 48, 48, 48, 48, 62, 57, 58, 53, 54, 55, 56, 57, 58, 50, 51, 68, 69, 48, 48, 48, 48, 48, 48, 48, 48, 48, 50, 73, 72, 76, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 49, 45, 45, 46, 57, 61, 60, 60, 60, 59, 54, 65, 66, 76, 75, 48, 48, 48, 48, 48, 48, 48, 53, 54, 55, 68, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 49, 45, 64, 63, 48, 63, 62, 57, 58, 50, 73, 72, 76, 48, 48, 48, 48, 48, 48]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}