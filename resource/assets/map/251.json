{"mW": 864, "mH": 672, "tW": 24, "tH": 24, "tiles": [["106_3", 0, 3, 3], ["255", 0, 1, 1], ["304_1", 0, 3, 2], ["304_1", 2, 3, 2], ["304_1", 1, 3, 2], ["304_1", 3, 3, 2], ["302", 0, 2, 2], ["491", 0, 3, 2], ["491", 2, 3, 2], ["491", 1, 3, 2], ["491", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["111", 0, 3, 2], ["111", 2, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "125", 387, 579, 18, 70, 0], [2, "125", 461, 536, 18, 70, 2], [2, "88", 392, -31, 88, 61, 2], [2, "88", 623, 303, 88, 61, 0], [2, "87", -9, 612, 72, 57, 2], [2, "268", 10, 547, 106, 82, 2], [2, "88", 65, 598, 88, 61, 2], [2, "87", -9, 612, 72, 57, 2], [2, "268", 10, 547, 106, 82, 2], [2, "88", 65, 598, 88, 61, 2], [2, "87", 107, 527, 72, 57, 0], [2, "88", 72, 484, 88, 61, 0], [2, "87", 28, 525, 72, 57, 0], [2, "88", 790, -6, 88, 61, 0], [2, "87", 766, -38, 72, 57, 0], [2, "88", 680, -36, 88, 61, 2], [2, "88", 734, -25, 88, 61, 2], [2, "224_1", -32, 518, 124, 194, 0], [2, "88", 147, 619, 88, 61, 0], [2, "87", 744, 337, 72, 57, 2], [2, "88", 821, 338, 88, 61, 0], [2, "88", 773, 295, 88, 61, 2], [2, "88", 799, 286, 88, 61, 2], [2, "268", 573, 258, 106, 82, 2], [2, "88", 539, 320, 88, 61, 2], [2, "88", 533, -35, 88, 61, 0], [2, "88", 461, -29, 88, 61, 2], [2, "88", -39, 7, 88, 61, 0], [2, "614_1", 467, 651, 22, 19, 2], [2, "257", 435, 610, 14, 66, 0], [2, "257", 506, 590, 14, 66, 0], [2, "612", 414, 637, 8, 26, 0], [2, "612", 393, 628, 8, 26, 0], [2, "612", 388, 597, 8, 26, 0], [2, "614_1", 375, 610, 22, 19, 0], [2, "614_1", 397, 621, 22, 19, 0], [2, "612", 523, 618, 8, 26, 2], [2, "612", 376, 603, 8, 26, 0], [2, "612", 376, 621, 8, 26, 0], [2, "614_1", 450, 660, 22, 19, 2], [2, "14_1", 348, 626, 32, 30, 2], [2, "14_1", 368, 635, 32, 30, 2], [2, "14_1", 363, 618, 32, 30, 2], [2, "115", 389, 628, 16, 37, 0], [2, "664", 503, 623, 52, 51, 2], [2, "90", 519, 627, 28, 36, 2], [2, "665", 506, 639, 30, 34, 2], [2, "90", 480, 648, 36, 28, 7], [2, "422", 466, 658, 16, 14, 0], [2, "257", 300, 644, 66, 14, 4], [2, "257", 347, 654, 66, 14, 7], [2, "614_1", 416, 631, 22, 19, 0], [2, "612", 414, 645, 8, 26, 0], [2, "124", 385, 554, 142, 70, 2], [2, "125", 437, 607, 18, 70, 0], [2, "90", 428, 645, 28, 36, 0], [2, "125", 515, 569, 18, 70, 0], [2, "224_1", 287, 529, 124, 194, 0], [2, "145", 371, 219, 20, 23, 0], [2, "146", 386, 231, 30, 25, 0], [2, "143", 382, 208, 38, 28, 0], [2, "144", 405, 236, 32, 19, 0], [2, "83_2", 410, 257, 64, 38, 0], [2, "482", 428, 274, 16, 22, 0], [2, "482", 444, 282, 16, 22, 0], [2, "482", 455, 288, 16, 22, 0], [2, "482", 412, 266, 16, 22, 0], [2, "143", 463, 244, 38, 28, 0], [2, "145", 454, 256, 20, 23, 0], [2, "146", 468, 267, 30, 25, 0], [2, "144", 487, 273, 32, 19, 0], [2, "83_2", 408, 281, 64, 38, 0]]}, {"type": 4, "obj": [[2, "219_1", 291, -30, 36, 30, 0], [2, "88", 243, -47, 88, 61, 0], [2, "88", 161, -43, 88, 61, 2], [2, "263", 629, -9, 34, 34, 0], [2, "220_1", 325, 7, 40, 29, 0], [2, "88", 84, -24, 88, 61, 0], [2, "87", 228, -15, 72, 57, 0], [2, "87", 278, -4, 72, 57, 0], [2, "263", 49, 23, 34, 34, 2], [2, "89", 536, -27, 48, 95, 0], [2, "262", 16, 45, 48, 39, 0], [4, 4, 381, 84, 0, 4001], [2, "89", 776, -11, 48, 95, 0], [2, "89", 437, -5, 48, 95, 0], [4, 2, 36, 165, 0, 4005], [2, "127", 194, 155, 46, 82, 0], [2, "220_1", 193, 211, 40, 29, 2], [2, "127", 159, 160, 46, 82, 2], [2, "263", 176, 212, 34, 34, 0], [2, "86", 828, 202, 50, 49, 0], [2, "127", 232, 171, 46, 82, 0], [2, "263", 573, 221, 34, 34, 0], [2, "127", 121, 178, 46, 82, 2], [2, "328", 150, 232, 32, 29, 0], [2, "328", 610, 234, 32, 29, 0], [4, 7, 732, 263, 0, 4001], [4, 1, 174, 265, 0, 4005], [2, "220_1", 238, 236, 40, 29, 2], [2, "127", 268, 188, 46, 82, 0], [2, "127", 95, 191, 46, 82, 2], [2, "263", 123, 242, 34, 34, 0], [2, "262", 846, 242, 48, 39, 0], [2, "127", 276, 207, 46, 82, 2], [2, "429_2", 71, 236, 64, 63, 0], [2, "127", 274, 221, 46, 82, 0], [2, "327", 807, 281, 30, 22, 0], [2, "220_1", 271, 276, 40, 29, 2], [2, "420", 823, 299, 16, 13, 2], [2, "429_2", 829, 252, 64, 63, 0], [2, "127", 310, 242, 46, 82, 0], [2, "263", 836, 291, 34, 34, 2], [2, "88", -46, 269, 88, 61, 0], [2, "328", 320, 308, 32, 29, 0], [2, "127", 346, 260, 46, 82, 0], [2, "185", 389, 254, 26, 94, 0], [2, "220_1", 349, 322, 40, 29, 2], [2, "185", 471, 290, 26, 94, 0], [2, "127", 669, 308, 46, 82, 2], [2, "127", 705, 309, 46, 82, 0], [2, "224_1", 716, 213, 124, 194, 0], [2, "127", 632, 327, 46, 82, 2], [2, "127", 743, 327, 46, 82, 0], [2, "429_2", 670, 350, 64, 63, 0], [2, "127", 491, 339, 46, 82, 0], [2, "127", 531, 343, 46, 82, 2], [2, "127", 781, 344, 46, 82, 0], [2, "127", 566, 346, 46, 82, 0], [2, "127", 595, 346, 46, 82, 2], [2, "89", 608, 342, 48, 95, 0], [2, "89", 799, 344, 48, 95, 0], [4, 5, 667, 453, 0, 4020], [4, 6, 636, 479, 1, 4020], [2, "73_1", 718, 409, 46, 72, 0], [2, "328", 736, 455, 32, 29, 0], [4, 3, 827, 542, 0, 4004], [2, "262", -8, 521, 48, 39, 0], [4, 8, 212, 596, 0, 4020], [2, "429_2", 115, 568, 64, 63, 0], [2, "89", 82, 539, 48, 95, 2], [2, "329", 109, 597, 42, 37, 0], [2, "422", 204, 638, 16, 14, 0], [2, "125", 842, 587, 18, 70, 0], [2, "89", 23, 570, 48, 95, 0], [2, "73_1", 236, 600, 46, 72, 0], [2, "220_1", 477, 644, 40, 29, 0], [2, "220_1", 608, 644, 40, 29, 0], [2, "89", 77, 581, 48, 95, 0], [2, "328", 38, 648, 32, 29, 0], [2, "429_2", 816, 615, 64, 63, 2], [2, "328", 644, 654, 32, 29, 0], [2, "429_2", 243, 625, 64, 63, 0]]}, {"type": 3, "obj": [[2, "313_3", 743, 643, 70, 44, 2], [2, "429_2", -29, 159, 64, 63, 0], [2, "329", -2, 206, 42, 37, 0], [2, "143", 381, 335, 38, 28, 0], [2, "326", 767, 457, 18, 14, 0], [2, "63_1", 780, 617, 16, 31, 2], [2, "325", 789, 647, 50, 37, 0], [2, "257", 800, 622, 66, 14, 6], [2, "257", 803, 609, 66, 14, 7], [2, "257", 808, 634, 66, 14, 7], [2, "647", 818, 623, 34, 39, 0], [2, "173", 806, 623, 70, 45, 0], [2, "90", 786, 634, 36, 28, 6], [2, "89", 205, -14, 48, 95, 0], [2, "143", 463, 372, 38, 28, 0], [2, "420", 509, 419, 16, 13, 0], [2, "220_1", 572, 41, 40, 29, 0], [2, "263", 556, 39, 34, 34, 0], [2, "328", 511, 59, 32, 29, 0], [2, "328", 644, 111, 32, 29, 0], [2, "263", 245, 43, 34, 34, 0], [2, "328", -7, 128, 32, 29, 0], [2, "89", -22, 44, 48, 95, 2], [2, "220_1", 28, 90, 40, 29, 2], [2, "263", 606, 468, 34, 34, 2], [2, "422", 431, 466, 16, 14, 0], [2, "220_1", 411, 451, 40, 29, 0], [2, "327", 638, 428, 30, 22, 0], [2, "328", 180, 353, 32, 29, 0], [2, "420", 165, 348, 16, 13, 0], [2, "127", 820, 361, 46, 82, 0], [2, "329", 838, 410, 42, 37, 0], [2, "328", 780, 435, 32, 29, 0], [2, "326", 808, 498, 18, 14, 0], [2, "262", 829, 487, 48, 39, 2], [2, "85_2", 812, 425, 48, 53, 0], [2, "327", 840, 460, 30, 22, 0], [2, "244", 827, 455, 20, 38, 0], [2, "327", 788, 462, 30, 22, 0], [2, "220_1", 86, 25, 40, 29, 2], [2, "220_1", 407, 52, 40, 29, 2], [2, "220_1", 832, 63, 40, 29, 2], [2, "328", 734, 35, 32, 29, 0], [2, "326", 589, 486, 18, 14, 0], [2, "420", 605, 497, 16, 13, 0], [2, "327", 615, 496, 30, 22, 0], [2, "326", 492, 409, 18, 14, 0], [2, "328", 463, 386, 32, 29, 0], [2, "328", 397, 462, 32, 29, 0]]}, {"type": 2, "data": [66, 66, 66, 66, 78, 78, 78, 66, 66, 66, -1, 66, 66, 66, 66, 66, 84, 66, 84, 66, 66, 66, 66, 84, 84, 79, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, 84, 66, 84, 84, 66, 84, 66, 66, 66, 85, 79, 81, 85, 66, 66, 66, 84, 84, 84, 66, 84, 84, 79, 85, 84, 83, 74, 80, -1, -1, -1, 74, 75, 78, 69, 68, 84, 84, 84, 84, 66, 79, 85, 79, 81, 81, 85, 70, 73, 78, 84, 84, 84, 84, 84, 84, 66, 72, 71, 74, 75, 80, -1, -1, -1, -1, -1, 62, 63, 78, 72, 71, 66, 84, 84, 66, 79, 76, 74, 80, -1, -1, 74, 75, 81, 85, 84, 66, 66, 66, 84, 79, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 75, 81, 81, 80, 66, 79, 81, 81, 80, -1, -1, -1, -1, -1, 86, 87, 88, 82, 81, 85, 66, 79, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 93, 79, 80, -1, -1, -1, -1, -1, -1, -1, 98, 109, 109, 91, 87, 88, 82, 75, 76, -1, -1, -1, -1, -1, 94, 93, 87, 88, -1, -1, 86, 87, 87, 88, 86, 97, 96, 76, -1, -1, 94, 93, 92, -1, -1, -1, -1, -1, 106, 105, 105, 104, -1, -1, -1, -1, -1, -1, 86, 87, 97, 96, 90, 91, 93, 92, 98, 109, 90, 91, 97, 102, 102, -1, -1, -1, 109, 108, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 90, 96, 96, 103, 105, 105, 104, -1, 101, 96, 96, 96, 103, 105, 69, 68, -1, 106, 105, 104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 98, 99, 105, 105, 100, -1, -1, -1, -1, 98, 99, 99, 105, 104, -1, 83, 80, -1, -1, -1, -1, 83, 78, 79, 85, 84, -1, -1, -1, -1, -1, -1, -1, 98, -1, -1, 86, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, -1, -1, -1, 78, 79, 79, 75, 80, 82, 81, 84, -1, 63, 64, -1, -1, -1, -1, -1, -1, 89, 91, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, 69, 68, -1, 62, 78, 79, 76, -1, -1, -1, 82, 81, -1, 66, 67, 69, 68, -1, -1, -1, -1, 89, 96, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 75, -1, -1, -1, 74, 75, 76, -1, -1, -1, -1, -1, -1, 85, 84, -1, -1, -1, -1, -1, -1, -1, 106, 109, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 65, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 88, 82, 81, 85, 84, -1, -1, -1, -1, -1, 86, 97, 104, -1, -1, -1, -1, -1, 62, 63, 62, 63, -1, 77, 84, 69, 68, -1, -1, -1, -1, -1, -1, -1, 89, 90, 91, 92, -1, 82, 85, 69, 68, -1, -1, -1, 101, 107, -1, 70, 68, -1, -1, 70, 65, 66, 65, 66, -1, 65, 67, 81, 80, -1, -1, 86, 87, 87, 88, 94, 97, 108, 108, 107, -1, -1, 82, 80, -1, -1, -1, 68, 106, 9, 9, 77, 85, 84, 83, 82, 81, 80, -1, -1, -1, 74, 85, -1, -1, -1, -1, 89, 90, 90, 91, 97, 108, 108, 103, 104, -1, -1, -1, -1, -1, -1, 77, 78, 84, 85, 84, 84, 82, 78, 78, 84, -1, -1, 85, 84, -1, -1, 82, -1, -1, -1, -1, 101, 102, 102, 108, 90, 90, 90, 107, -1, -1, -1, -1, -1, -1, -1, 74, 75, 81, 82, 81, 85, 84, 78, 78, 78, 67, 69, 82, 81, 81, 81, -1, -1, -1, -1, -1, 98, 99, 99, 99, 105, 108, 90, 91, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 84, 78, 78, 78, 79, 81, 69, 69, 63, 64, 69, -1, -1, -1, -1, -1, -1, 86, 87, 97, 108, 108, 108, 103, 104, -1, -1, -1, -1, -1, -1, 86, 87, 88, 70, 73, 84, 84, 84, 72, 71, 74, 81, 85, 78, 84, 84, -1, -1, -1, -1, -1, -1, 89, 102, 108, 107, 105, 109, 93, 92, -1, -1, -1, -1, -1, -1, 89, 102, 95, 74, 75, 84, 81, 75, 75, 80, -1, -1, 74, 85, 84, 84, -1, -1, -1, -1, -1, -1, 101, 102, 102, 95, 86, 97, 103, 104, 86, 87, 93, 93, 92, -1, 98, 109, 95, -1, -1, -1, -1, -1, -1, -1, 94, 93, 92, 74, 85, 84, -1, -1, -1, -1, -1, 72, 98, 99, 99, 100, 89, 103, 100, -1, 89, 90, 102, 102, 107, -1, -1, 101, 91, 92, -1, -1, -1, -1, -1, 86, 97, 96, 95, -1, 74, 75, 68, -1, -1, -1, 72, 72, 69, 68, -1, -1, 89, 107, -1, -1, 98, 99, 105, 104, -1, -1, -1, 98, 96, 96, 92, -1, -1, -1, -1, 89, 109, 108, 107, -1, -1, -1, 71, 72, 62, -1, -1, -1, 78, 67, 68, -1, 106, 104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 101, 102, 95, -1, -1, -1, -1, 106, 106, 105, 104, -1, -1, -1, 72, 66, -1, -1, -1, 72, 81, 81, 80, -1, -1, -1, -1, -1, -1, -1, 70, 69, 68, 70, 69, 69, 98, 99, 104, 106, 105, 104, -1, -1, -1, -1, -1, -1, -1, -1, 72, 66, 79, 75, 85, 66, 67, 68, 62, 63, 64, 62, 63, 64, -1, -1, 73, 72, 67, 73, 72, 72, 62, 69, 68, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, 66, 66, 67, 68, 74, 75, 85, 72, 65, 66, 67, 65, 66, 67, 69, 63, 64, 72, 72, 72, 66, 66, 66, 84, 83, 62, 63, 70, -1, -1, 62, 63, 69, 63, 73, 66]}, {"type": 3, "obj": [[2, "253_1", 328, 420, 92, 53, 2], [2, "208_1", 551, 472, 78, 40, 0], [2, "208_1", 641, 363, 78, 40, 0], [2, "208_1", 535, 336, 78, 40, 1], [2, "208_1", 594, 338, 78, 40, 3], [2, "208_1", 329, 51, 78, 40, 0], [2, "205_1", 210, 26, 54, 40, 2], [2, "208_1", 120, 10, 78, 40, 0], [2, "208_1", 152, 3, 78, 40, 0], [2, "205_1", 104, 45, 54, 40, 2], [2, "208_1", 287, 35, 78, 40, 1], [2, "253", 156, 324, 92, 53, 2], [2, "253", 631, 454, 92, 53, 2], [2, "253", 554, 426, 92, 53, 2], [2, "253", 514, 634, 92, 53, 2], [2, "253", 547, 469, 92, 53, 0], [2, "253", -22, 462, 92, 53, 0], [2, "253", 740, 374, 92, 53, 2], [2, "308", 416, 92, 52, 22, 2], [2, "308", 459, 65, 52, 22, 0], [2, "309", 523, 9, 46, 33, 0], [2, "308", 489, 620, 52, 22, 0], [2, "305", 465, 84, 30, 24, 0], [2, "307", 401, 78, 42, 19, 0], [2, "306", 188, 395, 46, 25, 0], [2, "308", 456, 646, 52, 22, 0], [2, "308", 428, 589, 52, 22, 0], [2, "309", 484, 589, 46, 33, 0], [2, "306", 383, 607, 46, 25, 0], [2, "309", 374, 624, 46, 33, 0], [2, "307", 430, 654, 42, 19, 0], [2, "307", 419, 387, 42, 19, 0], [2, "307", 440, 80, 42, 19, 0], [2, "253", 687, 620, 92, 53, 2], [2, "253", 785, 272, 92, 53, 2], [2, "253", 451, 159, 92, 53, 0], [2, "253", -10, 154, 92, 53, 0], [2, "253", 224, 641, 92, 53, 2], [2, "253", -6, 380, 92, 53, 2], [2, "253", 363, 568, 92, 53, 2], [2, "253", 632, 388, 92, 53, 0], [2, "325", 456, 512, 50, 37, 0], [2, "325", 456, 512, 50, 37, 0], [2, "174", 795, 312, 68, 33, 0], [2, "174", 791, 596, 68, 33, 0], [2, "174", 377, 615, 68, 33, 0], [2, "174", 215, 643, 68, 33, 0], [2, "174", 326, 125, 68, 33, 0], [2, "174", 580, 595, 68, 33, 0], [2, "174", 42, 638, 68, 33, 0], [2, "174", 551, 486, 68, 33, 0], [2, "174", 533, 135, 68, 33, 0], [2, "174", 273, 547, 68, 33, 0], [2, "253", 338, 642, 92, 53, 0], [2, "253_1", 295, 447, 92, 53, 0], [2, "253_1", 372, 395, 92, 53, 0], [2, "253_1", 339, 364, 92, 53, 2], [2, "309", 720, 154, 46, 33, 0], [2, "174", 670, 276, 68, 33, 0]]}, {"type": 2, "data": [20, 20, 20, 20, 20, 0, 20, 26, 26, 26, 2, 1, 20, 20, 26, 27, 23, 24, 33, 32, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 11, 17, 21, 26, 26, 26, 26, 20, 20, 20, 20, 18, 26, 20, 20, 20, 20, 4, 23, 24, 4, 5, 30, 29, 33, 20, 20, 20, 20, 20, 27, 29, 23, 29, 33, 20, 20, 20, 26, 21, 26, 26, 26, 26, 26, 14, 15, 21, 26, 32, 31, 20, 20, 20, 12, 11, 12, 8, 7, 8, 25, 20, 26, 32, 27, 23, 24, 10, 11, 16, 25, 32, 20, 26, 26, 14, 26, 26, 26, 26, 32, 32, 26, 26, 32, 26, 27, 33, 26, 14, 15, 14, 15, 17, 5, 26, 32, 32, 26, 27, 28, 3, 4, 22, 23, 24, 22, 33, 20, 20, 14, 14, 33, 32, 27, 33, 26, 32, 26, 27, 29, 29, 28, 25, 26, 26, 32, 32, 20, 20, 26, 20, 32, 27, 23, 24, 4, 10, 11, 12, 18, 17, 16, 25, 26, 14, 14, 27, 26, 27, 28, 30, 29, 29, 29, 28, 4, 5, 7, 22, 33, 26, 32, 32, 32, 26, 26, 20, 27, 28, 18, 17, 17, 21, 20, 15, 21, 20, 19, 22, 23, 29, 23, 24, 26, 15, 17, 17, 17, 16, 0, 1, 10, 11, 17, 16, 30, 29, 29, 33, 32, 27, 33, 32, 31, 10, 21, 20, 20, 20, 20, 20, 20, 20, 15, 17, 16, 4, 4, 8, 29, 15, 20, 27, 23, 28, 3, 4, 13, 14, 20, 15, 11, 12, 4, 22, 23, 24, 30, 29, 28, 25, 20, 20, 20, 14, 20, 20, 20, 20, 14, 20, 19, 4, 18, 17, 20, 26, 27, 24, 18, 17, 17, 7, 8, 1, 2, 26, 14, 15, 17, 17, 17, 16, 2, 0, 1, 22, 33, 32, 26, 14, 26, 14, 14, 14, 14, 14, 15, 17, 21, 20, 20, 27, 24, 18, 21, 20, 14, 14, 14, 14, 14, 26, 20, 20, 20, 20, 20, 19, 8, 10, 16, 5, 22, 33, 32, 32, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 20, 15, 17, 21, 20, 14, 14, 14, 14, 14, 14, 14, 26, 20, 20, 20, 20, 15, 16, 6, 7, 8, 8, 30, 29, 33, 27, 33, 32, 14, 14, 14, 14, 14, 14, 14, 20, 27, 33, 14, 26, 26, 26, 14, 14, 14, 14, 14, 8, 1, 2, 20, 20, 26, 31, 4, 6, 7, 0, 6, 3, 30, 24, 30, 33, 20, 20, 20, 20, 14, 14, 14, 26, 19, 30, 29, 33, 26, 26, 26, 26, 26, 26, 20, 32, 32, 5, 4, 5, 23, 24, 1, 10, 11, 16, 10, 5, 3, 0, 1, 30, 29, 29, 33, 20, 20, 20, 14, 27, 28, 4, 5, 22, 33, 20, 26, 26, 26, 26, 26, 20, 20, 20, 7, 17, 16, 2, 4, 13, 14, 19, 13, 1, 2, 3, 4, 3, 4, 5, 22, 33, 32, 20, 14, 31, 18, 17, 16, 4, 30, 33, 26, 26, 26, 26, 26, 26, 20, 20, 20, 26, 31, 5, 7, 20, 20, 20, 3, 0, 20, 6, 11, 17, 16, 8, 18, 21, 25, 26, 14, 15, 21, 26, 15, 16, 8, 30, 33, 26, 26, 26, 26, 26, 32, 20, 26, 27, 24, 10, 17, 17, 32, 32, 0, 1, 20, 20, 14, 20, 15, 11, 21, 32, 25, 26, 14, 26, 26, 26, 20, 19, 2, 2, 22, 33, 20, 26, 14, 14, 14, 20, 20, 31, 2, 13, 20, 20, 32, 32, 20, 20, 20, 14, 14, 14, 14, 26, 27, 5, 22, 33, 14, 26, 26, 26, 27, 28, 10, 16, 18, 17, 26, 26, 32, 14, 14, 32, 32, 15, 11, 21, 20, 32, 14, 14, 14, 20, 26, 26, 26, 20, 20, 14, 15, 17, 16, 18, 17, 27, 29, 29, 28, 1, 2, 0, 22, 23, 33, 26, 27, 23, 33, 33, 32, 20, 26, 14, 27, 29, 33, 32, 32, 20, 25, 26, 20, 14, 26, 20, 20, 20, 15, 21, 20, 28, 1, 2, 3, 4, 5, 3, 4, 5, 30, 29, 28, 18, 21, 26, 27, 33, 14, 20, 19, 4, 30, 33, 26, 20, 20, 23, 32, 26, 32, 26, 26, 15, 26, 16, 26, 8, 4, 18, 17, 16, 8, 18, 11, 17, 16, 6, 7, 22, 23, 23, 24, 22, 23, 23, 24, 3, 18, 21, 14, 33, 32, 32, 32, 32, 14, 20, 26, 26, 26, 15, 26, 18, 17, 21, 20, 19, 18, 20, 14, 20, 19, 6, 7, 8, 8, 18, 17, 17, 16, 18, 17, 16, 22, 33, 26, 27, 33, 32, 32, 26, 14, 14, 32, 26, 26, 20, 33, 21, 20, 20, 0, 14, 21, 14, 14, 27, 28, 1, 2, 10, 11, 21, 26, 20, 31, 22, 33, 15, 16, 22, 23, 24, 30, 29, 33, 26, 20, 32, 27, 29, 33, 20, 20, 20, 20, 16, 14, 14, 78, 14, 20, 19, 3, 4, 5, 22, 33, 20, 14, 14, 28, 3, 22, 29, 28, 4, 5, 10, 17, 16, 30, 29, 29, 23, 28, 8, 25, 26, 26, 20, 26, 14, 14, 14, 14, 14, 14, 15, 17, 16, 8, 6, 25, 20, 26, 16, 8, 6, 7, 8, 4, 5, 8, 30, 29, 28, 6, 7, 8, 0, 1, 18, 21, 26, 26, 20, 20, 20, 20, 20, 14, 14, 14, 14, 20, 15, 11, 17, 21, 14, 14, 14, 34, 35, 34, 35, 7, 18, 17, 17, 16, 1, 2, 18, 17, 11, 16, 25, 20, 20, 14, 20, 20, 20, 20, 20, 20, 14, 14, 14, 20, 14, 14, 14, 14, 14, 14, 14, 14, 34, 36, 37, 35, 21, 14, 14, 19, 4, 18, 21, 26, 14, 15, 21, 20, 14, 14, 20, 20, 20, 20, 14, 14, 20, 20, 14, 20, 14, 14, 14, 14, 14, 14, 15, 8, 36, 0, 20, 20, 20, 20, 20, 15, 11, 21, 20, 20, 20, 20, 20, 20, 14, 14]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1]}