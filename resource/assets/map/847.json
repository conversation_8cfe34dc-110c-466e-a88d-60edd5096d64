{"mW": 864, "mH": 672, "tW": 24, "tH": 24, "tiles": [["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["928", 0, 2, 1], ["497", 0, 2, 1], ["3175", 0, 3, 3]], "layers": [{"type": 3, "obj": [[2, "922", 607, 653, 30, 108, 0], [2, "3235", -1, 471, 22, 51, 2], [2, "3235", 21, 482, 22, 51, 2], [2, "3235", 43, 493, 22, 51, 2], [2, "3235", 65, 504, 22, 51, 2], [2, "3235", 87, 515, 22, 51, 2], [2, "3235", 109, 526, 22, 51, 2], [2, "3235", 131, 537, 22, 51, 2], [2, "3235", 153, 548, 22, 51, 2], [2, "3235", 175, 559, 22, 51, 2], [2, "3235", 197, 570, 22, 51, 2], [2, "3235", 219, 581, 22, 51, 2], [2, "3235", 241, 592, 22, 51, 2], [2, "3235", 263, 603, 22, 51, 2], [2, "3235", 285, 614, 22, 51, 2], [2, "3235", 307, 625, 22, 51, 2], [2, "3235", 329, 636, 22, 51, 2], [2, "3235", 351, 647, 22, 51, 2], [2, "3235", 373, 658, 22, 51, 2], [2, "3235", 395, 669, 22, 51, 2], [2, "3235", 417, 680, 22, 51, 2], [2, "3235", 439, 691, 22, 51, 2], [2, "3235", 461, 702, 22, 51, 2], [2, "3235", 483, 713, 22, 51, 2], [2, "3235", 505, 724, 22, 51, 2], [2, "3235", -1, 507, 22, 51, 2], [2, "3235", 21, 518, 22, 51, 2], [2, "3235", 43, 529, 22, 51, 2], [2, "3235", 65, 540, 22, 51, 2], [2, "3235", 87, 551, 22, 51, 2], [2, "3235", 109, 562, 22, 51, 2], [2, "3235", 131, 573, 22, 51, 2], [2, "3235", 153, 584, 22, 51, 2], [2, "3235", 175, 595, 22, 51, 2], [2, "3235", 197, 606, 22, 51, 2], [2, "3235", 219, 617, 22, 51, 2], [2, "3235", 241, 628, 22, 51, 2], [2, "3235", 263, 639, 22, 51, 2], [2, "3235", 285, 650, 22, 51, 2], [2, "3235", 307, 661, 22, 51, 2], [2, "3235", 329, 672, 22, 51, 2], [2, "3235", -4, 544, 22, 51, 2], [2, "3235", 18, 555, 22, 51, 2], [2, "3235", 40, 566, 22, 51, 2], [2, "3235", 62, 577, 22, 51, 2], [2, "3235", 84, 588, 22, 51, 2], [2, "3235", 106, 599, 22, 51, 2], [2, "3235", 128, 610, 22, 51, 2], [2, "3235", 150, 621, 22, 51, 2], [2, "3235", 172, 632, 22, 51, 2], [2, "3235", 194, 643, 22, 51, 2], [2, "3235", 216, 654, 22, 51, 2], [2, "3235", 238, 665, 22, 51, 2], [2, "3235", 260, 676, 22, 51, 2], [2, "3235", 282, 687, 22, 51, 2], [2, "3235", 304, 698, 22, 51, 2], [2, "3235", 326, 709, 22, 51, 2], [2, "3235", -10, 577, 22, 51, 2], [2, "3235", 12, 588, 22, 51, 2], [2, "3235", 34, 599, 22, 51, 2], [2, "3235", 56, 610, 22, 51, 2], [2, "3235", 78, 621, 22, 51, 2], [2, "3235", 100, 632, 22, 51, 2], [2, "3235", 122, 643, 22, 51, 2], [2, "3235", 144, 654, 22, 51, 2], [2, "3235", 166, 665, 22, 51, 2], [2, "3235", 188, 676, 22, 51, 2], [2, "3235", 210, 687, 22, 51, 2], [2, "3235", 232, 698, 22, 51, 2], [2, "3235", 252, 710, 22, 51, 2], [2, "3235", 274, 721, 22, 51, 2], [2, "3235", 296, 732, 22, 51, 2], [2, "3235", 318, 743, 22, 51, 2], [2, "3235", -8, 619, 22, 51, 2], [2, "3235", 14, 630, 22, 51, 2], [2, "3235", 36, 641, 22, 51, 2], [2, "3235", 58, 652, 22, 51, 2], [2, "3235", 80, 663, 22, 51, 2], [2, "3235", 102, 674, 22, 51, 2], [2, "3235", 124, 685, 22, 51, 2], [2, "3235", 146, 696, 22, 51, 2], [2, "3235", 164, 700, 22, 51, 2], [2, "3235", 186, 711, 22, 51, 2], [2, "3235", 208, 722, 22, 51, 2], [2, "3235", 230, 733, 22, 51, 2], [2, "3235", -5, 661, 22, 51, 2], [2, "3235", 17, 672, 22, 51, 2], [2, "3235", 39, 683, 22, 51, 2], [2, "3235", 61, 694, 22, 51, 2], [2, "3235", -9, 694, 22, 51, 2], [2, "3235", 13, 705, 22, 51, 2], [2, "3235", 35, 716, 22, 51, 2], [2, "3235", 57, 727, 22, 51, 2], [2, "3235", 76, 688, 22, 51, 2], [2, "3235", 98, 699, 22, 51, 2], [2, "3235", 120, 710, 22, 51, 2], [2, "3235", 142, 721, 22, 51, 2], [2, "3235", 349, 682, 22, 51, 2], [2, "3235", 371, 693, 22, 51, 2], [2, "3235", 393, 704, 22, 51, 2], [2, "3235", 415, 715, 22, 51, 2], [2, "733", -19, 315, 54, 35, 2], [2, "733", 22, 335, 54, 35, 2], [2, "733", 61, 357, 54, 35, 2], [2, "733", 102, 377, 54, 35, 2], [2, "733", 140, 401, 54, 35, 2], [2, "733", 181, 421, 54, 35, 2], [2, "733", 219, 443, 54, 35, 2], [2, "733", 260, 463, 54, 35, 2], [2, "733", 299, 486, 54, 35, 2], [2, "733", 340, 506, 54, 35, 2], [2, "733", 380, 528, 54, 35, 2], [2, "733", 421, 548, 54, 35, 2], [2, "922", 512, 607, 30, 108, 0], [2, "733", 462, 569, 54, 35, 2], [2, "733", 503, 589, 54, 35, 2], [2, "733", 542, 611, 54, 35, 2], [2, "733", 583, 631, 54, 35, 2], [2, "733", 621, 651, 54, 35, 2], [2, "733", 662, 671, 54, 35, 2]]}, {"type": 4, "obj": [[2, "3070", 278, 88, 56, 113, 2], [2, "3070", 96, 177, 56, 113, 2], [2, "922", 20, 337, 30, 108, 0], [2, "922", 93, 382, 30, 108, 0], [2, "922", 174, 425, 30, 108, 0], [2, "922", 253, 465, 30, 108, 0], [2, "922", 335, 513, 30, 108, 0], [2, "922", 421, 554, 30, 108, 0]]}, {"type": 2, "data": [30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 30, 30, 30, 30, 30, 30, 30, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 30, 30, 30, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 30, 30, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "925", 633, 216, 52, 34, 0], [2, "925", 565, 183, 52, 34, 0], [2, "943", 165, 144, 14, 41, 2], [2, "943", 150, 152, 14, 41, 2], [2, "943", 136, 160, 14, 41, 2], [2, "943", 121, 169, 14, 41, 2], [2, "943", 899, 298, 14, 41, 0], [2, "943", 898, 266, 14, 41, 0], [2, "943", 898, 231, 14, 41, 0], [2, "943", 897, 198, 14, 41, 0], [2, "943", 814, 234, 14, 41, 0], [2, "943", 813, 201, 14, 41, 0], [2, "943", 815, 166, 14, 41, 0], [2, "944", 659, 114, 12, 22, 0], [2, "931", 648, 115, 24, 37, 0], [2, "944", 611, 101, 12, 22, 0], [2, "944", 611, 70, 12, 22, 0], [2, "944", 611, 86, 12, 22, 0], [2, "931", 611, 98, 24, 37, 0], [2, "944", 605, 53, 12, 22, 0], [2, "943", 878, -10, 14, 41, 0], [2, "943", 880, 13, 14, 41, 0], [2, "943", 865, -7, 14, 41, 0], [2, "943", 672, -26, 14, 41, 0], [2, "943", 672, 8, 14, 41, 0], [2, "943", 850, -4, 14, 41, 0], [2, "943", 836, -8, 14, 41, 0], [2, "943", 807, -19, 14, 41, 0], [2, "943", 757, -20, 14, 41, 0], [2, "943", 507, 1, 14, 41, 0], [2, "943", 496, -2, 14, 41, 0], [2, "943", 482, -7, 14, 41, 0], [2, "943", 467, -8, 14, 41, 0], [2, "943", 541, 19, 14, 41, 2], [2, "923", 541, 24, 44, 125, 2], [2, "943", 390, 30, 14, 41, 0], [2, "943", 390, -9, 14, 41, 0], [2, "943", 356, -15, 14, 41, 2], [2, "943", 386, -23, 14, 41, 0], [2, "943", 386, 9, 14, 41, 0], [2, "943", 357, 14, 14, 41, 2], [2, "925", 342, 70, 52, 34, 0], [2, "943", 386, 42, 14, 41, 0], [2, "925", 360, 80, 52, 34, 0], [2, "925", 385, 92, 52, 34, 0], [2, "925", 410, 105, 52, 34, 0], [2, "925", 436, 118, 52, 34, 0], [2, "925", 460, 130, 52, 34, 0], [2, "925", 484, 143, 52, 34, 0], [2, "925", 508, 155, 52, 34, 0], [2, "943", 432, 12, 14, 41, 0], [2, "943", 432, 45, 14, 41, 0], [2, "943", 432, -18, 14, 41, 0], [2, "923", 396, -18, 44, 125, 0], [2, "943", 461, 47, 14, 41, 0], [2, "943", 461, 80, 14, 41, 0], [2, "943", 461, 15, 14, 41, 0], [2, "943", 506, 49, 14, 41, 0], [2, "943", 506, 88, 14, 41, 0], [2, "943", 505, 20, 14, 41, 0], [2, "943", 465, 29, 14, 41, 0], [2, "943", 465, 68, 14, 41, 0], [2, "923", 471, 20, 44, 125, 0], [2, "921", 502, 108, 56, 68, 0], [2, "922", 516, 15, 30, 108, 0], [2, "921", 426, 71, 56, 68, 0], [2, "922", 440, -22, 30, 108, 0], [2, "941", 511, -3, 44, 39, 0], [2, "921", 350, 34, 56, 68, 0], [2, "924", 495, -33, 58, 48, 0], [2, "921", 570, 76, 56, 68, 0], [2, "922", 584, -17, 30, 108, 0], [2, "924", 524, -18, 58, 48, 0], [2, "924", 551, -5, 58, 48, 0], [2, "943", 149, 124, 14, 41, 2], [2, "943", 148, 91, 14, 41, 2], [2, "943", 150, 57, 14, 41, 2], [2, "943", 45, 171, 14, 41, 2], [2, "943", 41, 139, 14, 41, 2], [2, "943", 43, 106, 14, 41, 2], [2, "943", 254, 62, 14, 41, 2], [2, "943", 254, 30, 14, 41, 2], [2, "943", 253, -2, 14, 41, 2], [2, "943", 52, 3, 14, 41, 2], [2, "943", 20, 16, 14, 41, 2], [2, "943", 20, -17, 14, 41, 2], [2, "943", 52, -30, 14, 41, 2], [2, "965", -6, 310, 40, 33, 0], [2, "922", 365, -60, 30, 108, 0], [2, "925", 326, 89, 52, 34, 0], [2, "943", 287, -7, 14, 41, 2], [2, "943", 302, -11, 14, 41, 2], [2, "943", 317, -17, 14, 41, 2], [2, "925", 298, 103, 52, 34, 0], [2, "925", 272, 116, 52, 34, 0], [2, "925", 249, 127, 52, 34, 0], [2, "943", 330, 5, 14, 41, 2], [2, "943", 330, 38, 14, 41, 2], [2, "943", 330, -27, 14, 41, 2], [2, "923", 285, -3, 44, 125, 2], [2, "943", 330, 64, 14, 41, 2], [2, "921", 246, 86, 56, 68, 0], [2, "922", 260, -6, 30, 108, 0], [2, "921", 321, 48, 56, 68, 0], [2, "922", 335, -44, 30, 108, 0], [2, "941", 254, -25, 44, 39, 0], [2, "941", 329, -58, 44, 39, 0], [2, "924", 280, -110, 58, 48, 0], [2, "924", 308, -96, 58, 48, 0], [2, "924", 336, -83, 58, 48, 0], [2, "924", 283, -82, 58, 48, 0], [2, "924", 311, -69, 58, 48, 0], [2, "924", 258, -71, 58, 48, 0], [2, "924", 287, -57, 58, 48, 0], [2, "924", 233, -58, 58, 48, 0], [2, "924", 260, -44, 58, 48, 0], [2, "923", 294, -181, 44, 125, 2], [2, "921", 259, -98, 56, 68, 0], [2, "922", 273, -191, 30, 108, 0], [2, "921", 325, -130, 56, 68, 0], [2, "925", 221, 141, 52, 34, 0], [2, "943", 182, 45, 14, 41, 2], [2, "943", 197, 41, 14, 41, 2], [2, "943", 212, 35, 14, 41, 2], [2, "925", 193, 155, 52, 34, 0], [2, "925", 167, 168, 52, 34, 0], [2, "925", 144, 180, 52, 34, 0], [2, "943", 225, 57, 14, 41, 2], [2, "943", 225, 90, 14, 41, 2], [2, "943", 225, 25, 14, 41, 2], [2, "943", 225, 116, 14, 41, 2], [2, "921", 216, 101, 56, 68, 0], [2, "922", 230, 8, 30, 108, 0], [2, "941", 149, 27, 44, 39, 0], [2, "941", 224, -6, 44, 39, 0], [2, "924", 175, -58, 58, 48, 0], [2, "924", 203, -44, 58, 48, 0], [2, "924", 231, -31, 58, 48, 0], [2, "924", 178, -30, 58, 48, 0], [2, "924", 206, -17, 58, 48, 0], [2, "924", 153, -19, 58, 48, 0], [2, "924", 182, -5, 58, 48, 0], [2, "924", 128, -6, 58, 48, 0], [2, "924", 155, 8, 58, 48, 0], [2, "923", 189, -129, 44, 125, 2], [2, "921", 154, -46, 56, 68, 0], [2, "922", 168, -139, 30, 108, 0], [2, "921", 220, -78, 56, 68, 0], [2, "925", 117, 195, 52, 34, 0], [2, "943", 78, 98, 14, 41, 2], [2, "943", 93, 94, 14, 41, 2], [2, "943", 108, 88, 14, 41, 2], [2, "925", 89, 208, 52, 34, 0], [2, "925", 63, 221, 52, 34, 0], [2, "925", 40, 232, 52, 34, 0], [2, "943", 121, 110, 14, 41, 2], [2, "943", 121, 143, 14, 41, 2], [2, "943", 121, 78, 14, 41, 2], [2, "921", 37, 191, 56, 68, 0], [2, "922", 51, 99, 30, 108, 0], [2, "941", 45, 80, 44, 39, 0], [2, "941", 120, 47, 44, 39, 0], [2, "924", 71, -5, 58, 48, 0], [2, "924", 99, 9, 58, 48, 0], [2, "924", 127, 22, 58, 48, 0], [2, "924", 74, 23, 58, 48, 0], [2, "924", 102, 36, 58, 48, 0], [2, "924", 49, 34, 58, 48, 0], [2, "924", 78, 48, 58, 48, 0], [2, "924", 24, 47, 58, 48, 0], [2, "924", 51, 61, 58, 48, 0], [2, "923", 85, -76, 44, 125, 2], [2, "921", 50, 7, 56, 68, 0], [2, "922", 64, -86, 30, 108, 0], [2, "921", 116, -25, 56, 68, 0], [2, "925", 13, 245, 52, 34, 0], [2, "943", -26, 149, 14, 41, 2], [2, "943", -11, 145, 14, 41, 2], [2, "943", 4, 139, 14, 41, 2], [2, "925", -15, 259, 52, 34, 0], [2, "925", -42, 270, 52, 34, 0], [2, "943", 17, 161, 14, 41, 2], [2, "943", 17, 194, 14, 41, 2], [2, "943", 17, 129, 14, 41, 2], [2, "923", -28, 153, 44, 125, 2], [2, "943", 17, 220, 14, 41, 2], [2, "921", 8, 205, 56, 68, 0], [2, "922", 22, 112, 30, 108, 0], [2, "941", 16, 98, 44, 39, 0], [2, "924", -33, 46, 58, 48, 0], [2, "924", -5, 60, 58, 48, 0], [2, "924", 23, 73, 58, 48, 0], [2, "924", -30, 74, 58, 48, 0], [2, "924", -2, 87, 58, 48, 0], [2, "924", -55, 85, 58, 48, 0], [2, "924", -26, 99, 58, 48, 0], [2, "924", -53, 112, 58, 48, 0], [2, "923", -19, -25, 44, 125, 2], [2, "921", 12, 26, 56, 68, 0], [2, "922", 26, -67, 30, 108, 0], [2, "965", 349, 113, 40, 33, 0], [2, "978", 290, 112, 66, 56, 0], [2, "978", 19, 277, 66, 56, 2], [2, "974", 0, 281, 26, 48, 0], [2, "977", 315, 145, 18, 36, 0], [2, "943", 836, 26, 14, 41, 0], [2, "943", 825, 258, 14, 41, 0], [2, "923", 670, 51, 44, 125, 0], [2, "925", 695, 245, 52, 34, 0], [2, "925", 713, 255, 52, 34, 0], [2, "925", 736, 266, 52, 34, 0], [2, "925", 760, 278, 52, 34, 0], [2, "925", 785, 291, 52, 34, 0], [2, "944", 632, 69, 12, 22, 0], [2, "944", 644, 73, 12, 22, 0], [2, "944", 634, 85, 12, 22, 0], [2, "944", 644, 89, 12, 22, 0], [2, "944", 633, 101, 12, 22, 0], [2, "944", 643, 105, 12, 22, 0], [2, "944", 633, 116, 12, 22, 0], [2, "944", 643, 122, 12, 22, 0], [2, "944", 659, 82, 12, 22, 0], [2, "944", 659, 98, 12, 22, 0], [2, "945", 687, 82, 10, 25, 0], [2, "945", 696, 77, 10, 25, 0], [2, "945", 706, 73, 10, 25, 0], [2, "931", 636, 109, 24, 37, 0], [2, "943", 764, 131, 14, 41, 0], [2, "943", 778, 137, 14, 41, 0], [2, "924", 685, 34, 58, 48, 0], [2, "690", 639, 68, 36, 85, 2], [2, "690", 612, 55, 36, 85, 2], [2, "943", 791, 179, 14, 41, 0], [2, "943", 791, 212, 14, 41, 0], [2, "943", 791, 147, 14, 41, 0], [2, "943", 749, 151, 14, 41, 0], [2, "943", 749, 184, 14, 41, 0], [2, "943", 749, 119, 14, 41, 0], [2, "923", 754, 158, 44, 125, 0], [2, "921", 710, 213, 56, 68, 0], [2, "690", 704, 104, 36, 85, 0], [2, "921", 781, 248, 56, 68, 0], [2, "922", 795, 157, 30, 108, 0], [2, "922", 723, 122, 30, 108, 0], [2, "941", 789, 138, 44, 39, 0], [2, "941", 717, 102, 44, 39, 0], [2, "924", 578, 9, 58, 48, 0], [2, "924", 605, 23, 58, 48, 0], [2, "924", 630, 36, 58, 48, 0], [2, "924", 656, 48, 58, 48, 0], [2, "924", 759, 24, 58, 48, 0], [2, "924", 734, 38, 58, 48, 0], [2, "924", 762, 52, 58, 48, 0], [2, "924", 791, 67, 58, 48, 0], [2, "924", 709, 49, 58, 48, 0], [2, "924", 737, 63, 58, 48, 0], [2, "924", 766, 78, 58, 48, 0], [2, "924", 794, 92, 58, 48, 0], [2, "924", 684, 63, 58, 48, 0], [2, "924", 712, 77, 58, 48, 0], [2, "924", 741, 92, 58, 48, 0], [2, "924", 769, 106, 58, 48, 0], [2, "923", 768, -21, 44, 125, 0], [2, "943", 754, 37, 14, 41, 0], [2, "943", 754, 9, 14, 41, 0], [2, "923", 687, -59, 44, 125, 0], [2, "921", 717, 26, 56, 68, 0], [2, "969", 381, 133, 36, 30, 0], [2, "925", 813, 305, 52, 34, 0], [2, "925", 837, 317, 52, 34, 0], [2, "925", 862, 330, 52, 34, 0], [2, "943", 841, 170, 14, 41, 0], [2, "943", 855, 176, 14, 41, 0], [2, "943", 868, 218, 14, 41, 0], [2, "943", 868, 251, 14, 41, 0], [2, "943", 868, 186, 14, 41, 0], [2, "943", 826, 190, 14, 41, 0], [2, "943", 825, 223, 14, 41, 0], [2, "943", 826, 158, 14, 41, 0], [2, "923", 831, 197, 44, 125, 0], [2, "921", 858, 287, 56, 68, 0], [2, "922", 872, 196, 30, 108, 0], [2, "941", 866, 177, 44, 39, 0], [2, "924", 827, 105, 58, 48, 0], [2, "924", 796, 120, 58, 48, 0], [2, "924", 856, 120, 58, 48, 0], [2, "924", 818, 131, 58, 48, 0], [2, "924", 846, 145, 58, 48, 0], [2, "924", 873, 159, 58, 48, 0], [2, "943", 836, 79, 14, 41, 0], [2, "943", 836, 51, 14, 41, 0], [2, "921", 795, 65, 56, 68, 0], [2, "922", 731, -67, 30, 108, 0], [2, "923", 600, -95, 44, 125, 0], [2, "921", 630, -18, 56, 68, 0], [2, "943", 585, -37, 14, 41, 0], [2, "923", 847, 20, 44, 125, 0], [2, "921", 875, 103, 56, 68, 0], [2, "922", 889, 10, 30, 108, 0], [2, "941", 884, -13, 44, 39, 0], [2, "924", 880, -36, 58, 48, 0], [2, "922", 810, -28, 30, 108, 0], [2, "925", 887, 343, 52, 34, 0], [2, "943", 135, 74, 14, 41, 2], [2, "943", 134, 105, 14, 41, 2], [2, "943", 136, 135, 14, 41, 2], [2, "943", 163, 58, 14, 41, 2], [2, "943", 172, 57, 14, 41, 2], [2, "943", 163, 91, 14, 41, 2], [2, "943", 170, 90, 14, 41, 2], [2, "943", 164, 124, 14, 41, 2], [2, "943", 173, 119, 14, 41, 2], [2, "943", 171, 137, 14, 41, 2], [2, "943", 78, 135, 14, 41, 2], [2, "943", 92, 129, 14, 41, 2], [2, "943", 78, 166, 14, 41, 2], [2, "943", 93, 160, 14, 41, 2], [2, "943", 80, 185, 14, 41, 2], [2, "943", 95, 177, 14, 41, 2], [2, "943", 107, 124, 14, 41, 2], [2, "943", 108, 155, 14, 41, 2], [2, "943", 108, 171, 14, 41, 2], [2, "943", 182, 76, 14, 41, 2], [2, "943", 197, 75, 14, 41, 2], [2, "943", 210, 67, 14, 41, 2], [2, "943", 210, 96, 14, 41, 2], [2, "943", 197, 104, 14, 41, 2], [2, "943", 182, 105, 14, 41, 2], [2, "943", 210, 112, 14, 41, 2], [2, "943", 198, 124, 14, 41, 2], [2, "943", 183, 123, 14, 41, 2], [2, "943", 185, 132, 14, 41, 2], [2, "943", 204, 121, 14, 41, 2], [2, "265", 115, 88, 68, 77, 2], [2, "3235", 135, 70, 22, 51, 0], [2, "3235", 121, 78, 22, 51, 0], [2, "3235", 162, 61, 22, 51, 0], [2, "1218", 161, 54, 28, 82, 0], [2, "3235", 143, 64, 22, 51, 0], [2, "921", 562, 140, 56, 68, 0], [2, "922", 576, 46, 30, 108, 0], [2, "921", 630, 174, 56, 68, 0], [2, "922", 643, 79, 30, 108, 0], [2, "733", 570, 40, 54, 35, 2], [2, "733", 626, 70, 54, 35, 2], [2, "921", 7, 432, 56, 68, 0], [2, "921", 80, 474, 56, 68, 0], [2, "921", 160, 517, 56, 68, 0], [2, "921", 239, 558, 56, 68, 0], [2, "921", 322, 603, 56, 68, 0], [2, "921", 407, 647, 56, 68, 0], [2, "3285", 332, 319, 166, 79, 0], [2, "3290", 383, 341, 76, 34, 0], [2, "181", 114, 128, 104, 100, 2], [2, "180", 112, 72, 104, 84, 2], [2, "921", 87, 166, 56, 68, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 30, 30, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 15, -1, 30, 30, 30, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 15, 15, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, 21, 30, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 39, -1, -1, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, 24, 30, -1, 24, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 36, 37, 39, 39, 39, 24, -1, -1, -1, 0, 5, -1, -1, -1, -1, -1, -1, 30, 30, -1, 24, 0, 1, -1, -1, -1, -1, -1, -1, -1, 24, 0, 7, 6, 5, 24, 33, 34, -1, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, 6, 5, 0, 1, 2, -1, -1, -1, -1, -1, 24, 24, 0, 1, 2, -1, 7, 7, 6, 5, 24, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, 24, 24, 24, 24, 24, 5, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, 24, -1, -1, -1, -1, -1, -1, 24, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, -1, -1, -1, -1, 36, 36, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, -1, -1, -1, 2, 37, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 24, 24, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 10, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 47, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, 44, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 48, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, 44, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, -1, -1, -1, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, -1, -1, -1, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, -1, -1, -1, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 44, 45, -1, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 45, 44, 45, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 45, 44, 45, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 45, 44, 45, 54, 55, 56, 48, 49, 50, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 45, 44, 45, 44, 45, 44, 51, 52, 53, 44, 45, 44, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 48, 49, 50, 45, 44, 45, 44, 45, 44, 54, 55, 56, 44, 45, 44, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 51, 52, 53, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 45, 44, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56, 54, 55, 56]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}