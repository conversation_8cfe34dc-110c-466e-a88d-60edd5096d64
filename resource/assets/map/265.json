{"mW": 720, "mH": 1008, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["373", 0, 1, 1], ["203_10", 0, 2, 1], ["203_11", 0, 2, 1], ["203_12", 0, 2, 1], ["203_11", 1, 2, 1], ["203_12", 1, 2, 1], ["203_10", 1, 2, 1], ["203_9", 0, 2, 1], ["203_9", 1, 2, 1], ["380", 0, 3, 2], ["380", 2, 3, 2], ["380", 1, 3, 2], ["380", 3, 3, 2], ["381", 0, 3, 2], ["382", 0, 1, 3], ["383", 0, 2, 3], ["383", 2, 2, 3], ["382", 2, 1, 3], ["381", 2, 3, 2], ["383", 1, 2, 3], ["384", 0, 1, 3], ["384", 2, 1, 3], ["111", 0, 3, 2], ["111", 2, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["996", 0, 2, 2], ["995", 0, 3, 2], ["995", 2, 3, 2], ["995", 1, 3, 2], ["995", 3, 3, 2], ["444_2", 0, 2, 2]], "layers": [{"type": 3, "obj": [[2, "87_1", 30, 319, 72, 57, 0], [2, "385", 678, 267, 72, 48, 0], [2, "385", 558, 127, 72, 48, 0], [2, "385", 417, 318, 72, 48, 0], [2, "268_1", 290, -45, 106, 82, 2], [2, "88_1", 251, 3, 88, 61, 2], [2, "87_1", 334, 12, 72, 57, 2], [2, "268_1", 472, 41, 106, 82, 2], [2, "88_1", 545, 87, 88, 61, 2], [2, "268_1", -13, 264, 106, 82, 2], [2, "88_1", -57, 315, 88, 61, 2], [2, "268_1", 594, 554, 106, 82, 2], [2, "88_1", 661, 594, 88, 61, 2], [2, "268_1", 154, 435, 106, 82, 2], [2, "88_1", 213, 480, 88, 61, 2], [2, "88_1", 135, 493, 88, 61, 2], [2, "268_1", 158, 846, 106, 82, 2], [2, "88_1", 225, 886, 88, 61, 2], [2, "919", 404, 349, 50, 80, 0], [2, "268_1", 303, 674, 106, 82, 2], [2, "88_1", 371, 716, 88, 61, 2], [2, "88_1", 284, 732, 88, 61, 2], [2, "917", 511, 592, 56, 55, 0], [2, "429", 269, 954, 64, 63, 0]]}, {"type": 4, "obj": [[2, "89", 307, 25, 48, 95, 0], [2, "429", -1, 70, 64, 63, 2], [2, "363", 135, 90, 54, 75, 0], [2, "365", -3, 74, 48, 94, 0], [2, "366", 37, 120, 32, 48, 0], [2, "263_2", 509, 137, 34, 34, 0], [2, "320_1", 170, 70, 62, 103, 0], [2, "263_2", 153, 141, 34, 34, 0], [2, "328", -1, 149, 32, 29, 0], [2, "86", 126, 129, 50, 49, 0], [2, "364", 200, 118, 44, 64, 0], [2, "329", 223, 147, 42, 37, 0], [2, "366", 196, 139, 32, 48, 2], [2, "328", 199, 166, 32, 29, 0], [2, "89", 526, 101, 48, 95, 2], [2, "328", 558, 178, 32, 29, 2], [2, "429", 676, 190, 64, 63, 0], [2, "422", 209, 294, 16, 14, 2], [2, "328", 391, 338, 32, 29, 2], [2, "422", 429, 361, 16, 14, 0], [2, "366", 404, 330, 32, 48, 0], [2, "365", 425, 284, 48, 94, 0], [2, "329", 299, 342, 42, 37, 0], [2, "328", 403, 350, 32, 29, 0], [2, "366", 467, 333, 32, 48, 0], [2, "421", 308, 377, 14, 11, 2], [2, "327", 461, 368, 30, 22, 0], [2, "364", 270, 331, 44, 64, 0], [2, "422", 286, 381, 16, 14, 0], [2, "364", 491, 333, 44, 64, 2], [2, "327", 693, 375, 30, 22, 0], [2, "422", 505, 386, 16, 14, 0], [2, "329", 515, 365, 42, 37, 0], [2, "363", 227, 351, 54, 75, 0], [2, "89", -6, 338, 48, 95, 0], [2, "366", 570, 401, 32, 48, 0], [2, "328", 571, 436, 32, 29, 2], [2, "364", 206, 404, 44, 64, 0], [2, "364", 587, 412, 44, 64, 2], [2, "329", 606, 441, 42, 37, 0], [2, "366", 214, 442, 32, 48, 0], [2, "920", 409, 409, 44, 88, 0], [2, "366", 620, 476, 32, 48, 2], [2, "328", 626, 506, 32, 29, 0], [2, "365", 590, 454, 48, 94, 2], [2, "366", 609, 525, 32, 48, 0], [2, "327", 617, 562, 30, 22, 0], [2, "327", 358, 567, 30, 22, 0], [2, "366", 592, 545, 32, 48, 0], [2, "365", 552, 502, 48, 94, 0], [2, "89", 200, 504, 48, 95, 2], [2, "329", 592, 562, 42, 37, 0], [2, "327", 542, 580, 30, 22, 0], [2, "328", 564, 578, 32, 29, 0], [2, "364", 330, 558, 44, 64, 2], [2, "429", -19, 565, 64, 63, 2], [2, "363", 365, 554, 54, 75, 0], [2, "329", 375, 595, 42, 37, 0], [2, "89", 633, 603, 48, 95, 2], [2, "890", 489, 609, 94, 105, 0], [2, "329", 636, 693, 42, 37, 0], [2, "328", 250, 721, 32, 29, 0], [2, "327", 448, 749, 30, 22, 0], [2, "328", 471, 748, 32, 29, 0], [2, "263_2", 228, 776, 34, 34, 0], [2, "263_2", 122, 782, 34, 34, 0], [2, "89", 365, 742, 48, 95, 2], [2, "263_2", 481, 820, 34, 34, 0], [2, "328", 25, 857, 32, 29, 0], [2, "328", 129, 907, 32, 29, 0]]}, {"type": 3, "obj": [[2, "325", 31, 573, 50, 37, 0], [2, "21", 1, 743, 28, 24, 0], [2, "253", 529, 704, 92, 53, 2], [2, "921", 546, 663, 56, 68, 2], [2, "253", 413, 709, 92, 53, 2], [2, "921", 476, 696, 56, 68, 2], [2, "325", 651, 352, 50, 37, 0], [2, "325", 58, 195, 50, 37, 0], [2, "126", 581, 58, 150, 54, 0], [2, "318", 599, 711, 18, 54, 0], [2, "317", 592, 718, 22, 42, 0], [2, "317", 602, 717, 22, 42, 0], [2, "253", 244, 361, 92, 53, 0], [2, "307", 336, 239, 42, 19, 0], [2, "325", 100, 436, 50, 37, 0], [2, "174_2", 105, 418, 68, 33, 2], [2, "325", 1415, 504, 50, 37, 0], [2, "325", 362, 206, 50, 37, 0], [2, "324", 591, 161, 70, 35, 2], [2, "326", 121, 459, 18, 14, 0], [2, "325", 543, 576, 50, 37, 2], [2, "319", 372, 753, 62, 65, 2], [2, "328", 180, 252, 32, 29, 0], [2, "22", 328, 195, 62, 38, 0], [2, "324", 659, 187, 70, 35, 2], [2, "21", 61, 198, 28, 24, 0], [2, "328", 575, 573, 32, 29, 0], [2, "329", 535, 555, 42, 37, 0], [2, "325", 433, 753, 50, 37, 2], [2, "326", 454, 764, 18, 14, 0], [2, "327", 124, 430, 30, 22, 0], [2, "381", 659, 116, 72, 48, 0], [2, "325", 680, 372, 50, 37, 2], [2, "174_2", 82, 107, 68, 33, 1], [2, "174_2", 144, 289, 68, 33, 1], [2, "307", 346, 711, 42, 19, 2], [2, "307", 354, 666, 42, 19, 2], [2, "325", 161, 946, 50, 37, 0], [2, "328", 194, 922, 32, 29, 0], [2, "253", 171, 575, 92, 53, 0], [2, "366", -2, 624, 32, 48, 0], [2, "307", 195, 606, 42, 19, 0], [2, "308", 192, 576, 52, 22, 0], [2, "319", 426, 784, 62, 65, 2], [2, "362", 449, 767, 64, 42, 0], [2, "319", 305, 739, 62, 65, 0], [2, "319", 251, 737, 62, 65, 0], [2, "371", 320, 741, 26, 48, 2], [2, "371", 291, 750, 26, 48, 2], [2, "318", 358, 736, 18, 54, 2], [2, "371", 308, 756, 26, 48, 2], [2, "362", 666, 346, 64, 42, 0], [2, "325", 547, 413, 50, 37, 0], [2, "319", 526, 110, 62, 65, 0], [2, "322", 536, 20, 52, 101, 2], [2, "372", 556, 123, 44, 50, 0], [2, "318", 575, 85, 18, 54, 2], [2, "385", 596, 73, 72, 48, 0], [2, "329", 574, 142, 42, 37, 0], [2, "366", 651, 158, 32, 48, 0], [2, "371", 185, 753, 26, 48, 2], [2, "319", 200, 734, 62, 65, 0], [2, "319", 158, 742, 62, 65, 2], [2, "319", 98, 749, 62, 65, 0], [2, "318", 95, 778, 18, 54, 2], [2, "371", 111, 781, 26, 48, 2], [2, "318", 149, 751, 18, 54, 2], [2, "318", 84, 777, 18, 54, 2], [2, "319", 28, 779, 62, 65, 2], [2, "361", -8, 759, 58, 104, 4], [2, "126", 1460, 475, 150, 54, 0], [2, "253", 132, 161, 92, 53, 0], [2, "329", 9, 827, 42, 37, 2], [2, "329", -5, 835, 42, 37, 0], [2, "366", 39, 752, 32, 48, 0], [2, "328", 153, 782, 32, 29, 0], [2, "327", 572, 701, 30, 22, 0], [2, "253", 222, 387, 92, 53, 0], [2, "253", 292, 570, 92, 53, 2], [2, "253", 442, 354, 92, 53, 2], [2, "325", 530, 570, 50, 37, 0], [2, "325", 392, 592, 50, 37, 2], [2, "325", 219, 448, 50, 37, 2], [2, "325", 12, 648, 50, 37, 2], [2, "328", 44, 645, 32, 29, 0], [2, "329", 62, 633, 42, 37, 0], [2, "327", 34, 620, 30, 22, 0], [2, "327", 44, 634, 30, 22, 0], [2, "326", 53, 610, 18, 14, 0], [2, "86", 201, 758, 50, 49, 0], [2, "328", -1, 681, 32, 29, 0], [2, "329", -14, 672, 42, 37, 0], [2, "327", 17, 674, 30, 22, 0], [2, "325", 486, 142, 50, 37, 0], [2, "327", 165, 931, 30, 22, 0], [2, "263_2", 630, 161, 34, 34, 0], [2, "325", 10, 29, 50, 37, 0], [2, "325", 566, 266, 50, 37, 0], [2, "327", 1, 865, 30, 22, 0], [2, "326", 547, 428, 18, 14, 0], [2, "326", 367, 237, 18, 14, 0], [2, "327", 191, 582, 30, 22, 0], [2, "327", 547, 583, 30, 22, 0], [2, "327", 333, 245, 30, 22, 0], [2, "327", 180, 183, 30, 22, 0], [2, "420", 397, 569, 16, 13, 0], [2, "420", 53, 599, 16, 13, 0], [2, "420", 615, 688, 16, 13, 0], [2, "423", 509, 757, 4, 6, 0], [2, "327", -5, 873, 30, 22, 0], [2, "420", 476, 382, 16, 13, 0], [2, "420", 499, 881, 16, 13, 0], [2, "420", 395, 615, 16, 13, 0], [2, "420", 532, 583, 16, 13, 2], [2, "420", 252, 418, 16, 13, 0], [2, "420", 585, 291, 16, 13, 0], [2, "421", 669, 383, 14, 11, 0], [2, "421", 549, 194, 14, 11, 0], [2, "421", 483, 153, 14, 11, 0], [2, "325", 461, 346, 50, 37, 0], [2, "420", 284, 742, 16, 13, 0], [2, "421", 474, 838, 14, 11, 0], [2, "421", 277, 789, 14, 11, 0], [2, "421", 189, 799, 14, 11, 0], [2, "420", 200, 804, 16, 13, 0], [2, "420", 130, 754, 16, 13, 0], [2, "420", 76, 838, 16, 13, 0], [2, "323", 504, -21, 46, 70, 2], [2, "325", 618, 790, 50, 37, 0], [2, "325", 608, 669, 50, 37, 2], [2, "326", 629, 680, 18, 14, 0], [2, "325", 674, 682, 50, 37, 0], [2, "319", 692, 711, 62, 65, 0], [2, "371", 686, 725, 26, 48, 2], [2, "319", 641, 712, 62, 65, 0], [2, "371", 654, 743, 26, 48, 2], [2, "319", 602, 724, 62, 65, 2], [2, "328", 701, 881, 32, 29, 0], [2, "324", 603, 715, 70, 35, 2], [2, "325", 681, 839, 50, 37, 2], [2, "326", 702, 850, 18, 14, 0], [2, "371", 669, 745, 26, 48, 2], [2, "361", 596, 696, 58, 104, 6], [2, "364", 659, 740, 44, 64, 0], [2, "361", 631, 712, 58, 104, 4], [2, "329", 626, 775, 42, 37, 0], [2, "328", 680, 773, 32, 29, 0], [2, "329", 704, 759, 42, 37, 0], [2, "327", 679, 835, 30, 22, 0], [2, "253", 547, 532, 92, 53, 0], [2, "429", 648, 776, 64, 63, 0], [2, "420", 620, 801, 16, 13, 0], [2, "319", 483, 783, 62, 65, 0], [2, "36_1", 502, 730, 140, 103, 2], [2, "361", 484, 744, 58, 104, 5], [2, "328", 531, 811, 32, 29, 0], [2, "329", 523, -4, 42, 37, 0], [2, "307", 176, 735, 42, 19, 0], [2, "307", 136, 741, 42, 19, 0], [2, "307", 165, 725, 42, 19, 2], [2, "328", 163, 734, 32, 29, 0], [2, "325", 191, 716, 50, 37, 0], [2, "420", 213, 720, 16, 13, 0], [2, "327", 227, 726, 30, 22, 0], [2, "21", 226, 706, 28, 24, 0], [2, "329", 51, 766, 42, 37, 2], [2, "325", 477, 859, 50, 37, 0], [2, "328", 464, 728, 32, 29, 0], [2, "327", 492, 737, 30, 22, 0], [2, "329", 441, 729, 42, 37, 0], [2, "327", 502, 837, 30, 22, 0], [2, "420", 528, 840, 16, 13, 0], [2, "328", 528, 835, 32, 29, 0], [2, "326", 493, 869, 18, 14, 0], [2, "326", 518, 854, 18, 14, 0], [2, "329", 444, 814, 42, 37, 0], [2, "420", 390, 622, 16, 13, 0], [2, "420", 535, 860, 16, 13, 0], [2, "422", 361, 213, 16, 14, 0], [2, "420", 384, 212, 16, 13, 0], [2, "420", 651, 199, 16, 13, 0], [2, "327", 672, 183, 30, 22, 0], [2, "420", 79, 213, 16, 13, 0], [2, "329", 86, 423, 42, 37, 2], [2, "420", 474, 514, 16, 13, 0], [2, "420", 604, 754, 16, 13, 0], [2, "326", 594, 271, 18, 14, 0], [2, "326", 145, 441, 18, 14, 0], [2, "326", 530, 877, 18, 14, 0], [2, "326", 20, 884, 18, 14, 0], [2, "327", 505, 860, 30, 22, 0], [2, "89", 207, 907, 48, 95, 2], [2, "328", 258, 981, 32, 29, 0], [2, "325", 671, 969, 50, 37, 0], [2, "328", 50, 973, 32, 29, 0], [2, "327", 553, 981, 30, 22, 0], [2, "326", 709, 971, 18, 14, 0], [2, "327", 298, 232, 30, 22, 0], [2, "325", 202, 275, 50, 37, 0], [2, "328", 224, 290, 32, 29, 0], [2, "326", 228, 280, 18, 14, 0], [2, "328", 140, 24, 32, 29, 0], [2, "326", 132, 47, 18, 14, 0], [2, "429", 74, -29, 64, 63, 0], [2, "328", -9, 709, 32, 29, 0]]}, {"type": 2, "data": [61, 60, 59, 15, 1, 2, -1, -1, -1, 41, 42, 43, -1, -1, -1, -1, -1, -1, 49, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, 47, 20, 19, 18, -1, -1, -1, 53, 54, 55, -1, -1, -1, -1, -1, -1, 56, 63, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 64, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 1, 1, 1, 7, 6, -1, 56, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, -1, -1, -1, 0, 1, -1, -1, -1, -1, 20, 19, 19, 23, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 5, 6, -1, -1, 3, 4, -1, 8, 7, 6, -1, -1, -1, 12, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 7, 6, -1, 0, 7, -1, -1, -1, 9, -1, 49, 47, -1, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, 13, 12, 13, 14, -1, 12, 19, 13, -1, -1, 107, 113, 53, 60, 59, -1, -1, -1, -1, 12, 13, 23, 16, 16, 7, 6, 8, -1, -1, -1, -1, -1, 0, 6, -1, 122, 108, 109, 107, 114, 118, 116, -1, -1, -1, 7, 6, -1, -1, -1, -1, 12, 13, 13, 19, 18, 20, 13, 18, -1, -1, 41, 42, 43, -1, 122, -127, 124, 126, -126, 111, 116, -1, -1, -1, -1, 18, -1, -1, 41, 42, 43, -1, -1, -1, -1, -1, -1, 0, 1, -1, 61, 60, 59, -1, 127, 126, 125, -1, 127, -126, 112, 113, -1, -1, -1, -1, -1, -1, 44, 45, 46, 48, 48, 48, 47, -1, -1, 12, 13, -1, -1, -1, -1, -1, -1, -1, 12, 14, -1, 127, -126, 112, 113, -1, 41, 43, -1, -1, 53, 64, 57, 57, 63, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 48, 47, -1, 6, 127, -126, 112, 113, 61, 46, 47, -1, -1, 61, 64, 57, 58, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 52, 57, 46, 47, -1, -1, 119, -126, 112, 113, 53, 59, -1, 7, 6, 61, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 54, 60, 59, 8, 7, -1, 122, 123, 116, -1, -1, -1, 10, 9, 7, 6, -1, -1, -1, -1, 0, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 10, -1, 127, -126, 112, 113, 20, 19, 18, -1, 10, 9, -1, -1, -1, -1, 3, 4, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, 43, -1, 16, 17, 18, 122, -127, -128, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, 12, 13, 13, 8, 7, 6, -1, -1, -1, -1, 53, 54, 55, 17, 13, 14, -1, 127, 126, 125, -1, -1, -1, -1, 53, 59, 7, -97, -1, -1, -1, -1, -1, 20, 19, 18, 0, 1, 2, -1, -1, 0, -1, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 18, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, 3, -1, 60, 48, 47, -1, -1, -1, 8, 7, 7, 6, -1, -1, -1, 20, 19, -1, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 60, -1, -1, -1, -1, 12, 13, 19, 18, 41, 48, 47, -1, 0, -1, 16, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 59, -1, -1, 3, -1, -1, 18, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 48, 48, 47, -1, 107, 108, 113, -1, -1, -1, -1, -1, -1, -1, 16, 21, 44, 45, -1, -1, -1, -1, -1, -1, 8, 7, 7, 6, -1, 53, 54, 60, 59, -1, 127, 124, -126, 108, 113, 8, 7, 6, 6, -1, 17, 18, 61, 60, -1, -1, -1, -1, -1, -1, 12, 13, 23, 22, 6, 8, 7, 6, -1, -1, -1, 121, 122, 111, 116, 20, 19, 16, 16, 17, 18, -1, -117, -1, 22, 7, 6, -104, -102, -103, -1, -1, 20, 19, 18, 20, 19, 13, 23, 16, -1, -1, 122, -127, 112, 113, -1, 19, 19, 18, -1, -1, -1, -1, 17, 19, 18, -107, -1, -1, -1, -1, -1, 49, 48, 48, 47, -1, 20, 19, 18, -1, 122, 124, -126, 116, -1, 49, 48, 48, 47, -1, -1, -1, 0, 1, 17, -1, -1, -1, -1, -1, -1, 53, 54, 54, 55, -1, -1, -1, -1, -1, 127, 125, 122, 116, -1, 61, 60, 60, 59, -1, -1, -1, 12, 19, 18, 41, 42, 43, -1, 49, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 122, 112, 113, -1, 7, 6, 61, 1, 2, 8, 21, -1, -1, 53, 64, 46, 48, 52, 51, 50, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, 119, -126, 113, -1, 10, 9, 3, 4, 5, 11, 18, -1, -1, -1, 61, 60, 60, 54, 54, 60, -1, -1, 12, 13, 14, -1, -1, -1, -1, -1, 0, 1, 2, 113, -1, 12, 13, 14, 16, 16, 1, -1, -1, -1, -1, -1, -1, 57, -1, 46, 48, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, 110, 117, 116, -1, -1, -1, 11, 10, 16, 12, 0, 1, 2, -1, -1, 61, 60, 60, 60, 60, 59, -1, -1, 10, 10, -1, 0, 1, 2, 2, 127, -126, 116, -1, -1, -1, -1, -1, 17, -1, -1, -1, -1, -1, 22, 17, 19, 19, 19, 19, 19, 19, 13, 23, 10, 10, 16, 10, 10, -1, -1, -1, -1, -1, -1, -1, -1, 22, 21, -1, -1, 22, 17, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, 12, 13, 19, 19, 23, -1, -1, -1, 22, 21, -1, -1, -1, -1, 19, 18, -1, 22, 17, 18, -1, -1, -1, -1, 41, 42, 43, -1, -1, -1, -1, -1, -1, -1, 20, 19, 13, 13, 13, 18, 114, 113, -1, -1, 20, 18, -1, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, 107, 108, 108, 109, -1, -1, -1, 41, 42, 43, -1, -1, 110, 117, 116, -1, -1, -1, -1, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 126, 126, 126, -126, 108, 114, 113, 61, 60, 59, -1, -1, 110, 111, -128, 49, 48, 48, 47, -1, -1, 122, 114, 113, 0, 17, 13, 14, 21, -1, -1, -1, -1, -1, 119, -126, 112, 109, -1, -1, -1, 107, 118, 124, 125, -1, 53, 64, 63, -1, -1, 119, 126, 125, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, 127, 111, 112, 108, 108, 108, 118, 111, 116, -1, -1, -1, 53, 54, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 6, -1, -1, -1, -1, -1, 119, 120, 126, -126, -127, 124, 125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, 127, 126, 125, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "253", 354, 423, 92, 53, 0], [2, "544", 345, 433, 86, 107, 2], [2, "544", 429, 434, 86, 107, 0], [2, "984", 356, 435, 76, 52, 0], [2, "984", 429, 435, 76, 52, 2], [2, "984", 429, 478, 76, 52, 3], [2, "984", 356, 478, 76, 52, 1]]}, {"type": 2, "data": [-97, -96, -97, -96, -111, -114, -115, -125, -118, -99, -99, -99, -1, -1, -1, -1, -111, -97, -96, -117, -117, -117, 37, 37, 37, 37, 37, 37, 37, 37, -95, -94, -97, -96, -111, -105, -112, -123, -101, -98, -99, -1, -1, -111, -1, -1, -97, -95, -94, -97, -97, -96, -117, 38, 38, 38, 38, 38, 38, 38, -111, -97, -96, -94, -111, -117, -116, -115, -125, -101, -98, -99, -99, -99, -97, -96, -96, -97, -96, -95, -95, -94, -117, 25, 25, 25, 25, 25, 25, 25, -99, -95, -94, -97, -96, -117, -117, -112, -123, -122, -109, -102, -98, -97, -96, -97, -96, -95, -94, -117, -111, -117, -117, -1, 26, 26, 26, 26, 26, 26, -99, -111, -99, -95, -94, -117, -117, -1, -1, -114, -115, -124, -109, -95, -97, -96, -96, -97, -96, -96, -111, -117, -117, -1, 28, 28, 28, 28, 28, 28, -99, -99, -111, -99, -117, -117, -117, -117, -117, -117, -100, -122, -121, -117, -95, -94, -96, -95, -94, -94, -111, -117, -117, -1, 28, 32, 32, 32, 32, 32, -1, -1, -102, -102, -99, -99, -117, -1, -117, -117, -103, -124, -118, -97, -97, -96, -96, -97, -96, -97, -96, -96, -1, -1, 31, 31, 31, 31, 31, 31, -104, -122, -125, -124, -101, -98, -102, -117, -117, -104, -103, -122, -118, -95, -96, -96, -94, -95, -94, -96, -96, -96, -97, -96, -111, -111, -111, 36, 36, 36, -116, -114, -114, -120, -115, -125, -124, -101, -102, -103, -125, -121, -110, -105, -97, -96, -97, -97, -96, -117, -111, -96, -96, -96, -117, -111, -96, -111, -111, -111, -97, -96, -111, -111, -112, -123, -122, -124, -125, -124, -123, -106, -111, -111, -95, -94, -96, -95, -94, -117, -99, -105, -117, -96, -96, -96, -96, -96, -117, -111, -95, -94, -96, -111, -116, -115, -123, -113, -114, -115, -123, -101, -102, -98, -111, -111, -97, -96, -117, -105, -111, -117, -105, -105, -105, -105, -96, -96, -111, -111, -95, -94, -94, -97, -96, -116, -114, -110, -111, -111, -115, -125, -124, -109, -111, -111, -95, -94, -96, -105, -111, -117, -105, -105, -105, -96, -96, -96, -105, -104, -99, -117, -1, -95, -94, -111, -111, -111, -111, -111, -116, -115, -125, -124, -109, -98, -96, -97, -96, -96, -105, -117, -105, -105, -117, -105, -117, -104, -108, -107, -99, -1, -97, -97, -96, -111, -111, -111, -111, -111, -111, -112, -123, -125, -124, -101, -95, -95, -94, -96, -96, -105, -105, -105, -111, -104, -108, -107, -125, -124, -117, -97, -96, -97, -96, -99, -99, -99, -99, -111, -111, -116, -114, -115, -125, -124, -106, -99, -99, -94, -111, -96, -96, -96, -111, -107, -125, -124, -123, -122, -117, -97, -96, -95, -94, -111, -117, -99, -111, -99, -97, -96, -111, -112, -123, -122, -109, -117, -117, -111, -111, -111, -95, -94, -111, -112, -125, -124, -1, -1, -99, -95, -94, -97, -97, -96, -97, -96, -117, -97, -96, -96, -117, -100, -125, -124, -124, -109, -102, -111, -111, -111, -99, -111, -111, -119, -123, -122, -125, -124, -98, -97, -96, -95, -95, -94, -95, -94, -111, -95, -94, -94, -117, -100, -123, -122, -125, -125, -124, -109, -102, -98, -99, -99, -1, -116, -114, -115, -123, -122, -106, -95, -94, -117, -117, -117, -111, -117, -97, -96, -117, -117, -104, -103, -125, -124, -123, -123, -122, -125, -124, -101, -98, -111, -111, -111, -111, -116, -114, -115, -109, -108, -98, -117, -117, -117, -117, -117, -95, -94, -96, -104, -103, -123, -125, -124, -1, -1, -1, -123, -125, -124, -106, -111, -105, -111, -105, -105, -111, -116, -125, -124, -101, -102, -98, -117, -105, -105, -117, -95, -94, -116, -123, -122, -122, -1, -1, -1, -1, -1, -123, -122, -109, -98, -105, -105, -105, -105, -97, -96, -114, -115, -125, -124, -118, -104, -105, -1, -105, -105, -111, -105, -114, -119, -122, -124, -1, -1, -1, -111, -116, -115, -122, -101, -105, -105, -105, -97, -95, -94, -102, -103, -113, -110, -104, -103, -106, -117, -105, -111, -111, -111, -111, -116, -119, -122, -101, -102, -98, -98, -111, -116, -115, -122, -105, -105, -105, -95, -97, -96, -113, -113, -110, -105, -116, -115, -106, -105, -105, -97, -96, -99, -97, -96, -116, -114, -115, -125, -124, -101, -98, -99, -116, -117, -1, -1, -105, -117, -95, -94, -110, -99, -99, -99, -99, -100, -109, -98, -97, -96, -94, -96, -95, -94, -1, -117, -1, -115, -125, -124, -101, -102, -98, -1, -1, -1, -99, -95, -94, -99, -99, -99, -99, -99, -99, -103, -121, -110, -95, -94, -95, -94, -117, -117, -117, -117, -99, -114, -115, -125, -124, -123, -106, -97, -96, -105, -105, -105, -99, -117, -99, -96, -99, -103, -113, -120, -110, -97, -96, -96, -97, -96, -117, -105, -117, -117, -105, -104, -103, -123, -122, -124, -118, -95, -94, -105, -105, -97, -96, -99, -99, -96, -117, -114, -110, -117, -96, -95, -94, -94, -96, -96, -104, -108, -102, -99, -105, -116, -115, -125, -124, -122, -118, -99, -117, -117, -105, -95, -94, -99, -99, -99, -96, -111, -111, -105, -96, -117, -99, -99, -96, -104, -103, -125, -124, -106, -99, -105, -100, -123, -122, -124, -109, -99, -97, -96, -117, -105, -117, -99, -111, -105, -105, -99, -105, -105, -105, -99, -99, -99, -99, -100, -123, -122, -122, -1, -99, -104, -103, -125, -124, -124, -124, -117, -95, -94, -117, -117, -117, -99, -111, -104, -108, -102, -98, -99, -105, -105, -105, -99, -105, -116, -114, -114, -1, -1, -1, -103, -125, -123, -122, -122, -122, -124, -117, -117, -117, -117, -117, -117, -111, -100, -125, -124, -101, -102, -105, -105, -105, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -1, -1, -1, -1, -122, -1, -1, -1, -1, -1, -1, -117, -117, -117, -117, -117, -117, -117, -117, -117, -1, -117, -117, -117, -117, -99, -99, -117, -117, -117, -117, -1, -1, -1, -1, -1, -117, -117, -117, -117, -99, -117, -1, -1, -117, -117, -117, -117, -111, -99, -99, -96, -96, -96, -96, -96, -99, -99, -117, -117, -117, -117, -1, -1, -1, -1, -117, -101, -1, -1, -105, -117, -117, -117, -117, -117, -117, -117, -111, -105, -99, -104, -102, -98, -96, -94, -99, -97, -96, -111, -1, -1, -1, -1, -104, -102, -103, -101, -98, -105, -105, -117, -117, -117, -117, -111, -111, -111, -117, -111, -111, -103, -125, -101, -108, -98, -99, -95, -94, -96, -97, -96, -111, -111, -100, -125, -124, -125, -109, -98, -111, -117, -117, -117, -117, -104, -108, -108, -108, -108, -125, -124, -125, -124, -125, -101, -98, -111, -111, -96, -96, -94, -111, -111, -112, -123, -122, -113, -114, -110, -97, -105, -105, -104, -102, -107, -125, -125, -1, -1, -123, -122, -123, -122, -123, -122, -109, -102, -102, -102, -98, -99, -104, -102, -103, -125, -121, -110, -111, -96, -95, -109, -108, -107, -123, -122, -121, -120, -120, -105, -1, -1, -1, -120, -120, -120, -119, -125, -124, -125, -109, -108, -103, -125, -125, -124, -109, -98, -105, -96, -97, -123, -123, -122, -121, -114, -110, -96, -97, -105, -97, -96, -97, -96, -97, -96, -116, -115, -123, -122, -123, -125, -113, -114, -123, -122, -125, -118, -97, -105, -96, -121, -120, -120, -110, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -116, -119, -113, -120, -120, -110, -96, -116, -114, -114, -110, -95, -94, -96, -95, -94, -97, -96, -95, -94, -95, -94, -97, -96, -95, -94, -95, -94, -95, -94, -95, -116, -110, -94, -95, -94, -94, -94, -111, -111, -95, -94, -95, -94]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}