{"mW": 960, "mH": 840, "tW": 24, "tH": 24, "tiles": [["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["315", 0, 3, 3], ["369", 0, 3, 3], ["369", 2, 3, 3], ["369", 1, 3, 3], ["444_1", 0, 2, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["380", 0, 3, 2], ["380", 2, 3, 2], ["380", 1, 3, 2], ["380", 3, 3, 2], ["450", 0, 1, 1], ["369", 3, 3, 3]], "layers": [{"type": 3, "obj": [[2, "232_1", 830, 503, 46, 45, 2], [2, "1355", 854, 470, 36, 47, 2], [2, "1352", 777, 493, 92, 52, 0], [2, "1354", 769, 439, 46, 75, 0], [2, "1354", 797, 450, 46, 75, 0], [2, "1354", 822, 463, 46, 75, 0], [2, "259", 898, 475, 18, 32, 2], [2, "1354", 856, 476, 46, 75, 0], [2, "260", 902, 495, 26, 34, 2], [2, "237", 804, 479, 36, 22, 2], [2, "237", 828, 491, 36, 22, 2], [2, "233_1", 794, 441, 44, 54, 2], [2, "233_1", 818, 449, 44, 54, 2], [2, "237", 788, 471, 36, 22, 2], [2, "237", 818, 486, 36, 22, 2], [2, "237", 847, 500, 36, 22, 2], [2, "237", 805, 432, 36, 22, 2], [2, "237", 825, 441, 36, 22, 2], [2, "237", 849, 452, 36, 22, 2], [2, "237", 872, 462, 36, 22, 2], [2, "385", 770, 800, 72, 48, 0], [2, "385", 792, 813, 72, 48, 0], [2, "385", 106, 804, 72, 48, 0], [2, "385", 297, 745, 72, 48, 0], [2, "385", 550, 795, 72, 48, 0], [2, "385", 141, 793, 72, 48, 2], [2, "385", 270, 819, 72, 48, 2], [2, "385", 671, 820, 72, 48, 2], [2, "457", 837, 461, 70, 59, 0], [2, "457", 781, 451, 70, 59, 0], [2, "232_1", 834, 75, 46, 45, 2], [2, "1355", 858, 42, 36, 47, 2], [2, "1352", 781, 65, 92, 52, 0], [2, "1354", 773, 11, 46, 75, 0], [2, "1354", 801, 22, 46, 75, 0], [2, "1354", 826, 35, 46, 75, 0], [2, "259", 902, 47, 18, 32, 2], [2, "1354", 860, 48, 46, 75, 0], [2, "260", 906, 67, 26, 34, 2], [2, "237", 808, 51, 36, 22, 2], [2, "237", 832, 63, 36, 22, 2], [2, "233_1", 798, 13, 44, 54, 2], [2, "233_1", 822, 21, 44, 54, 2], [2, "237", 792, 43, 36, 22, 2], [2, "237", 822, 58, 36, 22, 2], [2, "237", 851, 72, 36, 22, 2], [2, "237", 809, 4, 36, 22, 2], [2, "237", 829, 13, 36, 22, 2], [2, "237", 853, 24, 36, 22, 2], [2, "237", 876, 34, 36, 22, 2], [2, "457", 774, 42, 70, 59, 0], [2, "457", 817, 64, 70, 59, 0], [2, "457", 841, 28, 70, 59, 0], [2, "457", 795, 7, 70, 59, 0], [2, "458", 868, 64, 54, 55, 0], [2, "1496", 812, 2, 112, 43, 0], [2, "1499", 779, 65, 76, 60, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 21, 21, 21, 21, 21, 21, 21, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, 24, 24, 24, 24, 24, 24, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, 24, 24, 24, 24, 24, 24, 76, 77, -1, -1, -1, -1, -1, -1, 28, 27, 21, 21, 21, 21, 21, 21, 21, 21, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, 24, 24, 24, 24, 24, 24, 76, 77, 73, 74, -1, -1, -1, -1, -1, 28, 31, 30, 24, 24, 24, 24, 24, 24, 24, 24, 25, 21, 22, 28, 27, 21, 21, 21, 21, 21, 31, 30, 24, 24, 24, 24, 24, 24, 24, 24, 73, 74, -1, -1, -1, -1, -1, 28, 27, 31, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 25, 24, 24]}, {"type": 4, "obj": [[2, "3095", 408, -22, 62, 42, 0], [2, "3095", 289, -21, 62, 42, 0], [2, "3095", 272, -4, 62, 42, 0], [2, "3095", 320, -4, 62, 42, 0], [2, "3095", 440, -4, 62, 42, 0], [2, "3095", 266, 15, 62, 42, 0], [2, "1492", 686, -84, 38, 142, 2], [2, "3168", 477, -47, 96, 107, 0], [2, "3095", 240, 25, 62, 42, 0], [2, "3168", 318, -39, 96, 107, 0], [2, "3095", 903, 27, 62, 42, 0], [2, "3095", 663, 30, 62, 42, 0], [2, "3083", 706, -34, 118, 106, 0], [2, "3095", 495, 33, 62, 42, 0], [2, "1457", 256, 49, 22, 30, 0], [2, "3095", 239, 37, 62, 42, 0], [2, "3095", 343, 41, 62, 42, 0], [2, "3095", 164, 42, 62, 42, 0], [2, "3095", 192, 42, 62, 42, 0], [2, "829", 492, 34, 42, 54, 0], [2, "3095", 208, 49, 62, 42, 0], [2, "3095", 602, 50, 62, 42, 0], [2, "3095", 140, 51, 62, 42, 0], [4, 2, 696, 93, 0, 4019], [2, "1818", 537, -37, 90, 132, 0], [2, "1492", 780, -45, 38, 142, 0], [2, "1457", 89, 68, 22, 30, 0], [2, "829", 478, 44, 42, 54, 0], [2, "3095", 95, 57, 62, 42, 0], [2, "829", 464, 57, 42, 54, 0], [2, "3095", 80, 72, 62, 42, 0], [2, "3095", 551, 72, 62, 42, 0], [2, "3095", 437, 73, 62, 42, 0], [2, "3095", 123, 74, 62, 42, 0], [2, "3095", 470, 79, 62, 42, 0], [2, "3095", 279, 88, 62, 42, 0], [2, "3095", 51, 89, 62, 42, 0], [2, "3179", 514, 63, 86, 69, 0], [2, "3095", 137, 97, 62, 42, 0], [2, "3095", 548, 98, 62, 42, 0], [2, "3095", 18, 103, 62, 42, 0], [2, "125", 768, 79, 18, 70, 2], [2, "3095", 743, 115, 62, 42, 0], [2, "3095", 722, 122, 62, 42, 0], [2, "3095", 722, 122, 62, 42, 0], [2, "3095", -2, 123, 62, 42, 0], [4, 4, 86, 169, 0, 4021], [2, "125", 921, 99, 18, 70, 2], [2, "3095", 417, 129, 62, 42, 0], [2, "3095", 417, 129, 62, 42, 0], [2, "3095", 696, 135, 62, 42, 0], [2, "3095", 913, 141, 62, 42, 0], [2, "829", 383, 136, 42, 54, 0], [2, "125", 848, 123, 18, 70, 2], [2, "3095", -22, 152, 62, 42, 0], [2, "3095", 803, 161, 62, 42, 0], [2, "3095", 392, 164, 62, 42, 0], [2, "829", 368, 154, 42, 54, 0], [2, "3095", 440, 168, 62, 42, 0], [2, "3095", 889, 178, 62, 42, 0], [2, "3095", 811, 179, 62, 42, 0], [2, "829", 354, 170, 42, 54, 0], [2, "3095", -19, 191, 62, 42, 0], [2, "73", 881, 163, 46, 72, 0], [2, "3095", 353, 196, 62, 42, 0], [2, "3095", 353, 196, 62, 42, 0], [2, "829", 338, 185, 42, 54, 0], [2, "3095", 308, 202, 62, 42, 0], [2, "3095", 656, 205, 62, 42, 0], [2, "3095", 656, 205, 62, 42, 0], [2, "3095", 771, 207, 62, 42, 0], [2, "3095", 51, 210, 62, 42, 0], [2, "3095", 338, 210, 62, 42, 0], [2, "3083", 372, 149, 118, 106, 2], [2, "3095", 363, 226, 62, 42, 0], [2, "3095", 579, 240, 62, 42, 0], [2, "3095", 540, 243, 62, 42, 0], [2, "3095", 277, 254, 62, 42, 0], [2, "3095", 735, 258, 62, 42, 0], [2, "73", 782, 234, 46, 72, 0], [2, "3095", 466, 270, 62, 42, 0], [2, "3058", 142, 144, 168, 175, 2], [2, "3095", 212, 277, 62, 42, 0], [2, "3095", 422, 281, 62, 42, 0], [2, "3095", 806, 282, 62, 42, 0], [2, "3168", -14, 225, 96, 107, 0], [2, "3095", 844, 294, 62, 42, 0], [2, "3168", 867, 230, 96, 107, 0], [2, "3095", 17, 299, 62, 42, 0], [2, "1456", 380, 313, 24, 32, 0], [2, "3095", 385, 306, 62, 42, 0], [2, "3095", 891, 307, 62, 42, 0], [2, "3095", 69, 310, 62, 42, 0], [2, "3095", -3, 322, 62, 42, 0], [2, "3168", 668, 261, 96, 107, 0], [2, "3252", -58, 262, 96, 107, 0], [2, "3095", 614, 328, 62, 42, 0], [2, "3252", 200, 264, 96, 107, 0], [2, "3095", 580, 338, 62, 42, 0], [2, "3095", 204, 339, 62, 42, 0], [2, "3095", 479, 340, 62, 42, 0], [2, "3095", 760, 343, 62, 42, 0], [2, "3252", 710, 280, 96, 107, 0], [2, "3095", 518, 346, 62, 42, 0], [2, "3095", 690, 346, 62, 42, 0], [2, "3095", -32, 347, 62, 42, 0], [2, "3095", 797, 352, 62, 42, 0], [2, "3058", 63, 222, 168, 175, 2], [2, "3095", 186, 362, 62, 42, 0], [2, "3095", 713, 362, 62, 42, 0], [2, "3095", 713, 362, 62, 42, 0], [2, "3095", 437, 366, 62, 42, 0], [4, 3, 77, 414, 0, 4005], [2, "3095", 385, 376, 62, 42, 0], [2, "3095", 158, 377, 62, 42, 0], [2, "3168", 260, 317, 96, 107, 0], [2, "3095", 900, 384, 62, 42, 0], [2, "3095", 274, 399, 62, 42, 0], [2, "3095", 198, 404, 62, 42, 0], [2, "3095", 645, 411, 62, 42, 0], [2, "3095", 13, 414, 62, 42, 0], [2, "3127", 236, 429, 30, 30, 0], [2, "3095", 226, 418, 62, 42, 0], [2, "3095", 226, 418, 62, 42, 0], [2, "3095", 193, 422, 62, 42, 0], [2, "3095", 438, 423, 62, 42, 0], [2, "73", 348, 396, 46, 72, 0], [2, "3095", 182, 435, 62, 42, 0], [2, "3095", 329, 439, 62, 42, 0], [2, "3252", 745, 380, 96, 107, 0], [2, "3168", 821, 382, 96, 107, 0], [2, "1456", 304, 477, 24, 32, 0], [2, "3168", 863, 406, 96, 107, 0], [2, "3168", 706, 410, 96, 107, 0], [2, "3095", 275, 476, 62, 42, 0], [2, "3095", 719, 486, 62, 42, 0], [2, "3095", 894, 486, 62, 42, 0], [2, "3252", 666, 423, 96, 107, 0], [2, "3095", 687, 498, 62, 42, 0], [2, "3095", 99, 516, 62, 42, 0], [2, "73", 660, 486, 46, 72, 0], [2, "3252", 933, 451, 96, 107, 0], [2, "3095", 645, 528, 62, 42, 0], [2, "124", 858, 510, 142, 70, 0], [2, "3168", 175, 479, 96, 107, 0], [2, "3168", 454, 485, 96, 107, 0], [2, "3095", 189, 559, 62, 42, 0], [2, "73", 545, 539, 46, 72, 0], [2, "3095", 672, 571, 62, 42, 0], [2, "3095", 369, 573, 62, 42, 0], [2, "125", 864, 545, 18, 70, 2], [2, "1457", 698, 589, 22, 30, 0], [2, "3252", 476, 513, 96, 107, 0], [2, "3095", 98, 585, 62, 42, 0], [2, "1456", 854, 596, 24, 32, 0], [2, "3095", 508, 587, 62, 42, 0], [4, 5, 565, 631, 0, 4001], [2, "3168", 426, 530, 96, 107, 0], [2, "125", 933, 567, 18, 70, 2], [2, "3095", 527, 602, 62, 42, 0], [2, "3095", 445, 610, 62, 42, 0], [4, 1, 839, 654, 0, 4010], [2, "3095", 506, 616, 62, 42, 0], [2, "3252", -10, 560, 96, 107, 0], [2, "3095", 29, 645, 62, 42, 2], [2, "3095", 360, 650, 62, 42, 0], [2, "113", 848, 661, 26, 33, 0], [2, "3168", -37, 588, 96, 107, 0], [2, "3095", 755, 658, 62, 42, 0], [2, "3095", -18, 671, 62, 42, 0], [2, "3095", 555, 675, 62, 42, 0], [2, "31", 535, 656, 14, 63, 0], [2, "3095", 345, 695, 62, 42, 0], [2, "3095", 413, 698, 62, 42, 0], [2, "3095", 309, 700, 62, 42, 0], [2, "3095", 257, 702, 62, 42, 0], [2, "3095", 131, 711, 62, 42, 0], [2, "3095", 218, 712, 62, 42, 0], [2, "31", 616, 698, 14, 63, 0], [2, "3095", 621, 726, 62, 42, 0], [2, "63", 621, 740, 16, 31, 0], [2, "3095", 747, 731, 62, 42, 0], [2, "3095", 664, 736, 62, 42, 0], [2, "3095", 706, 737, 62, 42, 0], [2, "3095", 706, 737, 62, 42, 0], [2, "3095", 785, 738, 62, 42, 0], [2, "3168", 895, 675, 96, 107, 0], [2, "3095", 178, 741, 62, 42, 0], [2, "51", 591, 752, 38, 35, 0], [2, "3095", 52, 749, 62, 42, 0], [2, "3095", 917, 754, 62, 42, 0], [2, "73", 82, 731, 46, 72, 0], [2, "3095", 840, 761, 62, 42, 0], [2, "63", 577, 775, 16, 31, 0], [2, "3252", 856, 701, 96, 107, 0], [2, "3095", 872, 774, 62, 42, 0], [2, "3095", 51, 779, 62, 42, 0], [2, "51", 547, 787, 38, 35, 0], [2, "63", 534, 812, 16, 31, 0], [2, "51", 504, 824, 38, 35, 0], [2, "63", 494, 837, 16, 31, 0], [2, "51", 464, 849, 38, 35, 0]]}, {"type": 3, "obj": [[2, "510", 775, 147, 66, 40, 0], [2, "399", 348, 759, 58, 72, 0], [2, "385", 18, 77, 72, 48, 0], [2, "385", 154, 29, 72, 48, 0], [2, "399", 189, 289, 58, 72, 0], [2, "398", 249, 357, 58, 78, 2], [2, "399", 251, 308, 58, 72, 0], [2, "399", 694, 758, 58, 72, 2], [2, "399", 421, 326, 58, 72, 2], [2, "399", 100, 252, 58, 72, 2], [2, "399", 145, 254, 58, 72, 0], [2, "253_2", 734, 582, 92, 53, 0], [2, "253_2", 352, 1, 92, 53, 2], [2, "253_2", 55, 342, 92, 53, 0], [2, "253_2", 567, 365, 92, 53, 0], [2, "257_1", 830, 467, 14, 66, 0], [2, "257_1", 837, 471, 14, 66, 0], [2, "257_1", 845, 475, 14, 66, 0], [2, "257_1", 852, 479, 14, 66, 0], [2, "257_1", 860, 484, 14, 66, 0], [2, "257_1", 867, 488, 14, 66, 0], [2, "257_1", 875, 492, 14, 66, 0], [2, "257_1", 882, 496, 14, 66, 0], [2, "257_1", 890, 500, 14, 66, 0], [2, "257_1", 898, 504, 14, 66, 0], [2, "257_1", 905, 508, 14, 66, 0], [2, "257_1", 820, 473, 14, 66, 2], [2, "257_1", 813, 481, 14, 66, 2], [2, "257_1", 807, 485, 14, 66, 2], [2, "257_1", 800, 488, 14, 66, 2], [2, "257_1", 793, 492, 14, 66, 2], [2, "257_1", 787, 496, 14, 66, 2], [2, "257_1", 780, 499, 14, 66, 2], [2, "28", 891, 500, 12, 27, 0], [2, "685", 839, 548, 14, 14, 0], [2, "685", 836, 549, 14, 14, 2], [2, "257_1", 774, 500, 14, 66, 2], [2, "257_1", 783, 501, 14, 66, 2], [2, "257_1", 790, 504, 14, 66, 2], [2, "257_1", 797, 508, 14, 66, 2], [2, "257_1", 804, 511, 14, 66, 2], [2, "257_1", 811, 514, 14, 66, 2], [2, "257_1", 818, 517, 14, 66, 2], [2, "257_1", 825, 521, 14, 66, 2], [2, "257_1", 832, 524, 14, 66, 2], [2, "257_1", 839, 527, 14, 66, 2], [2, "257_1", 846, 530, 14, 66, 2], [2, "257_1", 853, 533, 14, 66, 2], [2, "257_1", 909, 511, 14, 66, 2], [2, "257_1", 898, 513, 14, 66, 2], [2, "257_1", 892, 517, 14, 66, 2], [2, "257_1", 885, 520, 14, 66, 2], [2, "257_1", 878, 525, 14, 66, 2], [2, "257_1", 871, 529, 14, 66, 2], [2, "257_1", 864, 533, 14, 66, 2], [2, "257_1", 858, 540, 14, 66, 2], [2, "33_1", 882, 539, 22, 35, 2], [2, "34", 803, 523, 30, 53, 2], [2, "34", 803, 530, 30, 53, 2], [2, "1351", 798, 569, 34, 26, 0], [2, "115", 843, 573, 16, 37, 0], [2, "14_1", 893, 569, 32, 30, 2], [2, "14_1", 741, 523, 32, 30, 2], [2, "14_1", 874, 578, 32, 30, 2], [2, "420", 681, 724, 16, 13, 2], [2, "237", 800, 522, 36, 22, 2], [2, "11", 725, 537, 32, 29, 0], [2, "422", 786, 561, 16, 14, 0], [2, "398", 532, -33, 58, 78, 0], [2, "399", 575, -24, 58, 72, 0], [2, "398", 658, -24, 58, 78, 2], [2, "829", 490, -33, 42, 54, 0], [2, "116", 434, 600, 46, 39, 0], [2, "422", 231, 700, 16, 14, 0], [2, "422", 416, 310, 16, 14, 0], [2, "422", 685, 758, 16, 14, 0], [2, "422", 383, 47, 16, 14, 0], [2, "399", 627, 273, 58, 72, 0], [2, "399", 490, 290, 58, 72, 2], [2, "399", 282, 341, 58, 72, 2], [2, "399", 332, 338, 58, 72, 0], [2, "398", 674, 270, 58, 78, 2], [2, "399", 712, 269, 58, 72, 0], [2, "399", 207, 363, 58, 72, 0], [2, "399", 201, 382, 58, 72, 2], [2, "398", 165, 523, 58, 78, 2], [2, "435", 179, 393, 50, 77, 2], [2, "438", 179, 466, 26, 43, 2], [2, "436", 177, 507, 34, 28, 2], [2, "399", 121, 537, 58, 72, 2], [2, "398", 81, 552, 58, 78, 2], [2, "399", 36, 566, 58, 72, 2], [2, "398", -12, 561, 58, 78, 0], [2, "422", 212, 576, 16, 14, 0], [2, "22", 63, 465, 62, 38, 0], [2, "552", 636, 343, 22, 15, 0], [2, "553", 604, 444, 14, 8, 0], [2, "552", 371, 657, 22, 15, 0], [2, "553", 696, 720, 14, 8, 0], [2, "553", 729, 65, 14, 8, 0], [2, "552", 578, 82, 22, 15, 0], [2, "553", 603, 89, 14, 8, 0], [2, "553", 524, 54, 14, 8, 0], [2, "553", 374, 504, 14, 8, 0], [2, "553", 272, 595, 14, 8, 0], [2, "553", 490, 565, 14, 8, 0], [2, "422", 61, 347, 16, 14, 0], [2, "420", 150, 372, 16, 13, 0], [2, "553", 134, 517, 14, 8, 0], [2, "420", 620, 582, 16, 13, 0], [2, "552", 458, 574, 22, 15, 0], [2, "420", 613, 444, 16, 13, 0], [2, "422", 463, 532, 16, 14, 0], [2, "422", 470, 370, 16, 14, 0], [2, "420", 363, 440, 16, 13, 0], [2, "420", 476, 367, 16, 13, 0], [2, "552", 664, 252, 22, 15, 0], [2, "553", 827, 203, 14, 8, 0], [2, "553", 302, 90, 14, 8, 0], [2, "420", 230, 61, 16, 13, 0], [2, "325", 24, 250, 50, 37, 2], [2, "22", 828, 679, 62, 38, 2], [2, "21", 821, 713, 28, 24, 0], [2, "325", 26, 493, 50, 37, 0], [2, "422", 54, 257, 16, 14, 0], [2, "420", 477, 576, 16, 13, 0], [2, "420", 751, 163, 16, 13, 2], [2, "420", 519, 44, 16, 13, 2], [2, "552", 687, 791, 22, 15, 0], [2, "553", 702, 803, 14, 8, 0], [2, "420", 703, 789, 16, 13, 0], [2, "422", 690, 792, 16, 14, 0], [2, "422", 551, 711, 16, 14, 0], [2, "552", 310, 335, 22, 15, 0], [2, "420", 329, 337, 16, 13, 0], [2, "420", 688, 831, 16, 13, 0], [2, "552", 37, 557, 22, 15, 0], [2, "553", 59, 567, 14, 8, 0], [2, "422", 52, 552, 16, 14, 0], [2, "553", 126, 464, 14, 8, 0], [2, "552", 270, 31, 22, 15, 0], [2, "553", 134, 72, 14, 8, 0], [2, "420", 24, 142, 16, 13, 0], [2, "553", 198, 259, 14, 8, 0], [2, "553", 127, 244, 14, 8, 0], [2, "552", 166, 268, 22, 15, 0], [2, "422", 171, 226, 16, 14, 0], [2, "420", 185, 270, 16, 13, 0], [2, "325", 685, 240, 50, 37, 2], [2, "420", 696, 246, 16, 13, 0], [2, "421", 670, 263, 14, 11, 0], [2, "123", 901, 632, 58, 42, 2], [2, "420", 643, 778, 16, 13, 2], [2, "90", 756, 546, 28, 36, 0], [2, "1369", 890, 637, 28, 36, 0], [2, "399", 579, 267, 58, 72, 0], [2, "36_3", 504, 276, 140, 103, 2], [2, "399", 444, 302, 58, 72, 2], [2, "399", 396, 304, 58, 72, 0], [2, "399", 347, 298, 58, 72, 0], [2, "399", 301, 287, 58, 72, 0], [2, "399", 251, 280, 58, 72, 0], [2, "399", 206, 269, 58, 72, 0], [2, "36_3", 105, 263, 140, 103, 0], [2, "399", 49, 238, 58, 72, 0], [2, "399", -2, 223, 58, 72, 0], [2, "399", -46, 215, 58, 72, 0], [2, "399", 378, 332, 58, 72, 0], [2, "399", 615, -13, 58, 72, 0], [2, "398", 697, -41, 58, 78, 2], [2, "399", 731, -33, 58, 72, 0], [2, "399", 484, 703, 58, 72, 0], [2, "399", 513, 712, 58, 72, 0], [2, "399", 552, 729, 58, 72, 0], [2, "399", 596, 743, 58, 72, 0], [2, "399", 642, 758, 58, 72, 0], [2, "399", 682, 783, 58, 72, 2], [2, "399", 731, 754, 58, 72, 2], [2, "399", 718, 796, 58, 72, 2], [2, "399", 648, 779, 58, 72, 0], [2, "399", 612, 766, 58, 72, 0], [2, "399", 224, 296, 58, 72, 0], [2, "399", -1, 239, 58, 72, 2], [2, "399", -28, 255, 58, 72, 2], [2, "399", 855, -10, 58, 72, 0], [2, "399", 815, -21, 58, 72, 0], [2, "398", 772, -30, 58, 78, 0], [2, "398", 894, -17, 58, 78, 2], [2, "399", 930, -13, 58, 72, 0], [2, "399", 755, 288, 58, 72, 0], [2, "399", 795, 304, 58, 72, 0], [2, "399", 838, 320, 58, 72, 0], [2, "399", 878, 330, 58, 72, 0], [2, "398", 903, 338, 58, 78, 2], [2, "398", 935, 329, 58, 78, 2], [2, "399", 775, 758, 58, 72, 0], [2, "399", 817, 773, 58, 72, 0], [2, "399", 859, 793, 58, 72, 0], [2, "398", 900, 796, 58, 78, 2], [2, "398", 934, 785, 58, 78, 2], [2, "439", 35, 83, 64, 42, 0], [2, "440", 7, 116, 34, 34, 0], [2, "439", -39, 141, 64, 42, 0], [2, "441", 86, 65, 28, 21, 0], [2, "437", 113, 65, 20, 19, 0], [2, "439", 234, 15, 64, 42, 0], [2, "440", 277, -18, 34, 34, 0], [2, "437", 132, 64, 20, 19, 0], [2, "441", 210, 48, 28, 21, 0], [2, "437", 188, 63, 20, 19, 0], [2, "437", 151, 63, 20, 19, 0], [2, "437", 169, 63, 20, 19, 0], [2, "385", -30, 4, 72, 48, 0], [2, "385", -24, -11, 72, 48, 0], [2, "385", 8, -2, 72, 48, 2], [2, "385", 32, -14, 72, 48, 2], [2, "385", 47, -4, 72, 48, 0], [2, "385", 162, -32, 72, 48, 0], [2, "398", 134, 782, 58, 78, 2], [2, "398", 97, 796, 58, 78, 2], [2, "398", 173, 769, 58, 78, 2], [2, "399", 54, 799, 58, 72, 0], [2, "398", 22, 800, 58, 78, 2], [2, "398", -18, 815, 58, 78, 2], [2, "398", 207, 751, 58, 78, 2], [2, "398", 245, 737, 58, 78, 2], [2, "398", 279, 723, 58, 78, 2], [2, "399", 310, 724, 58, 72, 0], [2, "399", 347, 713, 58, 72, 0], [2, "399", 392, 722, 58, 72, 0], [2, "399", 428, 726, 58, 72, 0], [2, "398", 460, 710, 58, 78, 2], [2, "398", 175, 815, 58, 78, 2], [2, "398", 212, 802, 58, 78, 2], [2, "398", 228, 775, 58, 78, 2], [2, "398", 256, 804, 58, 78, 2], [2, "398", 273, 777, 58, 78, 2], [2, "398", 307, 772, 58, 78, 2], [2, "399", 397, 780, 58, 72, 0], [2, "399", 346, 808, 58, 72, 0], [2, "399", 443, 770, 58, 72, 0], [2, "398", 478, 755, 58, 78, 2], [2, "398", 508, 752, 58, 78, 2], [2, "399", 551, 774, 58, 72, 0], [2, "399", 603, 795, 58, 72, 0], [2, "399", 519, 782, 58, 72, 0], [2, "399", 767, 799, 58, 72, 0], [2, "399", 483, 788, 58, 72, 0], [2, "510", 522, 715, 66, 40, 0], [2, "510", 576, 742, 66, 40, 0], [2, "510", 499, 733, 66, 40, 0], [2, "510", 551, 760, 66, 40, 0], [2, "510", 522, 781, 66, 40, 0], [2, "510", 475, 752, 66, 40, 0], [2, "510", 452, 771, 66, 40, 0], [2, "510", 502, 799, 66, 40, 0], [2, "510", 477, 817, 66, 40, 0], [2, "510", 430, 789, 66, 40, 0], [2, "510", 405, 807, 66, 40, 0], [2, "510", 455, 834, 66, 40, 0], [2, "510", 377, 820, 66, 40, 0], [2, "510", 431, 845, 66, 40, 0], [2, "510", 462, 845, 66, 40, 2], [2, "510", 454, 751, 66, 40, 2], [2, "510", 502, 712, 66, 40, 2], [2, "510", 538, 740, 66, 40, 2], [2, "63", 535, 691, 16, 31, 0], [2, "51", 502, 702, 38, 35, 0], [2, "63", 490, 726, 16, 31, 0], [2, "51", 457, 737, 38, 35, 0], [2, "63", 446, 763, 16, 31, 0], [2, "51", 413, 774, 38, 35, 0], [2, "63", 399, 795, 16, 31, 0], [2, "51", 366, 806, 38, 35, 0], [2, "510", 421, 826, 66, 40, 2], [2, "510", 480, 780, 66, 40, 2], [2, "510", 469, 764, 66, 40, 0], [2, "253_2", 198, 222, 92, 53, 0], [2, "21", 100, 441, 28, 24, 0], [2, "21", 78, 503, 28, 24, 0], [2, "21", 499, 625, 28, 24, 0], [2, "21", 603, 481, 28, 24, 0], [2, "116", 481, 585, 46, 39, 0], [2, "1457", 67, 644, 22, 30, 0], [2, "1457", 92, 608, 22, 30, 0], [2, "3127", 38, 410, 30, 30, 0], [2, "1457", 846, 758, 22, 30, 0], [2, "3127", 162, 705, 30, 30, 0], [2, "1457", 382, 635, 22, 30, 0], [2, "1456", 386, 647, 24, 32, 0], [2, "464", 827, 38, 18, 82, 0], [2, "464", 838, 43, 18, 82, 0], [2, "464", 847, 49, 18, 82, 0], [2, "464", 855, 54, 18, 82, 0], [2, "464", 864, 60, 18, 82, 0], [2, "464", 818, 43, 18, 82, 0], [2, "464", 809, 47, 18, 82, 0], [2, "464", 799, 51, 18, 82, 0], [2, "464", 788, 55, 18, 82, 0], [2, "464", 873, 65, 18, 82, 0], [2, "464", 882, 71, 18, 82, 0], [2, "464", 890, 76, 18, 82, 0], [2, "464", 899, 82, 18, 82, 0], [2, "464", 780, 60, 18, 82, 0], [2, "464", 791, 65, 18, 82, 0], [2, "464", 800, 71, 18, 82, 0], [2, "464", 808, 76, 18, 82, 0], [2, "464", 817, 82, 18, 82, 0], [2, "464", 826, 87, 18, 82, 0], [2, "464", 835, 93, 18, 82, 0], [2, "464", 843, 98, 18, 82, 0], [2, "464", 852, 104, 18, 82, 0], [2, "464", 908, 87, 18, 82, 0], [2, "464", 898, 91, 18, 82, 0], [2, "464", 887, 97, 18, 82, 0], [2, "464", 875, 102, 18, 82, 0], [2, "464", 863, 107, 18, 82, 0], [2, "459", 875, 125, 20, 33, 0], [2, "459", 899, 114, 20, 33, 0], [2, "3097", 799, 109, 34, 63, 0], [2, "11", 894, 162, 32, 29, 0], [2, "14_1", 871, 172, 32, 30, 2], [2, "14_1", 872, 158, 32, 30, 2], [2, "14_1", 860, 184, 32, 30, 2], [2, "1369", 643, 38, 28, 36, 0], [2, "123", 587, 80, 58, 42, 0], [2, "253_2", 441, 111, 92, 53, 2], [2, "253_2", 721, 172, 92, 53, 0], [2, "253_2", 584, 694, 92, 53, 0], [2, "1457", 755, 113, 22, 30, 0], [2, "1456", 931, 132, 24, 32, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 83, 82, 82, -1, -1, -1, -1, -1, -1, -1, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 75, 30, 76, 76, 82, 82, -1, -1, 76, 24, 24, 24, 24, 24, 76, 76, 76, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 75, 30, 76, 82, 76, 82, 82, 77, 73, 73, 73, 83, 82, 79, 73, 73, 76, 77, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 31, 30, 76, 76, 76, 79, 79, 74, -1, -1, -1, 80, 79, -1, -1, -1, 73, 74, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 83, 82, -1, 81, -1, -1, -1, -1, -1, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 79, 78, -1, -1, -1, -1, -1, 75, 82, -1, -1, -1, -1, -1, 28, 27, -1, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 83, 82, -1, -1, -1, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 83, 82, -1, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 76, 76, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 75, 76, 76, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 76, 76, 77, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 73, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 26, -1, -1, 84, 85, 85, 95, 101, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 21, 22, -1, -1, -1, -1, -1, 82, 82, 82, 86, 96, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, -1, -1, 31, 30, 24, 25, 26, -1, -1, -1, -1, 82, 82, 76, 77, -1, -1, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, -1, -1, 76, 29, -1, -1, 28, 27, 76, 77, 73, 74, 80, 83, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 82, 82, 82, -1, -1, -1, -1, -1, 31, 30, 73, 74, -1, -1, -1, 80, 79, 83, 82, 82, 82, -1, -1, -1, -1, 76, 77, -1, -1, 76, 77, 73, 73, 73, 73, 73, 73, 73, 79, 83, 82, 82, 82, 82, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, -1, -1, -1, 76, 77, 24, 76, 76, 76, 77, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 83, 82, 82, 82, 82, -1, 82, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 76, 24, 24, 76, 77, 73, 73, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 82, -1, 82, 82, 82, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 76, 77, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, -1, -1, 82, 82, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, 24, 76, 77, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 92, 91, 82, -1, -1, -1, -1, 82, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, 24, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 84, 95, 28, 27, -1, -1, 82, -1, 82, 82, 82, -1, -1, 84, 85, 86, -1, -1, -1, 82, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, -1, -1, -1, 82, 82, 82, 82, -1, -1, 104, 103, 102, -1, -1, -1, 79, 79, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 31, 30, 82, 83, 82, -1, -1, -1, -1, 82, 82, -1, -1, -1, -1, -1, -1, -1, -1, 76, 76, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 73, 73, 73, 83, 82, -1, -1, -1, -1, 82, 82, -1, -1, -1, -1, -1, -1, 76, 76, 76, 77, 74, -1, -1, -1, -1, -1, 20, 21, -1, 30, 30, 30, 24, 27, 26, -1, -1, -1, -1, -1, -1, -1, 80, 79, 83, 82, 82, -1, 82, 82, -1, -1, -1, -1, 76, 77, 73, 79, 73, 74, -1, -1, -1, -1, -1, -1, 80, 83, 82, 24, 24, 24, 24, 76, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 79, 83, 82, 76, 82, 82, 76, 77, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 83, 82, 82, 76, 77, 77, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 83, 76, 77, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 84, 85, 86, 80, 79, 79, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 76, 73, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 101, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 84, 85, 86, 104, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, -1, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 104, 103, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 76, 76, -1, -1, -1, -1, -1, -1, -1, 28, 27, 21, 28, 2, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, 76, 76, -1, -1, -1, -1, -1, 28, 27, 31, 30, -1, -1, -1, 80, 79, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 27, 27, 26, -1, -1, -1, 31, 30, -1, 76, 76, 76, -1, -1, -1, -1, -1, 31, 30, 77, 79, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 79, 79, 78, -1, -1, -1, -1, -1, -1, 31, 30, 76, -1, -1, -1, -1, 72, 73, 73, 74, -1, -1, -1, 84, 90, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 84, 85, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [108, 108, 108, 108, 108, 108, 108, 108, 108, 108, 108, 108, 70, 71, 45, 68, 69, 69, 68, 69, 68, 69, -1, 68, 69, 68, 69, 68, 69, 68, 69, 68, 69, 68, 69, 68, 69, 68, 68, 68, 108, 108, 108, 108, 108, 108, 108, 108, 108, 108, 108, 108, 71, 70, 54, 70, 71, 71, 70, 71, 70, 71, 69, 70, 71, 69, 68, 69, 68, 70, 68, 69, 71, 70, 71, 70, 71, 70, 70, 70, 108, 108, 108, 108, 108, 108, 108, 108, 108, 108, 41, 55, 68, 68, 69, 68, 69, 54, 68, 68, 69, 68, 69, 69, 70, 71, 68, 69, 70, 68, 68, 69, 68, 69, 68, 69, 68, 69, 68, 69, 108, 108, 108, 108, 68, 69, 68, 69, 64, 60, 62, 54, 70, 70, 71, 70, 71, 63, 64, 64, 71, 70, 68, 68, 69, 69, 70, 68, 68, 64, 114, 68, 69, 69, 68, 69, 68, 69, 68, 69, 108, 108, 108, 64, 45, 68, 69, 45, 53, 36, 62, 68, 69, 70, 70, 63, 64, 60, 61, 61, 60, 114, 113, 68, 69, 68, 68, 64, 60, 61, 62, 70, 71, 69, 70, 71, 70, 71, 70, 71, 108, 63, 64, 60, 114, 70, 71, 64, 61, 36, 59, 60, 60, 60, 60, 60, 61, 33, 35, 36, 32, 111, 110, 114, 113, 70, 64, 61, 52, 51, 55, 69, 63, 64, 34, 36, 32, 33, 68, 69, 64, 60, 61, 34, 62, 63, 64, 61, 38, 42, 43, 33, 32, 36, 35, 36, 35, 52, 51, 42, 42, 51, 50, 111, 110, 114, 53, 52, 55, 54, 70, 63, 64, 61, 37, 68, 35, 36, 70, 71, 51, 50, 36, 37, 59, 60, 61, 52, 52, 45, 46, 50, 41, 42, 51, 51, 70, 55, 54, 69, 69, 54, 53, 33, 36, 111, 61, 59, 60, 60, 60, 60, 61, 39, 40, 70, 71, 70, 70, 71, 54, 46, 42, 43, 32, 33, 41, 55, 55, 54, 55, 61, 44, 45, 69, 71, 68, 69, 70, 68, 68, 64, 61, 52, 51, 50, 40, 38, 39, 40, 38, 39, 52, 51, 55, 68, 69, 69, 69, 69, 64, 45, 45, 46, 43, 36, 44, 54, 63, 64, 36, 52, 55, 70, 71, 68, 69, 68, 69, 68, 64, 61, 52, 55, 54, 46, 51, 51, 42, 43, 36, 36, 44, 54, 54, 54, 54, 54, 71, 71, 46, 51, 68, 69, 46, 51, 55, 54, 60, 61, 55, 62, 68, 69, 68, 69, 68, 70, 71, 70, 53, 36, 62, 63, 68, 69, 68, 69, 45, 46, 42, 43, 111, 60, 60, 114, 113, 33, 54, 54, 68, 69, 69, 68, 68, 69, 68, 68, 69, 36, 42, 71, 68, 69, 68, 69, 68, 68, 69, 70, 46, 43, 59, 114, 69, 71, 70, 71, 70, 70, 45, 46, 42, 42, 43, 111, 110, 114, 113, 54, 68, 69, 71, 70, 68, 69, 70, 70, 71, -1, -1, -1, 70, 71, 70, 68, 69, 70, 71, 68, 45, 46, 36, 71, 71, 69, 68, 69, -1, -1, -1, 70, 70, 45, 46, 42, 43, 60, 60, 60, 68, 69, 68, 69, 68, 69, -1, 71, -1, -1, -1, -1, -1, -1, -1, 70, 71, -1, 70, 70, 71, -1, 70, 70, 71, 71, 70, 68, 69, -1, 68, 69, 68, 69, 70, 45, 46, 51, 51, 51, 70, 71, 70, 68, 69, 68, 69, -1, 68, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 69, 68, 69, 69, 33, 34, 58, 69, 68, 69, 68, 70, 68, 69, 69, 70, 70, 70, 71, 63, 64, 60, 58, 71, 70, 71, 68, 69, 68, 69, -1, -1, 68, 69, -1, -1, 68, 69, -1, 70, 71, 70, 63, 64, 36, 37, 62, 69, 70, 71, 70, 71, 70, 71, 71, 68, 69, 69, 69, 60, 61, 36, 62, 68, 69, 70, 70, 71, 69, 71, 69, -1, 68, 69, 68, 69, 70, 71, 69, 68, 63, 64, 60, 61, 34, 52, 55, 71, 69, 71, 70, 68, 69, 69, 69, 70, 71, 71, 69, 33, 52, 51, 55, 68, 69, 68, 69, 70, 71, 71, 68, 69, 70, 71, 70, 71, 70, 71, 68, 64, 60, 61, 32, 33, 52, 55, 54, 70, 71, 71, 70, 70, 71, 71, 69, 68, 69, 69, 71, 52, 55, 54, 71, 70, 71, 70, 68, 69, 69, 68, 69, 71, 70, 71, 68, 70, 68, 69, 70, 53, 32, 33, 34, 65, 55, 54, 70, 71, 71, 71, 71, 68, 69, 69, 71, 69, 71, 69, 69, 55, 54, 68, 69, 70, 71, 68, 70, 71, 71, 70, 68, 68, 68, 69, 68, 69, 63, 64, 60, 56, 35, 36, 37, 62, 63, 68, 69, 68, 69, 68, 69, 70, 71, 71, 70, 71, 70, 71, 71, 70, 70, 70, 71, 69, 68, 69, 71, 68, 68, 69, 68, 69, 70, 71, 70, 71, 64, 61, 36, 34, 38, 39, 40, 44, 45, 70, 71, 70, 71, 68, 69, 68, 32, 33, 32, 33, 69, 68, 69, 68, 45, 70, 71, 71, 70, 71, 69, 70, 70, 71, 70, 71, 68, 69, 68, 54, 53, 35, 36, 41, 42, 42, 43, 47, 60, 60, 114, 68, 69, 68, 69, 68, 69, 36, 35, 32, 33, 70, 68, 70, 71, 68, 69, 68, 69, 71, 71, 68, 69, 68, 68, 69, 69, 68, 63, 64, 56, 41, 42, 40, 70, 45, 46, 42, 43, 33, 44, 45, 69, 70, 71, 70, 71, 33, 32, 33, 68, 69, 70, 68, 69, 70, 71, 70, 71, 68, 69, 70, 71, 68, 70, 71, 63, 64, 60, 61, 44, 45, 68, 69, 70, 68, 69, 45, 46, 50, 47, 48, 69, 70, 63, 64, 35, 36, 35, 36, 70, 68, 69, 70, 71, 70, 71, 70, 71, 70, 71, 69, 68, 70, 63, 64, 60, 61, 41, 42, 55, 69, 70, 68, 69, 68, 69, 68, 68, 53, 33, 44, 45, 64, 60, 61, 36, 36, 35, 36, 68, 70, 71, 68, 68, 69, 63, 63, 63, 71, 70, 71, 63, 64, 60, 61, 33, 34, 44, 45, 68, 69, 68, 69, 71, 70, 71, 68, 69, 46, 43, 47, 60, 61, 36, 36, 36, 36, 52, 51, 70, 71, 71, 69, 70, 71, 63, 63, 63, 70, 63, 64, 60, 61, 32, 35, 36, 37, 62, 68, 70, 68, 70, 68, 69, 68, 69, 70, 71, 70, 46, 43, 32, 36, 36, 36, 52, 51, 55, 54, 70, 70, 71, 71, 69, 68, 69, 63, 64, 60, 60, 61, 35, 36, 35, 38, 39, 52, 55, 70, 71, 70, 68, 68, 69, 68, 69, 68, 69, 68, 57, 56, 35, 52, 51, 42, 55, 54, 70, 71, 68, 68, 69, 70, 68, 63, 64, 60, 61, 35, 36, 37, 38, 39, 38, 39, 65, 55, 54, 69, 71, 70, 70, 70, 71, 68, 69, 70, 71, 57, 56, 33, 52, 55, 54, 71, 71, 69, 70, 71, 70, 70, 71, 63, 64, 60, 61, 35, 36, 38, 52, 51, 51, 51, 42, 43, 62, 69, 70, 68, 69, 68, 69, 70, 71, 70, 64, 57, 57, 56, 36, 52, 55, 54, 71, 71, 69, 71, 70, 71, 68, 68, 69, 60, 61, 35, 35, 38, 52, 51, 55, 54, 69, 70, 45, 46, 32, 33, 68, 70, 71, 70, 71, 34, 47, 60, 36, 36, 36, 52, 51, 55, 54, 71, 71, 71, 71, 68, 69, 69, 70, 70, 71, 38, 39, 38, 52, 51, 55, 54, 70, 68, 69, 68, 69, 34, 33, 34, 37, 35, 35, 36, 36, 37, 35, 36, 37, 36, 36, 55, 54, 71, 71, 63, 63, 68, 69, 70, 71, 71, 68, 68, 69, 52, 51, 51, 55, 54, 69, 68, 69, 70, 71, 70, 71, 37, 36, 37, 40, 38, 38, 39, 39, 40, 38, 39, 40, 38, 39, 44, 45, 45, 46, 63, 63, 70, 71, 71, 68, 69, 70, 70, 71, 55, 70, 71, 68, 68, 69, 60, 60, 70, 71, 40, 39, 40, 39, 40, 52, 51, 51, 51, 50, 37, 41, 42, 51, 50, 41, 68, 69, 69, 69, 64, -1, 68, 68, 69, 70, 71, 68, 68, 69, 69, 54, 54, 70, 70, 32, 33, 34, 34, 32, 41, 42, 42, 42, 51, 55, 68, 69, 45, 46, 51, 55, 68, 69, 46, 55, 70, 71, 68, 69, -1, 36, 70, 70, 68, 69, 68, 69, 70, 71]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}