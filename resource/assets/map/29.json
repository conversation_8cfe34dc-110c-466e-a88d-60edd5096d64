{"mW": 648, "mH": 648, "tW": 24, "tH": 24, "tiles": [["111", 0, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["111", 2, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["369_2", 0, 3, 3], ["369_2", 2, 3, 3], ["369_2", 1, 3, 3], ["369_2", 3, 3, 3], ["315_2", 0, 3, 3]], "layers": [{"type": 3, "obj": [[2, "1352", 363, -43, 92, 52, 0], [2, "713", 329, 214, 18, 27, 0], [2, "713", 386, 212, 18, 27, 2], [2, "1271", 534, 631, 16, 54, 0], [2, "1272", 597, 551, 62, 109, 2], [2, "1271", 542, 641, 16, 54, 0], [2, "1272", 534, 551, 62, 109, 0]]}, {"type": 4, "obj": [[2, "714", 319, 159, 54, 132, 0], [2, "714", 367, 160, 54, 132, 2], [2, "710", 349, 234, 38, 62, 0], [2, "412", -16, 198, 64, 100, 2], [2, "714", 363, 182, 54, 132, 0], [2, "714", 320, 184, 54, 132, 2], [2, "257_1", 598, 261, 14, 66, 2], [2, "1350", 546, 289, 136, 57, 2], [2, "73", 166, 322, 46, 72, 0], [2, "125", 50, 368, 18, 70, 0], [2, "73", 76, 374, 46, 72, 0], [2, "47", 597, 445, 54, 63, 2], [2, "4", 193, 416, 122, 119, 0]]}, {"type": 3, "obj": [[2, "1358", 491, 18, 62, 34, 2], [2, "411", -25, -18, 44, 40, 2], [2, "1358", 620, 380, 62, 34, 2], [2, "1358", 450, 37, 62, 34, 2], [2, "1358", 507, 341, 62, 34, 2], [2, "1358", 585, 398, 62, 34, 0], [2, "1357", 310, -10, 58, 32, 2], [2, "313_3", 196, 239, 70, 44, 2], [2, "173_1", 530, 3, 70, 45, 0], [2, "173_1", 583, -2, 70, 45, 0], [2, "594", 510, 33, 52, 46, 2], [2, "598", 95, 617, 18, 22, 0], [2, "5", 273, 494, 42, 66, 2], [2, "1358", 309, 264, 62, 34, 2], [2, "327", 70, 399, 30, 22, 0], [2, "313_3", 586, 415, 70, 44, 2], [2, "313_3", 100, 146, 70, 44, 2], [2, "325", 140, 252, 50, 37, 2], [2, "1356", 548, 324, 40, 33, 0], [2, "1356", 524, 335, 40, 33, 0], [2, "1358", 532, 371, 62, 34, 0], [2, "413", -19, 241, 44, 72, 0], [2, "412", -35, 283, 100, 64, 4], [2, "413", 89, 229, 44, 72, 0], [2, "12", 66, 606, 28, 26, 7], [2, "412", 49, 219, 64, 100, 2], [2, "12", 49, 615, 26, 28, 0], [2, "420", 592, 441, 16, 13, 0], [2, "413", 33, 259, 44, 72, 0], [2, "411", 24, 314, 44, 40, 2], [2, "411", 120, 263, 44, 40, 0], [2, "411", 178, 219, 44, 40, 2], [2, "8", 615, 538, 38, 29, 0], [2, "1357", 578, 374, 58, 32, 0], [2, "1356", 539, 313, 40, 33, 0], [2, "1356", 630, 330, 40, 33, 2], [2, "1356", 638, 354, 40, 33, 0], [2, "1356", 614, 366, 40, 33, 0], [2, "257_1", 612, 337, 14, 66, 2], [2, "1356", 633, 315, 40, 33, 2], [2, "1356", 607, 287, 40, 33, 2], [2, "1356", 633, 299, 40, 33, 2], [2, "1356", 552, 291, 40, 33, 0], [2, "1356", 626, 343, 40, 33, 0], [2, "1355", 562, 308, 36, 47, 0], [2, "86", 615, 174, 50, 49, 0], [2, "1359", 572, 341, 22, 19, 0], [2, "12", 633, 372, 26, 28, 0], [2, "13", 600, 389, 22, 24, 0], [2, "114", 75, 303, 18, 32, 0], [2, "115", 114, 284, 16, 37, 2], [2, "763", 73, 325, 32, 31, 0], [2, "85", -9, 319, 48, 53, 0], [2, "325", 184, 271, 50, 37, 0], [2, "22", -13, 409, 62, 38, 0], [2, "411", 66, -14, 44, 40, 2], [2, "413", 36, -49, 44, 72, 0], [2, "366", 12, -23, 32, 48, 0], [2, "411", -21, 2, 44, 40, 2], [2, "763", 153, 285, 32, 31, 0], [2, "420", 62, 11, 16, 13, 0], [2, "420", -8, 434, 16, 13, 2], [2, "328", 14, 13, 32, 29, 0], [2, "1358", 410, -4, 62, 34, 0], [2, "1358", 394, 4, 62, 34, 0], [2, "1358", 422, -10, 62, 34, 0], [2, "1356", 436, -28, 40, 33, 2], [2, "1356", 462, -16, 40, 33, 2], [2, "1356", 485, -6, 40, 33, 2], [2, "1356", 481, -22, 40, 33, 2], [2, "1356", 400, -30, 40, 33, 0], [2, "1356", 383, -20, 40, 33, 0], [2, "1356", 360, -9, 40, 33, 0], [2, "257_1", 361, -41, 14, 66, 2], [2, "1356", 366, 7, 40, 33, 2], [2, "1356", 392, 19, 40, 33, 2], [2, "1356", 412, 28, 40, 33, 2], [2, "1355", 441, -9, 36, 47, 2], [2, "1359", 406, -2, 22, 19, 0], [2, "257_1", 514, -39, 14, 66, 2], [2, "1356", 484, 6, 40, 33, 0], [2, "1356", 460, 18, 40, 33, 0], [2, "1356", 468, -1, 40, 33, 0], [2, "1356", 436, 29, 40, 33, 0], [2, "257_1", 440, -1, 14, 66, 2], [2, "1358", 365, 29, 62, 34, 0], [2, "422", 577, 426, 16, 14, 0], [2, "422", 631, 426, 16, 14, 0], [2, "21", 633, 513, 28, 24, 0], [2, "11", 337, 8, 32, 29, 0], [2, "327", 419, 498, 30, 22, 0], [2, "328", 482, 23, 32, 29, 0], [2, "125", 2, 339, 18, 70, 0], [2, "420", 640, 218, 16, 13, 0], [2, "327", 602, 213, 30, 22, 0], [2, "327", 18, 510, 30, 22, 0], [2, "327", 204, 362, 30, 22, 2], [2, "422", 621, 401, 16, 14, 0], [2, "327", 481, 391, 30, 22, 0], [2, "422", 123, 289, 16, 14, 0], [2, "86", 191, 211, 50, 49, 0], [2, "1358", 252, -12, 62, 34, 0], [2, "1358", 304, 15, 62, 34, 0], [2, "327", 308, 30, 30, 22, 0], [2, "1358", 418, 55, 62, 34, 0], [2, "420", 424, 49, 16, 13, 0], [2, "116", 459, 72, 46, 39, 0], [2, "1358", 376, 264, 62, 34, 0], [2, "1358", 359, 271, 62, 34, 0], [2, "1358", 341, 275, 62, 34, 0], [2, "1358", 329, 284, 62, 34, 0], [2, "1358", 319, 292, 62, 34, 0], [2, "1358", 371, 298, 62, 34, 2], [2, "1358", 306, 300, 62, 34, 0], [2, "598", 312, 290, 18, 22, 2], [2, "598", 407, 290, 18, 22, 0], [2, "595", 435, 292, 50, 26, 0], [2, "113", 445, 270, 26, 33, 0], [2, "705", 27, 253, 74, 157, 0], [2, "705", 100, 253, 74, 157, 2], [2, "396", 110, 334, 34, 59, 2], [2, "1269", 54, 339, 34, 39, 0], [2, "328_1", 143, 367, 32, 29, 0], [2, "173_1", 40, 52, 70, 45, 0], [2, "174", 82, 56, 68, 33, 1], [2, "422_1", 53, 49, 16, 14, 0], [2, "594", 26, 81, 52, 46, 2], [2, "173_1", 91, 31, 70, 45, 0], [2, "488", 124, 59, 32, 51, 2], [2, "488", 101, 70, 32, 51, 2], [2, "488", 74, 83, 32, 51, 2], [2, "1366", 40, 45, 52, 39, 0], [2, "710", 49, 4, 38, 62, 0], [2, "339", 3, 62, 22, 22, 0], [2, "325_1", 355, 375, 50, 37, 2], [2, "1366", 341, 278, 52, 39, 0], [2, "598", 368, 304, 18, 22, 0], [2, "598", 350, 303, 18, 22, 2], [2, "174", 572, 7, 68, 33, 1], [2, "422_1", 543, 0, 16, 14, 0], [2, "488", 614, 10, 32, 51, 2], [2, "488", 591, 21, 32, 51, 2], [2, "488", 564, 34, 32, 51, 2], [2, "1366", 566, 89, 52, 39, 0], [2, "710", 575, 48, 38, 62, 0], [2, "339", 533, 105, 22, 22, 0], [2, "1270", 104, 325, 46, 40, 0], [2, "5", 232, 494, 42, 66, 0], [2, "219", 33, 366, 36, 30, 0], [2, "1356", 143, 613, 40, 33, 0], [2, "1356", 150, 617, 40, 33, 0], [2, "1356", 157, 621, 40, 33, 0], [2, "1356", 117, 625, 40, 33, 0], [2, "1356", 124, 629, 40, 33, 0], [2, "1356", 131, 633, 40, 33, 0], [2, "1356", 149, 633, 40, 33, 0], [2, "1356", 156, 637, 40, 33, 0], [2, "1356", 163, 641, 40, 33, 0], [2, "116_2", 64, 616, 46, 39, 2], [2, "56_2", 118, 611, 76, 47, 0], [2, "598", 31, 627, 18, 22, 2], [2, "339", 628, 71, 22, 22, 0], [2, "90", 319, 507, 28, 36, 0], [2, "113", 4, 389, 26, 33, 2], [2, "90", 315, 534, 36, 28, 5], [2, "420", 393, 383, 16, 13, 0], [2, "594", 3, 452, 52, 46, 2], [2, "488", 24, -7, 32, 51, 0], [2, "1367", 46, 8, 52, 40, 0], [2, "488", 89, 25, 32, 51, 0], [2, "488", 548, 44, 32, 51, 0], [2, "1367", 570, 59, 52, 40, 0], [2, "488", 613, 76, 32, 51, 0], [2, "253", 332, 66, 92, 53, 2], [2, "422", 272, 1, 16, 14, 0], [2, "1357", 524, 351, 58, 32, 0], [2, "598", 518, 364, 18, 22, 2], [2, "422_1", 630, 268, 16, 14, 0]]}, {"type": 2, "data": [-1, -1, -1, 46, 45, -1, 0, 1, 19, 18, -1, -1, 36, 40, 39, 40, 28, 40, 40, -1, 34, 40, 46, 45, -1, -1, -1, 41, 47, 46, 43, 42, -1, 9, 16, 22, 21, -1, -1, -1, -1, -1, 47, 46, 40, 40, 40, 41, 40, 41, 42, -1, 43, 42, 38, 44, 46, 46, 45, -1, 6, 17, 10, 5, 19, 18, -1, -1, -1, 44, 43, 47, 40, 40, 41, 37, 38, -1, 32, 31, 30, -1, 24, 46, 41, 42, -1, -1, 3, 22, 22, 22, 5, 19, 19, 18, -1, -1, 36, 37, 37, 38, -1, 44, 43, 44, 43, 42, -1, 39, 43, 38, -1, -1, -1, 3, 22, 22, 22, 22, 22, 22, 21, -1, -1, -1, -1, 36, 42, -1, -1, 32, 31, 30, -1, -1, -1, -1, -1, -1, 20, 19, 23, 11, 13, 17, 22, 22, 10, 5, 1, 19, 18, -1, -1, -1, -1, -1, 36, 37, 38, -1, -1, -1, -1, 0, 1, 23, 22, 22, 15, -1, 3, 4, 22, 11, 17, 4, 4, 5, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, 17, 16, 16, 15, -1, 9, 22, 22, 21, 14, 13, 16, 11, 12, 20, 19, 19, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 14, 13, 13, 12, -1, 6, 7, 13, 12, -1, -1, 13, 12, -1, 9, 16, 22, 22, 15, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 13, 17, 16, 15, -1, -1, 36, 37, 31, 30, -1, -1, -1, -1, 39, 46, 30, -1, 20, 19, 19, 18, -1, -1, -1, -1, -1, -1, 6, 7, 12, -1, -1, -1, 40, 40, 30, -1, -1, 31, 31, 44, 43, 42, 20, 23, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 33, -1, 47, 46, 45, -1, -1, 0, 23, 22, 22, 21, -1, -1, 32, -1, -1, 30, -1, -1, -1, -1, -1, 13, 31, 40, -1, 39, 40, 41, 43, -1, -1, -1, 3, 4, 11, 13, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, 39, 40, 34, 33, 38, 31, 30, -1, -1, 6, 7, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, 40, 46, 46, 40, 47, 46, 34, 33, -1, 3, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 46, 40, 40, 34, 33, 44, 43, 47, 46, 40, 41, 43, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 47, 46, 40, 46, -1, -1, 44, 43, 37, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, 44, 43, 43, 43, 43, 42, -1, 44, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, 8, 0, 1, 2, -1, -1, -1, -1, 32, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 4, 5, 18, -1, -1, -1, 36, 37, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 4, 4, 21, 32, 31, 31, -1, -1, -1, 0, 1, 1, 2, -1, -1, 32, -1, -1, 31, 30, -1, -1, -1, -1, -1, 14, 13, 7, 13, 12, 36, 37, 43, -1, -1, -1, 9, 10, 16, 5, 1, 2, 44, -1, -1, 46, 46, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, 13, 17, 16, 15, -1, 36, 37, 43, 43, 42, 20, 19, 19, 18, -1, -1, -1, -1, -1, -1, 36, 37, -1, -1, -1, -1, -1, -1, 14, 13, 12, -1, -1, -1, -1, -1, 20, 23, 22, 22, 5, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 17, 16, 10, 10, 16, 15, -1, -1, -1, -1, 44, 31, 24, 25, 31, 30, 31, 30, -1, 31, 30, -1, -1, -1, -1, -1, -1, 14, 13, 13, 13, 7, 8, 32, 31, -1, -1, -1, 40]}, {"type": 3, "obj": [[2, "253", 564, 470, 92, 53, 2], [2, "253", 276, 539, 92, 53, 0], [2, "597", 16, 618, 34, 26, 0], [2, "597", 3, 634, 34, 26, 2], [2, "597", 7, 460, 34, 26, 0], [2, "597", -4, 484, 34, 26, 2], [2, "597", 39, 477, 34, 26, 0], [2, "173_1", 541, 76, 70, 45, 0], [2, "173_1", 581, 98, 70, 45, 0], [2, "597", 512, 32, 34, 26, 0], [2, "597", 497, 56, 34, 26, 2], [2, "597", 22, 81, 34, 26, 0], [2, "597", 7, 105, 34, 26, 2]]}, {"type": 2, "data": [59, 58, 49, 49, 61, 60, 85, 85, 91, 77, 76, 80, 79, 52, 52, 52, 52, 51, 52, -1, 61, 88, 61, 61, 61, 52, 52, 62, 61, 61, 52, 70, 53, 57, 88, 85, 85, 85, 77, 80, 52, 70, 79, 78, 51, 52, 61, 61, 61, 61, 52, 52, 52, 52, 61, 61, 61, 61, 61, 53, 58, 57, 88, 88, 85, 88, 66, 80, 79, 79, 79, 79, 52, 52, 52, 52, 52, 52, 52, 52, 52, 80, 61, 52, 52, 52, 52, 79, 78, 85, 88, 88, 84, 48, 62, 79, 79, 52, 79, 70, 71, 61, 52, 52, 52, 52, 52, 52, 69, 52, 71, 80, 79, 71, 76, 75, 72, 49, 81, 87, 51, 52, 70, 52, 52, 71, 67, 80, 79, 79, 52, 52, 79, 70, 70, 66, 67, 68, 77, 76, 75, 84, 85, 77, 65, 63, 92, 66, 67, 80, 79, 71, 75, 87, 77, 76, 76, 80, 70, 79, 79, 71, 84, 85, 84, 88, 88, 87, 87, 88, 89, 84, 85, 86, 90, 91, 77, 76, 75, 92, 90, 91, 92, 88, 66, 67, 80, 70, 53, 48, 49, 50, 88, 88, 88, 88, 91, 92, 87, 88, 89, 85, 86, 90, 87, 88, 84, 85, 86, 89, 84, 85, 86, 66, 67, 80, 51, 52, 53, 57, 92, 90, 91, 92, 84, 92, 88, 92, 84, 85, 84, 84, 85, 86, 88, 89, 92, 87, 88, 89, 88, 48, 62, 84, 61, 79, 78, 84, 85, 48, 49, 88, 58, 57, 90, 87, 88, 87, 87, 84, 85, 86, 84, 85, 86, 91, 92, 88, 51, 52, 87, 79, 79, 89, 48, 49, 62, 52, 70, 79, 78, 86, 90, 91, 90, 48, 49, 50, 59, 58, 58, 57, 88, 88, 88, 54, 80, 79, 62, 86, 92, 90, 61, 54, 55, 80, 79, 78, 89, 48, 49, 84, 51, 52, 53, 62, 61, 70, 60, 88, 88, 88, 88, 51, -1, 88, 88, 61, 61, 61, 61, 78, 77, 76, 75, 92, 66, 67, 87, 51, 52, 61, 61, 70, 61, 53, 49, 50, 92, 74, 54, 90, -1, 70, 71, 67, 68, 71, 68, 86, 84, 85, 86, 90, 90, 90, 91, 67, 76, 67, 80, 70, 70, 52, 52, 52, -1, -1, 61, 61, 61, 68, 90, 91, 53, 58, 58, 57, 84, 85, 84, 72, 73, 74, 83, 82, 81, 66, 80, 52, 52, 52, 71, 80, 52, 73, 61, 49, 50, 91, 92, 70, 71, 79, 78, 87, 48, 57, 69, 70, 71, 80, 79, 78, 48, 62, 52, 52, 52, 53, 62, 52, 61, 61, 52, 52, 70, 71, 67, 76, 75, 59, 58, 62, 78, 66, 67, 68, 77, 76, 75, 69, 70, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 78, 92, 48, 49, 62, 61, 71, 75, 88, 88, 84, 85, 87, 88, 66, 67, 80, 79, 79, 52, 71, 80, 52, 52, 61, 52, 71, 68, 84, 69, 70, 79, 79, 78, 85, 88, 88, 87, 88, 90, 91, 88, 89, 69, 79, 79, 71, 52, 52, 52, 52, 52, 70, 78, 86, 87, 66, 67, 67, 76, 75, 88, 88, 72, 58, 58, 58, 81, 91, 92, 51, 79, 79, 53, 58, 52, 52, 52, 79, 79, 78, 89, 90, 48, 49, 49, 49, 49, 57, 92, 69, 70, 52, 52, 53, 81, 88, 77, 80, 79, 70, 52, 52, 70, 71, 76, 76, 75, 92, 90, 51, 52, 61, 61, 89, 58, 57, 66, 67, 67, 80, 79, 78, 48, 49, 62, 61, 79, 60, 69, 67, 68, 86, 84, 84, 85, 86, 66, 67, 80, 52, 92, 61, 53, 58, 57, 85, 77, 76, 75, 51, 52, 70, 61, 61, 53, 62, 87, 88, 89, 59, 58, 58, 57, 88, 89, 69, 70, 70, 70, 70, 61, 60, 88, 89, 89, 88, 66, 80, 70, 61, 61, 79, 79, 90, 91, 48, 62, 61, 61, 60, 84, 85, 66, 67, 67, 80, 79, 79, 78, 91, 92, 86, 72, 58, 62, 70, 61, 79, 61, 79, 48, 49, 62, 61, 61, 61, 53, 58, 57, 89, 84, 85, 77, 76, 76, 75, 87, 88, 89, 51, 52, 70, 70, 79, 52, 52, 79, 62, 52, 52, 52, 52, 52, 52, 61, 53, 57, 85, 86, 86, 84, 85, 86, 59, 58, 58, 62, 61, 61, 61, 61, 61, 61, 87]}], "blocks": [1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}