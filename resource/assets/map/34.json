{"mW": 648, "mH": 720, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2]], "layers": [{"type": 2, "data": [47, 47, 47, 47, 39, 47, 39, 40, 37, 47, 40, 41, 49, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 41, 48, 31, 47, 47, 47, 47, 40, 43, 35, 36, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 32, 31, 47, 47, 31, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 32, 31, 31, 47, 45, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 39, 31, 31, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 48, 31, 47, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 32, 47, 47, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 47, 47, 47, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 48, 47, 39, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 48, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 47, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 47, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 31, 31, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 39, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 32, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 23, 23]}, {"type": 4, "obj": [[2, "1308_2", 561, -1, 22, 37, 0], [2, "1310_2", 60, 76, 18, 29, 2], [2, "1308_2", 561, 73, 22, 37, 0], [2, "1310_2", 576, 100, 18, 29, 0], [2, "1308_2", 586, 159, 22, 37, 0], [2, "1308_2", 608, 185, 22, 37, 0], [2, "1310_2", 58, 378, 18, 29, 2], [2, "1310_2", 70, 382, 18, 29, 2], [2, "1310_2", 32, 393, 18, 29, 2], [2, "1309_2", 12, 406, 20, 32, 2], [2, "1310_2", 47, 426, 18, 29, 0], [2, "1310_2", 19, 448, 18, 29, 0], [2, "1310_2", -2, 475, 18, 29, 2], [2, "1308_2", 631, 526, 22, 37, 0]]}, {"type": 3, "obj": [[2, "208_3", 33, 554, 78, 40, 0], [2, "1150", -12, 680, 48, 85, 2], [2, "214_3", 121, 529, 54, 40, 0], [2, "208_3", 495, 231, 78, 40, 0], [2, "216", 559, 284, 46, 46, 0], [2, "214_3", 541, 311, 54, 40, 2], [2, "214_3", 568, 434, 54, 40, 2], [2, "216", 598, 374, 46, 46, 2], [2, "208_3", 514, 295, 78, 40, 2], [2, "208_3", 547, 406, 78, 40, 2], [2, "205_3", 551, 264, 54, 40, 2], [2, "426_2", 623, 332, 26, 22, 0], [2, "214_3", 554, 464, 54, 40, 2], [2, "205_3", 556, 442, 54, 40, 2], [2, "208_3", 532, 327, 78, 40, 0], [2, "207_2", 591, 386, 38, 27, 0], [2, "152_3", 503, 465, 76, 40, 2], [2, "205_3", 589, 350, 54, 40, 2], [2, "166_2", 576, 256, 30, 35, 2], [2, "165_1", 613, 340, 42, 37, 2], [2, "164_1", 580, 443, 60, 30, 2], [2, "214_3", 378, 241, 54, 40, 2], [2, "213_3", 295, 238, 64, 45, 2], [2, "214_3", 351, 246, 54, 40, 2], [2, "214_3", 89, 117, 54, 40, 0], [2, "216", 119, 135, 46, 46, 2], [2, "216", 467, 19, 46, 46, 0], [2, "1150", 37, 115, 48, 85, 0], [2, "1150", 10, 174, 48, 85, 0], [2, "214_3", 449, 46, 54, 40, 2], [2, "214_3", 476, 169, 54, 40, 2], [2, "1310_2", 48, 168, 18, 29, 0], [2, "1149", 0, 221, 40, 82, 0], [2, "216", 506, 109, 46, 46, 2], [2, "1149", -3, 463, 40, 82, 0], [2, "1150", 31, 55, 48, 85, 0], [2, "1147", 103, 24, 50, 42, 0], [2, "426_2", 158, 21, 26, 22, 0], [2, "208_3", 117, 120, 78, 40, 0], [2, "208_3", 422, 30, 78, 40, 2], [2, "208_3", 455, 141, 78, 40, 2], [2, "205_3", 167, 23, 54, 40, 0], [2, "208_3", 118, 52, 78, 40, 1], [2, "152_3", 89, 86, 76, 40, 2], [2, "164_1", 68, 86, 60, 30, 0], [2, "165_1", 107, 46, 42, 37, 0], [2, "166_2", 168, 11, 30, 35, 0], [2, "205_3", 459, -1, 54, 40, 2], [2, "955_4", 271, 201, 20, 18, 0], [2, "955_4", 436, 30, 20, 18, 0], [2, "1303_2", 385, 186, 34, 20, 2], [2, "955_4", 357, 391, 20, 18, 0], [2, "426_2", 531, 67, 26, 22, 0], [2, "1153", 19, 198, 34, 54, 0], [2, "1153", 5, 481, 34, 54, 0], [2, "205_3", 195, -9, 54, 40, 0], [2, "213_3", 124, 172, 64, 45, 0], [2, "205_3", 126, 154, 54, 40, 2], [2, "214_3", 155, 204, 54, 40, 0], [2, "208_3", 150, 176, 78, 40, 0], [2, "213_3", 175, 222, 64, 45, 0], [2, "206", 170, 203, 66, 40, 0], [2, "214_3", 227, 237, 54, 40, 0], [2, "208_3", 218, 209, 78, 40, 0], [2, "208_3", 300, 225, 78, 40, 2], [2, "214_3", 274, 245, 54, 40, 2], [2, "206", 271, 225, 66, 40, 2], [2, "214_3", 462, 199, 54, 40, 2], [2, "205_3", 464, 177, 54, 40, 2], [2, "208_3", 446, 62, 78, 40, 0], [2, "207_2", 499, 121, 38, 27, 0], [2, "214_3", 425, 225, 54, 40, 2], [2, "152_3", 411, 200, 76, 40, 2], [2, "206", 371, 217, 66, 40, 2], [2, "205_3", 497, 85, 54, 40, 2], [2, "166_2", 484, -9, 30, 35, 2], [2, "165_1", 522, 102, 42, 37, 2], [2, "164_1", 486, 179, 60, 30, 2], [2, "165_1", 122, 146, 42, 37, 0], [2, "1147", 613, 240, 50, 42, 0], [2, "1305_2", 252, 163, 20, 14, 0], [2, "1303_2", 189, 134, 34, 20, 2], [2, "955_4", 398, 132, 20, 18, 0], [2, "955_4", 257, -3, 20, 18, 0], [2, "544_1", 334, 56, 86, 107, 0], [2, "544_1", 250, 56, 86, 107, 2], [2, "178_4", 331, 68, 70, 37, 0], [2, "178_4", 268, 68, 70, 37, 2], [2, "178_4", 331, 105, 70, 37, 1], [2, "178_4", 268, 105, 70, 37, 3], [2, "1151", 234, 11, 38, 33, 0], [2, "313_2", 152, 310, 70, 44, 2], [2, "1150", 115, 393, 48, 85, 2], [2, "1150", 162, 420, 48, 85, 2], [2, "1150", 43, 363, 48, 85, 2], [2, "1149", 78, 385, 40, 82, 2], [2, "214_3", 91, 265, 54, 40, 0], [2, "1149", 98, 385, 40, 82, 0], [2, "214_3", 131, 434, 54, 40, 0], [2, "1152", 70, 526, 38, 26, 0], [2, "1152", 56, 487, 38, 26, 0], [2, "214_3", 79, 384, 54, 40, 0], [2, "208_3", 14, 332, 78, 40, 0], [2, "208_3", 90, 235, 78, 40, 1], [2, "166_2", 89, 235, 30, 35, 0], [2, "208_3", 74, 285, 78, 40, 2], [2, "205_3", 37, 312, 54, 40, 0], [2, "206", 74, 360, 66, 40, 2], [2, "208_3", 116, 389, 78, 40, 0], [2, "208_3", 131, 421, 78, 40, 1], [2, "1151", 125, 263, 38, 33, 0], [2, "313_2", 145, 345, 70, 44, 2], [2, "166_2", 132, 415, 30, 35, 0], [2, "1303_2", 111, 323, 34, 20, 2], [2, "955_4", 77, 345, 20, 18, 0], [2, "1147", 28, 251, 50, 42, 0], [2, "1154", 101, 417, 28, 51, 0], [2, "1153", 44, 381, 34, 54, 2], [2, "1144", 310, 241, 114, 70, 0], [2, "214_3", 177, 463, 54, 40, 0], [2, "206", 206, 473, 66, 40, 0], [2, "1302_3", 239, 502, 40, 29, 0], [2, "214_3", 531, 530, 54, 40, 2], [2, "208_3", 490, 526, 78, 40, 2], [2, "205_3", 530, 497, 54, 40, 2], [2, "208_3", 493, 562, 78, 40, 3], [2, "165_1", 552, 486, 42, 37, 2], [2, "1305_2", 502, 533, 20, 14, 0], [2, "1303_2", 480, 542, 34, 20, 0], [2, "1150", 524, 647, 48, 85, 0], [2, "1149", 585, 608, 40, 82, 0], [2, "1149", 561, 633, 40, 82, 0], [2, "208_3", 526, 623, 78, 40, 2], [2, "206", 547, 591, 66, 40, 0], [2, "213_3", 511, 661, 64, 45, 2], [2, "208_3", 502, 639, 78, 40, 2], [2, "1154", 597, 635, 28, 51, 0], [2, "1150", 401, 634, 48, 85, 2], [2, "1150", 481, 649, 48, 85, 2], [2, "1150", 438, 647, 48, 85, 2], [2, "214_3", 420, 666, 54, 40, 0], [2, "208_3", 405, 621, 78, 40, 0], [2, "208_3", 416, 646, 78, 40, 1], [2, "214_3", 477, 668, 54, 40, 2], [2, "205_3", 477, 646, 54, 40, 2], [2, "1150", 323, 635, 48, 85, 2], [2, "1150", 366, 634, 48, 85, 0], [2, "1150", 293, 638, 48, 85, 2], [2, "208_3", 275, 636, 78, 40, 1], [2, "1303_2", 366, 613, 34, 20, 0], [2, "214_3", 319, 654, 54, 40, 2], [2, "213_3", 355, 646, 64, 45, 2], [2, "208_3", 348, 627, 78, 40, 2], [2, "205_3", 319, 632, 54, 40, 2], [2, "208_3", 151, 499, 78, 40, 2], [2, "205_3", 118, 514, 54, 40, 0], [2, "1150", 220, 650, 48, 85, 2], [2, "1150", 263, 646, 48, 85, 0], [2, "1150", 181, 655, 48, 85, 2], [2, "214_3", 216, 669, 54, 40, 2], [2, "213_3", 252, 661, 64, 45, 2], [2, "208_3", 237, 642, 78, 40, 2], [2, "152_3", 102, 555, 76, 40, 2], [2, "213_3", 28, 684, 64, 45, 0], [2, "208_3", -16, 658, 78, 40, 1], [2, "206", 26, 665, 66, 40, 2], [2, "166_2", -15, 652, 30, 35, 0], [2, "1149", 161, 676, 40, 82, 0], [2, "208_3", 156, 658, 78, 40, 1], [2, "208_3", -42, 561, 78, 40, 2], [2, "214_3", 82, 695, 54, 40, 0], [2, "208_3", 78, 668, 78, 40, 1], [2, "205_3", 117, 682, 54, 40, 2], [2, "1147", 97, 475, 50, 42, 0], [2, "426_2", 34, 526, 26, 22, 0], [2, "1152", 548, 211, 38, 26, 0], [2, "1152", 592, 539, 38, 26, 0], [2, "1147", 604, 552, 50, 42, 0], [2, "205_3", 216, 647, 54, 40, 2], [2, "955_4", 532, 620, 20, 18, 0], [2, "1151", 290, 280, 38, 33, 2], [2, "1301_2", 142, 309, 24, 49, 0], [2, "313_2", 508, 371, 70, 44, 0], [2, "313_2", 499, 405, 70, 44, 0], [2, "1301_2", 554, 377, 24, 49, 2], [2, "426_2", 588, 231, 26, 22, 0], [2, "1152", 65, 200, 38, 26, 0], [2, "166_2", 417, 642, 30, 35, 0], [2, "152_3", 168, 440, 76, 40, 0], [2, "1151", 456, 505, 38, 33, 0], [2, "1151", 388, 11, 38, 33, 0], [2, "714", 137, 425, 54, 132, 0], [2, "713", 165, 462, 18, 27, 2], [2, "1301_2", 360, 602, 24, 49, 2], [2, "1303_2", 470, 389, 34, 20, 2], [2, "1303_2", 182, 542, 34, 20, 2], [2, "1302_3", 511, 620, 40, 29, 0], [2, "1302_3", 472, 251, 40, 29, 2], [2, "955_4", 339, 613, 20, 18, 0], [2, "955_4", 85, 647, 20, 18, 0], [2, "1303_2", 308, 300, 34, 20, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, 73, 72, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, -1, -1, -1, -1, 69, 68, 62, -1, -1, -1, 62, 63, 64, 64, 69, -1, 57, 56, -1, -1, -1, -1, -1, -1, -1, 53, 54, 54, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, 66, 71, -1, -1, 82, 81, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 52, -1, -1, 65, 72, 63, 68, -1, 74, 85, 84, 83, -1, -1, -1, -1, -1, -1, 74, 75, 81, 80, -1, -1, -1, 62, 63, 64, -1, -1, 62, 69, 73, 72, -1, 86, 87, 93, 92, -1, -1, -1, 50, -1, -1, 86, 87, 97, 79, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 69, 60, 56, 58, -1, -1, -1, -1, -1, 53, 66, 66, -1, -1, 94, 93, 92, -1, -1, -1, 74, 75, 76, -1, -1, -1, -1, -1, 72, 59, 50, 51, 60, -1, 66, 66, 67, 73, 72, 58, 57, 57, 57, 56, -1, -1, -1, 89, 90, 95, -1, 50, 51, 11, 72, 66, 59, 53, 54, 60, -1, 66, 63, 64, 70, 69, 66, 66, 72, 72, 68, -1, -1, -1, 86, 87, 88, 50, 61, 54, 14, 63, 63, 64, 65, 72, 60, -1, 63, 64, -1, -1, 65, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, 50, 53, 54, 16, 17, -1, -1, -1, 70, 69, 73, 66, 71, -1, -1, -1, -1, -1, 65, 66, 72, 73, 72, -1, -1, -1, -1, 53, 54, 66, 67, 68, -1, -1, -1, -1, -1, 70, 63, 56, 50, 51, -1, -1, -1, 62, 63, 69, 73, 72, 65, 66, -1, 75, 66, 67, 63, 64, -1, -1, -1, -1, 50, 56, -1, -1, 59, 50, 51, -1, -1, -1, -1, -1, -1, 70, 69, 69, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 61, 59, -1, -1, -1, 53, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 71, 74, -1, -1, 62, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 68, 86, -1, -1, -1, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 66, 66, 56, 73, 72, 51, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 66, 66, 59, 70, 73, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 66, 66, 67, 73, 72, 70, 69, 73, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 66, 59, 63, 63, 64, 70, 69, 73, 72, 70, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 66, 71, 57, 56, -1, -1, -1, 70, 69, 73, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, -1, 66, 67, 68, 66, 71, -1, -1, -1, -1, -1, 70, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 66, -1, 63, 64, -1, 63, 68, -1, -1, -1, -1, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 56, -1, -1, -1, 63, 64, -1, -1, -1, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 57, 56, -1, 50, 51, 57, 56, -1, 53, 54, -1, -1, -1, -1, 50, 51, 52, -1, 50, 51, 57, 57, 56, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, 58, 57, 61, -1, -1, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, 0, 1, 2, 0, 1, 2, 2, 9, 9, 10, 11, 10, 11, 9, 10, 9, 10, 0, 0, 1, 2, 0, 1, 2, -1, -1, -1, 3, 4, 5, 3, 4, 9, 10, 12, 12, 13, 14, 13, 14, 12, 13, 12, 13, 0, 3, 4, 5, 3, 4, 5, -1, -1, -1, 6, 7, 8, 6, 9, 12, 13, 15, 15, 16, 17, 9, 10, 11, 11, 15, 16, 3, 6, 7, 8, 6, 7, 8, -1, -1, 0, 1, 2, 11, 9, 12, 15, 16, 17, 12, 15, 16, 12, 13, 14, 14, 9, 9, 6, 7, 8, 6, 7, 8, 6, -1, -1, 0, 1, 2, 14, 12, 15, 16, 17, 10, 9, 10, 11, 15, 16, 17, 9, 10, 11, 11, 9, 0, 1, 2, 0, 1, 0, 1, 3, 4, 5, 17, 9, 9, 10, 11, 13, 12, 13, 9, 10, 11, 17, 12, 13, 14, 11, 12, 3, 4, 5, 3, 4, 3, 0, 6, 7, 8, 1, 12, 12, 13, 14, 11, 15, 16, 12, 13, 14, 14, 15, 16, 17, 14, 0, 6, 7, 8, 6, 7, 6, 3, 4, 5, 3, 4, 5, 15, 16, 17, 10, 15, 16, 15, 16, 17, 17, 9, 10, 11, 0, 1, 2, 0, 1, 2, 0, 3, 0, 1, 2, 6, 7, 8, 6, 9, 12, 13, 12, 13, 14, 10, 15, 9, 12, 13, 14, 3, 4, 5, 3, 4, 5, 3, 6, 3, 4, 5, 6, 7, 8, 9, 10, 15, 16, 15, 16, 17, 13, 14, 12, 15, 16, 17, 6, 7, 8, 6, 7, 8, 6, 0, 1, 2, 8, 4, 9, 9, 10, 11, 9, 10, 15, 16, 17, 16, 17, 15, 16, 9, 10, 11, 3, 4, 5, 2, 0, 1, 3, 4, 5, 6, 7, 12, 12, 13, 14, 12, 13, 17, 11, 12, 13, 14, 12, 9, 12, 13, 14, 9, 10, 11, 5, 3, 4, 6, 7, 8, 0, 1, 9, 15, 9, 10, 11, 16, 11, 14, 9, 10, 11, 10, 12, 15, 16, 17, 12, 13, 14, 0, 1, 2, -1, 0, 1, 9, 10, 11, 9, 10, 11, 14, 13, 9, 10, 12, 13, 14, 13, 15, 16, 17, 15, 15, 16, 17, 3, 4, 5, -1, 3, 4, 12, 13, 14, 12, 13, 14, 17, 16, 12, 13, 15, 16, 17, 9, 10, 11, 14, 12, 13, 14, 8, 0, 1, 2, -1, 6, 7, 15, 16, 17, 15, 16, 17, 17, 17, 15, 16, 17, 12, 15, 9, 10, 11, 9, 15, 16, 17, 10, 11, 4, 5, 1, 2, 3, 15, 16, 17, 12, 13, 9, 9, 10, 11, 16, 17, 15, 16, 12, 13, 14, 12, 13, 14, 12, 13, 14, 7, 8, 4, 5, 6, 3, 6, 7, 15, 16, 12, 12, 13, 14, 10, 11, 12, 13, 15, 16, 17, 15, 16, 17, 15, 16, 17, 4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 9, 10, 11, 2, 0, 1, 3, 4, 0, 1, 2, 0, 1, 2, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 12, 13, 14, 5, 3, 4, 1, 2, 3, 4, 5, 3, 4, 5, 8, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 15, 16, 17, 0, 1, 2, 4, 5, 6, 7, 8, 6, 7, 8, 8, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, -1, 3, 4, 5, 0, 1, 2, 0, 1, 2, -1, 9, 10, 11, 14, 12, 13, 14, 12, 9, 10, 11, 13, 14, 12, 13, 14, 0, 1, 2, 8, 3, 4, 5, 3, 4, 5, -1, 12, 13, 14, 17, 15, 16, 17, 15, 12, 13, 14, 16, 17, 15, 16, 17, 3, 4, 5, 2, 9, 10, 11, 9, 10, 11, 9, 10, 11, 17, 9, 10, 11, 9, 10, 15, 16, 17, 11, 11, 9, 10, 11, 6, 7, 8, 5, 9, 10, 11, 9, 10, 11, 12, 13, 14, 10, 11, 9, 10, 11, 13, 14, 12, 13, 14, 9, 10, 11, 10, 11, 6, 7, 8, 9, 10, 11, 9, 10, 11, 15, 9, 10, 11, 14, 12, 13, 14, 16, 17, 15, 16, 17, 12, 13, 14, 13, 14, 0, 1, 2, 12, 13, 14, 9, 10, 11, 9, 10, 11, 14, 17, 15, 16, 17, -1, -1, -1, -1, -1, 15, 16, 17, 16, 17, 3, 4, 5, 15, 16, 17, 12, 9, 10, 12, 13, 14, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, 8, 3, 4, 5, 15, 12, 13, 15, 16, 17, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}