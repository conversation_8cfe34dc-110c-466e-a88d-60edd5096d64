{"mW": 648, "mH": 576, "tW": 24, "tH": 24, "tiles": [["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2], ["203_2", 0, 2, 1], ["203_1", 0, 2, 1], ["203_2", 3, 2, 1], ["203_1", 3, 2, 1], ["1146", 0, 3, 3]], "layers": [{"type": 2, "data": [38, 38, 38, 38, 38, 38, 38, 38, 38, 30, 31, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 39, 38, 14, 38, 38, 38, 31, 34, 26, 32, 40, 40, 34, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, 39, 38, 38, 31, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 38, 38, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 30, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 14, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, 31, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, -1, 35, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 18, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 23, 30, 22, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 39, 38, 38, 38, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, 32, 32, 34, 33, -1, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 18, 10, 18, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 22, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 22, 37, -1, -1, -1, -1, -1, -1, -1, 19, 18, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 31, 26, 34, 33, -1, -1, -1, -1, -1, 19, 18, 23, 30, 30, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, 35, 39, 38, 38, 30, 31, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, 32, 40, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 4, "obj": [[2, "1310_2", 428, -19, 18, 29, 2], [2, "1308_2", 362, -19, 22, 37, 0], [2, "1310_2", 392, -11, 18, 29, 2], [2, "1310_2", 257, 4, 18, 29, 2], [2, "1308_2", 563, 0, 22, 37, 0], [2, "1310_2", 215, 13, 18, 29, 2], [2, "1310_2", 91, 15, 18, 29, 2], [2, "1310_2", 190, 17, 18, 29, 2], [2, "1310_2", 157, 20, 18, 29, 2], [2, "1310_2", 583, 20, 18, 29, 0], [2, "1310_2", 620, 21, 18, 29, 2], [2, "1310_2", 231, 23, 18, 29, 2], [2, "1310_2", 121, 26, 18, 29, 2], [2, "1308_2", 602, 21, 22, 37, 0], [2, "1310_2", 71, 39, 18, 29, 2], [2, "1310_2", 631, 44, 18, 29, 2], [2, "1310_2", 49, 116, 18, 29, 2], [2, "1310_2", 89, 161, 18, 29, 2], [2, "1310_2", 56, 165, 18, 29, 2], [2, "1310_2", 22, 193, 18, 29, 2], [2, "1308_2", 451, 258, 22, 37, 0], [2, "1308_2", 575, 266, 22, 37, 0], [2, "1310_2", 499, 279, 18, 29, 2], [2, "1308_2", 548, 271, 22, 37, 0], [2, "1310_2", 489, 287, 18, 29, 0], [2, "1308_2", 520, 280, 22, 37, 0], [2, "1308_2", 580, 288, 22, 37, 0], [2, "1308_2", 85, 396, 22, 37, 0], [2, "1308_2", 56, 400, 22, 37, 0], [2, "1308_2", 254, 419, 22, 37, 0], [2, "1308_2", 2, 427, 22, 37, 0], [2, "1308_2", 375, 427, 22, 37, 0], [2, "1310_2", 291, 441, 18, 29, 2], [2, "1308_2", 305, 444, 22, 37, 0], [2, "1310_2", 273, 453, 18, 29, 2], [2, "1308_2", 327, 449, 22, 37, 2]]}, {"type": 3, "obj": [[2, "313_2", 58, 225, 70, 44, 2], [2, "214_3", -3, 305, 54, 40, 0], [2, "214_3", -1, 200, 54, 40, 0], [2, "214_3", 19, 225, 54, 40, 0], [2, "214_3", 434, 402, 54, 40, 0], [2, "214_3", 591, 347, 54, 40, 2], [2, "214_3", 573, 393, 54, 40, 2], [2, "208_3", 551, 365, 78, 40, 2], [2, "205_3", 589, 327, 54, 40, 0], [2, "166_2", 600, 357, 30, 35, 2], [2, "214_3", 573, 241, 54, 40, 2], [2, "213_3", 333, 529, 64, 45, 2], [2, "208_3", 369, 408, 78, 40, 2], [2, "214_3", 622, 156, 54, 40, 0], [2, "208_3", 46, 78, 78, 40, 1], [2, "214_3", 452, 471, 54, 40, 2], [2, "214_3", 428, 517, 54, 40, 2], [2, "213_3", 369, 528, 64, 45, 2], [2, "214_3", 232, 104, 54, 40, 2], [2, "213_3", 517, 394, 64, 45, 2], [2, "214_3", 264, 53, 54, 40, 0], [2, "213_3", 563, 149, 64, 45, 2], [2, "213_3", 314, 59, 64, 45, 0], [2, "214_3", 513, 516, 54, 40, 0], [2, "214_3", 315, 91, 54, 40, 0], [2, "1309_2", 359, 81, 20, 32, 0], [2, "313_2", 326, 238, 70, 44, 0], [2, "207_2", 360, 93, 38, 27, 2], [2, "208_3", 305, 57, 78, 40, 3], [2, "208_3", 269, -8, 78, 40, 1], [2, "208_3", 168, 70, 78, 40, 3], [2, "208_3", 535, 242, 78, 40, 2], [2, "208_3", 495, 535, 78, 40, 2], [2, "207_2", 362, 51, 38, 27, 2], [2, "208_3", 571, 503, 78, 40, 0], [2, "152_3", 509, 496, 76, 40, 0], [2, "205_3", 111, 63, 54, 40, 0], [2, "208_3", 19, 205, 78, 40, 1], [2, "207_2", 566, 195, 38, 27, 2], [2, "205_3", 572, 215, 54, 40, 2], [2, "207_2", 555, 170, 38, 27, 2], [2, "208_3", 315, 117, 78, 40, 2], [2, "208_3", 283, 132, 78, 40, 0], [2, "207_2", 256, 133, 38, 27, 0], [2, "955_4", 51, 473, 20, 18, 0], [2, "1303_2", 345, 27, 34, 20, 2], [2, "152_3", 254, 24, 76, 40, 2], [2, "208_3", 22, 161, 78, 40, 1], [2, "152_3", 210, 92, 76, 40, 2], [2, "1151", 226, 128, 38, 33, 0], [2, "166_2", 271, 19, 30, 35, 0], [2, "208_3", 408, 488, 78, 40, 2], [2, "208_3", 356, 506, 78, 40, 2], [2, "208_3", -6, 274, 78, 40, 1], [2, "214_3", 75, 391, 54, 40, 0], [2, "208_3", 71, 364, 78, 40, 0], [2, "208_3", 73, 414, 78, 40, 1], [2, "208_3", -5, 423, 78, 40, 1], [2, "1302_3", -1, 454, 40, 29, 2], [2, "1303_2", -7, 479, 34, 20, 0], [2, "1303_2", 279, 170, 34, 20, 0], [2, "955_4", 626, 543, 20, 18, 0], [2, "1302_3", 156, 57, 40, 29, 2], [2, "1305_2", 185, 122, 20, 14, 0], [2, "1151", 55, 240, 38, 33, 0], [2, "1303_2", 197, 515, 34, 20, 0], [2, "955_4", 231, 315, 20, 18, 0], [2, "1151", 332, 336, 38, 33, 0], [2, "1301_2", 76, 380, 24, 49, 2], [2, "955_4", 110, 427, 20, 18, 0], [2, "208_3", 91, 374, 78, 40, 2], [2, "1303_2", 118, 355, 34, 20, 0], [2, "1366", 504, 332, 52, 39, 0], [2, "710", 512, 293, 38, 62, 0], [2, "1302_3", 295, 144, 40, 29, 0], [2, "1303_2", 472, 7, 34, 20, 2], [2, "1151", 493, 21, 38, 33, 2], [2, "313_2", 114, 140, 70, 44, 2], [2, "313_2", 325, 277, 70, 44, 0], [2, "313_2", 262, 279, 70, 44, 0], [2, "313_2", 100, 464, 70, 44, 2], [2, "313_2", 139, 479, 70, 44, 2], [2, "313_2", 108, 501, 70, 44, 2], [2, "166_2", 405, 497, 30, 35, 2], [2, "207_2", 47, 263, 38, 27, 0], [2, "1302_3", 33, 282, 40, 29, 0], [2, "214_3", 474, 396, 54, 40, 0], [2, "208_3", 447, 368, 78, 40, 2], [2, "208_3", 511, 372, 78, 40, 0], [2, "208_3", 329, 536, 78, 40, 0], [2, "208_3", 335, 554, 78, 40, 2], [2, "205_3", -7, 182, 54, 40, 0], [2, "208_3", 10, 308, 78, 40, 0], [2, "1151", 97, 358, 38, 33, 0], [2, "207_2", 425, 386, 38, 27, 0], [2, "1366", 122, 141, 52, 39, 0], [2, "1366", 584, 73, 52, 39, 0], [2, "710", 592, 34, 38, 62, 0], [2, "208_3", 401, 434, 78, 40, 0], [2, "205_3", 450, 452, 54, 40, 0], [2, "166_2", 459, 477, 30, 35, 2], [2, "1366", 297, 502, 52, 39, 0], [2, "710", 305, 463, 38, 62, 0], [2, "208_3", 622, 128, 78, 40, 0], [2, "152_3", 553, 135, 76, 40, 2], [2, "166_2", 575, 125, 30, 35, 0], [2, "1366", 359, 48, 52, 39, 0], [2, "710", 367, 9, 38, 62, 0], [2, "1303_2", 546, 234, 34, 20, 2], [2, "1151", 566, 192, 38, 33, 2], [2, "1366", 39, 216, 52, 39, 0], [2, "710", 47, 177, 38, 62, 0], [2, "1366", 23, 451, 52, 39, 0], [2, "710", 31, 412, 38, 62, 0]]}, {"type": 2, "data": [41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 54, 55, -1, -1, -1, -1, -1, -1, 53, 54, 42, 61, 60, 91, 91, 91, 91, 91, -1, 41, 51, 51, 47, -1, -1, 54, 55, -1, -1, -1, -1, 65, 66, 66, 72, 66, 67, -1, -1, -1, 92, 92, 92, 92, 92, -1, 56, 51, 51, 46, 47, -1, 45, 46, 42, 43, -1, -1, 68, 69, 75, 75, 69, 70, 71, 41, 42, 89, 89, 89, 89, 89, 89, 53, -1, -1, 60, 59, -1, -1, -1, 45, 46, 47, -1, 77, 88, 69, 69, 82, 84, 83, 60, 60, -1, -1, 57, 58, 59, -1, -1, -1, -1, -1, 72, 41, 42, 43, -1, 49, 48, 47, -1, 80, 81, 82, 83, -1, -1, -1, 61, -1, -1, 54, 42, 43, -1, -1, -1, -1, 64, 63, 53, 64, 46, 57, 58, 60, 59, -1, 77, 78, 79, -1, 49, 48, 48, 42, -1, -1, 42, -1, -1, -1, -1, -1, -1, 61, 60, 54, 58, -1, 54, 55, -1, -1, -1, -1, -1, -1, -1, 56, 44, 45, -1, -1, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 55, -1, -1, -1, -1, -1, 65, 66, 67, -1, 53, 45, 45, 45, -1, 57, 49, 48, 48, 47, -1, -1, -1, 73, 72, 71, -1, -1, 65, 72, 71, -1, 65, 76, 81, 86, -1, 53, 45, 45, 45, -1, 54, 61, 51, 51, 50, -1, 73, 72, 76, 75, 74, -1, -1, 77, 78, 83, -1, 77, 88, 69, 74, -1, 53, 61, 60, 64, -1, -1, -1, -1, -1, -1, 65, 76, 75, 81, 82, 83, -1, -1, -1, -1, -1, -1, -1, 85, 84, -1, 62, 55, -1, -1, 61, -1, -1, -1, -1, -1, -1, 80, 81, 87, 87, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, -1, 42, 43, -1, -1, 49, 48, 47, -1, -1, 77, 78, 88, 87, 70, 71, -1, -1, -1, -1, -1, -1, -1, 41, 42, 43, -1, -1, 60, 59, -1, -1, 61, 60, 59, -1, -1, -1, -1, 85, 84, 78, 83, -1, -1, -1, -1, 73, 72, 71, 44, 45, 46, -1, -1, -1, -1, -1, 51, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, 84, 83, 53, 54, 60, 59, -1, -1, -1, -1, -1, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 72, 72, 71, -1, -1, -1, -1, -1, 61, 60, 59, -1, -1, -1, 45, 57, 57, 48, 48, 47, -1, -1, -1, -1, 65, 66, 76, 75, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, 65, -1, -1, 45, 57, 57, 51, 51, -1, 73, 66, 67, -1, 85, 84, 83, -1, -1, -1, -1, -1, 84, 45, 45, -1, -1, -1, -1, -1, -1, 49, 48, 47, -1, -1, -1, 68, 69, 70, 71, -1, 45, -1, -1, 45, 45, -1, -1, 46, 48, 47, -1, -1, -1, -1, -1, -1, 53, 54, 55, -1, -1, 65, 76, 81, 81, 74, 41, 42, 45, 45, 45, 48, 47, -1, -1, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 81, 78, 79, 56, 57, 57, 57, 57, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 66, 67, -1, -1, -1, 53, 64, 57, 58, 60, 60, 59, -1, 46, 47, -1, -1, 80, 41, 42, 48, 47, 47, -1, -1, -1, 77, 78, 79, -1, -1, 49, 48, 52, 57, 62, -1, -1, 45, 45, 51, 50, 41, 42, 48, -1, -1, -1, -1, 50, 49, 48, 47, -1, -1, -1, 41, 42, 52, 51, 57, 57, 62, -1, 45, 45, 58, 60, 59, 61, 60, -1, -1, -1, -1, 49]}, {"type": 3, "obj": [[2, "208_3", 556, 298, 78, 40, 2], [2, "313_2", 606, 73, 70, 44, 2], [2, "313_2", 483, 310, 70, 44, 0], [2, "313_2", 523, 321, 70, 44, 2], [2, "955_4", 457, 352, 20, 18, 0], [2, "313_2", 470, 344, 70, 44, 0], [2, "313_2", 523, 356, 70, 44, 2], [2, "1303_2", 546, 366, 34, 20, 0], [2, "313_2", 388, 102, 70, 44, 2], [2, "1303_2", 414, 118, 34, 20, 0], [2, "313_2", 565, 58, 70, 44, 0], [2, "313_2", 565, 91, 70, 44, 0], [2, "313_2", 265, 482, 70, 44, 0], [2, "313_2", 266, 515, 70, 44, 0], [2, "313_2", 313, 489, 70, 44, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, 91, 91, 91, 91, 91, 91, 91, 0, 0, 0, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, -1, -1, -1, -1, -1, 91, 91, 91, 91, 91, 91, 91, 3, 3, 3, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, -1, -1, -1, -1, -1, 92, 92, 92, 92, 92, 92, 92, 92, 6, 6, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, -1, -1, -1, -1, -1, -1, 0, 1, 2, 89, 89, 89, 89, 89, 2, 2, 0, 1, 0, 0, 3, 4, 3, 4, 5, 3, 0, -1, -1, 0, 0, 1, 2, 0, 1, 0, 0, 90, 90, 90, 90, 5, 5, 3, 4, 0, 1, 2, 7, 6, 0, 1, 0, 1, -1, -1, 0, 3, 4, 5, 3, 0, 1, 2, -1, 94, 94, 94, 94, 8, 6, 7, 0, 1, 0, 1, 2, 0, 0, 3, 4, -1, -1, 1, 2, 7, 0, 1, 2, 4, 5, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 2, 5, 3, 3, 3, 3, 0, 1, 2, 0, 1, 3, 4, 5, 2, 0, 0, 1, 3, 4, 2, 0, 1, 3, 4, 0, 1, 2, 2, 2, 91, 91, 91, 89, 89, 5, 3, 4, 6, 7, 8, 5, 0, 3, 4, 5, 5, 0, 3, 0, 1, 2, 3, 4, 5, 5, 5, 91, 91, 91, 91, 89, 8, 6, 7, 8, 6, 7, 0, 1, 0, 1, 8, 8, 3, 0, 3, 4, 5, 6, 7, 8, 8, 8, 5, 92, 92, 92, 92, 89, 4, 4, 5, 5, 5, 3, 4, 3, 4, 0, 1, 6, 3, 6, 7, 8, 7, 8, 7, 6, 7, 8, 90, 90, 89, 89, 89, 6, 7, 8, 8, 8, 6, 7, 8, 4, 3, 4, 8, 6, 7, 8, 5, 8, 6, 7, 8, 6, 7, 94, 94, 5, 1, 0, 1, 0, 0, 0, 1, 3, 3, 6, 7, 8, 2, 1, 2, 6, 7, 8, 2, 0, 6, 7, 8, 6, 93, 93, 89, 89, 3, 4, 3, 3, 0, 1, 8, 6, 7, 8, 4, 5, 4, 5, 0, 3, 4, 5, 3, 4, 5, 3, 4, 5, 96, 89, 89, 89, 1, 2, 6, 3, 4, 1, 6, 7, 8, 0, 1, 7, 8, 3, 6, 0, 1, 2, 0, 1, 2, 0, 1, 95, 0, 89, 89, 89, 89, 2, 1, 3, 4, 5, 0, 1, 3, 4, 3, 6, 0, 0, 1, 2, 5, 3, 4, 5, 3, 4, 91, 0, 1, 2, 1, 2, 0, 1, 0, 0, 1, 3, 4, 7, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 1, 89, 92, 3, 4, 5, 1, 2, 3, 4, 3, 3, 4, 7, 8, 1, 3, 3, 4, 5, 6, 7, 89, 89, 89, 89, 89, 89, 89, 89, 6, 7, 8, 4, 5, 6, 7, 6, 7, 8, 7, 8, 4, 0, 1, 2, 0, 1, 89, 89, 89, 89, 89, 89, 89, 89, 89, 5, 5, 6, 7, 8, 8, 8, 5, 3, 4, 0, 1, 2, 3, 4, 5, 3, 4, 5, 89, 89, 89, 89, 89, 89, 89, 89, 8, 8, 0, 1, 2, 1, 2, 1, 6, 7, 3, 4, 5, 6, 7, 8, 6, 7, 0, 89, 89, 89, 89, 89, 89, 89, 89, 2, 0, 1, 1, 0, 1, 2, 0, 0, 0, 1, 2, 8, 6, 7, 8, 3, 0, 0, 89, 89, 89, 89, 89, 89, 89, 89, 5, 3, 4, 4, 3, 4, 5, 3, 3, 3, 4, 5, 0, 1, 0, 0, 89, 89, 89, 89, 89, 89, 89, 2, 1, 2, 8, 8, 7, 8, 4, 6, 7, 8, 6, 6, 6, 7, 8, 0, 1, 2, 96, 96, 96, 96, 96, 96, 89, 6, 5, 4, 0, 1]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}