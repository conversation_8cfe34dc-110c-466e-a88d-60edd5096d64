{"mW": 720, "mH": 720, "tW": 24, "tH": 24, "tiles": [["3310", 0, 3, 2], ["3310", 2, 3, 2], ["3310", 1, 3, 2], ["3310", 3, 3, 2], ["3311", 0, 3, 2], ["3311", 2, 3, 2], ["3311", 1, 3, 2], ["3311", 3, 3, 2], ["3314", 0, 3, 3], ["3315", 0, 3, 2], ["3315", 2, 3, 2], ["3315", 1, 3, 2], ["3315", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "3303", 224, 655, 66, 149, 0], [2, "3303", 320, 634, 66, 149, 0]]}, {"type": 4, "obj": [[2, "3307", 357, 47, 36, 30, 0], [2, "3307", 528, 52, 36, 30, 2], [2, "3307", 673, 57, 36, 30, 2], [2, "3307", 436, 61, 36, 30, 0], [2, "3307", 582, 62, 36, 30, 2], [2, "3307", 540, 70, 36, 30, 2], [2, "3307", 510, 75, 36, 30, 2], [2, "3307", 111, 83, 36, 30, 0], [2, "3307", 488, 85, 36, 30, 2], [2, "3307", 216, 93, 36, 30, 0], [2, "3303", -12, -25, 66, 149, 0], [2, "3303", -12, -25, 66, 149, 0], [2, "3304", 247, 7, 106, 119, 2], [2, "3306", 390, 99, 40, 29, 0], [2, "3306", 55, 101, 40, 29, 0], [2, "3307", 547, 105, 36, 30, 2], [2, "3307", 240, 106, 36, 30, 0], [2, "3307", 7, 109, 36, 30, 0], [2, "3303", 43, 9, 66, 149, 0], [2, "3303", 454, 16, 66, 149, 0], [2, "3303", 454, 16, 66, 149, 0], [2, "3307", 50, 140, 36, 30, 0], [2, "3307", 311, 142, 36, 30, 0], [2, "3307", 499, 142, 36, 30, 0], [2, "3307", 475, 151, 36, 30, 2], [2, "594", 42, 141, 52, 46, 2], [2, "3303", 12, 44, 66, 149, 0], [2, "3307", 144, 166, 36, 30, 0], [2, "3306", 203, 174, 40, 29, 0], [2, "3306", 34, 179, 40, 29, 0], [2, "3303", 138, 62, 66, 149, 0], [2, "3307", 665, 181, 36, 30, 2], [2, "3307", 475, 189, 36, 30, 2], [2, "3307", 420, 190, 36, 30, 2], [2, "3304", 330, 102, 106, 119, 0], [2, "3306", 139, 202, 40, 29, 0], [2, "3307", 366, 204, 36, 30, 0], [2, "3306", 9, 238, 40, 29, 0], [2, "3307", 608, 241, 36, 30, 2], [2, "3303", 610, 124, 66, 149, 0], [2, "3307", 32, 248, 36, 30, 0], [2, "3307", 32, 248, 36, 30, 0], [2, "3307", 634, 257, 36, 30, 2], [2, "3307", 50, 266, 36, 30, 0], [2, "3304", 511, 196, 106, 119, 2], [2, "3304", 31, 197, 106, 119, 0], [2, "3306", 497, 293, 40, 29, 0], [2, "3306", 338, 297, 40, 29, 0], [2, "3307", 551, 300, 36, 30, 0], [2, "3306", 91, 306, 40, 29, 0], [2, "1461", 580, 295, 90, 41, 0], [2, "3306", 441, 312, 40, 29, 0], [2, "3306", 42, 314, 40, 29, 0], [2, "3306", 263, 319, 40, 29, 0], [2, "3306", 263, 319, 40, 29, 0], [2, "3303", 447, 202, 66, 149, 0], [2, "3303", 447, 202, 66, 149, 0], [2, "3304", 196, 242, 106, 119, 2], [2, "3307", 481, 332, 36, 30, 2], [2, "3306", 236, 343, 40, 29, 0], [2, "3307", 575, 342, 36, 30, 0], [2, "3306", 189, 347, 40, 29, 0], [2, "3306", 228, 390, 40, 29, 0], [2, "3307", 671, 401, 36, 30, 2], [2, "3303", 34, 301, 66, 149, 0], [2, "3307", 339, 421, 36, 30, 0], [2, "3307", 605, 422, 36, 30, 0], [2, "3303", -8, 307, 66, 149, 0], [2, "3307", 373, 428, 36, 30, 0], [2, "3306", 55, 438, 40, 29, 0], [2, "3307", 275, 439, 36, 30, 0], [2, "3307", 303, 440, 36, 30, 0], [2, "3307", 331, 441, 36, 30, 2], [2, "3303", 616, 324, 66, 149, 0], [2, "3307", 652, 451, 36, 30, 2], [2, "3303", 234, 335, 66, 149, 0], [2, "3306", 247, 461, 40, 29, 0], [2, "3307", 316, 463, 36, 30, 0], [2, "3307", 11, 465, 36, 30, 0], [2, "3307", 443, 475, 36, 30, 0], [2, "3307", 443, 475, 36, 30, 0], [2, "3307", 474, 492, 36, 30, 0], [2, "3303", 588, 379, 66, 149, 0], [2, "3307", 592, 507, 36, 30, 0], [2, "3303", 333, 392, 66, 149, 0], [2, "3307", 628, 516, 36, 30, 2], [2, "3306", 139, 519, 40, 29, 0], [2, "3307", 75, 519, 36, 30, 0], [2, "3307", 350, 522, 36, 30, 0], [2, "3303", 24, 407, 66, 149, 0], [2, "3303", 542, 408, 66, 149, 0], [2, "3307", 546, 532, 36, 30, 0], [2, "3307", 546, 532, 36, 30, 0], [2, "3306", 63, 537, 40, 29, 0], [2, "3307", 657, 538, 36, 30, 0], [2, "3304", 371, 450, 106, 119, 2], [2, "3303", 655, 428, 66, 149, 0], [2, "594", 577, 532, 52, 46, 0], [2, "3306", 375, 552, 40, 29, 0], [2, "3303", 617, 440, 66, 149, 0], [2, "597", 605, 572, 34, 26, 0], [2, "3307", 613, 585, 36, 30, 2], [2, "3307", 42, 588, 36, 30, 0], [2, "3303", 77, 480, 66, 149, 0], [2, "3303", -1, 485, 66, 149, 0], [2, "3307", 93, 615, 36, 30, 0], [2, "3307", 6, 619, 36, 30, 0], [2, "3304", 161, 545, 106, 119, 2], [2, "3307", 362, 635, 36, 30, 0], [2, "3307", 114, 639, 36, 30, 0], [2, "3307", 203, 646, 36, 30, 0], [2, "3307", 160, 647, 36, 30, 0], [2, "3307", 160, 647, 36, 30, 0], [2, "3303", 630, 532, 66, 149, 0], [2, "3307", 282, 653, 36, 30, 0], [2, "3303", 114, 539, 66, 149, 0], [2, "3307", 673, 658, 36, 30, 2], [2, "3307", 405, 659, 36, 30, 0], [2, "3303", 405, 542, 66, 149, 0], [2, "3307", 165, 663, 36, 30, 2], [2, "3307", 614, 668, 36, 30, 0], [2, "3306", 290, 670, 40, 29, 0], [2, "3307", 436, 671, 36, 30, 0], [2, "3307", 132, 673, 36, 30, 0]]}, {"type": 3, "obj": [[2, "3308", 527, 371, 34, 36, 0], [2, "3308", 297, 394, 34, 36, 2], [2, "3312", 375, 364, 20, 7, 0], [2, "3313", 459, 410, 6, 4, 0], [2, "3312", 311, 352, 20, 7, 0], [2, "3312", 512, 469, 20, 7, 2], [2, "3312", 546, 428, 20, 7, 2], [2, "3313", 590, 391, 6, 4, 0], [2, "3313", 473, 430, 6, 4, 0], [2, "3313", 522, 482, 6, 4, 0], [2, "3313", 385, 396, 6, 4, 0], [2, "3313", 427, 369, 6, 4, 0], [2, "3312", 475, 416, 20, 7, 2], [2, "3303", 87, -83, 66, 149, 0], [2, "3303", 25, -56, 66, 149, 0], [2, "3303", 64, -30, 66, 149, 0], [2, "3303", 602, -94, 66, 149, 0], [2, "3303", 602, -94, 66, 149, 0], [2, "3303", 549, -71, 66, 149, 0], [2, "3303", 508, -94, 66, 149, 0], [2, "3303", 508, -94, 66, 149, 0], [2, "3303", 418, -74, 66, 149, 0], [2, "3303", 362, -63, 66, 149, 0], [2, "3303", 469, -54, 66, 149, 0], [2, "3303", 648, -74, 66, 149, 0], [2, "954", 286, 476, 24, 25, 0], [2, "954", 47, 306, 24, 25, 0], [2, "954", 414, 173, 24, 25, 2], [2, "954", 160, 618, 24, 25, 0]]}, {"type": 2, "data": [61, 61, 61, 61, 61, 66, -1, -1, -1, -1, -1, -1, -1, 77, 80, 79, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 66, -1, -1, -1, -1, -1, -1, -1, -1, 77, 80, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 61, 61, 61, 61, 61, 61, 61, 61, 61, 70, 70, 70, 70, 70, 61, 61, 61, 61, 61, 66, -1, -1, -1, -1, 57, 64, 59, -1, -1, 69, 70, 70, 70, 80, 61, 61, 73, 73, 74, -1, -1, -1, -1, -1, 61, 61, 61, 61, 67, 74, -1, -1, -1, -1, 61, 61, 66, -1, -1, -1, -1, -1, -1, 77, 76, 76, 76, 70, 71, -1, -1, -1, -1, -1, 61, 61, 61, 61, 74, 71, -1, -1, -1, -1, 77, 76, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 61, 61, 74, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, 61, 61, -1, 71, -1, -1, 57, 64, 63, -1, -1, -1, -1, -1, -1, 65, 64, 63, -1, 69, 70, 71, -1, -1, -1, -1, -1, -1, -1, -1, 70, 70, -1, -1, -1, -1, 72, 61, 78, -1, -1, -1, -1, -1, -1, 72, 61, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 76, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 64, 64, 64, 64, 64, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 67, 61, 61, 61, 67, 66, 65, 58, 58, 58, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 61, 61, 61, 61, 73, 74, 60, 61, 61, 61, 61, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 73, 61, 61, 70, 70, 70, 71, 69, 70, 70, 70, 71, 71, -1, -1, -1, -1, -1, -1, 24, 25, 25, 25, 26, -1, -1, -1, -1, 69, 70, 70, 70, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 35, 28, 28, 28, 29, 25, 26, -1, -1, 32, 31, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 35, 28, 28, 28, 28, 28, 28, 29, 31, 31, 35, 28, 28, 29, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 47, 28, 28, 46, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 33, -1, -1, -1, 64, 64, 64, -1, -1, -1, -1, -1, -1, -1, -1, 39, 28, 46, 41, 37, 47, 46, 28, 28, 28, 28, 28, 28, 28, 41, 38, -1, -1, -1, 80, 79, 61, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 37, 38, -1, 44, 47, 46, 28, 28, 28, 28, 28, 28, 33, -1, -1, -1, -1, 77, 76, 61, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 47, 46, 28, 28, 28, 28, 33, -1, 61, 64, 64, -1, 72, 61, 62, 59, -1, -1, -1, -1, -1, -1, -1, 65, 64, 58, 58, 64, 64, 64, 44, 47, 28, 28, 40, 41, 42, 64, 61, 61, 61, 58, 68, 61, 61, 62, -1, -1, -1, -1, -1, -1, 57, 68, 67, 61, 61, 61, 62, 66, -1, 36, 37, 37, 37, 38, 68, 67, 61, 61, 61, 61, 61, 61, 61, 61, 58, 59, -1, -1, -1, -1, 69, 80, 61, 61, 61, 61, 61, 66, -1, -1, -1, -1, 65, 64, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 62, -1, -1, 65, 64, 61, 61, 61, 61, 61, -1, 70, 71, -1, -1, -1, -1, 72, 79, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 61, 58, 59, 68, 67, 61, 61, 61, 61, 61, 78, -1, -1, -1, -1, -1, -1, 69, 76, 80, 79, 61, 61, 61, 70, 70, 80, 79, 61, 61, 61, 61, 62, 58, 61, 61, 61, 61, 73, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, 77, 76, 61, 61, 73, -1, -1, 77, 76, -1, 61, 61, 61, 61, 61, 61, 61, 73, 74, 70, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 79, 61, 73, -1, -1, -1, -1, 80, 79, 61, 61, 61, 61, 61, 61, 70, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 76, 61, 70, -1, -1, -1, -1, 77, 76, 80, 79, 61, 61, 61, 61, 66, -1, -1, -1, -1, 65, 64, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 76, 80, 61, 61, 61, 66, -1, -1, -1, -1, 69, 70, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [4, 4, 4, 4, 4, 4, 4, 21, 52, 20, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, 52, 52, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 18, 52, 8, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 52, 0, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 52, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 19, 19, 19, 4, 4, 4, 4, 4, 4, 5, 6, 12, 13, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 19, 19, 18, 52, 52, 52, 4, 4, 4, 4, 4, 4, 4, 5, 7, 6, 20, 13, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, 52, 52, 52, 8, 7, 7, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 6, 52, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 14, 8, 7, 7, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 14, 52, 20, 23, 4, 4, 4, 4, 4, 4, 4, 17, 14, 8, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 19, 18, 52, 52, 52, 20, 23, 4, 4, 4, 17, 13, 13, 14, 8, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, 52, 8, 7, 7, 6, 52, 20, 19, 19, 19, 18, 52, 52, 8, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 18, 8, 11, 4, 4, 5, 7, 7, 2, 52, 52, 52, 8, 7, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, 8, 11, 4, 4, 4, 4, 4, 4, 5, 1, 1, 1, 11, 16, 16, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 14, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 18, 52, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, 52, 52, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, 52, 52, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 6, 52, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 52, 12, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 7, 6, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 22, 9, 12, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 6, 15, 22, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 20, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 6, 20, 19, 19, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 7, 6, 52, 20, 19, 19, 13, 13, 13, 13, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 23, 4, 4, 4, 4, 4, 22, 16, 5, 7, 6, 52, 52, 8, 7, 6, 52, 20, 19, 19, 23, 4, 4, 4, 4, 4, 4, 4, 17, 18, 15, 16, 4, 4, 4, 4, 4, 4, 4, 16, 5, 7, 7, 11, 4, 5, 7, 6, 52, 52, 20, 19, 23, 4, 4, 4, 4, 19, 18, 52, 12, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 7, 7, 6, 52, 15, 4, 4, 4, 4, 52, 52, 52, 8, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 17, 14, 8, 11, 4, 4, 4, 4, 52, 52, 8, 11, 10, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 52, 15, 22, 4, 4, 4, 4]}], "blocks": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}