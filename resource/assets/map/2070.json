{"mW": 648, "mH": 720, "tW": 24, "tH": 24, "tiles": [["315", 0, 3, 3], ["369", 0, 3, 3], ["369", 2, 3, 3], ["369", 1, 3, 3], ["369", 3, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["384", 0, 1, 3], ["384", 2, 1, 3], ["137", 0, 5, 1], ["135", 0, 1, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["111", 0, 3, 2], ["111", 2, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["302", 0, 2, 2], ["302", 2, 2, 2], ["302", 1, 2, 2], ["302", 3, 2, 2], ["476", 0, 3, 2], ["476", 2, 3, 2], ["476", 1, 3, 2], ["476", 3, 3, 2], ["484", 0, 1, 1], ["663", 0, 3, 3], ["678", 0, 3, 2], ["678", 2, 3, 2], ["678", 1, 3, 2], ["678", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "613", 499, 151, 12, 20, 2], [2, "35", 211, 120, 22, 16, 0], [2, "26", 189, 121, 10, 25, 0], [2, "677", 358, 388, 16, 13, 0], [2, "677", 351, 392, 16, 13, 0], [2, "670", 520, 249, 90, 71, 0], [2, "606", 534, 257, 54, 64, 0], [2, "606", 542, 261, 54, 64, 0], [2, "606", 551, 265, 54, 64, 0], [2, "606", 559, 269, 54, 64, 0], [2, "606", 567, 273, 54, 64, 0], [2, "606", 575, 277, 54, 64, 0], [2, "606", 584, 281, 54, 64, 0], [2, "606", 592, 285, 54, 64, 0], [2, "606", 601, 288, 54, 64, 0], [2, "606", 609, 292, 54, 64, 0], [2, "606", 617, 296, 54, 64, 0], [2, "670", 618, 298, 90, 71, 0], [2, "613", 341, 143, 12, 20, 0], [2, "670", 330, 78, 90, 71, 0], [2, "606", 344, 86, 54, 64, 0], [2, "606", 352, 90, 54, 64, 0], [2, "606", 361, 94, 54, 64, 0], [2, "606", 369, 98, 54, 64, 0], [2, "606", 377, 102, 54, 64, 0], [2, "606", 385, 106, 54, 64, 0], [2, "606", 394, 110, 54, 64, 0], [2, "606", 402, 114, 54, 64, 0], [2, "606", 411, 117, 54, 64, 0], [2, "606", 419, 121, 54, 64, 0], [2, "606", 427, 125, 54, 64, 0], [2, "621", 505, 153, 28, 46, 0], [2, "651", 527, 198, 32, 34, 0], [2, "639", 348, 388, 12, 19, 0], [2, "646", 341, 373, 28, 23, 0], [2, "675", 326, 395, 26, 17, 0], [2, "675", 307, 404, 26, 17, 0], [2, "639", 305, 405, 12, 19, 0], [2, "646", 295, 389, 28, 23, 0], [2, "677", 292, 426, 16, 13, 0], [2, "677", 294, 413, 16, 13, 0], [2, "677", 286, 417, 16, 13, 0], [2, "87", 566, 418, 72, 57, 2], [2, "88", 599, 378, 88, 61, 2], [2, "87", 631, 362, 72, 57, 2], [2, "88", 626, 419, 88, 61, 0], [2, "100", 216, -30, 18, 33, 0], [2, "124", 130, 68, 144, 70, 0], [2, "237", 128, 109, 36, 22, 2], [2, "125", 152, 100, 14, 74, 0], [2, "237", 154, 122, 36, 22, 2], [2, "671", 457, 611, 40, 112, 2], [2, "608", 460, 676, 24, 57, 2], [2, "608", 451, 682, 24, 57, 2], [2, "608", 426, 693, 24, 57, 2], [2, "672", 423, 611, 36, 80, 2], [2, "608_1", 393, 659, 24, 57, 3], [2, "608_1", 368, 653, 24, 57, 3], [2, "620", 390, 642, 42, 44, 2], [2, "608_1", 367, 680, 24, 57, 0], [2, "608_1", 392, 690, 24, 57, 0], [2, "672", 355, 645, 36, 80, 2], [2, "647", 373, 654, 32, 37, 0], [2, "614_1", 369, 689, 22, 19, 0], [2, "614_1", 388, 698, 22, 19, 0], [2, "614_1", 394, 701, 22, 19, 0], [2, "620", 395, 639, 42, 44, 2], [2, "620", 401, 636, 42, 44, 2], [2, "620", 407, 633, 42, 44, 2], [2, "620", 413, 630, 42, 44, 2], [2, "620", 419, 627, 42, 44, 2], [2, "620", 425, 624, 42, 44, 2], [2, "620", 432, 621, 42, 44, 2], [2, "620", 436, 617, 42, 44, 2], [2, "620", 443, 615, 42, 44, 2], [2, "614_1", 466, 654, 22, 19, 2], [2, "614_1", 445, 664, 22, 19, 2], [2, "614_1", 424, 674, 22, 19, 2], [2, "623", 461, 663, 24, 25, 2], [2, "623", 439, 673, 24, 25, 2], [2, "623", 427, 679, 24, 25, 2], [2, "614_1", 424, 697, 22, 19, 2], [2, "614_1", 445, 687, 22, 19, 2], [2, "614_1", 466, 677, 22, 19, 2], [2, "671", 391, 645, 40, 112, 2], [2, "612", 387, 706, 8, 26, 0], [2, "612", 387, 715, 8, 26, 0], [2, "636", 243, 694, 18, 29, 0], [2, "635", 240, 655, 22, 43, 0], [2, "634", 241, 625, 20, 35, 0], [2, "636", 268, 703, 18, 29, 0], [2, "635", 265, 664, 22, 43, 0], [2, "634", 266, 634, 20, 35, 0], [2, "636", 293, 715, 18, 29, 0], [2, "635", 290, 676, 22, 43, 0], [2, "634", 291, 646, 20, 35, 0], [2, "636", 318, 726, 18, 29, 0], [2, "635", 315, 687, 22, 43, 0], [2, "634", 316, 657, 20, 35, 0], [2, "636", 342, 736, 18, 29, 0], [2, "635", 339, 697, 22, 43, 0], [2, "634", 340, 667, 20, 35, 0], [2, "636", 368, 748, 18, 29, 0], [2, "635", 365, 709, 22, 43, 0], [2, "634", 366, 679, 20, 35, 0], [2, "636", 393, 761, 18, 29, 0], [2, "635", 390, 722, 22, 43, 0], [2, "634", 391, 692, 20, 35, 0], [2, "636", 419, 771, 18, 29, 0], [2, "635", 416, 732, 22, 43, 0], [2, "634", 417, 702, 20, 35, 0], [2, "635", 441, 742, 22, 43, 0], [2, "634", 442, 712, 20, 35, 0], [2, "608_1", 624, 696, 24, 57, 1], [2, "608_1", 603, 705, 24, 57, 1], [2, "652", 655, 684, 12, 32, 2], [2, "612", 642, 719, 8, 26, 2], [2, "608_1", 628, 641, 24, 57, 1], [2, "608_1", 603, 671, 24, 57, 1], [2, "652", 617, 699, 12, 32, 2], [2, "652", 631, 694, 12, 32, 2], [2, "608", 578, 675, 24, 57, 3], [2, "608", 578, 702, 24, 57, 0], [2, "608", 514, 642, 24, 57, 3], [2, "608", 538, 654, 24, 57, 3], [2, "608", 565, 665, 24, 57, 3], [2, "608", 517, 673, 24, 57, 0], [2, "608", 542, 685, 24, 57, 0], [2, "608", 566, 697, 24, 57, 0], [2, "610", 548, 682, 30, 54, 0], [2, "624", 537, 731, 22, 18, 0], [2, "609", 572, 712, 14, 30, 0], [2, "607", 573, 703, 12, 14, 0], [2, "609", 519, 689, 14, 30, 0], [2, "607", 520, 680, 12, 14, 0], [2, "614", 517, 665, 22, 19, 0], [2, "614", 537, 675, 22, 19, 0], [2, "614", 559, 686, 22, 19, 0], [2, "614", 579, 696, 22, 19, 0], [2, "612", 510, 661, 8, 26, 0], [2, "613", 505, 644, 12, 20, 0], [2, "611", 517, 659, 22, 15, 0], [2, "611", 536, 668, 22, 15, 0], [2, "611", 554, 677, 22, 15, 0], [2, "611", 574, 685, 22, 15, 0], [2, "612", 510, 682, 8, 26, 0], [2, "612", 510, 700, 8, 26, 0], [2, "612", 597, 696, 8, 26, 0], [2, "612", 597, 717, 8, 26, 0], [2, "612", 597, 735, 8, 26, 0], [2, "652", 620, 699, 12, 32, 2], [2, "608_1", 638, 636, 24, 57, 1], [2, "652", 643, 689, 12, 32, 2], [2, "647", 635, 645, 32, 37, 2], [2, "612", 617, 703, 8, 26, 2], [2, "612", 662, 682, 8, 26, 2], [2, "614_1", 605, 695, 22, 19, 2], [2, "614_1", 626, 685, 22, 19, 2], [2, "614_1", 647, 675, 22, 19, 2], [2, "614_1", 605, 723, 22, 19, 2], [2, "614_1", 625, 713, 22, 19, 2], [2, "614_1", 645, 703, 22, 19, 2], [2, "614_1", 664, 693, 22, 19, 2], [2, "670", 494, 581, 90, 71, 0], [2, "606", 508, 589, 54, 64, 0], [2, "606", 516, 593, 54, 64, 0], [2, "606", 525, 597, 54, 64, 0], [2, "606", 533, 601, 54, 64, 0], [2, "606", 541, 605, 54, 64, 0], [2, "606", 549, 609, 54, 64, 0], [2, "606", 558, 613, 54, 64, 0], [2, "606", 566, 617, 54, 64, 0], [2, "606", 575, 620, 54, 64, 0], [2, "606", 583, 624, 54, 64, 0], [2, "606", 591, 628, 54, 64, 0], [2, "670", 592, 629, 90, 71, 0], [2, "88", 244, 578, 88, 61, 0], [2, "87", 334, 592, 72, 57, 0], [2, "88", 286, 546, 88, 61, 2], [2, "101", 223, -13, 10, 13, 0], [2, "9", 78, 118, 20, 16, 0], [2, "634", 196, 603, 20, 35, 0], [2, "634", 217, 613, 20, 35, 0], [2, "629", 53, 519, 36, 23, 0], [2, "630", 53, 560, 36, 18, 0], [2, "630", 53, 542, 36, 18, 0], [2, "634", 36, 532, 20, 35, 0], [2, "634", 11, 521, 20, 35, 0], [2, "634", -15, 511, 20, 35, 0], [2, "639", 232, 258, 12, 19, 0], [2, "640", 220, 238, 36, 26, 0], [2, "644", 274, 256, 30, 11, 0], [2, "644", 245, 256, 30, 11, 2], [2, "639", 305, 253, 12, 19, 0], [2, "640", 292, 236, 36, 26, 0], [2, "682", 76, 539, 38, 38, 0], [2, "682", 103, 552, 38, 38, 0], [2, "682", 129, 565, 38, 38, 0], [2, "682", 154, 577, 38, 38, 0], [2, "680", 77, 545, 20, 29, 0], [2, "681", 149, 579, 14, 33, 0], [2, "630", 160, 606, 36, 18, 0], [2, "629", 160, 565, 36, 23, 0], [2, "630", 160, 588, 36, 18, 0], [2, "628", 157, 544, 42, 40, 0], [2, "628", 50, 498, 42, 40, 0], [2, "670", 425, 123, 90, 71, 0]]}, {"type": 4, "obj": [[2, "237", 211, 69, 36, 22, 2], [2, "237", 239, 83, 36, 22, 2], [2, "125", 235, 61, 14, 74, 0], [2, "63_1", 155, 148, 16, 31, 0], [2, "18_1", 161, 115, 88, 64, 0], [2, "8", 212, 151, 38, 29, 0], [2, "1", 62, 82, 40, 111, 0], [2, "125", 57, 131, 14, 74, 0], [2, "124", -25, 143, 144, 70, 0], [2, "125", 113, 164, 14, 74, 0], [2, "125", 24, 202, 14, 74, 0], [2, "639", 305, 270, 12, 19, 0], [2, "639", 232, 275, 12, 19, 0], [2, "643", 275, 261, 30, 35, 0], [2, "643", 245, 262, 30, 35, 2], [2, "642", 332, 285, 8, 32, 0], [2, "642", 348, 288, 8, 32, 0], [2, "642", 366, 292, 8, 32, 0], [2, "642", 212, 297, 8, 32, 0], [2, "642", 200, 303, 8, 32, 0], [2, "642", 188, 311, 8, 32, 0], [2, "422", 142, 395, 16, 14, 0], [2, "422", 610, 508, 16, 14, 0], [2, "89", 615, 436, 48, 95, 0], [2, "635", -16, 541, 22, 43, 0], [2, "635", 10, 551, 22, 43, 0], [2, "630", 53, 578, 36, 18, 0], [2, "635", 35, 562, 22, 43, 0], [2, "631", 53, 596, 36, 14, 0], [2, "630", 160, 624, 36, 18, 0], [2, "631", 160, 642, 36, 14, 0], [2, "635", 194, 634, 22, 43, 0], [2, "635", 216, 643, 22, 43, 0], [2, "89", 307, 604, 48, 95, 2]]}, {"type": 3, "obj": [[2, "612", 502, 207, 8, 26, 2], [2, "612", 500, 163, 8, 26, 2], [2, "612", 500, 184, 8, 26, 2], [2, "608_1", 477, 185, 24, 57, 2], [2, "608_1", 477, 143, 24, 57, 1], [2, "608", 410, 201, 24, 57, 0], [2, "608", 379, 152, 24, 57, 3], [2, "608", 350, 141, 24, 57, 3], [2, "608", 374, 153, 24, 57, 3], [2, "608", 403, 164, 24, 57, 3], [2, "608", 353, 172, 24, 57, 0], [2, "608", 378, 184, 24, 57, 0], [2, "608", 401, 196, 24, 57, 0], [2, "610", 372, 190, 30, 54, 0], [2, "624", 374, 229, 22, 18, 0], [2, "609", 408, 211, 14, 30, 0], [2, "607", 409, 202, 12, 14, 0], [2, "609", 355, 188, 14, 30, 0], [2, "607", 356, 179, 12, 14, 0], [2, "614", 353, 164, 22, 19, 0], [2, "614", 373, 174, 22, 19, 0], [2, "614", 395, 185, 22, 19, 0], [2, "614", 415, 195, 22, 19, 0], [2, "612", 346, 160, 8, 26, 0], [2, "611", 352, 158, 22, 15, 0], [2, "611", 369, 166, 22, 15, 0], [2, "611", 386, 174, 22, 15, 0], [2, "611", 405, 183, 22, 15, 0], [2, "612", 346, 181, 8, 26, 0], [2, "612", 346, 199, 8, 26, 0], [2, "14_1", 335, 212, 32, 30, 2], [2, "608_1", 455, 195, 24, 57, 1], [2, "608_1", 434, 204, 24, 57, 1], [2, "652", 486, 184, 12, 32, 2], [2, "612", 473, 219, 8, 26, 2], [2, "608_1", 459, 141, 24, 57, 1], [2, "608_1", 434, 171, 24, 57, 1], [2, "652", 448, 199, 12, 32, 2], [2, "652", 462, 194, 12, 32, 2], [2, "652", 451, 199, 12, 32, 2], [2, "608_1", 469, 136, 24, 57, 1], [2, "652", 474, 189, 12, 32, 2], [2, "612", 448, 203, 8, 26, 2], [2, "612", 491, 182, 8, 26, 2], [2, "614_1", 436, 195, 22, 19, 2], [2, "614_1", 457, 185, 22, 19, 2], [2, "614_1", 478, 175, 22, 19, 2], [2, "614_1", 436, 223, 22, 19, 2], [2, "614_1", 456, 213, 22, 19, 2], [2, "614_1", 476, 203, 22, 19, 2], [2, "608_1", 450, 128, 24, 57, 2], [2, "608_1", 467, 131, 24, 57, 2], [2, "612", 476, 142, 8, 26, 2], [2, "647", 466, 143, 32, 37, 2], [2, "612", 431, 190, 8, 26, 0], [2, "612", 431, 211, 8, 26, 0], [2, "612", 431, 229, 8, 26, 0], [2, "612", 431, 235, 8, 26, 0], [2, "614_1", 483, 173, 22, 19, 2], [2, "614_1", 482, 201, 22, 19, 2], [2, "664", 506, 341, 52, 51, 0], [2, "679", 513, 349, 36, 33, 0], [2, "665", 523, 357, 30, 34, 0], [2, "8", 155, 119, 38, 29, 0], [2, "325", 231, 87, 50, 37, 2], [2, "63_1", 228, 109, 16, 31, 2], [2, "122", 86, 164, 106, 57, 2], [2, "673", 59, 176, 68, 62, 0], [2, "608_1", 79, -65, 24, 57, 3], [2, "608", 200, -59, 24, 57, 1], [2, "608", 200, -25, 24, 57, 2], [2, "608", 190, -53, 24, 57, 1], [2, "608", 166, -42, 24, 57, 1], [2, "608", 141, -31, 24, 57, 1], [2, "608", 191, -19, 24, 57, 2], [2, "608", 166, -7, 24, 57, 2], [2, "608_1", 111, -37, 24, 57, 3], [2, "608_1", 111, 0, 24, 57, 3], [2, "608_1", 95, -45, 24, 57, 3], [2, "608_1", 98, -7, 24, 57, 3], [2, "608_1", 74, -19, 24, 57, 3], [2, "608_1", 71, -56, 24, 57, 3], [2, "608_1", 647, 365, 24, 57, 1], [2, "608_1", 626, 374, 24, 57, 1], [2, "652", 678, 353, 12, 32, 2], [2, "612", 665, 388, 8, 26, 2], [2, "608_1", 651, 310, 24, 57, 1], [2, "608_1", 626, 340, 24, 57, 1], [2, "652", 640, 368, 12, 32, 2], [2, "652", 654, 363, 12, 32, 2], [2, "608", 601, 344, 24, 57, 3], [2, "608", 601, 371, 24, 57, 0], [2, "654", 292, 319, 94, 45, 0], [2, "654", 198, 319, 94, 45, 2], [2, "654", 198, 363, 94, 45, 3], [2, "658", 313, 413, 48, 29, 0], [2, "657", 313, 398, 48, 24, 0], [2, "661", 392, 362, 28, 38, 0], [2, "659", 361, 374, 30, 41, 0], [2, "655", 290, 408, 22, 15, 0], [2, "655", 269, 408, 22, 15, 2], [2, "657", 222, 398, 48, 24, 2], [2, "659", 192, 375, 30, 41, 2], [2, "661", 167, 366, 28, 38, 2], [2, "656", 291, 423, 22, 19, 0], [2, "660", 361, 398, 30, 35, 0], [2, "662", 391, 367, 28, 54, 0], [2, "656", 269, 423, 22, 19, 2], [2, "658", 222, 414, 48, 29, 2], [2, "660", 195, 402, 30, 35, 2], [2, "662", 167, 370, 28, 54, 2], [2, "661", 392, 324, 28, 38, 1], [2, "659", 362, 310, 30, 41, 1], [2, "657", 314, 304, 48, 24, 1], [2, "655", 295, 303, 22, 15, 1], [2, "655", 273, 303, 22, 15, 3], [2, "657", 226, 304, 48, 24, 3], [2, "659", 194, 312, 30, 41, 3], [2, "666", 164, 400, 60, 37, 0], [2, "666", 165, 399, 60, 37, 0], [2, "666", 165, 395, 60, 37, 0], [2, "666", 164, 391, 60, 37, 0], [2, "666", 157, 404, 60, 37, 0], [2, "666", 155, 400, 60, 37, 0], [2, "666", 148, 409, 60, 37, 0], [2, "666", 358, 401, 60, 37, 2], [2, "666", 357, 398, 60, 37, 2], [2, "666", 358, 394, 60, 37, 2], [2, "666", 358, 391, 60, 37, 2], [2, "666", 366, 403, 60, 37, 2], [2, "666", 366, 400, 60, 37, 2], [2, "666", 375, 410, 60, 37, 2], [2, "608", 537, 311, 24, 57, 3], [2, "608", 561, 323, 24, 57, 3], [2, "608", 588, 334, 24, 57, 3], [2, "608", 540, 342, 24, 57, 0], [2, "608", 565, 354, 24, 57, 0], [2, "608", 589, 366, 24, 57, 0], [2, "610", 559, 360, 30, 54, 0], [2, "624", 560, 400, 22, 18, 0], [2, "609", 595, 381, 14, 30, 0], [2, "607", 596, 372, 12, 14, 0], [2, "609", 542, 358, 14, 30, 0], [2, "607", 543, 349, 12, 14, 0], [2, "614", 540, 334, 22, 19, 0], [2, "614", 560, 344, 22, 19, 0], [2, "614", 582, 355, 22, 19, 0], [2, "614", 602, 365, 22, 19, 0], [2, "612", 533, 330, 8, 26, 0], [2, "613", 528, 313, 12, 20, 0], [2, "611", 540, 328, 22, 15, 0], [2, "611", 559, 337, 22, 15, 0], [2, "611", 577, 346, 22, 15, 0], [2, "611", 597, 354, 22, 15, 0], [2, "612", 533, 351, 8, 26, 0], [2, "612", 533, 369, 8, 26, 0], [2, "612", 620, 365, 8, 26, 0], [2, "612", 620, 386, 8, 26, 0], [2, "612", 620, 404, 8, 26, 0], [2, "652", 643, 368, 12, 32, 2], [2, "608_1", 661, 305, 24, 57, 1], [2, "652", 666, 358, 12, 32, 2], [2, "612", 640, 372, 8, 26, 2], [2, "612", 685, 351, 8, 26, 2], [2, "614_1", 628, 364, 22, 19, 2], [2, "614_1", 649, 354, 22, 19, 2], [2, "614_1", 670, 344, 22, 19, 2], [2, "614_1", 628, 392, 22, 19, 2], [2, "614_1", 648, 382, 22, 19, 2], [2, "614_1", 668, 372, 22, 19, 2], [2, "627", 149, 654, 60, 45, 0], [2, "628", 157, 634, 42, 40, 0], [2, "636", 197, 673, 18, 29, 0], [2, "636", 219, 682, 18, 29, 0], [2, "636", -13, 580, 18, 29, 0], [2, "636", 13, 590, 18, 29, 0], [2, "636", 38, 601, 18, 29, 0], [2, "612", 70, -36, 8, 26, 0], [2, "613", 65, -53, 12, 20, 0], [2, "608_1", 452, -6, 24, 57, 1], [2, "608_1", 431, 3, 24, 57, 1], [2, "652", 483, -18, 12, 32, 2], [2, "612", 470, 17, 8, 26, 2], [2, "608_1", 456, -61, 24, 57, 1], [2, "608_1", 431, -31, 24, 57, 1], [2, "652", 445, -3, 12, 32, 2], [2, "652", 459, -8, 12, 32, 2], [2, "608", 406, -27, 24, 57, 3], [2, "608", 406, 0, 24, 57, 0], [2, "608", 342, -60, 24, 57, 3], [2, "608", 366, -48, 24, 57, 3], [2, "608", 393, -37, 24, 57, 3], [2, "608", 345, -29, 24, 57, 0], [2, "608", 370, -17, 24, 57, 0], [2, "608", 394, -5, 24, 57, 0], [2, "614", 345, -37, 22, 19, 0], [2, "614", 365, -27, 22, 19, 0], [2, "614", 387, -16, 22, 19, 0], [2, "614", 407, -6, 22, 19, 0], [2, "612", 338, -41, 8, 26, 0], [2, "613", 333, -58, 12, 20, 0], [2, "611", 345, -43, 22, 15, 0], [2, "611", 364, -34, 22, 15, 0], [2, "611", 382, -25, 22, 15, 0], [2, "611", 402, -17, 22, 15, 0], [2, "612", 338, -20, 8, 26, 0], [2, "612", 338, -2, 8, 26, 0], [2, "612", 425, -6, 8, 26, 0], [2, "612", 425, 15, 8, 26, 0], [2, "612", 425, 33, 8, 26, 0], [2, "652", 448, -3, 12, 32, 2], [2, "608_1", 466, -66, 24, 57, 1], [2, "652", 471, -13, 12, 32, 2], [2, "647", 463, -57, 32, 37, 2], [2, "612", 445, 1, 8, 26, 2], [2, "612", 490, -20, 8, 26, 2], [2, "614_1", 433, -7, 22, 19, 2], [2, "614_1", 454, -17, 22, 19, 2], [2, "614_1", 475, -27, 22, 19, 2], [2, "614_1", 433, 21, 22, 19, 2], [2, "614_1", 453, 11, 22, 19, 2], [2, "614_1", 473, 1, 22, 19, 2], [2, "614_1", 492, -9, 22, 19, 2], [2, "608_1", 568, 44, 24, 57, 1], [2, "608_1", 547, 53, 24, 57, 1], [2, "652", 599, 32, 12, 32, 2], [2, "612", 586, 67, 8, 26, 2], [2, "608_1", 572, -11, 24, 57, 1], [2, "608_1", 547, 19, 24, 57, 1], [2, "652", 561, 47, 12, 32, 2], [2, "652", 575, 42, 12, 32, 2], [2, "608", 522, 23, 24, 57, 3], [2, "608", 522, 50, 24, 57, 0], [2, "608", 458, -10, 24, 57, 3], [2, "608", 482, 2, 24, 57, 3], [2, "608", 508, 13, 24, 57, 3], [2, "608", 461, 21, 24, 57, 0], [2, "608", 486, 33, 24, 57, 0], [2, "608", 510, 45, 24, 57, 0], [2, "610", 480, 39, 30, 54, 0], [2, "624", 481, 79, 22, 18, 0], [2, "609", 516, 60, 14, 30, 0], [2, "607", 517, 51, 12, 14, 0], [2, "609", 463, 37, 14, 30, 0], [2, "607", 464, 28, 12, 14, 0], [2, "614", 461, 13, 22, 19, 0], [2, "614", 481, 23, 22, 19, 0], [2, "614", 503, 34, 22, 19, 0], [2, "614", 523, 44, 22, 19, 0], [2, "612", 454, 9, 8, 26, 0], [2, "613", 449, -8, 12, 20, 0], [2, "611", 461, 7, 22, 15, 0], [2, "611", 480, 16, 22, 15, 0], [2, "611", 498, 25, 22, 15, 0], [2, "611", 518, 33, 22, 15, 0], [2, "612", 454, 30, 8, 26, 0], [2, "612", 454, 48, 8, 26, 0], [2, "612", 541, 44, 8, 26, 0], [2, "612", 541, 65, 8, 26, 0], [2, "612", 541, 83, 8, 26, 0], [2, "652", 564, 47, 12, 32, 2], [2, "608_1", 582, -16, 24, 57, 1], [2, "652", 587, 37, 12, 32, 2], [2, "647", 579, -7, 32, 37, 2], [2, "612", 561, 51, 8, 26, 2], [2, "612", 606, 30, 8, 26, 2], [2, "614_1", 549, 43, 22, 19, 2], [2, "614_1", 570, 33, 22, 19, 2], [2, "614_1", 591, 23, 22, 19, 2], [2, "614_1", 549, 71, 22, 19, 2], [2, "614_1", 569, 61, 22, 19, 2], [2, "614_1", 589, 51, 22, 19, 2], [2, "614_1", 608, 41, 22, 19, 2], [2, "608_1", 700, 68, 24, 57, 1], [2, "608_1", 679, 77, 24, 57, 1], [2, "652", 731, 56, 12, 32, 2], [2, "612", 718, 91, 8, 26, 2], [2, "608_1", 704, 13, 24, 57, 1], [2, "608_1", 679, 43, 24, 57, 1], [2, "652", 693, 71, 12, 32, 2], [2, "652", 707, 66, 12, 32, 2], [2, "608", 654, 47, 24, 57, 3], [2, "608", 654, 74, 24, 57, 0], [2, "608", 590, 14, 24, 57, 3], [2, "608", 614, 26, 24, 57, 3], [2, "608", 641, 37, 24, 57, 3], [2, "608", 593, 45, 24, 57, 0], [2, "608", 618, 57, 24, 57, 0], [2, "608", 642, 69, 24, 57, 0], [2, "610", 612, 63, 30, 54, 0], [2, "624", 613, 103, 22, 18, 0], [2, "609", 648, 84, 14, 30, 0], [2, "607", 649, 75, 12, 14, 0], [2, "609", 595, 61, 14, 30, 0], [2, "607", 596, 52, 12, 14, 0], [2, "614", 593, 37, 22, 19, 0], [2, "614", 613, 47, 22, 19, 0], [2, "614", 635, 58, 22, 19, 0], [2, "614", 655, 68, 22, 19, 0], [2, "612", 586, 33, 8, 26, 0], [2, "613", 581, 16, 12, 20, 0], [2, "611", 593, 31, 22, 15, 0], [2, "611", 612, 40, 22, 15, 0], [2, "611", 630, 49, 22, 15, 0], [2, "611", 650, 57, 22, 15, 0], [2, "612", 586, 54, 8, 26, 0], [2, "612", 586, 72, 8, 26, 0], [2, "612", 673, 68, 8, 26, 0], [2, "612", 673, 89, 8, 26, 0], [2, "612", 673, 107, 8, 26, 0], [2, "652", 696, 71, 12, 32, 2], [2, "608_1", 714, 8, 24, 57, 1], [2, "652", 719, 61, 12, 32, 2], [2, "647", 711, 17, 32, 37, 2], [2, "612", 693, 75, 8, 26, 2], [2, "612", 738, 54, 8, 26, 2], [2, "614_1", 681, 67, 22, 19, 2], [2, "614_1", 702, 57, 22, 19, 2], [2, "614_1", 723, 47, 22, 19, 2], [2, "614_1", 681, 95, 22, 19, 2], [2, "614_1", 701, 85, 22, 19, 2], [2, "614_1", 721, 75, 22, 19, 2], [2, "614_1", 740, 65, 22, 19, 2], [2, "612", 380, -12, 8, 26, 0], [2, "652", 388, -8, 12, 32, 0], [2, "652", 403, -1, 12, 32, 0], [2, "652", 414, 5, 12, 32, 0], [2, "652", 396, -4, 12, 32, 0], [2, "652", 345, -28, 12, 32, 0], [2, "652", 353, -25, 12, 32, 0], [2, "652", 361, -21, 12, 32, 0], [2, "652", 370, -18, 12, 32, 0], [2, "614_1", 367, 4, 22, 19, 0], [2, "614_1", 345, -7, 22, 19, 0], [2, "614_1", 383, 12, 22, 19, 0], [2, "614_1", 404, 22, 22, 19, 0], [2, "618", 1337, 352, 30, 61, 0], [2, "608", 142, 4, 24, 57, 2], [2, "614_1", 71, -34, 22, 19, 0], [2, "614_1", 93, -23, 22, 19, 0], [2, "614_1", 115, -12, 22, 19, 0], [2, "652", 88, -18, 12, 32, 0], [2, "612", 134, -3, 8, 26, 0], [2, "612", 112, 22, 8, 26, 0], [2, "612", 87, 10, 8, 26, 0], [2, "612", 82, -21, 8, 26, 0], [2, "614_1", 69, -8, 22, 19, 0], [2, "652", 98, -14, 12, 32, 0], [2, "652", 109, -8, 12, 32, 0], [2, "614_1", 91, 3, 22, 19, 0], [2, "612", 120, -3, 8, 26, 0], [2, "612", 217, -38, 8, 26, 2], [2, "612", 217, -24, 8, 26, 2], [2, "612", 217, 0, 8, 26, 2], [2, "613", 215, -50, 12, 20, 2], [2, "611", 196, -36, 22, 15, 2], [2, "611", 178, -27, 22, 15, 2], [2, "611", 159, -19, 22, 15, 2], [2, "611", 141, -10, 22, 15, 2], [2, "613", 135, -21, 12, 20, 2], [2, "612", 70, -15, 8, 26, 0], [2, "612", 70, 3, 8, 26, 0], [2, "614_1", 113, 14, 22, 19, 0], [2, "612", 134, 18, 8, 26, 0], [2, "612", 134, 36, 8, 26, 0], [2, "614_1", 142, -3, 22, 19, 2], [2, "614_1", 162, -13, 22, 19, 2], [2, "614_1", 183, -23, 22, 19, 2], [2, "614_1", 198, -30, 22, 19, 2], [2, "608_1", 86, -70, 24, 57, 3], [2, "647", 78, -62, 32, 37, 0], [2, "670", 145, -114, 90, 71, 2], [2, "606", 95, -77, 54, 64, 2], [2, "606", 104, -81, 54, 64, 2], [2, "606", 113, -85, 54, 64, 2], [2, "606", 122, -88, 54, 64, 2], [2, "606", 131, -91, 54, 64, 2], [2, "606", 140, -95, 54, 64, 2], [2, "606", 149, -99, 54, 64, 2], [2, "606", 157, -104, 54, 64, 2], [2, "606", 166, -108, 54, 64, 2], [2, "638", 173, 340, 22, 18, 0], [2, "638", 227, 307, 22, 18, 0], [2, "639", 232, 293, 12, 19, 0], [2, "644", 245, 291, 30, 11, 2], [2, "644", 275, 291, 30, 11, 0], [2, "638", 300, 299, 22, 18, 0], [2, "639", 305, 287, 12, 19, 0], [2, "627", 42, 608, 60, 45, 0], [2, "628", 50, 588, 42, 40, 0], [2, "670", 324, -121, 90, 71, 0], [2, "606", 338, -113, 54, 64, 0], [2, "606", 346, -109, 54, 64, 0], [2, "606", 355, -105, 54, 64, 0], [2, "606", 363, -101, 54, 64, 0], [2, "606", 371, -97, 54, 64, 0], [2, "606", 379, -93, 54, 64, 0], [2, "606", 388, -89, 54, 64, 0], [2, "606", 396, -85, 54, 64, 0], [2, "606", 405, -82, 54, 64, 0], [2, "606", 413, -78, 54, 64, 0], [2, "606", 421, -74, 54, 64, 0], [2, "670", 422, -72, 90, 71, 0], [2, "670", 443, -72, 90, 71, 0], [2, "606", 457, -64, 54, 64, 0], [2, "606", 465, -60, 54, 64, 0], [2, "606", 474, -56, 54, 64, 0], [2, "606", 482, -52, 54, 64, 0], [2, "606", 490, -48, 54, 64, 0], [2, "606", 498, -44, 54, 64, 0], [2, "606", 507, -40, 54, 64, 0], [2, "606", 515, -36, 54, 64, 0], [2, "606", 524, -33, 54, 64, 0], [2, "606", 532, -29, 54, 64, 0], [2, "606", 540, -25, 54, 64, 0], [2, "670", 541, -23, 90, 71, 0], [2, "670", 574, -48, 90, 71, 0], [2, "606", 588, -40, 54, 64, 0], [2, "606", 596, -36, 54, 64, 0], [2, "606", 605, -32, 54, 64, 0], [2, "606", 613, -28, 54, 64, 0], [2, "606", 621, -24, 54, 64, 0], [2, "606", 629, -20, 54, 64, 0], [2, "606", 638, -16, 54, 64, 0], [2, "606", 646, -12, 54, 64, 0], [2, "606", 655, -9, 54, 64, 0], [2, "606", 663, -5, 54, 64, 0], [2, "606", 671, -1, 54, 64, 0], [2, "670", 672, 1, 90, 71, 0], [2, "670", 60, -76, 90, 71, 2], [2, "608_1", -18, -31, 24, 57, 3], [2, "608", 103, -25, 24, 57, 1], [2, "608", 103, 9, 24, 57, 2], [2, "608", 93, -19, 24, 57, 1], [2, "608", 69, -8, 24, 57, 1], [2, "608", 44, 3, 24, 57, 1], [2, "608", 94, 15, 24, 57, 2], [2, "608", 69, 27, 24, 57, 2], [2, "608_1", 14, -3, 24, 57, 3], [2, "608_1", 14, 34, 24, 57, 3], [2, "608_1", -2, -11, 24, 57, 3], [2, "608_1", 1, 27, 24, 57, 3], [2, "608_1", -23, 15, 24, 57, 3], [2, "608_1", -26, -22, 24, 57, 3], [2, "612", -27, -2, 8, 26, 0], [2, "613", -32, -19, 12, 20, 0], [2, "608", 45, 38, 24, 57, 2], [2, "614_1", -26, 0, 22, 19, 0], [2, "614_1", -4, 11, 22, 19, 0], [2, "614_1", 18, 22, 22, 19, 0], [2, "652", -9, 16, 12, 32, 0], [2, "612", 37, 31, 8, 26, 0], [2, "612", 15, 56, 8, 26, 0], [2, "612", -10, 44, 8, 26, 0], [2, "612", -15, 13, 8, 26, 0], [2, "614_1", -28, 26, 22, 19, 0], [2, "652", 1, 20, 12, 32, 0], [2, "652", 12, 26, 12, 32, 0], [2, "614_1", -6, 37, 22, 19, 0], [2, "612", 23, 31, 8, 26, 0], [2, "612", 120, -4, 8, 26, 2], [2, "612", 120, 10, 8, 26, 2], [2, "612", 120, 34, 8, 26, 2], [2, "613", 118, -16, 12, 20, 2], [2, "611", 99, -2, 22, 15, 2], [2, "611", 81, 7, 22, 15, 2], [2, "611", 62, 15, 22, 15, 2], [2, "611", 44, 24, 22, 15, 2], [2, "613", 38, 13, 12, 20, 2], [2, "612", -27, 19, 8, 26, 0], [2, "612", -27, 37, 8, 26, 0], [2, "614_1", 16, 48, 22, 19, 0], [2, "612", 37, 52, 8, 26, 0], [2, "612", 37, 70, 8, 26, 0], [2, "610", 74, 28, 30, 54, 2], [2, "614_1", 45, 31, 22, 19, 2], [2, "614_1", 65, 21, 22, 19, 2], [2, "614_1", 86, 11, 22, 19, 2], [2, "614_1", 101, 4, 22, 19, 2], [2, "608_1", -11, -36, 24, 57, 3], [2, "647", -19, -28, 32, 37, 0], [2, "670", 48, -80, 90, 71, 2], [2, "606", -2, -43, 54, 64, 2], [2, "606", 7, -47, 54, 64, 2], [2, "606", 16, -51, 54, 64, 2], [2, "606", 25, -54, 54, 64, 2], [2, "606", 34, -57, 54, 64, 2], [2, "606", 43, -61, 54, 64, 2], [2, "606", 52, -65, 54, 64, 2], [2, "606", 60, -70, 54, 64, 2], [2, "606", 69, -74, 54, 64, 2], [2, "652", 192, -13, 12, 32, 2], [2, "652", 154, 2, 12, 32, 2], [2, "652", 168, -3, 12, 32, 2], [2, "652", 157, 2, 12, 32, 2], [2, "652", 180, -8, 12, 32, 2], [2, "612", 154, 6, 8, 26, 2], [2, "612", 199, -15, 8, 26, 2], [2, "614_1", 142, 26, 22, 19, 2], [2, "614_1", 162, 16, 22, 19, 2], [2, "614_1", 182, 6, 22, 19, 2], [2, "614_1", 201, -4, 22, 19, 2], [2, "608_1", 590, 236, 24, 57, 2], [2, "608_1", 585, 208, 24, 57, 1], [2, "608_1", 575, 204, 24, 57, 1], [2, "608_1", 562, 209, 24, 57, 1], [2, "608_1", 562, 253, 24, 57, 2], [2, "608", 529, 252, 24, 57, 0], [2, "608", 512, 244, 24, 57, 0], [2, "608", 488, 232, 24, 57, 0], [2, "652", 543, 267, 12, 32, 0], [2, "623", 530, 243, 24, 25, 0], [2, "672", 516, 169, 36, 80, 0], [2, "671", 476, 170, 40, 112, 0], [2, "623", 487, 222, 24, 25, 0], [2, "623", 511, 234, 24, 25, 0], [2, "620", 487, 173, 42, 44, 0], [2, "620", 493, 176, 42, 44, 0], [2, "620", 499, 179, 42, 44, 0], [2, "620", 505, 182, 42, 44, 0], [2, "620", 511, 185, 42, 44, 0], [2, "620", 517, 188, 42, 44, 0], [2, "620", 523, 192, 42, 44, 0], [2, "620", 529, 195, 42, 44, 0], [2, "620", 536, 198, 42, 44, 0], [2, "614", 487, 215, 22, 19, 0], [2, "614", 509, 226, 22, 19, 0], [2, "614", 520, 231, 22, 19, 0], [2, "614", 487, 234, 22, 19, 0], [2, "614", 509, 245, 22, 19, 0], [2, "614", 520, 250, 22, 19, 0], [2, "652", 488, 243, 12, 32, 0], [2, "652", 499, 248, 12, 32, 0], [2, "652", 521, 259, 12, 32, 0], [2, "614_1", 488, 262, 22, 19, 0], [2, "652", 532, 264, 12, 32, 0], [2, "614_1", 526, 281, 22, 19, 0], [2, "614", 534, 238, 22, 19, 0], [2, "620", 543, 200, 42, 44, 0], [2, "620", 549, 204, 42, 44, 0], [2, "614", 533, 257, 22, 19, 0], [2, "614_1", 534, 285, 22, 19, 0], [2, "671", 549, 202, 40, 112, 0], [2, "672", 589, 201, 36, 80, 0], [2, "608_1", 584, 242, 24, 57, 2], [2, "647", 574, 213, 32, 37, 2], [2, "614_1", 567, 255, 22, 19, 2], [2, "614_1", 588, 245, 22, 19, 2], [2, "614_1", 564, 256, 22, 19, 2], [2, "612", 587, 261, 8, 26, 2], [2, "612", 587, 274, 8, 26, 2], [2, "608_1", 671, 273, 24, 57, 2], [2, "608_1", 666, 245, 24, 57, 1], [2, "608_1", 656, 241, 24, 57, 1], [2, "608_1", 643, 246, 24, 57, 1], [2, "608_1", 643, 290, 24, 57, 2], [2, "608", 610, 289, 24, 57, 0], [2, "608", 593, 281, 24, 57, 0], [2, "608", 569, 269, 24, 57, 0], [2, "652", 624, 304, 12, 32, 0], [2, "623", 611, 280, 24, 25, 0], [2, "672", 597, 206, 36, 80, 0], [2, "671", 557, 207, 40, 112, 0], [2, "623", 568, 259, 24, 25, 0], [2, "623", 592, 271, 24, 25, 0], [2, "620", 568, 210, 42, 44, 0], [2, "620", 574, 213, 42, 44, 0], [2, "620", 580, 216, 42, 44, 0], [2, "620", 586, 219, 42, 44, 0], [2, "620", 592, 222, 42, 44, 0], [2, "620", 598, 225, 42, 44, 0], [2, "620", 604, 229, 42, 44, 0], [2, "620", 610, 232, 42, 44, 0], [2, "620", 617, 235, 42, 44, 0], [2, "614", 568, 252, 22, 19, 0], [2, "614", 590, 263, 22, 19, 0], [2, "614", 601, 268, 22, 19, 0], [2, "614", 568, 271, 22, 19, 0], [2, "614", 590, 282, 22, 19, 0], [2, "614", 601, 287, 22, 19, 0], [2, "652", 569, 280, 12, 32, 0], [2, "652", 580, 285, 12, 32, 0], [2, "652", 591, 291, 12, 32, 0], [2, "652", 602, 296, 12, 32, 0], [2, "614_1", 569, 299, 22, 19, 0], [2, "614_1", 590, 309, 22, 19, 0], [2, "652", 613, 301, 12, 32, 0], [2, "614_1", 607, 318, 22, 19, 0], [2, "614", 615, 275, 22, 19, 0], [2, "620", 624, 237, 42, 44, 0], [2, "620", 630, 241, 42, 44, 0], [2, "614", 614, 294, 22, 19, 0], [2, "614_1", 615, 322, 22, 19, 0], [2, "671", 630, 239, 40, 112, 0], [2, "672", 670, 238, 36, 80, 0], [2, "608_1", 665, 279, 24, 57, 2], [2, "647", 655, 250, 32, 37, 2], [2, "614_1", 648, 292, 22, 19, 2], [2, "614_1", 669, 282, 22, 19, 2], [2, "614_1", 645, 293, 22, 19, 2], [2, "612", 668, 298, 8, 26, 2], [2, "612", 668, 311, 8, 26, 2], [2, "612", 513, 271, 8, 26, 0], [2, "652", 510, 254, 12, 32, 0], [2, "614_1", 509, 272, 22, 19, 0], [2, "624", 82, 67, 22, 18, 2], [2, "647", 658, 311, 32, 37, 2], [2, "674", -7, 196, 92, 72, 0], [2, "673", -16, 213, 68, 62, 0], [2, "674", 26, 180, 92, 72, 0], [2, "654", 292, 363, 94, 45, 1], [2, "645", 10, 71, 14, 38, 0], [2, "645", -16, 84, 14, 38, 0], [2, "14_1", 469, 264, 32, 30, 2], [2, "14_1", 439, 267, 32, 30, 2], [2, "14_1", 454, 248, 32, 30, 2], [2, "647", 459, 268, 32, 37, 2], [2, "90", 626, 412, 28, 36, 0], [2, "90", 118, 41, 28, 36, 0], [2, "664", 197, 6, 52, 51, 2], [2, "90", 213, 9, 28, 36, 2], [2, "665", 200, 21, 30, 34, 2], [2, "90", 174, 30, 36, 28, 7], [2, "420", 16, 103, 16, 13, 0], [2, "422", -10, 111, 16, 14, 0], [2, "421", 45, 86, 14, 11, 2], [2, "647", 423, 24, 32, 37, 2], [2, "420", 263, 117, 16, 13, 0], [2, "90", 493, 275, 28, 36, 0], [2, "14_1", -17, 62, 32, 30, 2], [2, "677", 219, -32, 16, 13, 2], [2, "420", 449, 291, 16, 13, 2], [2, "119", 539, 374, 18, 31, 0], [2, "679", 45, 185, 36, 33, 0], [2, "327", 567, 89, 30, 22, 0], [2, "422", 540, 98, 16, 14, 0], [2, "422", 422, 47, 16, 14, 0], [2, "420", 171, 692, 16, 13, 2], [2, "420", 31, 625, 16, 13, 2], [2, "422", 69, 638, 16, 14, 0], [2, "326", 32, 92, 18, 14, 0], [2, "329", 177, 673, 42, 37, 2], [2, "327", 212, 700, 30, 22, 0], [2, "422", 160, 40, 16, 14, 0], [2, "679", 71, 192, 36, 33, 0], [2, "679", 36, 207, 36, 33, 0], [2, "422", 524, 384, 16, 14, 0], [2, "422", 363, 433, 16, 14, 0], [2, "422", 161, 682, 16, 14, 2], [2, "11", 25, 266, 32, 29, 0], [2, "638", 300, 450, 22, 18, 0], [2, "638", 343, 433, 22, 18, 0], [2, "639", 305, 438, 12, 19, 0], [2, "639", 305, 421, 12, 19, 0], [2, "132_1", 318, 405, 26, 41, 0], [2, "132_1", 326, 401, 26, 41, 0], [2, "677", 353, 400, 16, 13, 0], [2, "639", 348, 404, 12, 19, 0], [2, "639", 348, 421, 12, 19, 0], [2, "115", 107, 39, 16, 37, 0], [2, "115", 394, 222, 16, 37, 0], [2, "624", 344, 15, 22, 18, 0], [2, "624", 364, 25, 22, 18, 0], [2, "624", 383, 34, 22, 18, 0], [2, "624", 401, 43, 22, 18, 0], [2, "327", 420, 395, 30, 22, 0], [2, "645", 602, 592, 14, 38, 0], [2, "645", 630, 592, 14, 38, 2], [2, "420", 620, 617, 16, 13, 0], [2, "661", 167, 327, 28, 38, 3], [2, "327", 224, 435, 30, 22, 0], [2, "90", 413, 236, 28, 36, 0], [2, "670", -37, -42, 90, 71, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 88, -1, 83, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 75, 76, 77, -1, -1, -1, 83, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 75, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, 61, -1, -1, -1, -1, -1, -1, -1, 82, 81, 75, 75, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, 61, -1, 61, 61, 61, 61, -1, 83, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, 76, 82, 81, 61, -1, -1, -1, -1, -1, -1, 93, 92, 91, 86, 87, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, -1, -1, -1, -1, -1, 93, 92, 91, 67, 75, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 75, 62, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 93, 92, 91, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 88, -1, -1, -1, -1, -1, 83, 82, 81, 67, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 91, 67, 67, 75, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, 67, 61, 49, -1, -1, -1, -1, -1, -1, -1, 81, 75, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, 75, 81, -1, -1, -1, -1, -1, -1, 83, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 93, 92, 91, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 51, -1, 83, 82, 81, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, 67, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 62, 68, 55, 62, 63, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 64, 63, 65, 64, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 64, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 53, 52, 51, -1, -1, 53, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 64, 63, -1, -1, 65, 64, 63, -1, -1, -1, 93, 92, 91, -1, -1, -1, -1, -1]}, {"type": 2, "data": [-87, -89, -88, -87, -89, -88, -87, -89, -76, -77, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -95, -94, -93, -95, -94, -93, -70, -70, -69, -68, -94, -93, -95, -94, -93, -95, -94, -93, -70, -69, -93, -95, -94, -93, -95, -94, -93, -92, -91, -90, -92, -91, -90, -73, -73, -72, -92, -91, -90, -92, -91, -90, -92, -91, -90, -73, -72, -90, -92, -91, -90, -92, -91, -90, -64, -69, -87, -89, -88, -87, -89, -88, -87, -78, -79, -80, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -70, -69, -87, -67, -68, -93, -95, -94, -93, -95, -94, -93, -75, -76, -77, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -63, -64, -65, -93, -92, -91, -90, -92, -91, -90, -92, -91, -90, -66, -67, -68, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -66, -67, -68, -90, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -95, -94, -93, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -92, -91, -90, -92, -91, -90, -92, -91, -71, -70, -91, -90, -92, -91, -92, -91, -90, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -89, -88, -87, -89, -88, -87, -89, -88, -74, -67, -68, -87, -89, -88, -89, -88, -87, -88, -87, -89, -88, -87, -89, -88, -87, -89, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -89, -88, -87, -92, -91, -90, -95, -94, -93, -95, -94, -93, 4, 4, 4, 4, 4, 4, 4, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -89, -88, -87, -92, -91, -90, -92, -91, 7, 7, 7, 7, 7, 7, 7, 7, 7, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -95, -94, -93, -89, -86, -85, -79, 5, 5, 0, 1, 0, 1, 0, 1, 0, 5, -79, -80, -94, -93, -95, -94, -93, -95, -94, -93, -92, -91, -90, -92, -71, -70, -69, 3, 5, 3, 4, 3, 4, 3, 4, 3, -63, -64, -65, -91, -90, -92, -91, -90, -92, -91, -90, -89, -88, -87, -89, -74, -73, -72, -88, -87, -89, -88, -95, -94, -93, -95, -94, -66, -67, -68, -88, -87, -89, -88, -87, -89, -66, -67, -95, -94, -93, -95, -94, -93, -95, -66, -63, -64, -70, -70, -70, -70, -69, -73, -72, -94, -93, -95, -94, -93, -94, -93, -95, -94, -93, -92, -91, -90, -92, -91, -90, -92, -92, -66, -67, -63, -64, -69, -73, -72, -90, -92, -91, -90, -92, -91, -90, -91, -90, -92, -91, -90, -89, -88, -87, -89, -88, -87, -89, -89, -88, -87, -66, -67, -68, -89, -88, -87, -89, -88, -87, -89, -88, -87, -88, -87, -86, -85, -84, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -89, -88, -87, -89, -88, -87, -89, -88, -87, -94, -93, -95, -94, -93, -83, -82, -81, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -66, -67, -67, -85, -84, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -82, -81, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -92, -63, -64, -79, -80, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -79, -79, -80, -89, -66, -63, -64, -65, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -81, -95, -94, -74, -73, -72, -93, -86, -85, -86, -85, -79, -80, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -95, -94, -93, -92, -91, -90, -92, -91, -90, -83, -82, -82, -82, -76, -81, -84, -78, -79, -80, -91, -90, -92, -91, -90, -92, -91, -90, -92, -91, -90, -89, -88, -87, -89, -88, -87, -74, -73, -63, -82, -70, -81, -81, -75, -76, -72, -88, -87, -89, -88, -87, -89, -88, -87, -89, -88, -87]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}