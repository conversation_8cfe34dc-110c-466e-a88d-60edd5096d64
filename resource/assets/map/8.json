{"mW": 720, "mH": 960, "tW": 24, "tH": 24, "tiles": [["106", 0, 3, 3], ["139", 0, 1, 1], ["141", 0, 1, 1], ["142", 0, 3, 4], ["140", 2, 3, 4], ["142", 2, 3, 4], ["140", 0, 3, 4], ["140", 1, 3, 4], ["142", 1, 3, 4], ["203", 0, 2, 1], ["203_2", 0, 2, 1], ["203_3", 0, 2, 1], ["203_4", 0, 2, 1], ["203_1", 0, 2, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["311", 0, 3, 2], ["311", 2, 3, 2], ["311", 1, 3, 2], ["311", 3, 3, 2], ["142", 3, 3, 4]], "layers": [{"type": 3, "obj": [[2, "2", 51, 195, 90, 66, 0], [2, "2", 2, 246, 90, 66, 0], [2, "2", 98, 226, 90, 66, 2], [2, "20", 405, 724, 10, 14, 0], [2, "2", 117, 879, 90, 66, 0], [2, "2", 65, 933, 90, 66, 0], [2, "2", 131, 927, 90, 66, 2], [2, "2", 419, 323, 90, 66, 2], [2, "2", 473, 355, 90, 66, 0], [2, "2", 378, 379, 90, 66, 2], [2, "2", 235, 654, 90, 66, 2], [2, "224", 627, 817, 124, 194, 0], [2, "224", 566, 753, 124, 194, 0], [2, "224", 11, 841, 124, 194, 0], [2, "2", 291, 682, 90, 66, 2], [2, "2", 196, 713, 90, 66, 0], [2, "81", 584, 473, 6, 17, 0], [2, "81", 616, 490, 6, 17, 0], [2, "40", 95, 258, 28, 20, 0], [2, "40", 73, 268, 28, 20, 0], [2, "40", 52, 278, 28, 20, 0], [2, "37", 162, 296, 26, 26, 0], [2, "223", 151, 303, 24, 23, 0], [2, "223", 142, 307, 24, 23, 0], [2, "37", 130, 310, 26, 26, 0], [2, "223", 119, 317, 24, 23, 0], [2, "223", 111, 322, 24, 23, 0], [2, "37", 99, 325, 26, 26, 0], [2, "223", 88, 332, 24, 23, 0], [2, "223", 152, 282, 24, 23, 0], [2, "37", 140, 285, 26, 26, 0], [2, "223", 129, 292, 24, 23, 0], [2, "223", 120, 296, 24, 23, 0], [2, "37", 108, 299, 26, 26, 0], [2, "223", 97, 306, 24, 23, 0], [2, "223", 89, 311, 24, 23, 0], [2, "37", 77, 314, 26, 26, 0], [2, "223", 78, 340, 24, 23, 0], [2, "37", 20, 306, 26, 26, 2], [2, "223", 32, 315, 24, 23, 2], [2, "223", 42, 320, 24, 23, 2], [2, "37", 52, 320, 26, 26, 2], [2, "223", 64, 329, 24, 23, 2], [2, "223", 40, 296, 24, 23, 2], [2, "223", 50, 301, 24, 23, 2], [2, "37", 60, 301, 26, 26, 2], [2, "223", 72, 310, 24, 23, 2], [2, "40", 116, 261, 28, 20, 2], [2, "42", 113, 243, 16, 24, 0], [2, "38", 110, 265, 22, 17, 0], [2, "38", 102, 268, 22, 17, 0], [2, "38", 94, 272, 22, 17, 0], [2, "38", 86, 276, 22, 17, 0], [2, "38", 79, 279, 22, 17, 0], [2, "38", 72, 282, 22, 17, 0], [2, "40", 130, 267, 28, 20, 2], [2, "38", 125, 272, 22, 17, 0], [2, "38", 117, 275, 22, 17, 0], [2, "38", 109, 279, 22, 17, 0], [2, "38", 101, 283, 22, 17, 0], [2, "38", 94, 286, 22, 17, 0], [2, "38", 87, 289, 22, 17, 0], [2, "40", 125, 279, 28, 20, 0], [2, "40", 106, 288, 28, 20, 0], [2, "38", 64, 286, 22, 17, 0], [2, "38", 79, 293, 22, 17, 0], [2, "40", 93, 294, 28, 20, 0], [2, "40", 53, 288, 28, 20, 2], [2, "40", 69, 295, 28, 20, 2], [2, "39", 85, 308, 16, 83, 0], [2, "222", 152, 277, 60, 53, 0], [2, "222", 0, 288, 60, 53, 2], [2, "42", 148, 261, 16, 24, 0], [2, "42", 82, 286, 16, 24, 2], [2, "42", 49, 268, 16, 24, 2], [2, "53", 383, 197, 18, 9, 0]]}, {"type": 4, "obj": [[2, "84", 370, 59, 66, 65, 0], [2, "84", 581, 166, 66, 65, 0], [2, "223", 179, 315, 24, 23, 0], [2, "37", 167, 318, 26, 26, 0], [2, "223", 156, 325, 24, 23, 0], [2, "223", 8, 328, 24, 23, 2], [2, "223", 147, 329, 24, 23, 0], [2, "37", 18, 328, 26, 26, 2], [2, "37", 135, 332, 26, 26, 0], [2, "223", 30, 337, 24, 23, 2], [2, "223", 124, 339, 24, 23, 0], [2, "223", 40, 342, 24, 23, 2], [2, "223", 116, 344, 24, 23, 0], [2, "37", 50, 342, 26, 26, 2], [2, "37", 104, 347, 26, 26, 0], [2, "223", 62, 351, 24, 23, 2], [2, "223", 93, 354, 24, 23, 0], [2, "223", 73, 359, 24, 23, 2], [2, "223", 83, 362, 24, 23, 0], [2, "31", 161, 340, 14, 63, 2], [2, "31", 116, 358, 14, 63, 0], [2, "263_1", 254, 413, 34, 34, 0], [2, "224", 507, 277, 124, 194, 2], [2, "219", 537, 443, 36, 30, 0], [2, "4", 402, 359, 122, 119, 0], [2, "219", 470, 451, 36, 30, 2], [2, "224", 566, 304, 124, 194, 0], [2, "219", 398, 469, 36, 30, 0], [2, "220", 622, 491, 40, 29, 0], [2, "97", 551, 424, 36, 98, 0], [2, "83", 574, 484, 64, 38, 0], [2, "97", 620, 462, 36, 98, 0], [2, "224", 620, 368, 124, 194, 2], [2, "219", 668, 534, 36, 30, 2], [2, "224", -9, 373, 124, 194, 0], [2, "4", 277, 454, 122, 119, 0], [2, "224", -50, 394, 124, 194, 0], [2, "219", 332, 569, 36, 30, 0], [2, "23", 366, 555, 48, 65, 0], [2, "224", -7, 445, 124, 194, 0], [2, "219", 580, 623, 36, 30, 0], [2, "219", 470, 632, 36, 30, 2], [2, "179", 494, 627, 44, 67, 0], [2, "179", 536, 627, 44, 67, 2], [2, "219", 38, 667, 36, 30, 0], [2, "224", -24, 515, 124, 194, 0], [2, "224", 82, 520, 124, 194, 0], [2, "219", 143, 686, 36, 30, 2], [2, "14", 73, 702, 32, 30, 2], [2, "14", 45, 709, 32, 30, 2], [2, "219", 70, 713, 36, 30, 0], [2, "224", 46, 553, 124, 194, 0], [2, "219", 102, 720, 36, 30, 2], [2, "14", 49, 724, 32, 30, 2], [2, "224", -63, 606, 124, 194, 0], [2, "219", -7, 773, 36, 30, 2], [2, "19", 389, 681, 44, 123, 0], [2, "4", 215, 691, 122, 119, 0], [2, "219", 285, 782, 36, 30, 2]]}, {"type": 3, "obj": [[2, "313", 441, 515, 70, 44, 2], [2, "313", 622, 610, 70, 44, 0], [2, "313", 429, 629, 70, 44, 0], [2, "313", 398, 605, 70, 44, 2], [2, "313", 278, 775, 70, 44, 0], [2, "313", 58, 458, 70, 44, 2], [2, "313", 135, 428, 70, 44, 0], [2, "313", 149, 419, 70, 44, 2], [2, "313", 192, 389, 70, 44, 2], [2, "313", 383, 147, 70, 44, 2], [2, "313", 483, 132, 70, 44, 0], [2, "214", 330, 283, 54, 40, 2], [2, "313", 430, 139, 70, 44, 0], [2, "313", 345, 169, 70, 44, 0], [2, "25", 122, 359, 76, 45, 0], [2, "213", 159, 304, 64, 45, 0], [2, "213", 158, 317, 64, 45, 0], [2, "213", 231, 322, 64, 45, 0], [2, "214", 528, 326, 54, 40, 0], [2, "214", 591, 341, 54, 40, 0], [2, "213", 673, 305, 64, 45, 0], [2, "213", 428, 278, 64, 45, 0], [2, "213", 437, 377, 64, 45, 2], [2, "214", 248, 88, 54, 40, 0], [2, "214", 674, 340, 54, 40, 2], [2, "213", 589, 301, 64, 45, 0], [2, "213", 583, 314, 64, 45, 0], [2, "214", 629, 343, 54, 40, 0], [2, "214", 356, 225, 54, 40, 0], [2, "213", 311, 47, 64, 45, 0], [2, "213", 276, 339, 64, 45, 0], [2, "213", 351, 198, 64, 45, 0], [2, "214", 272, 142, 54, 40, 0], [2, "214", 399, 254, 54, 40, 0], [2, "213", 384, 233, 64, 45, 0], [2, "213", 550, 295, 64, 45, 2], [2, "214", 404, 273, 54, 40, 0], [2, "214", 450, 295, 54, 40, 0], [2, "213", 447, 264, 64, 45, 0], [2, "213", 497, 286, 64, 45, 0], [2, "213", 487, 273, 64, 45, 0], [2, "214", 500, 316, 54, 40, 0], [2, "213", 98, 120, 64, 45, 2], [2, "213", 626, 307, 64, 45, 0], [2, "214", 193, 184, 54, 40, 2], [2, "214", 296, 156, 54, 40, 0], [2, "214", 147, 131, 54, 40, 2], [2, "214", 318, 301, 54, 40, 2], [2, "213", 207, 325, 64, 45, 0], [2, "214", 376, 324, 54, 40, 2], [2, "214", 558, 329, 54, 40, 0], [2, "213", 317, 354, 64, 45, 0], [2, "213", 365, 349, 64, 45, 2], [2, "208", -35, 93, 78, 40, 1], [2, "28", 211, 386, 12, 27, 0], [2, "25", 149, 373, 76, 45, 0], [2, "25", 108, 394, 76, 45, 0], [2, "208", 468, 47, 78, 40, 3], [2, "152", 426, 356, 76, 40, 0], [2, "205", 370, 61, 54, 40, 2], [2, "220", 300, 378, 40, 29, 0], [2, "219", 368, 378, 36, 30, 0], [2, "208", 298, 121, 78, 40, 0], [2, "152", 296, 141, 76, 40, 0], [2, "208", 480, 248, 78, 40, 0], [2, "208", 31, 90, 78, 40, 1], [2, "208", 323, 81, 78, 40, 1], [2, "208", 189, 204, 78, 40, 3], [2, "208", 418, 46, 78, 40, 3], [2, "208", 355, 352, 78, 40, 3], [2, "205", 63, 128, 54, 40, 2], [2, "208", 115, 144, 78, 40, 0], [2, "208", 77, 95, 78, 40, 0], [2, "208", 108, 91, 78, 40, 0], [2, "205", 689, 285, 54, 40, 2], [2, "208", 397, 220, 78, 40, 2], [2, "208", 378, 48, 78, 40, 3], [2, "205", 309, 27, 54, 40, 0], [2, "208", 505, 267, 78, 40, 0], [2, "208", 286, 73, 78, 40, 0], [2, "212", 523, -31, 44, 99, 2], [2, "209", 520, -27, 44, 30, 0], [2, "212", 542, -14, 44, 99, 0], [2, "210", 547, 10, 42, 35, 0], [2, "212", 561, 2, 44, 99, 0], [2, "210", 566, 26, 42, 35, 0], [2, "211", 560, -10, 48, 24, 0], [2, "18", 293, 793, 88, 64, 0], [2, "95", 504, 25, 22, 33, 0], [2, "174", 286, 599, 68, 33, 1], [2, "174", 64, 762, 68, 33, 0], [2, "174", 683, 577, 68, 33, 0], [2, "63", 385, 782, 16, 31, 0], [2, "96", 517, 42, 18, 37, 0], [2, "95", 545, 63, 22, 33, 0], [2, "63", 573, 905, 16, 31, 0], [2, "68", 418, 546, 28, 44, 0], [2, "69", 519, 497, 46, 30, 0], [2, "68", 446, 532, 28, 44, 0], [2, "68", 475, 517, 28, 44, 0], [2, "68", 502, 504, 28, 44, 0], [2, "68", 578, 517, 28, 44, 2], [2, "68", 550, 503, 28, 44, 2], [2, "68", 606, 530, 28, 44, 2], [2, "68", 634, 544, 28, 44, 2], [2, "5", 313, 525, 42, 66, 0], [2, "5", 356, 525, 42, 66, 2], [2, "64", 446, 807, 14, 15, 2], [2, "63", 458, 807, 16, 31, 0], [2, "62", 431, 794, 16, 27, 0], [2, "64", 561, 905, 14, 15, 2], [2, "62", 554, 883, 16, 27, 0], [2, "63", 579, 914, 16, 31, 2], [2, "64", 592, 931, 14, 15, 2], [2, "62", 603, 929, 16, 27, 0], [2, "64", 617, 947, 14, 15, 2], [2, "62", 628, 945, 16, 27, 0], [2, "64", 206, 924, 14, 15, 2], [2, "63", 218, 924, 16, 31, 2], [2, "62", 191, 911, 16, 27, 0], [2, "64", 230, 940, 14, 15, 2], [2, "63", 244, 939, 16, 31, 2], [2, "63", 227, 830, 16, 31, 0], [2, "62", 270, 809, 16, 27, 2], [2, "63", 201, 822, 16, 31, 0], [2, "64", 192, 838, 14, 15, 0], [2, "62", 176, 840, 16, 27, 0], [2, "64", 388, 801, 14, 15, 2], [2, "63", 402, 803, 16, 31, 2], [2, "64", 418, 803, 14, 15, 0], [2, "70", 417, 561, 50, 26, 0], [2, "71", 541, 597, 50, 26, 0], [2, "67", 393, 560, 24, 43, 0], [2, "67", 663, 558, 24, 43, 2], [2, "70", 443, 572, 50, 26, 0], [2, "70", 467, 585, 50, 26, 0], [2, "70", 492, 597, 50, 26, 0], [2, "70", 442, 549, 50, 26, 0], [2, "70", 467, 560, 50, 26, 0], [2, "70", 516, 584, 50, 26, 0], [2, "70", 466, 537, 50, 26, 0], [2, "71", 564, 585, 50, 26, 0], [2, "70", 492, 524, 50, 26, 0], [2, "71", 565, 560, 50, 26, 0], [2, "71", 589, 572, 50, 26, 0], [2, "71", 614, 559, 50, 26, 0], [2, "70", 518, 512, 50, 26, 0], [2, "71", 586, 547, 50, 26, 0], [2, "71", 563, 534, 50, 26, 0], [2, "71", 543, 524, 50, 26, 0], [2, "71", 517, 609, 50, 26, 0], [2, "6", 202, 821, 98, 73, 0], [2, "7", 216, 829, 28, 27, 0], [2, "8", 289, 860, 38, 29, 0], [2, "174", 204, 893, 68, 33, 0], [2, "174", 385, 836, 68, 33, 2], [2, "174", 552, 676, 68, 33, 2], [2, "174", 653, 740, 68, 33, 0], [2, "174", 139, 641, 68, 33, 0], [2, "174", 184, 688, 68, 33, 0], [2, "174", 39, 742, 68, 33, 2], [2, "174", -3, 801, 68, 33, 0], [2, "174", 111, 486, 68, 33, 2], [2, "79", 588, 497, 28, 45, 0], [2, "80", 593, 508, 14, 19, 0], [2, "108", 583, 487, 6, 46, 0], [2, "108", 616, 503, 6, 46, 0], [2, "152", 260, 110, 76, 40, 2], [2, "164", 226, 113, 60, 30, 0], [2, "181", 578, 51, 104, 100, 0], [2, "152", 242, 54, 76, 40, 2], [2, "166", 311, 20, 30, 35, 0], [2, "152", 171, 167, 76, 40, 2], [2, "152", 442, 241, 76, 40, 2], [2, "8", 347, 917, 38, 29, 2], [2, "18", 444, 893, 88, 64, 2], [2, "8", 430, 936, 38, 29, 2], [2, "18", 291, 909, 88, 64, 2], [2, "27", 462, 907, 6, 8, 0], [2, "16", 468, 901, 18, 15, 2], [2, "16", 314, 911, 18, 15, 0], [2, "27", 315, 925, 6, 8, 0], [2, "27", 483, 896, 6, 8, 0], [2, "8", 516, 942, 38, 29, 0], [2, "8", 280, 951, 38, 29, 2], [2, "178", 538, 570, 70, 37, 1], [2, "178", 538, 534, 70, 37, 0], [2, "178", 478, 534, 70, 37, 2], [2, "178", 478, 570, 70, 37, 3], [2, "25", -12, 381, 76, 45, 0], [2, "28", 86, 443, 12, 27, 0], [2, "29", 159, 429, 42, 27, 0], [2, "29", 148, 418, 42, 27, 0], [2, "25", 18, 397, 76, 45, 0], [2, "25", 47, 412, 76, 45, 0], [2, "25", 60, 418, 76, 45, 0], [2, "93", 559, 185, 56, 29, 2], [2, "93", 560, 214, 56, 29, 3], [2, "93", 617, 185, 56, 29, 0], [2, "93", 617, 214, 56, 29, 1], [2, "93", 348, 80, 56, 29, 2], [2, "93", 348, 109, 56, 29, 3], [2, "93", 405, 80, 56, 29, 0], [2, "93", 405, 109, 56, 29, 1], [2, "4", 23, 226, 122, 119, 0], [2, "68", 638, 571, 28, 44, 0], [2, "68", 612, 584, 28, 44, 0], [2, "68", 584, 598, 28, 44, 0], [2, "68", 556, 612, 28, 44, 0], [2, "68", 539, 620, 28, 44, 0], [2, "68", 510, 619, 28, 44, 2], [2, "68", 482, 606, 28, 44, 2], [2, "68", 454, 592, 28, 44, 2], [2, "68", 426, 578, 28, 44, 2], [2, "68", 406, 568, 28, 44, 2], [2, "43", 414, 598, 82, 58, 0], [2, "43", 597, 592, 82, 58, 2], [2, "17", 329, 798, 38, 25, 0], [2, "27", 330, 820, 6, 8, 0], [2, "27", 312, 820, 6, 8, 0], [2, "16", 311, 806, 18, 15, 0], [2, "208", 627, 281, 78, 40, 2], [2, "205", 558, 281, 54, 40, 2], [2, "152", 552, 281, 76, 40, 0], [2, "205", 148, 110, 54, 40, 2], [2, "208", 343, 34, 78, 40, 0], [2, "208", 317, 339, 78, 40, 1], [2, "220", 285, 370, 40, 29, 0], [2, "208", 315, 347, 78, 40, 0], [2, "208", 123, 293, 78, 40, 2], [2, "208", 200, 301, 78, 40, 0], [2, "165", 228, 46, 42, 37, 0], [2, "205", 402, 356, 54, 40, 2], [2, "205", 463, 47, 54, 40, 0], [2, "219", 340, 373, 36, 30, 0], [2, "208", 650, 403, 78, 40, 2], [2, "63", 400, 363, 16, 31, 0], [2, "208", 561, 401, 78, 40, 2], [2, "220", 367, 441, 40, 29, 0], [2, "36", 271, 377, 140, 103, 0], [2, "219", 181, 343, 36, 30, 2], [2, "219", 253, 353, 36, 30, 0], [2, "220", 375, 420, 40, 29, 0], [2, "14", 393, 450, 32, 30, 2], [2, "14", 402, 465, 32, 30, 2], [2, "212", 679, 15, 44, 99, 1], [2, "209", 677, -7, 44, 30, 0], [2, "180", 579, -5, 104, 84, 0], [2, "212", 666, 12, 44, 99, 0], [2, "212", 675, 41, 44, 99, 2], [2, "209", 664, -11, 44, 30, 0], [2, "212", 677, 53, 44, 99, 0], [2, "210", 682, 77, 42, 35, 0], [2, "211", 676, 41, 48, 24, 0], [2, "96", 702, 129, 18, 37, 0], [2, "95", 671, 123, 22, 33, 0], [2, "208", 278, 315, 78, 40, 1], [2, "208", 311, 328, 78, 40, 1], [2, "152", 359, 314, 76, 40, 2], [2, "165", 392, 302, 42, 37, 2], [2, "213", -8, 304, 64, 45, 2], [2, "257", 20, 337, 14, 66, 0], [2, "257", 31, 339, 14, 66, 0], [2, "257", 38, 343, 14, 66, 0], [2, "257", 45, 347, 14, 66, 0], [2, "257", 52, 351, 14, 66, 0], [2, "257", 59, 355, 14, 66, 0], [2, "257", 66, 359, 14, 66, 0], [2, "257", 72, 362, 14, 66, 0], [2, "257", 79, 366, 14, 66, 0], [2, "257", 178, 328, 14, 66, 0], [2, "257", 167, 328, 14, 66, 0], [2, "257", 160, 331, 14, 66, 0], [2, "257", 153, 335, 14, 66, 0], [2, "257", 147, 338, 14, 66, 0], [2, "257", 140, 342, 14, 66, 0], [2, "257", 133, 345, 14, 66, 0], [2, "257", 126, 349, 14, 66, 0], [2, "257", 119, 352, 14, 66, 0], [2, "257", 113, 355, 14, 66, 0], [2, "257", 106, 359, 14, 66, 0], [2, "257", 99, 362, 14, 66, 0], [2, "257", 92, 366, 14, 66, 0], [2, "257", 85, 373, 14, 66, 0], [2, "241", 86, 381, 14, 11, 0], [2, "241", 179, 345, 14, 11, 0], [2, "34", 130, 364, 30, 53, 0], [2, "241", 179, 378, 14, 11, 0], [2, "241", 19, 385, 14, 11, 0], [2, "241", 19, 356, 14, 11, 0], [2, "208", -7, 281, 78, 40, 2], [2, "63", 321, 349, 16, 31, 2], [2, "241", 85, 420, 14, 11, 0], [2, "63", 696, 712, 16, 31, 2], [2, "64", 712, 712, 14, 15, 0], [2, "64", 706, 728, 14, 15, 2], [2, "62", 715, 729, 16, 27, 0], [2, "13", 342, 837, 22, 24, 0], [2, "13", 193, 372, 22, 24, 0], [2, "13", 234, 377, 22, 24, 2], [2, "14", 246, 390, 32, 30, 0], [2, "14", 221, 396, 32, 30, 0], [2, "14", 214, 384, 32, 30, 0], [2, "174", 397, 502, 68, 33, 0], [2, "205", 384, 214, 54, 40, 0], [2, "59", 329, 168, 84, 49, 0], [2, "60", 379, 210, 16, 16, 0], [2, "60", 377, 207, 16, 16, 0], [2, "60", 365, 215, 16, 16, 0], [2, "60", 357, 222, 16, 16, 0], [2, "60", 344, 232, 16, 16, 0], [2, "61", 333, 242, 16, 17, 0], [2, "57", 321, 178, 72, 44, 0], [2, "57", 311, 182, 72, 44, 0], [2, "56", 299, 188, 76, 47, 0], [2, "55", 288, 193, 70, 46, 0], [2, "56", 274, 206, 76, 47, 0], [2, "55", 266, 216, 70, 46, 0], [2, "313", 540, 127, 70, 44, 0], [2, "208", 274, 277, 78, 40, 3], [2, "208", 305, 270, 78, 40, 3], [2, "166", 354, 270, 30, 35, 2], [2, "313", 235, 245, 70, 44, 0], [2, "54", 241, 229, 86, 53, 0], [2, "313", 447, 450, 70, 44, 0], [2, "313", 245, 779, 70, 44, 2], [2, "174", 227, 786, 68, 33, 0], [2, "49", 336, 148, 20, 34, 0], [2, "53", 324, 168, 18, 9, 0], [2, "219", 135, 423, 36, 30, 0], [2, "263_1", 348, 441, 34, 34, 0], [2, "50", 321, 165, 8, 23, 0], [2, "52", 280, 175, 46, 22, 0], [2, "49", 394, 174, 20, 34, 0], [2, "50", 280, 189, 8, 23, 0], [2, "50", 376, 193, 8, 23, 0], [2, "52", 334, 203, 46, 22, 0], [2, "51", 247, 198, 38, 35, 0], [2, "50", 332, 216, 8, 23, 0], [2, "49", 240, 210, 20, 34, 0], [2, "51", 299, 225, 38, 35, 0], [2, "49", 294, 238, 20, 34, 0], [2, "263_1", 302, 250, 34, 34, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, -1, -1, 105, 116, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, 102, -1, -1, -1, 105, 116, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 116, 115, 110, 111, -1, -1, -1, 93, 104, 114, 97, 116, 97, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 106, 111, -1, -1, -1, -1, -1, 112, 111, -1, 105, 116, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 79, 80, 80, 81, 35, -1, -1, -1, -1, 93, 95, 105, 112, 80, 12, -104, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 20, -113, -114, -115, -1, 93, 99, -1, 97, 98, 99, -1, 20, 44, 46, -1, -1, -1, 11, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 116, 115, 97, 114, -1, -1, -1, -1, -1, 97, -1, -1, 38, -1, 101, 94, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, -1, -1, -1, -1, 113, 112, 106, 111, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, -1, -1, -1, -1, -1, -1, -1, 97, 97, 97, 97, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, -1, -1, -1, -1, -1, -1, -1, -1, 97, 97, 110, 116, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 97, -1, -1, -1, -1, -1, -1, 110, 106, -1, 113, 111, 105, -1, -1, -1, -1, -1, -1, -1, 93, 94, 95, -1, -1, -1, -1, -1, -1, -1, 97, 97, 97, 97, 97, 110, 116, -1, 111, 11, 12, 13, -1, -1, -1, -1, -1, 97, 97, 97, 97, 97, 97, -1, 97, 94, -1, -1, -1, -1, 106, 106, 97, 97, 106, 107, -1, -1, 11, 16, 47, 49, 9, 9, -1, -1, 108, 115, 110, -1, -1, 112, 111, 112, 106, 107, 109, 97, 97, 97, -1, -1, -1, -1, -1, -1, -1, -1, 14, 10, 50, 9, 9, 26, 36, 35, 105, 112, 111, -1, -1, -1, -1, -1, -1, -1, -1, 97, 106, 111, -1, -1, -1, -1, -1, -1, -1, -1, 14, 70, 69, 9, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 106, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 16, 50, 9, 26, 45, 43, -1, -1, 94, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 100, 99, 94, 95, -1, -1, -1, -1, -1, -1, 47, 69, 9, 26, 38, -1, -1, -1, 97, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 109, 114, 97, 97, -1, -1, -1, -1, -1, 74, 59, 60, 61, 34, 38, -1, 116, 115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 109, 110, 111, 97, 97, -1, 94, 95, -1, -1, 71, 75, 10, 10, 10, 38, -1, 113, 112, 106, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, 106, 107, -1, 97, -1, 103, 109, 102, -1, -1, -1, -1, 74, 10, 10, 10, 37, 37, 35, -1, 105, -1, -1, 97, -1, -1, -1, 97, 97, 110, 111, -1, -1, 97, 97, -1, 116, 111, 97, 100, 99, -1, 71, 21, 67, 68, 24, 23, 10, 37, 35, 113, 112, 112, 116, -1, 109, 110, 106, 107, -1, 79, 80, 97, 97, 100, 99, 108, 97, 115, 114, -1, -1, 74, -1, -1, 63, 28, 25, 48, 23, 37, 37, 35, 113, 112, 106, 107, -1, -1, -1, 19, 20, 105, 106, 97, 98, 104, 110, -122, 111, -1, 14, 70, 52, -1, -1, -1, 9, 9, 9, 25, 24, 23, 37, 37, 39, 35, -1, -1, -1, -1, -1, 101, 99, 97, 106, 107, 107, -121, -1, 11, -1, -1, -1, 42, 17, 59, 61, 60, 9, 9, 9, 28, 25, 24, 23, 37, 36, 35, -1, -1, -1, 104, 102, -1, -1, -1, -1, 11, 13, 47, -1, -1, 10, -1, -1, 19, 44, 75, 53, 9, 9, 9, 9, 9, 28, 25, 49, 23, 37, 36, 36, 97, 111, -1, -1, -1, 11, 16, 47, 52, 33, 34, 44, -1, 100, 99, -1, 18, 55, 56, 60, 60, 63, 60, 60, 60, 57, 28, 25, 24, 24, -1, -1, -1, 11, 12, 16, 47, 52, 33, 34, 46, -1, -1, -1, 107, -1, -1, 75, 75, 76, 10, 10, 10, 10, 10, 59, 57, 9, 9, 9, 11, 12, 13, 47, 48, 51, 52, 33, 34, 46, -1, -1, -1, 97, 97, 100, 99, -1, -1, 22, 21, 76, 76, 10, 10, 10, 59, 32, 56, 32, 16, 10, 47, 52, 33, 32, 56, 34, -1, -1, -1, -1, 97, 97, 99, 112, 111, 11, 36, 35, 22, 72, 73, 44, 20, 72, 72, 10, 10, 10, 48, 48, 69, 33, 34, 45, 46, -1, 105, -1, 109, 110, 112, 112, 111, -1, -1, 71, 72, 46, -1, -1, -1, -1, -1, -1, -1, 71, 44, 20, 9, 33, 63, 34, 44, 46, -1, -1, -1, 105, 106, 107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 34, 45, 43, -1, 97, -1, -1, -1, -1, -1, -1, -1, -1, -1, 100, 99, -1, 93, 94, -1, -1, 100, 99, -1, -1, -1, 99, -1, -1, 10, 45, 46, -1, -1, 97, 97, -1, 98, 100, 99, 93, 104, 97, 97, 103, 102, -1, 104, 97, 97, 97, 103, 102, -1, 93, 97, 97, 97, 99]}, {"type": 2, "data": [83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, -1, -1, -1, -1, -1, -1, -1, -1, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, -1, -1, -1, -1, -1, -1, -1, -1, 91, 91, 91, 91, 91, 91, 91, 91, 91, 91, 91, 91, 91, -1, -1, 4, 5, -1, -1, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 2, -1, -1, -1, 2, -1, -1, 2, 5, 1, 2, 2, 0, -1, -1, -1, -1, 2, -1, 0, 1, 2, 2, 4, -1, 85, 85, 85, 85, 85, -1, 0, -1, 2, 5, -1, -1, 124, 123, 4, 5, 5, 3, 4, -1, -1, -1, 5, -1, 3, 4, 5, 3, -1, -1, -1, -1, 85, 85, 85, -1, -1, 4, 5, 1, 6, -127, -126, -125, 2, 0, 125, 124, 123, 123, 6, 7, 8, -1, 6, 7, 8, 6, 7, -1, -1, 85, 85, 85, 85, 85, -1, -1, -1, 125, 124, 123, -123, -119, -120, 125, 124, 123, 1, 2, 0, 1, 2, 0, 3, 4, 5, 1, 2, 6, 7, 8, 85, 85, 85, 85, 85, -1, -1, 0, -117, -118, -126, -125, 7, -119, -120, -121, 4, 5, 3, 4, 5, 3, 6, 7, 8, 4, 5, 4, 5, 4, -1, -1, 85, 85, 85, -1, -1, -1, -1, -121, 2, 0, 1, 2, 2, 1, 2, -1, 5, 118, 119, 6, 1, 2, 0, -1, -1, 0, 1, 2, 4, 117, -1, 86, -1, -1, 86, -1, -1, 8, 2, 3, 4, 5, 8, 0, 1, 2, -126, -126, -125, 2, 4, 5, 0, -1, 2, -1, -1, 5, 1, -127, 124, -1, -1, 86, 87, 87, -1, -1, -1, 4, 7, 8, 2, 3, 2, 0, 6, 7, 8, 5, 7, -1, -1, -1, -1, -1, 1, 2, 0, 120, -117, 5, 5, 87, 87, 87, 87, -1, -1, -1, -1, 1, 2, 6, 0, 0, 2, 6, -1, -1, 0, -1, -1, -1, -1, -1, -1, 2, 1, -119, -120, -121, 8, 2, -1, 87, 87, 87, -1, -1, -1, -1, -1, 5, -1, 1, 1, 2, -1, -1, -1, -1, 2, -1, -1, -1, -1, -1, -1, 5, 3, 3, 4, 5, -1, 87, 87, 87, 87, 87, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, -1, 5, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 87, -1, 87, 87, 87, -1, -1, -1, -1, -1, -1, -1, 118, -1, -1, -1, -1, -1, -1, -1, 117, 127, 127, 119, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 88, 88, 88, 88, 88, 88, 88, 88, 121, -1, -1, -1, -1, -1, -1, -1, -1, -124, -123, -117, -118, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, 90, 90, 90, 90, 90, 90, 90, 6, -1, -1, -1, -1, -1, -1, -116, -117, -118, -126, -120, -121, -1, -1, -1, 7, -1, -1, 0, 1, 0, 8, 7, 8, 4, 5, 0, 1, -1, 4, 5, 4, 5, 4, 1, 2, -1, -120, -121, 3, -124, -1, -1, -1, 5, 5, 123, 117, -123, 124, 123, 4, -1, 4, 5, 8, 3, 4, 5, 7, 8, 7, 8, 0, 117, 123, 6, 7, 8, 6, -127, -126, -120, -121, -124, -123, 122, -122, -116, -1, 126, 1, 2, 0, 8, 5, 6, 7, 8, 1, 1, 2, 0, 1, -127, -121, 1, 2, 0, 1, 2, 2, 4, 5, -127, -120, -120, -121, -119, -120, -121, 4, 5, 6, 0, 8, 6, 7, 8, 4, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 5, 7, 8, 1, 2, 3, 4, 5, 1, 6, -1, -1, 2, 0, 117, 118, 0, 1, 1, 2, 1, 6, 7, 8, 6, 7, 8, 6, 7, 8, 8, 2, -1, 4, 124, 123, 7, 8, -1, -1, -1, -1, -1, 3, 120, 121, 124, 123, 2, 0, 1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 2, -124, -1, -1, 127, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 127, 126, 5, 3, 4, 3, 4, 5, 3, 4, 5, 3, 4, 5, 5, -127, -126, -120, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -118, 1, 4, 6, 124, 123, 7, 8, 6, 7, 8, 6, 7, 8, 8, 7, 8, 7, -127, -126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -118, -121, 2, 0, 1, 127, 126, 1, 6, 7, 0, 1, 4, 5, 3, 4, 5, 3, 4, 6, -127, 8, -1, 0, -1, -1, -1, -117, -118, -117, -117, -118, 1, 3, 4, 5, 3, 4, 5, 4, 3, 4, 0, 8, 6, 7, 8, 6, 7, 8, 3, 4, 5, -124, -123, -1, -118, -120, -120, -120, -120, -121, 0, 6, 117, 118, 123, 1, 2, 125, 0, 1, 2, 1, -1, -1, 1, 2, 1, 2, 6, 7, 8, -127, -126, -120, -121, 0, 1, 2, 0, 1, 2, 5, 120, 121, 126, 4, 127, -1, -121, 4, 5, 4, 5, -1, -1, -1, 4, 5, 4, 5, 4, 5, 3, 4, 5, 3, 2, 5, 3, 4, 117, 8, 6, -116, -117, -118, -120, -1, 6, 7, -1, -1, -1, 6, 0, 1, 7, 8, 7, 8, 7, 8, 6, 7, 8, 6, 7, 8, 6, 120, -116, 124, 123, -119, -120, -121, 2, 3, 3, -121, -1, -1, 1, 2, 3, 4, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, -127, 127, 126, 5, 3, 4, 5, 3, 4, 5, 3, 3, 4, 5, 124, 123, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, -120, -121, 8, 6, 7, 8, 6, 7, 8, 6, 6, -1, -123, -1, 126, 124, 124, 117, 124, 123, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, -127, -126, 0, 1, -120, -120, 120, 127, 126, 7, 8, 0, 1, 2, 0, 1, 2, 0, 1, 3, 4, 5, 3, 4, 5, 3, 117, 123, -1, -1, -1, -119, -120, -121, 5, 3, -119, -120, -121, 1, 2, 3, 4, 5, 3, 4, 5, 3, 4, 6, 7, 8, 6, 7, 8, 6, -119, -121, 6, 6, 7, 8, 6, 7, 8, 6, 7, 8, 3, 4, 5, 6, 0, 0, 6, 7, 8, 6, 7, 0, 1, 2, 0, 1, 2, -1, 1, 2, 0, 6, 7, 8, 0, 1, 2, 0, 1, 2, 6, 0, 8, 117, 123, 2, 0, 1, 2, 0, 1, 3, 4, 5, 117, 118, 119, -1, -1, 124, 123, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 5, -119, -121, 123, 117, 118, 124, 123, 4, 6, 7, 8, -1, 121, 122, 3, -1, 127, 126, 0, 1, 2, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, -119, -121, 120, 121, 127, 126, 124]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}