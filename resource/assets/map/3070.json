{"mW": 984, "mH": 720, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1233", 0, 3, 2], ["315", 0, 3, 3], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["154", 0, 2, 1], ["160", 0, 1, 1], ["160", 2, 1, 1]], "layers": [{"type": 3, "obj": [[2, "3988", 185, -45, 148, 179, 2], [2, "3988", 712, 472, 148, 179, 0], [2, "3988", 187, 209, 148, 179, 0], [2, "3988", 396, 295, 148, 179, 0], [2, "1501", -38, 168, 50, 26, 0], [2, "1501", -38, 194, 50, 26, 0], [2, "1501", -12, 181, 50, 26, 0], [2, "1501", -46, 216, 50, 26, 0], [2, "1501", -20, 203, 50, 26, 0], [2, "1501", 6, 190, 50, 26, 0], [2, "1501", 32, 203, 50, 26, 0], [2, "1501", 6, 216, 50, 26, 0], [2, "1501", -20, 229, 50, 26, 0], [2, "1501", 6, 242, 50, 26, 0], [2, "1501", 32, 229, 50, 26, 0], [2, "1501", 58, 216, 50, 26, 0], [2, "1501", -37, 203, 50, 26, 0], [2, "1501", -11, 216, 50, 26, 0], [2, "1501", -37, 229, 50, 26, 0], [2, "1501", -37, 255, 50, 26, 0], [2, "1501", -11, 242, 50, 26, 0], [2, "1501", 15, 229, 50, 26, 0], [2, "3410", 716, 356, 54, 74, 2], [2, "1501", 936, 520, 50, 26, 0], [2, "1501", 911, 533, 50, 26, 0], [2, "1501", 785, 600, 50, 26, 0], [2, "1501", 811, 587, 50, 26, 0], [2, "1501", 760, 613, 50, 26, 0], [2, "1501", 808, 589, 50, 26, 0], [2, "1501", 834, 576, 50, 26, 0], [2, "1501", 783, 602, 50, 26, 0], [2, "1501", 815, 605, 50, 26, 0], [2, "1501", 841, 592, 50, 26, 0], [2, "1501", 790, 618, 50, 26, 0], [2, "1501", 829, 615, 50, 26, 0], [2, "1501", 855, 602, 50, 26, 0], [2, "1501", 804, 628, 50, 26, 0], [2, "1501", 734, 626, 50, 26, 0], [2, "1501", 760, 613, 50, 26, 0], [2, "1501", 709, 639, 50, 26, 0], [2, "1501", 657, 665, 50, 26, 0], [2, "1501", 683, 652, 50, 26, 0], [2, "1501", 632, 678, 50, 26, 0], [2, "1501", 580, 704, 50, 26, 0], [2, "1501", 606, 691, 50, 26, 0], [2, "1501", 555, 717, 50, 26, 0], [2, "1501", 676, 681, 50, 26, 0], [2, "1501", 702, 668, 50, 26, 0], [2, "1501", 651, 694, 50, 26, 0], [2, "1501", 810, 638, 50, 26, 0], [2, "1501", 836, 625, 50, 26, 0], [2, "1501", 785, 651, 50, 26, 0], [2, "1501", 708, 666, 50, 26, 0], [2, "1501", 734, 653, 50, 26, 0], [2, "1501", 683, 679, 50, 26, 0], [2, "1501", 753, 642, 50, 26, 0], [2, "1501", 779, 629, 50, 26, 0], [2, "1501", 728, 655, 50, 26, 0], [2, "1501", 861, 561, 50, 26, 0], [2, "1501", 887, 548, 50, 26, 0], [2, "1501", 836, 574, 50, 26, 0], [2, "1501", 895, 566, 50, 26, 0], [2, "1501", 921, 553, 50, 26, 0], [2, "1501", 870, 579, 50, 26, 0], [2, "1501", 906, 581, 50, 26, 0], [2, "1501", 932, 568, 50, 26, 0], [2, "1501", 881, 594, 50, 26, 0], [2, "1501", 931, 512, 50, 26, 0], [2, "1501", 957, 525, 50, 26, 0], [2, "1501", 983, 538, 50, 26, 0], [2, "1501", 1009, 551, 50, 26, 0], [2, "1501", 885, 550, 50, 26, 0], [2, "1501", 911, 537, 50, 26, 0], [2, "1501", 860, 563, 50, 26, 0], [2, "1501", 920, 532, 50, 26, 0], [2, "1501", 946, 519, 50, 26, 0], [2, "1501", 895, 545, 50, 26, 0], [2, "1501", 904, 538, 50, 26, 0], [2, "1501", 930, 525, 50, 26, 0], [2, "1501", 879, 551, 50, 26, 0], [2, "1501", 908, 551, 50, 26, 0], [2, "1501", 934, 538, 50, 26, 0], [2, "1501", 883, 564, 50, 26, 0], [2, "1501", 908, 573, 50, 26, 0], [2, "1501", 934, 560, 50, 26, 0], [2, "1501", 960, 547, 50, 26, 0], [2, "1501", 986, 560, 50, 26, 0], [2, "1501", 960, 573, 50, 26, 0], [2, "1501", 934, 586, 50, 26, 0], [2, "1501", 960, 599, 50, 26, 0], [2, "1501", 986, 586, 50, 26, 0], [2, "1501", 1012, 573, 50, 26, 0], [2, "1501", 899, 582, 50, 26, 0], [2, "1501", 925, 569, 50, 26, 0], [2, "1501", 951, 556, 50, 26, 0], [2, "1501", 977, 569, 50, 26, 0], [2, "1501", 951, 582, 50, 26, 0], [2, "1501", 925, 595, 50, 26, 0], [2, "1501", 951, 608, 50, 26, 0], [2, "1501", 977, 595, 50, 26, 0], [2, "1501", 1003, 582, 50, 26, 0], [2, "1501", 825, 618, 50, 26, 0], [2, "1501", 851, 605, 50, 26, 0], [2, "1501", 877, 592, 50, 26, 0], [2, "1501", 903, 605, 50, 26, 0], [2, "1501", 877, 618, 50, 26, 0], [2, "1501", 851, 631, 50, 26, 0], [2, "1501", 877, 644, 50, 26, 0], [2, "1501", 903, 631, 50, 26, 0], [2, "1501", 929, 618, 50, 26, 0], [2, "1501", 718, 700, 50, 26, 0], [2, "1501", 744, 687, 50, 26, 0], [2, "1501", 770, 674, 50, 26, 0], [2, "1501", 796, 687, 50, 26, 0], [2, "1501", 770, 700, 50, 26, 0], [2, "1501", 744, 713, 50, 26, 0], [2, "1501", 770, 726, 50, 26, 0], [2, "1501", 796, 713, 50, 26, 0], [2, "1501", 822, 700, 50, 26, 0], [2, "1501", 792, 664, 50, 26, 0], [2, "1501", 818, 651, 50, 26, 0], [2, "1501", 844, 638, 50, 26, 0], [2, "1501", 870, 651, 50, 26, 0], [2, "1501", 844, 664, 50, 26, 0], [2, "1501", 818, 677, 50, 26, 0], [2, "1501", 844, 690, 50, 26, 0], [2, "1501", 870, 677, 50, 26, 0], [2, "1501", 896, 664, 50, 26, 0], [2, "1501", 863, 623, 50, 26, 0], [2, "1501", 889, 610, 50, 26, 0], [2, "1501", 915, 597, 50, 26, 0], [2, "1501", 941, 610, 50, 26, 0], [2, "1501", 915, 623, 50, 26, 0], [2, "1501", 889, 636, 50, 26, 0], [2, "1501", 915, 649, 50, 26, 0], [2, "1501", 941, 636, 50, 26, 0], [2, "1501", 967, 623, 50, 26, 0], [2, "1501", 825, 669, 50, 26, 0], [2, "1501", 851, 656, 50, 26, 0], [2, "1501", 877, 643, 50, 26, 0], [2, "1501", 903, 656, 50, 26, 0], [2, "1501", 877, 669, 50, 26, 0], [2, "1501", 851, 682, 50, 26, 0], [2, "1501", 877, 695, 50, 26, 0], [2, "1501", 903, 682, 50, 26, 0], [2, "1501", 929, 669, 50, 26, 0], [2, "1501", 899, 633, 50, 26, 0], [2, "1501", 925, 620, 50, 26, 0], [2, "1501", 951, 607, 50, 26, 0], [2, "1501", 977, 620, 50, 26, 0], [2, "1501", 951, 633, 50, 26, 0], [2, "1501", 925, 646, 50, 26, 0], [2, "1501", 951, 659, 50, 26, 0], [2, "1501", 977, 646, 50, 26, 0], [2, "1501", 1003, 633, 50, 26, 0], [2, "1501", 970, 592, 50, 26, 0], [2, "1501", 996, 579, 50, 26, 0], [2, "1501", 1022, 566, 50, 26, 0], [2, "1501", 1048, 579, 50, 26, 0], [2, "1501", 1022, 592, 50, 26, 0], [2, "1501", 996, 605, 50, 26, 0], [2, "1501", 1022, 618, 50, 26, 0], [2, "1501", 1048, 605, 50, 26, 0], [2, "1501", 1074, 592, 50, 26, 0], [2, "1501", 850, 680, 50, 26, 0], [2, "1501", 876, 667, 50, 26, 0], [2, "1501", 902, 654, 50, 26, 0], [2, "1501", 928, 667, 50, 26, 0], [2, "1501", 902, 680, 50, 26, 0], [2, "1501", 876, 693, 50, 26, 0], [2, "1501", 902, 706, 50, 26, 0], [2, "1501", 928, 693, 50, 26, 0], [2, "1501", 954, 680, 50, 26, 0], [2, "1501", 899, 633, 50, 26, 0], [2, "1501", 925, 620, 50, 26, 0], [2, "1501", 951, 607, 50, 26, 0], [2, "1501", 977, 620, 50, 26, 0], [2, "1501", 951, 633, 50, 26, 0], [2, "1501", 925, 646, 50, 26, 0], [2, "1501", 951, 659, 50, 26, 0], [2, "1501", 977, 646, 50, 26, 0], [2, "1501", 1003, 633, 50, 26, 0], [2, "1501", 932, 660, 50, 26, 0], [2, "1501", 958, 647, 50, 26, 0], [2, "1501", 984, 634, 50, 26, 0], [2, "1501", 1010, 647, 50, 26, 0], [2, "1501", 984, 660, 50, 26, 0], [2, "1501", 958, 673, 50, 26, 0], [2, "1501", 984, 686, 50, 26, 0], [2, "1501", 1010, 673, 50, 26, 0], [2, "1501", 1036, 660, 50, 26, 0], [2, "1501", 573, 729, 50, 26, 0], [2, "1501", 599, 716, 50, 26, 0], [2, "1501", 625, 703, 50, 26, 0], [2, "1501", 651, 716, 50, 26, 0], [2, "1501", 625, 729, 50, 26, 0], [2, "1501", 599, 742, 50, 26, 0], [2, "1501", 625, 755, 50, 26, 0], [2, "1501", 651, 742, 50, 26, 0], [2, "1501", 677, 729, 50, 26, 0], [2, "1501", 644, 718, 50, 26, 0], [2, "1501", 670, 705, 50, 26, 0], [2, "1501", 696, 692, 50, 26, 0], [2, "1501", 722, 705, 50, 26, 0], [2, "1501", 696, 718, 50, 26, 0], [2, "1501", 670, 731, 50, 26, 0], [2, "1501", 696, 744, 50, 26, 0], [2, "1501", 722, 731, 50, 26, 0], [2, "1501", 748, 718, 50, 26, 0], [2, "1501", 796, 715, 50, 26, 0], [2, "1501", 822, 702, 50, 26, 0], [2, "1501", 848, 689, 50, 26, 0], [2, "1501", 874, 702, 50, 26, 0], [2, "1501", 848, 715, 50, 26, 0], [2, "1501", 822, 728, 50, 26, 0], [2, "1501", 848, 741, 50, 26, 0], [2, "1501", 874, 728, 50, 26, 0], [2, "1501", 900, 715, 50, 26, 0], [2, "1501", 528, 730, 50, 26, 0], [2, "1501", 554, 717, 50, 26, 0], [2, "1501", 503, 743, 50, 26, 0], [2, "1501", 702, 681, 50, 26, 0], [2, "1501", 728, 668, 50, 26, 0], [2, "1501", 754, 655, 50, 26, 0], [2, "1501", 780, 668, 50, 26, 0], [2, "1501", 754, 681, 50, 26, 0], [2, "1501", 728, 694, 50, 26, 0], [2, "1501", 754, 707, 50, 26, 0], [2, "1501", 780, 694, 50, 26, 0], [2, "1501", 806, 681, 50, 26, 0], [2, "1501", 825, 600, 50, 26, 0], [2, "1501", 851, 587, 50, 26, 0], [2, "1501", 877, 574, 50, 26, 0], [2, "1501", 903, 587, 50, 26, 0], [2, "1501", 877, 600, 50, 26, 0], [2, "1501", 851, 613, 50, 26, 0], [2, "1501", 877, 626, 50, 26, 0], [2, "1501", 903, 613, 50, 26, 0], [2, "1501", 929, 600, 50, 26, 0], [2, "1199", 933, 229, 38, 73, 2], [2, "894", 449, 58, 24, 20, 2], [2, "894", 429, 67, 24, 20, 2], [2, "894", 405, 80, 24, 20, 2], [2, "873", 963, 235, 64, 80, 0], [2, "873", 940, 245, 64, 80, 0], [2, "873", 921, 257, 64, 80, 0], [2, "873", 899, 267, 64, 80, 0], [2, "873", 963, 235, 64, 80, 0], [2, "873", 940, 245, 64, 80, 0], [2, "873", 899, 267, 64, 80, 0], [2, "873", 921, 257, 64, 80, 0], [2, "873", 876, 278, 64, 80, 0], [2, "873", 853, 288, 64, 80, 0], [2, "873", 812, 310, 64, 80, 0], [2, "873", 834, 300, 64, 80, 0], [2, "873", 791, 323, 64, 80, 0], [2, "873", 769, 333, 64, 80, 0], [2, "873", 758, 338, 64, 80, 0], [2, "873", 737, 348, 64, 80, 0], [2, "1403", 722, 359, 18, 22, 0], [2, "1403", 714, 373, 18, 22, 0], [2, "1225", 951, 215, 48, 44, 2], [2, "1225", 913, 236, 48, 44, 2], [2, "1225", 875, 258, 48, 44, 2], [2, "1225", 837, 279, 48, 44, 2], [2, "1226", 778, 301, 70, 47, 2], [2, "1226", 726, 323, 70, 47, 2], [2, "1196", 770, 275, 60, 74, 0], [2, "1196", 904, 207, 60, 74, 0], [2, "3984", -24, 696, 24, 24, 0], [2, "3984", -48, 696, 24, 24, 0], [2, "3984", -72, 696, 24, 24, 0], [2, "3984", -72, 672, 24, 24, 0], [2, "3984", -48, 672, 24, 24, 0], [2, "3984", -24, 672, 24, 24, 0], [2, "3984", -72, 624, 24, 24, 0], [2, "3984", -48, 624, 24, 24, 0], [2, "3984", -24, 624, 24, 24, 0], [2, "3984", -72, 648, 24, 24, 0], [2, "3984", -48, 648, 24, 24, 0], [2, "3984", -24, 648, 24, 24, 0], [2, "3984", -72, 528, 24, 24, 0], [2, "3984", -48, 528, 24, 24, 0], [2, "3984", -24, 528, 24, 24, 0], [2, "3984", -72, 552, 24, 24, 0], [2, "3984", -48, 552, 24, 24, 0], [2, "3984", -24, 552, 24, 24, 0], [2, "3984", -72, 576, 24, 24, 0], [2, "3984", -48, 576, 24, 24, 0], [2, "3984", -24, 576, 24, 24, 0], [2, "3984", -72, 600, 24, 24, 0], [2, "3984", -48, 600, 24, 24, 0], [2, "3984", -24, 600, 24, 24, 0], [2, "3984", -72, 432, 24, 24, 0], [2, "3984", -48, 432, 24, 24, 0], [2, "3984", -24, 432, 24, 24, 0], [2, "3984", -72, 456, 24, 24, 0], [2, "3984", -48, 456, 24, 24, 0], [2, "3984", -24, 456, 24, 24, 0], [2, "3984", -72, 480, 24, 24, 0], [2, "3984", -48, 480, 24, 24, 0], [2, "3984", -24, 480, 24, 24, 0], [2, "3984", -72, 504, 24, 24, 0], [2, "3984", -48, 504, 24, 24, 0], [2, "3984", -24, 504, 24, 24, 0], [2, "3984", -72, 336, 24, 24, 0], [2, "3984", -48, 336, 24, 24, 0], [2, "3984", -24, 336, 24, 24, 0], [2, "3984", -72, 360, 24, 24, 0], [2, "3984", -48, 360, 24, 24, 0], [2, "3984", -24, 360, 24, 24, 0], [2, "3984", -72, 384, 24, 24, 0], [2, "3984", -48, 384, 24, 24, 0], [2, "3984", -24, 384, 24, 24, 0], [2, "3984", -72, 408, 24, 24, 0], [2, "3984", -48, 408, 24, 24, 0], [2, "3984", -24, 408, 24, 24, 0], [2, "3984", -72, 48, 24, 24, 0], [2, "3984", -48, 48, 24, 24, 0], [2, "3984", -24, 48, 24, 24, 0], [2, "3984", -72, 72, 24, 24, 0], [2, "3984", -48, 72, 24, 24, 0], [2, "3984", -24, 72, 24, 24, 0], [2, "3984", -72, 96, 24, 24, 0], [2, "3984", -48, 96, 24, 24, 0], [2, "3984", -24, 96, 24, 24, 0], [2, "3984", -72, 120, 24, 24, 0], [2, "3984", -48, 120, 24, 24, 0], [2, "3984", -24, 120, 24, 24, 0], [2, "3984", -72, -24, 24, 24, 0], [2, "3984", -48, -24, 24, 24, 0], [2, "3984", -24, -24, 24, 24, 0], [2, "3984", -72, 0, 24, 24, 0], [2, "3984", -48, 0, 24, 24, 0], [2, "3984", -24, 0, 24, 24, 0], [2, "3984", -72, 24, 24, 24, 0], [2, "3984", -48, 24, 24, 24, 0], [2, "3984", -24, 24, 24, 24, 0], [2, "3984", -72, 48, 24, 24, 0], [2, "3984", -48, 48, 24, 24, 0], [2, "3984", -24, 48, 24, 24, 0], [2, "3984", 984, 672, 24, 24, 0], [2, "3984", 1008, 672, 24, 24, 0], [2, "3984", 1032, 672, 24, 24, 0], [2, "3984", 984, 696, 24, 24, 0], [2, "3984", 1008, 696, 24, 24, 0], [2, "3984", 1032, 696, 24, 24, 0], [2, "3984", 984, 624, 24, 24, 0], [2, "3984", 1008, 624, 24, 24, 0], [2, "3984", 1032, 624, 24, 24, 0], [2, "3984", 984, 648, 24, 24, 0], [2, "3984", 1008, 648, 24, 24, 0], [2, "3984", 1032, 648, 24, 24, 0], [2, "3984", 984, 576, 24, 24, 0], [2, "3984", 1008, 576, 24, 24, 0], [2, "3984", 1032, 576, 24, 24, 0], [2, "3984", 984, 600, 24, 24, 0], [2, "3984", 1008, 600, 24, 24, 0], [2, "3984", 1032, 600, 24, 24, 0], [2, "3984", 984, 432, 24, 24, 0], [2, "3984", 1008, 432, 24, 24, 0], [2, "3984", 1032, 432, 24, 24, 0], [2, "3984", 984, 456, 24, 24, 0], [2, "3984", 1008, 456, 24, 24, 0], [2, "3984", 1032, 456, 24, 24, 0], [2, "3984", 984, 336, 24, 24, 0], [2, "3984", 1008, 336, 24, 24, 0], [2, "3984", 1032, 336, 24, 24, 0], [2, "3984", 984, 360, 24, 24, 0], [2, "3984", 1008, 360, 24, 24, 0], [2, "3984", 1032, 360, 24, 24, 0], [2, "3984", 984, 384, 24, 24, 0], [2, "3984", 1008, 384, 24, 24, 0], [2, "3984", 1032, 384, 24, 24, 0], [2, "3984", 984, 408, 24, 24, 0], [2, "3984", 1008, 408, 24, 24, 0], [2, "3984", 1032, 408, 24, 24, 0], [2, "3984", 984, 288, 24, 24, 0], [2, "3984", 1008, 288, 24, 24, 0], [2, "3984", 1032, 288, 24, 24, 0], [2, "3984", 984, 312, 24, 24, 0], [2, "3984", 1008, 312, 24, 24, 0], [2, "3984", 1032, 312, 24, 24, 0], [2, "3984", 984, 432, 24, 24, 0], [2, "3984", 1008, 432, 24, 24, 0], [2, "3984", 1032, 432, 24, 24, 0], [2, "3984", 984, 456, 24, 24, 0], [2, "3984", 1008, 456, 24, 24, 0], [2, "3984", 1032, 456, 24, 24, 0], [2, "3984", 984, 384, 24, 24, 0], [2, "3984", 1008, 384, 24, 24, 0], [2, "3984", 1032, 384, 24, 24, 0], [2, "3984", 984, 408, 24, 24, 0], [2, "3984", 1008, 408, 24, 24, 0], [2, "3984", 1032, 408, 24, 24, 0], [2, "3984", 984, 336, 24, 24, 0], [2, "3984", 1008, 336, 24, 24, 0], [2, "3984", 1032, 336, 24, 24, 0], [2, "3984", 984, 360, 24, 24, 0], [2, "3984", 1008, 360, 24, 24, 0], [2, "3984", 1032, 360, 24, 24, 0], [2, "3984", 984, 288, 24, 24, 0], [2, "3984", 1008, 288, 24, 24, 0], [2, "3984", 1032, 288, 24, 24, 0], [2, "3984", 984, 312, 24, 24, 0], [2, "3984", 1008, 312, 24, 24, 0], [2, "3984", 1032, 312, 24, 24, 0], [2, "3984", 984, 240, 24, 24, 0], [2, "3984", 1008, 240, 24, 24, 0], [2, "3984", 1032, 240, 24, 24, 0], [2, "3984", 984, 264, 24, 24, 0], [2, "3984", 1008, 264, 24, 24, 0], [2, "3984", 1032, 264, 24, 24, 0], [2, "3984", 984, 192, 24, 24, 0], [2, "3984", 1008, 192, 24, 24, 0], [2, "3984", 1032, 192, 24, 24, 0], [2, "3984", 984, 216, 24, 24, 0], [2, "3984", 1008, 216, 24, 24, 0], [2, "3984", 1032, 216, 24, 24, 0], [2, "3984", 984, 96, 24, 24, 0], [2, "3984", 1008, 96, 24, 24, 0], [2, "3984", 1032, 96, 24, 24, 0], [2, "3984", 984, 120, 24, 24, 0], [2, "3984", 1008, 120, 24, 24, 0], [2, "3984", 1032, 120, 24, 24, 0], [2, "3984", 984, 144, 24, 24, 0], [2, "3984", 1008, 144, 24, 24, 0], [2, "3984", 1032, 144, 24, 24, 0], [2, "3984", 984, 168, 24, 24, 0], [2, "3984", 1008, 168, 24, 24, 0], [2, "3984", 1032, 168, 24, 24, 0], [2, "3984", 984, 48, 24, 24, 0], [2, "3984", 1008, 48, 24, 24, 0], [2, "3984", 1032, 48, 24, 24, 0], [2, "3984", 984, 72, 24, 24, 0], [2, "3984", 1008, 72, 24, 24, 0], [2, "3984", 1032, 72, 24, 24, 0], [2, "3984", 528, -72, 24, 24, 0], [2, "3984", 552, -72, 24, 24, 0], [2, "3984", 576, -72, 24, 24, 0], [2, "3984", 528, -48, 24, 24, 0], [2, "3984", 552, -48, 24, 24, 0], [2, "3984", 576, -48, 24, 24, 0], [2, "3984", 600, -72, 24, 24, 0], [2, "3984", 624, -72, 24, 24, 0], [2, "3984", 648, -72, 24, 24, 0], [2, "3984", 600, -48, 24, 24, 0], [2, "3984", 624, -48, 24, 24, 0], [2, "3984", 648, -48, 24, 24, 0], [2, "3978", -25, 274, 50, 26, 0], [2, "3978", 0, 261, 50, 26, 0], [2, "3978", 49, 235, 50, 26, 0], [2, "3978", 24, 248, 50, 26, 0], [2, "3978", 99, 209, 50, 26, 0], [2, "3978", 74, 222, 50, 26, 0], [2, "3978", 148, 184, 50, 26, 0], [2, "3978", 123, 197, 50, 26, 0], [2, "3978", 199, 158, 50, 26, 0], [2, "3978", 174, 171, 50, 26, 0], [2, "3978", 250, 132, 50, 26, 0], [2, "3978", 225, 145, 50, 26, 0], [2, "3978", 300, 106, 50, 26, 0], [2, "3978", 275, 119, 50, 26, 0], [2, "3978", 351, 81, 50, 26, 0], [2, "3978", 326, 94, 50, 26, 0], [2, "3978", 402, 55, 50, 26, 0], [2, "3978", 377, 68, 50, 26, 0], [2, "3978", 451, 32, 50, 26, 0], [2, "3978", 426, 44, 50, 26, 0], [2, "3978", 486, 16, 50, 26, 0], [2, "3978", 461, 29, 50, 26, 0], [2, "3978", -28, 172, 50, 26, 2], [2, "3978", -2, 185, 50, 26, 2], [2, "3978", 24, 198, 50, 26, 2], [2, "3978", 50, 211, 50, 26, 2], [2, "3984", -72, 240, 24, 24, 0], [2, "3984", -48, 240, 24, 24, 0], [2, "3984", -24, 240, 24, 24, 0], [2, "3984", -72, 264, 24, 24, 0], [2, "3984", -48, 264, 24, 24, 0], [2, "3984", -24, 264, 24, 24, 0], [2, "3984", -72, 288, 24, 24, 0], [2, "3984", -48, 288, 24, 24, 0], [2, "3984", -24, 288, 24, 24, 0], [2, "3984", -72, 312, 24, 24, 0], [2, "3984", -48, 312, 24, 24, 0], [2, "3984", -24, 312, 24, 24, 0], [2, "3984", -72, 144, 24, 24, 0], [2, "3984", -48, 144, 24, 24, 0], [2, "3984", -24, 144, 24, 24, 0], [2, "3984", -72, 168, 24, 24, 0], [2, "3984", -48, 168, 24, 24, 0], [2, "3984", -24, 168, 24, 24, 0], [2, "3984", -72, 192, 24, 24, 0], [2, "3984", -48, 192, 24, 24, 0], [2, "3984", -24, 192, 24, 24, 0], [2, "3984", -72, 216, 24, 24, 0], [2, "3984", -48, 216, 24, 24, 0], [2, "3984", -24, 216, 24, 24, 0], [2, "3978", 50, 211, 50, 26, 2], [2, "3978", 236, 164, 50, 26, 0], [2, "3978", 257, 175, 50, 26, 0], [2, "3978", 302, 198, 50, 26, 0], [2, "3978", 281, 187, 50, 26, 0], [2, "3978", 345, 220, 50, 26, 0], [2, "3978", 324, 209, 50, 26, 0], [2, "3978", 392, 243, 50, 26, 0], [2, "3978", 371, 232, 50, 26, 0], [2, "3978", 431, 263, 50, 26, 0], [2, "3978", 410, 252, 50, 26, 0], [2, "3978", 477, 287, 50, 26, 0], [2, "3978", 456, 276, 50, 26, 0], [2, "3978", 524, 311, 50, 26, 0], [2, "3978", 503, 300, 50, 26, 0], [2, "3978", 572, 334, 50, 26, 0], [2, "3978", 550, 323, 50, 26, 0], [2, "3978", 618, 357, 50, 26, 0], [2, "3978", 597, 346, 50, 26, 0], [2, "3978", 643, 369, 50, 26, 0], [2, "3978", 664, 380, 50, 26, 0], [2, "3978", 688, 391, 50, 26, 0], [2, "3978", 709, 402, 50, 26, 0], [2, "3978", 734, 413, 50, 26, 0], [2, "3978", 755, 424, 50, 26, 0], [2, "3978", 779, 435, 50, 26, 0], [2, "3978", 800, 446, 50, 26, 0], [2, "3978", 820, 456, 50, 26, 0], [2, "3978", 841, 467, 50, 26, 0], [2, "3978", 859, 476, 50, 26, 0], [2, "3978", 880, 487, 50, 26, 0], [2, "3978", 895, 494, 50, 26, 0], [2, "3978", 916, 505, 50, 26, 0], [2, "3978", 939, 516, 50, 26, 0], [2, "3978", 914, 529, 50, 26, 0], [2, "3978", 888, 542, 50, 26, 0], [2, "3978", 863, 555, 50, 26, 0], [2, "3978", 839, 568, 50, 26, 0], [2, "3978", 814, 581, 50, 26, 0], [2, "3978", 788, 593, 50, 26, 0], [2, "3978", 763, 606, 50, 26, 0], [2, "3978", 738, 619, 50, 26, 0], [2, "3978", 713, 632, 50, 26, 0], [2, "3978", 687, 645, 50, 26, 0], [2, "3978", 662, 658, 50, 26, 0], [2, "3978", 637, 671, 50, 26, 0], [2, "3978", 612, 684, 50, 26, 0], [2, "3978", 588, 696, 50, 26, 0], [2, "3978", 563, 709, 50, 26, 0], [2, "3984", 504, 720, 24, 24, 0], [2, "3984", 528, 720, 24, 24, 0], [2, "3984", 552, 720, 24, 24, 0], [2, "3984", 504, 744, 24, 24, 0], [2, "3984", 528, 744, 24, 24, 0], [2, "3984", 552, 744, 24, 24, 0], [2, "3984", 576, 720, 24, 24, 0], [2, "3984", 600, 720, 24, 24, 0], [2, "3984", 624, 720, 24, 24, 0], [2, "3984", 576, 744, 24, 24, 0], [2, "3984", 600, 744, 24, 24, 0], [2, "3984", 624, 744, 24, 24, 0], [2, "3984", 984, 528, 24, 24, 0], [2, "3984", 1008, 528, 24, 24, 0], [2, "3984", 1032, 528, 24, 24, 0], [2, "3984", 984, 552, 24, 24, 0], [2, "3984", 1008, 552, 24, 24, 0], [2, "3984", 1032, 552, 24, 24, 0], [2, "3984", 984, 480, 24, 24, 0], [2, "3984", 1008, 480, 24, 24, 0], [2, "3984", 1032, 480, 24, 24, 0], [2, "3984", 984, 504, 24, 24, 0], [2, "3984", 1008, 504, 24, 24, 0], [2, "3984", 1032, 504, 24, 24, 0], [2, "894", 506, 314, 24, 20, 0], [2, "894", 482, 302, 24, 20, 0], [2, "894", 458, 290, 24, 20, 0], [2, "894", 534, 328, 24, 20, 0], [2, "894", 510, 316, 24, 20, 0], [2, "894", 486, 304, 24, 20, 0]]}, {"type": 4, "obj": [[2, "3538", 260, 51, 38, 39, 0], [2, "3987", 473, 55, 42, 42, 0], [2, "879", 447, 77, 26, 56, 0], [2, "3987", 370, 107, 42, 42, 0], [2, "3576", 43, 151, 36, 36, 0], [2, "1231", 532, 25, 114, 162, 0], [2, "3568", 107, 179, 48, 38, 0], [4, 1, 656, 217, 1, 4022], [2, "1231", 703, 114, 114, 162, 0], [4, 2, 705, 336, 1, 4006], [2, "72", 107, 299, 42, 44, 2], [2, "879", 457, 291, 26, 56, 2], [2, "72", 136, 315, 42, 44, 2], [2, "72", 136, 315, 42, 44, 2], [2, "673", 329, 315, 80, 63, 2], [2, "3566", 387, 288, 38, 91, 0], [2, "41", 83, 386, 12, 11, 0], [2, "41", 97, 395, 12, 11, 0], [2, "72", 222, 365, 42, 44, 2], [2, "41", 56, 401, 12, 11, 0], [2, "673", 258, 351, 80, 63, 2], [2, "41", 67, 406, 12, 11, 0], [2, "3566", 268, 345, 38, 91, 0], [2, "3987", 849, 396, 42, 42, 0], [2, "3566", 509, 352, 38, 91, 0], [2, "3576", 119, 410, 36, 36, 0], [2, "3576", 83, 427, 36, 36, 0], [2, "3549", 685, 460, 26, 28, 0], [4, 4, 229, 527, 0, 4023], [2, "673", 2, 476, 80, 63, 2], [2, "3568", 399, 504, 48, 38, 0], [2, "3566", 51, 458, 38, 91, 0], [2, "3566", 18, 474, 38, 91, 0], [2, "3580", 434, 520, 76, 64, 0], [2, "3580", 330, 529, 76, 64, 2], [2, "3516", 399, 613, 32, 33, 0], [2, "3580", 551, 583, 76, 64, 0], [2, "3568", 617, 615, 48, 38, 0], [2, "3580", 217, 596, 76, 64, 2], [2, "3568", 176, 626, 48, 38, 0], [2, "3566", 687, 585, 38, 91, 0]]}, {"type": 3, "obj": [[2, "884", 258, -14, 24, 25, 2], [2, "884", 278, -24, 24, 25, 2], [2, "3609", 25, 390, 112, 59, 2], [2, "3609", 359, 625, 112, 59, 2], [2, "3609", 136, 77, 112, 59, 2], [2, "3082", 892, 429, 76, 40, 0], [2, "884", 433, 44, 24, 25, 0], [2, "884", 453, 54, 24, 25, 0], [2, "884", 409, 32, 24, 25, 0], [2, "884", 429, 42, 24, 25, 0], [2, "884", 528, 387, 24, 25, 0], [2, "884", 548, 397, 24, 25, 0], [2, "895", 348, 267, 8, 31, 0], [2, "895", 348, 259, 8, 31, 0], [2, "895", 348, 232, 8, 31, 0], [2, "895", 893, 559, 8, 31, 0], [2, "895", 901, 563, 8, 31, 0], [2, "894", 882, 575, 24, 20, 0], [2, "895", 160, -11, 8, 31, 2], [2, "895", 160, 14, 8, 31, 2], [2, "895", 957, 590, 8, 31, 0], [2, "895", 957, 563, 8, 31, 0], [2, "895", 957, 536, 8, 31, 0], [2, "895", 965, 594, 8, 31, 0], [2, "895", 965, 567, 8, 31, 0], [2, "895", 965, 540, 8, 31, 0], [2, "895", 973, 598, 8, 31, 0], [2, "895", 973, 571, 8, 31, 0], [2, "895", 973, 544, 8, 31, 0], [2, "895", 981, 602, 8, 31, 0], [2, "895", 981, 575, 8, 31, 0], [2, "895", 981, 548, 8, 31, 0], [2, "895", 989, 606, 8, 31, 0], [2, "895", 989, 579, 8, 31, 0], [2, "895", 989, 552, 8, 31, 0], [2, "895", 997, 610, 8, 31, 0], [2, "895", 997, 583, 8, 31, 0], [2, "895", 997, 556, 8, 31, 0], [2, "894", 957, 536, 24, 20, 0], [2, "894", 980, 548, 24, 20, 0], [2, "894", 956, 575, 24, 20, 0], [2, "894", 979, 587, 24, 20, 0], [2, "894", 956, 611, 24, 20, 0], [2, "894", 978, 622, 24, 20, 0], [2, "895", 261, 227, 8, 31, 0], [2, "895", 261, 213, 8, 31, 0], [2, "895", 261, 186, 8, 31, 0], [2, "884", 236, 241, 24, 25, 0], [2, "884", 256, 251, 24, 25, 0], [2, "22", 468, 236, 62, 38, 0], [2, "21", 442, 219, 28, 24, 0], [2, "21", 529, 265, 28, 24, 0], [2, "3554", 379, 105, 20, 31, 2], [2, "895", 53, 80, 8, 31, 2], [2, "895", 53, 67, 8, 31, 2], [2, "895", 53, 40, 8, 31, 2], [2, "1207", 964, 344, 22, 81, 0], [2, "1207", 964, 306, 22, 81, 0], [2, "1207", 846, 392, 22, 81, 0], [2, "1207", 846, 354, 22, 81, 0], [2, "1197", 943, 321, 54, 44, 2], [2, "1197", 938, 320, 54, 44, 2], [2, "1197", 889, 344, 54, 44, 2], [2, "1197", 839, 369, 54, 44, 2], [2, "1197", 790, 393, 54, 44, 2], [2, "884", 276, 261, 24, 25, 0], [2, "884", 296, 271, 24, 25, 0], [2, "884", 317, 281, 24, 25, 0], [2, "884", 337, 291, 24, 25, 0], [2, "884", 357, 301, 24, 25, 0], [2, "884", 377, 311, 24, 25, 0], [2, "884", 435, 341, 24, 25, 0], [2, "884", 455, 351, 24, 25, 0], [2, "884", 395, 321, 24, 25, 0], [2, "884", 415, 331, 24, 25, 0], [2, "884", 476, 361, 24, 25, 0], [2, "884", 496, 371, 24, 25, 0], [2, "884", 516, 381, 24, 25, 0], [2, "884", 536, 391, 24, 25, 0], [2, "884", 198, 253, 24, 25, 2], [2, "884", 218, 243, 24, 25, 2], [2, "884", 158, 273, 24, 25, 2], [2, "884", 178, 263, 24, 25, 2], [2, "884", 118, 293, 24, 25, 2], [2, "884", 138, 283, 24, 25, 2], [2, "884", 78, 313, 24, 25, 2], [2, "884", 98, 303, 24, 25, 2], [2, "884", 38, 333, 24, 25, 2], [2, "884", 58, 323, 24, 25, 2], [2, "884", -2, 353, 24, 25, 2], [2, "884", 18, 343, 24, 25, 2], [2, "884", -22, 363, 24, 25, 2], [2, "884", 700, 473, 24, 25, 0], [2, "884", 720, 483, 24, 25, 0], [2, "884", 740, 493, 24, 25, 0], [2, "884", 760, 503, 24, 25, 0], [2, "884", 781, 514, 24, 25, 0], [2, "884", 801, 524, 24, 25, 0], [2, "884", 771, 509, 24, 25, 0], [2, "884", 791, 519, 24, 25, 0], [2, "884", 852, 548, 24, 25, 0], [2, "884", 872, 558, 24, 25, 0], [2, "884", 650, 448, 24, 25, 0], [2, "884", 670, 458, 24, 25, 0], [2, "884", 690, 468, 24, 25, 0], [2, "884", 710, 478, 24, 25, 0], [2, "895", 230, 207, 8, 31, 2], [2, "895", 230, 217, 8, 31, 2], [2, "895", 230, 180, 8, 31, 2], [2, "895", 138, 262, 8, 31, 2], [2, "895", 138, 252, 8, 31, 2], [2, "895", 138, 225, 8, 31, 2], [2, "895", 43, 309, 8, 31, 2], [2, "895", 43, 299, 8, 31, 2], [2, "895", 43, 272, 8, 31, 2], [2, "895", 453, 312, 8, 31, 0], [2, "895", 453, 285, 8, 31, 0], [2, "895", 453, 320, 8, 31, 0], [2, "895", 753, 463, 8, 31, 0], [2, "895", 753, 436, 8, 31, 0], [2, "895", 753, 471, 8, 31, 0], [2, "884", 811, 530, 24, 25, 0], [2, "884", 831, 540, 24, 25, 0], [2, "895", 398, 118, 8, 31, 2], [2, "895", 398, 108, 8, 31, 2], [2, "895", 398, 81, 8, 31, 2], [2, "895", 471, 79, 8, 31, 2], [2, "895", 471, 69, 8, 31, 2], [2, "895", 471, 42, 8, 31, 2], [2, "1207", 529, 47, 22, 81, 0], [2, "1207", 529, 28, 22, 81, 0], [2, "895", 320, 155, 8, 31, 2], [2, "895", 320, 145, 8, 31, 2], [2, "895", 320, 118, 8, 31, 2], [2, "895", 575, 48, 8, 31, 0], [2, "895", 575, 72, 8, 31, 0], [2, "895", 887, 228, 8, 31, 0], [2, "895", 887, 204, 8, 31, 0], [2, "895", 789, 178, 8, 31, 0], [2, "895", 789, 154, 8, 31, 0], [2, "895", 674, 121, 8, 31, 0], [2, "895", 674, 97, 8, 31, 0], [2, "894", 213, 177, 24, 20, 2], [2, "894", 189, 190, 24, 20, 2], [2, "894", 165, 202, 24, 20, 2], [2, "894", 141, 215, 24, 20, 2], [2, "884", 246, -7, 24, 25, 2], [2, "884", 266, -17, 24, 25, 2], [2, "884", 206, 13, 24, 25, 2], [2, "884", 226, 3, 24, 25, 2], [2, "884", 166, 33, 24, 25, 2], [2, "884", 186, 23, 24, 25, 2], [2, "884", 86, 73, 24, 25, 2], [2, "884", 106, 63, 24, 25, 2], [2, "884", 46, 93, 24, 25, 2], [2, "884", 66, 83, 24, 25, 2], [2, "884", 6, 113, 24, 25, 2], [2, "884", 26, 103, 24, 25, 2], [2, "884", -14, 123, 24, 25, 2], [2, "884", 126, 53, 24, 25, 2], [2, "884", 146, 43, 24, 25, 2], [2, "884", 557, 402, 24, 25, 0], [2, "884", 577, 412, 24, 25, 0], [2, "884", 597, 423, 24, 25, 0], [2, "884", 617, 433, 24, 25, 0], [2, "884", 629, 437, 24, 25, 0], [2, "884", 649, 447, 24, 25, 0], [2, "895", 542, 357, 8, 31, 0], [2, "895", 542, 330, 8, 31, 0], [2, "895", 542, 365, 8, 31, 0], [2, "895", 652, 411, 8, 31, 0], [2, "895", 652, 384, 8, 31, 0], [2, "895", 652, 419, 8, 31, 0], [2, "895", 871, 520, 8, 31, 0], [2, "895", 871, 493, 8, 31, 0], [2, "895", 871, 528, 8, 31, 0], [2, "3554", 857, 394, 20, 31, 2], [2, "3554", 973, 343, 20, 31, 2], [2, "1235", 542, 72, 12, 18, 2], [2, "1236", 515, 89, 42, 43, 0], [2, "1235", 531, 58, 12, 18, 2], [2, "1236", 865, 245, 42, 43, 0], [2, "894", 115, 227, 24, 20, 2], [2, "894", 91, 240, 24, 20, 2], [2, "894", 67, 252, 24, 20, 2], [2, "894", 43, 265, 24, 20, 2], [2, "894", 19, 277, 24, 20, 2], [2, "894", -5, 290, 24, 20, 2], [2, "894", 68, 17, 24, 20, 2], [2, "894", 44, 29, 24, 20, 2], [2, "894", 20, 41, 24, 20, 2], [2, "894", -4, 53, 24, 20, 2], [2, "894", 163, -31, 24, 20, 2], [2, "894", 139, -19, 24, 20, 2], [2, "894", 115, -7, 24, 20, 2], [2, "894", 91, 5, 24, 20, 2], [2, "894", 441, -18, 24, 20, 0], [2, "894", 465, -6, 24, 20, 0], [2, "894", 489, 6, 24, 20, 0], [2, "894", 418, -30, 24, 20, 0], [2, "894", 394, -42, 24, 20, 0], [2, "894", 370, -54, 24, 20, 0], [2, "894", 286, 203, 24, 20, 0], [2, "894", 262, 191, 24, 20, 0], [2, "894", 238, 179, 24, 20, 0], [2, "894", 359, 240, 24, 20, 0], [2, "894", 335, 228, 24, 20, 0], [2, "894", 311, 216, 24, 20, 0], [2, "894", 429, 275, 24, 20, 0], [2, "894", 405, 263, 24, 20, 0], [2, "894", 381, 251, 24, 20, 0], [2, "894", 597, 359, 24, 20, 0], [2, "894", 573, 347, 24, 20, 0], [2, "894", 549, 335, 24, 20, 0], [2, "894", 669, 395, 24, 20, 0], [2, "894", 645, 383, 24, 20, 0], [2, "894", 621, 371, 24, 20, 0], [2, "894", 741, 430, 24, 20, 0], [2, "894", 717, 418, 24, 20, 0], [2, "894", 693, 406, 24, 20, 0], [2, "894", 813, 466, 24, 20, 0], [2, "894", 789, 454, 24, 20, 0], [2, "894", 765, 442, 24, 20, 0], [2, "894", 954, 537, 24, 20, 0], [2, "894", 930, 525, 24, 20, 0], [2, "894", 906, 513, 24, 20, 0], [2, "894", 882, 501, 24, 20, 0], [2, "894", 858, 489, 24, 20, 0], [2, "894", 834, 477, 24, 20, 0], [2, "43_1", 422, 345, 82, 58, 0], [2, "43_1", 466, 367, 82, 58, 0], [2, "1398", 618, 44, 28, 66, 0], [2, "1398", 714, 91, 28, 66, 0], [2, "1398", 832, 150, 28, 66, 0], [2, "3127", 373, 169, 30, 30, 0], [2, "1457", 507, 104, 22, 30, 0], [2, "1456", 487, 109, 24, 32, 0], [2, "1456", 858, 260, 24, 32, 0], [2, "1457", 773, 250, 22, 30, 0], [2, "1457", 658, 160, 22, 30, 0], [2, "1457", 640, 151, 22, 30, 0], [2, "1456", 695, 180, 24, 32, 2], [2, "1457", 332, 187, 22, 30, 0], [2, "1457", 655, 336, 22, 30, 0], [2, "1457", 647, 352, 22, 30, 0], [2, "1457", 666, 353, 22, 30, 0], [2, "3127", 682, 350, 30, 30, 0], [2, "1456", 698, 366, 24, 32, 0], [2, "48", 204, 255, 52, 38, 0], [2, "48", 219, 264, 52, 38, 0], [2, "3745", 149, 267, 78, 60, 2], [2, "3518", 168, 222, 42, 37, 2], [2, "3560", 424, 287, 28, 31, 0], [2, "3560", 543, 344, 28, 31, 0], [2, "3603", 679, 408, 50, 54, 0], [2, "884", 294, -25, 24, 25, 0], [2, "884", 314, -15, 24, 25, 0], [2, "884", 334, -5, 24, 25, 0], [2, "884", 354, 5, 24, 25, 0], [2, "884", 375, 15, 24, 25, 0], [2, "884", 395, 25, 24, 25, 0], [2, "3535", 4, 104, 44, 61, 2], [2, "117", 188, 649, 22, 27, 0], [2, "117", 412, 527, 22, 27, 0], [2, "117", 628, 637, 22, 27, 0], [2, "3566", 851, 503, 38, 91, 0], [2, "3560", 29, 55, 28, 31, 2], [2, "891", 891, 381, 54, 75, 0], [2, "890", 874, 369, 94, 105, 0], [2, "3587", 499, 565, 64, 50, 0], [2, "3587", 281, 576, 64, 50, 2], [2, "3589", 372, 616, 90, 57, 2], [2, "3479", 727, 459, 62, 83, 0], [2, "3479", 615, 403, 62, 83, 0], [2, "3515", 170, 259, 34, 34, 0], [2, "3491", 282, 210, 34, 77, 0], [2, "3491", 575, 357, 34, 77, 0], [2, "3491", 809, 475, 34, 77, 0], [2, "3491", 204, -43, 34, 77, 2], [2, "3587", 665, 469, 64, 50, 0], [2, "3600", 58, 276, 28, 46, 2], [2, "3598", 10, 293, 30, 56, 2], [2, "3127", 349, 179, 30, 30, 0], [2, "942", 597, 15, 68, 61, 0], [2, "942", 698, 65, 68, 61, 0], [2, "942", 814, 125, 68, 61, 0], [2, "3506", 368, 2, 60, 68, 0], [2, "3505", 315, 266, 52, 63, 0], [2, "3508", 432, 6, 36, 40, 0], [2, "3580", 273, -18, 76, 64, 0], [2, "3611", 341, 55, 52, 30, 0], [2, "3587", 166, 19, 64, 50, 2], [2, "3595", 118, 37, 94, 82, 2], [2, "3587", 57, 77, 64, 50, 2], [2, "3609", 394, 378, 112, 59, 0], [2, "3557", 186, -3, 22, 38, 0], [2, "3557", 78, 60, 22, 38, 0], [2, "3604", 116, 8, 26, 26, 2], [2, "3576", 50, 364, 36, 36, 0], [2, "3576", 14, 381, 36, 36, 0], [2, "3589", 39, 383, 90, 57, 2], [2, "3523", 222, 247, 32, 32, 0], [2, "41", 503, 242, 12, 11, 0], [2, "3560", 107, 249, 28, 31, 2], [2, "3508", 348, 257, 36, 40, 0], [2, "1398", 338, 105, 28, 66, 2], [2, "1398", 497, 25, 28, 66, 2], [2, "3554", 482, 53, 20, 31, 2], [2, "41", 484, 239, 12, 11, 2], [2, "895", 674, 69, 8, 31, 0], [2, "895", 674, 45, 8, 31, 0], [2, "895", 674, 69, 8, 31, 0], [2, "895", 674, 45, 8, 31, 0], [2, "895", 674, 41, 8, 31, 0], [2, "895", 674, 17, 8, 31, 0], [2, "895", 674, 41, 8, 31, 0], [2, "895", 674, 17, 8, 31, 0], [2, "895", 575, 21, 8, 31, 0], [2, "895", 575, -3, 8, 31, 0], [2, "895", 575, -8, 8, 31, 0], [2, "895", 575, -32, 8, 31, 0], [2, "895", 790, 99, 8, 31, 0], [2, "895", 790, 75, 8, 31, 0], [2, "895", 790, 127, 8, 31, 0], [2, "895", 790, 103, 8, 31, 0], [2, "895", 887, 177, 8, 31, 0], [2, "895", 887, 153, 8, 31, 0], [2, "895", 887, 147, 8, 31, 0], [2, "895", 887, 123, 8, 31, 0], [2, "894", 255, -77, 24, 20, 2], [2, "894", 231, -65, 24, 20, 2], [2, "894", 207, -53, 24, 20, 2], [2, "894", 183, -41, 24, 20, 2], [2, "894", 277, -88, 24, 20, 2], [2, "894", 253, -76, 24, 20, 2], [2, "894", 229, -64, 24, 20, 2], [2, "894", 205, -52, 24, 20, 2], [2, "894", 349, -64, 24, 20, 0], [2, "894", 325, -76, 24, 20, 0], [2, "894", 301, -88, 24, 20, 0], [2, "895", 160, -14, 8, 31, 2], [2, "895", 359, -22, 8, 31, 0], [2, "895", 359, -46, 8, 31, 0], [2, "895", 359, -51, 8, 31, 0], [2, "895", 293, -52, 8, 31, 2], [2, "895", 293, -80, 8, 31, 2], [2, "3987", 965, 343, 42, 42, 0]]}, {"type": 3, "obj": [[2, "617", 254, 194, 22, 43, 2], [2, "617", 244, 199, 22, 43, 2], [2, "617", 244, 168, 22, 43, 2], [2, "617", 254, 163, 22, 43, 2], [2, "617", 254, 146, 22, 43, 2], [2, "617", 244, 151, 22, 43, 2], [2, "617", 941, 280, 22, 43, 0], [2, "617", 931, 275, 22, 43, 0], [2, "617", 931, 244, 22, 43, 0], [2, "617", 941, 249, 22, 43, 0], [2, "617", 941, 232, 22, 43, 0], [2, "617", 931, 227, 22, 43, 0], [2, "617", 974, 296, 22, 43, 0], [2, "617", 964, 291, 22, 43, 0], [2, "617", 964, 260, 22, 43, 0], [2, "617", 974, 265, 22, 43, 0], [2, "617", 974, 248, 22, 43, 0], [2, "617", 964, 243, 22, 43, 0], [2, "617", 775, 464, 22, 43, 2], [2, "617", 765, 469, 22, 43, 2], [2, "617", 765, 438, 22, 43, 2], [2, "617", 775, 433, 22, 43, 2], [2, "617", 775, 416, 22, 43, 2], [2, "617", 765, 421, 22, 43, 2], [2, "617", 968, 368, 22, 43, 2], [2, "617", 958, 373, 22, 43, 2], [2, "617", 958, 342, 22, 43, 2], [2, "617", 968, 337, 22, 43, 2], [2, "617", 968, 320, 22, 43, 2], [2, "617", 958, 325, 22, 43, 2], [2, "617", 935, 386, 22, 43, 2], [2, "617", 925, 391, 22, 43, 2], [2, "617", 925, 360, 22, 43, 2], [2, "617", 935, 355, 22, 43, 2], [2, "617", 935, 338, 22, 43, 2], [2, "617", 925, 343, 22, 43, 2], [2, "617", 903, 401, 22, 43, 2], [2, "617", 893, 406, 22, 43, 2], [2, "617", 893, 375, 22, 43, 2], [2, "617", 903, 370, 22, 43, 2], [2, "617", 903, 353, 22, 43, 2], [2, "617", 893, 358, 22, 43, 2], [2, "617", 870, 417, 22, 43, 2], [2, "617", 860, 422, 22, 43, 2], [2, "617", 860, 391, 22, 43, 2], [2, "617", 870, 386, 22, 43, 2], [2, "617", 870, 369, 22, 43, 2], [2, "617", 860, 374, 22, 43, 2], [2, "617", 838, 432, 22, 43, 2], [2, "617", 828, 437, 22, 43, 2], [2, "617", 828, 406, 22, 43, 2], [2, "617", 838, 401, 22, 43, 2], [2, "617", 838, 384, 22, 43, 2], [2, "617", 828, 389, 22, 43, 2], [2, "617", 805, 449, 22, 43, 2], [2, "617", 795, 454, 22, 43, 2], [2, "617", 795, 423, 22, 43, 2], [2, "617", 805, 418, 22, 43, 2], [2, "617", 805, 401, 22, 43, 2], [2, "617", 795, 406, 22, 43, 2], [2, "617", 286, 189, 22, 43, 2], [2, "617", 276, 194, 22, 43, 2], [2, "617", 276, 163, 22, 43, 2], [2, "617", 286, 158, 22, 43, 2], [2, "617", 286, 141, 22, 43, 2], [2, "617", 276, 146, 22, 43, 2], [2, "617", 275, 187, 22, 43, 2], [2, "617", 265, 192, 22, 43, 2], [2, "617", 265, 161, 22, 43, 2], [2, "617", 275, 156, 22, 43, 2], [2, "617", 275, 139, 22, 43, 2], [2, "617", 265, 144, 22, 43, 2], [2, "3401", 491, 28, 44, 81, 0], [2, "3401", 491, 7, 44, 81, 0], [2, "3401", 478, 21, 44, 81, 0], [2, "3401", 478, 0, 44, 81, 0], [2, "3401", -4, 43, 44, 81, 2], [2, "1501", 880, 692, 50, 26, 0], [2, "1501", 906, 679, 50, 26, 0], [2, "1501", 932, 666, 50, 26, 0], [2, "1501", 958, 679, 50, 26, 0], [2, "1501", 932, 692, 50, 26, 0], [2, "1501", 906, 705, 50, 26, 0], [2, "1501", 932, 718, 50, 26, 0], [2, "1501", 958, 705, 50, 26, 0], [2, "1501", 984, 692, 50, 26, 0], [2, "3401", -4, 64, 44, 81, 2], [2, "3401", 302, -88, 44, 81, 0], [2, "3401", 302, -67, 44, 81, 0], [2, "3401", 40, 42, 44, 81, 2], [2, "3401", 40, 21, 44, 81, 2], [2, "3401", 82, 21, 44, 81, 2], [2, "3401", 82, 0, 44, 81, 2], [2, "3401", 126, -1, 44, 81, 2], [2, "3401", 126, -22, 44, 81, 2], [2, "3401", 213, -45, 44, 81, 2], [2, "3401", 213, -66, 44, 81, 2], [2, "3401", 169, -23, 44, 81, 2], [2, "3401", 169, -44, 44, 81, 2], [2, "3401", 257, -67, 44, 81, 2], [2, "3401", 257, -88, 44, 81, 2], [2, "3401", 346, -45, 44, 81, 0], [2, "3401", 346, -66, 44, 81, 0], [2, "3401", 390, -23, 44, 81, 0], [2, "3401", 390, -44, 44, 81, 0], [2, "3401", 434, -1, 44, 81, 0], [2, "3401", 434, -22, 44, 81, 0], [2, "617", 513, 78, 22, 43, 2], [2, "617", 503, 83, 22, 43, 2], [2, "617", 503, 52, 22, 43, 2], [2, "617", 513, 47, 22, 43, 2], [2, "617", 513, 30, 22, 43, 2], [2, "617", 503, 35, 22, 43, 2], [2, "617", 546, 83, 22, 43, 0], [2, "617", 536, 78, 22, 43, 0], [2, "617", 536, 47, 22, 43, 0], [2, "617", 546, 52, 22, 43, 0], [2, "617", 546, 35, 22, 43, 0], [2, "617", 536, 30, 22, 43, 0], [2, "617", 579, 99, 22, 43, 0], [2, "617", 569, 94, 22, 43, 0], [2, "617", 569, 63, 22, 43, 0], [2, "617", 579, 68, 22, 43, 0], [2, "617", 579, 51, 22, 43, 0], [2, "617", 569, 46, 22, 43, 0], [2, "617", 481, 93, 22, 43, 2], [2, "617", 471, 98, 22, 43, 2], [2, "617", 471, 67, 22, 43, 2], [2, "617", 481, 62, 22, 43, 2], [2, "617", 481, 45, 22, 43, 2], [2, "617", 471, 50, 22, 43, 2], [2, "617", 383, 141, 22, 43, 2], [2, "617", 373, 146, 22, 43, 2], [2, "617", 373, 115, 22, 43, 2], [2, "617", 383, 110, 22, 43, 2], [2, "617", 383, 93, 22, 43, 2], [2, "617", 373, 98, 22, 43, 2], [2, "617", 351, 157, 22, 43, 2], [2, "617", 341, 162, 22, 43, 2], [2, "617", 341, 131, 22, 43, 2], [2, "617", 351, 126, 22, 43, 2], [2, "617", 351, 109, 22, 43, 2], [2, "617", 341, 114, 22, 43, 2], [2, "617", 318, 173, 22, 43, 2], [2, "617", 308, 178, 22, 43, 2], [2, "617", 308, 147, 22, 43, 2], [2, "617", 318, 142, 22, 43, 2], [2, "617", 318, 125, 22, 43, 2], [2, "617", 308, 130, 22, 43, 2], [2, "617", 612, 116, 22, 43, 0], [2, "617", 602, 111, 22, 43, 0], [2, "617", 602, 80, 22, 43, 0], [2, "617", 612, 85, 22, 43, 0], [2, "617", 612, 68, 22, 43, 0], [2, "617", 602, 63, 22, 43, 0], [2, "617", 645, 132, 22, 43, 0], [2, "617", 635, 127, 22, 43, 0], [2, "617", 635, 96, 22, 43, 0], [2, "617", 645, 101, 22, 43, 0], [2, "617", 645, 84, 22, 43, 0], [2, "617", 635, 79, 22, 43, 0], [2, "617", 677, 148, 22, 43, 0], [2, "617", 667, 143, 22, 43, 0], [2, "617", 667, 112, 22, 43, 0], [2, "617", 677, 117, 22, 43, 0], [2, "617", 677, 100, 22, 43, 0], [2, "617", 667, 95, 22, 43, 0], [2, "617", 710, 164, 22, 43, 0], [2, "617", 700, 159, 22, 43, 0], [2, "617", 700, 128, 22, 43, 0], [2, "617", 710, 133, 22, 43, 0], [2, "617", 710, 116, 22, 43, 0], [2, "617", 700, 111, 22, 43, 0], [2, "617", 743, 181, 22, 43, 0], [2, "617", 733, 176, 22, 43, 0], [2, "617", 733, 145, 22, 43, 0], [2, "617", 743, 150, 22, 43, 0], [2, "617", 743, 133, 22, 43, 0], [2, "617", 733, 128, 22, 43, 0], [2, "617", 776, 197, 22, 43, 0], [2, "617", 766, 192, 22, 43, 0], [2, "617", 766, 161, 22, 43, 0], [2, "617", 776, 166, 22, 43, 0], [2, "617", 776, 149, 22, 43, 0], [2, "617", 766, 144, 22, 43, 0], [2, "617", 809, 214, 22, 43, 0], [2, "617", 799, 209, 22, 43, 0], [2, "617", 799, 178, 22, 43, 0], [2, "617", 809, 183, 22, 43, 0], [2, "617", 809, 166, 22, 43, 0], [2, "617", 799, 161, 22, 43, 0], [2, "617", 842, 230, 22, 43, 0], [2, "617", 832, 225, 22, 43, 0], [2, "617", 832, 194, 22, 43, 0], [2, "617", 842, 199, 22, 43, 0], [2, "617", 842, 182, 22, 43, 0], [2, "617", 832, 177, 22, 43, 0], [2, "617", 875, 247, 22, 43, 0], [2, "617", 865, 242, 22, 43, 0], [2, "617", 865, 211, 22, 43, 0], [2, "617", 875, 216, 22, 43, 0], [2, "617", 875, 199, 22, 43, 0], [2, "617", 865, 194, 22, 43, 0], [2, "617", 908, 263, 22, 43, 0], [2, "617", 898, 258, 22, 43, 0], [2, "617", 898, 227, 22, 43, 0], [2, "617", 908, 232, 22, 43, 0], [2, "617", 908, 215, 22, 43, 0], [2, "617", 898, 210, 22, 43, 0], [2, "1208", 530, 80, 52, 56, 0], [2, "1208", 574, 102, 52, 56, 0], [2, "1208", 622, 125, 52, 56, 0], [2, "1208", 666, 147, 52, 56, 0], [2, "1208", 710, 169, 52, 56, 0], [2, "1208", 754, 191, 52, 56, 0], [2, "1208", 800, 215, 52, 56, 0], [2, "1208", 844, 237, 52, 56, 0], [2, "1208", 890, 259, 52, 56, 0], [2, "1208", 491, 80, 52, 56, 2], [2, "1208", 471, 88, 52, 56, 2], [2, "1208", 358, 146, 52, 56, 2], [2, "1208", 319, 165, 52, 56, 2], [2, "1208", 290, 178, 52, 56, 2], [2, "1208", 952, 366, 52, 56, 2], [2, "1208", 952, 368, 52, 56, 2], [2, "1208", 910, 389, 52, 56, 2], [2, "1208", 874, 406, 52, 56, 2], [2, "1208", 832, 427, 52, 56, 2], [2, "3401", 193, 199, 44, 81, 2], [2, "3401", 193, 178, 44, 81, 2], [2, "3401", 149, 222, 44, 81, 2], [2, "3401", 149, 201, 44, 81, 2], [2, "3401", 105, 243, 44, 81, 2], [2, "3401", 105, 222, 44, 81, 2], [2, "3401", 61, 266, 44, 81, 2], [2, "3401", 61, 245, 44, 81, 2], [2, "3401", 16, 289, 44, 81, 2], [2, "3401", 16, 268, 44, 81, 2], [2, "3401", -28, 312, 44, 81, 2], [2, "3401", -28, 291, 44, 81, 2], [2, "3401", 238, 198, 44, 81, 0], [2, "3401", 238, 177, 44, 81, 0], [2, "3401", 283, 221, 44, 81, 0], [2, "3401", 283, 200, 44, 81, 0], [2, "3401", 372, 266, 44, 81, 0], [2, "3401", 372, 245, 44, 81, 0], [2, "3401", 327, 243, 44, 81, 0], [2, "3401", 327, 222, 44, 81, 0], [2, "3401", 415, 287, 44, 81, 0], [2, "3401", 415, 266, 44, 81, 0], [2, "3401", 542, 347, 44, 81, 0], [2, "3401", 542, 326, 44, 81, 0], [2, "3401", 646, 404, 44, 81, 0], [2, "3401", 646, 383, 44, 81, 0], [2, "3401", 689, 425, 44, 81, 0], [2, "3401", 689, 404, 44, 81, 0], [2, "3401", 734, 448, 44, 81, 0], [2, "3401", 734, 427, 44, 81, 0], [2, "3401", 778, 470, 44, 81, 0], [2, "3401", 778, 449, 44, 81, 0], [2, "3401", 823, 492, 44, 81, 0], [2, "3401", 823, 471, 44, 81, 0], [2, "3401", 866, 513, 44, 81, 0], [2, "3401", 866, 492, 44, 81, 0], [2, "3401", 911, 536, 44, 81, 0], [2, "3401", 911, 515, 44, 81, 0], [2, "3401", 955, 558, 44, 81, 0], [2, "3401", 955, 537, 44, 81, 0], [2, "3401", 573, 367, 44, 81, 0], [2, "3401", 573, 346, 44, 81, 0], [2, "3401", 616, 388, 44, 81, 0], [2, "3401", 616, 367, 44, 81, 0], [2, "43_1", -53, 607, 82, 58, 2], [2, "43_1", -22, 636, 82, 58, 2], [2, "617", 580, 19, 22, 43, 0], [2, "617", 570, 14, 22, 43, 0], [2, "617", 570, -17, 22, 43, 0], [2, "617", 580, -12, 22, 43, 0], [2, "617", 580, -29, 22, 43, 0], [2, "617", 570, -34, 22, 43, 0], [2, "617", 547, 3, 22, 43, 0], [2, "617", 537, -2, 22, 43, 0], [2, "617", 537, -33, 22, 43, 0], [2, "617", 547, -28, 22, 43, 0], [2, "617", 547, -45, 22, 43, 0], [2, "617", 537, -50, 22, 43, 0], [2, "617", 643, 50, 22, 43, 0], [2, "617", 633, 45, 22, 43, 0], [2, "617", 633, 14, 22, 43, 0], [2, "617", 643, 19, 22, 43, 0], [2, "617", 643, 2, 22, 43, 0], [2, "617", 633, -3, 22, 43, 0], [2, "617", 610, 34, 22, 43, 0], [2, "617", 600, 29, 22, 43, 0], [2, "617", 600, -2, 22, 43, 0], [2, "617", 610, 3, 22, 43, 0], [2, "617", 610, -14, 22, 43, 0], [2, "617", 600, -19, 22, 43, 0], [2, "617", 709, 83, 22, 43, 0], [2, "617", 699, 78, 22, 43, 0], [2, "617", 699, 47, 22, 43, 0], [2, "617", 709, 52, 22, 43, 0], [2, "617", 709, 35, 22, 43, 0], [2, "617", 699, 30, 22, 43, 0], [2, "617", 676, 67, 22, 43, 0], [2, "617", 666, 62, 22, 43, 0], [2, "617", 666, 31, 22, 43, 0], [2, "617", 676, 36, 22, 43, 0], [2, "617", 676, 19, 22, 43, 0], [2, "617", 666, 14, 22, 43, 0], [2, "617", 772, 114, 22, 43, 0], [2, "617", 762, 109, 22, 43, 0], [2, "617", 762, 78, 22, 43, 0], [2, "617", 772, 83, 22, 43, 0], [2, "617", 772, 66, 22, 43, 0], [2, "617", 762, 61, 22, 43, 0], [2, "617", 739, 98, 22, 43, 0], [2, "617", 729, 93, 22, 43, 0], [2, "617", 729, 62, 22, 43, 0], [2, "617", 739, 67, 22, 43, 0], [2, "617", 739, 50, 22, 43, 0], [2, "617", 729, 45, 22, 43, 0], [2, "617", 838, 148, 22, 43, 0], [2, "617", 828, 143, 22, 43, 0], [2, "617", 828, 112, 22, 43, 0], [2, "617", 838, 117, 22, 43, 0], [2, "617", 838, 100, 22, 43, 0], [2, "617", 828, 95, 22, 43, 0], [2, "617", 805, 132, 22, 43, 0], [2, "617", 795, 127, 22, 43, 0], [2, "617", 795, 96, 22, 43, 0], [2, "617", 805, 101, 22, 43, 0], [2, "617", 805, 84, 22, 43, 0], [2, "617", 795, 79, 22, 43, 0], [2, "617", 898, 178, 22, 43, 0], [2, "617", 888, 173, 22, 43, 0], [2, "617", 888, 142, 22, 43, 0], [2, "617", 898, 147, 22, 43, 0], [2, "617", 898, 130, 22, 43, 0], [2, "617", 888, 125, 22, 43, 0], [2, "617", 865, 161, 22, 43, 0], [2, "617", 855, 156, 22, 43, 0], [2, "617", 855, 125, 22, 43, 0], [2, "617", 865, 130, 22, 43, 0], [2, "617", 865, 113, 22, 43, 0], [2, "617", 855, 108, 22, 43, 0], [2, "617", 930, 193, 22, 43, 0], [2, "617", 920, 188, 22, 43, 0], [2, "617", 920, 157, 22, 43, 0], [2, "617", 930, 162, 22, 43, 0], [2, "617", 930, 145, 22, 43, 0], [2, "617", 920, 140, 22, 43, 0], [2, "617", 984, 217, 22, 43, 0], [2, "617", 974, 212, 22, 43, 0], [2, "617", 974, 181, 22, 43, 0], [2, "617", 984, 186, 22, 43, 0], [2, "617", 984, 169, 22, 43, 0], [2, "617", 974, 164, 22, 43, 0], [2, "617", 952, 202, 22, 43, 0], [2, "617", 942, 197, 22, 43, 0], [2, "617", 942, 166, 22, 43, 0], [2, "617", 952, 171, 22, 43, 0], [2, "617", 952, 154, 22, 43, 0], [2, "617", 942, 149, 22, 43, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 30, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 24, 25, 26, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 39, 24, 25, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 22, 22, 24, 25, 26, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, 24, 25, 30, 29, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 24, 25, 26, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, 26, -1, -1, -1, -1, 41, 40, 39, 22, 22, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, 41, 40, 39, 22, 22, 24, 25, 30, 29, -1, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, 41, 40, 39, 22, 22, 24, 25, 26, -1, -1, 31, 30, 29, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 24, 25, 26, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 24, 24, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, 22, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 35, 36, -1, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 10, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 24, 25, 26, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 10, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 24, 25, 26, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, -1, -1, 15, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, 31, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 26, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "3025", 440, 159, 92, 53, 2], [2, "3617", -61, 151, 74, 37, 0], [2, "3617", -24, 133, 74, 37, 0], [2, "3617", 14, 152, 74, 37, 0], [2, "3617", -23, 170, 74, 37, 0], [2, "3617", 85, 79, 74, 37, 0], [2, "3617", 122, 61, 74, 37, 0], [2, "3617", 160, 80, 74, 37, 0], [2, "3617", 123, 98, 74, 37, 0], [2, "3617", 12, 115, 74, 37, 0], [2, "3617", 49, 97, 74, 37, 0], [2, "3617", 87, 116, 74, 37, 0], [2, "3617", 50, 134, 74, 37, 0], [2, "1501", -81, 15, 50, 26, 0], [2, "1501", -55, 2, 50, 26, 0], [2, "1501", -29, -11, 50, 26, 0], [2, "1501", -3, 2, 50, 26, 0], [2, "1501", -29, 15, 50, 26, 0], [2, "1501", -55, 28, 50, 26, 0], [2, "1501", -29, 41, 50, 26, 0], [2, "1501", -3, 28, 50, 26, 0], [2, "1501", 23, 15, 50, 26, 0], [2, "1501", -15, -15, 50, 26, 0], [2, "1501", 11, -28, 50, 26, 0], [2, "1501", 37, -41, 50, 26, 0], [2, "1501", 63, -28, 50, 26, 0], [2, "1501", 37, -15, 50, 26, 0], [2, "1501", 11, -2, 50, 26, 0], [2, "1501", 37, 11, 50, 26, 0], [2, "1501", 63, -2, 50, 26, 0], [2, "1501", 89, -15, 50, 26, 0], [2, "1501", 472, -12, 50, 26, 0], [2, "1501", 498, -25, 50, 26, 0], [2, "1501", 524, -38, 50, 26, 0], [2, "1501", 550, -25, 50, 26, 0], [2, "1501", 524, -12, 50, 26, 0], [2, "1501", 498, 1, 50, 26, 0], [2, "1501", 524, 14, 50, 26, 0], [2, "1501", 550, 1, 50, 26, 0], [2, "1501", 576, -12, 50, 26, 0], [2, "1501", 548, 23, 50, 26, 0], [2, "1501", 574, 10, 50, 26, 0], [2, "1501", 600, -3, 50, 26, 0], [2, "1501", 626, 10, 50, 26, 0], [2, "1501", 600, 23, 50, 26, 0], [2, "1501", 574, 36, 50, 26, 0], [2, "1501", 600, 49, 50, 26, 0], [2, "1501", 626, 36, 50, 26, 0], [2, "1501", 652, 23, 50, 26, 0], [2, "1501", 619, 61, 50, 26, 0], [2, "1501", 645, 48, 50, 26, 0], [2, "1501", 671, 35, 50, 26, 0], [2, "1501", 697, 48, 50, 26, 0], [2, "1501", 671, 61, 50, 26, 0], [2, "1501", 645, 74, 50, 26, 0], [2, "1501", 671, 87, 50, 26, 0], [2, "1501", 697, 74, 50, 26, 0], [2, "1501", 723, 61, 50, 26, 0], [2, "1501", 693, 97, 50, 26, 0], [2, "1501", 719, 84, 50, 26, 0], [2, "1501", 745, 71, 50, 26, 0], [2, "1501", 771, 84, 50, 26, 0], [2, "1501", 745, 97, 50, 26, 0], [2, "1501", 719, 110, 50, 26, 0], [2, "1501", 745, 123, 50, 26, 0], [2, "1501", 771, 110, 50, 26, 0], [2, "1501", 797, 97, 50, 26, 0], [2, "1501", 767, 137, 50, 26, 0], [2, "1501", 793, 124, 50, 26, 0], [2, "1501", 819, 111, 50, 26, 0], [2, "1501", 845, 124, 50, 26, 0], [2, "1501", 819, 137, 50, 26, 0], [2, "1501", 793, 150, 50, 26, 0], [2, "1501", 819, 163, 50, 26, 0], [2, "1501", 845, 150, 50, 26, 0], [2, "1501", 871, 137, 50, 26, 0], [2, "1501", 847, 174, 50, 26, 0], [2, "1501", 873, 161, 50, 26, 0], [2, "1501", 899, 148, 50, 26, 0], [2, "1501", 925, 161, 50, 26, 0], [2, "1501", 899, 174, 50, 26, 0], [2, "1501", 873, 187, 50, 26, 0], [2, "1501", 899, 200, 50, 26, 0], [2, "1501", 925, 187, 50, 26, 0], [2, "1501", 951, 174, 50, 26, 0], [2, "1501", 919, 214, 50, 26, 0], [2, "1501", 945, 201, 50, 26, 0], [2, "1501", 971, 188, 50, 26, 0], [2, "1501", 997, 201, 50, 26, 0], [2, "1501", 971, 214, 50, 26, 0], [2, "1501", 945, 227, 50, 26, 0], [2, "1501", 971, 240, 50, 26, 0], [2, "1501", 997, 227, 50, 26, 0], [2, "1501", 1023, 214, 50, 26, 0], [2, "1501", 592, -30, 50, 26, 0], [2, "1501", 618, -43, 50, 26, 0], [2, "1501", 644, -56, 50, 26, 0], [2, "1501", 670, -43, 50, 26, 0], [2, "1501", 644, -30, 50, 26, 0], [2, "1501", 618, -17, 50, 26, 0], [2, "1501", 644, -4, 50, 26, 0], [2, "1501", 670, -17, 50, 26, 0], [2, "1501", 696, -30, 50, 26, 0], [2, "1501", 663, 8, 50, 26, 0], [2, "1501", 689, -5, 50, 26, 0], [2, "1501", 715, -18, 50, 26, 0], [2, "1501", 741, -5, 50, 26, 0], [2, "1501", 715, 8, 50, 26, 0], [2, "1501", 689, 21, 50, 26, 0], [2, "1501", 715, 34, 50, 26, 0], [2, "1501", 741, 21, 50, 26, 0], [2, "1501", 767, 8, 50, 26, 0], [2, "1501", 737, 44, 50, 26, 0], [2, "1501", 763, 31, 50, 26, 0], [2, "1501", 789, 18, 50, 26, 0], [2, "1501", 815, 31, 50, 26, 0], [2, "1501", 789, 44, 50, 26, 0], [2, "1501", 763, 57, 50, 26, 0], [2, "1501", 789, 70, 50, 26, 0], [2, "1501", 815, 57, 50, 26, 0], [2, "1501", 841, 44, 50, 26, 0], [2, "1501", 811, 84, 50, 26, 0], [2, "1501", 837, 71, 50, 26, 0], [2, "1501", 863, 58, 50, 26, 0], [2, "1501", 889, 71, 50, 26, 0], [2, "1501", 863, 84, 50, 26, 0], [2, "1501", 837, 97, 50, 26, 0], [2, "1501", 863, 110, 50, 26, 0], [2, "1501", 889, 97, 50, 26, 0], [2, "1501", 915, 84, 50, 26, 0], [2, "1501", 891, 121, 50, 26, 0], [2, "1501", 917, 108, 50, 26, 0], [2, "1501", 943, 95, 50, 26, 0], [2, "1501", 969, 108, 50, 26, 0], [2, "1501", 943, 121, 50, 26, 0], [2, "1501", 917, 134, 50, 26, 0], [2, "1501", 943, 147, 50, 26, 0], [2, "1501", 969, 134, 50, 26, 0], [2, "1501", 995, 121, 50, 26, 0], [2, "1501", 963, 161, 50, 26, 0], [2, "1501", 989, 148, 50, 26, 0], [2, "1501", 1015, 135, 50, 26, 0], [2, "1501", 1041, 148, 50, 26, 0], [2, "1501", 1015, 161, 50, 26, 0], [2, "1501", 989, 174, 50, 26, 0], [2, "1501", 1015, 187, 50, 26, 0], [2, "1501", 1041, 174, 50, 26, 0], [2, "1501", 1067, 161, 50, 26, 0], [2, "1501", 799, 2, 50, 26, 0], [2, "1501", 825, -11, 50, 26, 0], [2, "1501", 851, -24, 50, 26, 0], [2, "1501", 877, -11, 50, 26, 0], [2, "1501", 851, 2, 50, 26, 0], [2, "1501", 825, 15, 50, 26, 0], [2, "1501", 851, 28, 50, 26, 0], [2, "1501", 877, 15, 50, 26, 0], [2, "1501", 903, 2, 50, 26, 0], [2, "1501", 873, 42, 50, 26, 0], [2, "1501", 899, 29, 50, 26, 0], [2, "1501", 925, 16, 50, 26, 0], [2, "1501", 951, 29, 50, 26, 0], [2, "1501", 925, 42, 50, 26, 0], [2, "1501", 899, 55, 50, 26, 0], [2, "1501", 925, 68, 50, 26, 0], [2, "1501", 951, 55, 50, 26, 0], [2, "1501", 977, 42, 50, 26, 0], [2, "1501", 953, 79, 50, 26, 0], [2, "1501", 979, 66, 50, 26, 0], [2, "1501", 1005, 53, 50, 26, 0], [2, "1501", 1031, 66, 50, 26, 0], [2, "1501", 1005, 79, 50, 26, 0], [2, "1501", 979, 92, 50, 26, 0], [2, "1501", 1005, 105, 50, 26, 0], [2, "1501", 1031, 92, 50, 26, 0], [2, "1501", 1057, 79, 50, 26, 0], [2, "1501", 725, -34, 50, 26, 0], [2, "1501", 751, -47, 50, 26, 0], [2, "1501", 777, -60, 50, 26, 0], [2, "1501", 803, -47, 50, 26, 0], [2, "1501", 777, -34, 50, 26, 0], [2, "1501", 751, -21, 50, 26, 0], [2, "1501", 777, -8, 50, 26, 0], [2, "1501", 803, -21, 50, 26, 0], [2, "1501", 829, -34, 50, 26, 0], [2, "1501", 884, 1, 50, 26, 0], [2, "1501", 910, -12, 50, 26, 0], [2, "1501", 936, -25, 50, 26, 0], [2, "1501", 962, -12, 50, 26, 0], [2, "1501", 936, 1, 50, 26, 0], [2, "1501", 910, 14, 50, 26, 0], [2, "1501", 936, 27, 50, 26, 0], [2, "1501", 962, 14, 50, 26, 0], [2, "1501", 988, 1, 50, 26, 0], [2, "163", 427, 110, 60, 33, 0], [2, "163", 380, 133, 60, 33, 0], [2, "163", 440, 117, 60, 33, 0], [2, "163", 393, 140, 60, 33, 0], [2, "163", 453, 339, 60, 33, 2], [2, "163", 503, 363, 60, 33, 2], [2, "43_1", 398, 121, 82, 58, 2], [2, "3617", 270, -11, 74, 37, 0], [2, "3617", 233, 7, 74, 37, 0], [2, "3617", 308, 8, 74, 37, 0], [2, "3617", 271, 26, 74, 37, 0], [2, "3617", 160, 43, 74, 37, 0], [2, "3617", 197, 25, 74, 37, 0], [2, "3617", 235, 44, 74, 37, 0], [2, "3617", 198, 62, 74, 37, 0], [2, "3617", 308, 44, 74, 37, 0], [2, "3617", 345, 26, 74, 37, 0], [2, "3617", 383, 45, 74, 37, 0], [2, "3617", 346, 63, 74, 37, 0], [2, "3617", 235, 80, 74, 37, 0], [2, "3617", 272, 62, 74, 37, 0], [2, "3617", 310, 81, 74, 37, 0], [2, "3617", 273, 99, 74, 37, 0], [2, "3617", 87, 152, 74, 37, 0], [2, "3617", 124, 134, 74, 37, 0], [2, "3617", 162, 153, 74, 37, 0], [2, "3617", 125, 171, 74, 37, 0], [2, "3617", 161, 117, 74, 37, 0], [2, "3617", 198, 99, 74, 37, 0], [2, "3617", 236, 118, 74, 37, 0], [2, "3617", 199, 136, 74, 37, 0], [2, "3617", 369, 74, 74, 37, 0], [2, "3617", 406, 56, 74, 37, 0], [2, "3617", 444, 75, 74, 37, 0], [2, "3617", 407, 93, 74, 37, 0], [2, "3617", 296, 110, 74, 37, 0], [2, "3617", 333, 92, 74, 37, 0], [2, "3617", 371, 111, 74, 37, 0], [2, "3617", 334, 129, 74, 37, 0], [2, "3612", 217, 254, 56, 30, 0], [2, "3612", 245, 268, 56, 30, 0], [2, "3612", 187, 269, 56, 30, 0], [2, "3612", 215, 283, 56, 30, 0], [2, "3612", 158, 284, 56, 30, 0], [2, "3612", 186, 298, 56, 30, 0], [2, "3612", 128, 299, 56, 30, 0], [2, "3612", 156, 313, 56, 30, 0], [2, "3612", 99, 313, 56, 30, 0], [2, "3612", 127, 327, 56, 30, 0], [2, "3612", 69, 328, 56, 30, 0], [2, "3612", 97, 342, 56, 30, 0], [2, "3612", 273, 282, 56, 30, 0], [2, "3612", 301, 296, 56, 30, 0], [2, "3612", 243, 297, 56, 30, 0], [2, "3612", 271, 311, 56, 30, 0], [2, "3612", 214, 312, 56, 30, 0], [2, "3612", 242, 326, 56, 30, 0], [2, "3612", 184, 327, 56, 30, 0], [2, "3612", 212, 341, 56, 30, 0], [2, "3612", 155, 341, 56, 30, 0], [2, "3612", 183, 355, 56, 30, 0], [2, "3612", 125, 356, 56, 30, 0], [2, "3612", 153, 370, 56, 30, 0], [2, "3612", 40, 343, 56, 30, 0], [2, "3612", 68, 357, 56, 30, 0], [2, "3612", 10, 358, 56, 30, 0], [2, "3612", 38, 372, 56, 30, 0], [2, "3612", 96, 371, 56, 30, 0], [2, "3612", 124, 385, 56, 30, 0], [2, "3612", 66, 386, 56, 30, 0], [2, "3612", 94, 400, 56, 30, 0], [2, "3612", -18, 372, 56, 30, 0], [2, "3612", 10, 386, 56, 30, 0], [2, "3612", -48, 387, 56, 30, 0], [2, "3612", -20, 401, 56, 30, 0], [2, "3612", 38, 400, 56, 30, 0], [2, "3612", 66, 414, 56, 30, 0], [2, "3612", 8, 415, 56, 30, 0], [2, "3612", 36, 429, 56, 30, 0], [2, "3612", -20, 429, 56, 30, 0], [2, "3612", 8, 443, 56, 30, 0], [2, "3612", -50, 444, 56, 30, 0], [2, "3612", -22, 458, 56, 30, 0], [2, "3612", 329, 309, 56, 30, 0], [2, "3612", 357, 323, 56, 30, 0], [2, "3612", 299, 324, 56, 30, 0], [2, "3612", 327, 338, 56, 30, 0], [2, "3612", 270, 339, 56, 30, 0], [2, "3612", 298, 353, 56, 30, 0], [2, "3612", 240, 354, 56, 30, 0], [2, "3612", 268, 368, 56, 30, 0], [2, "3612", 211, 368, 56, 30, 0], [2, "3612", 239, 382, 56, 30, 0], [2, "3612", 181, 383, 56, 30, 0], [2, "3612", 209, 397, 56, 30, 0], [2, "3612", 152, 398, 56, 30, 0], [2, "3612", 180, 412, 56, 30, 0], [2, "3612", 122, 413, 56, 30, 0], [2, "3612", 150, 427, 56, 30, 0], [2, "3612", 94, 427, 56, 30, 0], [2, "3612", 122, 441, 56, 30, 0], [2, "3612", 64, 442, 56, 30, 0], [2, "3612", 92, 456, 56, 30, 0], [2, "3612", 36, 456, 56, 30, 0], [2, "3612", 64, 470, 56, 30, 0], [2, "3612", 6, 471, 56, 30, 0], [2, "3612", 34, 485, 56, 30, 0], [2, "3612", -50, 473, 56, 30, 0], [2, "3612", -22, 487, 56, 30, 0], [2, "3612", 6, 501, 56, 30, 0], [2, "3612", -52, 502, 56, 30, 0], [2, "3612", -24, 516, 56, 30, 0], [2, "688", 358, 346, 46, 24, 0], [2, "688", 320, 365, 46, 24, 0], [2, "688", 284, 384, 46, 24, 0], [2, "688", 246, 403, 46, 24, 0], [2, "688", 208, 422, 46, 24, 0], [2, "688", 170, 441, 46, 24, 0], [2, "688", 132, 460, 46, 24, 0], [2, "688", 94, 479, 46, 24, 0], [2, "688", 56, 498, 46, 24, 0], [2, "688", 18, 517, 46, 24, 0], [2, "688", -10, 533, 46, 24, 0], [2, "3617", 14, 188, 74, 37, 0], [2, "3617", 51, 170, 74, 37, 0], [2, "3617", 89, 189, 74, 37, 0], [2, "3617", 52, 207, 74, 37, 0], [2, "3025", 498, 314, 92, 53, 0], [2, "3612", -48, 415, 56, 30, 0], [2, "1501", 557, -55, 50, 26, 0], [2, "1501", 583, -68, 50, 26, 0], [2, "1501", 609, -81, 50, 26, 0], [2, "1501", 635, -68, 50, 26, 0], [2, "1501", 609, -55, 50, 26, 0], [2, "1501", 583, -42, 50, 26, 0], [2, "1501", 609, -29, 50, 26, 0], [2, "1501", 635, -42, 50, 26, 0], [2, "1501", 661, -55, 50, 26, 0]]}, {"type": 2, "data": [48, 48, 48, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, 50, 51, 52, 73, 72, 48, 48, 48, 48, 48, 48, 69, 70, 67, 51, 52, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 54, 55, 68, 48, 48, 48, 48, 70, 66, 67, 53, 54, 55, 51, 52, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 68, 48, 48, 48, 48, 74, 54, 55, 56, 50, 51, 52, 50, 51, 52, 48, 48, 48, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 64, 48, 48, 48, 48, 49, 59, 50, 51, 52, 54, 55, 50, 51, 52, 51, 52, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 48, 48, 48, 48, 48, 48, 62, 53, 54, 55, 57, 58, 53, 54, 55, 50, 51, 61, 60, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, -1, -1, -1, 76, 75, 48, 48, 48, 48, 48, 48, 49, 45, 46, 58, 51, 52, 50, 51, 52, 61, 60, 64, 63, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, -1, 54, 54, 73, 72, 76, 75, 48, 48, 48, 48, 48, 48, 49, 46, 54, 55, 53, 61, 60, 64, 63, 69, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 45, 46, 54, 50, 73, 76, 75, 48, 48, 48, 48, 48, 48, 49, 45, 45, 64, 64, 63, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 49, 45, 46, 54, 73, 66, 66, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, -1, -1, 48, 48, 48, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 49, 45, 46, 54, 55, 73, 72, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 75, 48, 48, 48, 48, 49, 45, 46, 51, 50, 73, 72, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 69, 74, 50, 50, 51, 55, 73, 76, 75, 48, 48, 48, 48, 48, 48, 48, 48, 69, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 69, 70, 66, 67, 50, 53, 54, 51, 52, 73, 72, 76, 75, 48, 48, 48, 48, 75, 70, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 79, 48, 48, 69, 70, 66, 67, 50, 50, 61, 60, 60, 45, 46, 50, 50, 73, 72, 72, 48, 48, 70, 72, 71, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 80, 79, -1, 67, 50, 50, 48, 50, 64, 63, 48, 48, 49, 46, 57, 53, 54, 55, 48, 48, 62, 53, 56, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 79, 79, 80, 79, 80, 79, -1, 50, 50, 61, 60, 69, 70, 75, 75, 75, 48, 49, 48, 56, 57, 58, 48, 48, 49, 46, 57, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 79, 79, 80, 79, 80, 79, 80, 79, 80, 79, -1, 64, 80, 66, 67, 54, 55, 57, 75, 63, 48, 48, 48, 48, 48, 48, 48, 49, 45, 77, -1, -1, -1, -1, -1, -1, -1, -1, -1, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 56, 61, 60, 48, -1, 48, 48, 48, 48, 48, 48, 48, 77, 77, 77, 77, -1, -1, -1, -1, -1, -1, -1, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, 63, 48, 48, 48, -1, 48, 48, 48, 77, 78, 77, 78, 77, 77, -1, -1, -1, -1, -1, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, -1, -1, -1, -1, -1, 77, 77, 77, 78, 77, 77, 77, -1, -1, -1, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, -1, -1, -1, -1, 77, 77, 78, 77, 77, 77, -1, -1, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, -1, -1, 77, 77, 77, 77, 78, 77, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 80, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, 77, 77, 78, 77, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 80, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, -1, -1, -1, -1, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 80, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, -1, -1, -1, 48, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, 80, -1, -1, 48, -1, 48, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, -1, -1, 48, -1, -1, 48, 48, 48, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 48, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, -1, -1, -1, 48, 48, -1, 48, 48, 48, 48, 48, 48, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, 79, 80, -1, 80, 48, 48, 48, -1, 48, 48, 48, 48, 48, 48, -1, -1, 48]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}