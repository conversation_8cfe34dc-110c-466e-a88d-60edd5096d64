{"mW": 864, "mH": 720, "tW": 24, "tH": 24, "tiles": [["3327", 0, 2, 2], ["3347", 0, 1, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1]], "layers": [{"type": 3, "obj": [[2, "3330", 566, 43, 60, 43, 0], [2, "3330", 520, 68, 60, 43, 0], [2, "3330", 577, 76, 60, 43, 0], [2, "3330", 558, 47, 60, 43, 0], [2, "3330", 675, 95, 60, 43, 0], [2, "3330", 627, 117, 60, 43, 0], [2, "3329", 565, 8, 50, 59, 0], [2, "3331", 680, 56, 54, 59, 0], [2, "3329", 633, 80, 50, 59, 0], [2, "3344", 558, 105, 14, 15, 0], [2, "3345", 500, 83, 14, 15, 0], [2, "3342", 646, 150, 14, 15, 0], [2, "3342", 661, 147, 14, 15, 0], [2, "3343", 536, 101, 14, 15, 0], [2, "3342", 571, 106, 14, 15, 0], [2, "3342", 734, 111, 14, 15, 0], [2, "3344", 632, 67, 14, 15, 0], [2, "3333", 584, 95, 32, 33, 0], [2, "3330", 513, 691, 60, 43, 0], [2, "3330", 560, 705, 60, 43, 0], [2, "3330", 618, 699, 60, 43, 0], [2, "3330", 552, 677, 60, 43, 0], [2, "3330", 595, 679, 60, 43, 0], [2, "3330", 675, 701, 60, 43, 0], [2, "3330", 715, 713, 60, 43, 0], [2, "3330", 230, 674, 60, 43, 0], [2, "3330", 267, 697, 60, 43, 0], [2, "3330", 212, 699, 60, 43, 0], [2, "3330", 157, 684, 60, 43, 0], [2, "3330", 102, 695, 60, 43, 0], [2, "3330", -25, 649, 60, 43, 0], [2, "3330", -34, 679, 60, 43, 0], [2, "3330", 17, 668, 60, 43, 0], [2, "3330", 45, 690, 60, 43, 0], [2, "3330", 5, 702, 60, 43, 0], [2, "3330", 5, 669, 60, 43, 0], [2, "3329", 4, 624, 50, 59, 0], [2, "3329", 81, 655, 50, 59, 0], [2, "3329", 242, 658, 50, 59, 0], [2, "3329", 352, 690, 50, 59, 0], [2, "3329", 583, 639, 50, 59, 0], [2, "3329", 696, 677, 50, 59, 0], [2, "3329", 449, 705, 50, 59, 0], [2, "3340", 470, 637, 60, 123, 0]]}, {"type": 4, "obj": [[2, "3328", 86, 13, 76, 54, 0], [2, "3337", 574, 41, 38, 31, 0], [2, "3337", 556, 50, 38, 31, 0], [2, "3337", 593, 50, 38, 31, 0], [2, "3337", 538, 59, 38, 31, 0], [2, "3337", 575, 59, 38, 31, 0], [2, "3337", 614, 59, 38, 31, 0], [2, "3337", 519, 68, 38, 31, 0], [2, "3337", 557, 68, 38, 31, 0], [2, "3337", 596, 68, 38, 31, 0], [2, "3337", 633, 68, 38, 31, 0], [2, "3332", 105, 71, 48, 33, 0], [2, "3337", 500, 77, 38, 31, 0], [2, "3337", 538, 77, 38, 31, 0], [2, "3337", 578, 77, 38, 31, 0], [2, "3337", 615, 77, 38, 31, 0], [2, "3337", 652, 77, 38, 31, 0], [2, "3328", 152, 57, 76, 54, 0], [2, "3341", 477, 27, 44, 86, 0], [2, "3337", 481, 86, 38, 31, 0], [2, "3337", 519, 86, 38, 31, 0], [2, "3337", 559, 86, 38, 31, 0], [2, "3337", 597, 86, 38, 31, 0], [2, "3337", 634, 86, 38, 31, 0], [2, "3337", 671, 86, 38, 31, 0], [2, "3337", 689, 94, 38, 31, 0], [2, "3337", 500, 95, 38, 31, 0], [2, "3337", 540, 95, 38, 31, 0], [2, "3337", 578, 95, 38, 31, 0], [2, "3337", 616, 95, 38, 31, 0], [2, "3337", 653, 95, 38, 31, 0], [2, "3340", 58, 9, 60, 123, 2], [2, "3337", 671, 103, 38, 31, 0], [2, "3337", 708, 103, 38, 31, 0], [2, "3337", 521, 104, 38, 31, 0], [2, "3337", 559, 104, 38, 31, 0], [2, "3337", 597, 104, 38, 31, 0], [2, "3337", 635, 104, 38, 31, 0], [2, "3337", 653, 112, 38, 31, 0], [2, "3337", 690, 112, 38, 31, 0], [2, "3337", 727, 112, 38, 31, 0], [2, "3337", 540, 113, 38, 31, 0], [2, "3337", 578, 113, 38, 31, 0], [2, "3337", 616, 113, 38, 31, 0], [2, "3337", 634, 121, 38, 31, 0], [2, "3337", 672, 121, 38, 31, 0], [2, "3337", 709, 121, 38, 31, 0], [2, "3337", 559, 122, 38, 31, 0], [2, "3337", 597, 122, 38, 31, 0], [2, "3340", 425, 37, 60, 123, 0], [2, "3337", 615, 130, 38, 31, 0], [2, "3337", 653, 130, 38, 31, 0], [2, "3337", 691, 130, 38, 31, 0], [2, "3337", 578, 131, 38, 31, 0], [2, "3341", 757, 81, 44, 86, 0], [2, "3337", 596, 139, 38, 31, 0], [2, "3337", 634, 139, 38, 31, 0], [2, "3337", 672, 139, 38, 31, 0], [2, "3337", 615, 148, 38, 31, 0], [2, "3337", 653, 148, 38, 31, 0], [2, "3341", 315, 95, 44, 86, 2], [2, "3337", 634, 157, 38, 31, 0], [4, 2, 207, 255, 0, 4043], [4, 5, 347, 279, 0, 4043], [2, "3331", 543, 284, 54, 59, 0], [2, "3341", 581, 262, 44, 86, 2], [4, 1, 296, 349, 0, 4043], [2, "3341", 369, 312, 44, 86, 0], [2, "3340", 771, 291, 60, 123, 2], [2, "3329", 76, 369, 50, 59, 0], [2, "3328", 754, 385, 76, 54, 0], [2, "3336", 735, 411, 48, 45, 0], [2, "3340", 102, 336, 60, 123, 2], [2, "3328", 783, 418, 76, 54, 0], [2, "3335", 756, 432, 48, 45, 0], [2, "3335", 67, 493, 48, 45, 0], [4, 4, 574, 540, 0, 4043], [4, 3, 242, 564, 0, 4043], [2, "3335", 42, 539, 48, 45, 0], [2, "3336", 59, 563, 48, 45, 0], [2, "3340", 396, 488, 60, 123, 2], [2, "3341", 731, 545, 44, 86, 0], [2, "3330", 723, 604, 60, 43, 0], [2, "3330", 772, 616, 60, 43, 0], [2, "3330", 772, 616, 60, 43, 0], [2, "3330", 680, 625, 60, 43, 0], [2, "3330", 680, 625, 60, 43, 0], [2, "3330", 680, 625, 60, 43, 0], [2, "3331", 811, 618, 54, 59, 0], [2, "3329", 738, 619, 50, 59, 0], [2, "3340", 779, 564, 60, 123, 2], [2, "3341", 126, 618, 44, 86, 0]]}, {"type": 3, "obj": [[2, "3349", 37, 59, 66, 34, 0], [2, "3349", 726, 667, 66, 34, 0], [2, "3349", 703, 432, 66, 34, 0], [2, "3349", 487, 355, 66, 34, 0], [2, "3349", 613, 236, 66, 34, 0], [2, "3350", 83, 113, 66, 34, 0], [2, "3350", 710, 70, 66, 34, 0], [2, "3350", 808, 49, 66, 34, 0], [2, "3350", 781, 35, 66, 34, 0], [2, "3350", 744, 53, 66, 34, 0], [2, "3350", 304, 27, 66, 34, 0], [2, "3350", 277, 13, 66, 34, 0], [2, "3350", 246, -1, 66, 34, 0], [2, "3350", 217, -16, 66, 34, 0], [2, "3350", -16, 120, 66, 34, 0], [2, "3350", 514, 370, 66, 34, 0], [2, "3350", 500, 289, 66, 34, 0], [2, "3350", 495, 323, 66, 34, 0], [2, "3350", 790, 475, 66, 34, 0], [2, "3350", 726, 479, 66, 34, 0], [2, "3350", 759, 460, 66, 34, 0], [2, "3350", 65, 71, 66, 34, 0], [2, "3350", 14, 43, 66, 34, 0], [2, "3350", -31, 382, 66, 34, 0], [2, "3350", -2, 398, 66, 34, 0], [2, "3350", 187, 384, 66, 34, 0], [2, "3350", 116, 422, 66, 34, 0], [2, "3350", 80, 441, 66, 34, 0], [2, "3350", 52, 427, 66, 34, 0], [2, "3350", 24, 413, 66, 34, 0], [2, "3350", 76, 569, 66, 34, 0], [2, "3350", 53, 526, 66, 34, 0], [2, "3350", 82, 540, 66, 34, 0], [2, "3350", 18, 545, 66, 34, 0], [2, "3350", 727, 147, 66, 34, 0], [2, "3350", 755, 163, 66, 34, 0], [2, "3350", 722, 181, 66, 34, 0], [2, "3350", 687, 199, 66, 34, 0], [2, "3350", 650, 218, 66, 34, 0], [2, "3350", 423, 143, 66, 34, 0], [2, "3350", 446, 154, 66, 34, 0], [2, "3350", 472, 166, 66, 34, 0], [2, "3350", 501, 180, 66, 34, 0], [2, "3350", 529, 194, 66, 34, 0], [2, "3350", 556, 208, 66, 34, 0], [2, "3350", 584, 222, 66, 34, 0], [2, "3350", 459, 124, 66, 34, 0], [2, "3338", 580, 80, 56, 53, 0], [2, "3338", 581, 56, 56, 53, 0], [2, "3338", 545, 88, 56, 53, 2], [2, "3338", 545, 61, 56, 53, 2], [2, "3338", 503, 107, 56, 53, 2], [2, "3338", 503, 80, 56, 53, 2], [2, "3339", 484, 96, 14, 58, 0], [2, "3346", 577, 425, 16, 15, 0], [2, "3346", 570, 432, 16, 15, 0], [2, "3346", 566, 441, 16, 15, 0], [2, "3346", 562, 448, 16, 15, 0], [2, "3346", 549, 452, 16, 15, 0], [2, "3346", 544, 463, 16, 15, 0], [2, "3346", 542, 470, 16, 15, 0], [2, "3346", 528, 474, 16, 15, 0], [2, "3346", 527, 484, 16, 15, 0], [2, "3346", 526, 493, 16, 15, 0], [2, "3346", 517, 499, 16, 15, 0], [2, "3346", 504, 501, 16, 15, 0], [2, "3346", 500, 510, 16, 15, 0], [2, "3346", 497, 518, 16, 15, 0], [2, "3346", 495, 525, 16, 15, 0], [2, "3346", 498, 533, 16, 15, 0], [2, "3346", 502, 542, 16, 15, 0], [2, "3346", 512, 547, 16, 15, 0], [2, "3346", 517, 555, 16, 15, 0], [2, "3346", 523, 567, 16, 15, 0], [2, "3346", 539, 572, 16, 15, 0], [2, "3346", 592, 425, 16, 15, 0], [2, "3346", 605, 425, 16, 15, 0], [2, "3346", 615, 428, 16, 15, 0], [2, "3346", 620, 438, 16, 15, 0], [2, "3346", 618, 445, 16, 15, 0], [2, "3346", 627, 451, 16, 15, 0], [2, "3346", 635, 453, 16, 15, 0], [2, "3346", 635, 462, 16, 15, 0], [2, "3346", 637, 469, 16, 15, 0], [2, "3346", 641, 476, 16, 15, 0], [2, "3346", 652, 479, 16, 15, 0], [2, "3346", 661, 480, 16, 15, 0], [2, "3346", 665, 487, 16, 15, 0], [2, "3346", 669, 495, 16, 15, 0], [2, "3346", 670, 506, 16, 15, 0], [2, "3346", 671, 516, 16, 15, 0], [2, "3346", 661, 522, 16, 15, 0], [2, "3346", 648, 524, 16, 15, 0], [2, "3346", 644, 530, 16, 15, 0], [2, "3346", 640, 539, 16, 15, 0], [2, "3346", 631, 546, 16, 15, 0], [2, "3346", 621, 551, 16, 15, 0], [2, "3346", 619, 560, 16, 15, 0], [2, "3346", 614, 568, 16, 15, 0], [2, "3346", 602, 572, 16, 15, 0], [2, "3346", 590, 573, 16, 15, 0], [2, "3346", 578, 573, 16, 15, 0], [2, "3346", 552, 573, 16, 15, 0], [2, "3346", 566, 573, 16, 15, 0], [2, "3334", 187, 157, 24, 31, 0], [2, "3334", 175, 169, 24, 31, 0], [2, "3334", 164, 181, 24, 31, 0], [2, "3334", 159, 194, 24, 31, 0], [2, "3334", 142, 203, 24, 31, 0], [2, "3334", 136, 217, 24, 31, 0], [2, "3334", 132, 234, 24, 31, 0], [2, "3334", 130, 254, 24, 31, 0], [2, "3334", 132, 271, 24, 31, 0], [2, "3334", 136, 287, 24, 31, 0], [2, "3334", 149, 298, 24, 31, 0], [2, "3334", 160, 310, 24, 31, 0], [2, "3334", 178, 319, 24, 31, 0], [2, "3334", 186, 333, 24, 31, 0], [2, "3334", 202, 348, 24, 31, 0], [2, "3334", 211, 360, 24, 31, 0], [2, "3334", 232, 367, 24, 31, 0], [2, "3334", 252, 369, 24, 31, 0], [2, "3334", 207, 148, 24, 31, 0], [2, "3334", 227, 147, 24, 31, 0], [2, "3334", 248, 146, 24, 31, 0], [2, "3334", 268, 148, 24, 31, 0], [2, "3334", 287, 151, 24, 31, 0], [2, "3334", 300, 162, 24, 31, 0], [2, "3334", 314, 171, 24, 31, 0], [2, "3334", 332, 176, 24, 31, 0], [2, "3334", 344, 185, 24, 31, 0], [2, "3334", 353, 198, 24, 31, 0], [2, "3334", 365, 205, 24, 31, 0], [2, "3334", 374, 220, 24, 31, 0], [2, "3334", 390, 229, 24, 31, 0], [2, "3334", 398, 245, 24, 31, 0], [2, "3334", 402, 268, 24, 31, 0], [2, "3334", 402, 289, 24, 31, 0], [2, "3334", 401, 310, 24, 31, 0], [2, "3334", 383, 326, 24, 31, 0], [2, "3334", 372, 339, 24, 31, 0], [2, "3334", 357, 354, 24, 31, 0], [2, "3334", 342, 365, 24, 31, 0], [2, "3334", 323, 375, 24, 31, 0], [2, "3334", 303, 379, 24, 31, 0], [2, "3334", 270, 373, 24, 31, 0], [2, "3334", 284, 378, 24, 31, 0], [2, "3346", 211, 496, 16, 15, 0], [2, "3346", 200, 498, 16, 15, 0], [2, "3346", 190, 502, 16, 15, 0], [2, "3346", 187, 512, 16, 15, 0], [2, "3346", 180, 520, 16, 15, 0], [2, "3346", 168, 522, 16, 15, 0], [2, "3346", 161, 530, 16, 15, 0], [2, "3346", 157, 540, 16, 15, 0], [2, "3346", 156, 550, 16, 15, 0], [2, "3346", 157, 558, 16, 15, 0], [2, "3346", 163, 563, 16, 15, 0], [2, "3346", 175, 570, 16, 15, 0], [2, "3346", 183, 578, 16, 15, 0], [2, "3346", 189, 589, 16, 15, 0], [2, "3346", 202, 594, 16, 15, 0], [2, "3346", 218, 597, 16, 15, 0], [2, "3346", 233, 598, 16, 15, 0], [2, "3346", 223, 497, 16, 15, 0], [2, "3346", 238, 499, 16, 15, 0], [2, "3346", 252, 502, 16, 15, 0], [2, "3346", 257, 511, 16, 15, 0], [2, "3346", 265, 519, 16, 15, 0], [2, "3346", 277, 526, 16, 15, 0], [2, "3346", 281, 536, 16, 15, 0], [2, "3346", 285, 547, 16, 15, 0], [2, "3346", 286, 559, 16, 15, 0], [2, "3346", 279, 568, 16, 15, 0], [2, "3346", 266, 573, 16, 15, 0], [2, "3346", 258, 576, 16, 15, 0], [2, "3346", 255, 586, 16, 15, 0], [2, "3346", 248, 595, 16, 15, 0], [2, "3338", 486, 124, 56, 53, 0], [2, "3338", 487, 100, 56, 53, 0], [2, "3338", 527, 144, 56, 53, 0], [2, "3338", 528, 120, 56, 53, 0], [2, "3338", 566, 165, 56, 53, 0], [2, "3338", 567, 141, 56, 53, 0], [2, "3338", 621, 98, 56, 53, 0], [2, "3338", 622, 74, 56, 53, 0], [2, "3338", 662, 118, 56, 53, 0], [2, "3338", 663, 94, 56, 53, 0], [2, "3338", 703, 137, 56, 53, 0], [2, "3338", 704, 113, 56, 53, 0], [2, "3338", 688, 160, 56, 53, 2], [2, "3338", 688, 133, 56, 53, 2], [2, "3338", 647, 182, 56, 53, 2], [2, "3338", 647, 155, 56, 53, 2], [2, "3338", 606, 184, 56, 53, 0], [2, "3338", 607, 160, 56, 53, 0], [2, "3339", 652, 185, 14, 58, 0], [2, "3339", 750, 135, 14, 58, 0], [2, "1399", 552, 145, 30, 62, 2], [2, "1427", 601, 181, 22, 28, 0], [2, "1427", 510, 133, 22, 28, 0], [2, "3328", 236, 225, 76, 54, 0], [2, "3338", 134, -110, 56, 53, 2], [2, "3330", 539, 322, 60, 43, 0], [2, "3336", 67, 521, 48, 45, 0], [2, "3330", 393, 597, 60, 43, 0], [2, "1398", 692, 154, 28, 66, 2], [2, "3332", 133, 48, 48, 33, 0], [2, "3345", 529, 182, 14, 15, 0], [2, "3345", 519, 192, 14, 15, 0], [2, "3345", 524, 334, 14, 15, 0], [2, "3344", 526, 348, 14, 15, 0], [2, "3344", 365, 397, 14, 15, 0], [2, "3333", 337, 382, 32, 33, 0], [2, "3343", 372, 383, 14, 15, 0], [2, "3345", 316, 400, 14, 15, 0], [2, "3342", 282, 401, 14, 15, 0], [2, "1409", 542, 352, 26, 27, 0], [2, "1409", 588, 346, 26, 27, 0], [2, "3332", 102, 66, 48, 33, 0], [2, "3332", 84, 91, 48, 33, 0], [2, "3332", 129, 89, 48, 33, 0], [2, "3330", -18, -5, 60, 43, 0], [2, "3330", 34, 6, 60, 43, 0], [2, "3330", 34, 6, 60, 43, 0], [2, "3330", -11, 23, 60, 43, 0], [2, "3330", -19, 55, 60, 43, 0], [2, "3330", 660, -15, 60, 43, 0], [2, "3330", 710, -10, 60, 43, 0], [2, "3330", 674, 8, 60, 43, 0], [2, "3329", 726, 0, 50, 59, 0], [2, "3329", 696, 677, 50, 59, 0], [2, "3328", 811, 389, 76, 54, 0], [2, "3328", -47, 86, 76, 54, 0], [2, "3332", 576, 463, 48, 33, 0], [2, "1409", 590, 454, 26, 27, 0], [2, "3330", 70, 407, 60, 43, 0], [2, "3330", -28, 311, 60, 43, 0], [2, "3330", -7, 334, 60, 43, 0], [2, "3330", -26, 361, 60, 43, 0], [2, "3335", 773, -10, 48, 45, 0], [2, "3332", 781, 23, 48, 33, 0], [2, "3331", 820, -21, 54, 59, 0], [2, "3333", 820, 35, 32, 33, 0], [2, "3330", 268, -18, 60, 43, 0], [2, "3330", 314, -4, 60, 43, 0], [2, "3332", 374, 0, 48, 33, 0], [2, "3345", 279, 581, 14, 15, 0], [2, "3345", 298, 403, 14, 15, 0], [2, "3345", 503, 554, 14, 15, 0], [2, "3343", 508, 564, 14, 15, 0], [2, "3343", 522, 577, 14, 15, 0], [2, "3343", 637, 562, 14, 15, 0], [2, "3343", 646, 546, 14, 15, 0], [2, "3342", 541, 580, 14, 15, 0], [2, "3342", 391, 628, 14, 15, 0], [2, "3342", 408, 633, 14, 15, 0], [2, "3342", 590, 578, 14, 15, 0], [2, "3342", 787, 660, 14, 15, 0], [2, "3342", 665, 695, 14, 15, 0], [2, "3343", 291, 686, 14, 15, 0], [2, "3344", 53, 656, 14, 15, 0], [2, "3344", 98, 558, 14, 15, 0], [2, "3344", 164, 576, 14, 15, 0], [2, "3344", 164, 576, 14, 15, 0], [2, "3344", 265, 590, 14, 15, 0], [2, "3344", 115, 265, 14, 15, 0], [2, "3343", 117, 282, 14, 15, 0], [2, "3343", 37, 370, 14, 15, 0], [2, "3342", 109, 443, 14, 15, 0], [2, "3342", 29, 563, 14, 15, 0], [2, "3342", 18, 390, 14, 15, 0], [2, "3342", 95, 119, 14, 15, 0], [2, "3342", 130, 107, 14, 15, 0], [2, "3342", 24, 90, 14, 15, 0], [2, "3344", 42, 55, 14, 15, 0], [2, "3343", 147, 111, 14, 15, 0], [2, "3343", 304, 20, 14, 15, 0], [2, "3344", 319, 26, 14, 15, 0], [2, "3344", 466, 144, 14, 15, 0], [2, "3342", 476, 149, 14, 15, 0], [2, "3342", 370, 21, 14, 15, 0], [2, "3342", 356, 27, 14, 15, 0], [2, "3342", 343, 168, 14, 15, 0], [2, "3342", 578, 206, 14, 15, 0], [2, "3342", 565, 213, 14, 15, 0], [2, "3342", 665, 224, 14, 15, 0], [2, "3343", 682, 221, 14, 15, 0], [2, "3343", 682, 221, 14, 15, 0], [2, "3343", 715, 41, 14, 15, 0], [2, "3343", 772, 38, 14, 15, 0], [2, "3343", 798, 467, 14, 15, 0], [2, "3343", 785, 475, 14, 15, 0], [2, "3344", 809, 474, 14, 15, 0], [2, "3344", 842, 618, 14, 15, 0], [2, "3342", 770, 474, 14, 15, 0], [2, "3333", 96, 561, 32, 33, 0], [2, "3333", 828, 592, 32, 33, 0], [2, "3333", 111, 103, 32, 33, 0], [2, "3330", 842, 210, 60, 43, 0], [2, "3330", 824, 240, 60, 43, 0], [2, "3342", 832, 274, 14, 15, 0], [2, "3350", 666, 641, 66, 34, 0], [2, "3350", 695, 655, 66, 34, 0], [2, "3350", 762, 649, 66, 34, 0], [2, "3350", 789, 663, 66, 34, 0], [2, "3350", 753, 682, 66, 34, 0], [2, "3350", 827, 642, 66, 34, 0], [2, "3350", 799, 626, 66, 34, 0], [2, "3350", 47, 559, 66, 34, 0], [2, "3350", 41, 584, 66, 34, 0], [2, "3350", 5, 602, 66, 34, 0], [2, "3350", 49, 661, 66, 34, 0], [2, "3350", 109, 683, 66, 34, 0], [2, "3350", 152, 403, 66, 34, 0], [2, "3350", 731, 446, 66, 34, 0], [2, "3350", 494, 212, 66, 34, 0], [2, "3350", 458, 230, 66, 34, 0], [2, "3350", 447, 262, 66, 34, 0], [2, "3350", 475, 276, 66, 34, 0], [2, "3350", 469, 308, 66, 34, 0], [2, "3350", 173, 88, 66, 34, 0], [2, "3349", 177, 417, 66, 34, 0], [2, "3349", 141, 434, 66, 34, 0], [2, "3349", 109, 453, 66, 34, 0], [2, "3349", 421, 247, 66, 34, 0], [2, "3349", 292, 59, 66, 34, 0], [2, "3349", 103, 584, 66, 34, 0]]}, {"type": 2, "data": [-1, 21, 21, 6, 7, -1, -1, -1, -1, -1, 25, 28, -1, -1, -1, 27, 21, 22, 23, -1, -1, -1, -1, -1, -1, -1, 25, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, 21, 21, 9, 10, -1, 6, 7, -1, -1, -1, 25, 24, 24, 24, 24, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 28, 27, -1, -1, -1, -1, 21, 22, -1, 28, 27, 21, 21, -1, 9, 10, 12, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, 24, 24, 24, 24, 18, 19, -1, 22, 24, 28, 27, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 26, -1, 20, 24, -1, 21, 22, 24, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 19, -1, 17, 18, 18, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 45, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 34, 27, 27, -1, -1, -1, -1, -1, -1, -1, 39, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 34, 27, 27, -1, -1, -1, -1, 27, 29, 30, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 34, 27, 27, 27, 29, 30, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 35, 34, 30, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 26, -1, -1, -1, -1, -1, -1, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 23, -1, -1, -1, -1, 25, 24, 24, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 19, -1, -1, -1, -1, -1, -1, 25, 28, 27, -1, 21, 22, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, -1, -1, -1, -1, 18, 19, 13, 12, -1, -1, -1, -1, -1, -1, 25, 24, 24, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 16, -1, -1, -1, -1, -1, -1, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 15, -1, 15, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 28, 21, 15, 15, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 18, 18, 18, 18, -1, -1, 13, 12, 12, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 16, 15, 21, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, -1, -1, 21, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 27, 21, 22, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, 25, 24, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, -1, 12, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 27, 27, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 28, 27, 27, 27, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 12, -1, -1, 6, 7, -1, -1, -1, 25, 24, 28, 27, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 12, 16, 15, -1, -1, 9, 10, 6, 7, -1, -1, -1, 25, 24, 24]}, {"type": 2, "data": [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 4, 4, 4, 4, 4, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 4, 4, 4, 4, 4, 4, 4, 4, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 4, 4, 4, 4, 4, 4, 4, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 4, 4, 4, 4, 4, 4, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 4, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 4, 4, 4, 4, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 4, 4, 4, 4, 4, 4, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 4, 4, 4, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 4, 4, 4, 4, 4, 4, 4, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 4, 4, 4, 4, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 4, 4, 4, 4, 4, 4, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 4, 4, 4, 4, 4, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 4, 4, 4, 4, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 4, 4, 4, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]}