{"mW": 912, "mH": 624, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["692", 2, 1, 1], ["335", 0, 1, 1], ["336", 0, 1, 1], ["692", 0, 1, 1], ["497", 0, 2, 1], ["335", 2, 1, 1], ["336", 2, 1, 1], ["416", 0, 4, 1], ["417", 0, 1, 1], ["418", 0, 1, 1], ["416", 2, 4, 1], ["416", 1, 4, 1], ["416", 3, 4, 1], ["694", 0, 1, 1], ["695", 0, 1, 1], ["694", 2, 1, 1], ["695", 2, 1, 1], ["477", 0, 2, 2], ["477", 2, 2, 2], ["477", 1, 2, 2], ["477", 3, 2, 2], ["1300", 2, 3, 2], ["1300", 0, 3, 2], ["1300", 3, 3, 2], ["1300", 1, 3, 2], ["928", 0, 2, 1], ["203_13", 0, 2, 1], ["203_14", 0, 2, 1], ["203_15", 0, 2, 1], ["154", 0, 2, 1], ["373", 0, 1, 1]], "layers": [{"type": 3, "obj": [[2, "1218", 673, -57, 28, 82, 0], [2, "92", 886, 24, 40, 45, 2], [2, "92", 998, 244, 40, 45, 0], [2, "92", 1026, 258, 40, 45, 0], [2, "569", 166, 200, 34, 13, 0], [2, "570", 168, 179, 34, 21, 0], [2, "571", 169, 161, 34, 18, 0], [2, "569", 337, 111, 34, 13, 0], [2, "570", 339, 90, 34, 21, 0], [2, "571", 340, 72, 34, 18, 0], [2, "269", 1002, 201, 110, 58, 0], [2, "269", 1044, 178, 110, 58, 0], [2, "269", 1018, 119, 110, 58, 0], [2, "269", 993, 62, 110, 58, 0], [2, "924", 997, 185, 58, 48, 0], [2, "924", 1026, 199, 58, 48, 0], [2, "1218", 693, -46, 28, 82, 0], [2, "1218", 751, -19, 28, 82, 0], [2, "1218", 770, -8, 28, 82, 0], [2, "1218", 833, 24, 28, 82, 0], [2, "1218", 850, 32, 28, 82, 0], [2, "92", 778, 4, 40, 45, 0], [2, "92", 807, 18, 40, 45, 0], [2, "92", 831, 31, 40, 45, 0], [2, "92", 860, 37, 40, 45, 2], [2, "1218", 1014, -44, 28, 82, 0], [2, "1218", 996, -35, 28, 82, 0], [2, "1220", 685, -27, 48, 39, 2], [2, "1220", 721, -10, 48, 39, 2], [2, "1220", 758, 7, 48, 39, 2], [2, "1220", 885, 36, 48, 39, 0], [2, "1220", 863, 48, 48, 39, 0], [2, "269", 806, -12, 110, 58, 0], [2, "269", 849, -34, 110, 58, 0], [2, "1220", 789, 24, 48, 39, 2], [2, "1220", 824, 41, 48, 39, 2], [2, "967", 6, 207, 24, 41, 0], [2, "967", 6, 182, 24, 41, 0], [2, "967", 128, 263, 24, 41, 0], [2, "967", 128, 238, 24, 41, 0], [2, "967", 129, 212, 24, 41, 0], [2, "967", 129, 187, 24, 41, 0], [2, "967", 6, 155, 24, 41, 0], [2, "967", 6, 130, 24, 41, 0], [2, "967", 5, 104, 24, 41, 0], [2, "967", 5, 79, 24, 41, 0], [2, "967", 362, 19, 24, 41, 0], [2, "967", 362, -6, 24, 41, 0], [2, "967", 363, -32, 24, 41, 0], [2, "967", 261, -21, 24, 41, 0], [2, "683", 1051, 233, 22, 34, 0], [2, "1220", 1025, 255, 48, 39, 2], [2, "733", 862, 192, 54, 35, 2], [2, "733", 559, 62, 54, 35, 2], [2, "733", 678, 122, 54, 35, 2], [2, "733", 175, 371, 54, 35, 2], [2, "733", 58, 314, 54, 35, 2]]}, {"type": 4, "obj": [[2, "1218", 733, -18, 28, 82, 0], [2, "1218", 673, 9, 28, 82, 0], [2, "1218", 692, 20, 28, 82, 0], [2, "1218", 1014, 23, 28, 82, 0], [2, "1218", 995, 31, 28, 82, 0], [2, "1218", 750, 47, 28, 82, 0], [2, "1218", 769, 58, 28, 82, 0], [2, "986", 323, 88, 32, 61, 0], [2, "986", 355, 88, 32, 61, 2], [2, "1218", 830, 90, 28, 82, 0], [2, "922", 557, 66, 30, 108, 0], [2, "1218", 849, 101, 28, 82, 0], [2, "922", 584, 80, 30, 108, 0], [2, "922", 678, 126, 30, 108, 0], [2, "986", 152, 175, 32, 61, 0], [2, "986", 184, 176, 32, 61, 2], [2, "922", 703, 140, 30, 108, 0], [2, "922", 860, 194, 30, 108, 0], [2, "922", 892, 206, 30, 108, 0], [2, "969", 801, 318, 36, 30, 0], [2, "922", 997, 257, 30, 108, 0], [2, "922", 1025, 270, 30, 108, 0], [2, "922", 57, 318, 30, 108, 0], [2, "922", 82, 332, 30, 108, 0], [2, "922", 174, 376, 30, 108, 0], [2, "922", 199, 390, 30, 108, 0], [2, "690", 321, 448, 36, 85, 0]]}, {"type": 2, "data": [4, 4, 4, 4, 4, 4, 4, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 4, 4, 4, 4, 4, 4, 4, 4, 34, 35, 36, -1, -1, -1, -1, -1, -1, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, -1, -1, -1, -1, 16, 16, 4, 4, 4, 4, 4, 16, 24, 25, 26, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 10, 10, 10, 22, 16, 16, 4, 4, 4, 4, 24, 25, 26, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 10, 10, 16, 4, 4, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 16, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 31, 30, 29, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, 31, 31, 30, 29, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "364", 393, 81, 64, 44, 5], [2, "412", 420, 60, 64, 100, 0], [2, "364", 31, 322, 44, 64, 0], [2, "412", -17, 302, 64, 100, 0], [2, "507", 764, 369, 48, 58, 0], [2, "656", 331, 487, 22, 22, 0], [2, "656", 353, 487, 22, 22, 0], [2, "693", 323, 67, 24, 16, 2], [2, "921", 246, 7, 56, 68, 2], [2, "688", 240, 52, 46, 24, 2], [2, "498", 40, 356, 56, 32, 0], [2, "683", -8, 289, 22, 34, 2], [2, "683", 2, 294, 22, 34, 2], [2, "683", 10, 298, 22, 34, 2], [2, "683", 19, 303, 22, 34, 2], [2, "683", 29, 308, 22, 34, 2], [2, "683", 38, 312, 22, 34, 2], [2, "683", 48, 317, 22, 34, 2], [2, "683", 56, 321, 22, 34, 2], [2, "683", 65, 326, 22, 34, 2], [2, "683", 75, 331, 22, 34, 2], [2, "683", 84, 335, 22, 34, 2], [2, "683", 94, 340, 22, 34, 2], [2, "683", 102, 344, 22, 34, 2], [2, "683", 111, 349, 22, 34, 2], [2, "683", 121, 354, 22, 34, 2], [2, "683", -10, 227, 22, 34, 2], [2, "683", 0, 232, 22, 34, 2], [2, "683", 8, 236, 22, 34, 2], [2, "683", 17, 241, 22, 34, 2], [2, "683", 27, 246, 22, 34, 2], [2, "683", 36, 250, 22, 34, 2], [2, "683", 46, 255, 22, 34, 2], [2, "683", 54, 259, 22, 34, 2], [2, "683", 63, 264, 22, 34, 2], [2, "683", 73, 269, 22, 34, 2], [2, "683", 340, 127, 22, 34, 2], [2, "683", 332, 131, 22, 34, 2], [2, "683", 324, 135, 22, 34, 2], [2, "683", 350, 132, 22, 34, 2], [2, "683", 342, 136, 22, 34, 2], [2, "683", 334, 140, 22, 34, 2], [2, "683", 360, 137, 22, 34, 2], [2, "683", 352, 141, 22, 34, 2], [2, "683", 344, 145, 22, 34, 2], [2, "513", 499, 135, 20, 40, 0], [2, "683", 497, 166, 22, 34, 2], [2, "683", 488, 170, 22, 34, 2], [2, "683", 480, 174, 22, 34, 2], [2, "683", 471, 179, 22, 34, 2], [2, "683", 462, 183, 22, 34, 2], [2, "683", 454, 187, 22, 34, 2], [2, "683", 446, 191, 22, 34, 2], [2, "683", 438, 195, 22, 34, 2], [2, "683", 430, 199, 22, 34, 2], [2, "683", 421, 204, 22, 34, 2], [2, "683", 412, 208, 22, 34, 2], [2, "683", 404, 212, 22, 34, 2], [2, "683", 396, 216, 22, 34, 2], [2, "683", 419, 143, 22, 34, 2], [2, "683", 412, 146, 22, 34, 2], [2, "683", 404, 151, 22, 34, 2], [2, "683", 396, 155, 22, 34, 2], [2, "683", 388, 159, 22, 34, 2], [2, "683", 379, 164, 22, 34, 2], [2, "683", 370, 168, 22, 34, 2], [2, "683", 362, 172, 22, 34, 2], [2, "683", 354, 176, 22, 34, 2], [2, "683", 247, 48, 22, 34, 0], [2, "683", 239, 52, 22, 34, 0], [2, "683", 230, 57, 22, 34, 0], [2, "683", 221, 61, 22, 34, 0], [2, "683", 59, 143, 22, 34, 2], [2, "683", 68, 148, 22, 34, 2], [2, "683", 76, 153, 22, 34, 2], [2, "683", 86, 158, 22, 34, 2], [2, "683", 94, 162, 22, 34, 2], [2, "683", 103, 167, 22, 34, 2], [2, "683", 113, 172, 22, 34, 2], [2, "688", 219, 33, 46, 24, 2], [2, "468", 498, 484, 58, 31, 0], [2, "693", 678, 57, 24, 16, 0], [2, "693", 195, 320, 24, 16, 0], [2, "693", 626, 80, 24, 16, 0], [2, "693", 532, 128, 24, 16, 0], [2, "683", 760, 377, 22, 34, 0], [2, "683", 752, 381, 22, 34, 0], [2, "683", 744, 385, 22, 34, 0], [2, "683", 736, 389, 22, 34, 0], [2, "683", 726, 394, 22, 34, 0], [2, "683", 324, 476, 22, 34, 0], [2, "683", 315, 480, 22, 34, 0], [2, "683", 306, 485, 22, 34, 0], [2, "683", 297, 489, 22, 34, 0], [2, "683", 288, 493, 22, 34, 0], [2, "683", 280, 497, 22, 34, 0], [2, "683", 272, 501, 22, 34, 0], [2, "683", 264, 505, 22, 34, 0], [2, "683", 255, 509, 22, 34, 0], [2, "683", 246, 514, 22, 34, 0], [2, "683", 238, 518, 22, 34, 0], [2, "683", 228, 522, 22, 34, 0], [2, "683", 220, 527, 22, 34, 0], [2, "683", 211, 531, 22, 34, 0], [2, "683", 203, 535, 22, 34, 0], [2, "683", 195, 539, 22, 34, 0], [2, "683", 186, 544, 22, 34, 0], [2, "683", 177, 548, 22, 34, 0], [2, "683", 168, 553, 22, 34, 0], [2, "683", 159, 557, 22, 34, 0], [2, "683", 151, 561, 22, 34, 0], [2, "683", 142, 566, 22, 34, 0], [2, "683", 135, 569, 22, 34, 0], [2, "683", 127, 573, 22, 34, 0], [2, "92", 120, -5, 40, 45, 0], [2, "92", 149, 9, 40, 45, 0], [2, "249", 177, -6, 48, 72, 2], [2, "250", 185, -53, 32, 62, 2], [2, "92", 138, 22, 40, 45, 2], [2, "92", 8, 87, 40, 45, 2], [2, "479", 725, 483, 36, 18, 0], [2, "498", 501, 122, 56, 32, 0], [2, "498", 0, 377, 56, 32, 0], [2, "498", -39, 397, 56, 32, 0], [2, "10", 59, 39, 50, 26, 0], [2, "10", 31, 50, 50, 26, 2], [2, "10", 83, 52, 50, 26, 0], [2, "10", 55, 63, 50, 26, 0], [2, "10", 98, 4, 50, 26, 0], [2, "10", 123, 16, 50, 26, 0], [2, "10", -30, 70, 50, 26, 0], [2, "10", -6, 82, 50, 26, 2], [2, "10", 75, -10, 50, 26, 0], [2, "92", -21, 93, 40, 45, 0], [2, "468", 286, 471, 58, 31, 0], [2, "468", 245, 491, 58, 31, 0], [2, "468", 204, 511, 58, 31, 0], [2, "468", 163, 531, 58, 31, 0], [2, "508", 739, 408, 60, 37, 0], [2, "508", 692, 432, 60, 37, 0], [2, "508", 647, 454, 60, 37, 0], [2, "508", 730, 397, 60, 37, 0], [2, "508", 683, 421, 60, 37, 0], [2, "508", 638, 443, 60, 37, 0], [2, "508", 719, 386, 60, 37, 0], [2, "508", 672, 410, 60, 37, 0], [2, "508", 627, 432, 60, 37, 0], [2, "683", 846, 217, 22, 34, 0], [2, "683", 837, 222, 22, 34, 0], [2, "683", 829, 226, 22, 34, 0], [2, "683", 821, 230, 22, 34, 0], [2, "683", 812, 234, 22, 34, 0], [2, "683", 803, 239, 22, 34, 0], [2, "683", 794, 243, 22, 34, 0], [2, "683", 785, 248, 22, 34, 0], [2, "683", 777, 252, 22, 34, 0], [2, "683", 768, 257, 22, 34, 0], [2, "683", 761, 260, 22, 34, 0], [2, "683", 753, 264, 22, 34, 0], [2, "683", 744, 268, 22, 34, 0], [2, "683", 735, 273, 22, 34, 0], [2, "683", 727, 277, 22, 34, 0], [2, "683", 369, 487, 22, 34, 2], [2, "683", 378, 492, 22, 34, 2], [2, "683", 388, 497, 22, 34, 2], [2, "683", 398, 502, 22, 34, 2], [2, "683", 407, 507, 22, 34, 2], [2, "656", 441, 522, 22, 22, 2], [2, "656", 419, 522, 22, 22, 0], [2, "656", 461, 522, 22, 22, 0], [2, "656", 482, 522, 22, 22, 2], [2, "656", 504, 522, 22, 22, 0], [2, "656", 444, 514, 22, 22, 2], [2, "656", 422, 514, 22, 22, 0], [2, "656", 464, 514, 22, 22, 0], [2, "656", 485, 514, 22, 22, 2], [2, "656", 507, 514, 22, 22, 0], [2, "683", 119, 577, 22, 34, 0], [2, "683", 111, 581, 22, 34, 0], [2, "498", 655, 45, 56, 32, 0], [2, "693", 578, 104, 24, 16, 0], [2, "693", 408, 32, 24, 16, 2], [2, "693", 360, 8, 24, 16, 2], [2, "513", 323, 470, 40, 21, 6], [2, "468", 365, 477, 58, 31, 2], [2, "468", 383, 486, 58, 31, 2], [2, "513", 759, 313, 20, 40, 2], [2, "513", 759, 341, 20, 40, 2], [2, "264", 11, 63, 66, 34, 2], [2, "264", 86, 26, 66, 34, 2], [2, "251", 124, 44, 68, 57, 2], [2, "249", 73, -26, 48, 72, 2], [2, "250", 81, -73, 32, 62, 2], [2, "265", 12, -19, 68, 77, 2], [2, "481", 639, 445, 28, 8, 0], [2, "338", 396, 68, 34, 31, 0], [2, "338", 417, 58, 34, 31, 0], [2, "338", 434, 50, 34, 31, 0], [2, "340", 425, 39, 34, 18, 2], [2, "340", 404, 28, 34, 18, 2], [2, "340", 383, 17, 34, 18, 2], [2, "340", 362, 6, 34, 18, 2], [2, "340", 340, -5, 34, 18, 2], [2, "498", 575, 85, 56, 32, 0], [2, "508", 103, 90, 60, 37, 0], [2, "508", 92, 80, 60, 37, 0], [2, "508", 81, 68, 60, 37, 0], [2, "508", 69, 57, 60, 37, 0], [2, "251", 48, 82, 68, 57, 2], [2, "249", -11, 6, 48, 72, 2], [2, "250", -3, -41, 32, 62, 2], [2, "249", -22, 93, 48, 72, 2], [2, "250", -14, 49, 32, 62, 2], [2, "250", -14, 4, 32, 62, 2], [2, "250", -14, -40, 32, 62, 2], [2, "498", 541, 102, 56, 32, 0], [2, "479", 402, 536, 36, 18, 2], [2, "468", 804, 212, 58, 31, 0], [2, "468", 762, 233, 58, 31, 0], [2, "468", 722, 253, 58, 31, 0], [2, "513", 722, 272, 20, 40, 2], [2, "498", 723, 300, 56, 32, 2], [2, "468", 719, 371, 58, 31, 0], [2, "468", 678, 391, 58, 31, 0], [2, "468", 639, 410, 58, 31, 0], [2, "468", 613, 423, 58, 31, 0], [2, "513", 425, 496, 40, 21, 7], [2, "513", 456, 496, 40, 21, 7], [2, "513", 340, 470, 40, 21, 6], [2, "468", 124, 550, 58, 31, 0], [2, "468", 83, 570, 58, 31, 0], [2, "479", 195, 50, 36, 18, 0], [2, "479", -6, 150, 36, 18, 0], [2, "479", 234, 518, 36, 18, 0], [2, "479", 204, 576, 36, 18, 2], [2, "479", 781, 271, 36, 18, 0], [2, "481", 743, 385, 28, 8, 0], [2, "481", 317, 471, 28, 8, 0], [2, "479", 411, 55, 36, 18, 0], [2, "683", 1044, 208, 22, 34, 0], [2, "683", 1045, 202, 22, 34, 0], [2, "688", 201, 72, 46, 24, 2], [2, "688", 238, 90, 46, 24, 2], [2, "688", 277, 109, 46, 24, 2], [2, "683", 303, 124, 22, 34, 0], [2, "683", 295, 128, 22, 34, 0], [2, "683", 286, 133, 22, 34, 0], [2, "683", 277, 137, 22, 34, 0], [2, "683", 269, 141, 22, 34, 0], [2, "683", 261, 145, 22, 34, 0], [2, "683", 253, 149, 22, 34, 0], [2, "683", 245, 153, 22, 34, 0], [2, "683", 236, 158, 22, 34, 0], [2, "683", 227, 162, 22, 34, 0], [2, "683", 219, 166, 22, 34, 0], [2, "683", 211, 170, 22, 34, 0], [2, "683", 202, 175, 22, 34, 0], [2, "683", 194, 179, 22, 34, 0], [2, "683", 185, 184, 22, 34, 0], [2, "683", 176, 188, 22, 34, 0], [2, "683", 51, 147, 22, 34, 2], [2, "683", 43, 151, 22, 34, 2], [2, "683", 34, 156, 22, 34, 2], [2, "683", 25, 160, 22, 34, 2], [2, "683", 17, 164, 22, 34, 2], [2, "683", 9, 168, 22, 34, 2], [2, "683", 169, 191, 22, 34, 0], [2, "683", 123, 176, 22, 34, 2], [2, "683", 133, 181, 22, 34, 2], [2, "683", 141, 185, 22, 34, 2], [2, "683", 150, 190, 22, 34, 2], [2, "683", 160, 195, 22, 34, 2], [2, "134", 296, 134, 62, 57, 0], [2, "688", 202, 57, 46, 24, 0], [2, "508", 247, 145, 60, 37, 0], [2, "508", 200, 168, 60, 37, 0], [2, "508", 259, 157, 60, 37, 0], [2, "508", 212, 180, 60, 37, 0], [2, "508", 283, 181, 60, 37, 0], [2, "508", 236, 204, 60, 37, 0], [2, "508", 271, 169, 60, 37, 0], [2, "508", 224, 192, 60, 37, 0], [2, "134", 187, 191, 62, 57, 0], [2, "134", 344, 181, 62, 57, 0], [2, "508", 295, 192, 60, 37, 0], [2, "508", 248, 215, 60, 37, 0], [2, "508", 307, 204, 60, 37, 0], [2, "508", 260, 227, 60, 37, 0], [2, "508", 331, 228, 60, 37, 0], [2, "508", 284, 251, 60, 37, 0], [2, "508", 319, 216, 60, 37, 0], [2, "508", 272, 239, 60, 37, 0], [2, "134", 235, 238, 62, 57, 0], [2, "683", 219, 242, 22, 34, 2], [2, "683", 211, 246, 22, 34, 2], [2, "683", 202, 251, 22, 34, 2], [2, "683", 193, 255, 22, 34, 2], [2, "683", 185, 259, 22, 34, 2], [2, "683", 177, 263, 22, 34, 2], [2, "683", 169, 267, 22, 34, 2], [2, "683", 161, 271, 22, 34, 2], [2, "683", 152, 276, 22, 34, 2], [2, "683", 143, 280, 22, 34, 2], [2, "683", 135, 284, 22, 34, 2], [2, "683", 127, 288, 22, 34, 2], [2, "683", 265, 291, 22, 34, 2], [2, "683", 257, 295, 22, 34, 2], [2, "683", 248, 300, 22, 34, 2], [2, "683", 239, 304, 22, 34, 2], [2, "683", 231, 308, 22, 34, 2], [2, "683", 223, 312, 22, 34, 2], [2, "338", 374, 78, 34, 31, 0], [2, "688", 275, 70, 46, 24, 2], [2, "688", 315, 90, 46, 24, 2], [2, "688", 354, 109, 46, 24, 2], [2, "688", 391, 127, 46, 24, 2], [2, "688", 294, 61, 46, 24, 2], [2, "688", 330, 79, 46, 24, 2], [2, "688", 368, 98, 46, 24, 2], [2, "688", 406, 117, 46, 24, 2], [2, "688", 444, 136, 46, 24, 2], [2, "688", 470, 149, 46, 24, 2], [2, "921", 346, 47, 56, 68, 2], [2, "683", 170, 213, 22, 34, 2], [2, "683", 162, 217, 22, 34, 2], [2, "683", 154, 221, 22, 34, 2], [2, "683", 180, 218, 22, 34, 2], [2, "683", 172, 222, 22, 34, 2], [2, "683", 164, 226, 22, 34, 2], [2, "683", 190, 223, 22, 34, 2], [2, "683", 182, 227, 22, 34, 2], [2, "683", 174, 231, 22, 34, 2], [2, "506", 83, 160, 20, 45, 2], [2, "507", 43, 166, 48, 58, 2], [2, "508", 89, 166, 60, 37, 2], [2, "508", 79, 180, 60, 37, 2], [2, "508", 68, 194, 60, 37, 2], [2, "508", 57, 209, 60, 37, 2], [2, "506", 137, 189, 20, 45, 2], [2, "507", 97, 195, 48, 58, 2], [2, "683", 82, 273, 22, 34, 2], [2, "683", 92, 278, 22, 34, 2], [2, "683", 100, 282, 22, 34, 2], [2, "683", 109, 287, 22, 34, 2], [2, "683", 119, 292, 22, 34, 2], [2, "683", 213, 316, 22, 34, 2], [2, "683", 205, 320, 22, 34, 2], [2, "683", 196, 325, 22, 34, 2], [2, "683", 187, 329, 22, 34, 2], [2, "683", 179, 333, 22, 34, 2], [2, "683", 171, 337, 22, 34, 2], [2, "683", 163, 341, 22, 34, 2], [2, "683", 155, 345, 22, 34, 2], [2, "683", 146, 350, 22, 34, 2], [2, "683", 137, 354, 22, 34, 2], [2, "683", 129, 358, 22, 34, 2], [2, "921", 111, 289, 56, 68, 2], [2, "683", 1, 172, 22, 34, 2], [2, "683", -7, 176, 22, 34, 2], [2, "683", -16, 181, 22, 34, 2], [2, "498", 696, 25, 56, 32, 0], [2, "498", 737, 5, 56, 32, 0], [2, "498", 777, -15, 56, 32, 0], [2, "468", 574, 443, 58, 31, 0], [2, "468", 537, 463, 58, 31, 0], [2, "508", 602, 477, 60, 37, 0], [2, "508", 555, 501, 60, 37, 0], [2, "508", 592, 465, 60, 37, 0], [2, "508", 579, 455, 60, 37, 0], [2, "479", 511, 522, 36, 18, 2], [2, "508", 532, 479, 60, 37, 0], [2, "508", 545, 489, 60, 37, 0], [2, "507", 526, 490, 48, 58, 0], [2, "683", 514, 514, 22, 34, 0], [2, "513", 487, 496, 40, 21, 7], [2, "381", 529, 8, 72, 48, 0], [2, "382", 601, -15, 24, 72, 0], [2, "383", 625, -13, 48, 72, 0], [2, "384", 673, -10, 24, 72, 0], [2, "479", 354, 506, 36, 18, 2], [2, "479", 115, 558, 36, 18, 0], [2, "479", 138, 429, 36, 18, 0], [2, "479", 34, 382, 36, 18, 0], [2, "479", 142, 379, 36, 18, 0], [2, "479", 2, 569, 36, 18, 0], [2, "479", 643, 189, 36, 18, 2], [2, "479", 681, 62, 36, 18, 0], [2, "479", 531, 136, 36, 18, 0], [2, "479", 371, 312, 36, 18, 0], [2, "422", 530, 530, 16, 14, 0], [2, "422", 355, 502, 16, 14, 0], [2, "479", 749, 306, 36, 18, 2], [2, "498", 615, 65, 56, 32, 0], [2, "134", 392, 228, 62, 57, 0], [2, "508", 343, 239, 60, 37, 0], [2, "508", 296, 262, 60, 37, 0], [2, "508", 355, 251, 60, 37, 0], [2, "508", 308, 274, 60, 37, 0], [2, "508", 379, 275, 60, 37, 0], [2, "508", 332, 298, 60, 37, 0], [2, "508", 367, 263, 60, 37, 0], [2, "508", 320, 286, 60, 37, 0], [2, "134", 283, 285, 62, 57, 0], [2, "921", 454, 108, 56, 68, 2], [2, "921", -9, 234, 56, 68, 2], [2, "965", 529, 547, 40, 33, 0], [2, "965", 817, 392, 40, 33, 0], [2, "479", 814, 360, 36, 18, 2], [2, "479", 305, 429, 36, 18, 2], [2, "479", 107, 144, 36, 18, 0], [2, "479", 176, 315, 36, 18, 0], [2, "479", 547, 285, 36, 18, 2], [2, "479", 640, 106, 36, 18, 0], [2, "479", 727, 351, 36, 18, 0], [2, "422", 700, 262, 16, 14, 0], [2, "422", 624, 191, 16, 14, 0], [2, "422", 268, 481, 16, 14, 0], [2, "683", 923, 180, 22, 34, 0], [2, "683", 915, 184, 22, 34, 0], [2, "683", 906, 189, 22, 34, 0], [2, "683", 898, 191, 22, 34, 0], [2, "683", 890, 195, 22, 34, 0], [2, "683", 881, 200, 22, 34, 0], [2, "683", 863, 209, 22, 34, 0], [2, "683", 1026, 199, 22, 34, 0], [2, "683", 1036, 204, 22, 34, 0], [2, "683", 1027, 193, 22, 34, 0], [2, "683", 1037, 198, 22, 34, 0], [2, "468", 843, 193, 58, 31, 0], [2, "468", 1024, 186, 58, 31, 2], [2, "479", 1007, 168, 36, 18, 2], [2, "924", 846, 281, 58, 48, 0], [2, "924", 875, 295, 58, 48, 0], [2, "683", 102, 586, 22, 34, 0], [2, "683", 95, 589, 22, 34, 0], [2, "683", 87, 593, 22, 34, 0], [2, "513", 82, 588, 20, 40, 2], [2, "513", 82, 608, 20, 40, 2], [2, "479", 674, 589, 36, 18, 2], [2, "683", 872, 204, 22, 34, 0], [2, "309", 377, 101, 46, 33, 0], [2, "305", 428, 132, 30, 24, 0], [2, "3285", 446, 330, 166, 79, 0], [2, "3289", 499, 349, 68, 37, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, 81, 80, 79, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, 81, 79, 78, 78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, 81, 80, 80, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, 10, 10, 10, 10, -1, -1, 10, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 16, 29, 10, 10, 10, 10, 10, -1, 10, 10, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 75, -1, -1, 31, 30, 29, 16, 16, 10, 10, 10, 10, 10, 10, 24, 24, 25, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, 74, 75, 76, 77, -1, -1, -1, -1, 31, 30, 29, 16, 16, 16, 10, 22, 10, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, 76, 77, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 16, 29, 22, -1, -1, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, 75, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 10, 10, 10, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, 75, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 22, 31, 30, 29, 10, 10, 39, 10, -1, 10, -1, -1, -1, -1, -1, -1, 74, 75, 76, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, -1, -1, 31, 30, 29, 10, 10, 17, 10, 10, -1, -1, -1, 74, 75, 76, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 24, 30, 29, 22, -1, 31, 30, 29, 18, 16, 24, 25, 26, 75, 76, 77, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, 22, 4, 4, -1, 31, 30, 29, 22, -1, 16, 24, 25, 26, 75, 76, 77, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 4, 4, 4, 4, 4, -1, -1, -1, 31, 30, 29, 25, 26, 75, 76, 22, 24, 25, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 30, 29, 22, 4, -1, -1, -1, -1, -1, -1, -1, 76, 22, 24, 25, 26, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 25, -1, 31, 30, 29, -1, -1, -1, 30, 29, 22, 24, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, 31, -1, -1, 26, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 10, 10, 10, 10, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 89, -1, -1, -1, 22, 22, 24, 19, 19, 19, 29, 10, 22, 22, 22, 22, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, 20, 19, 19, 19, 19, 19, 19, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, -1, -1, 51, 46, 51, -1, 51, 45, 50, 46, 51, 45, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 51, 45, 50, 46, 51, -1, -1, -1, -1, -1, 51, 46, 51, -1, 50, 46, 51, 70, 50, 46, 51, 45, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 51, 45, 50, 46, 51, 45, 50, 44, -1, -1, -1, -1, 51, 114, 115, 114, 115, 45, 49, 46, 51, 45, 50, 46, 51, 46, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 51, 45, 50, 46, 51, 45, 50, 46, 51, 44, 44, 44, -1, -1, 51, 114, 115, 114, 115, 48, 48, 48, 49, 46, 51, 45, 124, 124, 124, 124, 124, 124, 124, 124, 124, 124, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 48, 48, 49, 49, 49, 124, 124, 124, 124, 124, 124, 124, 124, 124, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 45, 48, 48, 49, 49, 49, 124, 124, 124, 124, 124, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 46, 48, 49, 49, 48, 48, 49, 49, 56, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 114, 115, 49, 49, 45, 114, 115, 114, 115, 114, 115, 51, 45, 50, 48, 49, 49, 49, 48, 48, 49, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 48, 48, 49, 48, 48, 49, 45, 50, 46, 51, 45, 50, 46, 51, 45, 48, 48, 48, 49, 50, 46, 51, 45, 72, 46, 51, 70, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 48, 48, 49, 48, 49, 48, 49, 48, 49, 50, 48, 49, 45, 50, 46, 51, 48, 49, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 45, -1, 122, 48, 49, 48, 49, 48, 48, 49, 48, 49, 48, 49, 48, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, -1, 122, 48, 48, 48, 124, 48, 49, 48, 49, 48, 48, 49, 48, 49, 49, 56, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 73, 45, 72, 46, -1, 122, 48, 48, 48, 48, 48, 124, 49, 48, 49, 48, 49, 49, 48, 49, 48, 49, 49, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 70, 50, 46, 51, 45, 50, 46, 51, 46, 48, 48, 48, 48, 48, 48, 48, 124, 124, 49, 48, 49, 48, 49, 48, 48, 49, 46, 51, 45, 50, 46, 51, 70, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 48, 48, 48, 48, 48, 48, 124, 124, 124, 53, 48, 48, 49, 48, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 73, 45, 50, 71, 51, 45, 50, 48, 48, 48, 48, 48, 48, 124, 124, 124, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 48, 48, 48, 48, 48, 48, 124, 51, 45, 50, 46, 51, 70, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 44, 48, 48, 48, 48, 48, 48, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 72, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 44, 47, 44, 48, 48, 48, 48, 48, 48, 46, 51, 45, 50, 46, 51, 70, 50, 46, 51, 45, 50, 71, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 44, 44, 44, 48, 48, 48, 48, 48, 48, 48, 48, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, 122, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 45, 72, 46, -1, -1, 122, 46, 51, 45, 50, 46, 51, 45, 50, 46, 51, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 45, 50, 46, 51, 45, 50, 46, 51, 45, 50, 46, -1, 48, 48, 48, 48, 122, 123, 123, 122, 123, 122, 122, 123, 122, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 46, 51, 45, 50, 46, 51, 45, 50, 46, -1, 48, 49, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 45, 50, 46, 51, 45, 50, 46, -1, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 71, 51, 45, 50, 46, -1, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 45, 50, 46, 51, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}