{"mW": 840, "mH": 720, "tW": 24, "tH": 24, "tiles": [["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["203_20", 0, 2, 1], ["203_21", 0, 2, 1], ["203_22", 0, 2, 1], ["203_23", 0, 2, 1], ["203_24", 0, 2, 1], ["203_24", 1, 2, 1], ["203_23", 1, 2, 1], ["203_22", 1, 2, 1], ["203_21", 1, 2, 1], ["203_20", 1, 2, 1], ["981", 0, 1, 1], ["154", 0, 2, 1], ["497", 0, 2, 1]], "layers": [{"type": 3, "obj": [[2, "986", 382, 123, 32, 61, 0], [2, "986", 414, 123, 32, 61, 2], [2, "986", 105, 264, 32, 61, 0], [2, "986", 137, 264, 32, 61, 2], [2, "986", 394, 413, 32, 61, 0], [2, "986", 426, 413, 32, 61, 2], [2, "986", 676, 273, 32, 61, 0], [2, "986", 708, 273, 32, 61, 2]]}, {"type": 4, "obj": [[2, "967", 779, -16, 24, 41, 0], [2, "976", 68, 12, 34, 37, 0], [2, "978", 9, 7, 66, 56, 0], [2, "219_3", 26, 37, 36, 30, 2], [2, "276", 731, -1, 92, 134, 0], [2, "921", 385, 165, 56, 68, 0], [2, "272", 123, 212, 72, 54, 2], [2, "272", 655, 229, 72, 54, 0], [2, "271", 185, 240, 64, 50, 2], [2, "271", 603, 257, 64, 50, 0], [2, "921", 108, 305, 56, 68, 0], [2, "921", 679, 315, 56, 68, 0], [2, "272", 534, 441, 72, 54, 2], [2, "272", 235, 444, 72, 54, 0], [2, "271", 596, 469, 64, 50, 2], [2, "271", 183, 472, 64, 50, 0], [2, "921", 398, 455, 56, 68, 0], [2, "1236", 404, 483, 42, 43, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 31, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 31, 24, 24, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 24, 24, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 43, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 31, 30, 37, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 37, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 24, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 21, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 26, -1, -1, -1, -1, -1, -1, 28, 27, 27, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 0, 1, 2, -1, -1, -1, -1, 28, 27, 31, 30, 0, 5, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, 0, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 31, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "1457", 807, 144, 22, 30, 0], [2, "972", 340, 127, 66, 54, 0], [2, "971", 281, 152, 58, 50, 0], [2, "972", 232, 181, 66, 54, 0], [2, "971", 173, 206, 58, 50, 0], [2, "972", 125, 235, 66, 54, 0], [2, "971", 66, 260, 58, 50, 0], [2, "972", 17, 289, 66, 54, 0], [2, "971", -42, 314, 58, 50, 0], [2, "971", 539, 179, 58, 50, 2], [2, "972", 472, 154, 66, 54, 2], [2, "972", 580, 208, 66, 54, 2], [2, "971", 647, 233, 58, 50, 2], [2, "972", 688, 261, 66, 54, 2], [2, "971", 755, 286, 58, 50, 2], [2, "972", 796, 315, 66, 54, 2], [2, "971", 166, 428, 58, 50, 2], [2, "972", 99, 403, 66, 54, 2], [2, "972", 207, 457, 66, 54, 2], [2, "971", 274, 482, 58, 50, 2], [2, "972", 315, 510, 66, 54, 2], [2, "971", 382, 535, 58, 50, 2], [2, "972", 678, 410, 66, 54, 0], [2, "971", 629, 430, 58, 50, 0], [2, "972", 581, 459, 66, 54, 0], [2, "971", 522, 484, 58, 50, 0], [2, "972", 473, 513, 66, 54, 0], [2, "971", 414, 538, 58, 50, 0], [2, "971", 552, 661, 58, 50, 2], [2, "972", 485, 636, 66, 54, 2], [2, "972", 593, 690, 66, 54, 2], [2, "972", 348, 657, 66, 54, 0], [2, "971", 289, 682, 58, 50, 0], [2, "818", 473, 139, 30, 37, 2], [2, "818", 503, 154, 30, 37, 2], [2, "818", 533, 169, 30, 37, 2], [2, "818", 563, 184, 30, 37, 2], [2, "818", 683, 245, 30, 37, 2], [2, "818", 653, 229, 30, 37, 2], [2, "818", 623, 214, 30, 37, 2], [2, "818", 593, 199, 30, 37, 2], [2, "818", 833, 320, 30, 37, 2], [2, "818", 803, 305, 30, 37, 2], [2, "818", 773, 290, 30, 37, 2], [2, "818", 743, 275, 30, 37, 2], [2, "818", 713, 260, 30, 37, 2], [2, "818", 100, 388, 30, 37, 2], [2, "818", 130, 403, 30, 37, 2], [2, "818", 160, 418, 30, 37, 2], [2, "818", 190, 433, 30, 37, 2], [2, "818", 310, 494, 30, 37, 2], [2, "818", 280, 478, 30, 37, 2], [2, "818", 250, 463, 30, 37, 2], [2, "818", 220, 448, 30, 37, 2], [2, "818", 370, 524, 30, 37, 2], [2, "818", 340, 509, 30, 37, 2], [2, "818", 399, 539, 30, 37, 2], [2, "818", 377, 112, 30, 37, 0], [2, "818", 347, 127, 30, 37, 0], [2, "818", 288, 157, 30, 37, 0], [2, "818", 318, 142, 30, 37, 0], [2, "818", 258, 172, 30, 37, 0], [2, "818", 228, 187, 30, 37, 0], [2, "818", 199, 202, 30, 37, 0], [2, "818", 169, 217, 30, 37, 0], [2, "818", 139, 232, 30, 37, 0], [2, "818", 109, 247, 30, 37, 0], [2, "818", 50, 277, 30, 37, 0], [2, "818", 80, 262, 30, 37, 0], [2, "818", 20, 292, 30, 37, 0], [2, "818", -10, 307, 30, 37, 0], [2, "818", 383, 643, 30, 37, 0], [2, "818", 353, 658, 30, 37, 0], [2, "818", 323, 673, 30, 37, 0], [2, "818", 264, 703, 30, 37, 0], [2, "818", 294, 688, 30, 37, 0], [2, "818", 486, 621, 30, 37, 2], [2, "818", 516, 636, 30, 37, 2], [2, "818", 546, 651, 30, 37, 2], [2, "818", 576, 666, 30, 37, 2], [2, "818", 636, 696, 30, 37, 2], [2, "818", 606, 681, 30, 37, 2], [2, "818", 666, 711, 30, 37, 2], [2, "818", 637, 434, 30, 37, 0], [2, "818", 607, 449, 30, 37, 0], [2, "818", 548, 479, 30, 37, 0], [2, "818", 578, 464, 30, 37, 0], [2, "818", 518, 494, 30, 37, 0], [2, "818", 488, 509, 30, 37, 0], [2, "818", 459, 524, 30, 37, 0], [2, "818", 429, 539, 30, 37, 0], [2, "818", 714, 395, 30, 37, 0], [2, "818", 684, 410, 30, 37, 0], [2, "818", 655, 425, 30, 37, 0], [2, "1483", 473, 112, 22, 38, 0], [2, "1483", 495, 123, 22, 38, 0], [2, "1483", 538, 145, 22, 38, 0], [2, "1483", 516, 134, 22, 38, 0], [2, "1483", 625, 189, 22, 38, 0], [2, "1483", 603, 178, 22, 38, 0], [2, "1483", 582, 167, 22, 38, 0], [2, "1483", 560, 156, 22, 38, 0], [2, "1483", 647, 200, 22, 38, 0], [2, "1483", 669, 211, 22, 38, 0], [2, "1483", 712, 233, 22, 38, 0], [2, "1483", 690, 222, 22, 38, 0], [2, "1483", 799, 277, 22, 38, 0], [2, "1483", 777, 266, 22, 38, 0], [2, "1483", 756, 255, 22, 38, 0], [2, "1483", 734, 244, 22, 38, 0], [2, "1483", 821, 288, 22, 38, 0], [2, "1483", 385, 85, 22, 38, 2], [2, "1483", 363, 96, 22, 38, 2], [2, "1483", 319, 118, 22, 38, 2], [2, "1483", 341, 107, 22, 38, 2], [2, "1483", 298, 129, 22, 38, 2], [2, "1483", 276, 140, 22, 38, 2], [2, "1483", 254, 151, 22, 38, 2], [2, "1483", 232, 162, 22, 38, 2], [2, "1483", 210, 173, 22, 38, 2], [2, "1483", 188, 184, 22, 38, 2], [2, "1483", 144, 206, 22, 38, 2], [2, "1483", 166, 195, 22, 38, 2], [2, "1483", 123, 217, 22, 38, 2], [2, "1483", 101, 228, 22, 38, 2], [2, "1483", 79, 239, 22, 38, 2], [2, "1483", 57, 250, 22, 38, 2], [2, "1483", 36, 261, 22, 38, 2], [2, "1483", 14, 272, 22, 38, 2], [2, "1483", -8, 283, 22, 38, 2], [2, "1483", 721, 369, 22, 38, 2], [2, "1483", 699, 380, 22, 38, 2], [2, "1483", 677, 391, 22, 38, 2], [2, "1483", 655, 402, 22, 38, 2], [2, "1483", 611, 424, 22, 38, 2], [2, "1483", 633, 413, 22, 38, 2], [2, "1483", 590, 435, 22, 38, 2], [2, "1483", 568, 446, 22, 38, 2], [2, "1483", 546, 457, 22, 38, 2], [2, "1483", 524, 468, 22, 38, 2], [2, "1483", 503, 479, 22, 38, 2], [2, "1483", 481, 490, 22, 38, 2], [2, "1483", 459, 501, 22, 38, 2], [2, "1483", 438, 512, 22, 38, 2], [2, "1483", 428, 517, 22, 38, 2], [2, "1483", 100, 361, 22, 38, 0], [2, "1483", 122, 372, 22, 38, 0], [2, "1483", 165, 394, 22, 38, 0], [2, "1483", 143, 383, 22, 38, 0], [2, "1483", 252, 438, 22, 38, 0], [2, "1483", 230, 427, 22, 38, 0], [2, "1483", 209, 416, 22, 38, 0], [2, "1483", 187, 405, 22, 38, 0], [2, "1483", 274, 449, 22, 38, 0], [2, "1483", 296, 460, 22, 38, 0], [2, "1483", 384, 504, 22, 38, 0], [2, "1483", 362, 493, 22, 38, 0], [2, "1483", 340, 482, 22, 38, 0], [2, "1483", 318, 471, 22, 38, 0], [2, "1483", 406, 516, 22, 38, 0], [2, "1483", 486, 594, 22, 38, 0], [2, "1483", 508, 605, 22, 38, 0], [2, "1483", 551, 627, 22, 38, 0], [2, "1483", 529, 616, 22, 38, 0], [2, "1483", 638, 671, 22, 38, 0], [2, "1483", 616, 660, 22, 38, 0], [2, "1483", 595, 649, 22, 38, 0], [2, "1483", 573, 638, 22, 38, 0], [2, "1483", 660, 682, 22, 38, 0], [2, "1483", 682, 693, 22, 38, 0], [2, "1483", 704, 704, 22, 38, 0], [2, "1483", 726, 715, 22, 38, 0], [2, "1483", 391, 617, 22, 38, 2], [2, "1483", 369, 628, 22, 38, 2], [2, "1483", 347, 639, 22, 38, 2], [2, "1483", 326, 650, 22, 38, 2], [2, "1483", 304, 661, 22, 38, 2], [2, "1483", 282, 672, 22, 38, 2], [2, "1483", 260, 683, 22, 38, 2], [2, "1483", 238, 694, 22, 38, 2], [2, "1483", 216, 705, 22, 38, 2], [2, "586", 611, 171, 40, 22, 2], [2, "385", 771, 337, 72, 48, 0], [2, "385", -10, 322, 72, 48, 0], [2, "385", 500, 41, 72, 48, 2], [2, "1358", 278, 23, 62, 34, 0], [2, "1358", 329, 49, 62, 34, 0], [2, "1358", 345, 76, 62, 34, 2], [2, "1358", 293, 103, 62, 34, 2], [2, "1358", 191, 155, 62, 34, 2], [2, "1358", 138, 181, 62, 34, 2], [2, "1358", 86, 207, 62, 34, 2], [2, "1358", 35, 233, 62, 34, 2], [2, "1358", -16, 259, 62, 34, 2], [2, "1358", 198, 284, 62, 34, 2], [2, "1358", 148, 309, 62, 34, 2], [2, "1358", 99, 334, 62, 34, 2], [2, "1358", 115, 361, 62, 34, 0], [2, "1358", 166, 388, 62, 34, 0], [2, "1358", 218, 414, 62, 34, 0], [2, "1358", 270, 440, 62, 34, 0], [2, "1358", 349, 208, 62, 34, 2], [2, "1358", 299, 233, 62, 34, 2], [2, "1358", 250, 258, 62, 34, 2], [2, "1358", 399, 200, 62, 34, 0], [2, "1358", 661, 371, 62, 34, 2], [2, "1358", 611, 396, 62, 34, 2], [2, "1358", 562, 421, 62, 34, 2], [2, "1358", 510, 447, 62, 34, 2], [2, "1358", 460, 472, 62, 34, 2], [2, "1358", 411, 497, 62, 34, 2], [2, "1358", 322, 467, 62, 34, 0], [2, "1358", 374, 494, 62, 34, 0], [2, "1358", 448, 225, 62, 34, 0], [2, "1358", 499, 252, 62, 34, 0], [2, "1358", 551, 279, 62, 34, 0], [2, "1358", 603, 306, 62, 34, 0], [2, "1358", 639, 325, 62, 34, 0], [2, "1358", 678, 344, 62, 34, 0], [2, "1358", 585, 164, 62, 34, 0], [2, "1358", 634, 189, 62, 34, 0], [2, "1358", 685, 216, 62, 34, 0], [2, "1358", 737, 243, 62, 34, 0], [2, "1358", 789, 270, 62, 34, 0], [2, "1358", 487, 113, 62, 34, 0], [2, "1358", 536, 138, 62, 34, 0], [2, "1358", 473, 86, 62, 34, 2], [2, "1358", 527, 61, 62, 34, 2], [2, "1358", 580, 35, 62, 34, 2], [2, "1358", -12, 403, 62, 34, 0], [2, "1358", 39, 430, 62, 34, 0], [2, "1358", 91, 457, 62, 34, 0], [2, "1358", 143, 484, 62, 34, 0], [2, "1358", 195, 511, 62, 34, 0], [2, "1358", 247, 538, 62, 34, 0], [2, "1358", 298, 564, 62, 34, 0], [2, "1358", 349, 591, 62, 34, 0], [2, "1358", 500, 558, 62, 34, 2], [2, "1358", 549, 533, 62, 34, 2], [2, "1358", 599, 508, 62, 34, 2], [2, "1358", 651, 482, 62, 34, 2], [2, "1358", 700, 457, 62, 34, 2], [2, "1358", 750, 432, 62, 34, 2], [2, "1358", 803, 405, 62, 34, 2], [2, "1358", 187, 692, 62, 34, 2], [2, "1358", 236, 667, 62, 34, 2], [2, "1358", 286, 642, 62, 34, 2], [2, "1358", 338, 616, 62, 34, 2], [2, "1358", 588, 639, 62, 34, 0], [2, "1358", 537, 612, 62, 34, 0], [2, "1358", 486, 586, 62, 34, 0], [2, "1358", 691, 691, 62, 34, 0], [2, "1358", 640, 665, 62, 34, 0], [2, "1358", 176, -28, 62, 34, 0], [2, "1358", 227, -2, 62, 34, 0], [2, "1358", 685, -18, 62, 34, 2], [2, "1358", 632, 8, 62, 34, 2], [2, "1469", 387, 217, 40, 22, 0], [2, "1358", 242, 129, 62, 34, 2], [2, "1469", 361, 230, 40, 22, 0], [2, "1469", 335, 243, 40, 22, 0], [2, "1469", 309, 256, 40, 22, 0], [2, "1469", 283, 270, 40, 22, 0], [2, "1469", 257, 283, 40, 22, 0], [2, "1469", 231, 296, 40, 22, 0], [2, "1469", 153, 335, 40, 22, 0], [2, "1469", 179, 322, 40, 22, 0], [2, "1469", 205, 309, 40, 22, 0], [2, "1469", 130, 347, 40, 22, 0], [2, "1469", 668, 362, 40, 22, 0], [2, "1469", 642, 375, 40, 22, 0], [2, "1469", 616, 388, 40, 22, 0], [2, "1469", 590, 401, 40, 22, 0], [2, "1469", 564, 415, 40, 22, 0], [2, "1469", 512, 441, 40, 22, 0], [2, "1469", 538, 428, 40, 22, 0], [2, "1469", 486, 454, 40, 22, 0], [2, "1469", 460, 467, 40, 22, 0], [2, "1469", 411, 492, 40, 22, 0], [2, "1469", 434, 480, 40, 22, 0], [2, "1469", 145, 361, 40, 22, 2], [2, "1469", 171, 374, 40, 22, 2], [2, "1469", 197, 387, 40, 22, 2], [2, "1469", 223, 400, 40, 22, 2], [2, "1469", 249, 413, 40, 22, 2], [2, "1469", 275, 426, 40, 22, 2], [2, "1469", 301, 440, 40, 22, 2], [2, "1469", 327, 453, 40, 22, 2], [2, "1469", 376, 478, 40, 22, 2], [2, "1469", 402, 491, 40, 22, 2], [2, "1469", 350, 465, 40, 22, 2], [2, "1469", 402, 219, 40, 22, 2], [2, "1469", 428, 232, 40, 22, 2], [2, "1469", 480, 258, 40, 22, 2], [2, "1469", 454, 245, 40, 22, 2], [2, "1469", 506, 271, 40, 22, 2], [2, "1469", 532, 284, 40, 22, 2], [2, "1469", 558, 298, 40, 22, 2], [2, "1469", 584, 311, 40, 22, 2], [2, "1469", 607, 323, 40, 22, 2], [2, "1469", 633, 336, 40, 22, 2], [2, "1469", 659, 349, 40, 22, 2], [2, "930", 502, 96, 42, 22, 2], [2, "930", 536, 80, 42, 22, 2], [2, "930", 606, 46, 42, 22, 2], [2, "930", 572, 63, 42, 22, 2], [2, "930", 640, 28, 42, 22, 2], [2, "930", 674, 12, 42, 22, 2], [2, "930", 710, -5, 42, 22, 2], [2, "930", 510, 113, 42, 22, 0], [2, "930", 545, 131, 42, 22, 0], [2, "930", 612, 166, 42, 22, 0], [2, "930", 577, 148, 42, 22, 0], [2, "930", 644, 184, 42, 22, 0], [2, "930", 679, 202, 42, 22, 0], [2, "930", 711, 219, 42, 22, 0], [2, "930", 746, 237, 42, 22, 0], [2, "930", 780, 255, 42, 22, 0], [2, "930", 812, 271, 42, 22, 0], [2, "930", 203, -2, 42, 22, 0], [2, "930", 238, 15, 42, 22, 0], [2, "930", 270, 33, 42, 22, 0], [2, "930", 305, 51, 42, 22, 0], [2, "930", 337, 68, 42, 22, 0], [2, "930", 118, 190, 42, 22, 2], [2, "930", 152, 173, 42, 22, 2], [2, "930", 188, 155, 42, 22, 2], [2, "930", 222, 137, 42, 22, 2], [2, "930", 256, 120, 42, 22, 2], [2, "930", 290, 102, 42, 22, 2], [2, "930", 326, 85, 42, 22, 2], [2, "930", -20, 261, 42, 22, 2], [2, "930", 16, 243, 42, 22, 2], [2, "930", 50, 225, 42, 22, 2], [2, "930", 84, 208, 42, 22, 2], [2, "930", 339, 601, 42, 22, 0], [2, "930", 306, 583, 42, 22, 0], [2, "930", 272, 565, 42, 22, 0], [2, "930", 237, 547, 42, 22, 0], [2, "930", 102, 476, 42, 22, 0], [2, "930", 137, 494, 42, 22, 0], [2, "930", 171, 512, 42, 22, 0], [2, "930", 204, 530, 42, 22, 0], [2, "930", -33, 406, 42, 22, 0], [2, "930", 2, 424, 42, 22, 0], [2, "930", 36, 442, 42, 22, 0], [2, "930", 69, 460, 42, 22, 0], [2, "930", 332, 620, 42, 22, 2], [2, "930", 296, 638, 42, 22, 2], [2, "930", 264, 655, 42, 22, 2], [2, "930", 228, 673, 42, 22, 2], [2, "930", 194, 690, 42, 22, 2], [2, "930", 158, 708, 42, 22, 2], [2, "930", 713, 694, 42, 22, 0], [2, "930", 681, 678, 42, 22, 0], [2, "930", 647, 660, 42, 22, 0], [2, "930", 612, 642, 42, 22, 0], [2, "930", 580, 625, 42, 22, 0], [2, "930", 545, 607, 42, 22, 0], [2, "930", 513, 589, 42, 22, 0], [2, "930", 748, 711, 42, 22, 0], [2, "930", 522, 571, 42, 22, 2], [2, "930", 556, 554, 42, 22, 2], [2, "930", 590, 537, 42, 22, 2], [2, "930", 624, 520, 42, 22, 2], [2, "930", 657, 501, 42, 22, 2], [2, "930", 691, 484, 42, 22, 2], [2, "930", 725, 467, 42, 22, 2], [2, "930", 759, 450, 42, 22, 2], [2, "930", 791, 433, 42, 22, 2], [2, "930", 825, 416, 42, 22, 2], [2, "984", 351, 276, 76, 52, 0], [2, "984", 427, 276, 76, 52, 2], [2, "984", 351, 328, 76, 52, 1], [2, "984", 427, 328, 76, 52, 3], [2, "657", 438, 363, 48, 27, 0], [2, "655", 416, 374, 22, 16, 0], [2, "659", 486, 341, 30, 41, 0], [2, "661", 517, 328, 30, 41, 0], [2, "656", 417, 389, 22, 22, 0], [2, "658", 439, 381, 48, 29, 0], [2, "660", 487, 368, 30, 33, 0], [2, "662", 518, 338, 30, 49, 0], [2, "657", 368, 363, 48, 27, 2], [2, "659", 337, 341, 30, 41, 2], [2, "661", 307, 328, 30, 41, 2], [2, "661", 307, 287, 30, 41, 3], [2, "659", 337, 274, 30, 41, 3], [2, "657", 368, 266, 48, 27, 3], [2, "655", 416, 265, 22, 16, 2], [2, "657", 438, 266, 48, 27, 1], [2, "659", 486, 274, 30, 41, 1], [2, "661", 517, 287, 30, 41, 1], [2, "658", 369, 382, 48, 29, 2], [2, "660", 339, 369, 30, 33, 2], [2, "662", 309, 340, 30, 49, 2], [2, "43_1", 294, 356, 82, 58, 0], [2, "43_1", 489, 354, 82, 58, 2], [2, "269", 545, 418, 110, 58, 2], [2, "269", 587, 439, 110, 58, 2], [2, "269", 629, 460, 110, 58, 2], [2, "272", 614, 398, 72, 54, 2], [2, "271", 676, 426, 64, 50, 2], [2, "269", 194, 419, 110, 58, 0], [2, "269", 153, 440, 110, 58, 0], [2, "269", 111, 461, 110, 58, 0], [2, "272", 158, 400, 72, 54, 0], [2, "271", 106, 428, 64, 50, 0], [2, "272", 575, 185, 72, 54, 0], [2, "271", 523, 213, 64, 50, 0], [2, "269", 615, 201, 110, 58, 0], [2, "269", 574, 222, 110, 58, 0], [2, "269", 532, 243, 110, 58, 0], [2, "269", 130, 188, 110, 58, 2], [2, "269", 172, 209, 110, 58, 2], [2, "269", 214, 230, 110, 58, 2], [2, "272", 204, 171, 72, 54, 2], [2, "271", 266, 199, 64, 50, 2], [2, "965", 502, 92, 40, 33, 0], [2, "965", 601, 43, 40, 33, 0], [2, "967", 609, 12, 24, 41, 0], [2, "968", 609, -9, 24, 38, 0], [2, "938", 809, 0, 38, 22, 2], [2, "938", 781, 14, 38, 22, 2], [2, "938", 752, 29, 38, 22, 2], [2, "938", 724, 43, 38, 22, 2], [2, "938", 695, 57, 38, 22, 2], [2, "938", 667, 71, 38, 22, 2], [2, "938", 638, 86, 38, 22, 2], [2, "938", 648, 101, 38, 22, 0], [2, "938", 678, 116, 38, 22, 0], [2, "938", 707, 131, 38, 22, 0], [2, "938", 737, 146, 38, 22, 0], [2, "938", 766, 161, 38, 22, 0], [2, "938", 796, 176, 38, 22, 0], [2, "938", 825, 191, 38, 22, 0], [2, "938", -16, 501, 38, 22, 0], [2, "938", 14, 516, 38, 22, 0], [2, "938", 43, 531, 38, 22, 0], [2, "938", 73, 546, 38, 22, 0], [2, "938", 102, 561, 38, 22, 0], [2, "938", 132, 576, 38, 22, 0], [2, "938", 161, 591, 38, 22, 0], [2, "938", 171, 606, 38, 22, 2], [2, "938", 143, 621, 38, 22, 2], [2, "938", 113, 636, 38, 22, 2], [2, "938", 85, 651, 38, 22, 2], [2, "938", 56, 666, 38, 22, 2], [2, "938", 26, 681, 38, 22, 2], [2, "938", -2, 696, 38, 22, 2], [2, "938", -31, 711, 38, 22, 2], [2, "938", 678, 592, 38, 22, 0], [2, "938", 708, 607, 38, 22, 0], [2, "938", 737, 622, 38, 22, 0], [2, "938", 767, 637, 38, 22, 0], [2, "938", 796, 652, 38, 22, 0], [2, "938", 826, 667, 38, 22, 0], [2, "938", 834, 502, 38, 22, 2], [2, "938", 806, 517, 38, 22, 2], [2, "938", 776, 532, 38, 22, 2], [2, "938", 748, 547, 38, 22, 2], [2, "938", 719, 562, 38, 22, 2], [2, "938", 689, 577, 38, 22, 2], [2, "938", 152, 39, 38, 22, 0], [2, "938", 181, 54, 38, 22, 0], [2, "938", 191, 69, 38, 22, 2], [2, "938", 163, 84, 38, 22, 2], [2, "938", 133, 99, 38, 22, 2], [2, "938", 105, 114, 38, 22, 2], [2, "938", 76, 129, 38, 22, 2], [2, "938", 46, 144, 38, 22, 2], [2, "938", 18, 159, 38, 22, 2], [2, "938", -11, 174, 38, 22, 2], [2, "938", 63, -6, 38, 22, 0], [2, "938", 93, 9, 38, 22, 0], [2, "938", 122, 24, 38, 22, 0], [2, "969", 293, 47, 36, 30, 0], [2, "969", 307, 60, 36, 30, 0], [2, "1236", 597, 23, 42, 43, 0], [2, "219_1", 629, 89, 36, 30, 0], [2, "220_1", 340, 56, 40, 29, 0], [2, "219_1", 270, 11, 36, 30, 0], [2, "219_1", 32, 433, 36, 30, 0], [2, "219_1", -12, 411, 36, 30, 0], [2, "385", 405, 561, 72, 48, 0], [2, "385", 506, 670, 72, 48, 2], [2, "385", 335, -17, 72, 48, 2], [2, "385", 399, 167, 72, 48, 0], [2, "1236", 456, 373, 42, 43, 0], [2, "1371_2", 353, 584, 42, 35, 0], [2, "219_3", 292, 569, 36, 30, 0], [2, "219_1", 81, 651, 36, 30, 2], [2, "219_1", 669, 585, 36, 30, 0], [2, "220_3", 525, 563, 40, 29, 0], [2, "1368_2", 534, 533, 50, 42, 0], [2, "219_3", 369, 387, 36, 30, 0], [2, "1456", 330, 38, 24, 32, 0], [2, "1457", 230, -2, 22, 30, 0], [2, "1457", 652, 92, 22, 30, 0], [2, "1457", 386, 485, 22, 30, 0], [2, "1469", 512, 441, 40, 22, 0], [2, "1456", 357, 385, 24, 32, 0], [2, "219_3", 324, 579, 36, 30, 0], [2, "219_3", 302, 584, 36, 30, 0], [2, "219_3", 277, 647, 36, 30, 2], [2, "1457", 314, 574, 22, 30, 0], [2, "1456", 323, 579, 24, 32, 0], [2, "965", 693, 6, 40, 33, 0], [2, "965", 770, 47, 40, 33, 0], [2, "969", 668, 43, 36, 30, 2], [2, "969", 775, 145, 36, 30, 0], [2, "969", 787, 157, 36, 30, 0], [2, "969", 797, 215, 36, 30, 2], [2, "967", 701, -25, 24, 41, 0], [2, "967", 778, 16, 24, 41, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 30, 30, 30, 30, 30, 27, -1, 42, 42, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, 30, 30, 30, 30, 30, 39, 39, 39, 39, 38, -1, -1, -1, -1, -1, -1, -1, 35, 42, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 27, 26, -1, 7, 6, 5, 5, 30, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 39, 38, -1, -1, -1, 7, 6, 5, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, 39, 25, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, -1, -1, -1, -1, -1, -1, 30, -1, 36, 37, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 33, 33, 33, 33, 33, 33, 39, 33, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, -1, -1, -1, -1, -1, -1, 7, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [67, 67, 67, 67, 67, 66, 65, 65, 66, 65, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 66, 65, 66, 66, 65, 67, 67, 68, 67, 67, 67, 67, 67, 66, 65, 65, 65, 66, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 44, 66, 65, 66, 65, 66, 67, 68, 67, 67, 68, 67, 68, 67, 68, 67, 67, 67, 66, 65, 66, 65, 66, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 66, 65, 66, 65, 66, 67, 68, 67, 67, 68, 67, 68, 67, 68, 68, 67, 67, 67, 67, 65, 65, 66, 65, 66, 66, 66, 46, 46, 46, 46, 46, 46, 46, 66, 65, 66, 66, 65, 67, 68, 68, 68, 68, 67, 68, 67, 68, 67, 68, 67, 68, 67, 65, 66, 65, 65, 66, 65, 66, 65, 47, 47, 47, 47, 47, 47, 65, 65, 66, 65, 66, 66, 67, 68, 68, 68, 68, 68, 67, 68, 67, 68, 68, 67, 67, 65, 66, 65, 66, 65, 65, 66, 65, 48, 48, 48, 48, 48, 48, 48, 48, 65, 65, 65, 65, 65, 66, 66, 66, 67, 68, 67, 68, 67, 68, 67, 67, 67, 66, 66, 66, 65, 65, 65, 66, 65, 66, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 65, 66, 66, 65, 66, 66, 65, 67, 68, 67, 68, 67, 65, 66, 66, 66, 65, 65, 65, 66, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 66, 66, 65, 66, 66, 66, 65, 67, 68, 65, 65, 66, 65, 66, 66, 65, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 51, 65, 66, 66, 66, 66, 65, 65, 66, 65, 66, 65, 66, 65, 52, 52, 52, 52, 54, 52, 52, 52, 52, 52, 52, 67, 67, 52, 52, 52, 52, 52, 52, 54, 54, 54, 54, 52, 65, 66, 66, 65, 66, 66, 65, 65, 65, 52, 52, 52, 52, 54, 52, 52, 52, 52, 52, 52, 67, 68, 67, 68, 67, 68, 52, 52, 52, 52, 52, 52, 54, 52, 54, 54, 52, 65, 66, 66, 66, 65, 52, 52, 52, 52, 52, 54, 54, 52, 52, 52, 52, 67, 68, 67, 67, 67, 68, 68, 67, 67, 68, 52, 52, 52, 52, 52, 54, 54, 54, 54, 52, 52, 65, 66, 52, 52, 52, 52, 54, 54, 54, 52, 52, 52, 67, 68, 67, 68, 68, 67, 68, 68, 67, 67, 67, 67, 67, 68, 52, 52, 52, 52, 54, 54, 54, 54, 54, 52, 52, 52, 52, 52, 54, 54, 54, 54, 52, 67, 67, 68, 67, 68, 68, 68, 67, 68, 68, 67, 68, 67, 67, 67, 67, 68, 67, 52, 52, 54, 54, 54, 54, 54, 54, 54, 52, 52, 54, 54, 54, 52, 68, 67, 67, 67, 68, 67, 68, 68, 68, 67, 67, 68, 67, 67, 67, 68, 67, 68, 68, 67, 68, 67, 52, 54, 52, 52, 52, 54, 54, 54, 54, 54, 54, 52, 67, 67, 68, 67, 68, 67, 68, 67, 68, 67, 67, 68, 67, 67, 68, 67, 68, 67, 67, 68, 67, 68, 67, 68, 65, 52, 52, 52, 54, 54, 54, 54, 52, 52, 52, 52, 52, 67, 67, 67, 67, 68, 68, 67, 68, 67, 68, 67, 67, 67, 68, 67, 67, 67, 67, 68, 67, 68, 67, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 67, 68, 68, 67, 68, 67, 68, 67, 68, 67, 67, 68, 67, 68, 67, 68, 68, 67, 52, 52, 52, 52, 52, 52, 52, 52, 65, 66, 52, 52, 52, 52, 52, 52, 52, 52, 52, 67, 68, 67, 68, 67, 68, 67, 68, 67, 67, 68, 67, 68, 67, 52, 52, 52, 52, 52, 52, 52, 52, 66, 66, 65, 66, 65, 66, 52, 52, 52, 52, 52, 52, 52, 52, 54, 67, 68, 68, 67, 68, 67, 67, 67, 68, 65, 66, 52, 52, 52, 52, 52, 52, 52, 66, 66, 66, 66, 65, 65, 66, 65, 66, 66, 52, 52, 52, 52, 52, 52, 52, 54, 54, 67, 68, 67, 68, 68, 65, 66, 54, 52, 52, 52, 52, 52, 52, 65, 66, 65, 66, 66, 65, 67, 66, 66, 65, 66, 65, 65, 65, 52, 52, 52, 52, 52, 52, 52, 54, 54, 67, 68, 54, 54, 52, 52, 52, 52, 52, 52, 66, 66, 65, 66, 66, 65, 66, 65, 67, 67, 68, 65, 66, 65, 66, 65, 66, 65, 52, 52, 52, 52, 52, 52, 54, 54, 54, 52, 52, 52, 52, 52, 52, 66, 65, 65, 65, 66, 65, 66, 65, 67, 68, 67, 67, 68, 67, 68, 65, 66, 65, 66, 65, 65, 66, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 66, 65, 66, 66, 65, 66, 65, 66, 67, 68, 68, 68, 67, 68, 68, 67, 68, 67, 68, 65, 66, 65, 66, 66, 65, 66, 52, 52, 52, 52, 52, 52, 52, 66, 66, 65, 66, 65, 66, 66, 65, 67, 68, 68, 68, 67, 68, 67, 68, 67, 67, 67, 67, 67, 68, 65, 65, 66, 65, 66, 65, 66, 65, 52, 52, 52, 52, 52, 52, 65, 65, 66, 65, 66, 66, 66, 67, 68, 67, 68, 68, 67, 67, 67, 68, 68, 68, 67, 67, 65, 66, 65, 66, 65, 66, 65, 66, 52, 52, 52, 52, 52, 52, 52, 52, 65, 66, 65, 66, 66, 66, 65, 65, 67, 68, 67, 68, 67, 67, 68, 67, 67, 65, 65, 65, 66, 65, 66, 65, 66, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 65, 66, 65, 66, 65, 66, 65, 66, 67, 68, 67, 67, 67, 66, 65, 65, 66, 65, 66, 65, 66, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 65, 66, 65, 66, 65, 66, 66, 66, 67, 66, 65, 65, 66, 65, 66, 65, 66, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 65, 66, 65, 66, 66, 65]}], "blocks": [1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]}