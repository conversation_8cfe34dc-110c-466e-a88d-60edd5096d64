{"mW": 1128, "mH": 1128, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["3395", 0, 3, 2], ["3395", 2, 3, 2], ["3395", 1, 3, 2], ["3395", 3, 3, 2], ["1331", 0, 3, 3], ["154", 0, 2, 1]], "layers": [{"type": 3, "obj": [[2, "458", 1012, 608, 54, 55, 0], [2, "3410", 1028, 582, 54, 74, 0], [2, "3354", 780, -11, 78, 69, 2], [2, "458", -22, 362, 54, 55, 2], [2, "3402", 510, 742, 44, 80, 0], [2, "3402", 567, 716, 44, 80, 0], [2, "894", 579, 748, 24, 20, 2], [2, "3401", 472, 738, 44, 81, 0], [2, "894", 487, 771, 24, 20, 0], [2, "458", 537, 671, 54, 55, 0], [2, "458", 541, 678, 54, 55, 0], [2, "458", 531, 684, 54, 55, 0], [2, "1401", 689, 941, 62, 92, 0], [2, "1401", 663, 953, 62, 92, 0], [2, "1401", 638, 965, 62, 92, 0], [2, "1401", 612, 977, 62, 92, 0], [2, "1401", 587, 989, 62, 92, 0], [2, "1401", 561, 1001, 62, 92, 0], [2, "1401", 536, 1015, 62, 92, 0], [2, "1402", 1609, 796, 14, 29, 0], [2, "1402", 521, 1028, 14, 29, 0], [2, "1402", 511, 1044, 14, 29, 0], [2, "1402", 505, 1055, 14, 29, 0], [2, "3401", 337, 670, 44, 81, 0], [2, "3401", 382, 693, 44, 81, 0], [2, "3401", 427, 715, 44, 81, 0], [2, "1207", 326, 656, 22, 81, 0], [2, "1207", 594, 703, 22, 81, 2], [2, "3381", 391, 764, 70, 57, 2], [2, "3381", 528, 782, 70, 57, 0], [2, "1208", 344, 662, 52, 56, 0], [2, "1208", 392, 685, 52, 56, 0], [2, "1208", 438, 708, 52, 56, 0], [2, "1208", 463, 721, 52, 56, 0], [2, "1208", 551, 707, 52, 56, 2], [2, "1208", 513, 725, 52, 56, 2], [2, "1207", 503, 743, 22, 81, 0], [2, "3406", 322, 580, 62, 92, 2], [2, "3406", 348, 592, 62, 92, 2], [2, "3406", 374, 604, 62, 92, 2], [2, "3406", 400, 616, 62, 92, 2], [2, "3406", 425, 628, 62, 92, 2], [2, "3406", 451, 640, 62, 92, 2], [2, "3406", 475, 653, 62, 92, 2], [2, "3406", 501, 665, 62, 92, 2], [2, "3407", 589, 685, 14, 29, 2], [2, "3407", 577, 663, 14, 29, 2], [2, "3407", 600, 703, 14, 29, 2], [2, "1412", 358, 565, 32, 28, 2], [2, "1412", 377, 576, 32, 28, 2], [2, "1412", 394, 584, 32, 28, 2], [2, "1412", 411, 593, 32, 28, 2], [2, "1412", 431, 602, 32, 28, 2], [2, "1412", 431, 602, 32, 28, 2], [2, "1412", 447, 612, 32, 28, 2], [2, "1412", 447, 612, 32, 28, 2], [2, "1412", 464, 617, 32, 28, 2], [2, "1412", 481, 626, 32, 28, 2], [2, "1412", 498, 631, 32, 28, 2], [2, "1412", 511, 639, 32, 28, 2], [2, "1412", 511, 639, 32, 28, 2], [2, "1412", 531, 647, 32, 28, 2], [2, "1412", 531, 647, 32, 28, 2], [2, "1412", 545, 652, 32, 28, 0], [2, "3405", 370, 517, 44, 94, 2], [2, "3405", 517, 584, 44, 94, 2], [2, "894", 387, 726, 24, 20, 0], [2, "894", 347, 708, 24, 20, 0], [2, "894", 438, 750, 24, 20, 0], [2, "1279", 355, 687, 42, 58, 0], [2, "1279", 405, 711, 42, 58, 0], [2, "1279", 456, 737, 42, 58, 0], [2, "894", 525, 772, 24, 20, 2], [2, "1281", 597, 697, 26, 41, 2], [2, "1279", 543, 736, 42, 58, 2], [2, "3383", 1069, 1046, 68, 114, 0], [2, "3383", 926, 1090, 68, 114, 0], [2, "1228", 70, 829, 60, 75, 2], [2, "1228", 47, 841, 60, 75, 2], [2, "1228", 24, 853, 60, 75, 2], [2, "1228", 1, 864, 60, 75, 2], [2, "1228", -22, 876, 60, 75, 2], [2, "1228", -45, 888, 60, 75, 2], [2, "1225_1", 47, 810, 48, 44, 2], [2, "1226_1", -13, 832, 70, 47, 2], [2, "1226_1", -13, 832, 70, 47, 2], [2, "1226_1", -68, 857, 70, 47, 2], [2, "3404", 532, 789, 10, 11, 0], [2, "3404", 527, 799, 10, 11, 0], [2, "3403", 385, 743, 12, 12, 0], [2, "3403", 491, 795, 12, 12, 0], [2, "1200", 73, 856, 24, 32, 0], [2, "1200", 73, 858, 24, 32, 0], [2, "1200", 8, 890, 24, 32, 0], [2, "1412", 52, 807, 32, 28, 0], [2, "1412", 33, 815, 32, 28, 0], [2, "1412", 13, 821, 32, 28, 0], [2, "1412", -6, 833, 32, 28, 0], [2, "1412", -29, 845, 32, 28, 0], [2, "1412", -50, 854, 32, 28, 0], [2, "1416", 44, 899, 24, 17, 0], [2, "1413", -7, 934, 20, 13, 0], [2, "1222", 837, 505, 64, 80, 2], [2, "1222", 860, 518, 64, 80, 2], [2, "1222", 881, 529, 64, 80, 2], [2, "1222", 904, 542, 64, 80, 2], [2, "1222", 928, 553, 64, 80, 2], [2, "1222", 951, 566, 64, 80, 2], [2, "1222", 964, 570, 64, 80, 2], [2, "1222", 987, 583, 64, 80, 2], [2, "1222", 928, 553, 64, 80, 2], [2, "1222", 951, 566, 64, 80, 2], [2, "1225", 875, 484, 48, 44, 0], [2, "1225", 873, 481, 48, 44, 0], [2, "1225", 914, 508, 48, 44, 0], [2, "1225", 956, 529, 48, 44, 0], [2, "1225", 995, 550, 48, 44, 0], [2, "1226", 1000, 553, 70, 47, 0], [2, "1214", 813, 565, 34, 55, 0], [2, "1412", 877, 479, 32, 28, 2], [2, "1412", 897, 488, 32, 28, 2], [2, "1412", 913, 498, 32, 28, 2], [2, "1412", 930, 503, 32, 28, 2], [2, "1412", 947, 512, 32, 28, 2], [2, "1412", 958, 520, 32, 28, 2], [2, "1412", 978, 529, 32, 28, 2], [2, "1412", 994, 539, 32, 28, 2], [2, "1412", 1011, 544, 32, 28, 2], [2, "1412", 1028, 553, 32, 28, 2], [2, "3394", 401, 667, 54, 41, 2], [2, "1414", 854, 564, 46, 30, 2], [2, "1413", 884, 587, 20, 13, 2], [2, "1413", 933, 611, 20, 13, 2], [2, "1413", 989, 641, 20, 13, 2], [2, "1413", 1078, 595, 20, 13, 2], [2, "1413", 824, 564, 20, 13, 0], [2, "1413", 814, 568, 20, 13, 0], [2, "1413", 884, 520, 20, 13, 0], [2, "1413", 894, 516, 20, 13, 0], [2, "1413", 987, 571, 20, 13, 0], [2, "1413", 997, 567, 20, 13, 0], [2, "1395", 550, 1029, 4, 5, 0], [2, "1395", 550, 1029, 4, 5, 0], [2, "1395", 558, 1048, 4, 5, 0], [2, "1395", 565, 1066, 4, 5, 0], [2, "1395", 574, 1084, 4, 5, 0], [2, "1395", 738, 1006, 4, 5, 0], [2, "1395", 738, 1006, 4, 5, 0], [2, "1395", 730, 988, 4, 5, 0], [2, "1395", 724, 970, 4, 5, 0], [2, "1395", 713, 954, 4, 5, 0], [2, "1395", 544, 684, 4, 5, 0], [2, "1395", 544, 684, 4, 5, 0], [2, "1395", 534, 702, 4, 5, 0], [2, "1395", 534, 702, 4, 5, 0], [2, "1395", 527, 721, 4, 5, 0], [2, "1395", 527, 721, 4, 5, 0], [2, "1395", 519, 741, 4, 5, 0], [2, "1395", 519, 741, 4, 5, 0], [2, "1395", 351, 590, 4, 5, 0], [2, "1395", 351, 590, 4, 5, 0], [2, "1395", 343, 608, 4, 5, 0], [2, "1395", 343, 608, 4, 5, 0], [2, "1395", 336, 627, 4, 5, 0], [2, "1395", 336, 627, 4, 5, 0], [2, "1395", 329, 646, 4, 5, 0], [2, "1393", 384, 661, 18, 25, 0], [2, "1393", 451, 687, 18, 25, 2], [2, "1394", 351, 651, 10, 12, 0], [2, "1394", 485, 713, 10, 12, 0], [2, "1395", 369, 664, 4, 5, 0], [2, "1395", 369, 664, 4, 5, 0], [2, "1395", 340, 649, 4, 5, 0], [2, "1395", 340, 649, 4, 5, 0], [2, "1395", 468, 711, 4, 5, 0], [2, "1395", 468, 711, 4, 5, 0], [2, "1395", 503, 728, 4, 5, 0], [2, "1394", 585, 1073, 10, 12, 0], [2, "1394", 715, 1014, 10, 12, 0], [2, "3380", 520, 1089, 40, 55, 0], [2, "3408", 725, 1107, 42, 26, 0], [2, "894", 519, 1067, 24, 20, 0], [2, "894", 540, 1078, 24, 20, 0], [2, "894", 526, 1055, 24, 20, 0], [2, "894", 531, 1058, 24, 20, 0], [2, "870", 533, 1053, 12, 32, 0], [2, "870", 532, 1045, 12, 32, 0], [2, "1448", 91, 793, 44, 48, 0], [2, "1449", 134, 772, 44, 47, 0], [2, "1450", 174, 752, 44, 48, 0], [2, "1448", 217, 731, 44, 48, 0], [2, "1449", 260, 710, 44, 47, 0], [2, "1450", 300, 690, 44, 48, 0], [2, "1394", 128, 793, 10, 12, 0], [2, "1394", 170, 771, 10, 12, 0], [2, "1394", 211, 750, 10, 12, 0], [2, "1394", 253, 728, 10, 12, 0], [2, "1394", 296, 708, 10, 12, 0], [2, "1410", 1083, 1054, 22, 32, 0], [2, "1394", 1108, 1089, 10, 12, 0], [2, "1394", 1089, 1112, 10, 12, 0], [2, "1395", 1096, 1096, 4, 5, 0], [2, "1395", 1115, 1120, 4, 5, 0], [2, "1395", 1110, 1072, 4, 5, 0], [2, "3378", 1003, 125, 62, 42, 0], [2, "3378", 1003, 125, 62, 42, 0], [2, "1225_1", 859, -21, 48, 44, 0], [2, "1226_1", 900, 2, 70, 47, 0], [2, "1226_1", 959, 26, 70, 47, 0], [2, "3406", 146, 254, 62, 92, 0], [2, "3406", 120, 267, 62, 92, 0], [2, "3406", 93, 279, 62, 92, 0], [2, "3406", 67, 292, 62, 92, 0], [2, "3406", 41, 306, 62, 92, 0], [2, "3406", 15, 319, 62, 92, 0], [2, "3406", -10, 331, 62, 92, 0], [2, "3381", 38, 375, 70, 57, 0], [2, "3381", 133, 327, 70, 57, 0], [2, "3125", 46, 333, 36, 60, 0], [2, "3125", 146, 284, 36, 60, 0], [2, "3125", 97, 307, 36, 60, 0], [2, "1394", 846, 852, 10, 12, 0], [2, "1394", 838, 886, 10, 12, 0], [2, "1395", 857, 907, 4, 5, 0], [2, "1395", 838, 914, 4, 5, 0], [2, "1395", 851, 871, 4, 5, 0], [2, "1395", 279, 588, 4, 5, 0], [2, "1394", 287, 605, 10, 12, 0], [2, "1397", 836, 822, 22, 26, 0], [2, "1397", 272, 548, 22, 26, 0], [2, "1397", 558, 396, 22, 26, 0], [2, "1397", 605, 376, 22, 26, 0], [2, "1411", 603, 399, 26, 41, 0], [2, "1410", 569, 419, 22, 32, 2], [2, "1394", 556, 459, 10, 12, 0], [2, "1394", 578, 476, 10, 12, 0], [2, "1394", 604, 454, 10, 12, 0], [2, "1394", 629, 461, 10, 12, 0], [2, "1394", 643, 488, 10, 12, 0], [2, "1395", 561, 484, 4, 5, 0], [2, "1395", 561, 484, 4, 5, 0], [2, "1395", 577, 458, 4, 5, 0], [2, "1395", 562, 447, 4, 5, 0], [2, "1395", 602, 439, 4, 5, 0], [2, "1395", 602, 439, 4, 5, 0], [2, "1395", 621, 444, 4, 5, 0], [2, "1395", 607, 474, 4, 5, 0], [2, "1395", 630, 486, 4, 5, 0], [2, "1395", 86, 834, 4, 5, 0], [2, "1395", 98, 855, 4, 5, 0], [2, "1395", 112, 878, 4, 5, 0], [2, "1395", 873, 520, 4, 5, 0], [2, "1395", 861, 539, 4, 5, 0], [2, "1395", 847, 562, 4, 5, 0], [2, "1395", 1010, 642, 4, 5, 0], [2, "1395", 1010, 642, 4, 5, 0], [2, "1395", 1022, 626, 4, 5, 0], [2, "1395", 1033, 608, 4, 5, 0], [2, "1394", 853, 549, 10, 12, 0], [2, "1394", 1017, 628, 10, 12, 0], [2, "1397", 395, 25, 22, 26, 0], [2, "1397", 371, 94, 22, 26, 0], [2, "1397", 487, -1, 22, 26, 0], [2, "1397", 548, 34, 22, 26, 0], [2, "1411", 398, 54, 26, 41, 0], [2, "1411", 550, 60, 26, 41, 0], [2, "1394", 374, 141, 10, 12, 0], [2, "1394", 390, 81, 10, 12, 0], [2, "1394", 407, 114, 10, 12, 0], [2, "1394", 416, 95, 10, 12, 0], [2, "1394", 478, 86, 10, 12, 0], [2, "1394", 478, 86, 10, 12, 0], [2, "1394", 499, 48, 10, 12, 0], [2, "1394", 499, 48, 10, 12, 0], [2, "1394", 513, 84, 10, 12, 0], [2, "1394", 495, 100, 10, 12, 0], [2, "1394", 385, 166, 10, 12, 0], [2, "1395", 383, 130, 4, 5, 0], [2, "1395", 383, 130, 4, 5, 0], [2, "1395", 390, 146, 4, 5, 0], [2, "1395", 373, 164, 4, 5, 0], [2, "1395", 373, 164, 4, 5, 0], [2, "1395", 428, 128, 4, 5, 0], [2, "1395", 428, 128, 4, 5, 0], [2, "1395", 401, 100, 4, 5, 0], [2, "1395", 401, 100, 4, 5, 0], [2, "1395", 490, 71, 4, 5, 0], [2, "1395", 490, 71, 4, 5, 0], [2, "1395", 488, 52, 4, 5, 0], [2, "1395", 488, 52, 4, 5, 0], [2, "1395", 505, 79, 4, 5, 0], [2, "1395", 519, 107, 4, 5, 0], [2, "1395", 558, 106, 4, 5, 0], [2, "1395", 558, 106, 4, 5, 0], [2, "1395", 475, 109, 4, 5, 0], [2, "1411", 226, -18, 26, 41, 0], [2, "1394", 252, 20, 10, 12, 0], [2, "1394", 169, -3, 10, 12, 0], [2, "1395", 192, 1, 4, 5, 0], [2, "1395", 254, 7, 4, 5, 0], [2, "1395", 240, 31, 4, 5, 0], [2, "1397", 10, 90, 22, 26, 0], [2, "1394", 30, 199, 10, 12, 0], [2, "1394", 30, 199, 10, 12, 0], [2, "1394", 8, 139, 10, 12, 0], [2, "1394", 8, 139, 10, 12, 0], [2, "1394", 25, 127, 10, 12, 0], [2, "1394", 25, 153, 10, 12, 0], [2, "1394", 25, 153, 10, 12, 0], [2, "1394", 25, 153, 10, 12, 0], [2, "1395", 11, 171, 4, 5, 0], [2, "1395", 33, 188, 4, 5, 0], [2, "1395", 33, 188, 4, 5, 0], [2, "1395", 43, 223, 4, 5, 0], [2, "1395", 43, 223, 4, 5, 0], [2, "1395", 25, 234, 4, 5, 0], [2, "1395", 6, 341, 4, 5, 0], [2, "1395", 16, 359, 4, 5, 0], [2, "1395", 16, 359, 4, 5, 0], [2, "1395", 24, 379, 4, 5, 0], [2, "1395", 32, 397, 4, 5, 0], [2, "1395", 169, 264, 4, 5, 0], [2, "1395", 169, 264, 4, 5, 0], [2, "1395", 179, 281, 4, 5, 0], [2, "1395", 190, 300, 4, 5, 0], [2, "1395", 196, 316, 4, 5, 0], [2, "1411", 30, 404, 26, 41, 0], [2, "1394", 1090, 373, 10, 12, 0], [2, "1394", 1107, 361, 10, 12, 0], [2, "1394", 1104, 400, 10, 12, 0], [2, "1394", 1051, 406, 10, 12, 0], [2, "1394", 1051, 406, 10, 12, 0], [2, "1394", 1044, 429, 10, 12, 0], [2, "1394", 1087, 429, 10, 12, 0], [2, "1395", 1062, 430, 4, 5, 0], [2, "1395", 1062, 430, 4, 5, 0], [2, "1395", 1089, 408, 4, 5, 0], [2, "1395", 1127, 386, 4, 5, 0], [2, "1395", 1127, 386, 4, 5, 0], [2, "1395", 1122, 417, 4, 5, 0], [2, "1395", 1122, 417, 4, 5, 0], [2, "1395", 1108, 352, 4, 5, 0], [2, "1397", 1043, 366, 22, 26, 0], [2, "1397", 1098, 322, 22, 26, 0], [2, "1397", 1101, 146, 22, 26, 0], [2, "1394", 1099, 201, 10, 12, 0], [2, "1394", 1114, 222, 10, 12, 0], [2, "1395", 1096, 234, 4, 5, 0], [2, "1395", 1096, 234, 4, 5, 0], [2, "1395", 1120, 201, 4, 5, 0], [2, "1395", 1120, 201, 4, 5, 0], [2, "1395", 1110, 180, 4, 5, 0], [2, "1395", 1110, 180, 4, 5, 0], [2, "1395", 1121, 251, 4, 5, 0], [2, "1395", 797, 35, 4, 5, 0], [2, "1395", 817, 36, 4, 5, 0], [2, "1395", 817, 36, 4, 5, 0], [2, "1395", 836, 32, 4, 5, 0], [2, "1395", 845, 60, 4, 5, 0], [2, "1395", 865, 68, 4, 5, 0], [2, "1395", 889, 80, 4, 5, 0], [2, "1395", 915, 90, 4, 5, 0], [2, "1395", 915, 90, 4, 5, 0], [2, "1395", 939, 100, 4, 5, 0], [2, "1395", 939, 100, 4, 5, 0], [2, "1395", 963, 109, 4, 5, 0], [2, "1395", 963, 109, 4, 5, 0], [2, "1395", 984, 120, 4, 5, 0], [2, "1395", 1008, 129, 4, 5, 0], [2, "1395", 1027, 133, 4, 5, 0], [2, "1395", 1027, 133, 4, 5, 0], [2, "1395", 1045, 134, 4, 5, 0], [2, "1395", 1062, 128, 4, 5, 0], [2, "1395", 1083, 113, 4, 5, 0], [2, "1395", 1103, 106, 4, 5, 0], [2, "1412", 862, -27, 32, 28, 2], [2, "1412", 879, -16, 32, 28, 2], [2, "1412", 879, -16, 32, 28, 2], [2, "1412", 898, -11, 32, 28, 2], [2, "1412", 917, -2, 32, 28, 2], [2, "1412", 937, 6, 32, 28, 2], [2, "1412", 954, 13, 32, 28, 2], [2, "1412", 954, 13, 32, 28, 2], [2, "1412", 978, 23, 32, 28, 2], [2, "1412", 978, 23, 32, 28, 2], [2, "1412", 993, 29, 32, 28, 2], [2, "1416", 880, 47, 24, 17, 0], [2, "1416", 961, 53, 24, 17, 0], [2, "1416", 1071, 83, 24, 17, 0], [2, "1413", 1011, 73, 20, 13, 0], [2, "1413", 1073, 111, 20, 13, 0], [2, "1413", 1085, 106, 20, 13, 0], [2, "1413", 1096, 101, 20, 13, 0], [2, "1413", 1073, 111, 20, 13, 0], [2, "1413", 941, 104, 20, 13, 2], [2, "1413", 847, 65, 20, 13, 2], [2, "1448", 300, 572, 44, 48, 2], [2, "1449", 257, 549, 44, 47, 2], [2, "1450", 215, 529, 44, 48, 2], [2, "1394", 253, 547, 10, 12, 0], [2, "1394", 295, 569, 10, 12, 0], [2, "1394", 295, 569, 10, 12, 0], [2, "1448", 174, 508, 44, 48, 2], [2, "1449", 131, 485, 44, 47, 2], [2, "1450", 89, 465, 44, 48, 2], [2, "1448", 48, 443, 44, 48, 2], [2, "1449", 5, 420, 44, 47, 2], [2, "1450", -37, 400, 44, 48, 2], [2, "1394", 168, 504, 10, 12, 0], [2, "1394", 168, 504, 10, 12, 0], [2, "1394", 210, 526, 10, 12, 0], [2, "1394", 131, 484, 10, 12, 0], [2, "1394", 87, 463, 10, 12, 0], [2, "1394", 46, 440, 10, 12, 0], [2, "1394", 2, 419, 10, 12, 0], [2, "1448", 773, 1001, 44, 48, 2], [2, "1450", 814, 1022, 44, 48, 2], [2, "1394", 809, 1019, 10, 12, 0], [2, "1394", 767, 997, 10, 12, 0], [2, "1449", 730, 978, 44, 47, 2], [2, "1394", 730, 977, 10, 12, 0], [2, "1448", 897, 1064, 44, 48, 2], [2, "1450", 938, 1085, 44, 48, 2], [2, "1394", 933, 1082, 10, 12, 0], [2, "1394", 891, 1060, 10, 12, 0], [2, "1449", 854, 1041, 44, 47, 2], [2, "1394", 854, 1040, 10, 12, 0], [2, "1448", 1019, 1129, 44, 48, 2], [2, "1450", 1060, 1150, 44, 48, 2], [2, "1394", 1055, 1147, 10, 12, 0], [2, "1394", 1013, 1125, 10, 12, 0], [2, "1449", 976, 1106, 44, 47, 2], [2, "1394", 976, 1105, 10, 12, 0], [2, "1448", 617, 683, 44, 48, 0], [2, "1394", 654, 683, 10, 12, 0], [2, "1449", 660, 662, 44, 47, 0], [2, "1450", 700, 642, 44, 48, 0], [2, "1394", 737, 640, 10, 12, 0], [2, "1448", 747, 619, 44, 48, 0], [2, "1394", 784, 619, 10, 12, 0], [2, "1449", 790, 598, 44, 47, 0], [2, "1450", 830, 578, 44, 48, 0], [2, "1394", 826, 597, 10, 12, 0], [2, "1394", 696, 661, 10, 12, 0], [2, "1448", 375, 308, 44, 48, 2], [2, "1450", 416, 330, 44, 48, 2], [2, "1449", 458, 350, 44, 47, 2], [2, "1448", 501, 373, 44, 48, 2], [2, "1450", 542, 394, 44, 48, 2], [2, "1394", 373, 305, 10, 12, 0], [2, "1394", 414, 328, 10, 12, 0], [2, "1394", 458, 349, 10, 12, 0], [2, "1394", 495, 369, 10, 12, 0], [2, "1394", 537, 391, 10, 12, 0], [2, "1394", 580, 412, 10, 12, 0], [2, "1448", 647, 184, 44, 48, 2], [2, "1450", 688, 206, 44, 48, 2], [2, "1449", 730, 226, 44, 47, 2], [2, "1448", 773, 249, 44, 48, 2], [2, "1394", 640, 179, 10, 12, 0], [2, "1394", 682, 201, 10, 12, 0], [2, "1394", 725, 222, 10, 12, 0], [2, "1394", 765, 243, 10, 12, 0], [2, "1394", 808, 266, 10, 12, 0], [2, "1448", 224, 199, 44, 48, 0], [2, "1449", 267, 178, 44, 47, 0], [2, "1450", 307, 158, 44, 48, 0], [2, "1394", 259, 197, 10, 12, 0], [2, "1394", 304, 175, 10, 12, 0], [2, "3415", 788, -97, 52, 86, 2], [2, "3415", 1004, 8, 52, 86, 2], [2, "3354", 996, 94, 78, 69, 2], [2, "3405", 653, 901, 44, 94, 0], [2, "3405", 98, 217, 44, 94, 0], [2, "3125", 876, 520, 36, 60, 2], [2, "3125", 976, 570, 36, 60, 2], [2, "1403", 1064, 590, 18, 22, 2], [2, "1403", 1072, 607, 18, 22, 2]]}, {"type": 4, "obj": [[2, "1228", 904, -55, 60, 75, 0], [2, "1228", 928, -44, 60, 75, 0], [2, "1228", 953, -35, 60, 75, 0], [2, "1228", 976, -23, 60, 75, 0], [2, "1231_5", 66, -101, 114, 162, 0], [2, "1225_1", 846, 17, 48, 44, 0], [2, "1228", 1002, -13, 60, 75, 0], [2, "1228", 1030, -13, 60, 75, 2], [2, "1228", 1019, -9, 60, 75, 2], [2, "1410", 173, 35, 22, 32, 2], [2, "3383", 217, -42, 68, 114, 0], [2, "1228", 827, 2, 60, 75, 0], [2, "1231_5", -8, -78, 114, 162, 0], [2, "1228", 851, 12, 60, 75, 0], [2, "1228", 875, 23, 60, 75, 0], [4, 5, 279, 104, 0, 4010], [2, "3360", 183, 48, 58, 57, 2], [2, "1228", 897, 32, 60, 75, 0], [2, "1226_1", 940, 62, 70, 47, 0], [2, "1228", 920, 41, 60, 75, 0], [2, "3383", 117, 4, 68, 114, 0], [2, "1226_1", 975, 76, 70, 47, 0], [2, "1228", 1057, 49, 60, 75, 2], [2, "1228", 944, 51, 60, 75, 0], [2, "1228", 968, 60, 60, 75, 0], [2, "1228", 1032, 60, 60, 75, 2], [2, "1228", 980, 66, 60, 75, 0], [4, 6, 161, 160, 0, 4018], [2, "3383", 529, 49, 68, 114, 0], [2, "1231_5", 439, 9, 114, 162, 0], [2, "1404_1", 572, 90, 58, 86, 2], [2, "3359", 435, 148, 32, 43, 0], [2, "1231_5", 349, 30, 114, 162, 0], [2, "3377", 1011, 126, 46, 67, 2], [2, "1404_1", 789, 111, 58, 86, 2], [2, "1413", 453, 198, 20, 13, 0], [2, "3384", 396, 165, 52, 56, 0], [2, "3383", 349, 115, 68, 114, 0], [2, "3067", 75, 142, 18, 93, 0], [2, "3384", 615, 181, 52, 56, 0], [2, "3067", 337, 151, 18, 93, 0], [2, "1231_5", -36, 93, 114, 162, 0], [2, "1409", 642, 229, 26, 27, 0], [2, "3067", 631, 171, 18, 93, 0], [2, "3364", 383, 200, 94, 70, 0], [2, "1409", 508, 247, 26, 27, 0], [2, "1409", 595, 256, 26, 27, 0], [2, "1413", 603, 273, 20, 13, 2], [2, "1404_1", 945, 202, 58, 86, 2], [2, "3383", 2, 177, 68, 114, 0], [2, "1409_1", 488, 264, 26, 27, 0], [2, "1413", 487, 281, 20, 13, 0], [2, "1409_1", 505, 267, 26, 27, 0], [2, "1409", 548, 272, 26, 27, 0], [2, "1413", 551, 292, 20, 13, 0], [2, "3067", 213, 219, 18, 93, 0], [2, "1231_5", 1054, 154, 114, 162, 0], [2, "3067", 500, 233, 18, 93, 0], [2, "3067", 813, 271, 18, 93, 0], [2, "3383", 1018, 251, 68, 114, 0], [2, "3067", 369, 300, 18, 93, 0], [2, "1207", 195, 324, 22, 81, 0], [2, "1231_5", 912, 252, 114, 162, 0], [2, "3067", 708, 323, 18, 93, 0], [2, "1279", 167, 371, 42, 58, 2], [4, 1, 828, 436, 0, 4021], [2, "3383", 913, 336, 68, 114, 0], [2, "1231_5", 1049, 326, 114, 162, 0], [2, "3383", 660, 379, 68, 114, 0], [2, "3067", 868, 402, 18, 93, 0], [2, "3383", 1022, 389, 68, 114, 0], [2, "1231_5", 594, 349, 114, 162, 0], [2, "3384", 728, 480, 52, 56, 0], [4, 7, 529, 539, 0, 4029], [2, "1231_5", 557, 381, 114, 162, 0], [2, "3383", 605, 451, 68, 114, 0], [2, "3067", 392, 482, 18, 93, 0], [2, "1404_1", 575, 496, 58, 86, 0], [2, "3067", 1086, 516, 18, 93, 0], [2, "3067", 280, 538, 18, 93, 0], [2, "3383", 251, 570, 68, 114, 0], [2, "3069", 753, 610, 98, 74, 2], [2, "1409_1", 280, 667, 26, 27, 0], [2, "3067", 635, 602, 18, 93, 0], [2, "3359", 1099, 663, 32, 43, 0], [2, "3069", 768, 644, 98, 74, 0], [2, "3357", 828, 669, 40, 53, 0], [2, "3067", 52, 650, 18, 93, 0], [2, "3069", 1072, 682, 98, 74, 2], [2, "1105", 120, 699, 104, 75, 0], [2, "1208", 326, 727, 52, 56, 0], [2, "3069", 919, 716, 98, 74, 0], [2, "3069", 997, 723, 98, 74, 2], [2, "894", 615, 780, 24, 20, 0], [2, "1208", 375, 751, 52, 56, 0], [2, "870", 625, 777, 12, 32, 0], [2, "870", 600, 790, 12, 32, 0], [2, "1208", 564, 771, 52, 56, 2], [2, "875", 638, 793, 14, 34, 0], [2, "1208", 415, 772, 52, 56, 0], [2, "3067", 201, 741, 18, 93, 0], [2, "875", 609, 808, 14, 34, 0], [2, "3358", 668, 740, 50, 102, 0], [2, "875", 649, 810, 14, 34, 0], [2, "875", 649, 810, 14, 34, 0], [2, "870", 663, 818, 12, 32, 0], [2, "1208", 464, 796, 52, 56, 0], [2, "1208", 516, 796, 52, 56, 2], [2, "3067", 717, 765, 18, 93, 0], [2, "875", 620, 825, 14, 34, 0], [2, "870", 633, 835, 12, 32, 0], [2, "3067", 88, 791, 18, 93, 0], [2, "870", 113, 883, 12, 32, 0], [2, "870", 113, 902, 12, 32, 0], [4, 2, 204, 957, 0, 4019], [2, "3383", 814, 847, 68, 114, 0], [2, "3067", 252, 870, 18, 93, 0], [2, "3067", 488, 882, 18, 93, 0], [2, "3383", 854, 867, 68, 114, 0], [2, "1404_1", 897, 896, 58, 86, 0], [2, "1213", 108, 923, 26, 62, 2], [2, "3359", 104, 961, 32, 43, 0], [2, "3362", 47, 980, 38, 54, 0], [4, 4, 932, 1046, 0, 4020], [2, "3365", 748, 1023, 66, 51, 2], [2, "1404_1", 372, 994, 58, 86, 2], [2, "1409", 761, 1055, 26, 27, 0], [2, "3365", 458, 1047, 66, 51, 0], [2, "3365", 762, 1056, 66, 51, 0], [2, "3357", 1025, 1064, 40, 53, 0], [2, "3365", 393, 1076, 66, 51, 0]]}, {"type": 3, "obj": [[2, "3069", 1042, 607, 98, 74, 0], [2, "1414", 680, 90, 46, 30, 2], [2, "1414", 645, 72, 46, 30, 2], [2, "1414", 558, 28, 46, 30, 2], [2, "1414", 31, 72, 46, 30, 2], [2, "1414", -12, 506, 46, 30, 2], [2, "1414", 150, 462, 46, 30, 0], [2, "3383", 147, -68, 68, 114, 2], [2, "688", 488, 0, 46, 24, 0], [2, "688", 496, 4, 46, 24, 0], [2, "688", 450, 19, 46, 24, 0], [2, "688", 458, 23, 46, 24, 0], [2, "688", 413, 37, 46, 24, 0], [2, "688", 421, 41, 46, 24, 0], [2, "688", 375, 56, 46, 24, 0], [2, "688", 383, 60, 46, 24, 0], [2, "688", 337, 75, 46, 24, 0], [2, "688", 345, 79, 46, 24, 0], [2, "3369", 889, 55, 44, 81, 0], [2, "3369", 844, 19, 44, 81, 0], [2, "3369", 844, 78, 44, 81, 0], [2, "560", 902, 83, 130, 110, 0], [2, "3369", 1020, -68, 44, 81, 0], [2, "3369", 1020, -9, 44, 81, 0], [2, "3369", 1020, -121, 44, 81, 0], [2, "3369", 1065, -91, 44, 81, 0], [2, "3369", 1065, -32, 44, 81, 0], [2, "3369", 1065, -144, 44, 81, 0], [2, "1414", 65, 983, 46, 30, 0], [2, "1414", 599, 844, 46, 30, 0], [2, "1414", 570, 858, 46, 30, 0], [2, "1414", 510, 886, 46, 30, 0], [2, "1414", 338, 840, 46, 30, 2], [2, "1414", 432, 881, 46, 30, 2], [2, "1414", 314, 829, 46, 30, 2], [2, "1414", 294, 813, 46, 30, 2], [2, "1414", 818, 654, 46, 30, 2], [2, "688", 771, 598, 46, 24, 0], [2, "688", 779, 602, 46, 24, 0], [2, "688", 809, 579, 46, 24, 0], [2, "688", 817, 583, 46, 24, 0], [2, "3397", 842, 573, 44, 81, 0], [2, "3397", 886, 595, 44, 81, 0], [2, "3397", 931, 618, 44, 81, 0], [2, "3397", 975, 640, 44, 81, 0], [2, "3398", 1020, 640, 44, 80, 0], [2, "3398", 1045, 627, 44, 80, 0], [2, "3082", 382, 851, 76, 40, 2], [2, "1414", 763, 1098, 46, 30, 0], [2, "3402", 555, 802, 44, 80, 0], [2, "688", 539, 755, 46, 24, 2], [2, "688", 547, 759, 46, 24, 2], [2, "688", 530, 759, 46, 24, 2], [2, "688", 538, 763, 46, 24, 2], [2, "688", 585, 778, 46, 24, 2], [2, "688", 593, 782, 46, 24, 2], [2, "688", 576, 782, 46, 24, 2], [2, "688", 584, 786, 46, 24, 2], [2, "688", 631, 800, 46, 24, 2], [2, "688", 639, 804, 46, 24, 2], [2, "688", 622, 804, 46, 24, 2], [2, "688", 630, 808, 46, 24, 2], [2, "688", 677, 823, 46, 24, 2], [2, "688", 685, 827, 46, 24, 2], [2, "688", 668, 827, 46, 24, 2], [2, "688", 676, 831, 46, 24, 2], [2, "688", 294, 760, 46, 24, 0], [2, "688", 302, 764, 46, 24, 0], [2, "3371", 506, 1075, 44, 81, 0], [2, "3371", 534, 1089, 44, 81, 0], [2, "3372", 570, 1079, 44, 81, 0], [2, "3372", 614, 1058, 44, 81, 0], [2, "3372", 658, 1036, 44, 81, 0], [2, "3372", 700, 1015, 44, 81, 0], [2, "3371", 524, 1072, 44, 81, 0], [2, "3371", 514, 1068, 44, 81, 0], [2, "870", 504, 1077, 12, 32, 0], [2, "870", 504, 1105, 12, 32, 0], [2, "870", 571, 1100, 12, 32, 0], [2, "870", 571, 1128, 12, 32, 0], [2, "870", 735, 1028, 12, 32, 0], [2, "870", 735, 1056, 12, 32, 0], [2, "870", 736, 1010, 12, 32, 0], [2, "3374", 648, 1053, 32, 72, 0], [2, "1409_1", 699, 1073, 26, 27, 0], [2, "3402", 511, 824, 44, 80, 0], [2, "3402", 571, 793, 44, 80, 0], [2, "3401", 466, 823, 44, 81, 0], [2, "3401", 421, 800, 44, 81, 0], [2, "3401", 376, 778, 44, 81, 0], [2, "3401", 331, 755, 44, 81, 0], [2, "1207", 597, 790, 22, 81, 0], [2, "3402", 551, 722, 44, 80, 0], [2, "1194", 404, 816, 46, 60, 2], [2, "1208", 459, 853, 52, 56, 0], [2, "1207", 453, 813, 22, 81, 0], [2, "1207", 502, 838, 22, 81, 0], [2, "1208", 334, 790, 52, 56, 0], [2, "1207", 327, 749, 22, 81, 0], [2, "875", 489, 835, 14, 34, 0], [2, "875", 477, 815, 14, 34, 0], [2, "876", 348, 764, 14, 26, 0], [2, "876", 361, 752, 14, 26, 0], [2, "894", 350, 785, 24, 20, 0], [2, "894", 474, 846, 24, 20, 0], [2, "894", 479, 849, 24, 20, 0], [2, "894", 356, 734, 24, 20, 0], [2, "3393", 366, 1021, 80, 62, 0], [2, "3393", 366, 1021, 80, 62, 0], [2, "3393", 407, 1039, 80, 62, 0], [2, "3393", 325, 1042, 80, 62, 0], [2, "3393", 366, 1060, 80, 62, 0], [2, "3393", 285, 1060, 80, 62, 0], [2, "3393", 326, 1078, 80, 62, 0], [2, "3393", 246, 1077, 80, 62, 0], [2, "3393", 287, 1095, 80, 62, 0], [2, "3393", 205, 1096, 80, 62, 0], [2, "3393", 246, 1114, 80, 62, 0], [2, "688", 694, 845, 46, 24, 0], [2, "688", 702, 849, 46, 24, 0], [2, "688", 656, 864, 46, 24, 0], [2, "688", 664, 868, 46, 24, 0], [2, "688", 618, 883, 46, 24, 0], [2, "688", 626, 887, 46, 24, 0], [2, "688", 580, 902, 46, 24, 0], [2, "688", 588, 906, 46, 24, 0], [2, "688", 541, 921, 46, 24, 0], [2, "688", 549, 925, 46, 24, 0], [2, "688", 503, 940, 46, 24, 0], [2, "688", 511, 944, 46, 24, 0], [2, "688", 516, 934, 46, 24, 0], [2, "688", 524, 938, 46, 24, 0], [2, "688", 478, 953, 46, 24, 0], [2, "688", 486, 957, 46, 24, 0], [2, "688", 443, 941, 46, 24, 2], [2, "688", 451, 945, 46, 24, 2], [2, "688", 434, 945, 46, 24, 2], [2, "688", 442, 949, 46, 24, 2], [2, "688", 397, 918, 46, 24, 2], [2, "688", 405, 922, 46, 24, 2], [2, "688", 388, 922, 46, 24, 2], [2, "688", 396, 926, 46, 24, 2], [2, "688", 305, 872, 46, 24, 2], [2, "688", 313, 876, 46, 24, 2], [2, "688", 296, 876, 46, 24, 2], [2, "688", 304, 880, 46, 24, 2], [2, "688", 351, 895, 46, 24, 2], [2, "688", 359, 899, 46, 24, 2], [2, "688", 342, 899, 46, 24, 2], [2, "688", 350, 903, 46, 24, 2], [2, "688", 213, 827, 46, 24, 2], [2, "688", 221, 831, 46, 24, 2], [2, "688", 204, 831, 46, 24, 2], [2, "688", 212, 835, 46, 24, 2], [2, "688", 259, 850, 46, 24, 2], [2, "688", 267, 854, 46, 24, 2], [2, "688", 250, 854, 46, 24, 2], [2, "688", 258, 858, 46, 24, 2], [2, "688", 408, 703, 46, 24, 0], [2, "688", 416, 707, 46, 24, 0], [2, "688", 370, 722, 46, 24, 0], [2, "688", 378, 726, 46, 24, 0], [2, "688", 230, 792, 46, 24, 0], [2, "688", 238, 796, 46, 24, 0], [2, "688", 255, 779, 46, 24, 0], [2, "688", 263, 783, 46, 24, 0], [2, "688", 192, 811, 46, 24, 0], [2, "688", 200, 815, 46, 24, 0], [2, "1279", 542, 820, 42, 58, 2], [2, "3408", 596, 800, 42, 26, 0], [2, "3408", 607, 814, 42, 26, 0], [2, "3408", 618, 828, 42, 26, 0], [2, "3408", 630, 842, 42, 26, 0], [2, "3369", -12, 956, 44, 81, 0], [2, "3369", 28, 936, 44, 81, 0], [2, "3369", 68, 916, 44, 81, 0], [2, "3356", 459, 877, 44, 49, 0], [2, "1414", 1018, 1090, 46, 30, 0], [2, "1414", 1036, 1095, 46, 30, 0], [2, "1414", 1040, 1106, 46, 30, 0], [2, "1414", 769, 1084, 46, 30, 0], [2, "1414", 798, 1068, 46, 30, 0], [2, "1414", 915, 968, 46, 30, 0], [2, "1414", 915, 968, 46, 30, 0], [2, "1414", 890, 978, 46, 30, 0], [2, "1414", 854, 977, 46, 30, 2], [2, "1414", 838, 953, 46, 30, 2], [2, "1414", 830, 965, 46, 30, 2], [2, "1414", 808, 942, 46, 30, 2], [2, "1414", 945, 970, 46, 30, 2], [2, "1414", 966, 981, 46, 30, 2], [2, "1414", 1020, 1029, 46, 30, 2], [2, "1414", 1011, 1045, 46, 30, 2], [2, "688", 200, 821, 46, 24, 2], [2, "688", 208, 825, 46, 24, 2], [2, "688", 191, 825, 46, 24, 2], [2, "688", 199, 829, 46, 24, 2], [2, "688", 738, 854, 46, 24, 2], [2, "688", 746, 858, 46, 24, 2], [2, "688", 729, 858, 46, 24, 2], [2, "688", 737, 862, 46, 24, 2], [2, "688", 784, 877, 46, 24, 2], [2, "688", 792, 881, 46, 24, 2], [2, "688", 775, 881, 46, 24, 2], [2, "688", 783, 885, 46, 24, 2], [2, "688", 829, 900, 46, 24, 2], [2, "688", 837, 904, 46, 24, 2], [2, "688", 820, 904, 46, 24, 2], [2, "688", 828, 908, 46, 24, 2], [2, "688", 875, 923, 46, 24, 2], [2, "688", 883, 927, 46, 24, 2], [2, "688", 866, 927, 46, 24, 2], [2, "688", 874, 931, 46, 24, 2], [2, "688", 873, 923, 46, 24, 2], [2, "688", 881, 927, 46, 24, 2], [2, "688", 864, 927, 46, 24, 2], [2, "688", 872, 931, 46, 24, 2], [2, "688", 919, 946, 46, 24, 2], [2, "688", 927, 950, 46, 24, 2], [2, "688", 910, 950, 46, 24, 2], [2, "688", 918, 954, 46, 24, 2], [2, "688", 964, 969, 46, 24, 2], [2, "688", 972, 973, 46, 24, 2], [2, "688", 955, 973, 46, 24, 2], [2, "688", 963, 977, 46, 24, 2], [2, "688", 1009, 992, 46, 24, 2], [2, "688", 1017, 996, 46, 24, 2], [2, "688", 1000, 996, 46, 24, 2], [2, "688", 1008, 1000, 46, 24, 2], [2, "688", 1055, 1015, 46, 24, 2], [2, "688", 1063, 1019, 46, 24, 2], [2, "688", 1046, 1019, 46, 24, 2], [2, "688", 1054, 1023, 46, 24, 2], [2, "688", 1100, 1038, 46, 24, 2], [2, "688", 1108, 1042, 46, 24, 2], [2, "688", 1091, 1042, 46, 24, 2], [2, "688", 1099, 1046, 46, 24, 2], [2, "1414", 753, 875, 46, 30, 2], [2, "1414", 776, 888, 46, 30, 2], [2, "1414", 793, 905, 46, 30, 2], [2, "1414", 841, 801, 46, 30, 2], [2, "1414", 1107, 1043, 46, 30, 2], [2, "1414", 928, 954, 46, 30, 2], [2, "1414", 844, 908, 46, 30, 2], [2, "1414", 801, 888, 46, 30, 2], [2, "1414", 1008, 859, 46, 30, 2], [2, "1413", 1062, 884, 20, 13, 0], [2, "688", 195, 645, 46, 24, 0], [2, "688", 203, 649, 46, 24, 0], [2, "688", 157, 664, 46, 24, 0], [2, "688", 165, 668, 46, 24, 0], [2, "688", 119, 683, 46, 24, 0], [2, "688", 127, 687, 46, 24, 0], [2, "688", 81, 702, 46, 24, 0], [2, "688", 89, 706, 46, 24, 0], [2, "688", 42, 721, 46, 24, 0], [2, "688", 50, 725, 46, 24, 0], [2, "688", 17, 734, 46, 24, 0], [2, "688", 25, 738, 46, 24, 0], [2, "688", -21, 753, 46, 24, 0], [2, "688", -13, 757, 46, 24, 0], [2, "688", 451, 518, 46, 24, 0], [2, "688", 459, 522, 46, 24, 0], [2, "688", 413, 537, 46, 24, 0], [2, "688", 421, 541, 46, 24, 0], [2, "688", 375, 556, 46, 24, 0], [2, "688", 383, 560, 46, 24, 0], [2, "688", 337, 575, 46, 24, 0], [2, "688", 345, 579, 46, 24, 0], [2, "688", 298, 594, 46, 24, 0], [2, "688", 306, 598, 46, 24, 0], [2, "688", 273, 607, 46, 24, 0], [2, "688", 281, 611, 46, 24, 0], [2, "688", 235, 626, 46, 24, 0], [2, "688", 243, 630, 46, 24, 0], [2, "688", 649, 420, 46, 24, 0], [2, "688", 657, 424, 46, 24, 0], [2, "688", 611, 439, 46, 24, 0], [2, "688", 619, 443, 46, 24, 0], [2, "688", 573, 458, 46, 24, 0], [2, "688", 581, 462, 46, 24, 0], [2, "688", 535, 477, 46, 24, 0], [2, "688", 543, 481, 46, 24, 0], [2, "688", 496, 496, 46, 24, 0], [2, "688", 504, 500, 46, 24, 0], [2, "688", 471, 509, 46, 24, 0], [2, "688", 479, 513, 46, 24, 0], [2, "688", 433, 528, 46, 24, 0], [2, "688", 441, 532, 46, 24, 0], [2, "688", 905, 292, 46, 24, 0], [2, "688", 913, 296, 46, 24, 0], [2, "688", 867, 311, 46, 24, 0], [2, "688", 875, 315, 46, 24, 0], [2, "688", 829, 330, 46, 24, 0], [2, "688", 837, 334, 46, 24, 0], [2, "688", 791, 349, 46, 24, 0], [2, "688", 799, 353, 46, 24, 0], [2, "688", 752, 368, 46, 24, 0], [2, "688", 760, 372, 46, 24, 0], [2, "688", 727, 381, 46, 24, 0], [2, "688", 735, 385, 46, 24, 0], [2, "688", 689, 400, 46, 24, 0], [2, "688", 697, 404, 46, 24, 0], [2, "688", 1019, 235, 46, 24, 0], [2, "688", 1027, 239, 46, 24, 0], [2, "688", 981, 254, 46, 24, 0], [2, "688", 989, 258, 46, 24, 0], [2, "688", 943, 273, 46, 24, 0], [2, "688", 951, 277, 46, 24, 0], [2, "1413", 1111, 908, 20, 13, 0], [2, "1413", 763, 763, 20, 13, 2], [2, "1414", 807, 777, 46, 30, 2], [2, "688", 43, 880, 46, 24, 0], [2, "688", 51, 884, 46, 24, 0], [2, "3369", 68, 874, 44, 81, 0], [2, "3369", 28, 894, 44, 81, 0], [2, "3369", -12, 914, 44, 81, 0], [2, "1399", 61, 940, 30, 62, 0], [2, "3376", 43, 929, 58, 82, 0], [2, "1398", 4, 952, 28, 66, 2], [2, "3370", 89, 914, 12, 12, 0], [2, "3370", 96, 918, 12, 12, 0], [2, "3370", 99, 961, 12, 12, 0], [2, "3370", 40, 978, 12, 12, 0], [2, "3370", 28, 940, 12, 12, 0], [2, "3370", -15, 990, 12, 12, 0], [2, "3370", 0, 1016, 12, 12, 0], [2, "3403", 362, 782, 12, 12, 0], [2, "3403", 389, 828, 12, 12, 0], [2, "1207", 374, 776, 22, 81, 0], [2, "3360", 319, 799, 58, 57, 0], [2, "3404", 525, 855, 10, 11, 0], [2, "875", 98, 894, 14, 34, 0], [2, "1411", 33, 961, 26, 41, 0], [2, "1409", 23, 1004, 26, 27, 0], [2, "1409_1", 7, 1013, 26, 27, 0], [2, "688", 226, 953, 46, 24, 0], [2, "688", 234, 957, 46, 24, 0], [2, "688", 188, 972, 46, 24, 0], [2, "688", 196, 976, 46, 24, 0], [2, "688", 150, 991, 46, 24, 0], [2, "688", 158, 995, 46, 24, 0], [2, "688", 112, 1010, 46, 24, 0], [2, "688", 120, 1014, 46, 24, 0], [2, "688", 73, 1029, 46, 24, 0], [2, "688", 81, 1033, 46, 24, 0], [2, "688", 48, 1042, 46, 24, 0], [2, "688", 56, 1046, 46, 24, 0], [2, "688", 9, 1060, 46, 24, 0], [2, "688", 17, 1064, 46, 24, 0], [2, "688", -29, 1079, 46, 24, 0], [2, "688", -21, 1083, 46, 24, 0], [2, "688", -67, 1098, 46, 24, 0], [2, "688", -59, 1102, 46, 24, 0], [2, "688", -105, 1117, 46, 24, 0], [2, "688", -97, 1121, 46, 24, 0], [2, "688", -144, 1136, 46, 24, 0], [2, "688", -136, 1140, 46, 24, 0], [2, "688", -169, 1149, 46, 24, 0], [2, "688", -161, 1153, 46, 24, 0], [2, "688", 89, 870, 46, 24, 2], [2, "688", 97, 874, 46, 24, 2], [2, "688", 80, 874, 46, 24, 2], [2, "688", 88, 878, 46, 24, 2], [2, "688", 135, 892, 46, 24, 2], [2, "688", 143, 896, 46, 24, 2], [2, "688", 126, 896, 46, 24, 2], [2, "688", 134, 900, 46, 24, 2], [2, "688", 181, 915, 46, 24, 2], [2, "688", 189, 919, 46, 24, 2], [2, "688", 172, 919, 46, 24, 2], [2, "688", 180, 923, 46, 24, 2], [2, "688", 227, 938, 46, 24, 2], [2, "688", 235, 942, 46, 24, 2], [2, "688", 218, 942, 46, 24, 2], [2, "688", 226, 946, 46, 24, 2], [2, "3397", 842, 602, 44, 81, 0], [2, "3397", 886, 624, 44, 81, 0], [2, "3397", 930, 646, 44, 81, 0], [2, "3397", 975, 669, 44, 81, 0], [2, "3398", 1020, 670, 44, 80, 0], [2, "3398", 1045, 657, 44, 80, 0], [2, "891", 893, 642, 54, 75, 2], [2, "3082", 872, 687, 76, 40, 2], [2, "1279", 847, 599, 42, 58, 0], [2, "1279", 970, 660, 42, 58, 0], [2, "3399", 854, 653, 12, 12, 0], [2, "3399", 889, 607, 12, 12, 0], [2, "3399", 845, 588, 12, 12, 0], [2, "3399", 960, 648, 12, 12, 0], [2, "3399", 1004, 731, 12, 12, 0], [2, "3399", 999, 721, 12, 12, 0], [2, "3400", 1025, 682, 10, 11, 0], [2, "3400", 1026, 726, 10, 11, 0], [2, "1191", 1008, 657, 14, 64, 0], [2, "1191", 1008, 687, 14, 64, 0], [2, "1191", 1008, 657, 14, 64, 0], [2, "1191", 833, 568, 14, 64, 0], [2, "1191", 833, 599, 14, 64, 0], [2, "1191", 1022, 657, 14, 64, 2], [2, "1191", 1022, 687, 14, 64, 2], [2, "1197", 837, 641, 54, 44, 0], [2, "1197", 960, 700, 54, 44, 0], [2, "890", 872, 628, 94, 105, 2], [2, "1197", 841, 568, 54, 44, 0], [2, "1197", 890, 593, 54, 44, 0], [2, "1197", 940, 618, 54, 44, 0], [2, "1197", 958, 627, 54, 44, 0], [2, "1416", 997, 934, 24, 17, 0], [2, "1416", 903, 822, 24, 17, 0], [2, "688", 1190, 726, 46, 24, 0], [2, "688", 1198, 730, 46, 24, 0], [2, "688", 1152, 745, 46, 24, 0], [2, "688", 1160, 749, 46, 24, 0], [2, "688", 1114, 764, 46, 24, 0], [2, "688", 1122, 768, 46, 24, 0], [2, "688", 1076, 783, 46, 24, 0], [2, "688", 1084, 787, 46, 24, 0], [2, "688", 1037, 802, 46, 24, 0], [2, "688", 1045, 806, 46, 24, 0], [2, "688", 1012, 815, 46, 24, 0], [2, "688", 1020, 819, 46, 24, 0], [2, "688", 974, 834, 46, 24, 0], [2, "688", 982, 838, 46, 24, 0], [2, "688", 847, 560, 46, 24, 0], [2, "688", 855, 564, 46, 24, 0], [2, "688", 733, 617, 46, 24, 0], [2, "688", 741, 621, 46, 24, 0], [2, "688", 694, 636, 46, 24, 0], [2, "688", 702, 640, 46, 24, 0], [2, "688", 669, 649, 46, 24, 0], [2, "688", 677, 653, 46, 24, 0], [2, "688", 631, 668, 46, 24, 0], [2, "688", 639, 672, 46, 24, 0], [2, "688", 1092, 600, 46, 24, 2], [2, "688", 1100, 604, 46, 24, 2], [2, "688", 1083, 604, 46, 24, 2], [2, "688", 1091, 608, 46, 24, 2], [2, "688", 1049, 578, 46, 24, 2], [2, "688", 1057, 582, 46, 24, 2], [2, "688", 1040, 582, 46, 24, 2], [2, "688", 1048, 586, 46, 24, 2], [2, "1414", 808, 703, 46, 30, 2], [2, "1414", 808, 703, 46, 30, 2], [2, "1414", 786, 690, 46, 30, 2], [2, "1414", 762, 677, 46, 30, 2], [2, "1414", 762, 677, 46, 30, 2], [2, "1414", 741, 666, 46, 30, 2], [2, "1414", 741, 666, 46, 30, 2], [2, "1414", 911, 745, 46, 30, 2], [2, "1414", 911, 745, 46, 30, 2], [2, "1414", 953, 720, 46, 30, 2], [2, "1414", 953, 720, 46, 30, 2], [2, "1414", 981, 738, 46, 30, 2], [2, "1414", 981, 738, 46, 30, 2], [2, "1414", 851, 676, 46, 30, 2], [2, "1414", 837, 692, 46, 30, 2], [2, "1414", 936, 762, 46, 30, 2], [2, "1414", 936, 762, 46, 30, 2], [2, "1414", 968, 771, 46, 30, 2], [2, "1414", 995, 778, 46, 30, 0], [2, "1414", 1026, 769, 46, 30, 0], [2, "1414", 1051, 756, 46, 30, 0], [2, "1414", 1087, 742, 46, 30, 0], [2, "1414", 1098, 723, 46, 30, 0], [2, "1414", 362, 837, 46, 30, 2], [2, "1414", 451, 901, 46, 30, 2], [2, "1412", 495, 900, 32, 28, 0], [2, "1414", 545, 870, 46, 30, 0], [2, "1414", 545, 870, 46, 30, 0], [2, "1414", 606, 854, 46, 30, 0], [2, "1414", 643, 851, 46, 30, 0], [2, "1414", 27, 1015, 46, 30, 0], [2, "1414", -1, 1031, 46, 30, 0], [2, "1414", -27, 1027, 46, 30, 0], [2, "1416", 297, 1085, 24, 17, 0], [2, "1416", 356, 1084, 24, 17, 0], [2, "1414", 354, 1058, 46, 30, 2], [2, "1414", 406, 1103, 46, 30, 0], [2, "1414", 431, 1090, 46, 30, 0], [2, "1414", 642, 1105, 46, 30, 0], [2, "1414", 672, 1092, 46, 30, 0], [2, "1414", 597, 1129, 46, 30, 0], [2, "1414", 622, 1116, 46, 30, 0], [2, "1414", 711, 1082, 46, 30, 0], [2, "1414", 759, 1068, 46, 30, 0], [2, "1411", 618, 1071, 26, 41, 0], [2, "1411", 682, 1040, 26, 41, 0], [2, "688", 214, 446, 46, 24, 0], [2, "688", 222, 450, 46, 24, 0], [2, "688", 176, 465, 46, 24, 0], [2, "688", 184, 469, 46, 24, 0], [2, "688", 138, 484, 46, 24, 0], [2, "688", 146, 488, 46, 24, 0], [2, "688", 100, 503, 46, 24, 0], [2, "688", 108, 507, 46, 24, 0], [2, "688", 62, 522, 46, 24, 0], [2, "688", 70, 526, 46, 24, 0], [2, "688", 24, 541, 46, 24, 0], [2, "688", 32, 545, 46, 24, 0], [2, "688", -15, 560, 46, 24, 0], [2, "688", -7, 564, 46, 24, 0], [2, "688", -40, 573, 46, 24, 0], [2, "688", -32, 577, 46, 24, 0], [2, "688", 468, 318, 46, 24, 0], [2, "688", 476, 322, 46, 24, 0], [2, "688", 430, 337, 46, 24, 0], [2, "688", 438, 341, 46, 24, 0], [2, "688", 392, 356, 46, 24, 0], [2, "688", 400, 360, 46, 24, 0], [2, "688", 354, 375, 46, 24, 0], [2, "688", 362, 379, 46, 24, 0], [2, "688", 316, 394, 46, 24, 0], [2, "688", 324, 398, 46, 24, 0], [2, "688", 277, 413, 46, 24, 0], [2, "688", 285, 417, 46, 24, 0], [2, "688", 252, 426, 46, 24, 0], [2, "688", 260, 430, 46, 24, 0], [2, "688", 798, 153, 46, 24, 0], [2, "688", 806, 157, 46, 24, 0], [2, "688", 760, 172, 46, 24, 0], [2, "688", 768, 176, 46, 24, 0], [2, "688", 722, 191, 46, 24, 0], [2, "688", 730, 195, 46, 24, 0], [2, "688", 684, 210, 46, 24, 0], [2, "688", 692, 214, 46, 24, 0], [2, "688", 729, 414, 46, 24, 2], [2, "688", 737, 418, 46, 24, 2], [2, "688", 720, 418, 46, 24, 2], [2, "688", 728, 422, 46, 24, 2], [2, "688", 775, 437, 46, 24, 2], [2, "688", 783, 441, 46, 24, 2], [2, "688", 766, 441, 46, 24, 2], [2, "688", 774, 445, 46, 24, 2], [2, "688", 821, 460, 46, 24, 2], [2, "688", 829, 464, 46, 24, 2], [2, "688", 812, 464, 46, 24, 2], [2, "688", 820, 468, 46, 24, 2], [2, "688", 867, 483, 46, 24, 2], [2, "688", 875, 487, 46, 24, 2], [2, "688", 858, 487, 46, 24, 2], [2, "688", 866, 491, 46, 24, 2], [2, "688", 301, 625, 46, 24, 2], [2, "688", 309, 629, 46, 24, 2], [2, "688", 292, 629, 46, 24, 2], [2, "688", 300, 633, 46, 24, 2], [2, "688", 347, 648, 46, 24, 2], [2, "688", 355, 652, 46, 24, 2], [2, "688", 338, 652, 46, 24, 2], [2, "688", 346, 656, 46, 24, 2], [2, "688", 407, 571, 46, 24, 2], [2, "688", 415, 575, 46, 24, 2], [2, "688", 398, 575, 46, 24, 2], [2, "688", 406, 579, 46, 24, 2], [2, "688", 453, 594, 46, 24, 2], [2, "688", 461, 598, 46, 24, 2], [2, "688", 444, 598, 46, 24, 2], [2, "688", 452, 602, 46, 24, 2], [2, "688", 490, 612, 46, 24, 2], [2, "688", 498, 616, 46, 24, 2], [2, "688", 481, 616, 46, 24, 2], [2, "688", 489, 620, 46, 24, 2], [2, "688", 536, 635, 46, 24, 2], [2, "688", 544, 639, 46, 24, 2], [2, "688", 527, 639, 46, 24, 2], [2, "688", 535, 643, 46, 24, 2], [2, "688", 585, 658, 46, 24, 2], [2, "688", 593, 662, 46, 24, 2], [2, "688", 576, 662, 46, 24, 2], [2, "688", 584, 666, 46, 24, 2], [2, "688", 631, 681, 46, 24, 2], [2, "688", 639, 685, 46, 24, 2], [2, "688", 622, 685, 46, 24, 2], [2, "688", 630, 689, 46, 24, 2], [2, "688", 675, 702, 46, 24, 2], [2, "688", 683, 706, 46, 24, 2], [2, "688", 666, 706, 46, 24, 2], [2, "688", 674, 710, 46, 24, 2], [2, "688", 721, 725, 46, 24, 2], [2, "688", 729, 729, 46, 24, 2], [2, "688", 712, 729, 46, 24, 2], [2, "688", 720, 733, 46, 24, 2], [2, "688", 768, 747, 46, 24, 2], [2, "688", 776, 751, 46, 24, 2], [2, "688", 759, 751, 46, 24, 2], [2, "688", 767, 755, 46, 24, 2], [2, "688", 814, 770, 46, 24, 2], [2, "688", 822, 774, 46, 24, 2], [2, "688", 805, 774, 46, 24, 2], [2, "688", 813, 778, 46, 24, 2], [2, "688", 858, 791, 46, 24, 2], [2, "688", 866, 795, 46, 24, 2], [2, "688", 849, 795, 46, 24, 2], [2, "688", 857, 799, 46, 24, 2], [2, "688", 904, 814, 46, 24, 2], [2, "688", 912, 818, 46, 24, 2], [2, "688", 895, 818, 46, 24, 2], [2, "688", 903, 822, 46, 24, 2], [2, "688", 950, 836, 46, 24, 2], [2, "688", 958, 840, 46, 24, 2], [2, "688", 941, 840, 46, 24, 2], [2, "688", 949, 844, 46, 24, 2], [2, "688", 996, 859, 46, 24, 2], [2, "688", 1004, 863, 46, 24, 2], [2, "688", 987, 863, 46, 24, 2], [2, "688", 995, 867, 46, 24, 2], [2, "688", 1040, 880, 46, 24, 2], [2, "688", 1048, 884, 46, 24, 2], [2, "688", 1031, 884, 46, 24, 2], [2, "688", 1039, 888, 46, 24, 2], [2, "688", 1086, 903, 46, 24, 2], [2, "688", 1094, 907, 46, 24, 2], [2, "688", 1077, 907, 46, 24, 2], [2, "688", 1085, 911, 46, 24, 2], [2, "688", 1132, 926, 46, 24, 2], [2, "688", 1140, 930, 46, 24, 2], [2, "688", 1123, 930, 46, 24, 2], [2, "688", 1131, 934, 46, 24, 2], [2, "688", 215, 169, 46, 24, 2], [2, "688", 223, 173, 46, 24, 2], [2, "688", 206, 173, 46, 24, 2], [2, "688", 214, 177, 46, 24, 2], [2, "688", 260, 191, 46, 24, 2], [2, "688", 268, 195, 46, 24, 2], [2, "688", 251, 195, 46, 24, 2], [2, "688", 259, 199, 46, 24, 2], [2, "688", 306, 214, 46, 24, 2], [2, "688", 314, 218, 46, 24, 2], [2, "688", 297, 218, 46, 24, 2], [2, "688", 305, 222, 46, 24, 2], [2, "688", 352, 237, 46, 24, 2], [2, "688", 360, 241, 46, 24, 2], [2, "688", 343, 241, 46, 24, 2], [2, "688", 351, 245, 46, 24, 2], [2, "688", -6, 186, 46, 24, 2], [2, "688", 2, 190, 46, 24, 2], [2, "688", -15, 190, 46, 24, 2], [2, "688", -7, 194, 46, 24, 2], [2, "688", 40, 209, 46, 24, 2], [2, "688", 48, 213, 46, 24, 2], [2, "688", 31, 213, 46, 24, 2], [2, "688", 39, 217, 46, 24, 2], [2, "688", 83, 231, 46, 24, 2], [2, "688", 91, 235, 46, 24, 2], [2, "688", 74, 235, 46, 24, 2], [2, "688", 82, 239, 46, 24, 2], [2, "688", 129, 254, 46, 24, 2], [2, "688", 137, 258, 46, 24, 2], [2, "688", 120, 258, 46, 24, 2], [2, "688", 128, 262, 46, 24, 2], [2, "688", 175, 277, 46, 24, 2], [2, "688", 183, 281, 46, 24, 2], [2, "688", 166, 281, 46, 24, 2], [2, "688", 174, 285, 46, 24, 2], [2, "688", 221, 300, 46, 24, 2], [2, "688", 229, 304, 46, 24, 2], [2, "688", 212, 304, 46, 24, 2], [2, "688", 220, 308, 46, 24, 2], [2, "688", 267, 323, 46, 24, 2], [2, "688", 275, 327, 46, 24, 2], [2, "688", 258, 327, 46, 24, 2], [2, "688", 266, 331, 46, 24, 2], [2, "688", 313, 346, 46, 24, 2], [2, "688", 321, 350, 46, 24, 2], [2, "688", 304, 350, 46, 24, 2], [2, "688", 312, 354, 46, 24, 2], [2, "688", 346, 363, 46, 24, 2], [2, "688", 354, 367, 46, 24, 2], [2, "688", 337, 367, 46, 24, 2], [2, "688", 345, 371, 46, 24, 2], [2, "3393", 1111, 763, 80, 62, 0], [2, "3393", 1152, 781, 80, 62, 0], [2, "3393", 1070, 782, 80, 62, 0], [2, "3393", 1111, 800, 80, 62, 0], [2, "3393", 1037, 798, 80, 62, 0], [2, "3393", 1078, 816, 80, 62, 0], [2, "3393", 996, 816, 80, 62, 0], [2, "3393", 1037, 835, 80, 62, 0], [2, "3393", 1118, 832, 80, 62, 0], [2, "3393", 1074, 853, 80, 62, 0], [2, "3393", 1114, 872, 80, 62, 0], [2, "1414", 1096, 815, 46, 30, 2], [2, "1416", 1054, 831, 24, 17, 0], [2, "1416", 1092, 866, 24, 17, 0], [2, "1412", 1081, 803, 32, 28, 0], [2, "1409_1", 296, 802, 26, 27, 0], [2, "1409", 370, 833, 26, 27, 0], [2, "1409", 441, 883, 26, 27, 0], [2, "1409", 505, 891, 26, 27, 0], [2, "1412", 959, 823, 32, 28, 0], [2, "3408", 725, 1107, 42, 26, 0], [2, "3408", 739, 1118, 42, 26, 0], [2, "1409_1", 862, 677, 26, 27, 0], [2, "1409", 851, 688, 26, 27, 0], [2, "1411", 1009, 661, 26, 41, 0], [2, "1409", 840, 954, 26, 27, 0], [2, "1409", 958, 713, 26, 27, 0], [2, "1409_1", 978, 722, 26, 27, 0], [2, "1443", 902, 619, 34, 41, 0], [2, "1410", 904, 619, 22, 32, 0], [2, "1411", 375, 776, 26, 41, 0], [2, "1411", 506, 840, 26, 41, 0], [2, "1412", 298, 982, 32, 28, 0], [2, "1412", 271, 994, 32, 28, 0], [2, "1414", 76, 993, 46, 30, 0], [2, "1414", 72, 1004, 46, 30, 0], [2, "1414", 93, 1011, 46, 30, 0], [2, "1414", 238, 944, 46, 30, 0], [2, "1414", 228, 962, 46, 30, 0], [2, "1414", 233, 983, 46, 30, 0], [2, "1414", 249, 963, 46, 30, 0], [2, "1414", 163, 1063, 46, 30, 0], [2, "1414", 163, 1063, 46, 30, 0], [2, "1414", 149, 1077, 46, 30, 0], [2, "1414", 171, 1080, 46, 30, 0], [2, "1416", 739, 911, 24, 17, 0], [2, "1416", 567, 957, 24, 17, 0], [2, "1416", 875, 1055, 24, 17, 0], [2, "1416", 937, 1062, 24, 17, 0], [2, "1416", 867, 764, 24, 17, 0], [2, "1416", 704, 601, 24, 17, 0], [2, "1416", 704, 601, 24, 17, 0], [2, "1416", 580, 586, 24, 17, 0], [2, "1416", 480, 439, 24, 17, 0], [2, "1416", 580, 586, 24, 17, 0], [2, "1416", 580, 586, 24, 17, 0], [2, "1416", 300, 494, 24, 17, 0], [2, "1416", 334, 553, 24, 17, 0], [2, "1416", 334, 553, 24, 17, 0], [2, "1416", 443, 413, 24, 17, 0], [2, "1416", 623, 327, 24, 17, 0], [2, "1416", 623, 327, 24, 17, 0], [2, "1416", 865, 421, 24, 17, 0], [2, "1416", 941, 475, 24, 17, 0], [2, "1416", 941, 475, 24, 17, 0], [2, "1416", 773, 559, 24, 17, 0], [2, "1413", 703, 878, 20, 13, 0], [2, "1413", 560, 921, 20, 13, 0], [2, "1413", 560, 921, 20, 13, 0], [2, "1413", 444, 943, 20, 13, 0], [2, "1413", 429, 945, 20, 13, 0], [2, "1413", 212, 824, 20, 13, 0], [2, "1413", 227, 823, 20, 13, 0], [2, "1413", 136, 890, 20, 13, 0], [2, "1413", 132, 899, 20, 13, 0], [2, "1413", 132, 899, 20, 13, 0], [2, "1413", 164, 1006, 20, 13, 0], [2, "1413", 47, 1060, 20, 13, 0], [2, "1413", 41, 1068, 20, 13, 0], [2, "1413", 98, 1093, 20, 13, 0], [2, "1413", 463, 989, 20, 13, 0], [2, "1413", 281, 863, 20, 13, 0], [2, "1413", 204, 745, 20, 13, 0], [2, "1413", 114, 765, 20, 13, 0], [2, "1413", 114, 765, 20, 13, 0], [2, "1413", 770, 957, 20, 13, 0], [2, "1413", 770, 957, 20, 13, 0], [2, "1413", 956, 1036, 20, 13, 0], [2, "1413", 1024, 1003, 20, 13, 0], [2, "1413", 960, 899, 20, 13, 0], [2, "1413", 655, 731, 20, 13, 0], [2, "1413", 643, 689, 20, 13, 0], [2, "1413", 643, 689, 20, 13, 0], [2, "1413", 579, 632, 20, 13, 0], [2, "1413", 579, 632, 20, 13, 0], [2, "1413", 561, 639, 20, 13, 0], [2, "1413", 453, 610, 20, 13, 0], [2, "1413", 450, 522, 20, 13, 0], [2, "1413", 399, 546, 20, 13, 0], [2, "1413", 501, 501, 20, 13, 0], [2, "1414", 465, 501, 46, 30, 0], [2, "1414", 433, 529, 46, 30, 0], [2, "1414", 444, 547, 46, 30, 2], [2, "1414", 428, 551, 46, 30, 2], [2, "1414", 553, 546, 46, 30, 2], [2, "1414", 553, 546, 46, 30, 2], [2, "1414", 553, 565, 46, 30, 2], [2, "1414", 553, 565, 46, 30, 2], [2, "1414", 572, 563, 46, 30, 2], [2, "1414", 572, 563, 46, 30, 2], [2, "1414", 534, 574, 46, 30, 2], [2, "1414", 602, 559, 46, 30, 2], [2, "1414", 619, 550, 46, 30, 2], [2, "1414", 634, 544, 46, 30, 0], [2, "1414", 642, 518, 46, 30, 0], [2, "1414", 642, 518, 46, 30, 0], [2, "1414", 658, 529, 46, 30, 0], [2, "1414", 648, 497, 46, 30, 0], [2, "1414", 640, 478, 46, 30, 0], [2, "1414", 684, 475, 46, 30, 0], [2, "1414", 711, 468, 46, 30, 2], [2, "1413", 663, 426, 20, 13, 0], [2, "1413", 854, 482, 20, 13, 0], [2, "1413", 785, 536, 20, 13, 0], [2, "1413", 601, 665, 20, 13, 0], [2, "1413", 714, 649, 20, 13, 0], [2, "1413", 365, 913, 20, 13, 0], [2, "1413", 32, 750, 20, 13, 0], [2, "1413", 110, 694, 20, 13, 0], [2, "1413", 174, 494, 20, 13, 0], [2, "1412", 926, 1052, 32, 28, 0], [2, "1412", 589, 944, 32, 28, 2], [2, "1413", 114, 503, 20, 13, 0], [2, "1413", 213, 454, 20, 13, 0], [2, "1413", 357, 379, 20, 13, 0], [2, "1413", 415, 375, 20, 13, 0], [2, "1413", 462, 351, 20, 13, 0], [2, "1413", 741, 380, 20, 13, 0], [2, "1413", 741, 380, 20, 13, 0], [2, "1414", 787, 343, 46, 30, 0], [2, "1414", 819, 328, 46, 30, 0], [2, "1413", 705, 228, 20, 13, 0], [2, "1413", 748, 206, 20, 13, 0], [2, "1413", 795, 181, 20, 13, 0], [2, "1413", 881, 308, 20, 13, 0], [2, "1413", 930, 283, 20, 13, 0], [2, "1413", 1001, 249, 20, 13, 0], [2, "1413", 809, 155, 20, 13, 0], [2, "1413", 66, 525, 20, 13, 0], [2, "3367", 515, -60, 44, 81, 0], [2, "3367", 560, -37, 44, 81, 0], [2, "3367", 605, -14, 44, 81, 0], [2, "3367", 650, 9, 44, 81, 0], [2, "3367", 694, 31, 44, 81, 0], [2, "3367", 739, 54, 44, 81, 0], [2, "3367", 784, 77, 44, 81, 0], [2, "3367", 1054, 203, 44, 81, 0], [2, "3367", 1099, 226, 44, 81, 0], [2, "3367", 515, -119, 44, 81, 0], [2, "3367", 560, -96, 44, 81, 0], [2, "3367", 605, -73, 44, 81, 0], [2, "3367", 650, -50, 44, 81, 0], [2, "3367", 694, -28, 44, 81, 0], [2, "3367", 739, -5, 44, 81, 0], [2, "3367", 784, 18, 44, 81, 0], [2, "3367", 1054, 144, 44, 81, 0], [2, "3367", 1099, 167, 44, 81, 0], [2, "3367", 515, -177, 44, 81, 0], [2, "3367", 560, -154, 44, 81, 0], [2, "3367", 605, -131, 44, 81, 0], [2, "3367", 650, -108, 44, 81, 0], [2, "3367", 694, -86, 44, 81, 0], [2, "3367", 739, -63, 44, 81, 0], [2, "3367", 784, -40, 44, 81, 0], [2, "3367", 1054, 97, 44, 81, 0], [2, "3367", 1099, 120, 44, 81, 0], [2, "3367", 1054, 97, 44, 81, 0], [2, "3367", 1099, 120, 44, 81, 0], [2, "3367", 1054, 38, 44, 81, 0], [2, "3367", 1054, -20, 44, 81, 0], [2, "3367", 1099, 3, 44, 81, 0], [2, "3367", 1099, 61, 44, 81, 0], [2, "3367", 1054, -78, 44, 81, 0], [2, "3367", 1054, -136, 44, 81, 0], [2, "3367", 1099, -113, 44, 81, 0], [2, "3367", 1099, -55, 44, 81, 0], [2, "3378", 789, 131, 62, 42, 0], [2, "3377", 797, 86, 46, 67, 2], [2, "3378", 789, 68, 62, 42, 0], [2, "3377", 797, 23, 46, 67, 2], [2, "3378", 789, 22, 62, 42, 0], [2, "3369", 844, -34, 44, 81, 0], [2, "3369", 889, -4, 44, 81, 0], [2, "3369", 889, -57, 44, 81, 0], [2, "3369", 932, -25, 44, 81, 0], [2, "3369", 932, 34, 44, 81, 0], [2, "3369", 932, -78, 44, 81, 0], [2, "3369", 977, -48, 44, 81, 0], [2, "3369", 977, 11, 44, 81, 0], [2, "3369", 977, -101, 44, 81, 0], [2, "3367", 921, 1, 44, 81, 0], [2, "3367", 876, -22, 44, 81, 0], [2, "3367", 832, -44, 44, 81, 0], [2, "3367", 965, 23, 44, 81, 0], [2, "3367", 1009, 45, 44, 81, 0], [2, "3367", 1054, 68, 44, 81, 0], [2, "3367", 921, -57, 44, 81, 0], [2, "3367", 876, -80, 44, 81, 0], [2, "3367", 965, -35, 44, 81, 0], [2, "3367", 1009, -13, 44, 81, 0], [2, "3367", 1009, -71, 44, 81, 0], [2, "3378", 1003, 172, 62, 42, 0], [2, "1279", 1108, 132, 42, 58, 0], [2, "1279", 1114, 7, 42, 58, 0], [2, "1279", 668, -39, 42, 58, 0], [2, "1208", 505, -32, 52, 56, 0], [2, "1208", 553, -8, 52, 56, 0], [2, "1208", 597, 14, 52, 56, 0], [2, "1208", 643, 37, 52, 56, 0], [2, "1208", 689, 61, 52, 56, 0], [2, "1208", 736, 85, 52, 56, 0], [2, "1208", 746, 90, 52, 56, 0], [2, "1208", 1092, 71, 52, 56, 0], [2, "1208", 1054, 235, 52, 56, 0], [2, "1208", 1102, 260, 52, 56, 0], [2, "3368", 762, 72, 12, 12, 0], [2, "3368", 773, 62, 12, 12, 0], [2, "3368", 763, 37, 12, 12, 0], [2, "3368", 655, 9, 12, 12, 0], [2, "3368", 700, 30, 12, 12, 0], [2, "3368", 706, 47, 12, 12, 0], [2, "3368", 1083, 17, 12, 12, 0], [2, "3368", 1090, 28, 12, 12, 0], [2, "3368", 1117, 67, 12, 12, 0], [2, "3368", 780, 58, 12, 12, 0], [2, "3368", 1093, 138, 12, 12, 0], [2, "3368", 1125, 233, 12, 12, 0], [2, "3368", 1112, 232, 12, 12, 0], [2, "3368", 1091, 205, 12, 12, 0], [2, "3368", 1101, 176, 12, 12, 0], [2, "3370", 828, 71, 12, 12, 0], [2, "3370", 1044, 217, 12, 12, 0], [2, "3368", 800, 111, 12, 12, 0], [2, "3368", 1015, 240, 12, 12, 0], [2, "3378", 1003, 236, 62, 42, 0], [2, "3377", 1013, 191, 46, 67, 2], [2, "3378", 1003, 172, 62, 42, 0], [2, "3370", 1040, 240, 12, 12, 0], [2, "3368", 1015, 213, 12, 12, 0], [2, "3403", 971, 353, 12, 12, 0], [2, "688", 649, 229, 46, 24, 0], [2, "688", 657, 233, 46, 24, 0], [2, "688", 611, 248, 46, 24, 0], [2, "688", 619, 252, 46, 24, 0], [2, "688", 573, 267, 46, 24, 0], [2, "688", 581, 271, 46, 24, 0], [2, "688", 581, 262, 46, 24, 0], [2, "688", 589, 266, 46, 24, 0], [2, "688", 543, 281, 46, 24, 0], [2, "688", 551, 285, 46, 24, 0], [2, "688", 505, 300, 46, 24, 0], [2, "688", 513, 304, 46, 24, 0], [2, "688", 442, 282, 46, 24, 2], [2, "688", 450, 286, 46, 24, 2], [2, "688", 433, 286, 46, 24, 2], [2, "688", 441, 290, 46, 24, 2], [2, "688", 396, 259, 46, 24, 2], [2, "688", 404, 263, 46, 24, 2], [2, "688", 387, 263, 46, 24, 2], [2, "688", 395, 267, 46, 24, 2], [2, "688", 460, 292, 46, 24, 2], [2, "688", 468, 296, 46, 24, 2], [2, "688", 451, 296, 46, 24, 2], [2, "688", 459, 300, 46, 24, 2], [2, "3372", 31, 441, 44, 81, 0], [2, "3372", 73, 420, 44, 81, 0], [2, "3372", 31, 441, 44, 81, 0], [2, "3372", 115, 400, 44, 81, 0], [2, "3372", 144, 386, 44, 81, 0], [2, "3372", 162, 377, 44, 81, 0], [2, "3371", -14, 440, 44, 81, 0], [2, "3408", 731, 1051, 42, 26, 0], [2, "3408", 731, 1051, 42, 26, 0], [2, "3408", 751, 1062, 42, 26, 0], [2, "3408", 723, 1089, 42, 26, 0], [2, "3408", 723, 1089, 42, 26, 0], [2, "3408", 695, 1104, 42, 26, 0], [2, "3408", 695, 1104, 42, 26, 0], [2, "3408", 666, 1119, 42, 26, 0], [2, "1414", 663, 1111, 46, 30, 0], [2, "1414", 719, 1103, 46, 30, 0], [2, "1414", 745, 1090, 46, 30, 0], [2, "265", 102, 421, 68, 77, 2], [2, "879", 129, 407, 26, 56, 0], [2, "3372", 31, 392, 44, 81, 0], [2, "3372", 73, 371, 44, 81, 0], [2, "3372", 118, 349, 44, 81, 0], [2, "3371", -14, 386, 44, 81, 0], [2, "1207", 24, 452, 22, 81, 0], [2, "1207", 24, 385, 22, 81, 0], [2, "1207", 195, 372, 22, 81, 0], [2, "3372", 162, 328, 44, 81, 0], [2, "1279", 45, 426, 42, 58, 2], [2, "894", 170, 435, 24, 20, 2], [2, "894", 61, 488, 24, 20, 2], [2, "894", 46, 495, 24, 20, 2], [2, "894", 114, 383, 24, 20, 2], [2, "894", 99, 390, 24, 20, 2], [2, "894", 114, 383, 24, 20, 2], [2, "894", 99, 390, 24, 20, 2], [2, "894", 154, 362, 24, 20, 2], [2, "894", 139, 369, 24, 20, 2], [2, "870", 118, 365, 12, 32, 0], [2, "875", 160, 367, 14, 34, 0], [2, "1409", 232, 653, 26, 27, 0], [2, "688", 64, 752, 46, 24, 2], [2, "688", 72, 756, 46, 24, 2], [2, "688", 55, 756, 46, 24, 2], [2, "688", 63, 760, 46, 24, 2], [2, "688", 110, 775, 46, 24, 2], [2, "688", 118, 779, 46, 24, 2], [2, "688", 101, 779, 46, 24, 2], [2, "688", 109, 783, 46, 24, 2], [2, "688", 156, 798, 46, 24, 2], [2, "688", 164, 802, 46, 24, 2], [2, "688", 147, 802, 46, 24, 2], [2, "688", 155, 806, 46, 24, 2], [2, "688", 37, 738, 46, 24, 2], [2, "688", 45, 742, 46, 24, 2], [2, "688", 28, 742, 46, 24, 2], [2, "688", 36, 746, 46, 24, 2], [2, "1413", 19, 738, 20, 13, 0], [2, "688", 33, 832, 46, 24, 2], [2, "688", 41, 836, 46, 24, 2], [2, "688", 24, 836, 46, 24, 2], [2, "688", 32, 840, 46, 24, 2], [2, "688", -13, 809, 46, 24, 2], [2, "688", -5, 813, 46, 24, 2], [2, "688", -22, 813, 46, 24, 2], [2, "688", -14, 817, 46, 24, 2], [2, "688", 355, 66, 46, 24, 0], [2, "688", 363, 70, 46, 24, 0], [2, "688", 317, 85, 46, 24, 0], [2, "688", 325, 89, 46, 24, 0], [2, "688", 280, 103, 46, 24, 0], [2, "688", 288, 107, 46, 24, 0], [2, "688", 242, 122, 46, 24, 0], [2, "688", 250, 126, 46, 24, 0], [2, "688", 204, 141, 46, 24, 0], [2, "688", 212, 145, 46, 24, 0], [2, "1409", 451, 559, 26, 27, 0], [2, "1409_1", 565, 571, 26, 27, 0], [2, "1409", 675, 523, 26, 27, 0], [2, "1409_1", 671, 537, 26, 27, 0], [2, "1409", 587, 576, 26, 27, 0], [2, "1409", 731, 465, 26, 27, 0], [2, "3356", 642, 136, 44, 49, 0], [2, "3356", 601, 119, 44, 49, 0], [2, "1409", 515, 142, 26, 27, 0], [2, "1409_1", 468, 143, 26, 27, 0], [2, "1409_1", 467, 161, 26, 27, 0], [2, "1409_1", 340, 182, 26, 27, 0], [2, "1409", 333, 196, 26, 27, 0], [2, "1409", 346, 213, 26, 27, 0], [2, "1409_1", 371, 221, 26, 27, 0], [2, "1409_1", 441, 255, 26, 27, 0], [2, "1409", 673, 160, 26, 27, 0], [2, "1409", 569, 171, 26, 27, 0], [2, "688", 175, 157, 46, 24, 0], [2, "688", 183, 161, 46, 24, 0], [2, "688", 138, 175, 46, 24, 0], [2, "688", 146, 179, 46, 24, 0], [2, "688", 100, 194, 46, 24, 0], [2, "688", 108, 198, 46, 24, 0], [2, "688", 62, 213, 46, 24, 0], [2, "688", 70, 217, 46, 24, 0], [2, "1409_1", 456, 178, 26, 27, 0], [2, "1409", 494, 159, 26, 27, 0], [2, "1409", 596, 165, 26, 27, 0], [2, "1414", 975, 269, 46, 30, 0], [2, "1414", 56, 491, 46, 30, 0], [2, "1414", 56, 491, 46, 30, 0], [2, "1414", 27, 508, 46, 30, 0], [2, "1414", 42, 509, 46, 30, 0], [2, "1414", 74, 497, 46, 30, 0], [2, "1414", 74, 497, 46, 30, 0], [2, "1414", 95, 493, 46, 30, 0], [2, "1414", 133, 485, 46, 30, 0], [2, "1414", 163, 440, 46, 30, 0], [2, "1414", 181, 448, 46, 30, 0], [2, "1412", 214, 429, 32, 28, 0], [2, "1412", 21, 521, 32, 28, 0], [2, "1414", -12, 519, 46, 30, 2], [2, "1409", 49, 497, 26, 27, 0], [2, "1409_1", 33, 512, 26, 27, 0], [2, "1409_1", 10, 512, 26, 27, 0], [2, "1409_1", 182, 436, 26, 27, 0], [2, "1409", 52, 226, 26, 27, 0], [2, "1409_1", -1, 234, 26, 27, 0], [2, "1409_1", -1, 234, 26, 27, 0], [2, "1409_1", 8, 278, 26, 27, 0], [2, "1409_1", 29, 279, 26, 27, 0], [2, "1409_1", 87, 72, 26, 27, 0], [2, "1409", 101, 79, 26, 27, 0], [2, "1409", 202, 39, 26, 27, 0], [2, "1409_1", 247, 63, 26, 27, 0], [2, "1409_1", 271, 49, 26, 27, 0], [2, "1409", 315, 36, 26, 27, 0], [2, "1409", 971, 428, 26, 27, 0], [2, "1409_1", 903, 394, 26, 27, 0], [2, "1409_1", 994, 435, 26, 27, 0], [2, "1409_1", 1012, 461, 26, 27, 0], [2, "1409", 1081, 458, 26, 27, 0], [2, "1409_1", 1073, 483, 26, 27, 0], [2, "1409_1", 1101, 311, 26, 27, 0], [2, "1409", 1044, 304, 26, 27, 0], [2, "1414", 155, 165, 46, 30, 0], [2, "1414", 315, 228, 46, 30, 0], [2, "1414", 306, 213, 46, 30, 0], [2, "1414", 207, 292, 46, 30, 0], [2, "1414", 171, 78, 46, 30, 0], [2, "1414", 143, 93, 46, 30, 0], [2, "1414", 143, 93, 46, 30, 0], [2, "1414", 121, 105, 46, 30, 0], [2, "1414", 102, 93, 46, 30, 0], [2, "1414", 67, 86, 46, 30, 2], [2, "1412", 110, 151, 32, 28, 0], [2, "1412", 6, 65, 32, 28, 0], [2, "1412", 270, 26, 32, 28, 0], [2, "1412", 363, 10, 32, 28, 0], [2, "1413", 152, 220, 20, 13, 0], [2, "1413", 264, 122, 20, 13, 0], [2, "1413", 264, 122, 20, 13, 0], [2, "1413", 281, 200, 20, 13, 0], [2, "1413", 281, 200, 20, 13, 0], [2, "1413", 437, 270, 20, 13, 0], [2, "1413", 437, 270, 20, 13, 0], [2, "1413", 483, 196, 20, 13, 0], [2, "1413", 483, 196, 20, 13, 0], [2, "1413", 443, 74, 20, 13, 0], [2, "1413", 443, 74, 20, 13, 0], [2, "1413", 538, 37, 20, 13, 0], [2, "1413", 538, 37, 20, 13, 0], [2, "1413", 627, 88, 20, 13, 0], [2, "1413", 627, 88, 20, 13, 0], [2, "1413", 733, 147, 20, 13, 0], [2, "1413", 640, 321, 20, 13, 0], [2, "1413", 640, 321, 20, 13, 0], [2, "1413", 534, 426, 20, 13, 0], [2, "1413", 352, 302, 20, 13, 0], [2, "1413", 239, 376, 20, 13, 0], [2, "1413", 310, 356, 20, 13, 0], [2, "1413", 116, 251, 20, 13, 0], [2, "1413", 96, 216, 20, 13, 0], [2, "1413", 11, 312, 20, 13, 0], [2, "1413", 96, 583, 20, 13, 0], [2, "1413", 22, 562, 20, 13, 0], [2, "1413", 303, 615, 20, 13, 0], [2, "1413", 661, 599, 20, 13, 0], [2, "1413", 835, 393, 20, 13, 0], [2, "1413", 775, 275, 20, 13, 0], [2, "1413", 1055, 530, 20, 13, 0], [2, "1413", 973, 475, 20, 13, 0], [2, "1416", 256, 265, 24, 17, 0], [2, "1416", 265, 353, 24, 17, 0], [2, "1416", 76, 136, 24, 17, 0], [2, "1416", 236, 132, 24, 17, 0], [2, "1416", 236, 132, 24, 17, 0], [2, "1416", 331, 103, 24, 17, 0], [2, "1416", 331, 103, 24, 17, 0], [2, "1416", 301, 158, 24, 17, 0], [2, "1416", 652, 109, 24, 17, 0], [2, "1416", 652, 109, 24, 17, 0], [2, "1416", 559, 57, 24, 17, 0], [2, "1416", 441, 6, 24, 17, 0], [2, "1416", 1003, 312, 24, 17, 0], [2, "1416", 1003, 312, 24, 17, 0], [2, "1416", 866, 229, 24, 17, 0], [2, "1416", 754, 318, 24, 17, 0], [2, "1416", 1004, 520, 24, 17, 0], [2, "1416", 846, 470, 24, 17, 0], [2, "1416", 846, 470, 24, 17, 0], [2, "1416", 1054, 506, 24, 17, 0], [2, "1412", 609, 604, 32, 28, 0], [2, "1412", 609, 604, 32, 28, 0], [2, "1412", 805, 499, 32, 28, 0], [2, "1412", 851, 353, 32, 28, 0], [2, "1412", 851, 353, 32, 28, 0], [2, "1412", 1032, 497, 32, 28, 0], [2, "1412", 1032, 497, 32, 28, 0], [2, "1412", 648, 98, 32, 28, 0], [2, "1412", 595, 42, 32, 28, 2], [2, "1412", 341, 98, 32, 28, 0], [2, "1414", 507, 1, 46, 30, 2], [2, "1414", 531, 18, 46, 30, 2], [2, "1414", 618, 58, 46, 30, 2], [2, "1414", 742, 118, 46, 30, 2], [2, "688", 528, 485, 46, 24, 0], [2, "688", 536, 489, 46, 24, 0], [2, "688", 553, 472, 46, 24, 0], [2, "688", 561, 476, 46, 24, 0], [2, "890", 85, 401, 94, 105, 0], [2, "1414", 259, 438, 46, 30, 0], [2, "1414", 300, 419, 46, 30, 0], [2, "1413", 66, 718, 20, 13, 0], [2, "1413", 1039, 1006, 20, 13, 0], [2, "1413", 1092, 1029, 20, 13, 0], [2, "3990", 477, 195, 68, 85, 0], [2, "3990", 546, 195, 68, 85, 2]]}, {"type": 2, "data": [22, -1, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, 22, 22, 22, 22, 22, 22, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, 22, 22, 22, 22, 22, 22, 4, 5, 7, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 23, 22, -1, -1, 16, 17, -1, 19, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, 22, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 16, 17, 16, 17, 23, 22, 22, 7, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, 16, 17, 22, 13, 19, 13, 14, 20, 19, 13, 13, 18, -1, -1, -1, -1, 39, 31, 30, 29, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 11, 10, 22, 13, 14, -1, -1, -1, -1, -1, -1, 8, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, -1, -1, -1, -1, 10, 10, 10, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, 1, 2, -1, 8, 7, 22, 11, 10, 24, 5, 25, -1, -1, -1, -1, -1, -1, -1, -1, 30, 29, 22, 22, 22, -1, -1, -1, -1, 10, 10, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 4, 5, -1, 11, 10, 22, 24, 25, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, -1, -1, -1, -1, -1, 16, 16, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, 22, 24, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 29, 22, 22, -1, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 29, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 10, 10, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, -1, 1, 2, 20, 19, 23, 22, 10, 22, 22, 22, 22, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 5, 6, -1, 20, 19, -1, 22, 22, 22, 22, 22, 22, 22, 22, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, 17, 19, 18, 8, 7, -1, -1, 22, 22, 22, 22, 22, -1, 16, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, 8, 11, 10, -1, 22, -1, 22, 22, -1, 16, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 22, -1, 22, 22, 10, 16, 16, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 22, 16, 17, -1, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 19, 19, 16, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 22, 16, 16, 13, 14, -1, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, -1, 41, 40, 39, 22, 4, 16, 16, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 22, 22, 22, 16, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 10, 22, 22, 16, 17, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 39, 16, 22, 22, 22, 22, 22, 22, 22, 22, 22, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, -1, 16, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, 39, 16, 22, 22, 22, 22, 22, 22, 22, 16, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 29, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 16, 16, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, 22, 22, 22, 22, 16, 16, 22, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, 15, 16, 22, 9, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, 14, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 22, -1, -1, -1, -1, -1, -1, 22, 34, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 16, 17, -1, 10, 22, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 29, 22, 16, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, -1, -1, 21, -1, -1, -1, -1, -1, -1, -1, 15, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 23, -1, -1, 22, 7, 22, 39, -1, -1, -1, -1, -1, 10, 10, -1, -1, 16, 17, 21, -1, -1, -1, -1, -1, -1, -1, 15, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, -1, -1, -1, -1, -1, -1, 40, 22, 22, 22, 22, 22, 22, -1, -1, -1, 10, 10, 24, 25, 24, 25, 16, 17, 14, -1, -1, -1, -1, -1, -1, -1, 20, 23, 22, 22, 22]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 67, 66, 54, 54, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 67, 66, 54, 54, 54, 60, 61, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 57, 57, 57, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 51, 51, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 54, 54, 54, 48, 49, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 54, 54, 54, 54, 54, 54, 48, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 55, 54, 54, 54, 54, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 57, 67, 66, 54, 54, 54, 54, -1, -1, 60, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 49, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 67, 66, 54, 54, 60, 61, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, 60, 61, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 57, 57, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 54, 60, 61, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 57, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 45, 45, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 54, 60, 60, 48, 49, 45, 46, -1, -1, -1, 44, 45, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 51, 51, 45, 46, -1, -1, -1, -1, -1, -1, -1, 60, 60, 60, 60, 60, 48, 49, 46, -1, -1, 64, 67, 66, 60, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 67, 66, 54, 48, 49, 50, -1, -1, -1, -1, -1, 60, 60, 60, 60, 60, 60, 60, 60, 53, -1, -1, -1, 64, 63, 57, 57, 58, -1, -1, -1, -1, -1, -1, -1, 54, 54, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 67, 54, 61, 58, -1, -1, -1, 66, 66, 66, -1, 66, 66, 60, 60, 60, 60, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, 54, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 58, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, 63, 63, 63, 63, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, 60, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 60, 61, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 51, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 61, 57, 58, -1, -1, -1, 52, 45, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 55, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, -1, -1, -1, 52, 51, 54, 54, 48, 49, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 57, 57, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 55, 54, 54, 54, 54, 54, 48, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 51, 51, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 54, 54, 54, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 55, 54, 54, -1, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 67, 66, 54, 54, 54, 60, 61, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 55, 54, 54, 54, 54, 54, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 57, 57, 57, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 54, 54, 54, 54, 54, 54, 60, 61, 54, 67, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 67, 66, 54, 54, 60, 61, 57, 58, -1, 64, 63, 67, 66, 54, -1, -1, -1, -1, -1, -1, -1, 45, 45, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 63, 63, 57, 58, -1, -1, -1, -1, -1, 64, 63, 54, 54, -1, -1, -1, -1, -1, -1, 54, 54, 48, 49, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, -1, -1, -1, -1, -1, -1, 54, 54, 54, 54, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 63, 62, -1, -1, -1, -1, 54, 54, 54, 54, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 51, 45, 46, -1, -1, -1, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 55, 54, -1, -1, -1, -1, -1, -1, -1, -1, 60, 61, -1, -1, -1, -1, -1, 55, 54, 54, 48, 49, 45, 46, -1, 56, 67, 66, -1, -1, 54, -1, -1, -1, -1, -1, -1, -1, -1, 52, 45, 46, -1, -1, -1, -1, 59, 60, 60, 60, 60, 60, -1, -1, -1, 60, 61, 57, 58, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, 48, 49, 46, -1, 64, 63, 67, 66, 54, -1, -1, -1, -1, -1, -1, 52, 51, 55, 48, 49, 45, 50, -1, -1, 59, 66, 60, 60, 60, 60, 60, 60, 61, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 54, 54, 65, -1, -1, -1, 64, 63, 67, 66, -1, -1, -1, -1, -1, 55, 48, 48, 48, 48, 48, 53, -1, -1, 64, 63, 67, 66, 60, 60, 60, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 65, -1, -1, -1, -1, -1, 64, 63, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 53, -1, -1, -1, -1, 64, 63, 67, 66, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, -1, 66, 66, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, 53, -1, -1, -1, -1, -1, -1, 64, 63, 62, -1, 52, 51, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 67, 66, 66, 60, 61, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 48, 48, 48, 48, 60, 61, 58, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 55, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 57, 57, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 63, 67, 66, 48, 61, 57, 58, -1, -1, -1, -1, -1, -1, -1, 52, 51, 55, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, -1, -1, -1, 64, 63, 63, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 51, -1, -1, -1, -1, 48, 49, 45, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 55, 54, 54, 54, -1, -1, 54, 48, 48, 48, 49, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 48, 48, 48, 48, 48, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 55, 54, 54, 54, 54, 54, -1, -1, 54]}, {"type": 2, "data": [68, 69, 70, 69, 70, 68, 69, 68, 69, 70, 69, 70, 68, 68, 69, 70, 68, 68, 69, 70, 69, 70, 68, 69, 70, -1, 77, 78, 78, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 78, 77, 78, 77, 78, 71, 68, 69, 68, 69, 70, 68, 71, 72, 73, 72, 68, 71, 71, 72, 73, 68, 69, 70, 73, 72, 68, 69, 70, 68, 69, 70, -1, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 78, 77, 78, 78, 78, 77, 78, 68, 69, 70, 68, 69, 70, 68, 74, 75, 76, 68, 71, 74, 74, 75, 76, 71, 72, 68, 69, 70, 71, 72, 73, 71, 68, 69, 70, -1, -1, -1, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 78, 78, 77, 78, 71, 72, 73, 70, 72, 68, 69, 70, 68, 69, 70, 74, 68, 69, 70, 68, 69, 70, 71, 72, 73, 74, 75, 68, 69, 71, 72, 73, 69, 68, 69, 70, -1, 77, 78, -1, -1, -1, -1, 77, 77, 78, 77, 78, 77, 78, 78, 74, 75, 76, 73, 70, 71, 72, 73, 68, 69, 70, 68, 69, 70, 68, 69, 70, 70, 68, 69, 70, 68, 69, 70, 68, 74, 75, 76, 72, 71, 72, 68, 68, 69, 70, -1, -1, -1, -1, -1, 77, 78, 77, 78, 78, 78, 78, 68, 69, 70, 68, 69, 70, 75, 76, 71, 68, 69, 70, 68, 69, 70, 72, 68, 69, 70, 70, 73, 71, 72, 73, 71, 74, 75, 76, 69, 74, 68, 68, 69, 70, 73, -1, 77, 78, -1, 77, 77, 78, 77, 78, 77, 78, 78, 71, 72, 73, 71, 72, 73, 68, 69, 70, 71, 72, 68, 69, 70, 73, 68, 69, 70, 69, 70, 70, 74, 75, 76, 74, 75, 76, 68, 68, 69, 71, 71, 72, 73, 76, 77, 78, 78, 77, 77, 78, 78, 77, 78, 78, 77, 78, 74, 75, 76, 74, 75, 68, 69, 70, 73, 74, 75, 71, 72, 68, 69, 71, 72, 73, 72, 73, 69, 70, 68, 69, 70, 69, 68, 68, 71, 72, 74, 74, 75, 73, 77, 78, 77, 78, 77, 77, 78, 77, 77, 78, 77, 78, -1, 71, 68, 69, 70, 71, 71, 72, 73, 76, 68, 69, 70, 69, 70, 72, 74, 75, 76, 75, 76, 69, 70, 71, 72, 73, 68, 69, 70, 74, 75, 76, 76, 78, 77, 78, 77, 77, 78, 77, 78, 77, 77, 78, -1, 77, 77, 78, 74, 71, 72, 68, 69, 70, 75, 68, 69, 71, 72, 73, 72, 73, 75, 76, 71, 72, 68, 69, 70, 68, 74, 75, 76, 71, 72, 73, 75, 76, 77, 78, 77, 78, 77, 77, 78, 77, 78, 77, 77, 77, 78, -1, -1, 77, 78, 71, 74, 75, 71, 72, 73, 68, 69, 70, 74, 68, 69, 70, 68, 68, 68, 69, 70, 69, 70, 73, 71, 71, 72, 73, 74, 75, 78, 77, 78, 77, 78, 78, 77, 78, 77, 78, 77, 77, 78, 78, 77, 78, 78, 78, -1, -1, 68, 68, 68, 74, 75, 76, 71, 72, 73, 68, 71, 72, 73, 71, 68, 69, 70, 68, 68, 69, 70, 74, 74, 75, 76, 77, 78, 78, 77, 78, 77, 78, 78, 77, 77, 78, 78, 78, 77, 78, 77, 78, 68, 69, 70, 70, -1, 71, 71, 71, 71, 68, 68, 74, 75, 76, 71, 74, 75, 68, 68, 69, 70, 68, 68, 69, 70, 73, 72, 72, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 76, 68, 69, 70, 72, 73, 73, 68, 74, 68, 68, 69, 70, 68, 68, 68, 69, 68, 69, 70, 71, 68, 68, 68, 69, 71, 72, 73, 76, 77, 78, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 76, 68, 69, 70, 72, 73, 75, 76, 68, 69, 70, 71, 71, 72, 73, 71, 71, 68, 69, 71, 72, 73, 74, 71, 71, 71, 72, 74, 75, 76, 78, 77, 78, 77, 78, 78, 77, 78, 78, 77, 78, 78, 77, 78, 77, 76, 68, 69, 70, 72, 73, 75, 76, 74, 68, 69, 70, 68, 69, 70, 75, 76, 74, 74, 71, 68, 74, 75, 76, 72, 74, 74, 74, 75, 76, 78, 78, 77, 78, 77, 78, 77, 78, 78, 78, 78, 77, 78, 78, 77, 76, 68, 69, 71, 72, 73, 75, 76, 69, 70, 68, 69, 70, 73, 68, 69, 70, 76, 68, 68, 69, 68, 71, 71, 72, 73, 72, 72, 72, 77, 78, 77, 78, 77, 77, 78, 77, 77, 77, 78, 77, 78, 77, 78, 77, 76, 72, 72, 69, 70, 74, 75, 76, 68, 69, 70, 73, 71, 68, 69, 70, 71, 72, 73, 73, 71, 71, 72, 71, 74, 74, 75, 76, 72, 76, 78, 77, 78, 77, 77, 77, 77, 78, 78, 77, 78, 77, 77, 78, 77, 76, 72, 72, 72, 71, 72, 68, 69, 70, 70, 71, 72, 68, 69, 74, 71, 72, 73, 74, 75, 76, 69, 68, 68, 69, 70, 75, 76, 72, 76, 78, 77, 78, 77, 78, 77, 77, 77, 78, 78, 77, 78, 77, 78, 77, 76, 76, 68, 69, 70, 72, 74, 75, 71, 72, 73, 68, 69, 70, 71, 68, 69, 74, 75, 76, 68, 71, 71, 72, 71, 71, 72, 73, 72, 77, 78, 78, 78, 77, 77, 77, 77, 77, 77, 78, 77, 78, 77, 78, 77, 78, 76, 76, 68, 71, 68, 69, 70, 72, 72, 74, 75, 76, 71, 72, 73, 68, 69, 70, 68, 69, 70, 68, 74, 74, 75, 74, 74, 75, 76, 78, 77, 78, 78, 78, 77, 77, 77, 77, 78, 77, 77, 78, 78, 77, 78, 68, 68, 68, 68, 71, 68, 71, 72, 73, 72, 72, 72, 68, 69, 70, 75, 76, 71, 68, 69, 70, 68, 69, 71, 72, 73, 72, 72, 77, 78, 78, 78, 78, 78, 78, 77, 77, 77, 77, 77, 77, 78, 77, 78, 72, 68, 68, 71, 71, 68, 69, 70, 70, 74, 75, 76, 72, 68, 68, 69, 70, 73, 68, 69, 70, 71, 72, 68, 69, 70, 74, 75, 76, 77, 78, 78, 78, 78, 78, 78, 77, 77, 78, 78, 77, 77, 77, 77, 78, 72, 68, 69, 71, 71, 74, 68, 69, 70, 73, 73, 68, 69, 70, 69, 70, 71, 72, 68, 69, 70, 72, 73, 74, 75, 71, 68, 69, 72, 76, 78, 77, 78, 78, 78, 78, 77, 78, 77, 77, 78, 78, 77, 77, 77, 76, 72, 72, 71, 68, 69, 70, 70, 68, 69, 70, 76, 68, 71, 72, 73, 72, 73, 74, 75, 71, 72, 73, 75, 68, 69, 70, 74, 71, 72, 77, 77, 77, 78, 78, 77, 77, 77, 78, 77, 77, 78, 77, 77, 78, 77, 78, 76, 72, 72, 74, 71, 72, 73, 73, 71, 72, 68, 69, 70, 69, 70, 68, 69, 70, 76, 68, 69, 68, 69, 70, 69, 70, 68, 69, 70, 70, 77, 77, 78, 77, 78, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 77, 78, 72, 74, 75, 76, 68, 69, 68, 68, 69, 70, 72, 73, 71, 72, 73, 76, 71, 72, 71, 72, 73, 72, 73, 71, 72, 73, 73, 77, 77, 78, 78, 77, 77, 78, 77, 78, 77, 78, 77, 76, 77, 78, 77, 78, 77, 77, 78, 77, 78, 72, 72, 71, 72, 68, 69, 70, 73, 75, 76, 74, 75, 76, 70, 74, 75, 74, 75, 76, 75, 76, 74, 75, 76, 76, 77, 77, 78, 77, 77, 77, 77, 78, 77, 76, 68, 69, 70, 69, 70, 77, 78, 77, 78, 77, 77, 78, 77, 78, 74, 75, 71, 72, 73, 76, 68, 69, 70, 69, 70, 70, 68, 69, 68, 69, 70, 68, 69, 70, 68, 69, 70, 77, 78, 78, 77, 78, 77, 77, 68, 69, 70, 71, 72, 73, 70, 68, 69, 70, 77, 77, 78, 77, 77, 78, 77, 77, 78, 69, 75, 76, 72, 71, 68, 69, 70, 73, 68, 69, 70, 68, 69, 70, 71, 72, 73, 71, 72, 73, 77, 78, 77, 78, 77, 76, 68, 71, 72, 73, 74, 75, 68, 69, 71, 68, 68, 69, 70, 77, 78, 77, 77, 78, 77, 78, 77, 78, 69, 69, 74, 71, 72, 68, 69, 70, 68, 69, 70, 72, 73, 74, 68, 69, 70, 75, 68, 77, 78, 78, 68, 69, 70, 71, 74, 75, 76, 73, 76, 71, 72, 74, 71, 68, 68, 68, 69, 70, 77, 78, 77, 77, 77, 78, 77, 78, 78, 69, 74, 75, 71, 72, 68, 69, 70, 73, 75, 76, 71, 71, 72, 73, 68, 69, 77, 68, 69, 70, 72, 73, 74, 75, 74, 75, 76, 75, 74, 75, 76, 74, 71, 71, 68, 69, 68, 69, 70, 77, 78, 77, 77, 77, 78, 77, 78, 78, 69, 74, 75, 71, 72, 68, 69, 70, 68, 74, 74, 75, 76, 71, 72, 68, 71, 72, 73, 68, 69, 74, 75, 76, 72, 73, 68, 71, 72, 73, 68, 69, 70, 71, 72, 71, 68, 68, 69, 70, 77, 78, 77, 78, 77, 78, 77, 77, 78, 69, 74, 75, 71, 72, 68, 69, 68, 69, 68, 69, 68, 69, 71, 74, 75, 76, 71, 68, 69, 70, 74, 75, 76, 71, 74, 75, 76, 71, 68, 69, 70, 68, 74, 71, 71, 72, 73, 69, 78, 77, 78, 77, 77, 78, 77, 78, 77, 78, 69, 74, 75, 71, 72, 71, 68, 69, 70, 71, 72, 74, 75, 76, 75, 74, 71, 72, 73, 72, 73, 71, 74, 75, 76, 68, 71, 71, 72, 73, 68, 69, 70, 71, 72, 71, 72, 73, 70, 78, 77, 78, 77, 78, 77, 77, 78, 77, 78, 70, 74, 75, 74, 68, 69, 70, 68, 69, 74, 75, 76, 69, 70, 74, 75, 76, 75, 76, 74, 75, 76, 69, 70, 74, 75, 71, 71, 71, 72, 68, 69, 70, 71, 72, 68, 68, 69, 70, 77, 78, 78, 77, 77, 78, 78, 77, 78, 77, 78, 68, 71, 72, 73, 68, 69, 68, 69, 70, 72, 73, 68, 69, 70, 68, 69, 70, 73, 70, 72, 73, 71, 74, 74, 75, 76, 74, 71, 72, 73, 74, 75, 71, 71, 72, 73, 68, 69, 77, 77, 78, 77, 78, 77, 78, 77, 78, 77, 77, 75, 76, 71, 68, 71, 72, 73, 75, 76, 68, 69, 70, 68, 69, 70, 76, 73, 75, 68, 69, 70, 76, 68, 69, 68, 69, 70, 68, 69, 70, 74, 74, 75, 76, 71, 72, 73, 72, 77, 78, 77, 78, 77, 77, 78, 77, 78, 77, 78, 74, 71, 68, 69, 70, 72, 73, 71, 72, 73, 71, 72, 73, 68, 68, 69, 70, 72, 68, 69, 68, 69, 70, 72, 73, 68, 69, 70, 68, 74, 75, 76, 68, 69, 70, 73, 72, 72, 77, 78, 77, 77, 78, 77, 78, 77, 78, 77, 77, 71, 72, 73, 70, 76, 70, 75, 68, 69, 70, 76, 71, 71, 72, 73, 68, 71, 72, 71, 72, 73, 75, 76, 71, 72, 73, 71, 72, 68, 69, 70, 70, 70, 76, 72, 72, 72, 72, -1, 77, 77, 78, 77, 78, 78, 77, 78, 74, 75, 76, 73, 69, 70, 72, 71, 72, 73, 75, 74, 74, 75, 68, 68, 69, 70, 74, 75, 76, 68, 74, 68, 69, 70, 74, 75, 71, 72, 73, 68, 69, 70, 68, 68, 69, 70, 68, 69, 77, 78, 77, 78, 77, 77, 78, 74, 75, 76, 70, 72, 73, 69, 70, 68, 68, 69, 70, 70, 74, 71, 71, 72, 73, 69, 70, 71, 71, 72, 71, 68, 69, 70, 71, 74, 75, 76, 71, 68, 69, 70, 71, 72, 73, 71, 72, 68, 69, 77, 77, 78, 77, 78, 71, 71, 72, 73, 75, 76, 72, 73, 71, 71, 72, 73, 73, 72, 74, 74, 75, 76, 72, 73, 74, 74, 75, 74, 71, 72, 68, 69, 70, 75, 74, 74, 71, 68, 69, 70, 75, 76, 71, 72, 71, 72, 68, 69, 77, 77, 78, 74, 74, 75, 76, 70, 70, 75, 76, 74, 74, 75, 76, 76, 68, 71, 74, 75, 76, 75, 76, 72, 74, 75, 71, 74, 75, 71, 72, 73, 68, 69, 74, 74, 71, 72, 73, 69, 68, 74, 75, 74, 75, 71, 72, 68, 69, 77, 68, 69, 71, 72, 73, 73, 68, 71, 72, 73, 68, 69, 70, 71, 74, 75, 76, 71, 72, 73, 72, 68, 69, 74, 75, 76, 74, 68, 69, 70, 72, 68, 74, 74, 75, 76, 68, 71, 74, 74, 75, 71, 74, 75, 71, 72, 73, 71, 72, 74, 75, 76, 76, 71, 74, 75, 76, 71, 72, 73, 74, 75, 76, 76, 74, 75, 76, 70, 71, 72, 73, 68, 69, 72, 71, 72, 73, 72, 71, 72, 68, 68, 71, 71, 74, 68, 69, 70, 70, 68, 74, 74, 75, 76, 74, 75, 76, 74, 75, 76, 74, 75, 76, 76, 74, 75, 76, 76, 70, 70, 73, 75, 76, 72, 73, 74, 75, 76, 72, 72, 71, 74, 75, 76, 75, 74, 75, 71, 71, 74, 74, 75, 71, 72, 73, 73, 71, 72, 73, 75, 76]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}