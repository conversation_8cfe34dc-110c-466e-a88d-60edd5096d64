{"mW": 1056, "mH": 888, "tW": 24, "tH": 24, "tiles": [["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["373", 0, 1, 1], ["203_10", 0, 2, 1], ["203_11", 0, 2, 1], ["203_12", 0, 2, 1], ["203_11", 1, 2, 1], ["203_12", 1, 2, 1], ["203_10", 1, 2, 1], ["203_9", 0, 2, 1], ["203_9", 1, 2, 1], ["380", 0, 3, 2], ["380", 2, 3, 2], ["380", 1, 3, 2], ["380", 3, 3, 2], ["381", 0, 3, 2], ["382", 0, 1, 3], ["383", 0, 2, 3], ["383", 2, 2, 3], ["382", 2, 1, 3], ["381", 2, 3, 2], ["383", 1, 2, 3], ["384", 0, 1, 3], ["384", 2, 1, 3], ["996", 0, 2, 2], ["995", 0, 3, 2], ["995", 2, 3, 2], ["995", 1, 3, 2], ["995", 3, 3, 2], ["111", 0, 3, 2], ["111", 2, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["937", 0, 2, 1], ["937", 1, 2, 1], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1]], "layers": [{"type": 3, "obj": [[2, "87_1", 1036, 850, 72, 57, 2], [2, "385", 642, 411, 72, 48, 0], [2, "385", 676, 646, 72, 48, 0], [2, "87_1", 537, 318, 72, 57, 0], [2, "268_1", 469, 257, 106, 82, 0], [2, "88_1", 446, 315, 88, 61, 0], [2, "88_1", 949, 847, 88, 61, 2], [2, "268_1", 985, 792, 106, 82, 2], [2, "334", 952, 406, 32, 40, 4], [2, "334", 983, 350, 40, 32, 0], [2, "333", 1011, 349, 38, 35, 3], [2, "334", 1039, 388, 40, 32, 0], [2, "333", 1007, 393, 38, 35, 0], [2, "334", 971, 367, 40, 32, 0], [2, "333", 1013, 367, 38, 35, 2], [2, "332", 954, 332, 36, 38, 0], [2, "332", 1000, 340, 36, 38, 0], [2, "334", 1025, 403, 40, 32, 0], [2, "334", 1033, 420, 40, 32, 0], [2, "334", 1055, 431, 40, 32, 1], [2, "332", 1027, 418, 36, 38, 0], [2, "334", 974, 415, 40, 32, 2], [2, "333", 912, 431, 38, 35, 2], [2, "332", 930, 415, 36, 38, 0], [2, "332", 943, 364, 36, 38, 0], [2, "333", -14, 775, 38, 35, 1], [2, "334", 14, 814, 40, 32, 2], [2, "333", -18, 819, 38, 35, 2], [2, "333", -12, 793, 38, 35, 0], [2, "332", -25, 766, 36, 38, 2], [2, "334", 0, 829, 40, 32, 2], [2, "334", 8, 846, 40, 32, 2], [2, "334", 30, 857, 40, 32, 3], [2, "332", 2, 844, 36, 38, 2], [2, "968", 1033, 637, 24, 38, 0], [2, "968", 1033, 609, 24, 38, 0], [2, "987", 1021, 569, 24, 52, 0], [2, "987", 1045, 570, 24, 52, 2], [2, "968", 96, 264, 24, 38, 2], [2, "968", 96, 236, 24, 38, 2], [2, "987", 83, 196, 24, 52, 0], [2, "987", 108, 197, 24, 52, 2]]}, {"type": 4, "obj": [[2, "327", 624, 58, 30, 22, 0], [2, "366", 603, 160, 32, 48, 0], [2, "329", 622, 173, 42, 37, 0], [4, 3, 948, 215, 0, 4021], [2, "986", 94, 170, 32, 61, 0], [2, "986", 126, 170, 32, 61, 2], [2, "328", 317, 226, 32, 29, 0], [4, 4, 756, 287, 0, 4001], [2, "921", 81, 287, 56, 68, 2], [4, 6, 182, 383, 0, 4005], [2, "328", 535, 372, 32, 29, 2], [2, "89", 507, 317, 48, 95, 2], [2, "328", 514, 394, 32, 29, 2], [4, 1, 70, 430, 1, 4021], [2, "331", 944, 380, 104, 108, 2], [2, "361", 727, 398, 58, 104, 4], [2, "85_2", 605, 480, 48, 53, 0], [2, "361", 755, 445, 58, 104, 6], [2, "366", 823, 503, 32, 48, 2], [2, "328", 832, 547, 32, 29, 0], [2, "365", 789, 483, 48, 94, 2], [2, "329", 516, 541, 42, 37, 0], [2, "326", 821, 568, 18, 14, 0], [2, "263_2", 474, 553, 34, 34, 0], [2, "329", 763, 554, 42, 37, 2], [2, "328", 453, 563, 32, 29, 0], [2, "328", 796, 565, 32, 29, 0], [2, "329", 151, 559, 42, 37, 0], [2, "328", 65, 577, 32, 29, 0], [2, "328", 290, 648, 32, 29, 0], [2, "968", 1033, 663, 24, 38, 0], [2, "986", 343, 645, 32, 61, 0], [2, "986", 375, 645, 32, 61, 2], [2, "329", 491, 681, 42, 37, 0], [2, "329", 737, 682, 42, 37, 0], [2, "87_1", 651, 678, 72, 57, 0], [4, 0, 166, 755, 0, 4005], [4, 2, 1020, 767, 0, 4006], [4, 5, 564, 791, 0, 4004], [2, "329", 327, 851, 42, 37, 0]]}, {"type": 3, "obj": [[2, "470", 854, 43, 18, 62, 0], [2, "470", 991, 49, 18, 62, 2], [2, "365", 921, 445, 48, 94, 0], [2, "329", 1035, 26, 42, 37, 0], [2, "364", 998, 9, 44, 64, 0], [2, "385", 737, 550, 72, 48, 0], [2, "381", 714, 551, 72, 48, 0], [2, "385", 735, 577, 72, 48, 0], [2, "328", 784, 56, 32, 29, 0], [2, "683", 331, 239, 22, 34, 0], [2, "683", 322, 244, 22, 34, 0], [2, "683", 315, 247, 22, 34, 0], [2, "683", 307, 251, 22, 34, 0], [2, "683", 298, 256, 22, 34, 0], [2, "683", 289, 261, 22, 34, 0], [2, "683", 280, 265, 22, 34, 0], [2, "683", 271, 269, 22, 34, 0], [2, "683", 262, 274, 22, 34, 0], [2, "683", 255, 277, 22, 34, 0], [2, "683", 247, 281, 22, 34, 0], [2, "683", 238, 286, 22, 34, 0], [2, "683", 229, 291, 22, 34, 0], [2, "683", 220, 296, 22, 34, 0], [2, "683", 211, 300, 22, 34, 0], [2, "683", 202, 305, 22, 34, 0], [2, "683", 195, 308, 22, 34, 0], [2, "683", 187, 312, 22, 34, 0], [2, "683", 178, 317, 22, 34, 0], [2, "683", 169, 322, 22, 34, 0], [2, "683", -27, 295, 22, 34, 0], [2, "683", -17, 300, 22, 34, 0], [2, "683", -9, 304, 22, 34, 0], [2, "683", 1, 309, 22, 34, 0], [2, "683", 10, 313, 22, 34, 0], [2, "683", 18, 317, 22, 34, 0], [2, "683", 28, 322, 22, 34, 0], [2, "683", 36, 326, 22, 34, 0], [2, "683", 46, 331, 22, 34, 0], [2, "683", 55, 335, 22, 34, 0], [2, "683", 161, 326, 22, 34, 0], [2, "683", 152, 330, 22, 34, 0], [2, "683", 143, 335, 22, 34, 0], [2, "683", 136, 338, 22, 34, 0], [2, "683", 128, 342, 22, 34, 0], [2, "683", 119, 347, 22, 34, 0], [2, "683", 110, 352, 22, 34, 0], [2, "683", 64, 339, 22, 34, 0], [2, "683", 74, 344, 22, 34, 0], [2, "683", 82, 348, 22, 34, 0], [2, "683", 92, 353, 22, 34, 0], [2, "683", 101, 357, 22, 34, 0], [2, "319", 970, 481, 62, 65, 2], [2, "363", 885, 459, 54, 75, 0], [2, "364", 947, 488, 44, 64, 0], [2, "329", 970, 517, 42, 37, 0], [2, "366", 943, 509, 32, 48, 2], [2, "328", 946, 536, 32, 29, 0], [2, "268_1", 756, -20, 106, 82, 2], [2, "654", 127, 247, 96, 45, 2], [2, "654", 32, 247, 96, 45, 0], [2, "654", 32, 201, 96, 45, 1], [2, "654", 127, 201, 96, 45, 3], [2, "324", 166, 137, 70, 35, 2], [2, "328", 221, 149, 32, 29, 2], [2, "439", -31, 111, 64, 42, 2], [2, "324", 281, 164, 70, 35, 2], [2, "324", 288, 168, 70, 35, 2], [2, "89", 287, 125, 48, 95, 0], [2, "89", 182, 85, 48, 95, 2], [2, "89", 4, 87, 48, 95, 2], [2, "325", 641, 360, 50, 37, 0], [2, "325", 572, 544, 50, 37, 0], [2, "544", 597, 77, 86, 107, 0], [2, "544", 513, 76, 86, 107, 2], [2, "325", 246, 547, 50, 37, 0], [2, "174_2", 80, 557, 68, 33, 2], [2, "174_2", 251, 529, 68, 33, 2], [2, "371", 440, 611, 26, 48, 2], [2, "371", 232, 668, 26, 48, 2], [2, "371", 286, 619, 26, 48, 2], [2, "385", 438, 620, 72, 48, 0], [2, "381", 393, 137, 72, 48, 0], [2, "382", 413, 135, 72, 24, 4], [2, "382", 610, 671, 72, 24, 6], [2, "383", 596, 624, 72, 48, 4], [2, "318", 639, 380, 18, 54, 0], [2, "319", 669, 418, 62, 65, 2], [2, "317", 644, 405, 22, 42, 0], [2, "317", 656, 414, 22, 42, 0], [2, "317", 666, 413, 22, 42, 0], [2, "325", 245, 417, 50, 37, 0], [2, "328", 326, 166, 32, 29, 0], [2, "325", 471, 160, 50, 37, 0], [2, "361", 461, 264, 104, 58, 2], [2, "324", 385, 211, 70, 35, 2], [2, "371", 281, 596, 26, 48, 2], [2, "320", 577, 195, 62, 103, 0], [2, "371", 460, 143, 26, 48, 0], [2, "319", 482, 183, 62, 65, 2], [2, "319", 312, 572, 62, 65, 0], [2, "319", 582, 562, 62, 65, 0], [2, "319", 566, 539, 62, 65, 0], [2, "322", 605, 429, 52, 101, 2], [2, "319", 114, 627, 62, 65, 0], [2, "318", 491, 703, 18, 54, 2], [2, "318", 302, 586, 18, 54, 0], [2, "323", 573, 388, 46, 70, 2], [2, "324", 507, 358, 70, 35, 2], [2, "326", 267, 570, 18, 14, 0], [2, "319", 547, 202, 62, 65, 2], [2, "318", 471, 179, 18, 54, 0], [2, "318", 479, 180, 18, 54, 0], [2, "318", 499, 203, 18, 54, 0], [2, "319", 612, 202, 62, 65, 0], [2, "317", 490, 217, 22, 42, 0], [2, "318", 469, 142, 18, 54, 0], [2, "328", 479, 167, 32, 29, 0], [2, "318", 601, 218, 18, 54, 2], [2, "318", 663, 198, 18, 54, 2], [2, "372", 618, 532, 44, 50, 0], [2, "318", 641, 494, 18, 54, 2], [2, "371", 661, 206, 26, 48, 2], [2, "371", 636, 230, 26, 48, 2], [2, "319", 514, 563, 62, 65, 0], [2, "371", 572, 610, 26, 48, 2], [2, "319", 525, 594, 62, 65, 0], [2, "319", 340, 593, 62, 65, 2], [2, "319", 183, 653, 62, 65, 2], [2, "319", 235, 617, 62, 65, 0], [2, "319", 369, 568, 62, 65, 0], [2, "319", 454, 580, 62, 65, 0], [2, "319", 412, 570, 62, 65, 2], [2, "319", 486, 599, 62, 65, 2], [2, "371", 653, 236, 26, 48, 2], [2, "319", 389, 592, 62, 65, 0], [2, "371", 544, 626, 26, 48, 2], [2, "325", 625, 49, 50, 37, 0], [2, "319", 121, 583, 62, 65, 0], [2, "318", 118, 612, 18, 54, 2], [2, "371", 134, 615, 26, 48, 2], [2, "371", 113, 645, 26, 48, 2], [2, "318", 107, 611, 18, 54, 2], [2, "320", 162, 593, 62, 103, 0], [2, "319", 197, 600, 62, 65, 0], [2, "318", 248, 596, 18, 54, 2], [2, "371", 246, 604, 26, 48, 2], [2, "371", 221, 628, 26, 48, 2], [2, "318", 269, 602, 18, 54, 2], [2, "371", 238, 634, 26, 48, 2], [2, "366", 415, 203, 32, 48, 0], [2, "328", 444, 236, 32, 29, 0], [2, "329", 457, 234, 42, 37, 0], [2, "327", 369, 202, 30, 22, 0], [2, "365", 587, -20, 48, 94, 0], [2, "364", 525, 17, 44, 64, 0], [2, "363", 478, 30, 54, 75, 0], [2, "366", 566, 25, 32, 48, 0], [2, "364", 464, 88, 44, 64, 0], [2, "364", 668, 48, 44, 64, 2], [2, "366", 648, 34, 32, 48, 0], [2, "361", 502, 185, 104, 58, 2], [2, "362", 507, 231, 64, 42, 2], [2, "365", 367, 568, 48, 94, 0], [2, "364", 299, 601, 44, 64, 0], [2, "364", 412, 610, 44, 64, 2], [2, "363", 446, 628, 54, 75, 2], [2, "366", 334, 614, 32, 48, 0], [2, "366", 501, 722, 32, 48, 2], [2, "329", 676, 83, 42, 37, 0], [2, "36_1", 811, 479, 140, 103, 2], [2, "21", 257, 422, 28, 24, 0], [2, "329", 598, 405, 42, 37, 0], [2, "327", 270, 541, 30, 22, 0], [2, "328", 419, 238, 32, 29, 0], [2, "363", 246, 619, 54, 75, 0], [2, "364", 237, 680, 44, 64, 0], [2, "327", 244, 730, 30, 22, 0], [2, "327", 94, 584, 30, 22, 0], [2, "329", 449, 127, 42, 37, 2], [2, "385", 428, 171, 72, 48, 0], [2, "385", 567, -6, 72, 48, 0], [2, "385", 219, 639, 72, 48, 0], [2, "385", 607, 204, 72, 48, 0], [2, "385", 599, 295, 72, 48, 0], [2, "385", 536, 231, 72, 48, 0], [2, "385", 544, 613, 72, 48, 0], [2, "326", 242, 430, 18, 14, 0], [2, "174_2", 325, 277, 68, 33, 1], [2, "174_2", 339, 330, 68, 33, 0], [2, "174_2", 438, 541, 68, 33, 0], [2, "174_2", 435, 390, 68, 33, 0], [2, "327", 403, 487, 30, 22, 0], [2, "329", 249, 173, 42, 37, 2], [2, "328", 426, 494, 32, 29, 0], [2, "328", 365, 338, 32, 29, 0], [2, "327", 584, 181, 30, 22, 0], [2, "984", 523, 82, 76, 52, 0], [2, "984", 596, 82, 76, 52, 2], [2, "984", 524, 126, 76, 52, 1], [2, "984", 597, 126, 76, 52, 3], [2, "268_1", -16, 45, 106, 82, 0], [2, "88_1", 22, 99, 88, 61, 0], [2, "268_1", 140, 38, 106, 82, 0], [2, "88_1", 277, 74, 88, 61, 0], [2, "87_1", 302, 131, 72, 57, 0], [2, "88_1", 235, 114, 88, 61, 0], [2, "88_1", 175, 90, 88, 61, 0], [2, "174_2", 652, 380, 68, 33, 1], [2, "174_2", 329, 385, 68, 33, 1], [2, "326", 506, 572, 18, 14, 0], [2, "327", 423, 661, 30, 22, 0], [2, "327", 20, 356, 30, 22, 0], [2, "420", 274, 439, 16, 13, 0], [2, "420", 542, 568, 16, 13, 0], [2, "420", 184, 418, 16, 13, 0], [2, "339", 161, 408, 22, 22, 0], [2, "339", 111, 436, 22, 22, 0], [2, "339", 163, 437, 22, 22, 0], [2, "329", -8, 323, 42, 37, 2], [2, "420", 239, 442, 16, 13, 0], [2, "420", 174, 452, 16, 13, 0], [2, "422", 118, 446, 16, 14, 0], [2, "174_2", 25, 380, 68, 33, 2], [2, "322", 101, 644, 52, 101, 2], [2, "325", 283, 860, 50, 37, 2], [2, "326", 304, 871, 18, 14, 0], [2, "320", 714, 465, 62, 103, 0], [2, "319", 746, 530, 62, 65, 2], [2, "322", 768, 581, 52, 101, 0], [2, "990", 140, 160, 54, 27, 0], [2, "990", 170, 175, 54, 27, 0], [2, "990", 198, 189, 54, 27, 0], [2, "990", 228, 204, 54, 27, 0], [2, "990", 240, 250, 54, 27, 2], [2, "990", 212, 264, 54, 27, 2], [2, "990", 182, 279, 54, 27, 2], [2, "990", 152, 295, 54, 27, 2], [2, "990", 126, 309, 54, 27, 2], [2, "990", -4, 288, 54, 27, 0], [2, "990", -31, 276, 54, 27, 0], [2, "990", 24, 302, 54, 27, 0], [2, "990", 54, 316, 54, 27, 0], [2, "990", 48, 165, 54, 27, 2], [2, "990", 18, 180, 54, 27, 2], [2, "990", -10, 195, 54, 27, 2], [2, "990", -38, 209, 54, 27, 2], [2, "683", 111, 210, 22, 34, 2], [2, "683", 121, 215, 22, 34, 2], [2, "683", 132, 220, 22, 34, 2], [2, "683", 102, 214, 22, 34, 2], [2, "683", 112, 219, 22, 34, 2], [2, "683", 123, 224, 22, 34, 2], [2, "683", 94, 218, 22, 34, 2], [2, "683", 104, 223, 22, 34, 2], [2, "683", 115, 228, 22, 34, 2], [2, "326", 161, 359, 18, 14, 0], [2, "420", 158, 371, 16, 13, 0], [2, "327", 310, 299, 30, 22, 0], [2, "1102", 889, 115, 114, 63, 2], [2, "1102", 944, 94, 114, 63, 0], [2, "1102", 924, 99, 114, 63, 0], [2, "1102", 772, 81, 114, 63, 2], [2, "1102", 811, 126, 114, 63, 0], [2, "1102", 792, 132, 114, 63, 0], [2, "1102", 886, 141, 114, 63, 2], [2, "1102", 902, 149, 114, 63, 2], [2, "470", 892, 96, 18, 62, 0], [2, "1193", 1002, 75, 34, 71, 0], [2, "470", 964, 87, 18, 62, 2], [2, "470", 977, 84, 18, 62, 2], [2, "470", 845, 69, 18, 62, 0], [2, "1212", 810, 84, 40, 58, 0], [2, "1211", 816, 67, 30, 35, 0], [2, "470", 995, 76, 18, 62, 2], [2, "470", 936, 91, 18, 62, 0], [2, "470", 949, 93, 18, 62, 0], [2, "1208", 913, 117, 52, 56, 0], [2, "1208", 965, 109, 52, 56, 2], [2, "1200", 932, 100, 24, 32, 2], [2, "1203", 845, 106, 24, 55, 0], [2, "880", 841, 77, 66, 94, 2], [2, "1212", 901, 121, 40, 58, 0], [2, "1211", 906, 103, 30, 35, 0], [2, "1206", 870, 133, 14, 42, 0], [2, "1206", 830, 120, 14, 42, 2], [2, "1210", 827, 87, 62, 59, 0], [2, "1193", 943, 105, 34, 71, 0], [2, "872", 981, 86, 26, 39, 2], [2, "328", 878, 36, 32, 29, 0], [2, "1228", 963, -19, 60, 75, 2], [2, "1197", 973, 57, 54, 44, 2], [2, "1228", 942, -9, 60, 75, 2], [2, "1228", 845, -11, 60, 75, 0], [2, "1228", 871, 3, 60, 75, 0], [2, "1228", 893, 12, 60, 75, 0], [2, "1228", 889, 18, 60, 75, 2], [2, "1228", 913, 27, 60, 75, 0], [2, "1197", 837, 46, 54, 44, 0], [2, "1228", 864, 27, 60, 75, 2], [2, "1227", 808, 16, 46, 62, 2], [2, "1225_1", 942, -34, 48, 44, 2], [2, "1225_1", 869, -25, 48, 44, 0], [2, "1229_1", 902, -9, 48, 39, 2], [2, "1226_1", 849, 7, 70, 47, 2], [2, "1228", 938, 38, 60, 75, 0], [2, "1230_1", 986, 39, 58, 34, 2], [2, "1226_1", 942, 10, 70, 47, 0], [2, "1227", 898, 51, 46, 62, 2], [2, "322", 641, 233, 52, 101, 0], [2, "88_1", 710, 46, 88, 61, 2], [2, "319", 1011, 491, 62, 65, 0], [2, "688", 142, 143, 46, 24, 2], [2, "688", 179, 162, 46, 24, 2], [2, "688", 219, 181, 46, 24, 2], [2, "688", 45, 154, 46, 24, 0], [2, "688", 6, 173, 46, 24, 0], [2, "688", -32, 192, 46, 24, 0], [2, "506", 272, 274, 20, 45, 0], [2, "506", 281, 278, 20, 45, 0], [2, "507", 291, 284, 48, 58, 0], [2, "508", 268, 329, 60, 37, 0], [2, "508", 231, 348, 60, 37, 0], [2, "508", 255, 317, 60, 37, 0], [2, "508", 222, 333, 60, 37, 0], [2, "508", 245, 305, 60, 37, 0], [2, "508", 212, 321, 60, 37, 0], [2, "508", 238, 291, 60, 37, 0], [2, "508", 228, 280, 60, 37, 0], [2, "508", 202, 309, 60, 37, 0], [2, "508", 198, 295, 60, 37, 0], [2, "506", 191, 318, 20, 45, 0], [2, "507", 203, 324, 48, 58, 0], [2, "325", 164, 346, 50, 37, 0], [2, "324", 714, 676, 70, 35, 0], [2, "328", 520, 748, 32, 29, 2], [2, "366", 126, 697, 32, 48, 2], [2, "328", 145, 722, 32, 29, 2], [2, "85_2", 198, 705, 48, 53, 0], [2, "994", 169, 125, 10, 45, 0], [2, "994", 205, 141, 10, 45, 0], [2, "994", 240, 158, 10, 45, 0], [2, "994", 64, 126, 10, 45, 0], [2, "994", 25, 146, 10, 45, 0], [2, "994", -10, 164, 10, 45, 0], [2, "318", 625, 514, 18, 54, 2], [2, "329", 483, 724, 42, 37, 0], [2, "263_2", 763, 655, 34, 34, 0], [2, "263_2", 576, 738, 34, 34, 0], [2, "990", 255, 219, 54, 27, 0], [2, "921", 270, 194, 56, 68, 2], [2, "990", 98, 323, 54, 27, 2], [2, "990", 76, 150, 54, 27, 2], [2, "990", 113, 144, 54, 27, 0], [2, "921", 103, 104, 56, 68, 2], [2, "85_2", 1019, 532, 48, 53, 0], [2, "325", 910, 210, 50, 37, 0], [2, "327", 932, 191, 30, 22, 0], [2, "327", 945, 215, 30, 22, 0], [2, "366", 642, 315, 32, 48, 0], [2, "21", 1022, 171, 28, 24, 0], [2, "363", 718, 644, 54, 75, 0], [2, "366", 654, 724, 32, 48, 0], [2, "328", 679, 720, 32, 29, 0], [2, "325", 603, 757, 50, 37, 0], [2, "327", 555, 751, 30, 22, 0], [2, "326", 178, 738, 18, 14, 0], [2, "339", 1035, 215, 22, 22, 0], [2, "422", 1042, 225, 16, 14, 0], [2, "339", 985, 212, 22, 22, 0], [2, "422", 992, 222, 16, 14, 0], [2, "330", 992, 173, 60, 49, 0], [2, "325", 697, 736, 50, 37, 0], [2, "21", 689, 749, 28, 24, 0], [2, "327", 757, 851, 30, 22, 0], [2, "174_2", 700, 779, 68, 33, 1], [2, "325", 1013, 729, 50, 37, 0], [2, "21", 995, 730, 28, 24, 0], [2, "263_2", 345, 180, 34, 34, 0], [2, "385", 625, 578, 72, 48, 0], [2, "381", 602, 579, 72, 48, 0], [2, "381", 700, 601, 72, 48, 2], [2, "383", 652, 582, 48, 72, 0], [2, "385", 651, 626, 72, 48, 0], [2, "325", 774, 287, 50, 37, 0], [2, "21", 795, 319, 28, 24, 0], [2, "326", 753, 291, 18, 14, 0], [2, "420", 803, 309, 16, 13, 0], [2, "420", 768, 312, 16, 13, 0], [2, "328", 634, 357, 32, 29, 0], [2, "420", 669, 347, 16, 13, 0], [2, "550", 415, 804, 42, 28, 0], [2, "551", 311, 804, 30, 21, 0], [2, "553", 390, 821, 14, 8, 0], [2, "552", 466, 785, 22, 15, 0], [2, "553", 276, 797, 14, 8, 0], [2, "263_2", 395, 796, 34, 34, 0], [2, "550", 836, 685, 42, 28, 0], [2, "552", 856, 664, 22, 15, 0], [2, "921", 1016, 686, 56, 68, 0], [2, "1236", 1022, 714, 42, 43, 0], [2, "263_2", 1034, 756, 34, 34, 0], [2, "866", 820, 168, 42, 26, 2], [2, "339", 791, 859, 22, 22, 0], [2, "422", 867, 870, 16, 14, 0], [2, "22", 818, 855, 62, 38, 0], [2, "683", 360, 684, 22, 34, 2], [2, "683", 370, 689, 22, 34, 2], [2, "683", 381, 694, 22, 34, 2], [2, "683", 351, 688, 22, 34, 2], [2, "683", 361, 693, 22, 34, 2], [2, "683", 372, 698, 22, 34, 2], [2, "683", 343, 692, 22, 34, 2], [2, "683", 353, 697, 22, 34, 2], [2, "683", 364, 702, 22, 34, 2], [2, "385", 367, 115, 72, 48, 0], [2, "21", 77, 390, 28, 24, 0], [2, "89", 618, 690, 48, 95, 2], [2, "88_1", 560, 675, 88, 61, 0], [2, "268_1", 583, 617, 106, 82, 0], [2, "968", 118, 81, 24, 38, 2], [2, "968", 118, 53, 24, 38, 2], [2, "987", 105, 13, 24, 52, 0], [2, "987", 130, 14, 24, 52, 2], [2, "968", 284, 171, 24, 38, 2], [2, "968", 284, 143, 24, 38, 2], [2, "987", 271, 103, 24, 52, 0], [2, "987", 296, 104, 24, 52, 2]]}, {"type": 2, "data": [104, 81, 80, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 83, 82, 87, 91, 90, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 106, 85, 84, 88, 94, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 81, 80, 86, -1, -1, -1, -1, 18, 41, 42, 47, 20, 23, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 65, 66, 67, 83, 82, 87, 91, 90, 12, 18, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, 18, -78, -1, -1, -1, -1, -1, -83, -82, -81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 68, 69, 73, 85, 84, 88, 94, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -76, -77, -78, -1, -1, -1, -1, -1, -1, -1, -1, -1, -83, -1, -1, -1, -1, -1, -1, -1, 0, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -86, -87, -88, 10, -1, -1, -1, -1, -1, -1, -1, 16, -93, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, -1, -1, -1, -1, -1, 75, 101, -1, 71, 86, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -86, -87, -88, -1, -1, -1, -1, 16, -93, -92, -91, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 102, -1, 105, 83, -1, -1, -1, -1, -1, 0, 6, 8, -1, -1, -1, -1, -1, -1, -1, -86, -87, -88, 13, -93, -92, -91, 11, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 106, 85, -1, -1, -1, -1, -1, 4, -1, -1, -1, -1, 41, 42, 48, 48, 47, -1, -1, -86, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -83, -82, -81, 12, 13, 13, -1, -1, 81, -1, -1, -1, -1, 65, 66, -1, -1, 44, 45, 45, 58, 50, -1, -1, -1, 41, 42, 42, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, -93, -92, -91, -1, -1, -1, 12, -1, 83, -1, -1, -1, 80, 68, -1, -1, 41, 52, 45, 45, 62, -1, -1, -1, -1, 53, 64, 63, 46, 42, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -93, -92, -91, -1, 49, 47, -1, -1, 15, -1, 85, -1, -1, -1, -1, -1, -1, -1, 44, 45, 45, 45, 62, -1, -1, -1, -1, -1, 61, 64, 57, 45, 62, 10, -1, -1, -1, -1, -1, -1, -1, 16, -1, -1, -1, -1, -1, 61, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 64, 63, 62, -1, -1, -1, -1, -1, -1, 53, 54, 64, 62, 16, 17, 10, -1, -1, -1, 10, -1, 23, 22, -1, -1, -1, -1, -1, -1, 55, -1, -1, -1, -1, -1, -1, 74, 75, 101, -1, -1, -1, -1, -1, 61, 60, 59, -1, -1, -1, -1, -1, -1, -1, 61, 60, 59, 13, 14, -87, -88, 10, 10, -93, -92, 13, 19, -1, -121, -120, -114, -114, -119, 18, -1, 41, 47, -1, -1, -1, 76, 77, 102, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -86, -87, -92, -91, -1, 49, 48, 47, -1, -1, -109, -108, -107, -1, -1, -1, 0, 1, 2, 7, 6, 79, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, 43, -1, -1, 41, 42, 52, 63, 62, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 13, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 63, 46, 48, 47, -1, 61, 60, 60, 59, -1, 59, -1, -1, 49, 48, 47, -1, -1, -1, -1, 49, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 60, 60, 60, 59, -1, -1, -1, -1, -1, -1, -113, -114, -115, 53, 54, 59, -1, -1, -1, -1, 53, 50, -1, -1, -1, -1, 104, 81, -1, -1, -1, 1, 2, -1, -1, -1, -1, -1, -1, 0, 1, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -101, -102, -98, -111, -111, -1, -1, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 83, -1, -1, -1, 4, 5, -1, -1, -1, -1, -1, -1, 20, 19, 19, -1, -1, -1, -1, -1, -1, -1, -111, -111, -1, -1, -1, -101, -102, -103, -1, -1, 20, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, 106, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -109, -108, -107, 43, -1, 49, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 22, -1, -1, -1, 16, 17, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 64, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, 27, -1, -1, -1, -1, -1, -1, 16, 16, 16, 19, 19, 13, 14, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, -1, -1, 61, 60, 60, 59, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, 27, 27, -1, -1, -1, -1, -1, -1, 13, 19, 19, 18, -1, -1, -1, -1, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 27, -1, 27, 27, -1, -1, -1, -1, -1, 41, 42, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, -1, 18, 12, 23, 7, 6, -1, -1, 91, 90, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 52, 45, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 18, -1, -1, 12, -1, -1, -1, -1, 94, 93, 92, 65, 66, 67, -1, -1, -1, -1, -1, -1, -1, 56, 57, 57, 63, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 69, 70, -1, -1, 95, 96, -1, -1, -1, 56, 57, 58, 60, 59, -1, -1, -1, -1, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 6, -1, 20, 19, 18, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 55, -1, -1, -1, 49, 48, 47, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 52, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 57, 58, 59, -1, -1, -1, -1, -1, 41, 42, 48, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -121, -120, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 55, -1, -1, -1, -1, -1, -1, 53, 64, 45, 45, 50, -1, -1, -1, -1, -1, -1, -1, -1, 41, 42, 45, 45, 45, 46, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, -1, -1, 53, 54, 60, 59, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 64, 45, 45, 58, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, -1, -1, 3, 4]}, {"type": 3, "obj": [[2, "544", 375, 667, 86, 107, 0], [2, "544", 291, 666, 86, 107, 2], [2, "984", 303, 667, 76, 52, 0], [2, "984", 375, 668, 76, 52, 2], [2, "984", 303, 715, 76, 52, 1], [2, "984", 375, 716, 76, 52, 3]]}, {"type": 2, "data": [37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 127, 127, 25, 25, 25, 25, 35, 35, 35, 35, 35, 35, -1, -1, -1, -1, -1, 115, 115, 115, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, -128, 124, 125, 127, 127, -127, 127, 26, 26, 26, -1, 115, 115, -1, -1, -1, -1, -1, -1, -1, 115, 115, 115, 27, 27, 27, 27, 27, 27, 27, 27, 27, -1, 27, 27, 27, -1, 27, -1, 27, 27, 27, 27, 124, 125, 107, 108, 107, 108, 109, 123, 124, 108, 115, 115, 115, 115, 115, 115, -1, 115, -1, 115, 115, 115, 115, 115, 28, 28, 28, 27, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, -1, 109, 110, 109, 110, 109, 110, 109, 110, -122, 127, 115, 115, 115, 115, -1, -1, -1, -1, -1, -1, -1, -1, 115, 115, -123, -123, -123, -97, -96, -97, -96, 127, 127, 127, -1, -1, 32, 29, 29, 29, 29, 29, 29, -1, -127, 108, 107, 108, 107, 108, 107, 110, -125, -126, -126, -126, -122, -123, -128, -1, -1, -1, -1, -1, -1, 115, 115, 115, -123, -95, -94, -95, -94, -95, -94, -95, -94, 127, -123, -123, -123, -123, 30, 30, 30, 30, 30, 30, 109, 112, 113, 110, 109, 110, 118, 117, 107, 108, 107, 108, -125, -126, 125, 108, -122, -1, 115, 115, 115, 115, 115, 115, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -123, -123, -123, -1, 34, 34, 34, 34, 34, 34, 108, 107, 108, 107, 108, -123, 112, 113, 107, 108, 108, 107, 108, 111, 112, 115, 115, 115, 115, 115, 115, 115, -95, -94, -95, -94, -95, -94, -95, -94, -95, -94, -95, -94, -1, -123, -123, 127, -123, -123, 33, 33, 33, 33, 33, 33, 110, 33, 33, 33, 115, 116, 112, 113, 110, 109, 110, 114, 115, 115, 115, 115, 115, 115, 115, 115, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, 127, 127, -123, -123, -123, 32, 32, 32, 32, 32, 32, 32, 32, 32, 127, -123, 115, 116, 112, 113, 107, 123, 124, -122, 127, -123, 127, 127, 115, 115, -95, -94, -95, -94, -95, -94, -95, -94, -95, -94, -95, -94, -123, 127, 127, -123, -123, -123, -128, -122, 31, 31, 31, 31, 31, 31, 31, 31, 127, 127, 127, 127, 115, 116, 117, 108, 107, 123, -126, -123, -123, 127, 127, 127, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -97, -96, -123, -123, -123, -123, -128, 124, 125, 110, -1, 36, 36, 36, 36, 36, 36, -123, 127, 127, 127, 127, 127, 127, 116, 118, 117, 109, 109, 126, 127, 127, 127, 127, -95, -94, -95, -94, -95, -94, -95, -94, -95, -94, -1, -126, -126, -126, -122, -128, 125, 108, 107, 119, 118, -1, 35, 35, 35, 35, 35, -123, 127, 127, 127, 127, 127, 127, 127, 121, 120, 107, 108, 123, 124, 127, 127, 127, 115, 115, -97, -96, -97, -96, -97, -96, 127, -128, 125, 107, 108, 107, -125, -127, 109, 110, 119, 122, 121, -1, -1, -1, 27, 27, 27, -123, -124, -122, -123, 115, 127, 127, 127, -128, 125, 109, 110, 109, 110, 127, 127, 127, 115, 115, 115, 115, 115, 115, 127, 127, 127, 116, 117, 109, 110, 109, 110, 123, -127, 111, 122, 122, 115, 115, 115, 28, 28, 28, 28, -126, -127, -125, -122, 115, 127, -128, 124, 125, 108, 108, 119, 118, 127, 127, 127, 127, 115, 115, 115, 115, 115, 127, 127, 127, 127, 127, 116, 118, 117, 107, 108, 107, 108, 123, -126, 124, -122, -123, 127, 127, 33, 33, 33, -127, 107, 108, 126, 115, 127, -124, 108, 109, 111, 112, 122, 121, 127, 127, 127, 127, -123, 115, 115, 115, 115, 127, -123, 127, -123, -123, -123, 121, 120, 109, 119, 118, 117, 109, 107, 108, -125, -122, 127, 127, 127, 33, 33, 116, 109, 112, 123, 115, 127, 120, 110, 108, 114, 115, 127, 127, 127, 127, 127, 127, -123, 115, 115, 115, 115, 115, 121, 121, 115, 115, -123, -128, 109, 110, 126, 121, 116, 113, 107, 108, 108, 123, -122, -123, 127, 127, 29, 29, 29, -122, -123, -1, 127, 116, 117, 108, -125, -126, 127, 127, 127, 127, 127, 127, 127, -128, -122, -123, 127, 121, 127, -128, -126, -126, -126, -127, 107, 108, 123, -122, 115, 116, 117, 110, 110, 109, -125, -126, 127, 127, 30, 30, 30, -125, -126, 115, 127, 127, 120, 110, 108, 123, 127, 127, 127, 127, 127, 127, 124, 125, -125, -126, -126, -126, -126, 125, 109, 110, 109, 110, 109, 110, 109, -125, 124, -122, 116, 118, 117, 107, 108, 107, -125, 127, 127, 34, 34, 34, 115, 115, -1, 127, 127, 109, 110, 115, -1, 127, 127, 127, 127, 127, 107, 108, 107, 108, 107, 108, 107, 108, 107, 108, 119, 118, 118, 117, 107, 108, 107, 108, -125, -122, 120, 109, 110, 109, 119, 118, 127, 29, 29, 29, 115, 115, -1, 115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 109, 119, 118, 112, 113, 110, 109, 110, 109, 111, 122, 121, 121, 116, 113, 110, 109, 110, 119, 122, -122, 112, 113, 111, 122, 127, 127, 32, 32, 32, 31, 115, 115, 115, -123, 115, -1, -1, -1, 121, 127, 121, 121, 121, 111, 122, 121, 121, 116, 117, 119, 118, 118, 122, 127, 127, 127, 127, 116, 118, 117, 111, 122, 115, 115, 115, 116, 122, 121, -1, -1, 31, 31, 31, 31, 27, 115, 115, 127, 127, 127, 107, 108, 123, -122, 121, 121, 121, 126, 127, 127, -128, -122, -123, 122, 121, 127, 121, 127, 121, 127, 127, 127, 127, 109, 115, 115, 115, 115, 115, -1, 109, 110, -1, 27, 27, 27, 27, 31, 31, 31, 115, 127, -123, -124, 109, 110, 108, -125, -126, -122, 121, 122, 127, -128, 125, -125, -126, -1, -1, 121, 121, 121, 127, -1, -1, -1, -1, -1, -1, -1, 37, 37, 37, 37, -1, -1, 31, 31, 31, 27, 27, 31, 31, 31, 127, 127, 127, -124, 107, 107, 108, 107, 108, 126, 121, 115, 115, -124, 111, 122, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 37, 37, 37, 37, 37, 37, -1, -1, 27, 27, 31, 31, 31, 115, 127, 127, 116, 109, 109, 107, 108, 110, 123, 121, 121, 121, -127, 111, 124, -1, 37, 37, -1, -1, -1, 107, 108, -126, -127, -125, -126, -122, 108, 38, 38, 38, 38, 37, 38, 38, 37, -1, 27, 27, 31, 31, 127, 115, 127, 127, 127, 116, 117, 109, 110, 111, 122, 121, -128, -127, 108, 126, 121, 38, 38, 38, 38, 38, 38, 127, -127, 109, 110, 109, 110, -125, -126, -122, 35, 26, 26, 26, 26, 26, -1, -1, -1, 31, 31, 127, 127, 115, 127, 127, 127, 127, -124, 107, 108, 114, 115, 127, 125, 110, 110, 126, 121, 127, 25, 25, 25, 25, 25, -124, 107, 108, 107, 108, 107, 108, 107, -125, -126, 28, 28, 28, 28, 28, 28, 26, 28, 28, 127, 127, 127, 115, 115, 127, 127, -128, -127, 109, 110, 114, 127, 127, 117, 107, 108, 126, 127, 121, 26, 26, 26, 25, 109, -127, 109, 110, 109, 110, 109, 110, 109, 126, 127, 32, 29, 29, 29, 29, 29, 26, 29, 115, 115, 127, -128, -122, 127, 127, -128, 125, 107, 108, 119, 122, 127, 127, 120, 110, 107, 123, 124, -122, -123, 121, 121, 121, 127, -128, 108, 107, 108, 107, 108, 107, 108, 123, 126, 127, 29, 29, 29, 29, 29, 29, 127, 127, -128, 124, 125, -125, -122, 115, -124, 110, 111, 112, 122, 121, 127, 127, 120, 109, 109, 110, 108, -125, -126, -122, -123, 121, -128, 125, 110, 109, 110, 109, 109, 119, 118, 117, 123, 124, 124, 124, -122, 115, -123, 115, 115, 127, -124, 109, 109, 110, -125, -126, -127, 107, 114, 115, 127, 127, 127, 115, 116, 116, 117, 109, 110, 110, 110, -125, -126, -126, -127, 111, 112, 118, 117, 108, 119, 122, 121, 116, 118, 117, 109, 110, -125, -126, -127, -125, -126, -126, 116, 118, 113, 109, 110, 109, 119, 118, 127, 127, 127, 127, 127, 127, -123, 115, 116, 118, 118, 117, 111, 112, 118, 117, 119, 122, 115, 121, 116, 118, 122, 121, 115, 127, 115, 116, 118, 118, 117, 109, 110, 119, 118, 117, 126, 115, 115, 116, 118, 118, 122, 121, 127, 127, 127, 127, 127, 127, -123, 127, 127, 127, 127, 127, 127, 127, 115, 116, 122, 115, 115, 115, 127, 127, 127, 127, 115, 127, 115, 127, 127, 127, 116, 118, 118, 122, 127, 116, 122, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 115, 115, 127, 115, 127, 127, 115, 115, 127, 127, 127, 127, 127, 127, 115, 115, 127, 115, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127, 127]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}