{"mW": 960, "mH": 960, "tW": 24, "tH": 24, "tiles": [["315_6", 0, 3, 3], ["1331", 0, 3, 3], ["1333", 0, 3, 2], ["1333", 2, 3, 2], ["1333", 1, 3, 2], ["1333", 3, 3, 2], ["1314", 0, 3, 2], ["1314", 2, 3, 2], ["1314", 1, 3, 2], ["1314", 3, 3, 2]], "layers": [{"type": 4, "obj": [[2, "85_6", 572, 180, 48, 53, 0], [2, "1231_2", 165, 153, 114, 162, 0], [2, "1161_1", 929, 281, 54, 52, 0], [2, "1161_1", 923, 307, 54, 52, 0], [2, "262_3", 908, 328, 48, 39, 0], [2, "1231_2", 888, 332, 114, 162, 0], [2, "1160_1", 911, 476, 88, 75, 0], [2, "1159_1", 943, 520, 36, 37, 2], [2, "1161_1", 892, 639, 54, 52, 2], [2, "1231_2", 47, 689, 114, 162, 0], [2, "1231_2", -53, 739, 114, 162, 0], [2, "1231_2", 489, 793, 114, 162, 0], [2, "1160_1", 886, 883, 88, 75, 0]]}, {"type": 3, "obj": [[2, "1157_1", 364, 629, 42, 41, 0], [2, "1183_1", 523, 233, 58, 31, 2], [2, "1184_1", 244, 17, 50, 36, 0], [2, "1183_1", 255, 69, 58, 31, 0], [2, "1162_1", 874, 913, 36, 46, 0], [2, "1160_1", 874, 657, 88, 75, 0], [2, "1158_1", 510, 70, 34, 37, 0], [2, "1161_1", 469, 227, 54, 52, 2], [2, "1160_1", 823, -22, 88, 75, 2], [2, "1184_1", 239, 310, 50, 36, 0], [2, "1182_1", 308, 292, 94, 46, 2], [2, "1182_1", 183, 320, 94, 46, 0], [2, "1185_1", 78, 335, 76, 68, 0], [2, "1182_1", 43, 376, 94, 46, 0], [2, "1184_1", 27, 410, 50, 36, 0], [2, "1183_1", 562, 188, 58, 31, 2], [2, "1182_1", 518, 703, 94, 46, 2], [2, "1160_1", 459, 590, 88, 75, 2], [2, "1162_1", 532, 615, 36, 46, 0], [2, "1162_1", 440, 623, 36, 46, 0], [2, "1161_1", 399, 633, 54, 52, 2], [2, "1161_1", 433, 655, 54, 52, 0], [2, "1182_1", 647, 606, 94, 46, 0], [2, "1183_1", 733, 632, 58, 31, 2], [2, "1185_1", 786, 616, 76, 68, 2], [2, "1159_1", 862, 660, 36, 37, 2], [2, "1183_1", 610, 633, 58, 31, 0], [2, "1185_1", 543, 632, 76, 68, 0], [2, "1161_1", 506, 629, 54, 52, 0], [2, "1160_1", 457, 651, 88, 75, 0], [2, "429_4", 370, 657, 64, 63, 0], [2, "1159_1", 346, 660, 36, 37, 2], [2, "1157_1", 199, 715, 42, 41, 0], [2, "21_2", 574, 602, 28, 24, 0], [2, "1157_1", 599, 592, 42, 41, 0], [2, "22_3", 659, 563, 62, 38, 0], [2, "1158_1", 640, 570, 34, 37, 0], [2, "1159_1", 699, 569, 36, 37, 0], [2, "262_3", 726, 605, 48, 39, 0], [2, "262_3", 499, 719, 48, 39, 0], [2, "21_2", 13, 308, 28, 24, 0], [2, "1334", 766, 767, 30, 30, 0], [2, "1182_1", 564, 213, 94, 46, 2], [2, "1184_1", 615, 242, 50, 36, 2], [2, "1184_1", 828, 669, 50, 36, 2], [2, "1183_1", 144, 348, 58, 31, 0], [2, "1182_1", 246, 276, 94, 46, 0], [2, "1332", 437, 74, 106, 57, 0], [2, "1159_1", 429, 78, 36, 37, 2], [2, "262_3", 687, 124, 48, 39, 0], [2, "1160_1", 884, -18, 88, 75, 0], [2, "1160_1", 764, 2, 88, 75, 0], [2, "1159_1", 759, 45, 36, 37, 2], [2, "1161_1", 799, -9, 54, 52, 2], [2, "1161_1", 875, 30, 54, 52, 2], [2, "85_6", 830, 40, 48, 53, 2], [2, "429_4", 908, 27, 64, 63, 0], [2, "1158_1", 928, 157, 34, 37, 0], [2, "1161_1", 493, 243, 54, 52, 0], [2, "262_3", 933, 183, 48, 39, 0], [2, "1160_1", -17, -26, 88, 75, 2], [2, "85_6", 53, 6, 48, 53, 2], [2, "1159_1", -8, 22, 36, 37, 2], [2, "1184_1", 282, 83, 50, 36, 2], [2, "1182_1", 286, 4, 94, 46, 2], [2, "1185_1", 366, 5, 76, 68, 2], [2, "1184_1", 407, 59, 50, 36, 2], [2, "1182_1", 179, 42, 94, 46, 0], [2, "262_3", 233, 65, 48, 39, 0], [2, "1182_1", 302, 94, 94, 46, 2], [2, "390_2", 306, 697, 102, 80, 2], [2, "1160_1", 698, 234, 88, 75, 0], [2, "1160_1", 625, 221, 88, 75, 0], [2, "1159_1", 655, 268, 36, 37, 2], [2, "1334", 293, 250, 30, 30, 0], [2, "1334", 204, 240, 30, 30, 0], [2, "1334", 20, 58, 30, 30, 0], [2, "1334", 55, 146, 30, 30, 2], [2, "1334", 660, 182, 30, 30, 0], [2, "1334", 48, 304, 30, 30, 0], [2, "1157_1", 519, 260, 42, 41, 0], [2, "262_3", 274, -12, 48, 39, 0], [2, "1159_1", 612, 239, 36, 37, 2], [2, "1158_1", 856, 680, 34, 37, 0], [2, "429_4", 390, 77, 64, 63, 0], [2, "1161_1", 677, 283, 54, 52, 2], [2, "1162_1", 715, 292, 36, 46, 0], [2, "429_4", 727, 290, 64, 63, 0], [2, "1163_1", 708, 181, 80, 54, 0], [2, "1161_1", 632, 196, 54, 52, 2], [2, "1161_1", 663, 205, 54, 52, 0], [2, "1185_1", 418, 250, 76, 68, 0], [2, "1183_1", 394, 301, 58, 31, 0], [2, "1160_1", 11, 418, 88, 75, 2], [2, "1162_1", 84, 443, 36, 46, 0], [2, "1162_1", -8, 451, 36, 46, 0], [2, "1161_1", -11, 483, 54, 52, 0], [2, "1182_1", 208, 438, 94, 46, 0], [2, "1183_1", 278, 452, 58, 31, 2], [2, "1183_1", 162, 461, 58, 31, 0], [2, "1185_1", 95, 460, 76, 68, 0], [2, "1161_1", 58, 457, 54, 52, 0], [2, "1160_1", 10, 479, 88, 75, 0], [2, "1182_1", 494, 366, 94, 46, 0], [2, "1183_1", 580, 392, 58, 31, 2], [2, "1183_1", 457, 393, 58, 31, 0], [2, "1185_1", 390, 392, 76, 68, 0], [2, "262_3", 573, 365, 48, 39, 0], [2, "1184_1", 623, 399, 50, 36, 2], [2, "1183_1", 345, 428, 58, 31, 0], [2, "1185_1", 697, 338, 76, 68, 0], [2, "1183_1", 664, 394, 58, 31, 0], [2, "390_2", 242, 733, 102, 80, 2], [2, "1160_1", 169, 743, 88, 75, 0], [2, "1162_1", 158, 767, 36, 46, 0], [2, "1162_1", 250, 787, 36, 46, 0], [2, "1161_1", 221, 793, 54, 52, 0], [2, "1161_1", 262, 804, 54, 52, 0], [2, "1162_1", 243, 816, 36, 46, 0], [2, "1160_1", 112, 765, 88, 75, 0], [2, "63_3", 75, 825, 16, 31, 0], [2, "63_3", 47, 805, 16, 31, 0], [2, "1182_1", -20, 824, 94, 46, 2], [2, "63_3", 89, 857, 16, 31, 0], [2, "1157_1", 39, 916, 42, 41, 0], [2, "1159_1", 109, 864, 36, 37, 2], [2, "262_3", 115, 827, 48, 39, 0], [2, "1183_1", 416, 934, 58, 31, 0], [2, "1185_1", 579, 902, 76, 68, 2], [2, "1183_1", 537, 897, 58, 31, 0], [2, "262_3", 1, 530, 48, 39, 0], [2, "429_4", 924, 700, 64, 63, 0], [2, "1332", 407, 698, 106, 57, 0], [2, "1157_1", 394, 705, 42, 41, 0], [2, "21_2", 769, 894, 28, 24, 0], [2, "63_3", 52, 174, 16, 31, 0], [2, "1182_1", -43, 184, 94, 46, 2], [2, "1185_1", -25, 210, 76, 68, 0], [2, "85_6", 48, 223, 48, 53, 2], [2, "1157_1", 13, 270, 42, 41, 0], [2, "262_3", 670, 725, 48, 39, 0], [2, "1185_1", 11, 852, 76, 68, 0], [2, "1161_1", -19, 911, 54, 52, 0], [2, "1334", 696, 31, 30, 30, 0], [2, "1334", 378, 266, 30, 30, 0], [2, "1334", 701, 514, 30, 30, 2], [2, "1334", 495, 444, 30, 30, 0], [2, "1334", 247, 688, 30, 30, 0], [2, "1334", 159, 655, 30, 30, 0], [2, "1334", 195, 597, 30, 30, 0], [2, "1334", 234, 613, 30, 30, 0], [2, "1334", 338, 513, 30, 30, 0], [2, "1334", 493, 554, 30, 30, 0], [2, "1334", 14, 615, 30, 30, 0], [2, "1334", 367, 785, 30, 30, 0], [2, "1334", 466, 802, 30, 30, 0], [2, "1334", 379, 862, 30, 30, 0], [2, "262_3", 316, 439, 48, 39, 0], [2, "1334", 447, 181, 30, 30, 0], [2, "1334", 515, 175, 30, 30, 0], [2, "1334", 586, 138, 30, 30, 0], [2, "1334", 157, 127, 30, 30, 0], [2, "1334", 178, 175, 30, 30, 0], [2, "1334", 876, 815, 30, 30, 0], [2, "1334", 919, 791, 30, 30, 0], [2, "1334", 619, 864, 30, 30, 0], [2, "1160_1", 590, 632, 88, 75, 0], [2, "1159_1", 645, 677, 36, 37, 0], [2, "1161_1", 763, 628, 54, 52, 2], [2, "1163_1", 668, 611, 80, 54, 0], [2, "1159_1", 559, 927, 36, 37, 0], [2, "1159_1", 288, 827, 36, 37, 0], [2, "1159_1", 2, 840, 36, 37, 0], [2, "1161_1", 178, 812, 54, 52, 2], [2, "1332", 93, 845, 106, 57, 2], [2, "21_2", 758, 374, 28, 24, 0], [2, "1159_1", 639, 384, 36, 37, 2], [2, "1183_1", 938, 357, 58, 31, 0], [2, "1159_1", 928, 353, 36, 37, 2], [2, "262_3", 793, 658, 48, 39, 0], [2, "1334", 848, 253, 30, 30, 0], [2, "1334", 811, 333, 30, 30, 0], [2, "1334", 887, 396, 30, 30, 0], [2, "1334", 785, 451, 30, 30, 0], [2, "1231_2", 695, -113, 114, 162, 0], [2, "1231_2", 633, 520, 114, 162, 0], [2, "1231_2", 42, -116, 114, 162, 0], [2, "1231_2", 638, 266, 114, 162, 0], [2, "85_6", 71, 864, 48, 53, 2]]}, {"type": 2, "data": [-1, -1, 49, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 46, 46, 46, 46, -1, 46, 46, -1, -1, 52, 52, 51, -1, -1, -1, -1, -1, 42, 49, 49, 49, -1, -1, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, -1, -1, 46, 46, 46, 46, 46, -1, 18, 19, 19, 20, -1, -1, -1, 56, -1, 55, 59, 55, 61, 61, 65, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, -1, 46, 46, 46, 46, 46, 46, -1, 21, 22, 22, 23, 19, 20, -1, -1, 65, -1, -1, -1, -1, -1, 62, 61, 43, -1, -1, -1, -1, 43, 44, -1, -1, -1, -1, -1, -1, 18, 19, 20, 54, 61, -1, 62, 61, 65, 64, -1, 38, 41, 40, 34, 22, 23, 19, 20, 62, 55, 56, -1, -1, -1, -1, 45, 46, 43, 43, 49, 53, 46, 63, -1, -1, -1, -1, 18, 19, 29, 34, 23, 25, 25, 24, -1, -1, 62, 61, -1, -1, 38, 37, 41, 34, 34, 22, 23, 25, 24, -1, -1, -1, -1, -1, -1, 55, 55, 55, 55, 55, 55, 56, -1, -1, -1, -1, 21, 22, 34, 34, 28, 40, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, 30, 41, 40, 34, 34, 28, 23, 25, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 41, 40, 40, 34, 34, 34, 23, 24, -1, -1, -1, 42, 43, 44, -1, -1, 33, 34, 34, 34, 34, 34, 34, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 34, 34, 28, 27, -1, 42, 43, 54, 55, 63, -1, -1, 30, 41, 40, 34, 34, 34, 34, 34, 23, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 37, 37, 31, 32, -1, 54, 55, -1, 61, 60, -1, -1, -1, 38, 37, 37, 37, 41, 34, 40, 34, 28, 27, -1, -1, -1, -1, -1, -1, -1, 50, 49, 48, -1, -1, -1, 50, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 49, 49, -1, -1, -1, -1, -1, -1, 38, 37, 37, 37, 37, 36, -1, -1, -1, -1, -1, -1, 52, 52, 52, 64, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 59, 55, 61, 61, 65, 64, -1, 42, 43, 43, 49, 48, -1, -1, -1, -1, -1, -1, 42, 43, 44, -1, -1, -1, -1, 42, 43, 43, 44, -1, -1, -1, -1, -1, -1, -1, 18, 58, 59, 55, 56, -1, -1, -1, 62, 61, 65, 64, 64, 49, 52, 51, -1, 26, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 61, 60, -1, 58, 59, 65, 64, -1, -1, 62, 61, 55, 56, -1, -1, -1, -1, -1, -1, -1, 62, 61, 64, 52, 59, 60, 18, 29, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 59, 56, 62, 61, 65, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 61, 64, 63, -1, 21, 40, 40, 39, 42, 58, -1, -1, -1, -1, -1, -1, 58, 59, 55, 55, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 61, 60, -1, 33, 34, 35, 36, 45, 58, -1, -1, -1, -1, -1, 58, 59, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 34, -1, -1, -1, -1, -1, -1, 54, -1, -1, -1, -1, 30, 31, 32, -1, 54, 55, -1, -1, -1, 58, 59, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 28, 34, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 56, -1, -1, -1, -1, -1, 49, 49, 49, 48, 53, -1, -1, -1, 26, 22, 28, 34, 22, 23, 25, 25, 25, 19, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, -1, -1, -1, -1, 48, -1, -1, -1, 26, 29, 28, 28, 34, 34, 34, 28, 28, 28, 22, 40, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 29, 28, 34, 34, 34, 40, 40, 34, 34, 34, 34, 40, 39, -1, -1, -1, -1, 26, 25, 24, -1, -1, -1, 46, -1, 57, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 29, 28, 34, 34, 40, 40, 40, 35, 37, 31, 41, 40, 40, 39, -1, -1, 18, 19, 29, 28, 27, -1, 42, 43, 65, 64, 59, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 34, 34, 34, 40, 35, 31, 32, -1, -1, 38, 37, 37, 36, -1, -1, 21, 22, 34, 34, 23, 24, 57, 58, 58, 59, 56, -1, -1, -1, -1, -1, -1, -1, 26, 19, 19, 19, 19, 29, 34, 34, 35, 31, 31, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 41, 34, 34, 40, 39, 54, 55, 55, 56, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 34, 34, 34, 34, 34, 40, 39, -1, -1, -1, -1, -1, -1, -1, 50, 49, 58, 43, 44, -1, -1, 38, 41, 34, 40, 23, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 31, 31, 41, 40, 40, 35, 37, 36, -1, -1, -1, 50, 49, 48, -1, 62, 61, 56, -1, -1, -1, -1, -1, 30, 31, 37, 37, 36, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 24, -1, -1, -1, 38, 37, 36, -1, -1, -1, -1, -1, -1, 53, 52, 51, 42, 43, 44, 43, 49, 49, 43, 44, -1, -1, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, 18, 19, 29, 28, 23, 25, 24, -1, -1, -1, 50, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 64, 59, 61, 65, -1, -1, 64, 52, 47, 43, 44, 42, -1, -1, -1, -1, -1, 21, 22, 22, 34, 34, 28, 27, -1, 50, 49, 48, 52, 52, 52, -1, -1, -1, 58, -1, -1, -1, -1, -1, 59, 60, -1, 54, 55, 55, 57, 58, -1, -1, 42, 43, -1, -1, -1, -1, -1, 30, 41, 40, 40, 40, 39, -1, -1, 62, 61, 61, -1, 64, 64, 64, -1, -1, 58, 63, 54, 55, 55, 61, 60, -1, -1, -1, -1, -1, 54, 65, 58, 58, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 37, 37, 36, -1, -1, -1, -1, -1, -1, 65, 64, 64, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 55, 65, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 61, 61, 61, 60, -1, -1, -1, -1, -1, -1, -1, -1, 18, 19, 19, 19, 20, -1, -1, -1, 62, 61, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 22, 22, 23, 24, -1, -1, -1, -1, -1, -1, -1, -1, 50, 49, -1, 64, 64, -1, -1, -1, 49, 48, -1, -1, -1, -1, 26, 25, 25, 25, 24, -1, -1, -1, -1, -1, -1, -1, 21, 22, 28, 28, 34, 23, 24, -1, -1, -1, -1, -1, -1, 42, 53, 58, 58, 64, 64, 64, -1, -1, 52, 51, -1, -1, 18, 19, 29, 28, 34, 28, 27, 26, 25, 24, -1, -1, -1, 26, 29, 34, 34, 34, 34, 28, 27, -1, -1, -1, -1, -1, -1, 45, 46, 58, 58, 58, 59, 65, 64, 64, 59, 60, -1, -1, 21, 22, 34, 34, 34, 34, 23, 29, 28, 27, -1, -1, -1, 33, 34, 34, 34, 35, 37, 31, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 56, 62, 61, 61, 60, -1, -1, -1, -1, 41, 40, 40, 40, 40, 39, -1, -1, -1, -1, -1, -1, 30, 31, 31, 37, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 37, 37, 37, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, 46, 46, 58]}, {"type": 2, "data": [9, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 11, 9, 10, 11, 14, 9, 10, 11, 9, 10, 11, 9, 10, 11, 12, 13, 9, 9, 10, 11, 11, 12, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 0, 1, 0, 1, 14, 12, 13, 14, 14, 12, 13, 14, 17, 12, 13, 14, 12, 13, 14, 12, 13, 14, 15, 16, 12, 12, 13, 14, 14, 15, 15, 16, 17, 15, 16, 9, 10, 11, 9, 0, 3, 4, 3, 0, 1, 2, 11, 17, 17, 15, 16, 17, 9, 15, 16, 17, 15, 16, 17, 9, 10, 11, 9, 10, 15, 15, 16, 17, 9, 10, 11, 9, 10, 11, 13, 9, 10, 11, 12, 3, 4, 6, 3, 3, 4, 5, 14, 10, 11, 9, 10, 11, 12, 13, 14, 17, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 9, 10, 11, 12, 13, 14, 15, 16, 12, 9, 3, 4, 7, 8, 10, 11, 10, 11, 13, 9, 10, 11, 9, 10, 11, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 12, 13, 9, 10, 11, 9, 9, 10, 11, 12, 13, 14, 9, 12, 13, 14, 13, 14, 16, 12, 13, 14, 9, 10, 11, 9, 10, 11, 9, 9, 10, 11, 9, 10, 11, 9, 10, 9, 10, 11, 15, 16, 12, 13, 14, 12, 9, 10, 11, 15, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 12, 13, 14, 12, 13, 14, 12, 12, 13, 14, 12, 13, 14, 12, 13, 9, 10, 11, 12, 13, 15, 16, 17, 15, 12, 13, 14, 15, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 9, 10, 9, 10, 11, 16, 17, 15, 15, 16, 17, 15, 16, 9, 10, 11, 12, 13, 14, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 16, 17, 15, 16, 17, 15, 9, 10, 11, 10, 12, 13, 12, 13, 14, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 9, 10, 11, 11, 14, 12, 13, 14, 12, 13, 14, 9, 10, 11, 12, 13, 14, 15, 16, 17, 12, 13, 14, 13, 15, 16, 15, 16, 17, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 12, 13, 14, 14, 10, 11, 16, 17, 15, 16, 17, 9, 10, 11, 9, 9, 10, 9, 10, 11, 15, 16, 0, 1, 0, 1, 2, 10, 11, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 15, 16, 17, 17, 13, 14, 13, 14, 12, 13, 14, 12, 13, 14, 12, 12, 13, 12, 13, 14, 0, 1, 2, 4, 3, 4, 5, 13, 14, 11, 10, 11, 9, 10, 11, 9, 9, 10, 11, 9, 15, 16, 17, 15, 16, 17, 9, 10, 11, 10, 11, 15, 16, 17, 15, 15, 16, 15, 16, 17, 3, 4, 0, 1, 2, 0, 0, 1, 2, 14, 13, 14, 12, 13, 14, 12, 12, 13, 14, 12, 12, 13, 9, 10, 11, 9, 12, 13, 14, 9, 10, 0, 1, 2, 2, 5, 0, 1, 2, 0, 6, 7, 3, 4, 5, 1, 2, 1, 0, 1, 2, 17, 15, 9, 9, 10, 11, 9, 10, 11, 9, 10, 11, 13, 14, 12, 15, 16, 17, 0, 1, 2, 2, 5, 5, 5, 3, 4, 5, 0, 3, 4, 6, 7, 8, 0, 0, 1, 2, 1, 2, 11, 9, 10, 11, 13, 14, 12, 13, 14, 12, 13, 14, 16, 17, 0, 1, 2, 2, 1, 0, 1, 2, 8, 0, 1, 0, 1, 2, 3, 6, 7, 6, 7, 6, 3, 3, 0, 1, 4, 5, 1, 9, 10, 11, 16, 17, 15, 16, 17, 15, 16, 17, 9, 10, 3, 4, 5, 5, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 9, 6, 6, 3, 4, 5, 9, 10, 12, 13, 14, 11, 17, 12, 13, 14, 12, 13, 0, 1, 2, 6, 0, 1, 2, 0, 6, 7, 8, 3, 4, 5, 6, 7, 8, 14, 17, 12, 13, 14, 12, 13, 9, 10, 11, 10, 12, 13, 15, 16, 17, 14, 9, 10, 11, 11, 13, 14, 3, 4, 5, 0, 0, 0, 0, 3, 4, 5, 4, 6, 7, 9, 10, 11, 16, 17, 10, 15, 16, 17, 15, 16, 12, 13, 14, 13, 15, 16, 17, 10, 11, 17, 10, 11, 14, 14, 16, 17, 14, 7, 8, 3, 3, 3, 3, 6, 9, 10, 16, 8, 14, 12, 13, 14, 14, 12, 13, 14, 12, 13, 14, 12, 15, 16, 17, 16, 17, 12, 12, 13, 14, 10, 11, 14, 17, 17, 12, 16, 17, 12, 15, 6, 9, 10, 11, 9, 12, 13, 14, 11, 17, 15, 16, 17, 17, 15, 16, 17, 15, 16, 17, 15, 9, 10, 11, 12, 13, 14, 15, 16, 17, 13, 14, 17, 13, 14, 15, 16, 17, 15, 16, 17, 12, 13, 14, 12, 15, 16, 17, 14, 9, 10, 11, 9, 10, 11, 9, 10, 11, 12, 13, 14, 12, 13, 14, 15, 16, 17, 13, 12, 15, 16, 17, 15, 16, 17, 9, 10, 11, 13, 14, 12, 15, 16, 17, 15, 16, 15, 16, 17, 12, 13, 14, 12, 13, 14, 12, 13, 14, 15, 16, 17, 15, 16, 17, 9, 10, 15, 16, 15, 16, 17, 13, 14, 15, 16, 12, 13, 14, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 11, 9, 10, 11, 9, 10, 12, 13, 14, 12, 13, 14, 15, 16, 17, 11, 9, 15, 16, 17, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 15, 16, 17, 15, 16, 17, 15, 16, 17, 14, 12, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 9, 10, 11, 9, 10, 11, 10, 9, 10, 11, 9, 10, 9, 10, 11, 9, 10, 11, 9, 10, 11, 15, 16, 17, 15, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 12, 13, 14, 12, 13, 14, 13, 12, 13, 9, 10, 11, 12, 13, 14, 12, 13, 14, 12, 13, 14, 9, 9, 10, 11, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 15, 16, 17, 15, 16, 17, 16, 15, 16, 12, 13, 14, 9, 10, 11, 15, 16, 17, 11, 9, 10, 11, 12, 13, 14, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 15, 16, 17, 0, 15, 16, 17, 12, 13, 14, 14, 12, 13, 14, 12, 13, 14, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 9, 10, 15, 16, 17, 17, 15, 16, 17, 15, 16, 17, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 12, 13, 14, 9, 10, 11, 14, 11, 9, 10, 11, 9, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 9, 10, 11, 15, 16, 17, 15, 16, 17, 9, 15, 16, 17, 12, 13, 14, 17, 14, 12, 13, 14, 12, 13, 14, 17, 9, 10, 9, 10, 9, 10, 11, 10, 11, 9, 10, 11, 9, 10, 11, 12, 13, 14, 15, 16, 17, 15, 16, 9, 12, 13, 14, 12, 15, 16, 17, 16, 17, 15, 16, 17, 15, 16, 17, 11, 12, 13, 12, 13, 12, 13, 14, 13, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 12, 15, 16, 17, 15, 16, 17, 13, 14, 12, 15, 16, 17, 14, 12, 13, 14, 15, 16, 15, 16, 15, 16, 17, 16, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 15, 16, 15, 16, 17, 9, 15, 16, 17, 15, 16, 17, 16, 17, 15, 16, 17, 9, 10, 11, 9, 10, 11, 9, 10, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 9, 10, 11, 12, 13, 14, 12, 13, 14, 12, 9, 10, 11, 10, 11, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 9, 10, 11, 9, 10, 11, 12, 13, 14, 15, 16, 17, 15, 16, 17, 15, 12, 13, 14, 13, 14, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 9, 10, 11, 9, 10, 9, 10, 11, 12, 13, 14, 12, 13, 14, 15, 16, 17, 11, 9, 10, 11, 9, 10, 11, 9, 10, 11, 9, 10, 9, 10, 11, 9, 10, 11, 12, 13, 14, 12, 13, 12, 13, 14, 12, 13, 12, 13, 14, 15, 16, 17, 15, 16, 17, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 12, 13, 14, 12, 13, 14, 15, 16, 17, 15, 16, 15, 16, 17, 15, 16, 15, 16, 17, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16]}], "blocks": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1]}