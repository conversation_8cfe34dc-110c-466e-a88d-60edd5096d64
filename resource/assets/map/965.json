{"mW": 960, "mH": 960, "tW": 24, "tH": 24, "tiles": [["315_7", 0, 3, 3], ["1233", 0, 3, 2], ["1233", 2, 3, 2], ["1233", 1, 3, 2], ["1233", 3, 3, 2], ["444_4", 0, 2, 2], ["417", 0, 1, 1], ["369_1", 0, 3, 3], ["304_1", 0, 3, 2], ["444_1", 0, 2, 2], ["419", 0, 4, 1], ["417", 0, 1, 1], ["418", 0, 1, 1], ["91", 0, 3, 2], ["444_1", 0, 2, 2], ["444_2", 0, 2, 2], ["709_2", 0, 2, 1], ["369_2", 0, 3, 3], ["450", 0, 1, 1], ["415_1", 0, 4, 1], ["415", 0, 4, 1], ["415_1", 1, 4, 1], ["415_1", 2, 4, 1], ["415_1", 3, 4, 1], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "1466", -100, 356, 158, 161, 2], [2, "704", 905, 150, 82, 76, 0], [2, "1466", 877, 419, 158, 161, 0], [2, "980", 582, 545, 58, 41, 0], [2, "980", 633, 548, 58, 41, 0], [2, "980", 43, 693, 58, 41, 0], [2, "980", 27, 445, 58, 41, 0], [2, "980", 739, 143, 58, 41, 0]]}, {"type": 4, "obj": [[2, "591", 865, -23, 16, 41, 0], [2, "593", 840, -14, 28, 48, 0], [2, "1460", 41, -9, 84, 59, 0], [2, "1466", 386, -94, 158, 161, 2], [4, 4, 475, 78, 0, 4022], [2, "592", 921, 20, 30, 65, 2], [2, "1455", 46, 19, 42, 76, 2], [4, 8, 176, 114, 0, 4024], [2, "1465", 274, 80, 122, 52, 5], [2, "1460", 835, 101, 84, 59, 0], [4, 9, 581, 179, 0, 4021], [2, "63", 158, 159, 16, 31, 0], [2, "51", 170, 175, 38, 35, 2], [2, "63", 93, 193, 16, 31, 0], [2, "50", 208, 203, 8, 23, 0], [2, "1466", 881, 74, 158, 161, 2], [2, "51", 103, 209, 38, 35, 2], [2, "1460", 869, 187, 84, 59, 0], [2, "51", 216, 213, 38, 35, 2], [2, "51", 218, 213, 38, 35, 2], [2, "50", 142, 232, 8, 23, 0], [2, "1460", 619, 196, 84, 59, 0], [2, "1464", 772, 144, 66, 123, 0], [2, "63", 256, 238, 16, 31, 0], [2, "1466", 630, 117, 158, 161, 0], [2, "51", 146, 244, 38, 35, 2], [2, "1465", 866, 162, 52, 122, 0], [2, "1466", -84, 124, 158, 161, 0], [2, "1460", 404, 228, 84, 59, 2], [2, "63", 181, 275, 16, 31, 0], [2, "1465", 449, 190, 52, 122, 2], [4, 3, 211, 360, 0, 4005], [4, 2, 628, 360, 0, 4005], [2, "1460", 95, 355, 84, 59, 2], [2, "1466", -26, 406, 158, 161, 0], [4, 6, 189, 610, 0, 4006], [4, 7, 919, 622, 0, 4006], [2, "1465", 52, 506, 52, 122, 2], [2, "1466", 441, 477, 158, 161, 0], [2, "1460", 523, 601, 84, 59, 2], [2, "1460", 286, 604, 84, 59, 2], [2, "1466", 653, 509, 158, 161, 0], [2, "62", 772, 673, 16, 27, 0], [2, "1364", 251, 649, 44, 69, 0], [2, "1460", -34, 663, 84, 59, 2], [2, "51", 739, 687, 38, 35, 0], [2, "62", 858, 702, 16, 27, 0], [2, "51", 706, 714, 38, 35, 0], [2, "51", 825, 720, 38, 35, 0], [2, "51", 679, 734, 38, 35, 0], [2, "1465", 431, 649, 52, 122, 2], [2, "62", 664, 754, 16, 27, 0], [2, "51", 791, 748, 38, 35, 0], [2, "1460", 373, 728, 84, 59, 2], [4, 5, 122, 789, 0, 4006], [4, 1, 422, 803, 0, 4005], [2, "1466", -7, 643, 158, 161, 2], [2, "51", 760, 776, 38, 35, 0], [2, "1460", -23, 771, 84, 59, 0], [2, "62", 749, 804, 16, 27, 0], [2, "62", 749, 804, 16, 27, 0], [2, "1464", 34, 732, 66, 123, 0], [2, "1460", 288, 832, 84, 59, 2], [2, "1455", 775, 825, 42, 76, 0], [2, "1460", 235, 853, 84, 59, 2], [2, "1466", 790, 763, 158, 161, 0], [2, "1460", 471, 873, 84, 59, 2], [2, "1466", 141, 794, 158, 161, 0], [2, "1460", 580, 897, 84, 59, 2], [2, "1460", 919, 907, 84, 59, 2], [2, "1460", 143, 915, 84, 59, 2]]}, {"type": 2, "data": [113, 113, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 120, 117, -1, -1, 113, 113, 113, 68, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 114, 113, 113, 120, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 116, 117, -1, -1, -1, 113, 113, -1, 119, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 126, 113, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 126, 113, 120, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 123, 116, 116, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 116, 121, -1, -1, -1, -1, -1, -1, 111, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 123, 126, 113, 113, 113, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 123, 116, 116, 116, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, -1, -1, -1, -1, 110, 110, 110, 110, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, -1, -1, 113, 113, -1, -1, 113, 113, 113, -1, 113, 113, 113, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 110, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, 113, 113, 113, 120, 116, 126, 113, 113, 113, 120, 113, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, 110, 65, -1, -1, -1, -1, -1, 123, 116, 116, 116, 116, 116, 121, -1, 115, 116, 116, 116, 121, 113, 120, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 126, 113, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 116, 116, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 113, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 114, 113, 113, 113, -1, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 110, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 119, 116, 126, 113, 113, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 123, 116, -1, 123, 116, 116, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, 121, -1, -1, -1, -1, -1, 111, 110, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, -1, -1, -1, -1, -1, -1, -1, 123, 116, 116, 121, -1, -1, -1, -1, -1, 118, 113, 113, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 110, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 123, 116, 116, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 113, 119, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 116, 116, 117, -1, -1, -1, -1, 111, 110, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 110, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 119, 119, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 119, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 122, 122, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 114, 113, 113, 119, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 113, -1, 119, 124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 126, 113, 113, 120, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 110, 110, 110, -1, -1, -1, 110, 110, 115, 122, 122, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, 113, 113, -1, 113, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 110, -1, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 109, -1, -1, -1, -1, -1, 118, 113, 113, 120, 126, 113, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 114, 113, 113, 68, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 116, 116, 117, 115, 126, 113, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, 113, 120, 116, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 123, 116, 116, 116]}, {"type": 3, "obj": [[2, "1457", 558, 183, 22, 30, 0], [2, "435", 322, -43, 50, 77, 2], [2, "397", 351, 127, 28, 61, 0], [2, "174_4", 2, 43, 68, 33, 0], [2, "1457", 567, -7, 22, 30, 0], [2, "325", 807, 266, 50, 37, 0], [2, "434", 87, 256, 70, 54, 2], [2, "434", 260, 148, 70, 54, 2], [2, "398", 250, 110, 58, 78, 2], [2, "398", 271, 89, 58, 78, 2], [2, "365", 328, 165, 48, 94, 0], [2, "398", 81, 211, 58, 78, 2], [2, "397", 319, 29, 28, 61, 2], [2, "397", 383, 46, 28, 61, 0], [2, "399", 205, 149, 58, 72, 2], [2, "398", 166, 165, 58, 78, 2], [2, "551", 592, 447, 30, 21, 0], [2, "550", 754, 825, 42, 28, 0], [2, "434", 717, 684, 70, 54, 0], [2, "433", 851, 712, 62, 61, 0], [2, "56_2", 777, 697, 76, 47, 0], [2, "63", 730, 748, 16, 31, 0], [2, "63", 780, 771, 16, 31, 0], [2, "63", 819, 749, 16, 31, 0], [2, "63", 842, 725, 16, 31, 0], [2, "1458", 673, 678, 34, 36, 0], [2, "1454", 291, 660, 90, 55, 0], [2, "56", 765, 712, 76, 47, 0], [2, "57", 756, 722, 72, 44, 0], [2, "58", 751, 730, 66, 39, 0], [2, "58", 742, 736, 66, 39, 0], [2, "57", 788, 691, 72, 44, 0], [2, "55_2", 729, 740, 70, 46, 0], [2, "58", 719, 755, 66, 39, 0], [2, "434", 913, 716, 70, 54, 2], [2, "58", 712, 760, 66, 39, 0], [2, "58", 704, 764, 66, 39, 0], [2, "436", 712, 657, 34, 28, 0], [2, "438", 713, 615, 26, 43, 0], [2, "435", 677, 546, 50, 77, 0], [2, "434", 608, 523, 70, 54, 0], [2, "439", 541, 555, 64, 42, 2], [2, "441", 524, 536, 28, 21, 2], [2, "438", 590, 591, 26, 43, 2], [2, "54", 677, 767, 86, 53, 0], [2, "437", 813, 841, 20, 19, 0], [2, "551", 772, 837, 30, 21, 0], [2, "552", 794, 841, 22, 15, 0], [2, "437", 834, 841, 20, 19, 0], [2, "439", 926, 806, 64, 42, 0], [2, "441", 899, 838, 28, 21, 0], [2, "437", 853, 841, 20, 19, 0], [2, "551", 834, 828, 30, 21, 0], [2, "1456", 900, 883, 24, 32, 0], [2, "1457", 939, 836, 22, 30, 0], [2, "174_4", 866, 907, 68, 33, 0], [2, "1460", 726, 592, 84, 59, 0], [2, "328", 768, 638, 32, 29, 0], [2, "326", 778, 644, 18, 14, 0], [2, "329", 728, 654, 42, 37, 2], [2, "327", 746, 678, 30, 22, 0], [2, "1457", 87, 785, 22, 30, 0], [2, "1456", 92, 810, 24, 32, 2], [2, "328", 0, 877, 32, 29, 0], [2, "325", 32, 835, 50, 37, 0], [2, "599", 283, 700, 40, 34, 0], [2, "392", 477, 426, 118, 69, 0], [2, "391", 415, 410, 86, 55, 0], [2, "436_3", 605, 500, 34, 28, 0], [2, "445", 585, 467, 50, 22, 0], [2, "392", 310, 414, 118, 69, 2], [2, "391", 254, 445, 86, 55, 0], [2, "550", 570, 456, 42, 28, 0], [2, "411", 584, 468, 44, 40, 0], [2, "366", 622, 464, 32, 48, 0], [2, "1368", 588, 487, 50, 42, 0], [2, "1368", 688, 630, 50, 42, 0], [2, "1368", 852, 717, 50, 42, 0], [2, "1368", 876, 724, 50, 42, 0], [2, "1368", 928, 786, 50, 42, 0], [2, "434", 133, 460, 70, 54, 0], [2, "433", 202, 456, 62, 61, 2], [2, "438", 90, 357, 26, 43, 0], [2, "436", 128, 440, 34, 28, 0], [2, "435", 4, 308, 50, 77, 2], [2, "438", 5, 385, 26, 43, 2], [2, "552", 310, 519, 22, 15, 0], [2, "437", 331, 518, 20, 19, 0], [2, "437", 352, 518, 20, 19, 0], [2, "439", 26, 470, 64, 42, 2], [2, "438", 15, 428, 26, 43, 2], [2, "439", 76, 508, 64, 42, 2], [2, "437", 140, 541, 20, 19, 0], [2, "437", 161, 540, 20, 19, 0], [2, "438", 143, 313, 26, 43, 0], [2, "441", 177, 260, 28, 21, 0], [2, "445", 194, 253, 50, 22, 2], [2, "399", 681, 184, 58, 72, 0], [2, "398", 911, 141, 58, 78, 2], [2, "398", 872, 156, 58, 78, 2], [2, "399", 824, 163, 58, 72, 0], [2, "399", 778, 166, 58, 72, 2], [2, "399", 732, 177, 58, 72, 2], [2, "363", 354, 194, 54, 75, 0], [2, "57", 106, 190, 72, 44, 2], [2, "55", 115, 197, 70, 46, 2], [2, "56", 126, 209, 76, 47, 2], [2, "56_2", 141, 220, 76, 47, 2], [2, "57", 158, 231, 72, 44, 2], [2, "58", 168, 241, 66, 39, 2], [2, "54", 176, 245, 86, 53, 2], [2, "441", 158, 292, 28, 21, 0], [2, "397", 248, 133, 28, 61, 2], [2, "361", 254, 88, 104, 58, 0], [2, "438", 394, 4, 26, 43, 0], [2, "412", 40, 225, 64, 100, 2], [2, "411", 26, 309, 44, 40, 0], [2, "425", 63, 311, 30, 36, 0], [2, "411", 12, 282, 44, 40, 0], [2, "550", 269, 244, 42, 28, 0], [2, "366", 303, 214, 32, 48, 0], [2, "411", 310, 236, 44, 40, 0], [2, "551", 393, 76, 30, 21, 0], [2, "552", 323, 62, 22, 15, 0], [2, "434", 213, 181, 70, 54, 2], [2, "1368", 244, 144, 50, 42, 0], [2, "550", 178, 537, 42, 28, 0], [2, "328", 286, 245, 32, 29, 0], [2, "445", 482, 528, 50, 22, 0], [2, "445", 365, 513, 50, 22, 2], [2, "437", 414, 513, 20, 19, 0], [2, "549", 463, 513, 34, 28, 2], [2, "550", 427, 510, 42, 28, 0], [2, "445", 212, 530, 50, 22, 2], [2, "445", 261, 522, 50, 22, 2], [2, "552", 269, 538, 22, 15, 0], [2, "552", 414, 518, 22, 15, 0], [2, "552", 573, 588, 22, 15, 0], [2, "552", 474, 535, 22, 15, 0], [2, "552", 477, 543, 22, 15, 0], [2, "552", 494, 541, 22, 15, 0], [2, "552", 511, 545, 22, 15, 0], [2, "552", 135, 547, 22, 15, 0], [2, "552", 163, 544, 22, 15, 0], [2, "548", 109, 537, 30, 24, 2], [2, "548", 62, 509, 30, 24, 2], [2, "552", 15, 469, 22, 15, 0], [2, "552", 92, 542, 22, 15, 0], [2, "1458", 936, 754, 34, 36, 2], [2, "325", 376, 471, 50, 37, 0], [2, "325", 432, 472, 50, 37, 2], [2, "553", 251, 452, 14, 8, 0], [2, "553", 381, 453, 14, 8, 0], [2, "553", 438, 465, 14, 8, 0], [2, "553", 416, 469, 14, 8, 0], [2, "174_4", 676, 258, 68, 33, 0], [2, "1357", 753, 267, 58, 32, 0], [2, "1456", 744, 237, 24, 32, 0], [2, "1457", 699, 243, 22, 30, 0], [2, "328", 824, 205, 32, 29, 0], [2, "327", 908, 248, 30, 22, 0], [2, "329", 940, 198, 42, 37, 0], [2, "328", 181, 455, 32, 29, 0], [2, "329", 341, 237, 42, 37, 0], [2, "1456", 324, 248, 24, 32, 0], [2, "174_2", 697, 293, 68, 33, 0], [2, "329", 837, 188, 42, 37, 2], [2, "1357", 932, 267, 58, 32, 0], [2, "1371", 553, 467, 42, 35, 0], [2, "1368", 212, 457, 50, 42, 0], [2, "22", 297, 896, 62, 38, 0], [2, "86", 888, 683, 50, 49, 0], [2, "421", 661, 488, 14, 11, 0], [2, "422", 669, 504, 16, 14, 0], [2, "420", 687, 521, 16, 13, 0], [2, "420", 741, 280, 16, 13, 0], [2, "422", 722, 276, 16, 14, 0], [2, "421", 918, 272, 14, 11, 0], [2, "422", 853, 240, 16, 14, 0], [2, "551", 243, 537, 30, 21, 0], [2, "422", 228, 541, 16, 14, 0], [2, "1371", 74, 508, 42, 35, 0], [2, "1368", 16, 319, 50, 42, 0], [2, "1368", 84, 259, 50, 42, 0], [2, "1371", 74, 291, 42, 35, 0], [2, "329", 45, 310, 42, 37, 0], [2, "422", 159, 447, 16, 14, 0], [2, "328", 344, 898, 32, 29, 0], [2, "327", 329, 925, 30, 22, 0], [2, "1457", 300, 904, 22, 30, 0], [2, "1457", 469, 741, 22, 30, 0], [2, "1457", 95, 539, 22, 30, 0], [2, "1456", 44, 527, 24, 32, 0], [2, "328", -11, 506, 32, 29, 0], [2, "326", -4, 515, 18, 14, 0], [2, "329", 131, 511, 42, 37, 0], [2, "445", 647, 756, 50, 22, 0], [2, "439", 607, 726, 64, 42, 2], [2, "438", 599, 634, 26, 43, 2], [2, "438", 600, 676, 26, 43, 3], [2, "551", 598, 711, 30, 21, 0], [2, "552", 664, 767, 22, 15, 0], [2, "549", 748, 814, 34, 28, 2], [2, "86", 838, 856, 50, 49, 0], [2, "325", 775, 876, 50, 37, 0], [2, "327", 590, 652, 30, 22, 0], [2, "328", 585, 687, 32, 29, 0], [2, "329", 592, 618, 42, 37, 0], [2, "328", 257, 715, 32, 29, 0], [2, "329", 463, 699, 42, 37, 0], [2, "329", 633, 716, 42, 37, 0], [2, "1457", 645, 745, 22, 30, 0], [2, "1456", 618, 748, 24, 32, 0], [2, "1456", 167, 598, 24, 32, 0], [2, "1457", 204, 536, 22, 30, 0], [2, "328", 183, 598, 32, 29, 0], [2, "174_4", 378, 896, 68, 33, 0], [2, "174_3", 304, 776, 68, 33, 0], [2, "174_4", 478, 586, 68, 33, 0], [2, "1456", 666, 470, 24, 32, 0], [2, "174_4", 894, 544, 68, 33, 0], [2, "1457", 931, 565, 22, 30, 0], [2, "1456", 945, 583, 24, 32, 0], [2, "328", 683, 508, 32, 29, 0], [2, "1457", 31, 43, 22, 30, 0], [2, "1456", 165, 142, 24, 32, 0], [2, "1457", 423, 17, 22, 30, 0], [2, "1457", 247, 120, 22, 30, 0], [2, "329", 319, 50, 42, 37, 0], [2, "1456", 19, 237, 24, 32, 0], [2, "1457", -5, 276, 22, 30, 0], [2, "174_2", 103, 58, 68, 33, 0], [2, "174_4", 23, 178, 68, 33, 0], [2, "174_2", 31, 574, 68, 33, 0], [2, "174_4", 304, 269, 68, 33, 0], [2, "21", 573, 353, 28, 24, 0], [2, "86", 592, 419, 50, 49, 0], [2, "422", 558, 363, 16, 14, 0], [2, "420", 589, 372, 16, 13, 0], [2, "422", 731, 439, 16, 14, 0], [2, "421", 895, 631, 14, 11, 0], [2, "420", 929, 595, 16, 13, 0], [2, "552", 947, 704, 22, 15, 0], [2, "219_1", 750, 896, 36, 30, 0], [2, "220_1", 776, 933, 40, 29, 0], [2, "220_1", 821, 392, 40, 29, 0], [2, "219_1", 204, 675, 36, 30, 0], [2, "219_1", 115, 748, 36, 30, 2], [2, "220_1", 613, 776, 40, 29, 0], [2, "220_1", 534, 198, 40, 29, 0], [2, "220_1", 801, 28, 40, 29, 0], [2, "220_1", 5, 77, 40, 29, 0], [2, "220_1", 208, 126, 40, 29, 0], [2, "219_1", 360, 335, 36, 30, 0], [2, "219_1", 438, 74, 36, 30, 0], [2, "219_1", 772, 36, 36, 30, 0], [2, "219_1", 703, 6, 36, 30, 0], [2, "219_1", 831, 224, 36, 30, 0], [2, "597", 750, 15, 34, 26, 0], [2, "594", 760, -14, 52, 46, 2], [2, "955", 733, -4, 20, 18, 0], [2, "220_1", 489, 17, 40, 29, 0], [2, "969", 548, 13, 36, 30, 0], [2, "1454", -32, 611, 90, 55, 0], [2, "422", 558, 727, 16, 14, 0], [2, "552", 462, 776, 22, 15, 0], [2, "553", 449, 776, 14, 8, 0], [2, "1371", 773, 814, 42, 35, 0], [2, "1371", 929, 665, 42, 35, 0], [2, "1368", -1, 367, 50, 42, 0], [2, "326", 98, 585, 18, 14, 0], [2, "327", 4, 830, 30, 22, 0], [2, "1457", 808, 382, 22, 30, 0], [2, "1456", 622, 202, 24, 32, 0], [2, "1457", 374, 344, 22, 30, 0], [2, "328", -6, 321, 32, 29, 0], [2, "1457", 834, 135, 22, 30, 2], [2, "328", 459, 6, 32, 29, 0], [2, "174_4", 466, 38, 68, 33, 0], [2, "174_2", 478, 132, 68, 33, 0], [2, "174_4", 628, 138, 68, 33, 0], [2, "174_4", -11, 107, 68, 33, 0], [2, "599", 8, 532, 40, 34, 0], [2, "599", 927, 880, 40, 34, 0], [2, "599", 248, 924, 40, 34, 0], [2, "599", 544, 895, 40, 34, 0], [2, "422", 722, 867, 16, 14, 0], [2, "421", 226, 779, 14, 11, 0], [2, "955", 17, 852, 20, 18, 0], [2, "421", 101, 875, 14, 11, 2], [2, "220_1", 225, 696, 40, 29, 2], [2, "220_1", 518, 654, 40, 29, 2], [2, "422", 130, 587, 16, 14, 0], [2, "553", 115, 572, 14, 8, 0], [2, "220_1", 726, 463, 40, 29, 0], [2, "1371", 391, 20, 42, 35, 0], [2, "364", 269, -44, 44, 64, 0], [2, "411", 233, -24, 44, 40, 0], [2, "328", 257, 3, 32, 29, 0], [2, "553", 150, 115, 14, 8, 0], [2, "1456", 526, 12, 24, 32, 0], [2, "1456", 280, -3, 24, 32, 0], [2, "174_2", 712, 405, 68, 33, 0], [2, "329", 85, 345, 42, 37, 0], [2, "552", 171, 306, 22, 15, 0], [2, "552", 155, 320, 22, 15, 0], [2, "436", 88, 398, 34, 28, 0], [2, "436", 96, 422, 34, 28, 0], [2, "436", 106, 436, 34, 28, 0], [2, "441", 104, 337, 28, 21, 0], [2, "441", 125, 320, 28, 21, 0], [2, "1368", 86, 414, 50, 42, 0], [2, "329", 278, 157, 42, 37, 0], [2, "599", 110, 330, 40, 34, 0], [2, "425", 112, 454, 30, 36, 2], [2, "552", 128, 437, 22, 15, 0], [2, "174_4", 159, 397, 68, 33, 0], [2, "1456", 216, 421, 24, 32, 0], [2, "220_1", 147, 310, 40, 29, 0], [2, "550", 372, 89, 42, 28, 0], [2, "362", 338, 105, 64, 42, 0], [2, "553", 369, 143, 14, 8, 0], [2, "422", 401, 61, 16, 14, 0], [2, "397", 667, 189, 28, 61, 0], [2, "422", 333, 523, 16, 14, 0], [2, "420", 350, 522, 16, 13, 0], [2, "422", 289, 531, 16, 14, 0], [2, "1457", 310, -10, 22, 30, 0], [2, "595", 802, 901, 50, 26, 0], [2, "597", 807, 870, 34, 26, 0], [2, "1356", 132, 547, 40, 33, 2], [2, "1356", 175, 547, 40, 33, 0], [2, "969", 716, 285, 36, 30, 0], [2, "955", 692, 289, 20, 18, 0], [2, "552", 845, 358, 22, 15, 0], [2, "955", 913, 288, 20, 18, 0], [2, "954", 115, 562, 24, 25, 0], [2, "965", 633, 256, 40, 33, 0], [2, "969", 370, 158, 36, 30, 2], [2, "955", 392, 188, 20, 18, 0], [2, "1357", 114, 6, 58, 32, 2], [2, "969", 174, -6, 36, 30, 0], [2, "969", 485, 253, 36, 30, 0], [2, "978", 7, 551, 66, 56, 0], [2, "1457", 29, 595, 22, 30, 0], [2, "1357", 59, 632, 58, 32, 0], [2, "220_1", 360, 777, 40, 29, 2], [2, "220_1", 874, 543, 40, 29, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 68, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 126, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 120, 117, 115, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 113, 68, 109, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 113, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 120, 126, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 112, 115, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 120, 116, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 120, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 126, 119, 119, 119, 119, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, -1, -1, -1, 123, 126, 119, 119, 119, 112, -1, -1, -1, 123, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 116, 116, 116, 121, -1, -1, -1, -1, -1, 123, 126, 119, 119, 112, -1, -1, -1, -1, 123, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 110, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 119, 119, 112, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 113, 113, 113, 109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 109, -1, -1, -1, 118, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 112, -1, -1, -1, 118, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 112, -1, -1, -1, 118, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 114, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 118, 113, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 115, 126, 116, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 111, 114, 67, 67, 67, 120, 116, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 114, -1, -1, 67, 120, 117, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 120, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [33, 34, 33, 33, 34, 33, 34, 25, 33, 33, 33, 34, 33, 33, 37, 37, 37, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 34, 34, 33, 33, 34, 33, 34, 34, 26, 23, 4, 4, 35, 33, 34, 35, 36, 26, 28, 28, 28, 32, 34, 33, 34, 36, 37, 37, 37, 13, 13, 13, 13, 19, 31, 13, 26, 28, 28, 28, 28, 28, 32, 35, 36, 35, 36, 26, 23, 4, 4, 17, 25, 35, 36, 33, 26, 23, 4, 4, 4, 29, 22, 22, 32, 33, 37, 37, 37, 13, 13, 13, 19, 31, 13, 26, 23, 4, 4, 4, 4, 4, 29, 32, 35, 26, 28, 27, 4, 4, 17, 20, 33, 34, 36, 26, 23, 4, 17, 16, 11, 4, 4, 4, 29, 32, 37, 37, 37, 13, 13, 13, 26, 22, 22, 23, 4, 17, 16, 16, 16, 15, 4, 29, 22, 23, 4, 4, 4, 17, 20, 36, 35, 36, 13, 18, 4, 17, 20, 13, 14, 16, 16, 16, 4, 37, 37, 37, 13, 13, 26, 22, 23, 4, 4, 4, 17, 20, 19, 33, 19, 14, 15, 4, 4, 4, 17, 16, 16, 20, 33, 13, 33, 34, 13, 30, 4, 12, 13, 13, 13, 13, 13, 4, 37, 37, 37, 13, 13, 13, 30, 4, 4, 9, 10, 10, 20, 19, 19, 35, 36, 19, 14, 10, 10, 10, 20, 13, 13, 13, 36, 36, 35, 36, 13, 30, 4, 12, 13, 13, 13, 13, 13, 37, 37, 37, 37, 13, 13, 13, 18, 4, 4, 12, 13, 13, 19, 13, 13, 13, 13, 33, 13, 13, 13, 13, 13, 13, 13, 13, 36, 36, 33, 34, 19, 18, 4, 21, 32, 13, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 18, 4, 4, 12, 19, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 4, 13, 13, 13, 13, 13, 35, 36, 13, 14, 15, 4, 4, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 18, 4, 4, 12, 13, 31, 25, 35, 13, 13, 13, 13, 4, 4, 4, 4, 13, 13, 13, 13, 33, 13, 19, 13, 13, 13, 4, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 14, 11, 4, 29, 22, 32, 13, 31, 13, 13, 13, 36, 35, 35, 36, 34, 13, 13, 13, 13, 36, 13, 19, 13, 13, 13, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 13, 14, 11, 4, 4, 21, 32, 13, 13, 19, 31, 19, 13, 13, 13, 13, 13, 13, 13, 13, 33, 33, 19, 13, 13, 37, 37, 37, 37, 37, 13, 13, 13, 33, 36, 33, 26, 22, 22, 32, 13, 13, 14, 15, 4, 4, 12, 13, 13, 13, 13, 19, 13, 19, 13, 13, 13, 13, 13, 13, 33, 33, 25, 13, 13, 37, 37, 37, 37, 37, 13, 13, 13, 26, 22, 22, 27, 4, 4, 29, 32, 13, 13, 30, 4, 4, 21, 28, 28, 32, 13, 13, 13, 13, 19, 19, 13, 13, 13, 13, 13, 13, 34, 37, 37, 37, 37, 37, 37, 13, 13, 36, 26, 27, 4, 4, 4, 4, 4, 4, 21, 22, 22, 23, 4, 4, 4, 4, 4, 29, 32, 13, 13, 13, 13, 13, 19, 13, 13, 13, 33, 13, 36, 37, 37, 37, 37, 13, 13, 13, 13, 13, 14, 15, 4, 4, 17, 16, 15, 4, 4, 4, 4, 4, 17, 16, 16, 15, 4, 4, 29, 22, 32, 13, 13, 13, 25, 13, 13, 13, 13, 13, 36, 37, 37, 37, 13, 13, 13, 13, 13, 13, 13, 14, 10, 10, 20, 13, 14, 16, 10, 16, 10, 10, 20, 13, 36, 14, 15, 4, 4, 4, 29, 32, 13, 13, 25, 13, 13, 13, 26, 28, 25, 37, 37, 37, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 34, 13, 13, 13, 13, 13, 13, 14, 10, 10, 15, 4, 29, 22, 32, 13, 13, 26, 22, 27, 4, 25, 37, 37, 37, 13, 13, 13, 13, 13, 13, 13, 31, 35, 13, 35, 13, 13, 13, 35, 36, 13, 13, 13, 13, 13, 13, 13, 13, 13, 14, 11, 4, 4, 29, 22, 22, 23, 4, 4, 4, 13, 37, 37, 37, 37, 13, 13, 13, 13, 13, 13, 13, 13, 13, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 13, 34, 13, 13, 13, 18, 4, 4, 4, 4, 4, 4, 4, 4, 4, 13, 37, 37, 37, 37, 37, 13, 13, 13, 13, 13, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 36, 35, 13, 14, 16, 11, 4, 4, 17, 16, 16, 13, 13, 13, 13, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 33, 34, 13, 13, 18, 4, 4, 24, 13, 13, 13, 13, 13, 13, 13, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 36, 13, 13, 14, 11, 4, 29, 32, 13, 13, 13, 19, 19, 19, 13, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 13, 19, 13, 13, 13, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 13, 13, 13, 18, 4, 4, 24, 13, 13, 33, 19, 19, 13, 13, 33, 34, 19, 19, 13, 13, 13, 13, 13, 26, 22, 22, 22, 22, 22, 32, 13, 13, 13, 37, 37, 37, 37, 37, 37, 13, 13, 13, 13, 18, 4, 4, 24, 13, 13, 33, 19, 19, 34, 33, 35, 36, 13, 13, 13, 26, 22, 22, 22, 23, 4, 4, 4, 4, 4, 24, 13, 13, 13, 13, 37, 37, 37, 37, 37, 13, 13, 13, 13, 14, 15, 4, 29, 32, 33, 13, 19, 19, 19, 35, 26, 22, 32, 31, 26, 23, 4, 4, 4, 4, 9, 16, 16, 15, 4, 29, 22, 32, 13, 13, 13, 37, 37, 37, 37, 37, 13, 13, 13, 13, 18, 4, 4, 24, 36, 36, 33, 19, 19, 13, 30, 4, 29, 28, 27, 4, 4, 17, 16, 16, 20, 13, 34, 18, 4, 4, 4, 24, 13, 13, 13, 37, 37, 37, 37, 37, 13, 13, 13, 13, 18, 4, 4, 12, 36, 13, 33, 34, 36, 36, 18, 4, 4, 4, 9, 10, 10, 20, 13, 13, 13, 13, 13, 14, 10, 11, 4, 24, 13, 13, 13, 37, 37, 37, 37, 37, 33, 34, 13, 13, 18, 4, 4, 24, 36, 13, 35, 36, 36, 33, 14, 4, 4, 4, 24, 13, 13, 13, 13, 19, 13, 13, 13, 13, 13, 14, 11, 29, 22, 32, 13, 13, 37, 37, 37, 37, 13, 34, 13, 26, 23, 4, 17, 20, 13, 13, 33, 36, 33, 34, 36, 14, 11, 4, 21, 32, 13, 13, 19, 19, 19, 13, 13, 13, 13, 13, 18, 4, 4, 21, 32, 37, 37, 37, 37, 37, 37, 37, 13, 13, 4, 17, 20, 13, 13, 13, 19, 19, 13, 13, 33, 33, 18, 4, 4, 24, 13, 13, 13, 19, 19, 19, 13, 13, 19, 33, 14, 15, 4, 1, 12, 13, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 13, 13, 13, 13, 33, 19, 33, 34, 35, 13, 18, 4, 4, 24, 13, 13, 13, 19, 19, 13, 19, 36, 36, 35, 13, 18, 4, 17, 20, 13, 13, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 13, 37, 19, 19, 34, 36, 35, 26, 23, 4, 4, 24, 13, 13, 13, 19, 13, 33, 19, 19, 19, 33, 34, 18, 4, 24, 13, 13, 13, 26, 4, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 33, 34, 13, 34, 33, 18, 4, 4, 4, 24, 13, 13, 13, 19, 19, 19, 19, 19, 19, 33, 13, 18, 4, 24, 13, 26, 22, 23, 4, 4, 4, 37, 37, 37, 37, 37, 37, 37, 37, 37, 35, 34, 34, 36, 35, 18, 4, 4, 4, 13, 13, 13, 35, 13, 13, 13, 13, 19, 19, 35, 36, 18, 4, 29, 28, 27, 4, 4, 4, 4, 17, 20, 37, 37, 37, 37, 37, 37, 37, 37, 34, 34, 34, 33, 34, 30, 4, 4, 4, 13, 13, 13, 13, 13, 13, 13, 13, 36, 35, 36, 26, 23, 4, 4, 4, 4, 4, 4, 4, 4, 24, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 34, 26, 22, 22, 27, 4, 4, 4, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 26, 27, 5, 5, 4, 4, 4, 4, 4, 4, 4, 29, 32, 13, 13, 13, 13, 13, 13, 13, 13, 13, 26, 23, 4, 4, 4, 4, 4, 17, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 18, 4, 5, 5, 9, 16, 16, 15, 4, 4, 4, 4, 24, 36, 13, 13, 36, 13, 13, 13, 13, 28, 27, 4, 4, 4, 4, 17, 20, 13, 13, 13, 13, 34, 13, 13, 26, 32, 33, 13, 14, 10, 10, 10, 20, 36, 36, 14, 11, 4, 4, 4, 21, 32, 13, 13, 13, 13, 13, 13, 13, 4, 4, 4, 4, 17, 16, 20, 34, 13, 13, 13, 34, 34, 13, 26, 23, 21, 22, 32, 13, 13, 13, 33, 33, 36, 36, 13, 14, 11, 4, 4, 4, 12, 13, 13, 13, 13, 13, 13, 13]}], "blocks": [0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}