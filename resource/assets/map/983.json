{"mW": 912, "mH": 672, "tW": 24, "tH": 24, "tiles": [["315_5", 0, 3, 3], ["1318", 0, 3, 2], ["1318", 2, 3, 2], ["1318", 1, 3, 2], ["1318", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["709_3", 0, 2, 1]], "layers": [{"type": 2, "data": [62, 62, 62, 62, 79, 87, 62, 79, 82, 82, 76, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 79, 80, 87, 86, 86, 78, 86, 62, 62, 62, 62, 62, 62, 62, 62, 62, 79, 80, 75, 73, 74, 75, -1, -1, 73, 74, 88, 87, 62, 62, 62, 78, 79, 74, 87, 78, 62, 79, 75, -1, 83, 82, 87, 86, 78, 79, 74, 87, 62, 62, 62, 62, 62, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 87, 62, 79, 75, -1, 73, 74, 82, 75, -1, -1, -1, -1, 83, 82, 82, 81, -1, 77, 79, 87, 62, 62, 62, 62, 63, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 70, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 75, 83, 80, 87, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 68, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 71, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 86, 86, 63, 59, -1, -1, -1, -1, -1, -1, 57, 66, 71, 62, 62, 70, 84, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 71, 86, 79, 74, 74, 75, -1, -1, -1, -1, -1, -1, 61, 62, 62, 70, 62, 86, 63, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 78, 79, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 82, 87, 78, 78, 70, 63, 66, 64, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 86, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 87, 70, 70, 70, 70, 70, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 86, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 74, 87, 78, 78, 79, 82, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 87, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 79, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 70, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 71, 62, 63, 72, 72, 58, 59, -1, -1, 57, 58, 59, -1, -1, -1, -1, -1, -1, 67, 71, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 86, 86, 86, 62, 63, 64, 64, 60, 70, 63, 65, -1, -1, -1, 57, 58, 71, 70, 79, 81, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 59, -1, -1, 67, 66, 71, 62, 62, 79, 76, 86, 79, 87, 78, 70, 70, 70, 86, 69, -1, -1, -1, 61, 78, 78, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, 67, 71, 62, 63, 59, 67, 60, 62, 62, 84, 82, 75, 83, 82, 75, 83, 82, 87, 86, 78, 79, 75, -1, -1, -1, 83, 87, 62, 62, 69, -1, -1, -1, -1, -1, -1, -1, 67, 71, 70, 70, 70, 63, 71, 62, 62, 79, 75, -1, -1, -1, -1, -1, -1, -1, 83, 82, 74, 75, -1, -1, -1, -1, -1, 83, 87, 62, 63, 58, 59, -1, -1, -1, -1, -1, 83, 82, 80, 88, 87, 86, 86, 84, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 78, 79, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 79, 75, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 71, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 58, 58, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 68, 59, -1, -1, 67, 64, 59, -1, -1, -1, -1, -1, 67, 66, 66, 65, -1, -1, -1, 67, 60, 78, 78, 79, 75, -1, -1, -1, -1, 67, 66, 72, 59, -1, -1, -1, 77, 62, 62, 63, 64, 64, 71, 70, 85, -1, -1, -1, -1, 67, 71, 62, 62, 69, -1, -1, -1, 83, 87, 70, 79, 75, -1, -1, -1, -1, 67, 60, 62, 62, 63, 65, 67, 64, 71, 62, 62, 62, 62, 62, 79, 82, 81, -1, -1, 67, 66, 71, 62, 62, 62, 68, 59, -1, -1, 67, 60, 70, 68, 59, -1, -1, 67, 66, 60, 70, 70, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 63, 64, 64, 64, 64, 71, 62, 62, 62, 62, 62, 62, 68, 66, 66, 71, 70, 62, 62, 63, 58, 72, 71, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62]}, {"type": 3, "obj": [[2, "1308", 582, 1, 22, 37, 0], [2, "1310", 504, 47, 18, 29, 0], [2, "1309", 426, 190, 20, 32, 2], [2, "1310", 870, 307, 18, 29, 0], [2, "1310", 715, 353, 18, 29, 0], [2, "1308", 274, 186, 22, 37, 0], [2, "1308", 826, 347, 22, 37, 0], [2, "1310", -1, 548, 18, 29, 2], [2, "1310", 537, 240, 18, 29, 2], [2, "1309", 650, 16, 20, 32, 0], [2, "1310", 806, 7, 18, 29, 0], [2, "1309", 684, 47, 20, 32, 0], [2, "1310", 569, 573, 18, 29, 2], [2, "1309", 195, 3, 20, 32, 2], [2, "1308", 108, 1, 22, 37, 0], [2, "1308", 335, 26, 22, 37, 0], [2, "1310", 862, 385, 18, 29, 0], [2, "1308", 759, 22, 22, 37, 0], [2, "1309", 470, 595, 20, 32, 0], [2, "1310", 672, 381, 18, 29, 2], [2, "1308", 512, 229, 22, 37, 0], [2, "1310", 495, 337, 18, 29, 0], [2, "1308", 540, 351, 22, 37, 0], [2, "1310", 742, 39, 18, 29, 0], [2, "1310", 319, 193, 18, 29, 2], [2, "1310", 411, 196, 18, 29, 0], [2, "1310", 381, 403, 18, 29, 0], [2, "1308", 844, 363, 22, 37, 0], [2, "1310", 234, 402, 18, 29, 2], [2, "1310", 198, 3, 18, 29, 2], [2, "1308", 347, 425, 22, 37, 0], [2, "1310", 799, 44, 18, 29, 0], [2, "1310", 705, 338, 18, 29, 2], [2, "1310", 408, 241, 18, 29, 2], [2, "1310", 502, 257, 18, 29, 2], [2, "1310", 368, 417, 18, 29, 0], [2, "1310", 141, 605, 18, 29, 0], [2, "1308", 84, 157, 22, 37, 0], [2, "1308", 65, 165, 22, 37, 0], [2, "1310", 848, 70, 18, 29, 0], [2, "1308", 512, 350, 22, 37, 0]]}, {"type": 4, "obj": [[2, "1321", 703, 29, 54, 84, 0], [2, "1321", 727, 29, 54, 84, 2], [2, "1321", 556, 330, 54, 84, 0]]}, {"type": 3, "obj": [[2, "364_3", 796, 386, 44, 64, 2], [2, "425_1", 611, 385, 30, 36, 0], [2, "1326", 563, 352, 58, 21, 0], [2, "1322", 495, 398, 64, 38, 0], [2, "1323", 547, 421, 94, 24, 0], [2, "1324", 630, 380, 40, 43, 0], [2, "1325", 487, 351, 80, 36, 0], [2, "1327", 611, 349, 70, 44, 0], [2, "1306_1", 475, 372, 24, 24, 2], [2, "426_1", 509, 373, 26, 22, 0], [2, "1144_2", 699, 405, 114, 70, 0], [2, "1311", 425, 36, 44, 81, 0], [2, "14_4", 832, 575, 32, 30, 2], [2, "1311", 634, 389, 44, 81, 2], [2, "1311", 293, 419, 44, 81, 0], [2, "1311", 248, 393, 44, 81, 0], [2, "1311", 341, 205, 44, 81, 2], [2, "1311", 169, 5, 44, 81, 2], [2, "1312", 141, 18, 36, 78, 0], [2, "1312", 493, 264, 36, 78, 0], [2, "1311", 813, 29, 44, 81, 2], [2, "1311", 298, 7, 44, 81, 0], [2, "1312", 270, 14, 36, 78, 0], [2, "1309", 172, 5, 20, 32, 2], [2, "1308", 154, 2, 22, 37, 0], [2, "1311", 201, 22, 44, 81, 2], [2, "1311", 234, 10, 44, 81, 0], [2, "212", 322, 21, 44, 99, 0], [2, "1306_1", 28, 95, 24, 24, 0], [2, "1306_1", 280, 67, 24, 24, 0], [2, "1311", 553, 21, 44, 81, 0], [2, "1311", 607, 5, 44, 81, 2], [2, "212", 576, 1, 44, 99, 0], [2, "212", 367, 194, 44, 99, 2], [2, "1311", 868, 500, 44, 81, 2], [2, "1311", 861, 581, 44, 81, 2], [2, "212", 275, 410, 44, 99, 2], [2, "1306_1", 832, 536, 24, 24, 0], [2, "411_3", 351, 148, 44, 40, 2], [2, "1311", 104, 616, 44, 81, 0], [2, "1311", 28, 173, 44, 81, 0], [2, "1311", 528, 49, 44, 81, 0], [2, "366_3", 453, 193, 32, 48, 2], [2, "1311", 672, 389, 44, 81, 0], [2, "1306_1", 839, 597, 24, 24, 2], [2, "1311", 535, 590, 44, 81, 0], [2, "1312", 859, 374, 36, 78, 2], [2, "1312", 450, 31, 36, 78, 2], [2, "11_1", 665, 453, 32, 29, 0], [2, "212", 201, 5, 44, 99, 0], [2, "1306_1", 19, 385, 24, 24, 0], [2, "11_1", 247, 83, 32, 29, 0], [2, "364_3", 278, 60, 44, 64, 2], [2, "1311", 477, 40, 44, 81, 2], [2, "212", 516, 30, 44, 99, 2], [2, "955_2", 441, 238, 20, 18, 0], [2, "955_2", 208, 303, 20, 18, 0], [2, "1306_1", 187, 640, 24, 24, 0], [2, "1109_1", 263, 472, 18, 21, 0], [2, "1109_1", 249, 481, 18, 21, 0], [2, "1109_1", 269, 488, 18, 21, 0], [2, "13_1", 396, 290, 22, 24, 0], [2, "955_2", 490, 451, 20, 18, 2], [2, "14_4", 318, 107, 32, 30, 2], [2, "14_4", 338, 122, 32, 30, 2], [2, "14_4", 327, 99, 32, 30, 2], [2, "1301_1", 402, 623, 24, 49, 2], [2, "1306_1", 296, 484, 24, 24, 0], [2, "1306_1", 589, 635, 24, 24, 0], [2, "1311", 391, 63, 44, 81, 0], [2, "425_1", 786, 73, 30, 36, 0], [2, "1326", 738, 40, 58, 21, 0], [2, "1322", 670, 86, 64, 38, 0], [2, "1323", 722, 109, 94, 24, 0], [2, "1324", 805, 68, 40, 43, 0], [2, "1325", 662, 39, 80, 36, 0], [2, "1327", 786, 37, 70, 44, 0], [2, "1306_1", 650, 60, 24, 24, 2], [2, "426_1", 684, 61, 26, 22, 0], [2, "955_2", 687, 112, 20, 18, 0], [2, "1311", 386, 224, 44, 81, 2], [2, "1311", 455, 265, 44, 81, 2], [2, "1311", 415, 240, 44, 81, 2], [2, "212", 442, 250, 44, 99, 0], [2, "1306_1", 180, 72, 24, 24, 2], [2, "1306_1", 426, 138, 24, 24, 2], [2, "1301_1", 204, 71, 24, 49, 2], [2, "1301_1", 552, 77, 24, 49, 2], [2, "955_2", 225, 551, 20, 18, 2], [2, "955_2", 581, 125, 20, 18, 2], [2, "116_1", 352, 276, 46, 39, 2], [2, "113_1", 357, 264, 26, 33, 0], [2, "1311", 831, 368, 44, 81, 2], [2, "181_2", 55, 15, 104, 100, 2], [2, "212", -5, 182, 44, 99, 2], [2, "1301_1", 78, 89, 24, 49, 0], [2, "1301_1", 21, 247, 24, 49, 0], [2, "1306_1", 814, 95, 24, 24, 2], [2, "1311", 844, 67, 44, 81, 2], [2, "955_2", 802, 113, 20, 18, 2], [2, "955_2", 866, 154, 20, 18, 2], [2, "1108_1", 159, 71, 26, 31, 2], [2, "8_1", 441, 100, 38, 29, 0], [2, "1109_1", 456, 91, 18, 21, 0], [2, "1109_1", 441, 95, 18, 21, 0], [2, "1109_1", 458, 98, 18, 21, 0], [2, "1109_1", 435, 109, 18, 21, 0], [2, "1109_1", 494, 112, 18, 21, 0], [2, "11_1", 816, 118, 32, 29, 0], [2, "1306_1", 265, 533, 24, 24, 0], [2, "1306_1", 624, 443, 24, 24, 0], [2, "1306_1", 447, 336, 24, 24, 0], [2, "1306_1", 325, 268, 24, 24, 2], [2, "1301_1", 321, 252, 24, 49, 2], [2, "955_2", 306, 283, 20, 18, 2], [2, "1311", 205, 412, 44, 81, 2], [2, "1311", 453, 358, 44, 81, 0], [2, "1312", 430, 371, 36, 78, 0], [2, "1311", 399, 392, 44, 81, 0], [2, "1311", 362, 405, 44, 81, 2], [2, "1311", 331, 416, 44, 81, 0], [2, "1301_1", 329, 465, 24, 49, 2], [2, "1306_1", 380, 468, 24, 24, 0], [2, "1306_1", 778, 395, 24, 24, 2], [2, "1306_1", 805, 429, 24, 24, 0], [2, "1306_1", 698, 448, 24, 24, 0], [2, "1311", 8, 442, 44, 81, 0], [2, "1312", 0, 330, 36, 78, 0], [2, "116_1", 839, 431, 46, 39, 0], [2, "113_1", 853, 411, 26, 33, 0], [2, "1311", 872, 75, 44, 81, 2], [2, "1312", 873, 238, 36, 78, 2], [2, "8_1", 415, 457, 38, 29, 0], [2, "1109_1", 430, 448, 18, 21, 0], [2, "1109_1", 415, 452, 18, 21, 0], [2, "1109_1", 432, 455, 18, 21, 0], [2, "1109_1", 409, 466, 18, 21, 0], [2, "1109_1", 468, 469, 18, 21, 0], [2, "1306_1", 465, 417, 24, 24, 2], [2, "1306_1", 21, 506, 24, 24, 2], [2, "955_2", 804, 503, 20, 18, 2], [2, "8_1", 848, 475, 38, 29, 2], [2, "12_1", 854, 463, 26, 28, 0], [2, "1301_1", 860, 109, 24, 49, 2], [2, "955_2", 519, 422, 20, 18, 2], [2, "1320", 471, 139, 76, 64, 0], [2, "12_1", 481, 142, 26, 28, 0], [2, "8_1", 528, 184, 38, 29, 2], [2, "1306_1", 282, 264, 24, 24, 0], [2, "955_2", 844, 300, 20, 18, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, -1, -1, -1, 49, -1, 49, 49, 49, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, 43, 43, 43, 43, 55, -1, -1, -1, -1, 49, 49, 49, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, 43, 43, 43, 43, -1, 55, 55, -1, 55, 55, 49, 37, 49, 46, 47, 52, 56, 49, 48, 49, -1, 56, 43, 43, 43, 49, 49, 49, 50, -1, -1, 37, 37, 37, 37, 49, 50, 52, 52, 52, 56, 43, -1, -1, 55, -1, 55, 55, 37, 37, 37, 54, -1, -1, 45, 46, 45, 46, 49, 53, 56, 43, 43, 46, 49, 50, 56, 55, -1, 50, 52, 52, 52, 56, 38, 39, -1, -1, 45, 46, 56, 55, 55, -1, 55, 55, 37, 37, 50, 51, 9, 10, 15, -1, -1, 45, 46, 56, 55, 43, 43, 49, 46, 47, 48, 55, 50, 51, -1, -1, -1, 53, 52, 51, -1, -1, -1, 46, 56, 56, 55, -1, 55, 55, 55, 37, 54, -1, 12, 13, 14, 16, 15, -1, -1, 53, 52, 45, 49, 50, 54, 35, 53, 52, 51, -1, 17, 16, 15, -1, 9, 10, 16, 16, 15, -1, 53, 53, 52, 55, 27, -1, 37, 37, 54, 17, 20, 31, 13, 31, 14, 15, 26, 27, 53, 56, 46, 47, 37, 38, 40, 39, -1, 9, 20, 19, 31, 16, 20, 25, 25, 25, 14, 15, -1, -1, 53, 52, -1, 37, 37, 37, 54, 24, 25, 25, 31, 31, 13, 13, -1, 56, 56, 45, 46, 52, 49, 49, 50, 51, 38, 21, 32, 31, 31, 31, 31, 31, 25, 25, 31, 30, -1, -1, -1, -1, 37, 37, 50, 52, 51, 21, 32, 31, 31, 31, 26, 27, -1, 53, 56, 34, -1, 49, 49, 55, 54, 45, 56, -1, 24, 31, 31, 31, 26, 32, 31, 25, 26, 27, -1, 41, 40, 28, 37, 52, 51, -1, -1, -1, 24, 31, 31, 31, 30, -1, 33, 34, 44, 27, 49, 49, 50, 52, 51, -1, -1, -1, 24, 31, 31, 31, 27, 29, 28, 28, 27, -1, 41, 44, 43, -1, 50, 49, 50, -1, 9, 10, 20, 31, 31, 31, 16, 15, 45, 46, 56, 49, 49, 50, 47, 17, 16, 40, 39, -1, 21, 22, 28, 27, -1, -1, -1, -1, -1, -1, 53, 56, 55, 40, -1, 54, 47, -1, 12, 13, 31, 31, 31, 31, 31, 30, -1, -1, 56, 55, 55, 56, 17, 20, 31, 43, 42, -1, -1, -1, -1, -1, -1, 9, 10, 15, -1, -1, -1, 53, 52, 43, -1, 35, -1, -1, 24, 25, 31, 31, 31, 31, 26, 27, -1, -1, 53, 52, 56, 44, 56, 55, 49, 49, 49, 49, -1, -1, -1, -1, 38, 12, 13, 14, 16, 15, -1, 53, 52, 46, -1, 38, 39, -1, 21, 32, 31, 31, 26, 28, 27, -1, -1, -1, -1, -1, 44, 43, 45, 46, 49, 49, 49, 49, 49, 49, 49, 55, 16, 24, 25, 26, 28, 27, -1, 41, 40, -1, -1, 43, 42, -1, -1, 21, 22, 28, 27, -1, -1, -1, -1, 41, 40, -1, 9, 10, 16, 15, 49, -1, 45, 52, 56, 55, 55, 52, 26, 21, 22, 23, -1, 41, 40, 44, 43, -1, 55, 55, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, -1, 9, 20, 31, 14, 16, 39, -1, -1, 48, 55, 55, -1, 41, 40, 39, -1, -1, 44, 43, -1, -1, -1, 55, 55, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, -1, -1, -1, 21, 32, 19, 49, 55, 42, -1, -1, 45, 56, 55, -1, 44, 43, 42, -1, 42, -1, 33, 34, -1, -1, 55, -1, -1, -1, -1, -1, -1, -1, 33, 34, 49, 49, 20, -1, -1, -1, -1, 9, 49, 49, 50, 51, -1, -1, -1, 56, 52, -1, 37, 49, 50, 46, 47, -1, 45, 46, -1, -1, 55, 55, -1, -1, -1, -1, -1, 33, 44, 49, 49, 49, 49, 40, 40, 39, -1, 49, 50, 46, 47, 17, 16, 16, 15, 45, 56, 55, 49, 50, 47, 17, 16, 15, 33, 34, 37, -1, -1, -1, -1, 9, 10, 16, 15, 36, 49, 49, 49, 56, 55, 40, 55, 38, 49, 46, 47, 9, 10, 20, 25, 31, 30, -1, 45, 46, 52, 51, 9, 20, 19, 18, 45, 56, 37, -1, 49, 50, -1, 12, 13, 14, 30, 45, 46, 52, 56, 55, 50, 46, 46, 52, 47, -1, -1, 12, 13, 31, 31, 31, 14, 16, 15, -1, 9, 10, 20, 31, 31, 14, 15, 48, 37, -1, 49, 50, 9, 20, 13, 13, 14, 16, 15, -1, 53, 52, 38, 39, -1, -1, -1, -1, -1, -1, 29, 32, 31, 31, 31, 31, 30, -1, 12, 13, 25, 25, 31, 26, 27, 44, 37, -1, 46, 47, 12, 13, 13, 13, 13, 13, 14, 16, 15, -1, 52, 51, -1, 9, 10, 16, 15, -1, -1, 29, 28, 28, 28, 28, 27, -1, -1, 21, 22, 22, 28, 27, 48, 43, 37, -1, 49, -1, 21, 22, 28, 13, 13, 13, 13, 19, 18, -1, -1, 9, 10, 20, 19, 26, 27, -1, 45, 46, -1, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 43, 37, -1, 40, 46, 47, -1, -1, 28, -1, 21, 22, 28, 27, -1, -1, 21, 22, 28, 28, 27, -1, -1, -1, -1, 45, -1, -1, -1, -1, 33, 40, 39, -1, -1, 41, 40, 44, 37, 37, -1, 37, 38, 39, 41, 40, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, -1, 14, 15, 40, 40, 39, 33, 36, 43, 42, -1, 28, 44, 43, 43, 48, 43, -1, -1, 43, 38, 44, 43, 43, 42, -1, -1, -1, -1, 40, 39, -1, -1, -1, -1, -1, 36, 37, -1, -1, -1, 43, 43, 42, 36, 37, -1, -1, -1, -1, 29, 28, 27, 44, 43, -1]}, {"type": 2, "data": [0, 1, 0, 1, 2, 3, 4, 5, 1, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 1, 2, 0, 0, 1, 2, 1, 2, 0, 1, 2, 1, 2, 1, 2, 0, 1, 2, 0, 3, 0, 1, 2, 2, 6, 7, 8, 4, 3, 4, 5, 3, 3, 4, 5, 3, 4, 5, 4, 5, 3, 3, 4, 5, 4, 5, 3, 4, 5, 4, 5, 4, 0, 1, 2, 2, 3, 6, 3, 4, 5, 5, 3, 0, 1, 2, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 7, 8, 0, 1, 2, 0, 1, 2, 6, 7, 89, 90, 89, 90, 89, 90, 89, 90, 6, 0, 6, 7, 8, 8, 0, 0, 1, 2, 3, 4, 5, 1, 2, 1, 2, 3, 0, 1, 2, 1, 3, 4, 5, 3, 4, 5, 0, 89, 89, 90, 89, 90, 89, 90, 89, 90, 90, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 4, 5, 6, 3, 4, 5, 0, 6, 7, 8, 6, 7, 8, 3, 8, 90, 89, 90, 89, 90, 90, 4, 5, 0, 6, 7, 0, 1, 2, 5, 6, 0, 1, 0, 1, 2, 4, 5, 0, 1, 2, 6, 7, 8, 2, 4, 5, 2, 3, 0, 1, 2, 7, 3, 4, 5, 0, 1, 6, 7, 8, 3, 0, 1, 3, 4, 5, 8, 0, 3, 4, 3, 4, 5, 7, 8, 3, 4, 5, 5, 3, 4, 5, 0, 0, 1, 0, 1, 2, 5, 6, 6, 7, 8, 3, 4, 5, 6, 7, 6, 0, 1, 2, 7, 0, 0, 1, 6, 7, 6, 7, 8, 4, 5, 6, 7, 8, 1, 6, 7, 8, 3, 3, 4, 3, 4, 5, 8, 1, 2, 4, 0, 6, 7, 8, 5, 3, 3, 3, 4, 5, 1, 3, 3, 4, 3, 4, 5, 0, 1, 2, 8, 7, 8, 3, 4, 0, 1, 2, 1, 6, 7, 6, 0, 1, 2, 4, 5, 7, 3, 6, 7, 8, 0, 1, 6, 6, 7, 8, 4, 6, 6, 7, 6, 7, 8, 3, 4, 5, 4, 5, 3, 6, 7, 3, 4, 5, 4, 5, 5, 1, 3, 4, 5, 1, 2, 0, 6, 7, 8, 1, 3, 4, 0, 0, 1, 6, 7, 8, 8, 0, 0, 0, 0, 1, 2, 0, 1, 2, 6, 6, 3, 6, 7, 8, 7, 8, 8, 4, 6, 7, 8, 0, 1, 2, 4, 5, 7, 8, 8, 7, 3, 3, 4, 0, 1, 2, 3, 3, 8, 0, 1, 2, 0, 1, 2, 5, 2, 2, 6, 7, 8, 6, 3, 4, 5, 0, 1, 2, 8, 3, 4, 5, 7, 0, 1, 2, 0, 1, 2, 6, 7, 3, 4, 5, 6, 6, 6, 3, 4, 5, 3, 4, 5, 8, 5, 5, 6, 7, 6, 0, 1, 2, 8, 3, 4, 5, 1, 2, 7, 0, 1, 2, 1, 2, 0, 1, 2, 3, 4, 6, 7, 8, 7, 8, 1, 6, 7, 8, 6, 7, 8, 7, 8, 8, 1, 2, 0, 3, 4, 5, 8, 6, 7, 8, 4, 5, 3, 3, 4, 5, 4, 5, 3, 4, 5, 6, 7, 8, 0, 1, 0, 3, 4, 5, 4, 0, 1, 3, 4, 0, 1, 2, 4, 5, 3, 6, 7, 8, 0, 0, 3, 6, 7, 8, 6, 6, 7, 8, 7, 8, 6, 7, 8, 0, 1, 0, 0, 1, 3, 6, 7, 8, 2, 3, 4, 6, 7, 3, 4, 5, 7, 8, 6, 3, 0, 89, 90, 89, 90, 89, 7, 8, 2, 7, 6, 7, 8, 3, 4, 5, 6, 3, 4, 3, 0, 1, 2, 7, 3, 4, 5, 6, 7, 8, 4, 6, 7, 8, 2, 4, 5, 0, 89, 90, 89, 90, 89, 90, 3, 4, 5, 3, 4, 5, 3, 6, 7, 8, 0, 6, 0, 6, 3, 4, 5, 6, 6, 7, 8, 0, 1, 6, 7, 8, 0, 1, 2, 7, 0, 6, 7, 89, 90, 89, 90, 89, 90, 7, 0, 1, 2, 0, 1, 3, 4, 5, 3, 0, 1, 2, 6, 7, 8, 0, 1, 2, 2, 3, 0, 1, 2, 1, 2, 4, 5, 5, 3, 4, 5, 6, 7, 8, 6, 7, 8, 5, 3, 4, 5, 3, 4, 6, 7, 8, 6, 3, 4, 5, 3, 3, 3, 3, 4, 5, 5, 6, 3, 4, 5, 4, 5, 7, 8, 0, 6, 7, 8, 6, 7, 8, 0, 1, 2, 8, 6, 7, 8, 6, 7, 8, 3, 4, 0, 6, 7, 8, 0, 1, 6, 6, 7, 8, 8, 0, 6, 7, 8, 7, 8, 5, 4, 0, 1, 2, 8, 4, 5, 4, 3, 4, 5, 0, 1, 2, 2, 3, 4, 5, 6, 7, 3, 0, 0, 1, 3, 4, 5, 0, 3, 4, 5, 3, 4, 0, 6, 7, 8, 0, 0, 3, 4, 5, 6, 7, 8, 7, 6, 7, 8, 3, 4, 5, 8, 6, 7, 8, 3, 4, 6, 3, 3, 4, 6, 7, 8, 0, 1, 2, 0, 0, 1, 2, 4, 5, 0, 3, 3, 6, 7, 8, 4, 3, 4, 5, 6, 7, 8, 6, 7, 8, 5, 0, 1, 2, 0, 1, 0, 6, 6, 7, 0, 0, 4, 3, 4, 5, 3, 3, 4, 5, 2, 8, 0, 6, 6, 7, 8, 8, 0, 6, 7, 8, 4, 5, 3, 0, 1, 2, 8, 3, 4, 5, 0, 1, 3, 0, 0, 1, 3, 3, 4, 6, 7, 8, 6, 6, 7, 8, 5, 5, 3, 4, 3, 4, 5, 0, 3, 4, 5, 6, 7, 8, 6, 3, 4, 5, 2, 6, 7, 8, 3, 4, 6, 3, 3, 4, 6, 6, 7, 8, 6, 7, 8, 5, 6, 7, 8, 8, 6, 7, 6, 7, 8, 0, 6, 7, 8, 2, 3, 4, 5, 6, 0, 1, 2, 2, 0, 1, 6, 7, 0, 6, 6, 7, 0, 3, 4, 5, 3, 4, 5, 0, 1, 2, 0, 1, 3, 4, 5, 5, 3, 3, 4, 3, 4, 5, 6, 7, 8, 4, 3, 4, 5, 5, 3, 4, 5, 8, 3, 0, 1, 2, 3, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 4, 6, 7, 8, 8, 6, 6, 7, 6, 7, 8, 7, 8, 6, 7, 6, 7, 8, 8, 6, 7, 8, 6, 6]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}