{"mW": 792, "mH": 840, "tW": 24, "tH": 24, "tiles": [["106_1", 0, 3, 3], ["255", 0, 1, 1], ["304", 0, 3, 2], ["304", 2, 3, 2], ["304", 1, 3, 2], ["304", 3, 3, 2], ["490", 0, 3, 2], ["490", 2, 3, 2], ["490", 1, 3, 2], ["490", 3, 3, 2], ["491", 0, 3, 2], ["491", 2, 3, 2], ["491", 1, 3, 2], ["491", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "1231_1", 717, 671, 114, 162, 0], [2, "1231_1", 441, 722, 114, 162, 0], [2, "1231_1", 94, 633, 114, 162, 0], [2, "1231_1", -51, 653, 114, 162, 0], [2, "1231_1", 645, 724, 114, 162, 0], [2, "1231", 30, 684, 114, 162, 0], [2, "1232", 235, 722, 100, 158, 0]]}, {"type": 4, "obj": [[2, "1231_1", 466, -126, 114, 162, 0], [2, "85", 286, -15, 48, 53, 0], [2, "1231_1", 311, -122, 114, 162, 0], [2, "1231_1", 168, -118, 114, 162, 0], [2, "1231_1", -18, -109, 114, 162, 0], [2, "1231_1", 670, -90, 114, 162, 0], [2, "1231_1", 79, -67, 114, 162, 2], [2, "1231_1", 389, -65, 114, 162, 0], [2, "1231", -48, -4, 114, 162, 0], [2, "1231_1", 373, 12, 114, 162, 0], [2, "1231", 549, 32, 114, 162, 0], [4, 1, 420, 196, 1, 4024], [2, "1231_1", 705, 95, 114, 162, 2], [2, "1231_1", -62, 97, 114, 162, 0], [2, "85", 410, 224, 48, 53, 0], [2, "1232", 4, 164, 100, 158, 0], [2, "1231_1", 47, 192, 114, 162, 2], [2, "205_1", 195, 316, 54, 40, 2], [2, "263", 282, 364, 34, 34, 0], [2, "1231_1", -48, 247, 114, 162, 0], [2, "1232", 173, 254, 100, 158, 0], [2, "114", 564, 381, 18, 32, 2], [2, "1232", 724, 300, 100, 158, 0], [2, "592", 432, 497, 30, 65, 2], [2, "593", 485, 520, 28, 48, 2], [2, "263", 735, 535, 34, 34, 0], [2, "420", 747, 562, 16, 13, 2], [2, "1231_1", -45, 418, 114, 162, 0], [2, "591", 550, 543, 16, 41, 2], [2, "1231", 294, 455, 114, 162, 0], [4, 0, 537, 655, 0, 4024]]}, {"type": 3, "obj": [[2, "594", 702, 663, 52, 46, 2], [2, "592", 388, 527, 30, 65, 0], [2, "594", 494, 579, 52, 46, 0], [2, "24", 621, 339, 28, 38, 0], [2, "24", 613, 351, 28, 38, 2], [2, "24", 769, 242, 28, 38, 0], [2, "245", 63, 235, 22, 22, 2], [2, "245", 44, 236, 22, 22, 0], [2, "244", 715, 532, 20, 38, 0], [2, "245", 729, 546, 22, 22, 0], [2, "422", 465, 408, 16, 14, 0], [2, "326", 708, 572, 18, 14, 0], [2, "420", 721, 581, 16, 13, 0], [2, "244", 786, 480, 20, 38, 0], [2, "245", 771, 488, 22, 22, 2], [2, "245", 799, 495, 22, 22, 0], [2, "244", 812, 514, 20, 38, 0], [2, "263", 793, 514, 34, 34, 0], [2, "21", 794, 542, 28, 24, 2], [2, "24", 758, 452, 28, 38, 2], [2, "208_1", 414, -16, 78, 40, 3], [2, "244", 41, 690, 20, 38, 0], [2, "245", 28, 706, 22, 22, 2], [2, "244", 10, 704, 20, 38, 0], [2, "245", -1, 721, 22, 22, 2], [2, "245", 700, 545, 22, 22, 2], [2, "245", 23, 721, 22, 22, 0], [2, "263", 106, 364, 34, 34, 0], [2, "263", 244, 708, 34, 34, 2], [2, "174", 108, 162, 68, 33, 0], [2, "90", 528, 379, 28, 36, 0], [2, "219_1", 209, 723, 36, 30, 2], [2, "244", 18, 231, 38, 20, 5], [2, "245", 19, 243, 22, 22, 2], [2, "219_1", 352, 207, 36, 30, 2], [2, "22", 375, 211, 62, 38, 0], [2, "21", 357, 243, 28, 24, 0], [2, "420", 407, 238, 16, 13, 0], [2, "422", 376, 233, 16, 14, 0], [2, "24", 587, 382, 28, 38, 0], [2, "24", 618, 368, 28, 38, 0], [2, "24", 443, 375, 28, 38, 2], [2, "24", 474, 394, 28, 38, 2], [2, "24", 671, 528, 28, 38, 0], [2, "24", 741, 256, 28, 38, 0], [2, "24", 712, 256, 28, 38, 2], [2, "263", 197, 355, 34, 34, 2], [2, "328", 471, 403, 32, 29, 0], [2, "329", 638, 539, 42, 37, 2], [2, "329", 120, 420, 42, 37, 2], [2, "90", 742, 457, 28, 36, 0], [2, "14_1", 736, 291, 32, 30, 0], [2, "14_1", 756, 299, 32, 30, 0], [2, "14_1", 748, 276, 32, 30, 0], [2, "208_1", 132, 349, 78, 40, 3], [2, "205_1", 129, 359, 54, 40, 2], [2, "205_1", 421, 6, 54, 40, 2], [2, "208_1", 456, 45, 78, 40, 3], [2, "205_1", 463, 67, 54, 40, 2], [2, "208_1", 418, 96, 78, 40, 2], [2, "326", 549, 417, 18, 14, 0], [2, "327", 560, 407, 30, 22, 0], [2, "326", 148, 449, 18, 14, 0], [2, "219_1", 66, 226, 36, 30, 0], [2, "219_1", 514, 401, 36, 30, 0], [2, "219_1", 590, 390, 36, 30, 2], [2, "205_1", 17, 537, 54, 40, 0], [2, "208_1", 105, 294, 78, 40, 0], [2, "208_1", 50, 265, 78, 40, 0], [2, "208_1", -12, 258, 78, 40, 0], [2, "208_1", 143, 304, 78, 40, 3], [2, "22", 436, 575, 62, 38, 0], [2, "244", 427, 354, 20, 38, 0], [2, "244", 32, 514, 20, 38, 0], [2, "326", 25, 554, 18, 14, 0], [2, "420", 38, 563, 16, 13, 0], [2, "245", 17, 527, 22, 22, 2], [2, "24", -4, 516, 28, 38, 0], [2, "245", 46, 528, 22, 22, 0], [2, "329", -5, 448, 42, 37, 0], [2, "263", 378, 575, 34, 34, 2], [2, "219_1", 358, 596, 36, 30, 2], [2, "219_1", 547, 532, 36, 30, 0], [2, "263", 541, 506, 34, 34, 0], [2, "309", 505, 537, 46, 33, 2], [2, "205_1", 751, 596, 54, 40, 0], [2, "208_1", 765, 628, 78, 40, 0], [2, "208_1", 743, 661, 78, 40, 2], [2, "208_1", 741, 686, 78, 40, 0], [2, "21", 218, 789, 28, 24, 0], [2, "21", 270, 594, 28, 24, 0], [2, "219_1", 442, 595, 36, 30, 0], [2, "420", 498, 643, 16, 13, 0], [2, "593", 420, 584, 28, 48, 0], [2, "591", 480, 612, 16, 41, 0], [2, "166_1", 503, 40, 30, 35, 2], [2, "597", 262, 547, 34, 26, 2], [2, "596", 294, 540, 14, 15, 2], [2, "597", 292, 546, 34, 26, 0], [2, "596", 280, 540, 14, 15, 0], [2, "166_1", 474, 83, 30, 35, 2], [2, "166_1", 746, 586, 30, 35, 0], [2, "166_1", 739, 675, 30, 35, 0], [2, "166_1", 775, 648, 30, 35, 0], [2, "166_1", 151, 347, 30, 35, 2], [2, "166_1", 80, 410, 30, 35, 2], [2, "263", 668, 669, 34, 34, 0], [2, "263", 564, -14, 34, 34, 0], [2, "244", 689, 541, 20, 38, 0]]}, {"type": 2, "data": [-1, 92, 92, 92, 92, 92, 92, 92, 92, 92, 105, 104, 99, -1, 90, 89, 88, -1, -1, -1, 82, 83, 89, 88, -1, -1, 66, 65, 64, -1, -1, -1, -1, -1, 86, 86, 86, 86, 92, 92, 105, 104, 104, 104, 104, 103, -1, 94, 95, 100, -1, -1, -1, 102, 101, 101, 100, -1, 66, 51, 57, 67, 82, 83, 89, 88, 86, 86, 86, 86, 86, 103, -1, 102, 101, 101, 101, 101, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 65, 51, 48, 49, 67, 102, 94, 101, 100, -1, 94, 95, 105, 104, 103, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 83, 88, -1, 58, 51, 53, 47, 48, 34, 45, 79, -1, -1, -1, -1, 82, -1, -1, 102, 101, 100, 65, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, 102, 101, 100, 58, 51, 48, 42, 41, 41, 45, 77, 76, -1, -1, -1, -1, -1, 103, 58, 59, 69, 51, 53, 57, 64, -1, -1, -1, 66, 65, 65, 64, -1, -1, -1, -1, 61, 39, 40, 49, 77, 76, -1, -1, -1, -1, -1, -1, -1, 58, 100, 61, 51, 47, 52, 9, 54, 57, 64, -1, -1, 73, 51, 57, 67, 90, 89, 83, 84, -1, 73, 39, 90, 89, 83, 84, -1, -1, -1, -1, -1, -1, 61, 88, 61, 55, 42, 41, 35, 36, 49, 67, -1, -1, 73, 39, 45, 63, 94, 95, 95, 96, -1, 70, 71, 102, 105, 104, 103, -1, -1, -1, -1, -1, -1, 61, 88, 73, 39, 45, 50, 80, 39, 45, -1, 58, 59, 78, 77, 77, 76, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, 96, -1, -1, -1, -1, -1, -1, 70, 91, 73, 56, 75, 75, 77, 77, 76, -1, 61, 51, 57, 63, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 78, 70, 77, 76, -1, -1, -1, -1, -1, 61, 43, 46, 57, 67, 74, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 83, 89, 89, 88, -1, -1, -1, -1, -1, -1, -1, -1, 82, 83, 61, 39, 40, 49, 50, 74, 67, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, 101, 100, -1, 83, 89, 89, 89, 84, -1, -1, -1, 94, 95, 96, 61, 43, 46, 47, 57, 79, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 95, 105, 98, 88, 98, 98, 98, 66, 65, 64, 70, 39, 41, 40, 54, 57, 64, -1, -1, -1, -1, -1, -1, -1, -1, -1, 58, 51, 47, 57, 64, -1, 98, 98, 98, 99, 105, 104, 105, 98, 78, 77, 76, 76, 70, 77, 43, 9, 49, 67, 90, 89, 88, -1, -1, -1, -1, -1, -1, 84, 39, 40, 46, 57, -1, 98, 98, 98, 103, 102, 101, -1, -1, 97, 89, 88, -1, -1, -1, 39, 36, 49, 76, 102, 101, 100, -1, 82, 83, 89, 88, 101, 100, 61, 39, 40, 37, 79, 104, 99, 101, 100, -1, -1, -1, -1, 102, 104, 103, 82, 83, 88, 78, 39, 45, 79, 95, 90, 89, 88, 82, 83, 101, 100, -1, -1, 66, 51, 52, 49, 79, 101, 100, -1, -1, -1, 82, 83, -1, 102, 101, 100, 94, 100, -1, -1, -1, -1, 76, -1, 102, 95, 100, -1, -1, -1, -1, -1, 66, 51, 52, 34, 45, 76, -1, -1, -1, -1, -1, 94, 95, -1, -1, 58, 59, 59, -1, -1, -1, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 47, 48, 42, 45, 82, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 51, 57, 65, 65, 64, 61, 51, 57, 64, -1, -1, -1, -1, -1, 51, 47, 48, 9, 34, 45, -1, 94, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 46, 47, 57, 74, 51, 48, 49, 79, -1, -1, -1, -1, 58, 39, 40, 34, 35, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 35, 36, 49, -1, 39, 41, 45, 79, -1, -1, -1, -1, 70, 74, 39, 45, 76, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 83, 83, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 89, 50, 51, 57, 56, -1, -1, -1, -1, -1, -1, -1, 94, 95, 100, -1, -1, 94, 95, 96, 90, 89, 88, -1, -1, 66, 65, 59, 60, 59, 60, -1, 102, 101, 47, 48, 54, 53, 57, 56, 51, 53, 57, 64, -1, -1, -1, 90, 89, 88, -1, -1, -1, 94, 95, 96, 90, 100, 58, 51, 53, 57, 62, 63, -1, -1, -1, 9, 9, 9, 9, 54, 53, 48, 9, 49, 63, -1, -1, -1, 102, 101, 95, 96, 82, 83, 84, 90, 89, 91, 66, 61, 55, 9, 54, 57, 67, -1, -1, -1, 9, 9, 42, 41, 40, 42, 35, 36, 37, 51, 47, 47, 57, 59, 59, 59, 60, 94, 95, 96, 102, 101, 100, -1, 69, 39, 36, 9, 49, 67, 90, 89, 89, 34, 35, 45, 44, 39, 45, 44, 39, 45, 55, 9, 9, 54, 53, 47, 57, 63, 59, -1, 94, 95, 66, 65, 50, 51, 53, 52, 42, 45, 63, 102, 105, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 41, 36, 9, 9, 9, 54, 53, 47, 57, 59, 65, 65, 51, 47, 48, 9, 42, 45, 79, -1, -1, 94, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 35, 36, 9, 9, 9, 9, 46, 57, 51, 47, 48, 9, 9, 9, 37, 79, 76, 94, 95, 89, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 38, 39, 41, 40, 9, 9, 42, 45, 55, 9, 34, 35, 36, 9, 37, 79, -1, -1, 97, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 70, 77, 81, 44, 39, 41, 41, 45, 75, -1, 35, 45, 38, 39, 35, 45, 79, -1, -1, 94, 95, -1, -1, -1, -1, -1, -1, 90, 89, 88, -1, -1, -1, -1, -1, 70, 71, 71, 77, 76, 77, 76, -1, -1, 70, 71, 71, 71, 71, 76, -1, -1, -1, -1, -1, 89, 88, -1, -1, -1, 102, 101, 100, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 101, 100, 90, 89, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 82, 83]}, {"type": 3, "obj": [[2, "253", 287, 382, 92, 53, 0], [2, "253", 42, 509, 92, 53, 2], [2, "253", 272, 178, 92, 53, 0], [2, "253", 235, 419, 92, 53, 2], [2, "208_1", 344, 495, 78, 40, 0], [2, "253", 405, 221, 92, 53, 2], [2, "214_1", 196, 345, 54, 40, 0], [2, "213_1", 49, 282, 64, 45, 2], [2, "214_1", 72, 300, 54, 40, 0], [2, "214_1", 104, 312, 54, 40, 0], [2, "213_1", 124, 319, 64, 45, 2], [2, "214_1", -15, 279, 54, 40, 0], [2, "214_1", 8, 291, 54, 40, 0], [2, "253", 503, 388, 92, 53, 2], [2, "253", 426, 360, 92, 53, 2], [2, "214_1", 162, 349, 54, 40, 2], [2, "253", 339, 490, 92, 53, 0], [2, "305", 492, 68, 30, 24, 0], [2, "308", 242, 69, 52, 22, 2], [2, "308", 279, 45, 52, 22, 0], [2, "308", 460, 89, 52, 22, 0], [2, "307", 412, 112, 42, 19, 0], [2, "306", 454, 107, 46, 25, 0], [2, "307", 431, 86, 42, 19, 0], [2, "305", 285, 64, 30, 24, 0], [2, "307", 225, 55, 42, 19, 0], [2, "308", 52, 223, 52, 22, 0], [2, "307", 29, 232, 42, 19, 0], [2, "309", 8, 207, 46, 33, 0], [2, "306", -9, 423, 46, 25, 0], [2, "308", 422, 354, 52, 22, 0], [2, "308", 490, 375, 52, 22, 0], [2, "306", 370, 250, 46, 25, 0], [2, "309", 362, 273, 46, 33, 0], [2, "308", -18, 235, 52, 22, 0], [2, "307", 294, 463, 42, 19, 0], [2, "307", 224, 519, 42, 19, 0], [2, "309", -19, 207, 46, 33, 0], [2, "307", 260, 60, 42, 19, 0], [2, "253", 476, 44, 92, 53, 0], [2, "253", 347, 193, 92, 53, 2], [2, "325", 237, 385, 50, 37, 0], [2, "325", 145, 488, 50, 37, 0], [2, "174", 717, 304, 68, 33, 0], [2, "174", 241, 424, 68, 33, 0], [2, "174", 256, 43, 68, 33, 0], [2, "174", -3, 192, 68, 33, 2], [2, "174", 661, 518, 68, 33, 0], [2, "174", 344, 509, 68, 33, 0], [2, "174", 750, 157, 68, 33, 0], [2, "174", 543, 99, 68, 33, 0], [2, "174", 67, 479, 68, 33, 0], [2, "174", -17, 503, 68, 33, 0], [2, "208_1", 162, 330, 78, 40, 1], [2, "420", 239, 455, 16, 13, 0], [2, "220_1", -10, 300, 40, 29, 0], [2, "219_1", 43, 312, 36, 30, 0], [2, "220_1", 73, 312, 40, 29, 0], [2, "219_1", 155, 484, 36, 30, 2], [2, "219_1", 214, 356, 36, 30, 2], [2, "263", 78, 314, 34, 34, 0], [2, "21", -10, 444, 28, 24, 2], [2, "420", 751, 46, 16, 13, 2], [2, "420", 166, 510, 16, 13, 0], [2, "420", 58, 492, 16, 13, 2], [2, "420", 115, 514, 16, 13, 2], [2, "219_1", 322, 38, 36, 30, 2], [2, "219_1", 7, 18, 36, 30, 2], [2, "219_1", 117, 493, 36, 30, 0], [2, "219_1", 10, 471, 36, 30, 0], [2, "219_1", 424, 375, 36, 30, 0], [2, "219_1", 537, 127, 36, 30, 2], [2, "253", 295, 31, 92, 53, 2], [2, "309", 548, 153, 46, 33, 0], [2, "307", 627, 305, 42, 19, 0], [2, "308", 585, 317, 52, 22, 0], [2, "219_1", 714, 245, 36, 30, 2], [2, "219_1", 460, 20, 36, 30, 2], [2, "420", 586, 305, 16, 13, 0], [2, "420", 724, 450, 16, 13, 0], [2, "219_1", 545, 366, 36, 30, 0], [2, "219_1", 15, 347, 36, 30, 0], [2, "214_1", 79, 402, 54, 40, 2], [2, "213_1", 145, 371, 64, 45, 2], [2, "213_1", 108, 381, 64, 45, 2], [2, "208_1", 76, 384, 78, 40, 1], [2, "219_1", 162, 398, 36, 30, 2], [2, "208_1", 66, 419, 78, 40, 0], [2, "263", 193, 399, 34, 34, 0], [2, "90", 183, 401, 28, 36, 0], [2, "90", 156, 419, 36, 28, 5], [2, "327", 156, 436, 30, 22, 0], [2, "219_1", 228, 161, 36, 30, 0], [2, "219_1", 611, 361, 36, 30, 2], [2, "219_1", 570, 384, 36, 30, 2], [2, "219_1", 393, 354, 36, 30, 0], [2, "253", 668, 264, 92, 53, 2], [2, "253", 589, 302, 92, 53, 2], [2, "219_1", 739, 272, 36, 30, 2], [2, "219_1", 639, 335, 36, 30, 2], [2, "219_1", 665, 486, 36, 30, 2], [2, "219_1", 568, 196, 36, 30, 0], [2, "219_1", 603, 204, 36, 30, 0], [2, "219_1", 443, 186, 36, 30, 0], [2, "219_1", 212, 74, 36, 30, 0], [2, "219_1", 602, 185, 36, 30, 2], [2, "219_1", 724, 183, 36, 30, 0], [2, "219_1", 730, 487, 36, 30, 0], [2, "219_1", 393, 319, 36, 30, 0], [2, "219_1", 203, 267, 36, 30, 0], [2, "420", 199, 432, 16, 13, 0], [2, "219_1", 708, 281, 36, 30, 2], [2, "219_1", 751, 373, 36, 30, 2], [2, "219_1", 766, 260, 36, 30, 2], [2, "219_1", 293, 404, 36, 30, 2], [2, "214_1", 441, -4, 54, 40, 2], [2, "214_1", 480, 60, 54, 40, 2], [2, "214_1", 466, 88, 54, 40, 2], [2, "219_1", 482, 221, 36, 30, 2], [2, "219_1", 480, 161, 36, 30, 0], [2, "174", 451, 366, 68, 33, 0], [2, "253", 7, 172, 92, 53, 0], [2, "219_1", 368, 348, 36, 30, 0], [2, "308", 574, 377, 52, 22, 2], [2, "420", 7, 238, 16, 13, 2], [2, "325", 619, 376, 50, 37, 0], [2, "219_1", 72, 214, 36, 30, 0], [2, "213_1", 750, 624, 64, 45, 0], [2, "214_1", 764, 651, 54, 40, 0], [2, "213_1", 737, 700, 64, 45, 0], [2, "325", 262, 803, 50, 37, 0], [2, "219_1", 272, 799, 36, 30, 2], [2, "420", 283, 825, 16, 13, 0], [2, "219_1", 234, 808, 36, 30, 0], [2, "307", 413, 813, 42, 19, 0], [2, "253", 634, 807, 92, 53, 2], [2, "174", 659, 777, 68, 33, 0], [2, "174", 575, 801, 68, 33, 0], [2, "420", 650, 790, 16, 13, 2], [2, "420", 707, 812, 16, 13, 2], [2, "219_1", 709, 791, 36, 30, 0], [2, "219_1", 622, 782, 36, 30, 0], [2, "388", 400, 605, 60, 51, 2], [2, "388", 461, 627, 60, 51, 2], [2, "388", 529, 558, 60, 51, 2], [2, "388", 425, 535, 60, 51, 2], [2, "388", 473, 541, 60, 51, 2], [2, "219_1", 151, 765, 36, 30, 0], [2, "219_1", 117, 813, 36, 30, 0], [2, "219_1", 50, 696, 36, 30, 0], [2, "219_1", 67, 531, 36, 30, 0], [2, "219_1", 724, 639, 36, 30, 0], [2, "219_1", 719, 721, 36, 30, 0], [2, "174", 662, 402, 68, 33, 0], [2, "174", 394, 427, 68, 33, 0], [2, "219_1", 541, 598, 36, 30, 0]]}, {"type": 2, "data": [14, 14, 14, 14, 14, 14, 14, 14, 26, 15, 17, 16, 2, 33, 11, 21, 32, 14, 14, 20, 20, 31, 30, 33, 20, 26, 31, 1, 22, 33, 21, 17, 26, 14, 14, 14, 15, 16, 30, 33, 26, 26, 26, 20, 19, 5, 22, 23, 23, 33, 32, 14, 14, 27, 28, 10, 21, 20, 27, 28, 5, 25, 26, 26, 20, 26, 26, 32, 32, 32, 15, 17, 21, 32, 32, 32, 32, 31, 1, 2, 8, 4, 30, 33, 27, 29, 28, 5, 29, 27, 29, 28, 7, 8, 22, 23, 33, 20, 26, 26, 26, 27, 29, 33, 20, 27, 33, 32, 27, 29, 10, 17, 12, 7, 7, 8, 30, 28, 5, 7, 8, 8, 4, 7, 4, 5, 7, 8, 4, 22, 23, 33, 20, 32, 31, 6, 30, 29, 28, 30, 29, 28, 4, 22, 33, 15, 17, 16, 4, 5, 4, 5, 4, 5, 8, 7, 8, 10, 11, 12, 18, 17, 17, 17, 21, 27, 29, 28, 3, 6, 7, 8, 3, 4, 10, 11, 12, 30, 29, 23, 24, 7, 8, 10, 11, 12, 8, 10, 17, 11, 21, 20, 15, 21, 20, 20, 15, 26, 15, 16, 8, 3, 4, 5, 0, 8, 10, 21, 32, 31, 0, 0, 7, 8, 3, 5, 13, 32, 31, 8, 25, 20, 20, 26, 20, 32, 20, 32, 32, 32, 20, 29, 28, 2, 6, 7, 8, 3, 10, 21, 26, 20, 19, 1, 3, 4, 10, 11, 11, 21, 26, 15, 16, 13, 20, 20, 32, 26, 20, 26, 32, 26, 20, 20, 4, 5, 5, 6, 7, 8, 6, 30, 33, 27, 23, 24, 0, 7, 8, 25, 20, 20, 20, 20, 20, 31, 22, 33, 26, 20, 20, 32, 32, 32, 20, 32, 20, 7, 8, 8, 6, 18, 17, 17, 16, 30, 28, 0, 1, 2, 4, 18, 21, 20, 26, 20, 20, 20, 19, 8, 30, 33, 20, 20, 26, 32, 32, 32, 20, 26, 17, 16, 10, 11, 21, 20, 14, 15, 16, 7, 3, 4, 5, 1, 25, 20, 20, 26, 27, 33, 32, 15, 17, 16, 22, 33, 20, 20, 20, 26, 20, 20, 26, 20, 19, 13, 14, 26, 26, 26, 27, 28, 4, 6, 7, 8, 1, 22, 33, 32, 20, 19, 30, 29, 29, 33, 15, 16, 30, 23, 33, 20, 20, 20, 27, 26, 26, 31, 30, 29, 33, 26, 20, 15, 17, 17, 11, 12, 3, 4, 5, 30, 33, 20, 15, 16, 2, 2, 22, 23, 24, 8, 3, 30, 33, 20, 27, 28, 20, 26, 15, 26, 0, 22, 33, 20, 26, 26, 26, 26, 15, 16, 3, 3, 3, 30, 29, 28, 10, 11, 12, 6, 0, 1, 10, 17, 16, 30, 29, 28, 7, 20, 26, 14, 27, 29, 33, 26, 33, 26, 26, 26, 26, 26, 15, 17, 16, 5, 3, 8, 8, 13, 14, 15, 16, 3, 0, 13, 32, 15, 16, 1, 0, 1, 33, 26, 14, 15, 16, 30, 29, 33, 26, 26, 26, 26, 26, 26, 20, 19, 5, 10, 11, 11, 21, 20, 32, 15, 17, 17, 21, 32, 32, 15, 16, 3, 4, 13, 26, 26, 26, 19, 1, 2, 30, 29, 27, 33, 26, 14, 14, 14, 15, 8, 13, 14, 20, 20, 32, 32, 32, 20, 20, 20, 32, 32, 27, 24, 6, 7, 13, 26, 26, 26, 19, 18, 17, 16, 3, 3, 22, 23, 33, 14, 14, 32, 15, 21, 14, 14, 20, 32, 14, 14, 20, 14, 32, 32, 27, 24, 0, 10, 11, 21, 26, 26, 26, 15, 21, 20, 19, 18, 17, 16, 0, 25, 23, 33, 26, 32, 27, 33, 20, 26, 27, 33, 32, 26, 20, 27, 29, 24, 4, 3, 13, 14, 20, 20, 26, 26, 20, 27, 23, 24, 25, 26, 31, 3, 22, 28, 22, 33, 27, 28, 22, 33, 27, 28, 30, 29, 33, 27, 28, 6, 6, 7, 18, 21, 26, 20, 20, 20, 20, 20, 15, 17, 17, 21, 20, 15, 16, 4, 5, 3, 22, 24, 7, 8, 25, 15, 17, 17, 16, 22, 28, 8, 7, 8, 10, 21, 27, 23, 20, 20, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 17, 16, 10, 21, 20, 11, 11, 21, 20, 26, 20, 15, 17, 17, 16, 10, 21, 20, 32, 15, 20, 20, 20, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 19, 7, 8, 18, 17, 14, 14, 14, 14, 14, 14, 14, 32, 31, 25, 26, 14, 32, 32, 32, 32, 20, 20, 20, 20, 32, 32, 32, 32, 32, 32, 26, 26, 15, 17, 11, 21, 20, 20, 26, 26, 14, 32, 14, 14, 32, 31, 22, 33, 32, 32, 32, 32, 20, 20, 20, 27, 29, 33, 32, 27, 23, 23, 33, 26, 26, 26, 20, 20, 20, 26, 26, 20, 26, 26, 32, 32, 26, 27, 28, 5, 30, 29, 33, 32, 20, 20, 23, 33, 31, 8, 22, 23, 24, 3, 4, 30, 33, 32, 26, 26, 26, 26, 20, 20, 26, 26, 20, 26, 32, 27, 24, 8, 0, 1, 2, 13, 32, 32, 20, 8, 13, 15, 16, 3, 4, 5, 0, 1, 0, 30, 29, 33, 32, 26, 27, 33, 32, 20, 20, 20, 32, 32, 15, 11, 12, 3, 4, 5, 13, 20, 20, 20, 18, 21, 14, 15, 12, 7, 8, 10, 11, 12, 0, 0, 30, 29, 23, 24, 30, 33, 32, 32, 27, 33, 32, 27, 23, 24, 6, 7, 8, 25, 20, 20, 20, 21, 20, 14, 14, 15, 11, 12, 13, 14, 15, 12, 3, 4, 5, 0, 1, 2, 30, 29, 29, 24, 30, 23, 24, 0, 1, 2, 10, 11, 21, 14, 20, 20, 14, 14, 26, 26, 26, 14, 15, 21, 14, 14, 15, 12, 0, 1, 3, 0, 0, 1, 2, 0, 1, 0, 1, 0, 3, 4, 5, 13, 14, 20, 20, 20, 20, 20, 26, 26, 26, 26, 26, 26, 26, 26, 26, 32, 19, 3, 4, 10, 11, 12, 1, 2, 3, 4, 3, 4, 3, 6, 7, 8, 30, 33, 20, 20, 20, 20, 20, 14, 14, 26, 26, 26, 32, 32, 26, 26, 27, 24, 6, 7, 22, 23, 24, 4, 5, 18, 17, 11, 12, 6, 7, 8, 4, 5, 25, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 26, 20, 14, 15, 17, 17, 12, 7, 8, 6, 7, 10, 21, 20, 14, 15, 11, 12, 10, 11, 11, 21, 20, 20, 20, 20, 20, 20, 20, -1, -1, 20, 20, 20, 20, 20, 20, 20, 20, 15, 17, 17, 11, 17, 21, 20, 20, 20, 20, 20, 15, 21, 14, 14, 14, 14, 14, 14, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, -1, -1, 20, 20, 20, 20, 20, 14, 14, 14, 14, 14, 14]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1]}