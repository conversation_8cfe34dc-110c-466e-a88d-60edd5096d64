{"mW": 1056, "mH": 960, "tW": 24, "tH": 24, "tiles": [["1408", 0, 3, 2], ["1408", 2, 3, 2], ["1408", 1, 3, 2], ["1408", 3, 3, 2], ["1803", 0, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "818_1", 86, 12, 30, 37, 2], [2, "1401", 200, 7, 62, 92, 0], [2, "1396", 175, 106, 50, 41, 2], [2, "1401", 175, 21, 62, 92, 0], [2, "1396", 162, 111, 50, 41, 2], [2, "1401", 150, 33, 62, 92, 0], [2, "1396", 124, 130, 50, 41, 2], [2, "1396", 82, 151, 50, 41, 2], [2, "1403", 41, 102, 18, 22, 0], [2, "1403", 31, 119, 18, 22, 0], [2, "1403", 24, 133, 18, 22, 0], [2, "1402", 37, 92, 14, 29, 0], [2, "1402", 26, 111, 14, 29, 0], [2, "1402", 19, 123, 14, 29, 0], [2, "1401", 125, 46, 62, 92, 0], [2, "1401", 101, 57, 62, 92, 0], [2, "1403", 85, 12, 18, 22, 0], [2, "1402", 81, 3, 14, 29, 0], [2, "1403", 75, 29, 18, 22, 0], [2, "1403", 68, 41, 18, 22, 0], [2, "1402", 68, 23, 14, 29, 0], [2, "1402", 63, 31, 14, 29, 0], [2, "1403", 258, 77, 18, 22, 2], [2, "1403", 267, 90, 18, 22, 2], [2, "1403", 276, 103, 18, 22, 2], [2, "1402", 266, 69, 14, 29, 2], [2, "1402", 276, 84, 14, 29, 2], [2, "1402", 283, 93, 14, 29, 2], [2, "1401", 187, 48, 62, 92, 2], [2, "1401", 204, 57, 62, 92, 2], [2, "1396", 160, 36, 50, 41, 2], [2, "1396", 184, 123, 50, 41, 0], [2, "1231_4", 808, 733, 114, 162, 0], [2, "1231_3", 164, 791, 114, 162, 0], [2, "818_1", 85, 33, 30, 37, 2], [2, "818_1", 93, 37, 30, 37, 2], [2, "818_1", 94, 59, 30, 37, 2], [2, "818_1", 75, 48, 30, 37, 2], [2, "1401", 76, 70, 62, 92, 0], [2, "1401", 51, 81, 62, 92, 0], [2, "1405", 51, 22, 44, 94, 0], [2, "818_1", 125, 60, 30, 37, 0], [2, "1396", 125, 52, 50, 41, 2], [2, "1401", 147, -34, 62, 92, 0], [2, "1401", 122, -22, 62, 92, 0], [2, "1401", 95, -9, 62, 92, 0], [2, "1279", 117, 157, 42, 58, 2]]}, {"type": 4, "obj": [[2, "1185_1", 358, -32, 76, 68, 2], [2, "1185_1", 406, -14, 76, 68, 2], [2, "1231_4", 459, -103, 114, 162, 0], [2, "1185_1", 454, 7, 76, 68, 2], [2, "1231_4", 171, -81, 114, 162, 0], [2, "1185_1", 505, 32, 76, 68, 2], [2, "267_1", 276, 31, 46, 72, 2], [2, "1185_1", 545, 59, 76, 68, 2], [2, "1231_4", 584, -22, 114, 162, 0], [2, "1231_3", 968, 3, 114, 162, 0], [2, "1185_1", 550, 102, 76, 68, 0], [2, "1185_1", 518, 128, 76, 68, 0], [2, "1185_1", 995, 144, 76, 68, 0], [2, "1159_1", 986, 177, 36, 37, 0], [2, "1185_1", 470, 150, 76, 68, 0], [2, "219_1", 1011, 193, 36, 30, 0], [2, "1185_1", 422, 172, 76, 68, 0], [2, "829", 610, 192, 42, 54, 0], [2, "1185_1", 377, 190, 76, 68, 0], [2, "829", 569, 215, 42, 54, 0], [2, "1185_1", 332, 212, 76, 68, 0], [2, "1488", 11, 236, 64, 53, 0], [2, "829", 533, 236, 42, 54, 0], [2, "891", 379, 226, 54, 75, 0], [2, "1185_1", 289, 241, 76, 68, 0], [2, "890", 359, 211, 94, 105, 0], [2, "267_1", -12, 250, 46, 72, 2], [2, "1231_3", 641, 165, 114, 162, 0], [2, "1185_1", 244, 268, 76, 68, 0], [2, "219_1", 668, 316, 36, 30, 0], [2, "1185_1", 203, 292, 76, 68, 0], [2, "1185_1", -18, 311, 76, 68, 2], [2, "1185_1", 160, 314, 76, 68, 0], [2, "1185_1", 27, 328, 76, 68, 2], [2, "829", 313, 346, 42, 54, 0], [2, "1185_1", 119, 338, 76, 68, 0], [2, "1185_1", 76, 343, 76, 68, 2], [2, "829", 274, 371, 42, 54, 0], [2, "829", 236, 401, 42, 54, 0], [2, "829", -10, 420, 42, 54, 2], [2, "1231_3", 783, 312, 114, 162, 0], [2, "829", 190, 433, 42, 54, 0], [2, "1231_4", 883, 329, 114, 162, 0], [2, "1185_1", 859, 427, 76, 68, 2], [2, "829", 147, 445, 42, 54, 0], [2, "1231_3", -5, 347, 114, 162, 0], [2, "1488", 865, 456, 64, 53, 0], [2, "1185_1", 792, 445, 76, 68, 0], [2, "1185_1", 922, 458, 76, 68, 2], [2, "1488", 825, 477, 64, 53, 0], [2, "1819", 876, 481, 82, 74, 2], [2, "1185_1", 932, 497, 76, 68, 0], [2, "220_1", 961, 548, 40, 29, 0], [2, "1182_1", 866, 541, 94, 46, 0], [2, "220_1", 936, 562, 40, 29, 0], [2, "1159_1", 957, 563, 36, 37, 0], [2, "219_1", 870, 571, 36, 30, 0], [2, "1159_1", 888, 570, 36, 37, 0], [2, "1231_4", 85, 478, 114, 162, 0], [2, "1231_4", 481, 541, 114, 162, 0], [2, "1183_1", 541, 679, 58, 31, 0], [2, "1184_1", 568, 679, 50, 36, 0], [2, "1185_1", 458, 670, 76, 68, 0], [2, "1488", 505, 686, 64, 53, 2], [2, "1185_1", 607, 672, 76, 68, 2], [2, "1488", 466, 706, 64, 53, 0], [2, "1412", 461, 735, 32, 28, 2], [2, "1819", 547, 694, 82, 74, 2], [2, "220_1", 467, 746, 40, 29, 2], [2, "1185_1", 605, 710, 76, 68, 0], [2, "1159_1", 449, 742, 36, 37, 2], [2, "1182_1", 556, 757, 94, 46, 0], [2, "1332", 606, 754, 106, 57, 2], [2, "220_1", 584, 785, 40, 29, 0], [2, "219_1", 610, 791, 36, 30, 2], [2, "1159_1", 580, 786, 36, 37, 2], [2, "1332", 275, 769, 106, 57, 0]]}, {"type": 2, "data": [-1, -1, -1, 37, 37, 37, 37, 37, 37, 37, 37, 38, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 37, 38, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, -1, -1, -1, -1, -1, -1, 55, 55, 55, 55, 55, 37, 38, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, 55, 55, 55, 37, 38, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, 48, 55, 50, 52, 56, 55, 55, 37, 38, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, 48, 50, 47, -1, 53, 52, 56, 55, 37, 37, 38, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 44, 43, 43, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, 48, 42, -1, -1, -1, -1, 53, 52, 56, 55, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 50, 43, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, 48, 42, -1, -1, -1, -1, -1, -1, 53, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, 43, 43, 43, 43, -1, -1, 41, 40, 40, -1, -1, -1, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, 41, 40, 44, 43, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, 44, 43, 49, 50, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 55, 49, 50, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 46, 46, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 42, -1, -1, -1, -1, 33, 34, 39, -1, -1, 55, 55, 55, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, 41, 40, 35, -1, 36, 37, 42, -1, -1, 49, 50, 52, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 38, 39, 45, 46, 47, -1, -1, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 56, 49, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 34, 34, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, 48, 49, 43, 43, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 4, 5, -1, -1, -1, -1, 45, 46, 46, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 4, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 22, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 54, 41, 40, 40, 40, 40, 40, 40, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 51, 48, 49, 43, 43, 43, 49, 49, 38, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 34, 40, 39, -1, -1, -1, -1, -1, 45, 46, 56, 49, 49, 49, 49, 49, 50, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 55, 55, 54, -1, -1, -1, -1, -1, -1, -1, 45, 56, 55, 49, 50, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 49, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 49, 43, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 37, 37, 37, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 37, 37, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "1414", 209, 219, 46, 30, 0], [2, "1414", 244, 203, 46, 30, 0], [2, "1414", 379, 81, 46, 30, 2], [2, "1185_1", 333, 299, 76, 68, 2], [2, "1161_1", 317, 268, 54, 52, 0], [2, "1368_1", 702, 46, 50, 42, 0], [2, "1368_1", 727, 880, 50, 42, 0], [2, "1368_1", 252, 647, 50, 42, 0], [2, "1368_1", 569, 399, 50, 42, 0], [2, "1414_1", 919, 253, 46, 30, 0], [2, "1414", 62, 263, 46, 30, 2], [2, "1414", 89, 255, 46, 30, 0], [2, "1414", 320, 312, 46, 30, 0], [2, "1414", 444, 249, 46, 30, 0], [2, "1414", 779, 510, 46, 30, 0], [2, "1231_4", -44, 54, 114, 162, 0], [2, "902_1", 264, 189, 28, 25, 2], [2, "902_1", 153, 217, 28, 25, 2], [2, "902_2", 30, 225, 28, 25, 0], [2, "902_1", 125, 231, 28, 25, 2], [2, "902_2", 58, 239, 28, 25, 0], [2, "902_2", 69, 245, 28, 25, 0], [2, "902_1", 97, 245, 28, 25, 2], [2, "902_1", 174, 207, 28, 25, 2], [2, "902_1", 164, 212, 28, 25, 2], [2, "902_1", 237, 202, 28, 25, 2], [2, "902_1", 228, 207, 28, 25, 2], [2, "818_1", 30, 203, 30, 37, 2], [2, "818_1", 60, 218, 30, 37, 2], [2, "818_1", 60, 197, 30, 37, 2], [2, "818_1", 30, 182, 30, 37, 2], [2, "818_1", 60, 155, 30, 37, 2], [2, "818_1", 30, 140, 30, 37, 2], [2, "818_1", 30, 161, 30, 37, 2], [2, "818_1", 60, 176, 30, 37, 2], [2, "818_1", 30, 119, 30, 37, 2], [2, "818_1", 60, 134, 30, 37, 2], [2, "818_1", 44, 105, 30, 37, 2], [2, "818_1", 74, 120, 30, 37, 2], [2, "818_1", 50, 87, 30, 37, 2], [2, "923", 38, 126, 44, 125, 0], [2, "1463_1", 18, 193, 22, 59, 0], [2, "1208", 30, 218, 52, 56, 0], [2, "1463_1", 18, 147, 22, 59, 0], [2, "1463_1", 82, 222, 22, 59, 0], [2, "1463_1", 82, 176, 22, 59, 0], [2, "818_1", 162, 190, 30, 37, 0], [2, "902_1", 201, 208, 28, 25, 0], [2, "818_1", 134, 204, 30, 37, 0], [2, "818_1", 104, 219, 30, 37, 0], [2, "818_1", 162, 169, 30, 37, 0], [2, "818_1", 134, 183, 30, 37, 0], [2, "818_1", 104, 198, 30, 37, 0], [2, "818_1", 104, 177, 30, 37, 0], [2, "818_1", 134, 162, 30, 37, 0], [2, "818_1", 162, 148, 30, 37, 0], [2, "818_1", 104, 157, 30, 37, 0], [2, "818_1", 134, 142, 30, 37, 0], [2, "818_1", 162, 128, 30, 37, 0], [2, "1208", 104, 216, 52, 56, 2], [2, "818_1", 199, 185, 30, 37, 2], [2, "1208", 153, 191, 52, 56, 2], [2, "818_1", 199, 164, 30, 37, 2], [2, "818_1", 199, 143, 30, 37, 2], [2, "1463_1", 184, 175, 22, 59, 0], [2, "1463_1", 184, 129, 22, 59, 0], [2, "818_1", 231, 182, 30, 37, 0], [2, "818_1", 261, 168, 30, 37, 0], [2, "818_1", 231, 161, 30, 37, 0], [2, "818_1", 261, 147, 30, 37, 0], [2, "818_1", 261, 126, 30, 37, 0], [2, "818_1", 261, 105, 30, 37, 0], [2, "818_1", 231, 119, 30, 37, 0], [2, "818_1", 231, 140, 30, 37, 0], [2, "818_1", 236, 95, 30, 37, 0], [2, "818_1", 251, 89, 30, 37, 0], [2, "818_1", 240, 74, 30, 37, 0], [2, "1463_1", 196, 182, 22, 59, 0], [2, "1463_1", 196, 136, 22, 59, 0], [2, "1231_4", -34, -77, 114, 162, 0], [2, "1231_4", 12, -34, 114, 162, 0], [2, "325_3", 323, 256, 50, 37, 0], [2, "325_3", 352, 231, 50, 37, 0], [2, "1414", 120, 244, 46, 30, 0], [2, "1414", 458, 232, 46, 30, 0], [2, "1414", 491, 225, 46, 30, 0], [2, "1414", 521, 210, 46, 30, 0], [2, "1414", 216, 386, 46, 30, 0], [2, "1414", 135, 422, 46, 30, 0], [2, "1414", 172, 413, 46, 30, 0], [2, "1414", 148, 229, 46, 30, 0], [2, "1412", 138, 227, 32, 28, 0], [2, "1414", 483, 128, 46, 30, 2], [2, "1414", 463, 139, 46, 30, 2], [2, "1414", 511, 143, 46, 30, 2], [2, "1414", 489, 150, 46, 30, 2], [2, "1414", 459, 158, 46, 30, 2], [2, "1414", 468, 99, 46, 30, 2], [2, "1414", 422, 79, 46, 30, 2], [2, "1414", 277, 82, 46, 30, 0], [2, "1414", 389, 64, 46, 30, 2], [2, "1463_1", 280, 110, 22, 59, 0], [2, "1414", 831, 503, 46, 30, 2], [2, "1414", 870, 507, 46, 30, 2], [2, "1414", 877, 526, 46, 30, 2], [2, "1414", 875, 549, 46, 30, 2], [2, "1416", 848, 555, 24, 17, 0], [2, "1416", 782, 580, 24, 17, 0], [2, "1414", 521, 729, 46, 30, 2], [2, "1414", 486, 741, 46, 30, 0], [2, "1414", 530, 750, 46, 30, 2], [2, "1416", 447, 767, 24, 17, 0], [2, "1416", 507, 776, 24, 17, 0], [2, "1416", 479, 781, 24, 17, 2], [2, "1368_2", 328, 755, 50, 42, 0], [2, "219_1", 674, 745, 36, 30, 0], [2, "1159_1", 437, 721, 36, 37, 0], [2, "1159_1", 766, 496, 36, 37, 0], [2, "1159_1", 759, 512, 36, 37, 2], [2, "1416", 814, 533, 24, 17, 0], [2, "1416", 818, 535, 24, 17, 0], [2, "1414", 284, 334, 46, 30, 0], [2, "1416", 221, 420, 24, 17, 0], [2, "1416", 163, 452, 24, 17, 0], [2, "1416", 53, 284, 24, 17, 0], [2, "1416", 332, 86, 24, 17, 0], [2, "1463_1", 412, 220, 22, 59, 0], [2, "1463_1", 399, 227, 22, 59, 0], [2, "1463_1", 384, 235, 22, 59, 0], [2, "1463_1", 371, 242, 22, 59, 0], [2, "1463_1", 359, 247, 22, 59, 0], [2, "1463_1", 310, 273, 22, 59, 0], [2, "1463_1", 297, 282, 22, 59, 0], [2, "1463_1", 284, 290, 22, 59, 0], [2, "1463_1", 273, 296, 22, 59, 0], [2, "1414", 547, 189, 46, 30, 0], [2, "1414", 572, 178, 46, 30, 0], [2, "1414", 604, 162, 46, 30, 0], [2, "1412_1", 319, 307, 32, 28, 0], [2, "1414", 229, 368, 46, 30, 0], [2, "1414", 258, 349, 46, 30, 0], [2, "1185_1", 580, 167, 76, 68, 0], [2, "1185_1", 530, 198, 76, 68, 0], [2, "1185_1", 480, 224, 76, 68, 0], [2, "1185_1", 434, 248, 76, 68, 0], [2, "390_2", 371, 271, 102, 80, 2], [2, "220_1", 521, 272, 40, 29, 0], [2, "1185_1", 266, 321, 76, 68, 0], [2, "1185_1", 218, 350, 76, 68, 0], [2, "1185_1", 176, 377, 76, 68, 0], [2, "1185_1", 137, 405, 76, 68, 0], [2, "1185_1", -13, 392, 76, 68, 2], [2, "1185_1", 38, 407, 76, 68, 2], [2, "1185_1", 90, 415, 76, 68, 2], [2, "219_1", 119, 463, 36, 30, 0], [2, "219_1", 84, 462, 36, 30, 0], [2, "220_1", 206, 426, 40, 29, 0], [2, "220_1", 333, 347, 40, 29, 0], [2, "220_1", 360, 348, 40, 29, 0], [2, "220_1", 497, 281, 40, 29, 2], [2, "220_1", 521, 272, 40, 29, 0], [2, "219_1", 294, 375, 36, 30, 2], [2, "219_1", 548, 248, 36, 30, 2], [2, "1414", 587, 227, 46, 30, 0], [2, "1414", 237, 410, 46, 30, 0], [2, "1414", 40, 458, 46, 30, 2], [2, "1414", 8, 447, 46, 30, 2], [2, "1416", 403, 344, 24, 17, 0], [2, "1416", 461, 309, 24, 17, 0], [2, "1182_1", 325, 534, 94, 46, 0], [2, "1159_1", 446, 529, 36, 37, 0], [2, "1184_1", 331, 563, 50, 36, 0], [2, "1159_1", 312, 552, 36, 37, 0], [2, "1159_1", 365, 508, 36, 37, 0], [2, "219_1", 353, 575, 36, 30, 0], [2, "1368_1", 692, 741, 50, 42, 0], [2, "1184_1", 152, 622, 50, 36, 0], [2, "1414_1", 174, 630, 46, 30, 0], [2, "1414_1", 141, 640, 46, 30, 2], [2, "1412_1", 198, 616, 32, 28, 2], [2, "1159_1", 170, 638, 36, 37, 0], [2, "1182_1", 550, 566, 94, 46, 0], [2, "1159_1", 561, 585, 36, 37, 2], [2, "1159_1", 627, 572, 36, 37, 0], [2, "1159_1", 577, 427, 36, 37, 2], [2, "1159_1", 587, 450, 36, 37, 2], [2, "1159_1", 677, 495, 36, 37, 2], [2, "1159_1", 782, 273, 36, 37, 2], [2, "1159_1", 617, 357, 36, 37, 0], [2, "1159_1", 326, 471, 36, 37, 0], [2, "1159_1", 781, 447, 36, 37, 0], [2, "1159_1", 887, 808, 36, 37, 0], [2, "1183_1", 912, 241, 58, 31, 0], [2, "1416", 731, 399, 24, 17, 0], [2, "1416", 556, 371, 24, 17, 0], [2, "1416", 877, 256, 24, 17, 0], [2, "1416", 996, 218, 24, 17, 0], [2, "1416", 1002, 560, 24, 17, 0], [2, "1416", 988, 662, 24, 17, 0], [2, "1416", 705, 709, 24, 17, 0], [2, "1416", 777, 791, 24, 17, 0], [2, "1416", 120, 821, 24, 17, 0], [2, "1416", 200, 657, 24, 17, 0], [2, "1416", 373, 608, 24, 17, 0], [2, "1416", 400, 454, 24, 17, 0], [2, "1416", 49, 528, 24, 17, 0], [2, "1416", 3, 584, 24, 17, 0], [2, "1159_1", 875, 121, 36, 37, 2], [2, "1159_1", 638, 194, 36, 37, 0], [2, "1159_1", 731, 67, 36, 37, 0], [2, "1159_1", 694, 65, 36, 37, 2], [2, "1159_1", 761, 9, 36, 37, 0], [2, "1416", 726, 13, 24, 17, 0], [2, "1416", 765, 215, 24, 17, 0], [2, "1416", 902, 91, 24, 17, 0], [2, "1414_1", 680, -12, 46, 30, 2], [2, "1414_1", 705, -7, 46, 30, 2], [2, "1414_1", 727, -13, 46, 30, 0], [2, "1368_1", 1017, 352, 50, 42, 0], [2, "220_1", -7, 481, 40, 29, 0], [2, "220_1", 9, 493, 40, 29, 0], [2, "220_1", -5, 506, 40, 29, 2], [2, "220_1", 106, 629, 40, 29, 2], [2, "1159_1", 262, 609, 36, 37, 2], [2, "1159_1", 226, 744, 36, 37, 2], [2, "1159_1", 395, 661, 36, 37, 0], [2, "1159_1", 370, 674, 36, 37, 0], [2, "1159_1", 27, 669, 36, 37, 0], [2, "1159_1", 262, 609, 36, 37, 2], [2, "1159_1", 317, 712, 36, 37, 0], [2, "1159_1", 288, 673, 36, 37, 0], [2, "1159_1", 256, 676, 36, 37, 0], [2, "1159_1", 287, 638, 36, 37, 2], [2, "1159_1", 348, 605, 36, 37, 2], [2, "1159_1", 388, 620, 36, 37, 2], [2, "1159_1", 414, 638, 36, 37, 2], [2, "1159_1", 2, 743, 36, 37, 0], [2, "1159_1", 1009, 596, 36, 37, 2], [2, "1159_1", 988, 610, 36, 37, 2], [2, "1159_1", 1011, 619, 36, 37, 2], [2, "1159_1", 997, 644, 36, 37, 2], [2, "1159_1", 1032, 650, 36, 37, 2], [2, "1159_1", 1001, 672, 36, 37, 0], [2, "1159_1", 92, 838, 36, 37, 0], [2, "1159_1", 551, 811, 36, 37, 2], [2, "1159_1", 582, 823, 36, 37, 2], [2, "1159_1", 667, 833, 36, 37, 2], [2, "1159_1", 704, 829, 36, 37, 2], [2, "1159_1", 714, 785, 36, 37, 0], [2, "1159_1", 676, 852, 36, 37, 0], [2, "1159_1", 679, 682, 36, 37, 0], [2, "1159_1", 515, 291, 36, 37, 0], [2, "1159_1", 724, 477, 36, 37, 0], [2, "1159_1", 997, 356, 36, 37, 2], [2, "1159_1", 1018, 383, 36, 37, 2], [2, "1159_1", 935, 255, 36, 37, 0], [2, "1159_1", 974, 223, 36, 37, 0], [2, "1159_1", 963, 176, 36, 37, 2], [2, "1159_1", 948, 197, 36, 37, 0], [2, "1463_1", 260, 304, 22, 59, 0], [2, "1463_1", 248, 311, 22, 59, 0], [2, "1463_1", 236, 318, 22, 59, 0], [2, "1463_1", 224, 325, 22, 59, 0], [2, "1463_1", 212, 332, 22, 59, 0], [2, "1463_1", 200, 339, 22, 59, 0], [2, "1463_1", 186, 346, 22, 59, 0], [2, "1463_1", 174, 353, 22, 59, 0], [2, "1463_1", 161, 360, 22, 59, 0], [2, "1463_1", 149, 367, 22, 59, 0], [2, "1463_1", 136, 374, 22, 59, 0], [2, "1463_1", 124, 381, 22, 59, 0], [2, "1463_1", -4, 341, 22, 59, 0], [2, "1463_1", 9, 348, 22, 59, 0], [2, "1463_1", 22, 353, 22, 59, 0], [2, "1463_1", 35, 360, 22, 59, 0], [2, "1463_1", 46, 364, 22, 59, 0], [2, "1463_1", 59, 371, 22, 59, 0], [2, "1463_1", 74, 377, 22, 59, 0], [2, "1463_1", 87, 384, 22, 59, 0], [2, "1463_1", 100, 388, 22, 59, 0], [2, "1463_1", 112, 390, 22, 59, 0], [2, "1160_1", -32, 345, 88, 75, 0], [2, "1160_1", 25, 363, 88, 75, 0], [2, "1161_1", 281, 284, 54, 52, 0], [2, "1161_1", 257, 300, 54, 52, 0], [2, "1161_1", 232, 315, 54, 52, 0], [2, "1161_1", 210, 331, 54, 52, 0], [2, "1161_1", 187, 347, 54, 52, 0], [2, "1161_1", 165, 360, 54, 52, 0], [2, "1161_1", 139, 376, 54, 52, 0], [2, "1160_1", 90, 371, 88, 75, 2], [2, "1161_1", 586, 126, 54, 52, 0], [2, "1161_1", 562, 143, 54, 52, 0], [2, "1161_1", 536, 156, 54, 52, 0], [2, "1161_1", 514, 171, 54, 52, 0], [2, "1161_1", 491, 181, 54, 52, 0], [2, "1161_1", 467, 194, 54, 52, 0], [2, "1161_1", 445, 206, 54, 52, 0], [2, "1161_1", 421, 222, 54, 52, 0], [2, "1159_1", 323, 286, 36, 37, 2], [2, "1160_1", 256, -24, 88, 75, 0], [2, "1160_1", 308, -8, 88, 75, 0], [2, "1160_1", 359, 2, 88, 75, 0], [2, "1160_1", 412, 15, 88, 75, 0], [2, "1160_1", 451, 32, 88, 75, 0], [2, "1160_1", 487, 51, 88, 75, 0], [2, "1160_1", 534, 76, 88, 75, 0], [2, "1414", 344, 57, 46, 30, 2], [2, "1414", 303, 54, 46, 30, 2], [2, "1416", 408, 108, 24, 17, 0], [2, "1488", 383, 50, 64, 53, 0], [2, "1488", 419, 65, 64, 53, 0], [2, "1488", 485, 95, 64, 53, 0], [2, "1412", 422, 94, 32, 28, 2], [2, "1159_1", 229, 359, 36, 37, 0], [2, "1159_1", 169, 399, 36, 37, 0], [2, "1159_1", 147, 419, 36, 37, 0], [2, "1159_1", 88, 416, 36, 37, 2], [2, "1159_1", 115, 417, 36, 37, 2], [2, "1159_1", -8, 397, 36, 37, 2], [2, "1159_1", 535, 194, 36, 37, 0], [2, "1159_1", 572, 178, 36, 37, 0], [2, "1159_1", 459, 238, 36, 37, 0], [2, "1159_1", 556, 917, 36, 37, 0], [2, "1159_1", 587, 905, 36, 37, 0], [2, "1159_1", 727, 907, 36, 37, 0], [2, "1368_1", 996, 850, 50, 42, 0], [2, "1159_1", 923, 903, 36, 37, 0], [2, "1159_1", 930, 812, 36, 37, 2], [2, "1159_1", 954, 831, 36, 37, 2], [2, "1159_1", 924, 827, 36, 37, 2], [2, "1159_1", 1020, 879, 36, 37, 2], [2, "1368_1", 1011, 624, 50, 42, 0], [2, "1159_1", 260, 864, 36, 37, 2], [2, "1159_1", 162, 817, 36, 37, 2], [2, "1416", 114, 900, 24, 17, 0], [2, "1159_1", 63, 908, 36, 37, 0], [2, "1159_1", 423, 421, 36, 37, 2], [2, "1159_1", 771, 167, 36, 37, 2], [2, "1159_1", 60, 532, 36, 37, 0], [2, "923", 238, 88, 44, 125, 2], [2, "891", 233, 142, 54, 75, 0], [2, "890", 214, 137, 94, 105, 0], [2, "1159_1", 324, 37, 36, 37, 2], [2, "1414", 272, 192, 46, 30, 0], [2, "1368_1", 928, 240, 50, 42, 0], [2, "1416", 252, 239, 24, 17, 0], [2, "1416", 290, 237, 24, 17, 0], [2, "1416", 360, 722, 24, 17, 0], [2, "1416", 416, 897, 24, 17, 0], [2, "1416", 641, 863, 24, 17, 0], [2, "1416", 704, 896, 24, 17, 0], [2, "1416", 974, 884, 24, 17, 0], [2, "1416", 933, 739, 24, 17, 0], [2, "1416", 746, 613, 24, 17, 0], [2, "1416", 604, 605, 24, 17, 0], [2, "1416", 492, 463, 24, 17, 0], [2, "1159_1", 791, 726, 36, 37, 2], [2, "1159_1", 877, 399, 36, 37, 2], [2, "1159_1", 903, 257, 36, 37, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 37, 37, 37, 37, 34, 35, -1, -1, -1, -1, -1, 12, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 37, 38, 34, 35, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 37, 38, 35, -1, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 4, 4, 13, 14, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, -1, -1, -1, -1, -1, 8, 7, 7, 11, 4, 4, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 42, -1, -1, -1, -1, 8, 7, 10, 16, 4, 4, 4, 4, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 54, -1, -1, -1, 8, 11, 10, 16, 4, 4, 4, 4, 4, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, -1, -1, -1, 11, 4, 10, 16, 16, 4, 4, 4, 4, 4, 5, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 46, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 54, -1, -1, -1, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 46, 47, -1, -1, -1, 15, 16, 4, 4, 4, 4, 4, 4, 4, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 46, 47, -1, -1, -1, -1, -1, 12, 23, 16, 4, 4, 4, 4, 16, 17, 13, 14, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 46, 47, -1, -1, -1, 41, 40, -1, -1, -1, 12, 23, 4, 4, 4, 4, 17, 14, -1, -1, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 46, 47, -1, -1, -1, -1, -1, 48, 55, 55, -1, -1, -1, 20, 19, 23, 4, 4, 21, -1, -1, 0, 11, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 46, 47, -1, -1, -1, -1, -1, -1, -1, 53, 52, 52, -1, -1, -1, -1, -1, 15, 4, 4, 21, -1, -1, 3, 4, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, 49, 50, 51, -1, -1, -1, -1, -1, -1, 8, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 6, -1, 15, 16, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 49, 50, 46, 47, -1, -1, -1, -1, -1, -1, 8, 11, 10, 4, 5, 1, 2, -1, -1, 8, 7, 7, 11, 4, 4, 4, 5, 2, 12, 23, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 49, 50, 46, 47, -1, 8, 7, 6, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 5, 7, 7, 11, 10, 10, 16, 16, 16, 4, 4, 5, -1, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 46, 47, -1, -1, 8, 11, 10, 5, 1, 2, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 4, 4, 7, 6, -1, 56, 55, -1, -1, -1, -1, 49, 49, 50, 46, 47, -1, -1, -1, 8, 11, 4, 4, 4, 4, 5, 2, -1, -1, 23, 22, 22, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 4, 10, 9, -1, 53, 52, 46, 46, 46, 46, 46, 46, 47, -1, -1, -1, -1, 8, 11, 4, 4, 4, 4, 4, 4, 21, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 16, 4, 16, 16, 16, 16, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 5, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 9, -1, 7, 7, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 5, 7, 7, 7, 2, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 9, -1, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 16, 16, 16, 4, 4, 4, 4, 10, 10, 2, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 4, 16, 9, -1, 4, 4, 4, 4, 4, 4, 5, 7, 4, 1, 2, -1, -1, 15, 16, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 22, 9, -1, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 16, 17, 19, 14, -1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 7, 6, 12, 23, 22, 4, 4, 4, 4, 4, 4, 4, 17, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, 20, 19, 13, 13, 13, 13, 14, 8, -1, -1, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 4, 10, 9, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 2, -1, -1, 20, 23, 22, 22, 22, 22, 22, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 16, 17, 19, 23, 22, 4, 22, 16, 9, -1, -1, 8, 11, 4, 4, 4, 4, 4, 4, 5, 7, 7, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 4, 4, 4, 13, 14, -1, 20, 19, 4, 19, 13, 14, 8, 7, 11, 10, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 10, 16, 17, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, 4, 4, 4, 22, 22, 22, 16, 16, 16, 16, 16, 16, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 13, 14, -1, -1, -1, -1, -1, 0, 1, 1, 11, 10, 4, 4, 4, 4, 4, 4, 16, 22, 22, 22, 22, 22, 16, 16, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 13, 14, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 4, 17, 23, 22, 4, 4, 4, 16, 16, 16, 4, 4, 4, 4, 4, 4, 16, 16, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 4, 4, 4, 4, 4, 4, 17, 14, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 1, 2, 20, 19, 23, 22, 4, 4, 17, 13, 14, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 5, 6, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, 4, 5, 1, 2, 20, 19, 19, 19, 14, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 16, 16, 16, 17, 19, 13, 23, 16, 16, 16, 16, 5, 2, -1, -1, -1, -1, 0, 11, 10, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 4, 16, 16, 22, 22, 9, -1, -1, 20, 19, 19, 23, 16, 4, 5, 6, -1, 0, 1, 11, 4, 4, 4, 4, 4, 4, -1, 4, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 4, 4, 4, 4, 5, 2, -1, -1, -1, -1, 3, 4, 4, 4, 4, -1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, 4, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, 3, 4, 4, 4, 4, 4, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 2, -1, -1, -1, -1, -1, 8, 11, 10, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 7, 7, 11, 4, 4, 4, 4, 4]}, {"type": 2, "data": [24, 25, 26, 24, 25, 24, 25, 26, 24, 25, 24, 25, 26, 26, 24, 24, 25, 24, 25, 26, 24, 25, 24, 25, 26, 26, 24, 25, 24, 25, 26, 24, 25, 24, 25, 26, 24, 25, 26, 24, 24, 25, 26, 25, 27, 28, 29, 27, 28, 27, 28, 29, 27, 28, 27, 28, 29, 29, 27, 27, 28, 27, 28, 24, 25, 26, 27, 28, 29, 26, 27, 28, 27, 28, 29, 24, 25, 26, 28, 29, 27, 28, 24, 27, 27, 28, 29, 28, 30, 31, 24, 25, 26, 30, 31, 32, 30, 31, 30, 31, 32, 32, 30, 30, 31, 30, 31, 27, 28, 29, 30, 31, 32, 29, 30, 31, 30, 31, 32, 27, 28, 29, 31, 32, 30, 31, 24, 30, 30, 31, 32, 31, 24, 25, 27, 28, 24, 25, 26, 31, 24, 25, 26, 30, 31, 32, 30, 31, 32, 27, 28, 30, 31, 24, 25, 26, 25, 26, 27, 28, 29, 29, 30, 30, 31, 32, 24, 25, 26, 26, 27, 30, 31, 32, 24, 25, 27, 28, 30, 31, 27, 28, 29, 24, 27, 28, 29, 30, 31, 32, 32, 32, 29, 30, 31, 32, 24, 27, 28, 29, 28, 29, 30, 31, 32, 32, 24, 30, 31, 32, 27, 28, 29, 29, 24, 30, 24, 25, 27, 28, 24, 24, 25, 26, 30, 31, 32, 27, 30, 31, 32, 24, 24, 25, 26, 31, 24, 24, 25, 26, 27, 30, 31, 32, 31, 32, 30, 31, 32, 29, 27, 24, 25, 26, 30, 31, 32, 32, 27, 28, 27, 28, 30, 31, 24, 27, 28, 29, 24, 25, 26, 30, 24, 25, 26, 27, 27, 28, 29, 25, 27, 27, 28, 29, 30, 31, 32, 27, 28, 24, 25, 26, 25, 26, 24, 25, 26, 24, 25, 26, 27, 28, 30, 31, 30, 24, 25, 26, 27, 30, 31, 32, 27, 28, 29, 25, 27, 28, 29, 24, 25, 26, 32, 28, 30, 30, 24, 25, 26, 24, 25, 26, 26, 27, 28, 29, 24, 25, 27, 28, 29, 27, 28, 29, 30, 31, 32, 24, 25, 26, 25, 26, 30, 31, 32, 24, 30, 31, 24, 25, 26, 31, 32, 27, 28, 29, 29, 31, 32, 24, 25, 26, 29, 27, 24, 25, 24, 30, 31, 32, 27, 28, 30, 31, 32, 30, 24, 25, 26, 24, 25, 24, 25, 26, 26, 26, 24, 25, 24, 25, 26, 24, 27, 24, 25, 26, 28, 30, 31, 32, 32, 31, 32, 27, 28, 29, 32, 30, 27, 28, 27, 30, 31, 32, 30, 31, 32, 31, 30, 31, 27, 28, 29, 27, 28, 27, 28, 29, 29, 29, 27, 24, 27, 28, 29, 27, 30, 27, 28, 29, 25, 26, 25, 26, 25, 26, 26, 30, 31, 32, 32, 30, 30, 31, 30, 24, 25, 30, 27, 28, 29, 29, 24, 25, 30, 31, 32, 30, 31, 30, 31, 32, 32, 32, 30, 27, 30, 31, 32, 26, 31, 30, 31, 32, 28, 29, 28, 29, 28, 29, 29, 30, 31, 32, 24, 24, 25, 24, 25, 27, 28, 29, 30, 31, 32, 25, 27, 24, 25, 26, 26, 24, 24, 25, 26, 24, 25, 26, 24, 25, 26, 27, 28, 29, 32, 30, 24, 30, 31, 32, 31, 32, 31, 32, 32, 24, 25, 26, 27, 27, 28, 27, 28, 30, 31, 32, 32, 26, 24, 25, 30, 27, 28, 29, 29, 27, 27, 28, 29, 24, 24, 25, 27, 28, 29, 30, 31, 32, 26, 30, 27, 28, 29, 24, 25, 24, 24, 25, 26, 27, 28, 29, 30, 24, 25, 26, 24, 25, 26, 26, 28, 29, 27, 28, 30, 30, 24, 25, 26, 25, 26, 31, 32, 26, 24, 25, 30, 31, 32, 29, 27, 28, 24, 25, 26, 31, 32, 27, 28, 27, 27, 28, 29, 30, 24, 25, 24, 27, 28, 29, 27, 28, 29, 25, 26, 32, 30, 31, 32, 28, 27, 28, 29, 28, 29, 27, 24, 25, 27, 28, 30, 31, 32, 32, 30, 31, 27, 28, 29, 28, 29, 30, 31, 30, 30, 24, 25, 26, 27, 28, 27, 30, 31, 32, 30, 31, 32, 28, 24, 25, 24, 24, 25, 26, 30, 31, 32, 31, 24, 25, 26, 24, 25, 26, 24, 24, 25, 26, 25, 26, 30, 31, 32, 31, 32, 24, 25, 26, 26, 27, 24, 25, 26, 31, 30, 31, 32, 30, 31, 32, 30, 31, 27, 28, 27, 27, 28, 29, 24, 25, 26, 24, 25, 26, 24, 27, 28, 29, 27, 27, 24, 25, 26, 29, 27, 24, 25, 26, 25, 27, 28, 29, 29, 30, 27, 28, 29, 32, 24, 25, 26, 24, 24, 25, 24, 25, 26, 24, 30, 30, 31, 32, 26, 28, 29, 27, 28, 29, 27, 30, 31, 32, 30, 30, 24, 25, 26, 32, 30, 27, 28, 29, 28, 30, 31, 32, 24, 25, 30, 31, 32, 26, 27, 28, 24, 25, 26, 28, 27, 28, 29, 27, 30, 31, 27, 24, 25, 26, 24, 25, 26, 32, 30, 24, 25, 26, 24, 25, 26, 28, 24, 25, 26, 30, 31, 24, 25, 26, 24, 25, 26, 28, 29, 27, 28, 29, 30, 31, 27, 28, 29, 31, 30, 31, 32, 30, 31, 30, 30, 27, 28, 29, 25, 24, 25, 26, 24, 25, 26, 29, 27, 28, 29, 31, 27, 24, 25, 26, 24, 25, 26, 25, 27, 28, 29, 31, 32, 24, 25, 26, 24, 25, 26, 24, 25, 26, 30, 31, 32, 25, 24, 25, 26, 30, 31, 32, 28, 27, 24, 25, 26, 24, 25, 26, 24, 25, 24, 25, 30, 27, 28, 29, 27, 28, 29, 24, 24, 25, 26, 24, 25, 26, 26, 24, 25, 24, 25, 26, 25, 26, 24, 24, 25, 26, 27, 28, 29, 24, 25, 26, 31, 30, 27, 28, 29, 27, 28, 29, 24, 25, 27, 24, 25, 30, 31, 24, 25, 24, 25, 26, 27, 28, 29, 27, 28, 29, 29, 27, 24, 27, 28, 29, 28, 29, 27, 24, 25, 26, 30, 31, 32, 27, 24, 24, 25, 26, 26, 24, 25, 26, 25, 26, 27, 24, 25, 26, 28, 29, 24, 25, 26, 27, 28, 29, 26, 25, 26, 24, 25, 26, 24, 25, 26, 30, 31, 32, 24, 25, 26, 26, 25, 26, 30, 31, 32, 30, 27, 24, 25, 26, 24, 25, 26, 29, 28, 29, 24, 25, 26, 29, 24, 24, 25, 26, 29, 30, 31, 32, 29, 28, 29, 27, 28, 29, 27, 28, 29, 31, 24, 25, 27, 28, 29, 29, 26, 29, 24, 25, 26, 26, 24, 25, 26, 29, 27, 28, 29, 32, 31, 32, 27, 28, 29, 32, 27, 27, 28, 29, 32, 30, 31, 24, 25, 26, 32, 30, 31, 24, 30, 31, 32, 26, 27, 28, 30, 31, 32, 32, 26, 32, 27, 28, 29, 24, 25, 26, 29, 32, 30, 31, 24, 25, 24, 25, 30, 31, 32, 26, 30, 30, 31, 32, 27, 28, 30, 27, 28, 24, 25, 26, 29, 27, 24, 25, 26, 29, 30, 31, 24, 25, 26, 24, 24, 25, 26, 31, 32, 27, 28, 29, 32, 24, 25, 26, 24, 25, 26, 28, 27, 27, 28, 29, 24, 27, 28, 29, 30, 31, 32, 30, 31, 27, 28, 29, 24, 25, 27, 28, 29, 32, 24, 24, 27, 28, 29, 27, 27, 28, 29, 26, 24, 30, 31, 32, 24, 27, 28, 29, 27, 28, 29, 31, 30, 30, 31, 32, 27, 24, 25, 26, 26, 24, 30, 24, 25, 26, 24, 25, 26, 28, 30, 31, 32, 25, 27, 27, 30, 31, 32, 30, 30, 31, 32, 29, 24, 25, 26, 25, 26, 24, 25, 26, 26, 31, 32, 25, 24, 25, 24, 25, 26, 26, 28, 29, 26, 27, 28, 27, 28, 29, 27, 28, 29, 31, 32, 29, 27, 28, 30, 30, 31, 32, 30, 31, 32, 24, 25, 26, 27, 28, 29, 28, 29, 27, 28, 29, 29, 25, 26, 26, 24, 25, 27, 28, 29, 29, 31, 24, 25, 26, 25, 30, 31, 32, 30, 31, 32, 24, 25, 26, 30, 31, 32, 24, 25, 26, 24, 25, 26, 27, 28, 29, 30, 31, 32, 31, 32, 30, 31, 32, 32, 26, 29, 29, 27, 28, 30, 31, 32, 32, 30, 27, 24, 25, 26, 29, 28, 30, 31, 32, 24, 27, 28, 29, 24, 25, 26, 27, 28, 29, 27, 24, 25, 26, 31, 24, 25, 26, 27, 30, 31, 32, 30, 31, 32, 24, 25, 26, 30, 31, 32, 24, 25, 26, 32, 30, 27, 28, 29, 32, 31, 32, 24, 25, 24, 25, 26, 24, 25, 26, 24, 30, 31, 32, 30, 27, 28, 29, 24, 25, 26, 24, 25, 26, 24, 25, 26, 25, 26, 27, 28, 29, 24, 25, 26, 27, 28, 29, 26, 26, 30, 31, 32, 26, 30, 31, 27, 28, 24, 25, 26, 24, 25, 26, 27, 28, 29, 28, 29, 30, 31, 32, 27, 28, 29, 24, 25, 26, 27, 28, 29, 28, 29, 30, 31, 32, 24, 25, 26, 30, 31, 32, 24, 24, 25, 27, 28, 29, 24, 25, 30, 31, 27, 28, 29, 27, 28, 29, 30, 31, 32, 24, 25, 26, 24, 25, 30, 31, 32, 27, 28, 29, 26, 31, 32, 31, 32, 24, 25, 26, 27, 28, 29, 24, 25, 24, 25, 27, 28, 30, 31, 32, 27, 28, 29, 28, 30, 31, 32, 30, 31, 32, 25, 26, 24, 27, 28, 29, 27, 28, 29, 27, 28, 30, 31, 32, 29, 24, 25, 26, 27, 27, 28, 29, 30, 31, 32, 27, 28, 27, 28, 30, 31, 32, 28, 30, 30, 31, 32, 31, 32, 30, 31, 32, 29, 27, 28, 29, 27, 30, 31, 32, 30, 31, 32, 30, 31, 32, 30, 31, 32, 27, 28, 29, 30, 30, 31, 32, 24, 25, 24, 25, 24, 24, 25, 26, 24, 24, 25, 26, 24, 24, 25, 26, 24, 24, 25, 24, 25, 26, 24, 24, 25, 26, 24, 25, 24, 25, 26, 25, 24, 25, 26, 25, 26, 24, 25, 26, 24, 25, 26, 26, 27, 28, 27, 28, 27, 27, 28, 29, 27, 27, 28, 29, 27, 27, 28, 29, 24, 25, 26, 27, 24, 24, 27, 27, 28, 29, 27, 28, 27, 28, 29, 28, 27, 28, 29, 28, 29, 27, 28, 29, 27, 24, 25, 26, 24, 25, 26, 31, 24, 30, 31, 32, 30, 30, 31, 32, 24, 25, 26, 26, 24, 24, 25, 26, 24, 25, 26, 24, 25, 26, 24, 25, 26, 24, 25, 26, 30, 24, 25, 26, 24, 25, 26, 24, 30, 27, 28, 29]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}