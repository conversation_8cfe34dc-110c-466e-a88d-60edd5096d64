{"mW": 912, "mH": 672, "tW": 24, "tH": 24, "tiles": [["315_5", 0, 3, 3], ["1318", 0, 3, 2], ["1318", 2, 3, 2], ["1318", 1, 3, 2], ["1318", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["709_3", 0, 2, 1]], "layers": [{"type": 2, "data": [62, 62, 62, 62, 79, 87, 62, 79, 82, 82, 76, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 79, 80, 87, 86, 86, 78, 86, 62, 62, 62, 62, 62, 62, 62, 62, 62, 79, 80, 75, 73, 74, 75, -1, -1, 73, 74, 88, 87, 62, 62, 62, 78, 79, 74, 87, 78, 62, 79, 75, -1, 83, 82, 87, 86, 78, 79, 74, 87, 62, 62, 62, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 62, 79, 75, -1, 73, 74, 82, 75, -1, -1, -1, -1, 83, 82, 82, 81, -1, 77, 62, 62, 62, 62, 62, 62, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, 57, 71, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 62, 62, 62, 62, 62, 62, 62, 69, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 78, 79, 81, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 71, 62, 62, 62, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 62, 63, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 62, 62, 62, 79, 82, 81, -1, -1, -1, -1, -1, -1, -1, 57, 58, 60, 62, 62, 78, 68, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 87, 62, 62, 86, 85, -1, -1, -1, -1, -1, 67, 58, 64, 64, 60, 62, 62, 70, 62, 86, 86, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 79, 81, -1, -1, -1, -1, -1, 83, 87, 62, 62, 62, 79, 82, 87, 78, 78, 79, 81, -1, -1, 57, 58, 58, 58, 59, 67, 66, 58, 64, 64, 64, 59, -1, -1, -1, 62, 62, 75, -1, -1, -1, -1, -1, -1, -1, 73, 87, 62, 62, 69, -1, 73, 74, 82, 81, -1, -1, -1, 61, 62, 62, 62, 63, 71, 62, 62, 62, 62, 62, 63, 59, -1, 61, 62, 62, 59, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, 83, 82, 88, 87, 62, 86, 62, 79, 80, 80, 87, 62, 63, 64, 71, 62, 62, 63, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 74, 74, 74, 75, 67, 64, 71, 62, 62, 62, 62, 62, 62, 70, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 71, 62, 62, 62, 62, 62, 62, 62, 62, 62, 69, -1, -1, -1, -1, -1, 67, 58, 64, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 66, 71, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 63, 59, -1, -1, -1, 67, 71, 70, 62, 68, 64, 64, 64, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 71, 62, 62, 62, 78, 79, 80, 88, 87, 62, 62, 62, 62, 62, 63, 64, 64, 72, 71, 62, 62, 78, 78, 78, 78, 70, 70, 85, -1, -1, -1, -1, -1, 67, 66, 66, 60, 62, 62, 62, 79, 74, 74, 75, -1, -1, 83, 87, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 79, 87, 62, 79, 87, 68, 59, -1, -1, -1, 67, 71, 62, 62, 62, 78, 78, 79, 75, -1, -1, -1, -1, -1, -1, 83, 87, 62, 62, 62, 62, 62, 62, 79, 74, 75, 83, 74, 75, 83, 82, 75, 77, 84, 75, -1, -1, 67, 71, 62, 62, 84, 82, 74, 74, 75, -1, -1, -1, -1, -1, -1, -1, -1, 77, 62, 62, 62, 62, 62, 79, 75, -1, -1, -1, -1, -1, -1, 67, 66, 60, 63, 59, -1, -1, 77, 62, 84, 82, 75, 67, 66, 65, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 62, 62, 79, 74, 74, 75, -1, -1, 57, 58, 66, 65, 57, 71, 62, 62, 86, 85, -1, -1, 61, 62, 63, 64, 64, 71, 62, 68, 58, 59, -1, -1, -1, -1, -1, -1, -1, 77, 62, 79, 75, -1, -1, -1, -1, -1, 61, 62, 70, 69, 73, 87, 62, 62, 79, 81, -1, -1, 77, 62, 62, 70, 70, 62, 62, 62, 62, 85, -1, -1, -1, -1, -1, -1, -1, 61, 62, 85, -1, -1, -1, -1, -1, -1, 83, 82, 74, 75, -1, 83, 82, 74, 75, -1, -1, -1, 73, 74, 80, 80, 80, 88, 87, 62, 62, 68, 66, 65, -1, -1, -1, -1, -1, 83, 87, 63, 64, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 88, 87, 62, 62, 68, 66, 65, -1, -1, -1, 57, 71, 62, 62, 63, 64, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 62, 70, 68, 65, -1, -1, 61, 62, 62, 62, 62, 62, 63, 58, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 87, 62, 62, 62, 69, -1, -1, 77, 62, 62, 62, 62, 62, 62, 70, 85, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 67, 71, 62, 62, 62, 63, 64, 64, 71, 62, 62, 62, 62, 62, 79, 82, 81, -1, -1, 67, 66, 59, -1, 67, 66, 66, 59, -1, -1, 67, 66, 64, 58, 59, -1, -1, 67, 66, 60, 70, 70, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 63, 64, 64, 64, 64, 71, 62, 63, 64, 71, 62, 62, 68, 66, 66, 71, 70, 62, 62, 63, 58, 72, 71, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62]}, {"type": 3, "obj": [[2, "1308", 582, 1, 22, 37, 0], [2, "1310", 504, 47, 18, 29, 0], [2, "1309", 426, 190, 20, 32, 2], [2, "1310", 839, 145, 18, 29, 0], [2, "1310", 802, 142, 18, 29, 0], [2, "1310", 716, 238, 18, 29, 0], [2, "1308", 778, 327, 22, 37, 0], [2, "1308", 832, 345, 22, 37, 0], [2, "1310", 358, 355, 18, 29, 2], [2, "1310", 540, 238, 18, 29, 2], [2, "1309", 650, 16, 20, 32, 0], [2, "1310", 806, 7, 18, 29, 0], [2, "1310", 179, 186, 18, 29, 0], [2, "1309", 684, 47, 20, 32, 0], [2, "1310", 664, 498, 18, 29, 2], [2, "1309", 195, 3, 20, 32, 2], [2, "1308", 102, 6, 22, 37, 0], [2, "1308", 335, 26, 22, 37, 0], [2, "1310", 862, 385, 18, 29, 0], [2, "1310", 344, 449, 18, 29, 0], [2, "1308", 753, 335, 22, 37, 0], [2, "1308", 608, 519, 22, 37, 0], [2, "1308", 665, 549, 22, 37, 0], [2, "1308", 557, 499, 22, 37, 0], [2, "1308", 564, 406, 22, 37, 0], [2, "1308", 633, 400, 22, 37, 0], [2, "1308", 519, 423, 22, 37, 0], [2, "1310", 689, 357, 18, 29, 2], [2, "1310", 643, 525, 18, 29, 0], [2, "1308", 512, 496, 22, 37, 0], [2, "1309", 594, 505, 20, 32, 0], [2, "1310", 579, 496, 18, 29, 0], [2, "1310", 695, 575, 18, 29, 0], [2, "1310", 672, 381, 18, 29, 2], [2, "1310", 471, 492, 18, 29, 0], [2, "1308", 512, 229, 22, 37, 0], [2, "1309", 609, 267, 20, 32, 0], [2, "1310", 648, 264, 18, 29, 0], [2, "1308", 587, 256, 22, 37, 0], [2, "1310", 795, 328, 18, 29, 0], [2, "1308", 190, 192, 22, 37, 0], [2, "1310", 319, 193, 18, 29, 2], [2, "1310", 293, 191, 18, 29, 2], [2, "1310", 411, 196, 18, 29, 0], [2, "1309", 376, 408, 20, 32, 0], [2, "1310", 364, 419, 18, 29, 2], [2, "1310", 298, 492, 18, 29, 0], [2, "1310", 366, 486, 18, 29, 0], [2, "1310", 383, 453, 18, 29, 2], [2, "1308", 844, 363, 22, 37, 0], [2, "1310", 234, 402, 18, 29, 2], [2, "1310", 273, 241, 18, 29, 2], [2, "1310", 198, 3, 18, 29, 2]]}, {"type": 4, "obj": []}, {"type": 3, "obj": [[2, "1311", 174, 401, 44, 81, 2], [2, "1311", 248, 389, 44, 81, 0], [2, "1312", 36, 461, 36, 78, 0], [2, "1311", 77, 438, 44, 81, 0], [2, "425_1", 248, 551, 30, 36, 0], [2, "1326", 200, 518, 58, 21, 0], [2, "1311", 430, 27, 44, 81, 0], [2, "1311", 341, 205, 44, 81, 2], [2, "1311", 167, 8, 44, 81, 2], [2, "1312", 138, 12, 36, 78, 0], [2, "11_1", 803, 204, 32, 29, 0], [2, "1312", 792, 341, 36, 78, 2], [2, "1311", 674, 240, 44, 81, 0], [2, "1312", 620, 519, 36, 78, 0], [2, "1311", 590, 388, 44, 81, 0], [2, "1311", 701, 352, 44, 81, 0], [2, "1311", 750, 340, 44, 81, 0], [2, "1311", 0, 163, 44, 81, 0], [2, "1312", 392, 208, 36, 78, 0], [2, "1311", 356, 494, 44, 81, 0], [2, "1311", 546, 237, 44, 81, 2], [2, "1312", 611, 399, 36, 78, 2], [2, "1311", 199, 217, 44, 81, 2], [2, "1306_1", 804, 235, 34, 33, 2], [2, "1311", 813, 29, 44, 81, 2], [2, "1312", 682, -2, 36, 78, 0], [2, "1311", 646, 26, 44, 81, 0], [2, "1312", 197, 405, 36, 78, 2], [2, "1311", 298, 7, 44, 81, 0], [2, "1312", 270, 14, 36, 78, 0], [2, "1311", 93, 1, 44, 81, 2], [2, "1309", 172, 5, 20, 32, 2], [2, "1308", 154, 2, 22, 37, 0], [2, "1311", 201, 22, 44, 81, 2], [2, "1311", 234, 10, 44, 81, 0], [2, "212", 322, 21, 44, 99, 0], [2, "181_2", 55, 13, 104, 100, 2], [2, "1311", 27, 43, 44, 81, 0], [2, "1306_1", 28, 95, 34, 33, 0], [2, "212", 41, 451, 44, 99, 0], [2, "1311", 698, 233, 44, 81, 0], [2, "1311", 219, 349, 44, 81, 2], [2, "1306_1", 280, 67, 34, 33, 0], [2, "1311", 572, 509, 44, 81, 2], [2, "1312", 473, 460, 36, 78, 0], [2, "1311", 617, 225, 44, 81, 2], [2, "1311", 583, 243, 44, 81, 0], [2, "1311", 556, 19, 44, 81, 0], [2, "1311", 607, 5, 44, 81, 2], [2, "212", 576, 1, 44, 99, 0], [2, "1306_1", 627, 80, 34, 33, 0], [2, "1312", 700, -5, 36, 78, 2], [2, "1311", 725, 1, 44, 81, 2], [2, "1311", 759, 7, 44, 81, 0], [2, "1308", 852, 46, 22, 37, 0], [2, "1312", 820, 212, 36, 78, 2], [2, "1311", 842, 221, 44, 81, 2], [2, "212", 367, 194, 44, 99, 2], [2, "1311", 492, 500, 44, 81, 2], [2, "1311", 535, 511, 44, 81, 2], [2, "1311", 868, 500, 44, 81, 2], [2, "1311", 1, 481, 44, 81, 0], [2, "212", 723, 334, 44, 99, 2], [2, "1311", 861, 581, 44, 81, 2], [2, "1311", 288, 373, 44, 81, 2], [2, "1311", 127, 406, 44, 81, 0], [2, "212", 272, 406, 44, 99, 0], [2, "1311", 336, 480, 44, 81, 0], [2, "1306_1", 792, 506, 34, 33, 0], [2, "411_3", 351, 148, 44, 40, 2], [2, "1311", 576, 522, 44, 81, 2], [2, "212", 584, 502, 44, 99, 0], [2, "1311", 476, 505, 44, 81, 2], [2, "1311", 123, 601, 44, 81, 0], [2, "1311", 849, 34, 44, 81, 2], [2, "1311", 844, 78, 44, 81, 2], [2, "1311", 531, 47, 44, 81, 0], [2, "212", 782, 8, 44, 99, 0], [2, "1311", 618, 249, 44, 81, 2], [2, "366_3", 462, 173, 32, 48, 2], [2, "212", 512, 497, 44, 99, 0], [2, "181_2", 773, 342, 104, 100, 0], [2, "364_3", 830, 432, 44, 64, 0], [2, "1311", 672, 363, 44, 81, 0], [2, "212", 635, 370, 44, 99, 0], [2, "1312", 566, 401, 36, 78, 0], [2, "1312", 514, 433, 36, 78, 0], [2, "1311", 537, 424, 44, 81, 0], [2, "1306_1", 839, 597, 34, 33, 2], [2, "1311", 684, 585, 44, 81, 2], [2, "1312", 859, 374, 36, 78, 2], [2, "1312", 719, 245, 36, 78, 2], [2, "212", 641, 251, 44, 99, 0], [2, "212", 562, 227, 44, 99, 0], [2, "212", 821, 146, 44, 99, 0], [2, "11_1", 679, 429, 32, 29, 0], [2, "212", 691, 29, 44, 99, 0], [2, "1312", 417, 46, 36, 78, 0], [2, "6_1", 694, 95, 98, 73, 2], [2, "13_1", 709, 89, 22, 24, 0], [2, "7_1", 746, 105, 28, 27, 2], [2, "212", 197, 6, 44, 99, 0], [2, "1306_1", 208, 166, 34, 33, 2], [2, "1311", 293, 119, 44, 81, 2], [2, "1311", 53, 120, 44, 81, 0], [2, "212", 30, 135, 44, 99, 2], [2, "1311", 264, 223, 44, 81, 0], [2, "212", 229, 209, 44, 99, 0], [2, "366_3", 80, 156, 32, 48, 0], [2, "11_1", 45, 214, 32, 29, 0], [2, "1311", -10, 194, 44, 81, 0], [2, "1311", 386, 81, 44, 81, 0], [2, "1306_1", 57, 332, 34, 33, 0], [2, "13_1", 26, 233, 22, 24, 2], [2, "11_1", 229, 84, 32, 29, 0], [2, "364_3", 278, 60, 44, 64, 2], [2, "1311", 462, 17, 44, 81, 0], [2, "1311", 483, 38, 44, 81, 2], [2, "212", 518, 31, 44, 99, 2], [2, "955_2", 441, 238, 20, 18, 0], [2, "1321", 184, 506, 54, 84, 0], [2, "1322", 132, 564, 64, 38, 0], [2, "1323", 184, 587, 94, 24, 0], [2, "1324", 267, 546, 40, 43, 0], [2, "1325", 124, 517, 80, 36, 0], [2, "1327", 248, 515, 70, 44, 0], [2, "955_2", 111, 326, 20, 18, 0], [2, "1306_1", 112, 538, 34, 33, 2], [2, "8_1", 470, 315, 38, 29, 2], [2, "1320", 423, 290, 76, 64, 0], [2, "8_1", 414, 338, 38, 29, 2], [2, "12_1", 449, 285, 26, 28, 0], [2, "8_1", 454, 105, 38, 29, 0], [2, "1109_1", 471, 91, 18, 21, 0], [2, "1109_1", 456, 95, 18, 21, 0], [2, "1109_1", 473, 98, 18, 21, 0], [2, "1109_1", 448, 114, 18, 21, 0], [2, "1109_1", 507, 117, 18, 21, 0], [2, "8_1", 689, 149, 38, 29, 2], [2, "955_2", 745, 165, 20, 18, 0], [2, "1311", 317, 381, 44, 81, 0], [2, "1306_1", 187, 640, 34, 33, 0], [2, "1311", 309, 489, 44, 81, 2], [2, "212", 344, 487, 44, 99, 2], [2, "1312", 225, 404, 36, 78, 0], [2, "1312", 158, 405, 36, 78, 0], [2, "1311", 97, 428, 44, 81, 0], [2, "11_1", 140, 476, 32, 29, 0], [2, "13_1", 95, 507, 22, 24, 0], [2, "1109_1", 263, 468, 18, 21, 0], [2, "1109_1", 249, 477, 18, 21, 0], [2, "1109_1", 269, 484, 18, 21, 0], [2, "1108_1", 439, 146, 26, 31, 2], [2, "13_1", 403, 260, 22, 24, 0], [2, "366_3", 277, 261, 32, 48, 0], [2, "12_1", 546, 297, 26, 28, 0], [2, "1306_1", 560, 313, 34, 33, 0], [2, "955_2", 456, 564, 20, 18, 2], [2, "955_2", 508, 583, 20, 18, 2], [2, "426_1", 146, 539, 26, 22, 0], [2, "14_4", 760, 78, 32, 30, 2], [2, "14_4", 780, 93, 32, 30, 2], [2, "14_4", 769, 70, 32, 30, 2], [2, "116_1", 641, 93, 46, 39, 0], [2, "113_1", 655, 73, 26, 33, 0], [2, "1301_1", 178, 235, 24, 49, 0], [2, "1301_1", 450, 188, 24, 49, 2], [2, "1301_1", 402, 623, 24, 49, 2], [2, "955_2", 170, 273, 20, 18, 2], [2, "955_2", 705, 463, 20, 18, 2], [2, "955_2", 488, 387, 20, 18, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, 55, -1, -1, -1, 55, 55, -1, 55, 55, -1, 37, -1, -1, 53, 52, 56, 49, 48, 49, -1, 56, 43, 43, 43, 49, 49, 49, 50, -1, -1, 37, 37, 37, 37, 49, 50, 52, 52, 52, 56, 47, -1, -1, 55, -1, 55, 55, 37, 37, 37, 54, -1, -1, 45, 46, 45, 46, 49, 53, 56, 43, 43, 46, 49, 50, 56, 55, -1, 50, 52, 52, 52, 56, 38, 39, -1, -1, 45, 46, 56, 55, 55, -1, 55, 55, 37, 37, 50, 51, 9, 10, 15, -1, -1, 45, 46, 15, -1, 43, 43, 49, 46, 47, 56, 55, 50, 51, -1, -1, -1, 53, 56, 55, -1, -1, -1, -1, 53, 52, 55, -1, 55, 55, 55, 37, 54, -1, 12, 13, 14, 16, 15, -1, -1, 30, 33, 45, 49, 50, 54, 35, 53, 52, 51, -1, 17, 16, 15, -1, 48, 52, 55, 55, 49, 50, -1, -1, 55, -1, 27, -1, 37, 37, 54, 17, 20, 31, 30, 31, 30, -1, 26, 27, 53, 56, 46, 47, 37, 38, 40, 39, -1, 9, 20, 19, 18, -1, 45, 46, 52, 56, 55, 54, 41, 40, -1, -1, -1, 37, 37, 37, 54, 24, 25, 25, 25, 32, 13, 13, -1, -1, 11, 45, 46, 52, 49, 49, 50, 51, 38, 21, 22, 28, 27, -1, -1, -1, -1, 53, 52, 51, 44, 43, -1, -1, 37, 37, 50, 52, 51, 21, 22, 28, 27, 21, 22, 28, -1, 33, 34, -1, -1, 49, 49, 55, 54, 45, 56, -1, -1, 40, 40, -1, 40, -1, 29, 28, 27, 41, 40, 49, -1, -1, 37, 52, 51, -1, 17, 16, 15, -1, -1, -1, -1, -1, -1, 48, 49, 27, 49, 49, 50, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 46, -1, -1, 50, 51, -1, 9, 20, 19, 18, 33, 34, 49, -1, -1, -1, 45, 46, 46, 49, 50, 47, 17, 16, 15, 33, 34, -1, 13, 13, 13, -1, 41, 40, 39, -1, 45, 46, -1, -1, -1, -1, 35, 9, 20, 25, 25, 18, 45, 46, 56, 49, 40, 39, -1, -1, 45, 46, 51, -1, 21, 22, 27, 45, 56, -1, -1, 49, -1, 49, 49, 49, 42, -1, -1, -1, -1, -1, -1, -1, 38, 24, 32, 31, 31, 14, 16, 15, 56, 46, 52, 52, 51, 17, 16, 15, 33, 34, 40, 39, -1, 36, 55, 49, 50, 56, 49, 52, 52, 52, 51, -1, -1, -1, -1, -1, 46, 55, 55, 55, 29, 28, 28, 28, 27, -1, -1, -1, -1, -1, -1, 21, 22, 27, 44, 34, 55, 38, 39, 48, 55, 55, 54, 9, 10, 16, 15, -1, -1, -1, -1, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, 21, -1, -1, 45, 46, 46, 52, 51, 53, 52, 51, 17, 20, 25, 26, 27, -1, -1, 33, 34, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, -1, -1, 9, 16, 15, -1, -1, 49, 54, 9, 20, 13, 26, 23, -1, -1, -1, 37, 46, -1, -1, -1, -1, 55, 55, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, 9, 20, 13, 13, 30, 21, 32, 30, -1, -1, 49, 38, 29, 32, 25, 14, 37, 37, 37, 49, 50, -1, 37, 56, -1, -1, 55, 55, 49, 50, -1, 50, 46, 17, 16, 16, 49, 49, 20, 25, 25, -1, -1, 9, 20, 30, -1, -1, -1, -1, -1, 29, 25, 25, 37, 49, 50, 46, 47, -1, -1, 53, -1, -1, -1, 49, 50, 46, 46, 9, 49, 49, 49, 49, 49, 49, 49, -1, 25, 27, -1, 24, 26, 27, -1, -1, -1, -1, 37, 37, 37, 49, 49, 50, 47, 17, 16, 15, 33, 34, 37, -1, -1, 46, 40, 39, 49, 50, 49, 50, 49, 49, 49, 56, 55, -1, -1, -1, -1, 21, 27, -1, 37, 37, 37, -1, -1, -1, -1, 46, 46, 47, 9, 20, 19, 18, 45, 56, 37, -1, -1, -1, 49, 49, 49, 50, 46, 47, 17, 16, 15, 53, 56, 55, -1, 40, 40, 39, -1, 33, -1, -1, 37, -1, -1, -1, -1, -1, -1, -1, 12, 31, 26, 23, -1, 48, 37, -1, -1, -1, 49, 50, 46, 47, 50, 46, 46, 45, 46, 56, 53, 52, -1, 43, 43, 42, -1, 48, 49, 46, 49, 49, 49, -1, 41, 40, 39, 9, 21, 28, 27, -1, 41, 44, 37, -1, -1, -1, 46, 47, -1, -1, 54, -1, -1, -1, -1, 56, 56, 49, 49, 50, 46, 51, -1, 45, 46, 49, -1, 56, 49, 49, 49, 50, -1, -1, -1, -1, 33, 34, 44, 43, 37, -1, -1, 49, 50, -1, 32, 13, 13, -1, -1, -1, -1, 15, 45, 46, 52, 51, 17, 16, 16, 15, 45, 46, 37, 37, 37, 37, 46, -1, -1, -1, 13, 13, 14, 37, 37, 43, 37, -1, 40, 50, 47, -1, 21, 22, 28, 27, 21, 22, 28, 27, -1, -1, 9, 10, 20, 25, 31, 14, 16, 15, 45, 46, 52, 51, 17, 16, 15, -1, 13, 13, 13, 37, 37, 37, 37, -1, 37, 38, 39, 41, 40, 49, 50, -1, -1, -1, -1, -1, -1, -1, 12, 13, 28, 32, 31, 31, 25, 14, 15, -1, -1, 9, 20, 19, 18, 21, 22, 28, 32, 31, 18, 48, 43, -1, -1, 43, 38, 44, 43, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 27, -1, -1, -1, -1, -1, 21, 22, 23, -1, -1, -1, -1, 29, 28, 27, 44, 43, -1]}, {"type": 2, "data": [0, 1, 0, 1, 2, 3, 4, 5, 1, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 1, 2, 0, 0, 1, 2, 1, 2, 0, 1, 2, 1, 2, 1, 2, 0, 1, 2, 0, 3, 0, 1, 2, 2, 6, 7, 8, 4, 3, 4, 5, 3, 3, 4, 5, 3, 4, 5, 4, 5, 3, 3, 4, 5, 4, 5, 3, 4, 5, 4, 5, 4, 0, 1, 2, 2, 3, 6, 3, 4, 5, 5, 3, 0, 1, 2, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 7, 8, 0, 1, 2, 0, 1, 2, 6, 7, 8, 7, 8, 7, 3, 4, 5, 5, 6, 0, 6, 7, 8, 8, 0, 0, 1, 2, 3, 4, 5, 1, 2, 1, 2, 3, 0, 1, 2, 1, 3, 4, 5, 3, 4, 5, 0, 1, 2, 0, 1, 2, 6, 7, 8, 2, 6, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 4, 5, 6, 3, 4, 5, 0, 6, 7, 8, 6, 7, 8, 3, 4, 0, 1, 2, 5, 3, 3, 4, 5, 0, 6, 7, 0, 1, 2, 5, 6, 0, 1, 0, 1, 2, 4, 5, 0, 1, 2, 6, 7, 8, 2, 4, 5, 2, 3, 0, 1, 2, 7, 3, 4, 5, 0, 1, 6, 7, 8, 3, 0, 1, 3, 4, 5, 8, 0, 3, 4, 3, 4, 5, 7, 8, 3, 4, 5, 5, 3, 4, 5, 0, 0, 1, 0, 1, 2, 5, 6, 6, 7, 8, 3, 4, 5, 6, 7, 6, 0, 1, 2, 7, 0, 0, 1, 6, 7, 6, 7, 8, 4, 5, 6, 7, 8, 1, 6, 7, 8, 3, 3, 4, 3, 4, 5, 8, 1, 2, 4, 0, 6, 7, 8, 5, 3, 3, 3, 4, 5, 1, 3, 3, 4, 3, 4, 5, 0, 1, 2, 8, 7, 8, 3, 4, 0, 1, 2, 1, 6, 7, 6, 0, 1, 2, 4, 5, 7, 3, 6, 7, 8, 0, 1, 6, 6, 7, 8, 4, 6, 6, 7, 6, 7, 8, 3, 4, 5, 4, 5, 3, 6, 7, 3, 4, 5, 4, 5, 5, 1, 3, 4, 5, 1, 2, 0, 6, 7, 8, 1, 3, 4, 0, 0, 1, 6, 7, 8, 8, 0, 0, 1, 2, 6, 7, 0, 1, 2, 6, 6, 3, 6, 7, 8, 7, 8, 8, 4, 6, 7, 8, 0, 1, 2, 4, 5, 3, 4, 6, 7, 3, 3, 4, 0, 1, 2, 3, 3, 3, 4, 5, 8, 6, 3, 4, 5, 2, 2, 6, 7, 8, 6, 3, 4, 5, 0, 1, 2, 8, 3, 4, 5, 7, 8, 6, 7, 8, 6, 6, 6, 7, 3, 4, 5, 6, 6, 6, 7, 8, 5, 0, 6, 7, 8, 5, 5, 6, 7, 6, 0, 1, 2, 8, 3, 4, 5, 1, 2, 7, 8, 2, 0, 3, 0, 1, 2, 6, 3, 4, 6, 7, 8, 7, 8, 1, 2, 8, 8, 3, 0, 6, 7, 8, 8, 1, 2, 0, 3, 4, 5, 8, 6, 7, 8, 4, 5, 3, 4, 5, 0, 1, 2, 4, 5, 0, 6, 7, 8, 0, 1, 0, 3, 4, 5, 4, 0, 1, 3, 4, 0, 1, 2, 4, 5, 3, 6, 7, 8, 0, 0, 3, 6, 7, 8, 6, 7, 8, 3, 4, 5, 7, 8, 3, 0, 1, 0, 0, 1, 3, 6, 7, 8, 2, 3, 4, 6, 7, 3, 4, 5, 7, 8, 6, 3, 0, 1, 2, 3, 6, 6, 7, 8, 2, 7, 8, 6, 7, 8, 1, 2, 6, 3, 4, 3, 0, 1, 2, 7, 3, 4, 5, 6, 7, 8, 4, 6, 7, 8, 2, 4, 5, 0, 1, 2, 5, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, 0, 6, 0, 6, 3, 4, 5, 6, 6, 7, 8, 0, 1, 6, 7, 8, 0, 1, 2, 7, 0, 1, 2, 0, 1, 2, 7, 0, 6, 7, 0, 1, 2, 0, 1, 3, 4, 5, 3, 0, 1, 2, 6, 7, 8, 0, 1, 2, 2, 3, 4, 5, 0, 1, 2, 4, 5, 5, 3, 4, 5, 3, 4, 5, 2, 3, 4, 5, 3, 4, 5, 3, 4, 6, 7, 8, 6, 3, 4, 5, 3, 3, 3, 3, 4, 5, 5, 6, 7, 8, 3, 4, 5, 7, 8, 0, 6, 7, 8, 6, 7, 8, 0, 1, 2, 8, 6, 7, 8, 6, 7, 8, 3, 4, 0, 6, 7, 8, 0, 1, 6, 6, 7, 8, 8, 0, 1, 2, 6, 7, 8, 5, 4, 0, 1, 2, 8, 4, 5, 4, 3, 4, 5, 0, 1, 2, 2, 3, 4, 5, 6, 7, 3, 0, 0, 1, 3, 4, 5, 0, 3, 4, 5, 3, 4, 0, 6, 7, 8, 0, 0, 3, 4, 5, 6, 7, 8, 7, 6, 7, 8, 3, 4, 5, 8, 6, 7, 8, 3, 4, 6, 3, 3, 4, 6, 7, 8, 89, 90, 90, 89, 90, 89, 3, 4, 5, 0, 3, 3, 6, 7, 8, 4, 3, 4, 5, 6, 7, 8, 6, 7, 8, 5, 0, 1, 2, 0, 1, 0, 6, 6, 7, 0, 0, 4, 90, 89, 90, 89, 89, 90, 1, 2, 8, 0, 6, 6, 7, 8, 8, 0, 6, 7, 8, 4, 5, 3, 0, 1, 2, 8, 3, 4, 5, 0, 1, 3, 0, 0, 1, 3, 3, 4, 5, 89, 90, 89, 90, 4, 4, 5, 5, 3, 4, 3, 4, 5, 0, 3, 4, 5, 6, 7, 8, 6, 3, 4, 5, 2, 6, 7, 8, 3, 4, 6, 3, 3, 4, 6, 6, 7, 8, 6, 7, 8, 5, 6, 7, 8, 8, 6, 7, 6, 7, 8, 0, 6, 7, 8, 2, 3, 4, 5, 6, 0, 1, 2, 2, 0, 1, 6, 7, 0, 6, 6, 7, 0, 3, 4, 5, 3, 4, 5, 0, 1, 2, 0, 1, 3, 4, 5, 5, 3, 3, 4, 3, 4, 5, 6, 7, 8, 4, 3, 4, 5, 5, 3, 4, 5, 8, 3, 0, 1, 2, 3, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 4, 6, 7, 8, 8, 6, 6, 7, 6, 7, 8, 7, 8, 6, 7, 6, 7, 8, 8, 6, 7, 8, 6, 6]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1]}