{"mW": 960, "mH": 672, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2]], "layers": [{"type": 4, "obj": []}, {"type": 3, "obj": [[2, "313_2", 181, 288, 70, 44, 0], [2, "313_2", 658, 69, 70, 44, 0], [2, "313_2", 774, 131, 70, 44, 0], [2, "313_2", 740, 71, 70, 44, 0], [2, "313_2", 775, 92, 70, 44, 0], [2, "216", 82, 378, 46, 46, 0], [2, "214_3", 133, 533, 54, 40, 0], [2, "208_3", 262, 257, 78, 40, 2], [2, "205_3", 330, 265, 54, 40, 2], [2, "1150", 655, 214, 48, 85, 2], [2, "214_3", 660, 570, 54, 40, 2], [2, "1150", 197, 28, 48, 85, 0], [2, "1150", 162, 29, 48, 85, 2], [2, "216", 228, 109, 46, 46, 0], [2, "216", 817, 549, 46, 46, 0], [2, "1150", 862, 536, 48, 85, 2], [2, "1149", 893, 559, 40, 82, 2], [2, "1148", 624, 402, 46, 108, 2], [2, "1148", 579, 421, 46, 108, 2], [2, "216", 254, 260, 46, 46, 0], [2, "214_3", 258, 248, 54, 40, 2], [2, "208_3", 232, 234, 78, 40, 2], [2, "205_3", 262, 203, 54, 40, 2], [2, "165_1", 278, 222, 42, 37, 2], [2, "205_3", 239, 270, 54, 40, 0], [2, "313_2", 687, 94, 70, 44, 2], [2, "313_2", 628, 135, 70, 44, 2], [2, "313_2", 582, 158, 70, 44, 2], [2, "313_2", 627, 175, 70, 44, 2], [2, "1152", 635, 239, 38, 26, 0], [2, "214_3", 660, 238, 54, 40, 0], [2, "1150", 689, 257, 48, 85, 2], [2, "1150", 395, 141, 48, 85, 2], [2, "1150", 434, 139, 48, 85, 0], [2, "214_3", 703, 256, 54, 40, 0], [2, "216", 557, 158, 46, 46, 0], [2, "1152", 553, 202, 38, 26, 0], [2, "1150", 916, 569, 48, 85, 2], [2, "208_3", 363, 36, 78, 40, 3], [2, "214_3", 914, 555, 54, 40, 2], [2, "1150", 921, 209, 48, 85, 0], [2, "1149", 551, 449, 40, 82, 0], [2, "208_3", 63, 362, 78, 40, 1], [2, "1150", 674, 384, 48, 85, 2], [2, "1149", 526, 119, 40, 82, 0], [2, "1149", 496, 118, 40, 82, 2], [2, "1149", 407, -44, 40, 82, 2], [2, "1150", 469, 126, 48, 85, 0], [2, "216", 645, 370, 46, 46, 0], [2, "216", 492, 109, 46, 46, 0], [2, "213_3", 445, 116, 64, 45, 2], [2, "1149", 707, 407, 40, 82, 2], [2, "1150", 437, -45, 48, 85, 0], [2, "214_3", -5, 114, 54, 40, 0], [2, "216", 560, 409, 46, 46, 2], [2, "214_3", 519, 500, 54, 40, 2], [2, "214_3", 717, 289, 54, 40, 0], [2, "214_3", 868, 544, 54, 40, 2], [2, "1147", 427, 227, 50, 42, 0], [2, "1149", 727, 404, 40, 82, 0], [2, "214_3", 753, 463, 54, 40, 0], [2, "216", 92, 157, 46, 46, 0], [2, "1152", 717, 518, 38, 26, 0], [2, "214_3", 61, 351, 54, 40, 0], [2, "213_3", 97, 332, 64, 45, 0], [2, "214_3", 708, 406, 54, 40, 0], [2, "1147", 535, 293, 50, 42, 0], [2, "1147", 141, 103, 50, 42, 0], [2, "1154", 528, 154, 28, 51, 0], [2, "313_2", 830, 148, 70, 44, 0], [2, "313_2", 805, 312, 70, 44, 2], [2, "313_2", 207, 360, 70, 44, 2], [2, "152_3", 434, 102, 76, 40, 2], [2, "208_3", 528, 393, 78, 40, 2], [2, "208_3", 515, -6, 78, 40, 0], [2, "208_3", 878, 206, 78, 40, 2], [2, "208_3", 643, 354, 78, 40, 0], [2, "205_3", 913, 179, 54, 40, 2], [2, "208_3", 506, 95, 78, 40, 0], [2, "208_3", -15, 135, 78, 40, 2], [2, "208_3", 44, 110, 78, 40, 0], [2, "152_3", -9, 94, 76, 40, 0], [2, "208_3", 497, 473, 78, 40, 2], [2, "208_3", 495, 513, 78, 40, 3], [2, "208_3", 716, 271, 78, 40, 1], [2, "207_2", 439, 139, 38, 27, 2], [2, "205_3", 86, 138, 54, 40, 2], [2, "206", 515, 435, 66, 40, 0], [2, "1151", 889, 163, 38, 33, 0], [2, "208_3", 703, 307, 78, 40, 2], [2, "205_3", 662, 335, 54, 40, 2], [2, "206", 705, 381, 66, 40, 2], [2, "208_3", 561, 508, 78, 40, 2], [2, "208_3", 748, 408, 78, 40, 0], [2, "208_3", 752, 442, 78, 40, 1], [2, "208_3", 776, 469, 78, 40, 0], [2, "214_3", 235, 301, 54, 40, 2], [2, "206", 224, 282, 66, 40, 2], [2, "1144", 149, 313, 114, 70, 0], [2, "208_3", 57, 327, 78, 40, 1], [2, "206", 99, 322, 66, 40, 2], [2, "1151", 146, 355, 38, 33, 0], [2, "208_3", 482, 20, 78, 40, 1], [2, "1151", 750, 282, 38, 33, 0], [2, "208_3", 529, 362, 78, 40, 2], [2, "313_2", 774, 288, 70, 44, 2], [2, "313_2", 767, 323, 70, 44, 2], [2, "313_2", 762, 365, 70, 44, 2], [2, "166_2", 755, 436, 30, 35, 0], [2, "166_2", 59, 319, 30, 35, 0], [2, "165_1", 253, 272, 42, 37, 2], [2, "1303_2", 811, 265, 34, 20, 0], [2, "1302_3", 106, 367, 40, 29, 0], [2, "1303_2", 128, 382, 34, 20, 0], [2, "1305_2", 150, 385, 20, 14, 0], [2, "955_4", 820, 269, 20, 18, 0], [2, "955_4", 219, 262, 20, 18, 0], [2, "1303_2", 507, 533, 34, 20, 0], [2, "955_4", 127, 246, 20, 18, 0], [2, "1303_2", 740, 345, 34, 20, 2], [2, "955_4", 706, 367, 20, 18, 0], [2, "426_2", 824, 638, 26, 22, 0], [2, "1301_2", 54, 101, 24, 49, 0], [2, "1151", 217, 620, 38, 33, 0], [2, "1303_2", 67, 133, 34, 20, 2], [2, "1153", 478, 150, 34, 54, 0], [2, "208_3", 863, 522, 78, 40, 0], [2, "207_2", 931, 542, 38, 27, 2], [2, "1150", 303, 110, 48, 85, 2], [2, "1149", 336, 135, 40, 82, 2], [2, "1149", 356, 138, 40, 82, 0], [2, "214_3", 337, 132, 54, 40, 0], [2, "206", 334, 107, 66, 40, 2], [2, "955_4", 335, 93, 20, 18, 0], [2, "216", 178, 21, 46, 46, 0], [2, "214_3", 432, 136, 54, 40, 2], [2, "213_3", 378, 138, 64, 45, 2], [2, "208_3", 408, 115, 78, 40, 2], [2, "206", 374, 123, 66, 40, 0], [2, "214_3", 206, 38, 54, 40, 0], [2, "214_3", 222, 92, 54, 40, 0], [2, "213_3", 263, 111, 64, 45, 0], [2, "208_3", 206, 16, 78, 40, 1], [2, "205_3", 235, 46, 54, 40, 2], [2, "208_3", 222, 76, 78, 40, 1], [2, "206", 265, 101, 66, 40, 2], [2, "166_2", 219, 72, 30, 35, 0], [2, "955_4", 290, 37, 20, 18, 0], [2, "205_3", 173, -5, 54, 40, 0], [2, "214_3", 772, 510, 54, 40, 0], [2, "213_3", 813, 529, 64, 45, 0], [2, "208_3", 772, 494, 78, 40, 1], [2, "206", 815, 519, 66, 40, 2], [2, "166_2", 821, 510, 30, 35, 0], [2, "213_3", 546, 144, 64, 45, 2], [2, "206", 544, 139, 66, 40, 0], [2, "1144", 563, 183, 114, 70, 2], [2, "206", 654, 208, 66, 40, 0], [2, "165_1", 651, 199, 42, 37, 0], [2, "166_2", 553, 135, 30, 35, 0], [2, "208_3", 202, 188, 78, 40, 3], [2, "208_3", 697, 233, 78, 40, 0], [2, "166_2", 711, 257, 30, 35, 0], [2, "1152", 594, 255, 38, 26, 0], [2, "1152", 546, 235, 38, 26, 0], [2, "1152", 562, 275, 38, 26, 0], [2, "1152", 502, 266, 38, 26, 0], [2, "216", 653, 643, 46, 46, 0], [2, "214_3", 654, 631, 54, 40, 2], [2, "208_3", 628, 617, 78, 40, 2], [2, "205_3", 625, 519, 54, 40, 2], [2, "166_2", 646, 507, 30, 35, 2], [2, "207_2", 659, 570, 38, 27, 2], [2, "205_3", 665, 586, 54, 40, 2], [2, "165_1", 677, 605, 42, 37, 2], [2, "205_3", 638, 653, 54, 40, 0], [2, "1153", 383, 160, 34, 54, 0], [2, "1154", 424, 168, 28, 51, 2], [2, "1152", 473, 293, 38, 26, 0], [2, "1152", 493, 326, 38, 26, 0], [2, "313_2", 422, 325, 70, 44, 0], [2, "313_2", 461, 347, 70, 44, 2], [2, "426_2", 483, 230, 26, 22, 0], [2, "205_3", 543, 339, 54, 40, 0], [2, "208_3", 512, 365, 78, 40, 1], [2, "166_2", 547, 323, 30, 35, 0], [2, "313_2", 693, 134, 70, 44, 0], [2, "214_3", 415, 286, 54, 40, 2], [2, "208_3", 389, 273, 78, 40, 2], [2, "165_1", 436, 261, 42, 37, 2], [2, "205_3", 384, 298, 54, 40, 0], [2, "1150", 668, 397, 48, 85, 0], [2, "1154", 660, 427, 28, 51, 2], [2, "1153", 682, 422, 34, 54, 0], [2, "426_2", 692, 464, 26, 22, 0], [2, "208_3", 882, 279, 78, 40, 0], [2, "208_3", 854, 244, 78, 40, 2], [2, "208_3", 319, 20, 78, 40, 2], [2, "206", 322, -12, 66, 40, 0], [2, "208_3", 434, 35, 78, 40, 1], [2, "208_3", 126, 184, 78, 40, 2], [2, "206", 648, 546, 66, 40, 2], [2, "1151", 659, 571, 38, 33, 2], [2, "208_3", 91, 176, 78, 40, 0], [2, "1149", -16, -23, 40, 82, 2], [2, "216", 81, -16, 46, 46, 0], [2, "208_3", 122, 550, 78, 40, 1], [2, "208_3", 344, 271, 78, 40, 2], [2, "1150", 58, 397, 48, 85, 0], [2, "208_3", 13, 392, 78, 40, 2], [2, "205_3", 48, 365, 54, 40, 2], [2, "1150", 119, 395, 48, 85, 2], [2, "214_3", 124, 419, 54, 40, 0], [2, "1150", 153, 438, 48, 85, 2], [2, "214_3", 167, 437, 54, 40, 0], [2, "214_3", 181, 470, 54, 40, 0], [2, "208_3", 180, 452, 78, 40, 1], [2, "208_3", 167, 488, 78, 40, 2], [2, "205_3", 133, 516, 54, 40, 2], [2, "1151", 214, 462, 38, 33, 0], [2, "1303_2", 204, 526, 34, 20, 2], [2, "206", 118, 389, 66, 40, 0], [2, "165_1", 115, 380, 42, 37, 0], [2, "208_3", 161, 414, 78, 40, 0], [2, "166_2", 175, 438, 30, 35, 0], [2, "213_3", -2, 454, 64, 45, 2], [2, "1150", -2, 453, 48, 85, 0], [2, "216", 19, 453, 46, 46, 2], [2, "208_3", -11, 430, 78, 40, 2], [2, "216", 128, 639, 46, 46, 0], [2, "208_3", 89, 563, 78, 40, 0], [2, "214_3", 83, 604, 54, 40, 0], [2, "213_3", 124, 623, 64, 45, 0], [2, "208_3", 83, 588, 78, 40, 1], [2, "206", 120, 616, 66, 40, 2], [2, "166_2", 132, 606, 30, 35, 0], [2, "313_2", 348, 424, 70, 44, 2], [2, "313_2", 305, 412, 70, 44, 2], [2, "313_2", 417, 360, 70, 44, 0], [2, "313_2", 309, 449, 70, 44, 0], [2, "208_3", 165, 636, 78, 40, 0], [2, "1244", 659, -43, 30, 67, 0], [2, "1141_1", 677, -23, 54, 67, 2], [2, "1254", 678, -30, 54, 32, 0], [2, "1244", 716, -13, 30, 67, 2], [2, "1244", 715, -63, 30, 67, 2], [2, "1141_1", 930, 61, 54, 67, 0], [2, "1141_1", 930, 17, 54, 67, 0], [2, "1141_1", 929, -26, 54, 67, 0], [2, "1141_1", 737, 5, 54, 67, 2], [2, "1141_1", 737, -39, 54, 67, 2], [2, "1141_1", 781, 27, 54, 67, 2], [2, "1141_1", 781, -17, 54, 67, 2], [2, "1141_1", 781, -60, 54, 67, 2], [2, "1141_1", 824, 49, 54, 67, 2], [2, "1141_1", 824, 5, 54, 67, 2], [2, "1141_1", 824, -38, 54, 67, 2], [2, "1141_1", 867, 70, 54, 67, 2], [2, "1141_1", 867, 26, 54, 67, 2], [2, "1141_1", 867, -17, 54, 67, 2], [2, "1141_1", 872, -59, 54, 67, 2], [2, "1249", 825, 17, 22, 53, 0], [2, "1249", 806, 23, 22, 53, 0], [2, "43_9", 802, 45, 82, 58, 0], [2, "955_5", 769, 94, 20, 18, 0], [2, "954_4", 890, 124, 24, 25, 0], [2, "1244", 911, 72, 30, 67, 2], [2, "1248", 806, -16, 22, 52, 0], [2, "1253", 757, -24, 22, 35, 2], [2, "1244", 911, 22, 30, 67, 2], [2, "1244", 910, -28, 30, 67, 2], [2, "1252", 678, 5, 96, 71, 2], [2, "1253", 927, 35, 22, 35, 0], [2, "958_1", 577, 10, 90, 68, 0], [2, "90_2", 934, 101, 28, 36, 0], [2, "700_2", 670, 53, 22, 48, 2], [2, "700_2", 693, 54, 22, 48, 0], [2, "181_3", 817, 27, 104, 100, 0], [2, "181_3", 767, -4, 104, 100, 0], [2, "1245", 793, -26, 76, 130, 2], [2, "1252", 798, 55, 96, 71, 2], [2, "700_2", 787, 114, 22, 48, 2], [2, "700_2", 810, 114, 22, 48, 0], [2, "1253", 880, 25, 22, 35, 2], [2, "313_2", 252, 7, 70, 44, 2], [2, "1301_2", 279, -10, 24, 49, 0], [2, "1151", 918, 327, 38, 33, 0], [2, "1302_3", 891, 339, 40, 29, 2], [2, "1303_2", 927, 327, 34, 20, 0], [2, "1151", 922, 336, 38, 33, 2], [2, "313_2", 825, 215, 70, 44, 2], [2, "1301_2", 852, 198, 24, 49, 0], [2, "313_2", 269, 326, 70, 44, 2], [2, "1301_2", 296, 309, 24, 49, 0], [2, "313_2", 570, 593, 70, 44, 2], [2, "1301_2", 597, 576, 24, 49, 0], [2, "1151", 218, 218, 38, 33, 0], [2, "1302_3", 191, 230, 40, 29, 2], [2, "1303_2", 227, 218, 34, 20, 0], [2, "1151", 222, 227, 38, 33, 2], [2, "1147", 760, 561, 50, 42, 2], [2, "426_2", 118, 44, 26, 22, 0], [2, "426_2", 58, 527, 26, 22, 0], [2, "1152", 17, 596, 38, 26, 0], [2, "1150", 13, -24, 48, 85, 0], [2, "313_2", 260, 614, 70, 44, 2], [2, "1301_2", 287, 597, 24, 49, 0], [2, "1302_3", 602, 245, 40, 29, 0], [2, "1151", 504, 260, 38, 33, 0], [2, "594", 533, 185, 52, 46, 2], [2, "598", 523, 357, 18, 22, 2], [2, "1302_3", 509, 328, 40, 29, 2], [2, "1303_2", 547, 231, 34, 20, 0], [2, "955_4", 556, 235, 20, 18, 0], [2, "955_4", 484, 291, 20, 18, 0], [2, "958_1", 853, 415, 90, 68, 0], [2, "1151", 51, 165, 38, 33, 0], [2, "1302_3", 477, 413, 40, 29, 2], [2, "1303_2", 513, 401, 34, 20, 0], [2, "1151", 508, 410, 38, 33, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, 30, 31, 41, 40, -1, -1, -1, -1, -1, -1, 34, -1, 18, 19, 20, 25, 24, -1, -1, -1, -1, 30, 31, 41, 40, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 25, 24, -1, -1, 38, 37, -1, -1, -1, -1, -1, -1, -1, -1, 30, 31, 37, 37, 36, 37, -1, -1, -1, -1, -1, 38, 37, 41, 40, 34, 34, -1, 34, 40, 40, -1, -1, -1, 31, 37, 37, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, -1, -1, -1, -1, 50, 49, 49, 49, 48, -1, -1, -1, 38, 37, 41, 40, -1, 31, 37, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 61, 65, 64, 63, -1, 18, 19, 25, 24, 38, 37, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 22, 49, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, 60, 62, 61, 60, -1, 30, 31, 37, 36, -1, -1, 38, 37, 41, 40, -1, 26, 25, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 22, 55, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 19, 24, 30, 31, 41, 40, 28, -1, -1, -1, -1, 34, -1, -1, -1, -1, 38, 37, 41, 22, 22, 22, -1, -1, 22, 22, 63, -1, -1, 40, 40, 34, 35, -1, -1, -1, -1, -1, -1, 18, 19, 25, 19, 24, 38, 37, 41, -1, -1, -1, -1, -1, 25, 24, -1, -1, -1, -1, 38, 37, 41, 40, 40, -1, -1, 54, 60, 41, 40, 34, 35, 41, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 25, 24, 38, -1, -1, -1, -1, -1, 28, 23, -1, 37, -1, -1, -1, -1, 38, 37, 37, -1, -1, -1, -1, 38, 37, 41, 40, 40, 40, 28, 28, -1, -1, 50, 43, 43, 44, -1, -1, 30, 37, 36, 34, 50, 49, 48, -1, -1, -1, -1, -1, -1, 38, 31, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 41, 40, 39, -1, 54, 65, 46, 47, 49, 48, 60, -1, -1, 34, 53, 64, 63, -1, -1, 42, 43, 44, -1, -1, -1, -1, 19, 19, 20, -1, 50, 49, 48, -1, -1, -1, -1, -1, 38, 37, 41, 40, 36, -1, -1, 54, 55, 65, 52, 51, -1, -1, 34, 35, 55, 61, 60, -1, -1, 45, 46, 47, 48, -1, 42, 43, 22, 22, 23, 43, 53, 52, 51, -1, -1, -1, -1, -1, -1, -1, 30, 41, 40, -1, 50, -1, -1, 54, 61, 60, -1, 34, 34, 35, -1, -1, -1, -1, -1, 54, 55, 61, 60, 42, 45, 34, 34, 35, 31, -1, -1, 64, 19, 20, -1, -1, -1, -1, -1, -1, -1, 38, 41, 40, 53, 46, -1, -1, -1, 42, 43, 34, 31, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 34, 35, 31, 32, -1, -1, -1, 61, 22, 23, 19, 20, -1, -1, -1, -1, -1, -1, 38, 37, 64, 64, -1, 60, -1, 54, 61, 60, -1, -1, 18, 19, 20, -1, -1, -1, -1, -1, -1, 57, 34, 35, -1, -1, -1, 42, 43, 44, -1, -1, 22, 23, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 29, -1, 29, 28, 27, -1, -1, 34, 35, 34, 35, -1, 31, 32, -1, -1, -1, 54, 55, 56, -1, -1, -1, -1, 22, 23, -1, -1, -1, -1, 54, 65, -1, -1, -1, -1, 18, 19, 29, 40, 40, 34, 37, 36, 34, 28, 28, 31, 32, 31, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 60, -1, -1, 34, 35, 37, 41, 41, 40, 27, 34, 34, 28, 34, 34, 40, 39, -1, -1, -1, 18, 19, -1, -1, 25, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 31, 32, -1, 38, 37, 41, 34, 35, 32, 28, 41, 40, 37, 36, 18, 19, 20, 21, 22, 34, -1, -1, 34, 32, -1, -1, -1, -1, -1, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, -1, 18, 19, 29, 31, 32, 28, 28, 35, 41, 40, -1, 21, 22, 23, 30, 34, 34, 18, 35, 41, 23, 25, 24, -1, -1, -1, 21, 22, 34, 34, -1, -1, -1, 41, 40, 49, 48, 37, 36, -1, 30, 31, -1, -1, 34, 35, 31, 32, 38, 41, 40, -1, -1, -1, -1, 30, 31, 36, 18, 29, 34, 37, 36, -1, -1, -1, 30, 41, 34, -1, -1, -1, -1, 38, 41, 40, 63, -1, -1, 42, 43, 49, 48, 34, 35, 32, -1, -1, 45, 38, 37, -1, -1, -1, 42, 43, 44, -1, 30, 37, 36, -1, -1, -1, -1, 26, -1, -1, 24, -1, -1, -1, 43, 53, 33, 40, 60, -1, -1, 54, 55, 61, 60, 40, 39, -1, -1, 26, 25, 46, -1, -1, -1, -1, 45, 46, 47, 48, -1, -1, -1, -1, -1, -1, 18, -1, 28, 27, 27, 42, 43, 25, 24, 59, 38, 37, 40, -1, -1, 23, 29, 28, -1, 37, 36, -1, -1, 29, 28, -1, -1, -1, -1, -1, 54, 55, 61, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 65, 28, 27, 60, -1, 38, 37, 41, 40, 34, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, -1, -1, -1, -1, -1, 38, 37, 41, 40, -1, -1, -1, -1, -1, 18, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 49, 49, 48, -1, -1, -1, -1, -1, 18, 19, 26, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, 38, 41, 40, -1, -1, -1, -1, 21, 22, 40, -1, 25, 25, 25, 24, 42, 43, 43, 53, 46, 52, 47, 49, 48, -1, -1, -1, 30, 31, 41, 40, 40, 25, 25, 24, -1, -1, -1, -1, -1, -1, 38, 41, 40, -1, -1, -1, 38, 41, 40, 55, 28, 28, 28, 27, 54, 55, 61, 61, 61, 61, 61, 61, 60, -1, -1, -1, -1, -1, 38, 37, 37, 28, 28, 27, -1, -1, -1, -1, -1, -1, -1, 38, 37]}, {"type": 2, "data": [0, 1, 2, 0, 1, 2, 0, 1, 0, 12, 13, 14, 9, 10, 11, 1, 2, 1, 2, 1, 2, 4, 5, 14, 12, 13, 14, 10, 11, 1, 2, 15, 16, 17, 11, 0, 1, 2, -1, -1, 3, 4, 5, 3, 4, 5, 3, 4, 3, 15, 16, 17, 10, 11, 14, 4, 5, 4, 5, 4, 5, 15, 16, 17, 15, 16, 17, 13, 14, 17, 17, 2, 12, 13, 14, 0, 1, 2, -1, -1, 1, 0, 1, 2, 7, 8, 6, 7, 6, 7, 8, 9, 10, 11, 17, 15, 15, 16, 17, 14, 17, 10, 9, 10, 11, 9, 15, 16, 17, 10, 9, 10, 15, 16, 17, 10, 11, 9, 10, 11, 0, 1, 1, 2, 1, 2, 0, 1, 2, 0, 1, 12, 13, 14, 11, 11, 9, 15, 16, 17, 12, 13, 12, 9, 10, 11, 13, 9, 10, 11, 12, 13, 14, 10, 11, 13, 14, 12, 13, 14, 10, 4, 4, 1, 2, 5, 3, 4, 5, 0, 1, 15, 16, 17, 14, 14, 12, 13, 9, 10, 11, 16, 15, 12, 13, 14, 10, 11, 13, 14, 15, 16, 17, 13, 14, 16, 17, 15, 16, 17, 13, 14, 13, 4, 5, 8, 6, 7, 8, 3, 4, 0, 1, 2, 17, 17, 15, 16, 9, 10, 11, 0, 1, 15, 16, 17, 13, 14, 16, 17, 11, 9, 15, 16, 17, 10, 11, 11, 12, 13, 16, 15, 16, 17, 0, 4, 5, 4, 5, 6, 7, 3, 4, 5, 1, 1, 2, 9, 12, 13, 14, 0, 1, 2, 3, 15, 16, 17, 12, 13, 9, 12, 13, 14, 15, 16, 15, 16, 15, 16, 15, 15, 16, 17, 11, 7, 1, 2, 8, 5, 5, 6, 7, 8, 2, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 10, 11, 11, 15, 16, 12, 15, 16, 17, 12, 9, 10, 11, 10, 10, 12, 13, 12, 13, 14, 1, 2, 14, 9, 9, 10, 3, 3, 4, 5, 3, 4, 5, 3, 4, 5, 6, 0, 1, 2, 2, 10, 11, 10, 11, 11, 11, 11, 12, 15, 9, 10, 11, 13, 13, 11, 16, 15, 16, 17, 15, 16, 17, 10, 11, 10, 11, 6, 7, 8, 6, 0, 1, 2, 0, 1, 2, 3, 4, 5, 0, 1, 3, 4, 10, 11, 14, 14, 9, 10, 11, 13, 14, 9, 1, 12, 13, 12, 13, 14, 13, 14, 11, 10, 11, 11, 14, 0, 1, 2, 0, 1, 2, 0, 1, 2, 5, 0, 1, 2, 0, 1, 6, 7, 8, 10, 11, 11, 12, 13, 9, 10, 11, 12, 4, 15, 16, 15, 16, 17, 10, 11, 14, 9, 10, 11, 17, 3, 4, 5, 3, 4, 5, 3, 4, 5, 8, 1, 2, 5, 0, 0, 1, 0, 3, 4, 5, 9, 9, 10, 11, 13, 14, 1, 4, 9, 10, 11, 10, 11, 13, 9, 10, 11, 9, 10, 11, 14, 9, 10, 11, 10, 11, 6, 7, 8, 3, 4, 0, 1, 2, 0, 1, 3, 6, 7, 8, 10, 11, 9, 10, 11, 9, 4, 5, 12, 13, 14, 13, 14, 16, 12, 13, 14, 12, 13, 9, 10, 12, 13, 14, 13, 14, 10, 2, 2, 6, 7, 3, 4, 5, 3, 4, 6, 7, 8, 10, 11, 14, 12, 13, 14, 12, 13, 14, 15, 16, 17, 13, 14, 9, 10, 11, 17, 15, 16, 12, 13, 15, 16, 17, 13, 14, 13, 14, 5, 6, 7, 6, 7, 8, 6, 7, 8, 11, 12, 13, 14, 17, 15, 16, 17, 15, 16, 17, 15, 16, 9, 10, 11, 11, 13, 14, 14, 17, 17, 15, 16, 17, 9, 10, 11, 9, 10, 11, 9, 10, 9, 10, 11, 7, 3, 4, 5, 14, 15, 16, 9, 9, 10, 11, 9, 10, 11, 9, 15, 16, 12, 13, 14, 14, 16, 17, 17, 11, 16, 17, 16, 17, 12, 13, 14, 12, 13, 14, 12, 13, 12, 13, 14, 1, 6, 7, 8, 9, 10, 16, 12, 12, 13, 14, 12, 13, 14, 12, 9, 10, 15, 0, 1, 2, 17, 17, 13, 14, 17, 9, 10, 11, 15, 16, 17, 15, 16, 17, 15, 9, 15, 16, 17, 4, 0, 1, 2, 11, 11, 16, 15, 15, 9, 10, 11, 11, 9, 10, 12, 13, 14, 3, 4, 5, 14, 16, 17, 13, 14, 12, 13, 10, 12, 13, 14, 12, 13, 9, 10, 11, 11, 16, 2, 0, 3, 4, 5, 14, 14, 10, 10, 11, 12, 13, 14, 14, 12, 13, 15, 0, 1, 2, 0, 1, 0, 1, 2, 13, 14, 15, 16, 13, 15, 16, 17, 15, 16, 12, 13, 14, 14, 17, 5, 3, 6, 0, 1, 2, 1, 2, 13, 14, 15, 16, 17, 17, 15, 16, 12, 3, 4, 5, 3, 4, 3, 4, 5, 9, 10, 11, 9, 10, 11, 9, 12, 13, 14, 15, 16, 17, 17, 7, 8, 6, 7, 3, 4, 5, 4, 5, 5, 12, 9, 9, 10, 11, 9, 10, 4, 6, 7, 0, 1, 2, 6, 7, 8, 12, 13, 14, 12, 13, 14, 12, 15, 16, 17, 9, 10, 11, 5, 7, 8, 4, 5, 6, 7, 8, 1, 2, 1, 2, 12, 12, 13, 14, 12, 13, 0, 1, 2, 3, 4, 5, 17, 16, 17, 9, 10, 11, 9, 10, 11, 9, 10, 9, 10, 11, 13, 12, 12, 12, 11, 12, 13, 14, 1, 3, 4, 5, 4, 5, 15, 15, 16, 17, 15, 16, 1, 2, 5, 6, 7, 8, 16, 17, 9, 12, 13, 14, 12, 13, 14, 12, 13, 9, 10, 11, 16, 17, 12, 9, 10, 15, 16, 17, 15, 6, 7, 8, 7, 8, 3, 4, 5, 7, 8, -1, 2, 5, 0, 1, 2, 17, 11, 9, 12, 15, 16, 17, 15, 16, 17, 15, 16, 12, 13, 14, 10, 11, 10, 12, 13, 9, 10, 11, 17, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 5, 8, 3, 4, 5, 10, 11, 12, 9, 10, 11, 11, 14, 12, 9, 10, 11, 15, 16, 17, 13, 14, 13, 9, 10, 9, 10, 11, 11, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 0, 1, 6, 0, 1, 2, 14, 15, 9, 10, 11, 14, 17, 15, 12, 13, 14, 12, 13, 14, 16, 17, 16, 12, 13, 12, 13, 14, 11, 6, 7, 8, 0, 1, 2, 0, 1, 0, 1, 2, 3, 4, 5, 3, 4, 5, 9, 10, 12, 13, 14, 17, 15, 16, 15, 16, 17, 15, 16, 17, 9, 10, 11, 15, 16, 15, 16, 17, 8, 7, 0, 1, 2, 4, 5, 3, 4, 3, 4, 5]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}