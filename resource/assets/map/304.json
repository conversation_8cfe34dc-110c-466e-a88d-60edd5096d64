{"mW": 912, "mH": 720, "tW": 24, "tH": 24, "tiles": [["315_1", 0, 3, 3], ["369_1", 0, 3, 3], ["369_1", 2, 3, 3], ["369_1", 1, 3, 3], ["369_1", 3, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["451", 0, 1, 1], ["450", 0, 1, 1], ["415_1", 0, 4, 1], ["415_1", 2, 4, 1], ["415_1", 3, 4, 1], ["416_1", 0, 4, 1], ["416_1", 2, 4, 1], ["416_1", 1, 4, 1], ["416_1", 3, 4, 1], ["111", 0, 3, 2], ["111", 2, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["380", 0, 3, 2], ["380", 2, 3, 2], ["380", 1, 3, 2], ["380", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "2_1", 839, 234, 90, 66, 0], [2, "1114", 168, 22, 46, 45, 0], [2, "2_1", 373, -34, 90, 66, 2], [2, "2_1", -20, 669, 90, 66, 2], [2, "1104", 416, 622, 138, 132, 2], [2, "1104", 523, 597, 138, 132, 0], [2, "1104", 612, 649, 138, 132, 0], [2, "1124", 147, 21, 26, 26, 0], [2, "1125", 162, -14, 62, 46, 0], [2, "1126", 209, -30, 44, 41, 0], [2, "1121", 34, 440, 32, 44, 2], [2, "1122", 58, 433, 52, 77, 2], [2, "1117", 96, 474, 50, 29, 2], [2, "429", 740, 677, 64, 63, 0], [2, "1104", 831, 608, 138, 132, 0], [2, "2_1", -55, 248, 90, 66, 2], [2, "2_1", 14, 260, 90, 66, 2], [2, "1125", 839, 323, 62, 46, 2], [2, "1126", 881, 341, 44, 41, 2], [2, "1125", 904, 356, 62, 46, 2]]}, {"type": 4, "obj": [[2, "1101", 252, -24, 64, 55, 0], [2, "1101", 242, -18, 64, 55, 0], [2, "1113", 263, -38, 58, 76, 0], [2, "1101", 232, -13, 64, 55, 0], [4, 10, 468, 47, 0, 4001], [2, "1101", 223, -7, 64, 55, 0], [2, "1101", 213, -2, 64, 55, 0], [2, "1101", 203, 3, 64, 55, 0], [2, "1101", 193, 8, 64, 55, 0], [2, "1101", 184, 13, 64, 55, 0], [2, "1104", -3, -62, 138, 132, 0], [2, "1113", 183, 2, 58, 76, 0], [2, "1103", 437, 63, 44, 41, 0], [2, "125", 424, 47, 18, 70, 0], [2, "125", 472, 73, 18, 70, 0], [4, 0, 752, 144, 1, 4010], [4, 1, 719, 150, 0, 4010], [4, 6, 143, 158, 0, 4019], [2, "422", 571, 237, 16, 14, 0], [4, 9, 818, 264, 0, 4019], [4, 5, 573, 287, 0, 4021], [4, 4, 160, 324, 1, 4011], [4, 3, 135, 352, 0, 4018], [4, 8, 764, 387, 0, 4011], [2, "331", -26, 285, 104, 108, 2], [2, "1101", 814, 353, 64, 55, 2], [2, "1113", 811, 338, 58, 76, 2], [2, "1101", 824, 359, 64, 55, 2], [2, "1101", 834, 365, 64, 55, 2], [2, "1101", 845, 370, 64, 55, 2], [2, "1101", 855, 375, 64, 55, 2], [2, "1101", 865, 380, 64, 55, 2], [2, "1101", 874, 385, 64, 55, 2], [2, "1101", 884, 391, 64, 55, 2], [2, "1104", 437, 318, 138, 132, 2], [2, "1113", 889, 374, 58, 76, 2], [4, 2, 74, 453, 0, 4005], [2, "47_3", 501, 402, 54, 63, 0], [2, "1110", 748, 385, 78, 101, 2], [2, "1120", -19, 435, 36, 76, 2], [2, "1120", 10, 459, 36, 76, 2], [2, "1119", 37, 467, 30, 76, 2], [4, 7, 861, 551, 0, 4005], [2, "1118", 108, 491, 28, 87, 2], [2, "1120", 126, 520, 36, 76, 2], [4, 11, 297, 616, 0, 4010], [2, "1120", 158, 545, 36, 76, 2], [2, "1104", 117, 504, 138, 132, 2], [2, "328", 654, 628, 32, 29, 0], [2, "1120", 200, 585, 36, 76, 2], [2, "1120", 211, 628, 36, 76, 2], [2, "429", 220, 668, 64, 63, 0], [2, "1104", 46, 630, 138, 132, 2]]}, {"type": 3, "obj": [[2, "1102", 805, 454, 114, 63, 0], [2, "1115", 822, 396, 14, 70, 2], [2, "1112", 861, 416, 46, 83, 0], [2, "1115", 902, 435, 14, 70, 2], [2, "1112", 833, 404, 46, 83, 0], [2, "1116", 841, 425, 34, 60, 2], [2, "8", 869, 496, 38, 29, 0], [2, "329", -3, 14, 42, 37, 0], [2, "174_2", 799, 294, 68, 33, 3], [2, "63_1", 796, 282, 16, 31, 0], [2, "64", 784, 300, 14, 15, 0], [2, "62_1", 770, 297, 16, 27, 0], [2, "327", 789, 302, 30, 22, 2], [2, "422", 704, 309, 16, 14, 0], [2, "22", 714, 302, 62, 38, 2], [2, "325", 309, 78, 50, 37, 0], [2, "48", 297, 57, 52, 38, 2], [2, "325", 258, 621, 50, 37, 0], [2, "1112", 173, 43, 46, 83, 0], [2, "1112", 253, 34, 46, 83, 2], [2, "1112", 227, 48, 46, 83, 2], [2, "325", 242, 239, 50, 37, 0], [2, "1102", 209, 86, 114, 63, 2], [2, "1102", 136, 87, 114, 63, 0], [2, "22", 820, 558, 62, 38, 0], [2, "329", 329, 289, 42, 37, 0], [2, "328", 305, 294, 32, 29, 0], [2, "422", 323, 307, 16, 14, 0], [2, "422", 751, 326, 16, 14, 0], [2, "325", -21, 47, 50, 37, 2], [2, "253", 484, 210, 92, 53, 0], [2, "253", 522, 218, 92, 53, 0], [2, "436", 595, 34, 34, 28, 2], [2, "325", 106, 695, 50, 37, 2], [2, "325", 697, 89, 50, 37, 2], [2, "439", 531, -6, 64, 42, 2], [2, "174_2", 697, 571, 68, 33, 1], [2, "325", 743, 86, 50, 37, 2], [2, "325", 189, 363, 50, 37, 0], [2, "328", 391, 59, 32, 29, 0], [2, "392", 507, 26, 118, 69, 2], [2, "391", 460, 61, 86, 55, 0], [2, "325", 505, 2, 50, 37, 2], [2, "328", 542, 6, 32, 29, 0], [2, "329", 514, -25, 42, 37, 0], [2, "328", 509, 14, 32, 29, 0], [2, "329", 125, 671, 42, 37, 0], [2, "325", 73, 359, 50, 37, 0], [2, "328", -1, 373, 32, 29, 2], [2, "21", 452, 93, 28, 24, 0], [2, "28", 638, 108, 12, 27, 0], [2, "439", 619, 135, 64, 42, 0], [2, "437", 602, 165, 20, 19, 0], [2, "436", 491, 103, 34, 28, 2], [2, "439", 506, 129, 64, 42, 2], [2, "441", 563, 158, 28, 21, 2], [2, "437", 585, 166, 20, 19, 0], [2, "437", 754, 64, 20, 19, 0], [2, "441", 794, 64, 28, 21, 2], [2, "21", 731, 88, 28, 24, 0], [2, "325", 676, 124, 50, 37, 2], [2, "329", 865, 129, 42, 37, 2], [2, "325", 718, 121, 50, 37, 2], [2, "463", 681, 136, 16, 13, 0], [2, "463", 753, 86, 16, 13, 0], [2, "463", 747, 91, 16, 13, 0], [2, "425", 530, 93, 30, 36, 0], [2, "21", 674, 124, 28, 24, 0], [2, "325", 638, 154, 50, 37, 2], [2, "437", 535, 162, 20, 19, 0], [2, "21", 121, 366, 28, 24, 0], [2, "328", 228, 406, 32, 29, 0], [2, "86", 124, 59, 50, 49, 0], [2, "8", 132, 96, 38, 29, 0], [2, "12", 137, 84, 26, 28, 0], [2, "325", 772, 65, 50, 37, 2], [2, "328", 773, 56, 32, 29, 0], [2, "328", -7, 237, 32, 29, 0], [2, "437", 575, 165, 20, 19, 0], [2, "437", 527, 159, 20, 19, 2], [2, "421", 523, 104, 14, 11, 2], [2, "57_1", 699, 48, 72, 44, 0], [2, "56_1", 687, 54, 76, 47, 0], [2, "57_1", 682, 62, 72, 44, 0], [2, "56_1", 670, 68, 76, 47, 0], [2, "57_1", 661, 77, 72, 44, 0], [2, "57_1", 653, 83, 72, 44, 0], [2, "56_1", 647, 87, 76, 47, 0], [2, "54_1", 624, 92, 86, 53, 0], [2, "63_1", 636, 80, 16, 31, 0], [2, "62_1", 702, 33, 16, 27, 0], [2, "420", 694, 135, 16, 13, 0], [2, "422", 732, 101, 16, 14, 0], [2, "420", 666, 140, 16, 13, 0], [2, "326", 534, 28, 18, 14, 0], [2, "327", 306, 589, 30, 22, 0], [2, "327", 137, 699, 30, 22, 2], [2, "328", 35, 413, 32, 29, 0], [2, "329", -14, 337, 42, 37, 0], [2, "327", 755, 61, 30, 22, 2], [2, "420", 119, 124, 16, 13, 0], [2, "422", 142, 110, 16, 14, 0], [2, "326", 195, 699, 18, 14, 0], [2, "112", 636, 132, 16, 11, 0], [2, "174_2", 557, 253, 68, 33, 1], [2, "174_2", 434, 14, 68, 33, 3], [2, "420", 789, 90, 16, 13, 0], [2, "174_2", 61, 586, 68, 33, 3], [2, "422", 653, 165, 16, 14, 0], [2, "326", 577, 245, 18, 14, 0], [2, "420", 589, 252, 16, 13, 0], [2, "327", 501, 237, 30, 22, 2], [2, "21", 170, 679, 28, 24, 0], [2, "325", 317, 254, 50, 37, 2], [2, "21", 555, 240, 28, 24, 2], [2, "64", 305, 291, 14, 15, 0], [2, "63_1", 315, 275, 16, 31, 0], [2, "422", 315, 296, 16, 14, 0], [2, "62_1", 288, 294, 16, 27, 0], [2, "422", 286, 309, 16, 14, 0], [2, "326", 298, 312, 18, 14, 0], [2, "64", 888, 584, 14, 15, 2], [2, "62_1", 899, 582, 16, 27, 0], [2, "329", 251, 678, 42, 37, 0], [2, "174_2", 214, 649, 68, 33, 3], [2, "64", 293, 677, 14, 15, 2], [2, "63_1", 276, 665, 16, 31, 0], [2, "62_1", 304, 675, 16, 27, 0], [2, "327", 269, 685, 30, 22, 2], [2, "328", 304, 683, 32, 29, 2], [2, "64", 302, 700, 14, 15, 0], [2, "63_1", 313, 684, 16, 31, 0], [2, "422", 317, 704, 16, 14, 0], [2, "62_1", 286, 703, 16, 27, 0], [2, "64", 402, 697, 14, 15, 0], [2, "63_1", 413, 681, 16, 31, 0], [2, "422", 413, 702, 16, 14, 0], [2, "62_1", 386, 700, 16, 27, 0], [2, "328", 46, 68, 32, 29, 0], [2, "65", 352, 16, 12, 6, 2], [2, "64", 156, 385, 14, 15, 2], [2, "63_1", 139, 373, 16, 31, 0], [2, "64", 127, 391, 14, 15, 0], [2, "62_1", 167, 383, 16, 27, 0], [2, "62_1", 113, 388, 16, 27, 0], [2, "327", 132, 393, 30, 22, 2], [2, "422", 115, 404, 16, 14, 0], [2, "328", 172, 393, 32, 29, 2], [2, "174_2", 332, 556, 68, 33, 3], [2, "62_1", 57, 87, 16, 27, 0], [2, "62_1", 32, 76, 16, 27, 0], [2, "327", 48, 108, 30, 22, 2], [2, "327", 19, 94, 30, 22, 2], [2, "420", 603, 235, 16, 13, 0], [2, "420", 301, 248, 16, 13, 0], [2, "327", 653, 130, 30, 22, 0], [2, "422", 296, 637, 16, 14, 0], [2, "422", 191, 684, 16, 14, 0], [2, "422", 13, 68, 16, 14, 0], [2, "309", 555, 419, 46, 33, 2], [2, "422", 580, 411, 16, 14, 2], [2, "174_2", 521, 440, 68, 33, 1], [2, "422", 562, 438, 16, 14, 2], [2, "21", 710, 629, 28, 24, 0], [2, "326", 479, 443, 18, 14, 0], [2, "420", 560, 471, 16, 13, 0], [2, "420", 493, 447, 16, 13, 0], [2, "174_2", 479, 476, 68, 33, 1], [2, "327", 512, 466, 30, 22, 2], [2, "22", 256, 249, 62, 38, 0], [2, "422", 336, 259, 16, 14, 2], [2, "329", 827, 689, 42, 37, 2], [2, "328", 425, 695, 32, 29, 2], [2, "420", 696, 615, 16, 13, 0], [2, "328", 731, 442, 32, 29, 2], [2, "1115", 161, 36, 14, 70, 0], [2, "1115", 218, 62, 14, 70, 0], [2, "1115", 299, 28, 14, 70, 0], [2, "1109", 203, 104, 18, 21, 0], [2, "328", 226, 140, 32, 29, 0], [2, "1116", 260, 57, 34, 60, 0], [2, "1111", 237, 75, 30, 40, 0], [2, "1106", 222, 70, 22, 41, 0], [2, "1108", 186, 119, 26, 31, 0], [2, "1107", 136, 122, 24, 15, 0], [2, "1100", 125, 51, 90, 53, 0], [2, "125", 164, 86, 18, 70, 0], [2, "125", 117, 60, 18, 70, 0], [2, "420", 129, 320, 16, 13, 0], [2, "328", 93, -4, 32, 29, 0], [2, "422", 830, 521, 16, 14, 0], [2, "327", 691, 279, 30, 22, 2], [2, "327", 726, 637, 30, 22, 2], [2, "422", 132, 252, 16, 14, 0], [2, "328", 473, 92, 32, 29, 0], [2, "327", 467, 177, 30, 22, 2], [2, "328", 533, 224, 32, 29, 0], [2, "326", 496, 130, 18, 14, 0], [2, "63_1", -4, 365, 16, 31, 0], [2, "62_1", 37, 391, 16, 27, 0], [2, "422", 77, 417, 16, 14, 0], [2, "329", 71, 388, 42, 37, 0], [2, "64", 52, 404, 14, 15, 2], [2, "65", 23, 391, 12, 6, 0], [2, "64", 8, 380, 14, 15, 2], [2, "326", 63, 425, 18, 14, 0], [2, "325", 499, 396, 50, 37, 0], [2, "65", 520, 267, 12, 6, 0], [2, "64", 492, 260, 14, 15, 2], [2, "63_1", 480, 249, 16, 31, 0], [2, "62_1", 503, 258, 16, 27, 0], [2, "327", 473, 269, 30, 22, 2], [2, "62_1", 527, 247, 16, 27, 0], [2, "437", 859, 83, 20, 19, 0], [2, "441", 820, 76, 28, 21, 2], [2, "437", 842, 84, 20, 19, 0], [2, "437", 832, 83, 20, 19, 0], [2, "1104", 758, -32, 138, 132, 0], [2, "1104", 822, 52, 138, 132, 2], [2, "331", 418, -51, 104, 108, 2], [2, "1105", 807, 160, 104, 75, 2], [2, "329", 877, 592, 42, 37, 2], [2, "328", 99, 95, 32, 29, 0], [2, "422", 109, 69, 16, 14, 0], [2, "64", 98, 92, 14, 15, 0], [2, "63_1", 109, 76, 16, 31, 0], [2, "422", 109, 97, 16, 14, 0], [2, "62_1", 82, 95, 16, 27, 0], [2, "422", 80, 110, 16, 14, 0], [2, "326", 92, 113, 18, 14, 0], [2, "64", 71, 100, 14, 15, 2], [2, "21", 454, 144, 28, 24, 0], [2, "325", 788, 354, 50, 37, 0], [2, "174_2", 718, 361, 68, 33, 1], [2, "1109", 564, 438, 18, 21, 0], [2, "331", 863, 264, 104, 108, 0], [2, "327", 204, 702, 30, 22, 0], [2, "1106", 300, 34, 22, 41, 0], [2, "113", 276, 230, 26, 33, 0], [2, "1104", 503, 61, 138, 132, 0], [2, "327", 347, -9, 30, 22, 0], [2, "327", 97, 355, 30, 22, 0], [2, "327", 496, 38, 30, 22, 0], [2, "326", 135, 310, 18, 14, 0], [2, "429", 870, 511, 64, 63, 0], [2, "326", 0, 528, 18, 14, 0], [2, "326", 847, 522, 18, 14, 0], [2, "328", 820, 517, 32, 29, 0], [2, "325", 409, 117, 50, 37, 2], [2, "327", 191, 143, 30, 22, 2], [2, "422", 856, 347, 16, 14, 0], [2, "420", 888, 361, 16, 13, 0], [2, "420", 68, 54, 16, 13, 0], [2, "420", 581, 451, 16, 13, 0], [2, "420", 177, 635, 16, 13, 2], [2, "422", 64, 522, 16, 14, 0], [2, "64", 518, 259, 14, 15, 0], [2, "328", 503, 266, 32, 29, 2], [2, "62_1", 18, 174, 16, 27, 0], [2, "327", 19, 192, 30, 22, 2], [2, "64", 9, 187, 14, 15, 0], [2, "62_1", -5, 184, 16, 27, 0], [2, "422", -3, 202, 16, 14, 0], [2, "329", -6, 205, 42, 37, 0], [2, "1111", 871, 446, 30, 40, 2], [2, "1107", 877, 493, 24, 15, 0], [2, "429", -19, 388, 64, 63, 0], [2, "328", 886, 213, 32, 29, 0], [2, "420", 819, 581, 16, 13, 2], [2, "63_1", 880, 571, 16, 31, 0], [2, "1110", 565, 125, 78, 101, 0], [2, "62_1", 65, 397, 16, 27, 0], [2, "422", 350, 83, 16, 14, 2], [2, "1110", -13, 73, 78, 101, 2], [2, "62_1", 338, 2, 16, 27, 0], [2, "63_1", 315, -14, 16, 31, 2], [2, "64", 327, 1, 14, 15, 2], [2, "62_1", 364, 4, 16, 27, 0], [2, "420", 376, 25, 16, 13, 0], [2, "327", 310, 13, 30, 22, 2], [2, "328", 290, -2, 32, 29, 0], [2, "420", 350, 23, 16, 13, 0], [2, "422", 338, 26, 16, 14, 0], [2, "1109", 386, 23, 18, 21, 0], [2, "420", 478, 154, 16, 13, 0], [2, "1107", 318, 63, 24, 15, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 50, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 65, 64, 63, 57, 58, 63, 60, 61, 62, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, -113, -112, -111, -1, -1, -1, -1, -1, -1, 46, 47, -1, -113, -112, -1, 57, 58, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, -1, -1, -1, -1, -1, -1, -1, 57, 58, 59, -101, -100, -1, -1, -1, -1, -1, -1, -1, 79, 80, 81, -110, -106, -108, -106, -107, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, -1, -1, -1, -1, -1, -1, -1, 85, 52, 84, 68, 55, -1, -1, -1, -1, 61, 69, 70, 71, -101, -100, -99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 69, 69, 62, 64, 64, 63, 57, 58, -1, 52, 51, -1, 76, 75, 74, 55, 68, -1, 61, 69, 70, 71, 127, 126, 125, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 57, -1, 69, 70, 71, -1, -1, -1, -1, -1, -1, 61, 62, 63, -1, -1, -1, 76, 75, 74, 69, 70, 71, -1, -1, -117, -118, -119, 119, 120, 125, -1, -1, -1, -1, -1, -1, -1, -1, 69, 70, 71, -1, -1, -1, -1, -1, 45, 46, 58, 59, -1, -1, -105, -106, -107, -1, 76, 71, -1, -1, -1, -1, -1, -1, 119, -126, 123, 124, 125, -1, 53, 52, 51, -120, -124, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, -92, -1, -113, -112, -102, -103, -108, -106, -107, -1, -1, -1, -1, -1, -1, -1, -125, -124, -114, -115, 124, 121, 57, 58, 68, 52, 51, 119, 120, 121, 127, 120, 126, 125, 45, 46, -1, 61, -112, -107, -110, -109, -103, -103, -103, -103, -104, -1, -1, -1, -1, -1, -113, -106, -107, -1, -117, -114, 123, 124, 126, 121, 65, 64, 63, -126, -121, 124, -126, 123, -115, -116, 57, 58, 68, 67, -100, -99, -98, -103, -103, -103, -103, -103, -108, -107, 53, 52, -1, -113, -102, -103, -108, -111, -1, -122, 53, 45, 52, 51, -1, -125, -118, -118, -118, -118, -118, -118, -118, -119, -1, -1, -1, 64, -1, -1, -1, -90, -91, -103, -103, -103, -103, -108, -107, -1, -1, -110, -109, -96, -94, -95, -1, -1, 65, 64, 64, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -126, -1, -1, -1, -1, -101, -100, -100, -90, -91, -109, -104, -1, 52, 46, -94, -95, -1, 119, 126, 125, -1, 119, 120, 125, -1, -1, -1, -1, -1, -1, -1, -1, 45, 52, 51, -1, -1, 59, -1, -1, -1, -1, -1, -1, -1, -93, -94, -100, -99, -1, 57, 64, 63, -1, -1, 122, 123, -116, -1, 122, 123, 124, 125, -1, -113, -112, -111, 65, 68, 52, 62, 63, -1, -1, -1, -1, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, 119, 120, 121, -1, -1, -1, -125, -118, -119, -1, -125, -114, 123, -116, -1, -110, -109, -108, -107, 65, 64, 63, -1, -1, 45, 46, 52, 48, 49, 52, 51, -1, 45, 51, -1, -1, -1, -1, -125, -114, -128, -1, -1, -1, -113, -112, -106, -107, -1, -125, -124, -123, -1, -101, -100, -100, -99, -1, -1, -1, -1, -1, 57, 58, 64, -1, 65, 64, 63, 53, 53, 53, 53, 51, -1, -1, -1, -117, -119, -1, -1, -1, -110, -109, -96, -95, -1, -1, -1, 119, 120, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 51, 53, 51, 57, 63, 65, 58, 59, -1, -1, -1, -1, -1, -1, 127, 125, -93, -94, -95, 45, 46, 47, -1, -125, -114, 124, 126, 125, -1, -1, -1, -1, 86, 85, 84, -1, -1, 58, 63, -1, -1, -1, 119, 120, 121, -1, -1, -1, -1, -1, -1, -1, 122, -128, -1, -1, -1, 48, 49, 52, 51, -1, -117, -114, -127, -128, -1, 86, 86, 85, 84, 61, 51, -1, -1, -1, 52, -1, -1, 119, -126, -115, -116, -1, -1, -1, -1, -1, -1, -1, 122, 124, 125, -1, -1, 57, 58, 64, 63, -1, -1, -122, 123, -128, -1, 76, 75, 74, 74, 61, -1, -1, -1, -1, 64, 63, -1, -1, -124, -124, -119, -1, -1, -1, -1, -1, -1, -1, -125, -114, 124, 125, -1, -1, -1, -113, -112, -112, -111, -122, 123, -128, -1, -1, -1, -1, 76, 75, 74, -1, 45, 68, 67, 46, 47, 119, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -114, 124, 126, 126, 125, -93, -94, -94, -95, -117, -118, -119, 119, 120, 121, -1, -1, -1, 76, 75, 74, 65, 64, 58, 63, 122, -1, 125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -117, -118, -118, -118, -119, -1, 119, 120, 125, -1, -1, -1, -125, -124, -123, -1, -1, -1, -1, 45, 46, -104, -1, -1, -1, 45, 46, -1, 126, 125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 119, 120, 121, -1, -122, 123, -128, -1, -1, -1, -1, -1, -1, -1, -1, 45, 52, 57, 56, -95, -1, -1, -1, 57, 68, 67, -1, -119, -1, -113, -112, -107, -1, -1, -1, -1, -1, 127, -126, -127, 124, 126, -126, -120, -119, -1, -1, -1, -1, -113, -112, -111, -1, 57, 64, 68, 61, -1, -1, -1, -113, -111, 65, 64, 68, -1, -113, -102, -96, -95, -1, -1, -1, 127, 126, -126, -118, -124, -118, -118, -118, -119, -1, -1, -1, -1, -105, -102, -109, -108, -106, -107, -1, 57, 58, -1, -1, -1, -110, -108, -107, -1, 60, 61, -110, -96, -99, -1, -1, 119, 120, -126, 123, -120, -119, -1, -1, -1, -1, -1, -1, -1, -1, -1, -110, -109, -109, -109, -103, -104, -1, 53, -1, -1, -1, -1, -101, -94, -95, -1, 57, 68, 64, -95, -1, -1, -1, -122, -120, -119, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 59, -98, -109, -97, -103, -96, -95, -1, -1, -1, -1, -1, -1, -1, 53, -1, -1, -1, 60, 61, -1, 51, -1, 119, -126, -116, -1, 45, 46, 46, 46, 52, 51, -1, -1, -1, -1, -1, -1, -101, -90, -100, -100, -99, -1, -1, 53, 52, -1, -1, 52, 51, -1, 52, 52, 51, 57, 58, -1, 66, -1, -122, -120, -119, 53, 56, 55, 55, 55, 64, 63, 53, 52, 51, 45, 46, 52, 51, 53, 52, 46, 47, -1, 45, 56, 55]}, {"type": 3, "obj": [[2, "253_1", 496, 683, 92, 53, 0], [2, "253_1", 64, 238, 92, 53, 0], [2, "253", 834, 194, 92, 53, 0], [2, "174_2", 210, 388, 68, 33, 3], [2, "174_2", 89, 422, 68, 33, 3], [2, "174_2", 235, 292, 68, 33, 3], [2, "174_2", 306, 275, 68, 33, 1], [2, "174_2", 499, 173, 68, 33, 1], [2, "174_2", 576, 219, 68, 33, 3], [2, "174_2", 752, 440, 68, 33, 3], [2, "174_2", 801, 556, 68, 33, 3], [2, "174_2", 678, 651, 68, 33, 3], [2, "174_2", 732, 686, 68, 33, 3], [2, "327", 878, 646, 30, 22, 2], [2, "328", 872, 621, 32, 29, 2], [2, "327", 845, 592, 30, 22, 2], [2, "327", 516, 669, 30, 22, 0], [2, "420", 536, 681, 16, 13, 0], [2, "422", 503, 709, 16, 14, 0], [2, "326", 307, 613, 18, 14, 0], [2, "328", 280, 598, 32, 29, 0], [2, "328", 127, 579, 32, 29, 0], [2, "329", 49, 463, 42, 37, 0], [2, "328", 25, 468, 32, 29, 0], [2, "327", 15, 449, 30, 22, 0], [2, "174_2", -13, 506, 68, 33, 1], [2, "326", 722, 336, 18, 14, 0], [2, "420", 511, 137, 16, 13, 0], [2, "325", 459, 103, 50, 37, 2], [2, "327", 79, 57, 30, 22, 2], [2, "253_1", 263, 114, 92, 53, 2], [2, "253_1", 475, 424, 92, 53, 2], [2, "253_1", 799, 475, 92, 53, 2], [2, "253_1", 790, 97, 92, 53, 2], [2, "253_1", 18, 546, 92, 53, 0], [2, "253_1", -6, 140, 92, 53, 0], [2, "253_1", 438, 420, 92, 53, 0], [2, "253_1", 736, 121, 92, 53, 0]]}, {"type": 2, "data": [13, 13, 13, 13, 13, 40, 40, 40, -1, -1, -1, -1, 13, 13, 13, 13, 31, -1, 40, 13, 13, 13, 13, 97, 96, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 40, 31, 31, 31, 31, 31, 31, -1, -1, -1, -1, -1, -1, 13, 13, 31, 31, 40, 13, 13, 13, 13, 5, 3, 90, 90, 118, 117, 114, 90, 90, 90, 90, 90, 118, 117, 117, 113, 40, 40, 40, 31, 31, 31, 40, -1, -1, -1, -1, -1, -1, 41, 13, 31, 40, 40, 40, 40, 13, 13, -1, -1, -1, 89, 115, 104, 106, 97, 97, 97, 90, 114, 110, 109, 109, 109, 40, 40, 40, 40, 40, 31, -1, -1, -1, -1, -1, -1, -1, 30, 31, 40, 40, 40, 40, 40, -1, -1, -1, 102, 118, 117, 116, 103, 90, -1, -1, -1, 40, 40, 114, 114, 114, 114, 40, 40, 40, 40, 40, 40, 40, 40, -1, -1, -1, -1, 40, 31, 31, 40, 40, 40, 40, 40, 13, 102, 101, 118, 116, 105, 106, -1, -1, -1, 22, 23, 22, 40, 40, 40, 40, 31, 40, 40, 40, 40, 40, 40, 40, 19, 5, -1, 40, 32, 37, 28, 41, 40, 40, 40, 31, 40, 40, 98, 90, 110, 106, 92, 93, 94, -1, 12, 13, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 22, 22, 22, 40, 39, 7, 4, 38, 37, 41, 40, 31, 31, 40, 40, 90, 92, 93, 94, 89, 89, 2, 27, 26, 25, 37, 37, 41, 40, 40, 40, 40, 40, 22, 22, 40, 40, 22, 22, 22, 22, 31, 14, 19, 18, 7, 8, 30, 31, 31, 31, 40, 40, 40, 31, 31, 14, 31, 40, 43, 42, 8, 7, 8, 0, 27, 41, -1, 31, 31, 31, 31, 22, 22, 22, 31, 22, 22, 22, 22, 31, 40, 39, 8, 7, 27, 28, 41, 40, 31, 40, 40, 40, 13, 13, 22, 32, 41, 39, 5, 7, 8, 3, 4, 30, -1, 31, 31, 31, 31, 22, 31, 31, 31, 31, 22, 22, 22, 31, 31, 14, 10, 11, 4, 5, 38, 37, 41, 22, 31, 13, 13, 22, 32, 36, 27, 29, 8, 1, 2, 9, 10, 23, 40, 40, 13, 31, 31, 31, 31, 31, 31, 31, 22, 22, 22, 31, 31, 31, 13, 14, 18, 8, 3, 9, 23, 22, 13, 40, 40, 31, 39, 5, 2, 2, 3, 4, 9, 23, 40, 40, -1, 40, -1, 31, -1, 31, 22, 22, 31, 22, 22, 22, 22, 22, 22, 40, 22, 32, 36, 5, 1, 12, 22, 40, 40, 40, 40, 32, 36, 8, 9, 10, 11, 7, 12, 22, 40, 40, -1, -1, 31, -1, -1, -1, 31, 22, 32, 40, 40, 40, 40, 40, 40, 22, 22, 39, 5, 8, 1, 12, 22, 32, 28, 28, 28, 29, 20, 19, 23, 13, 14, 19, 23, 22, 31, 31, 31, 40, 31, 31, 31, 40, 40, 32, 40, 40, 40, 40, 40, 40, 40, 40, 40, 39, 5, 3, 4, 38, 37, 36, 5, 5, 0, 9, 23, 22, 40, 40, 22, 13, 13, 31, 31, 31, 31, 31, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 32, 37, 36, 4, 6, 7, 8, 5, 7, 8, 0, 1, 30, 40, 22, 22, 22, 40, 22, 31, 31, 31, 31, 31, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 32, 28, 29, 1, 6, 7, 6, 9, 10, 10, 11, 1, 3, 4, 38, 37, 41, 40, 40, 31, 31, 31, 40, 31, 31, 31, 40, -1, 40, 40, 40, 40, 31, 31, 31, 40, 31, 40, 39, 8, 5, 4, 5, 4, 3, 12, 13, 13, 14, 11, 6, 7, 8, 8, 38, 37, 37, 37, 41, 31, 22, 40, 22, 22, 22, 13, 14, 40, 40, 31, 31, 31, 32, 37, 41, 31, 39, 9, 10, 11, 8, 4, 5, 12, 40, 40, 40, 14, 19, 19, 19, 18, 2, 0, 4, 4, 30, 31, 22, 22, 22, 22, 40, 40, 13, 13, 31, 31, 32, 28, 29, 5, 27, 28, 36, 27, 41, 21, 4, 7, 9, 23, 31, 41, 40, 40, 40, 31, 22, 21, 5, 3, 4, 9, 23, 31, 22, 22, 22, 22, 40, 13, 13, 13, 13, 31, 39, 9, 10, 11, 6, 20, 19, 18, 38, 36, 7, 8, 30, 31, 40, 40, 40, 23, 13, 31, 31, 14, 19, 18, 7, 12, 13, 22, 22, 22, 22, 22, 31, -1, 13, 13, 31, 32, 36, 12, 13, 14, 10, 23, 22, 21, 9, 18, 4, 5, 27, 28, 41, 40, 32, 28, 41, 40, 31, 31, 22, 21, 2, 15, 28, 41, 22, 22, 22, 22, 31, 40, 13, 40, 31, 32, 8, 27, 28, 37, 41, 40, 32, 36, 27, 36, 7, 8, 4, 5, 38, 37, 36, 6, 38, 37, 41, 40, 40, 39, 4, 5, 9, 23, 31, 31, 31, 31, 40, 40, 40, 32, 28, 29, -1, 7, 8, 4, 38, 37, 36, 4, 20, 19, 10, 11, 7, 8, 0, 20, 19, 18, 2, 3, 38, 37, 37, 36, 7, 4, 30, 40, 31, 31, 31, 31, 13, 37, 37, 36, 2, 9, 10, -1, 19, 18, 20, 19, 18, 20, 23, 31, 13, 39, 4, 9, 10, 23, 22, 21, 5, 6, 3, 5, 9, 10, 10, 10, 23, 40, 40, 40, 31, 31, 31, 9, 10, 11, 5, 12, 13, 22, -1, 14, 23, 22, 14, 23, 31, 32, 37, 36, 7, 38, 41, 40, 40, 39, 8, 8, 6, 9, 23, 40, 31, 40, 40, 40, 40, 40, 40, 31, 31, 27, 28, 29, 20, 23, 13, 22, 22, -1, 31, 31, 31, 31, 32, 29, 2, 0, 1, 2, 38, 37, 37, 36, 0, 0, 1, 12, 22, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 8, 9, 10, 23, 31, 22, 22, 22, 22, 31, 31, 31, 31, 39, 4, 5, 3, 4, 5, 3, 3, 4, 5, 3, 9, 10, 23, 22, 22, 31, 40, 40, 40, 40, 40, 40, 40, 40, 19, 23, 31, 40, 31, 31, 40, 31, 31, 31, 31, 31, 31, 14, 18, 8, 20, 19, 19, 10, 19, 19, 18, 6, 12, 13, 22, 22, 22, 22, 22, 40, 40, 40, 40, 40, 41, 22, 31, 31, 31, 31, 31, 31, 31, 31, 31, 31, 40, 31, 31, 31, 14, 19, 23, 22, 22, 13, 40, 22, 14, 19, 10, 23, 13, 31, 22, 31, 40, 40, 40, 40, 40, 22, 22, 22, -1, -1, 40, 31, 31, 31, 31, 31, 31, 31, -1, 31, 31, 31, 31, 31, 31, 22, 22, 31, 40, 22, 31, 31, 22, 22, 31, 31, 31, 31, 31, 31, -1, 40, 22, 22, 22, 22]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}