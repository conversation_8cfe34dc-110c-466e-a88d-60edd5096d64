{"mW": 960, "mH": 672, "tW": 24, "tH": 24, "tiles": [["709_3", 0, 2, 1], ["302_3", 0, 2, 2], ["302_3", 2, 2, 2], ["497_1", 0, 2, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1134", 0, 3, 2], ["1134", 2, 3, 2], ["1134", 1, 3, 2], ["1134", 3, 3, 2], ["1316", 0, 4, 2]], "layers": [{"type": 3, "obj": [[2, "1128", 210, 579, 90, 99, 0], [2, "1328", 41, 338, 20, 29, 0], [2, "1328", 561, 544, 20, 29, 2], [2, "1328", 928, 480, 20, 29, 0], [2, "1328", 423, 82, 20, 29, 2], [2, "1328", 216, -15, 20, 29, 2], [2, "1328", 12, 76, 20, 29, 2], [2, "1328", 258, 5, 20, 29, 2], [2, "1328", 720, 625, 20, 29, 2], [2, "1128", 168, 599, 90, 99, 0]]}, {"type": 4, "obj": [[2, "1129", 352, -27, 30, 29, 2], [2, "1133", 621, -32, 24, 34, 0], [2, "1129", 674, -22, 30, 29, 0], [2, "1319", 874, -28, 52, 42, 0], [2, "1133", 852, -17, 24, 34, 0], [2, "1319", 315, -19, 52, 42, 2], [2, "1133", 692, -10, 24, 34, 2], [2, "1129", 309, -4, 30, 29, 2], [2, "1319", 914, -9, 52, 42, 0], [2, "1129", 952, 9, 30, 29, 0], [2, "1319", 272, 3, 52, 42, 2], [2, "1319", 225, 6, 52, 42, 0], [2, "1129", 256, 21, 30, 29, 0], [2, "1133", 240, 30, 24, 34, 0], [2, "1319", 271, 29, 52, 42, 0], [2, "700_1", 700, 23, 22, 48, 2], [2, "700_1", 723, 23, 22, 48, 0], [2, "1129", 311, 48, 30, 29, 0], [2, "1319", 327, 56, 52, 42, 0], [2, "1129", 365, 74, 30, 29, 0], [2, "1133", 323, 71, 24, 34, 0], [2, "1133", 79, 88, 24, 34, 2], [2, "1319", 381, 82, 52, 42, 0], [2, "1129", 419, 100, 30, 29, 0], [2, "1133", 435, 114, 24, 34, 2], [2, "1328", 120, 129, 20, 29, 2], [2, "700_1", 455, 141, 22, 48, 2], [2, "700_1", 478, 141, 22, 48, 0], [2, "1319", 933, 162, 52, 42, 2], [2, "1129", 925, 177, 30, 29, 2], [2, "1319", 888, 185, 52, 42, 2], [2, "1129", 882, 199, 30, 29, 2], [2, "1129", 864, 207, 30, 29, 2], [2, "1328", 857, 209, 20, 29, 2], [2, "1319", -30, 323, 52, 42, 0], [2, "1129", -5, 336, 30, 29, 0], [2, "1319", 10, 343, 52, 42, 0], [2, "1129", 35, 356, 30, 29, 0], [2, "1133", 494, 354, 24, 34, 0], [2, "1319", -3, 363, 52, 42, 2], [2, "1133", 51, 372, 24, 34, 2], [2, "1129", -19, 381, 30, 29, 0], [2, "1133", 393, 376, 24, 34, 2], [2, "1133", 577, 393, 24, 34, 0], [2, "1129", 959, 476, 30, 29, 2], [2, "1319", 922, 483, 52, 42, 2], [2, "1129", 920, 497, 30, 29, 0], [2, "1133", 912, 506, 24, 34, 0], [2, "1133", 336, 507, 24, 34, 2], [2, "1319", 936, 505, 52, 42, 0], [2, "1133", 220, 566, 24, 34, 2], [2, "1133", 553, 582, 24, 34, 0], [2, "1133", 88, 630, 24, 34, 2], [2, "1133", 654, 630, 24, 34, 0]]}, {"type": 3, "obj": [[2, "1132", -4, 619, 48, 42, 0], [2, "1132", 23, 634, 48, 42, 0], [2, "1132", 51, 649, 48, 42, 0], [2, "1131", -8, 588, 48, 53, 0], [2, "1329", 2, 594, 22, 35, 0], [2, "1319", -13, 565, 52, 42, 0], [2, "1130", 27, 600, 30, 53, 0], [2, "1131", 48, 615, 48, 53, 0], [2, "1329", 58, 621, 22, 35, 0], [2, "1129", 27, 585, 30, 29, 0], [2, "1319", 43, 592, 52, 42, 0], [2, "1129", 209, -2, 30, 29, 0], [2, "1129", 295, 419, 30, 29, 0], [2, "1329", 731, 473, 22, 35, 0], [2, "1329", 730, 470, 22, 35, 0], [2, "1130", 403, -32, 30, 53, 2], [2, "1131", 367, -26, 48, 53, 2], [2, "1329", 387, -20, 22, 35, 2], [2, "1130", 353, -9, 30, 53, 2], [2, "1131", 314, 0, 48, 53, 2], [2, "1329", 334, 6, 22, 35, 2], [2, "1130", 308, 13, 30, 53, 2], [2, "1131", 273, 20, 48, 53, 2], [2, "1329", 293, 26, 22, 35, 2], [2, "1130", 267, 33, 30, 53, 2], [2, "1132", 204, 40, 48, 42, 0], [2, "1132", 225, 52, 48, 42, 0], [2, "1132", 253, 66, 48, 42, 0], [2, "1132", 282, 80, 48, 42, 0], [2, "1132", 311, 95, 48, 42, 0], [2, "1132", 339, 110, 48, 42, 0], [2, "1132", 367, 126, 48, 42, 0], [2, "1132", 395, 141, 48, 42, 0], [2, "1132", 188, 57, 48, 42, 2], [2, "1132", 159, 71, 48, 42, 2], [2, "1132", 129, 86, 48, 42, 2], [2, "1132", 100, 101, 48, 42, 2], [2, "1132", 74, 114, 48, 42, 2], [2, "1132", 426, 158, 48, 42, 0], [2, "1131", 933, 185, 48, 53, 2], [2, "1329", 953, 191, 22, 35, 2], [2, "1130", 927, 198, 30, 53, 2], [2, "1131", 926, 507, 48, 53, 2], [2, "1329", 946, 513, 22, 35, 2], [2, "1132", 16, 144, 48, 42, 2], [2, "1132", -15, 140, 48, 42, 0], [2, "1132", 13, 155, 48, 42, 0], [2, "1132", 41, 169, 48, 42, 0], [2, "1132", 70, 183, 48, 42, 0], [2, "1132", 99, 198, 48, 42, 0], [2, "1132", 45, 128, 48, 42, 2], [2, "1132", 700, 49, 48, 42, 2], [2, "1132", 674, 61, 48, 42, 2], [2, "1132", 474, 374, 48, 42, 0], [2, "1132", 502, 389, 48, 42, 0], [2, "1132", 531, 404, 48, 42, 0], [2, "1132", 558, 418, 48, 42, 0], [2, "1132", 586, 433, 48, 42, 0], [2, "1132", 615, 448, 48, 42, 0], [2, "1132", 642, 462, 48, 42, 0], [2, "1132", 652, 469, 48, 42, 0], [2, "1132", 436, 378, 48, 42, 2], [2, "1132", 406, 394, 48, 42, 2], [2, "1132", 376, 409, 48, 42, 2], [2, "1132", 347, 424, 48, 42, 2], [2, "1132", 669, 477, 48, 42, 0], [2, "1132", 553, 614, 48, 42, 0], [2, "1132", 580, 628, 48, 42, 0], [2, "1132", 608, 643, 48, 42, 0], [2, "1132", 637, 658, 48, 42, 0], [2, "1132", 664, 672, 48, 42, 0], [2, "1132", 645, 76, 48, 42, 2], [2, "1130", 36, 378, 30, 53, 2], [2, "1131", -2, 386, 48, 53, 2], [2, "1130", -16, 402, 30, 53, 2], [2, "1329", 17, 393, 22, 35, 2], [2, "1131", -10, 350, 48, 53, 0], [2, "1329", -6, 360, 22, 35, 0], [2, "1302_2", 348, 579, 40, 29, 2], [2, "1303_1", 319, 585, 34, 20, 0], [2, "1302_2", 914, 339, 40, 29, 2], [2, "1302_2", 224, 79, 40, 29, 0], [2, "1305_1", 221, 108, 20, 14, 0], [2, "1303_1", 189, 111, 34, 20, 0], [2, "1130", 461, 336, 30, 53, 2], [2, "1131", 424, 345, 48, 53, 2], [2, "1329", 444, 351, 22, 35, 2], [2, "1130", 419, 357, 30, 53, 2], [2, "1131", 381, 365, 48, 53, 2], [2, "1329", 401, 371, 22, 35, 2], [2, "1130", 374, 378, 30, 53, 2], [2, "1131", 337, 387, 48, 53, 2], [2, "1329", 357, 393, 22, 35, 2], [2, "1130", 332, 399, 30, 53, 2], [2, "1131", 295, 408, 48, 53, 2], [2, "1329", 315, 414, 22, 35, 2], [2, "1132", 357, 514, 48, 42, 2], [2, "1132", 327, 530, 48, 42, 2], [2, "1132", 297, 545, 48, 42, 2], [2, "1132", 268, 560, 48, 42, 2], [2, "1132", 238, 575, 48, 42, 2], [2, "1130", 372, 482, 30, 53, 2], [2, "1131", 335, 491, 48, 53, 2], [2, "1329", 355, 497, 22, 35, 2], [2, "1130", 330, 503, 30, 53, 2], [2, "1131", 292, 511, 48, 53, 2], [2, "1329", 312, 517, 22, 35, 2], [2, "1130", 285, 524, 30, 53, 2], [2, "1131", 248, 533, 48, 53, 2], [2, "1329", 268, 539, 22, 35, 2], [2, "1130", 243, 545, 30, 53, 2], [2, "1131", 480, 343, 48, 53, 0], [2, "1130", 501, 354, 30, 53, 0], [2, "1329", 483, 349, 22, 35, 0], [2, "1131", 521, 364, 48, 53, 0], [2, "1130", 542, 376, 30, 53, 0], [2, "1329", 524, 370, 22, 35, 0], [2, "1131", 564, 385, 48, 53, 0], [2, "1130", 582, 396, 30, 53, 0], [2, "1329", 567, 391, 22, 35, 0], [2, "1131", 648, 429, 48, 53, 0], [2, "1130", 668, 440, 30, 53, 0], [2, "1329", 651, 435, 22, 35, 0], [2, "1131", 604, 406, 48, 53, 0], [2, "1130", 624, 417, 30, 53, 0], [2, "1329", 607, 412, 22, 35, 0], [2, "1130", 560, 583, 30, 53, 0], [2, "1131", 577, 593, 48, 53, 0], [2, "1130", 608, 608, 30, 53, 0], [2, "1329", 586, 600, 22, 35, 0], [2, "1131", 682, 644, 48, 53, 0], [2, "1130", 715, 661, 30, 53, 0], [2, "1329", 685, 650, 22, 35, 0], [2, "1131", 628, 617, 48, 53, 0], [2, "1130", 661, 634, 30, 53, 0], [2, "1329", 637, 624, 22, 35, 0], [2, "1131", 687, 450, 48, 53, 0], [2, "1130", 707, 461, 30, 53, 0], [2, "1329", 690, 456, 22, 35, 0], [2, "1129", 459, 318, 30, 29, 2], [2, "1319", 423, 325, 52, 42, 2], [2, "1319", 385, 343, 52, 42, 2], [2, "1129", 368, 363, 30, 29, 2], [2, "1319", 333, 370, 52, 42, 2], [2, "1129", 320, 387, 30, 29, 2], [2, "1319", 289, 391, 52, 42, 2], [2, "1129", 277, 410, 30, 29, 0], [2, "1319", 474, 325, 52, 42, 0], [2, "1129", 511, 344, 30, 29, 0], [2, "1319", 526, 352, 52, 42, 0], [2, "1129", 565, 370, 30, 29, 0], [2, "1319", 580, 378, 52, 42, 0], [2, "1129", 618, 396, 30, 29, 0], [2, "1319", 633, 404, 52, 42, 0], [2, "1129", 671, 423, 30, 29, 0], [2, "1319", 688, 431, 52, 42, 0], [2, "1319", 716, 445, 52, 42, 0], [2, "43_5", 564, 513, 82, 58, 0], [2, "43_5", 592, 485, 82, 58, 0], [2, "43_5", 621, 459, 82, 58, 0], [2, "43_5", 648, 432, 82, 58, 0], [2, "1129", 762, 460, 30, 29, 2], [2, "1319", 726, 467, 52, 42, 2], [2, "1129", 710, 486, 30, 29, 2], [2, "1319", 674, 492, 52, 42, 2], [2, "1129", 658, 511, 30, 29, 2], [2, "1319", 622, 518, 52, 42, 2], [2, "1129", 606, 537, 30, 29, 2], [2, "1319", 570, 543, 52, 42, 2], [2, "1129", 554, 562, 30, 29, 0], [2, "1319", 570, 570, 52, 42, 0], [2, "1129", 608, 588, 30, 29, 0], [2, "1319", 624, 596, 52, 42, 0], [2, "1129", 662, 615, 30, 29, 0], [2, "1319", 678, 623, 52, 42, 0], [2, "1129", 717, 641, 30, 29, 0], [2, "1319", 732, 650, 52, 42, 0], [2, "1132", 624, 87, 48, 42, 2], [2, "1132", 595, 102, 48, 42, 2], [2, "1132", 567, 115, 48, 42, 2], [2, "1132", 540, 128, 48, 42, 2], [2, "1132", 512, 141, 48, 42, 2], [2, "1132", 483, 156, 48, 42, 2], [2, "1132", 455, 169, 48, 42, 2], [2, "43_5", 648, 80, 82, 58, 2], [2, "990_1", 596, -14, 54, 27, 0], [2, "990_1", 623, 2, 54, 27, 0], [2, "990_1", 653, 16, 54, 27, 0], [2, "990_1", 643, 74, 54, 27, 2], [2, "990_1", 589, 101, 54, 27, 2], [2, "990_1", 559, 115, 54, 27, 2], [2, "990_1", 534, 128, 54, 27, 2], [2, "990_1", 504, 143, 54, 27, 2], [2, "990_1", 451, 169, 54, 27, 0], [2, "990_1", 475, 158, 54, 27, 2], [2, "43_5", 538, 137, 82, 58, 2], [2, "990_1", 670, 62, 54, 27, 2], [2, "1130", 206, 7, 30, 53, 0], [2, "1131", 227, 17, 48, 53, 0], [2, "1130", 248, 28, 30, 53, 0], [2, "1329", 230, 23, 22, 35, 0], [2, "1131", 268, 38, 48, 53, 0], [2, "1130", 289, 50, 30, 53, 0], [2, "1329", 271, 44, 22, 35, 0], [2, "1131", 311, 59, 48, 53, 0], [2, "1130", 329, 70, 30, 53, 0], [2, "1329", 314, 65, 22, 35, 0], [2, "1131", 395, 103, 48, 53, 0], [2, "1130", 415, 114, 30, 53, 0], [2, "1329", 398, 109, 22, 35, 0], [2, "1131", 351, 80, 48, 53, 0], [2, "1130", 371, 91, 30, 53, 0], [2, "1329", 354, 86, 22, 35, 0], [2, "1131", 651, -16, 48, 53, 0], [2, "1130", 671, -5, 30, 53, 0], [2, "1329", 654, -10, 22, 35, 0], [2, "1131", 607, -39, 48, 53, 0], [2, "1130", 627, -28, 30, 53, 0], [2, "1329", 610, -33, 22, 35, 0], [2, "1131", 170, 27, 48, 53, 2], [2, "1329", 190, 33, 22, 35, 2], [2, "1130", 160, 39, 30, 53, 2], [2, "1131", 124, 52, 48, 53, 2], [2, "1329", 144, 58, 22, 35, 2], [2, "1130", 114, 64, 30, 53, 2], [2, "1131", 76, 76, 48, 53, 2], [2, "1329", 96, 82, 22, 35, 2], [2, "1130", 66, 88, 30, 53, 2], [2, "1131", 26, 99, 48, 53, 2], [2, "1329", 46, 105, 22, 35, 2], [2, "1130", 16, 111, 30, 53, 2], [2, "1130", 208, 19, 30, 53, 2], [2, "1130", -10, 105, 30, 53, 0], [2, "1131", 11, 115, 48, 53, 0], [2, "1130", 32, 126, 30, 53, 0], [2, "1329", 14, 121, 22, 35, 0], [2, "1131", 52, 136, 48, 53, 0], [2, "1130", 73, 148, 30, 53, 0], [2, "1329", 55, 142, 22, 35, 0], [2, "1131", 95, 157, 48, 53, 0], [2, "1130", 113, 168, 30, 53, 0], [2, "1329", 98, 163, 22, 35, 0], [2, "43_5", -58, 113, 82, 58, 2], [2, "43_5", -32, 142, 82, 58, 2], [2, "43_5", -4, 170, 82, 58, 2], [2, "43_5", 23, 197, 82, 58, 2], [2, "1130", 915, 518, 30, 53, 0], [2, "1131", 933, 526, 48, 53, 0], [2, "1329", 940, 534, 22, 35, 0], [2, "1131", 891, 204, 48, 53, 2], [2, "1329", 911, 210, 22, 35, 2], [2, "1130", 885, 217, 30, 53, 2], [2, "1131", 847, 226, 48, 53, 2], [2, "1329", 867, 232, 22, 35, 2], [2, "1130", 837, 239, 30, 53, 0], [2, "1131", 855, 247, 48, 53, 0], [2, "1130", 879, 263, 30, 53, 0], [2, "1329", 862, 255, 22, 35, 0], [2, "1131", 898, 268, 48, 53, 0], [2, "1329", 905, 276, 22, 35, 0], [2, "1130", 922, 282, 30, 53, 0], [2, "1131", 937, 286, 48, 53, 0], [2, "1329", 943, 292, 22, 35, 0], [2, "1130", 820, -34, 30, 53, 0], [2, "1131", 838, -26, 48, 53, 0], [2, "1329", 845, -18, 22, 35, 0], [2, "1130", 862, -10, 30, 53, 0], [2, "1131", 881, -5, 48, 53, 0], [2, "1329", 888, 3, 22, 35, 0], [2, "1130", 905, 9, 30, 53, 0], [2, "1131", 922, 15, 48, 53, 0], [2, "1130", 949, 29, 30, 53, 0], [2, "1329", 929, 22, 22, 35, 0], [2, "90_1", 329, 551, 28, 36, 0], [2, "90_1", 853, 273, 28, 36, 0], [2, "90_1", 835, 9, 28, 36, 0], [2, "90_1", 860, 20, 28, 36, 0], [2, "90_1", 271, 92, 28, 36, 0], [2, "90_1", 296, 103, 28, 36, 0], [2, "90_1", 536, 418, 36, 28, 7], [2, "90_1", 522, 420, 28, 36, 0], [2, "90_1", 310, 136, 36, 28, 7], [2, "1305_1", 887, 327, 20, 14, 0], [2, "1304_1", 382, 557, 22, 19, 0], [2, "1304_1", 246, 115, 22, 19, 0], [2, "90_1", 1, 221, 28, 36, 0], [2, "90_1", 431, -17, 28, 36, 0], [2, "90_1", 387, 0, 28, 36, 0], [2, "1302_2", -5, 285, 40, 29, 0], [2, "1302_2", 330, 318, 40, 29, 2], [2, "1305_1", 324, 350, 20, 14, 0], [2, "1304_1", 935, 129, 22, 19, 0], [2, "1302_2", 487, 194, 40, 29, 2], [2, "1302_2", 575, 454, 40, 29, 2], [2, "1304_1", 547, 459, 22, 19, 0], [2, "1303_1", 890, 359, 34, 20, 0], [2, "1303_1", 557, 476, 34, 20, 0], [2, "1303_1", 281, 347, 34, 20, 0], [2, "1303_1", 804, 32, 34, 20, 0], [2, "1302_2", 787, 4, 40, 29, 2], [2, "1303_1", 524, 297, 34, 20, 0], [2, "1303_1", 19, 272, 34, 20, 0], [2, "1303_1", 369, 632, 34, 20, 0], [2, "1302_2", 267, 449, 40, 29, 2], [2, "1305_1", 302, 461, 20, 14, 0], [2, "1303_1", 234, 469, 34, 20, 0], [2, "1330", 472, -1, 112, 80, 2], [2, "990_1", 780, 458, 54, 27, 2], [2, "990_1", 760, 468, 54, 27, 2], [2, "990_1", 730, 482, 54, 27, 2], [2, "990_1", 705, 495, 54, 27, 2], [2, "990_1", 675, 509, 54, 27, 2], [2, "990_1", 650, 522, 54, 27, 2], [2, "990_1", 624, 534, 54, 27, 2], [2, "990_1", 599, 547, 54, 27, 2], [2, "990_1", 583, 557, 54, 27, 0], [2, "990_1", 611, 571, 54, 27, 0], [2, "990_1", 640, 585, 54, 27, 0], [2, "990_1", 667, 599, 54, 27, 0], [2, "990_1", 695, 612, 54, 27, 0], [2, "990_1", 722, 626, 54, 27, 0], [2, "990_1", 751, 640, 54, 27, 0], [2, "990_1", 780, 654, 54, 27, 0], [2, "1129", 303, 419, 30, 29, 0], [2, "1129", 318, 428, 30, 29, 0], [2, "1319", 335, 437, 52, 42, 0], [2, "1129", 372, 458, 30, 29, 2], [2, "1319", 336, 467, 52, 42, 2], [2, "1319", 298, 486, 52, 42, 2], [2, "1129", 281, 505, 30, 29, 2], [2, "1319", 246, 512, 52, 42, 2], [2, "1129", 233, 529, 30, 29, 2], [2, "990_1", 234, 408, 54, 27, 0], [2, "990_1", 262, 423, 54, 27, 0], [2, "990_1", 291, 437, 54, 27, 0], [2, "990_1", 320, 451, 54, 27, 0], [2, "1303_1", 261, 409, 34, 20, 0], [2, "703_1", 582, 111, 16, 23, 2], [2, "990_1", 446, 296, 54, 27, 0], [2, "990_1", 476, 310, 54, 27, 0], [2, "990_1", 503, 324, 54, 27, 0], [2, "990_1", 531, 337, 54, 27, 0], [2, "990_1", 558, 351, 54, 27, 0], [2, "990_1", 588, 365, 54, 27, 0], [2, "990_1", 618, 379, 54, 27, 0], [2, "990_1", 645, 393, 54, 27, 0], [2, "990_1", 683, 410, 54, 27, 0], [2, "990_1", 711, 424, 54, 27, 0], [2, "990_1", 753, 446, 54, 27, 0], [2, "990_1", 298, 465, 54, 27, 2], [2, "990_1", 273, 478, 54, 27, 2], [2, "990_1", 244, 492, 54, 27, 2], [2, "990_1", 218, 505, 54, 27, 2], [2, "990_1", 420, 308, 54, 27, 2], [2, "990_1", 391, 322, 54, 27, 2], [2, "990_1", 366, 335, 54, 27, 2], [2, "990_1", 336, 349, 54, 27, 2], [2, "990_1", 311, 362, 54, 27, 2], [2, "990_1", 281, 377, 54, 27, 2], [2, "990_1", 252, 392, 54, 27, 2], [2, "700_1", 659, 376, 22, 48, 2], [2, "700_1", 682, 376, 22, 48, 0], [2, "700_1", 734, 411, 22, 48, 2], [2, "700_1", 757, 411, 22, 48, 0], [2, "1328", 468, 304, 20, 29, 0], [2, "1328", 291, 399, 20, 29, 0], [2, "1128", 858, 28, 90, 99, 2], [2, "990_1", 616, 86, 54, 27, 2], [2, "703_1", 638, 81, 16, 23, 2], [2, "90_1", 548, 635, 36, 28, 7], [2, "90_1", 535, 637, 28, 36, 0], [2, "1128", 135, 63, 90, 99, 0], [2, "1319", 172, 4, 52, 42, 2], [2, "1129", 156, 22, 30, 29, 2], [2, "1319", 121, 29, 52, 42, 2], [2, "1129", 112, 45, 30, 29, 2], [2, "1319", 77, 51, 52, 42, 2], [2, "1129", 60, 69, 30, 29, 2], [2, "1319", 25, 76, 52, 42, 2], [2, "1129", 16, 92, 30, 29, 2], [2, "1319", -31, 74, 52, 42, 0], [2, "1129", 9, 93, 30, 29, 0], [2, "1319", 25, 101, 52, 42, 0], [2, "1129", 63, 119, 30, 29, 0], [2, "1319", 78, 127, 52, 42, 0], [2, "1129", 116, 145, 30, 29, 0], [2, "1133", 64, 140, 24, 34, 0], [2, "1133", 134, 164, 24, 34, 2], [2, "1328", 763, 452, 20, 29, 2], [2, "1129", 843, 221, 30, 29, 0], [2, "1319", 859, 229, 52, 42, 0], [2, "1129", 884, 242, 30, 29, 0], [2, "1319", 899, 249, 52, 42, 0], [2, "1129", 924, 262, 30, 29, 0], [2, "1319", 940, 270, 52, 42, 0], [2, "1133", 916, 276, 24, 34, 0], [2, "1133", 834, 235, 24, 34, 0], [2, "1132", 210, 590, 48, 42, 2], [2, "1132", 180, 606, 48, 42, 2], [2, "1132", 150, 621, 48, 42, 2], [2, "1132", 121, 636, 48, 42, 2], [2, "1132", 91, 651, 48, 42, 2], [2, "1131", 204, 555, 48, 53, 2], [2, "1329", 224, 561, 22, 35, 2], [2, "1130", 197, 568, 30, 53, 2], [2, "1131", 160, 577, 48, 53, 2], [2, "1329", 180, 583, 22, 35, 2], [2, "1130", 155, 589, 30, 53, 2], [2, "1319", 210, 530, 52, 42, 2], [2, "1129", 193, 549, 30, 29, 2], [2, "1319", 158, 556, 52, 42, 2], [2, "1129", 145, 573, 30, 29, 2], [2, "1131", 123, 596, 48, 53, 2], [2, "1329", 143, 602, 22, 35, 2], [2, "1130", 116, 609, 30, 53, 2], [2, "1131", 79, 618, 48, 53, 2], [2, "1329", 99, 624, 22, 35, 2], [2, "1319", 129, 571, 52, 42, 2], [2, "1129", 112, 590, 30, 29, 2], [2, "1132", 74, 659, 48, 42, 2], [2, "1130", 74, 630, 30, 53, 2], [2, "1319", 77, 597, 52, 42, 2], [2, "990_1", 190, 520, 54, 27, 2], [2, "990_1", 161, 534, 54, 27, 2], [2, "990_1", 132, 548, 54, 27, 2], [2, "990_1", 106, 561, 54, 27, 2], [2, "990_1", 78, 576, 54, 27, 2], [2, "990_1", 62, 583, 54, 27, 0], [2, "990_1", 33, 569, 54, 27, 0], [2, "990_1", 5, 555, 54, 27, 0], [2, "990_1", -24, 541, 54, 27, 0], [2, "1302_2", -3, 522, 40, 29, 2], [2, "1305_1", 35, 549, 20, 14, 0], [2, "1328", 375, 444, 20, 29, 2], [2, "1328", 235, 516, 20, 29, 2], [2, "1328", 79, 592, 20, 29, 2], [2, "90_1", 941, 560, 28, 36, 0], [2, "90_1", 8, 423, 28, 36, 0]]}, {"type": 2, "data": [28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 35, 34, 28, 28, 34, 33, 28, 28, 28, 28, 28, 28, 28, 27, 28, 34, 34, 28, 29, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, -1, -1, 27, 28, 28, 28, 34, 28, 31, 30, 28, 28, -1, 27, 28, 34, 28, 34, 25, 34, 35, 34, 28, 22, 22, 28, 28, 28, 28, 28, -1, 28, 28, 28, 28, 28, 28, 28, -1, -1, 28, 28, 24, 35, 34, 28, 31, 30, -1, 27, 28, 29, 27, 28, 29, 26, 24, 34, 34, 34, 32, 31, 35, 34, 22, 22, 22, 28, 28, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 35, 34, 32, 31, 30, -1, -1, -1, 24, 25, 26, 24, 25, 26, -1, -1, 29, 31, 34, 33, -1, 32, 31, 28, -1, 28, 28, 28, 28, 28, 28, 28, 28, 28, -1, -1, 27, 28, 34, 28, 29, 31, -1, -1, 32, 31, -1, -1, -1, -1, -1, -1, -1, -1, 27, 28, 34, 33, 31, 30, -1, -1, -1, 27, 28, 29, 28, 28, 28, -1, 28, 28, 28, 28, 28, -1, 27, 28, 34, 33, 26, 34, 34, 33, 33, 44, 43, -1, -1, -1, -1, -1, 43, 27, 34, 33, 31, 30, 26, -1, -1, -1, -1, 24, 25, 34, 28, 28, 28, 28, 28, 28, 28, 33, 35, 34, 33, 25, 31, 30, -1, 32, 31, 35, 34, 33, -1, -1, -1, -1, 58, 27, 28, 29, 31, 30, 26, -1, -1, 36, 37, 43, 42, -1, -1, 27, 28, 29, 35, 34, -1, 28, 28, 34, 33, 31, 30, -1, -1, -1, -1, -1, -1, 27, 28, 29, 34, 33, 27, 28, 22, 34, 33, 26, -1, -1, 44, 43, 43, 47, 40, 46, 41, 42, -1, 24, 25, 26, 32, 31, 27, 28, 29, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 28, 28, 29, 24, 25, 31, 31, 30, -1, -1, 36, 37, 47, 46, 46, 46, 52, 52, 45, -1, 34, 28, -1, -1, -1, 35, 34, 33, 43, 43, 43, 43, 42, -1, -1, 44, 43, 43, 43, 42, 24, 25, 26, -1, -1, -1, -1, -1, -1, -1, 39, 40, 40, 46, 52, 58, 58, 53, 50, 32, 31, 35, 34, 28, 34, 33, 36, 37, 47, 40, 40, 40, 41, 43, 43, 47, 46, 46, 46, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 47, 40, 46, 46, 52, 53, 49, 50, 18, 19, 18, 32, 31, 34, 31, 30, 39, 40, 40, 40, 46, 58, 58, 46, 46, 46, 46, 46, 46, 41, 42, -1, -1, -1, -1, -1, -1, -1, -1, 51, 52, 46, 46, 52, 58, 57, 27, 28, 31, 33, -1, 20, 19, 31, 30, -1, 48, 59, 52, 46, 46, 52, 52, 52, 52, 46, 52, 52, 52, 46, 45, -1, -1, -1, -1, -1, -1, 44, -1, 48, 49, 59, 46, 52, 58, 54, 24, 34, 22, 22, 19, 23, 22, 34, 33, -1, -1, 48, 49, 59, 52, 52, 52, 53, 49, 59, 58, 49, 49, 49, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 51, 46, 46, 52, 45, -1, 27, 34, 22, 22, 22, 22, 31, 30, -1, -1, -1, -1, -1, 48, 49, 49, 50, -1, 56, 55, 58, 57, -1, -1, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, 51, 46, 46, 46, 41, 42, 24, 31, 35, 34, 28, 29, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, -1, 55, 54, -1, -1, 16, 16, 17, -1, -1, 48, 48, 49, -1, 36, 47, 52, 52, 52, 46, 45, -1, -1, 32, 31, 25, 35, 14, -1, 19, 18, -1, -1, -1, -1, -1, -1, -1, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, 16, 17, 13, 18, 48, 59, -1, 51, 52, 53, 49, 54, -1, 36, 37, 38, -1, -1, 32, 17, 28, 34, 33, 36, 37, 38, -1, -1, -1, -1, 16, 16, 17, -1, 28, 35, 34, -1, 27, 28, 28, 27, 28, 29, -1, 56, 55, 48, 49, 50, -1, -1, -1, 39, 40, 41, 42, -1, -1, 35, 34, 28, 30, 39, 40, 41, 57, -1, -1, -1, -1, -1, 14, -1, -1, 27, 28, 29, 24, 34, 33, 28, 28, 28, 29, 28, 28, 28, 55, 54, 23, -1, -1, 51, 52, 53, 45, -1, -1, 28, 28, 21, -1, 51, 52, 53, 54, -1, -1, -1, -1, -1, 17, 13, 14, 28, 28, 28, 31, 31, 30, -1, 24, 27, 28, 28, 29, -1, -1, -1, -1, -1, -1, 48, 49, 50, -1, -1, -1, 24, 25, 26, -1, 48, 49, 50, -1, -1, -1, -1, -1, -1, -1, 16, 27, 34, 33, 36, 37, 38, -1, -1, -1, 24, 25, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 34, 33, 48, 59, 41, 42, 32, -1, 35, 34, 28, 28, 36, 37, 38, -1, -1, -1, -1, -1, -1, 35, 34, 28, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, -1, -1, -1, -1, -1, -1, 31, 30, -1, 51, 40, 58, 57, -1, 32, 35, 19, 28, -1, 36, 37, 38, -1, -1, -1, 12, 13, 32, 31, -1, 25, 19, 18, -1, -1, -1, -1, 23, 22, 28, 19, 18, -1, -1, 28, 28, -1, -1, 36, 47, 53, 55, 54, 12, 13, 23, -1, -1, -1, 39, 40, 41, 42, -1, -1, 24, 35, 34, -1, -1, -1, 22, 21, -1, -1, -1, -1, -1, -1, -1, 22, 21, 35, 28, 28, 36, 37, 37, 40, 40, 57, 20, 19, 23, 22, -1, -1, -1, -1, 51, 52, 53, 45, -1, -1, -1, 32, 31, 34, 22, -1, 16, 17, 23, 20, 19, 18, -1, -1, -1, 34, 28, 29, 31, 30, 39, 40, 40, 40, 49, 54, 23, 22, 28, -1, -1, -1, -1, -1, 48, 49, 50, -1, -1, -1, -1, -1, 32, 31, 35, 28, 28, -1, -1, 23, 22, 28, 19, 18, 19, 29, 25, 26, -1, -1, 48, 49, 49, 55, 54, 19, 23, 22, 28, -1, -1, -1, -1, -1, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, 32, 28, 28, 28, 28, 28, 28, 23, 22, 28, 22, 21, -1, -1, 12, 13, 14, 12, 13, 16, 23, 22, 28, 28, 28, 28, -1, -1, -1, -1, -1, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 10, 10, 11, 11, 10, 11, 11, 5, 10, 11, 11, 10, 11, 10, 11, -1, 11, 10, 11, 11, 10, 11, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 5, 10, 11, 10, 11, 10, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 10, 11, 10, 11, 10, 11, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, 65, -1, 7, 6, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 10, 11, 11, 10, 11, 10, 11, 65, 65, 65, 65, 65, 4, 65, 65, 65, 65, -1, 10, 11, 6, 9, 8, 9, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 65, 65, 65, 10, 11, 10, 11, 10, 11, -1, -1, 10, 11, 10, 11, 7, 6, 9, 9, 7, 6, 10, 11, 10, 11, 10, 11, 10, 11, 7, 6, 11, 7, 10, 11, 10, 11, 10, 11, 65, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 9, 9, 7, 10, 11, 11, 10, 11, 11, 11, 7, 9, 10, 11, 10, 11, 11, 11, 10, 11, 11, 11, 11, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 10, 11, 6, 10, 11, 9, 9, 10, 11, 10, 11, 10, 11, 10, 10, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 11, 11, 11, 11, 11, 10, 11, 11, 11, 10, 11, 11, 11, 11, 10, 11, 10, 11, 10, 11, 9, 8, 7, 6, 7, 6, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 11, 11, 11, 10, 11, 10, 11, 10, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 11, 10, 11, 11, 10, 11, 10, 11, 10, 11, 11, 10, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, -1, 11, 11, 11, 11, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 11, 10, 11, 11, 10, 11, 10, 11, 11, 10, 11, 11, 10, 10, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 10, 10, 11, 10, 11, 10, 11, 11, 11, 11, 11, 11, 11, 11, 11, 10, 10, 11, 11, 10, 11, 10, 11, 10, 11, 10, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 65, 65, 11, 11, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 10, 10, 11, 10, 11, 10, 11, 10, 11, 65, 11, 11, 10, 11, 11, 11, 11, 11, 11, 11, 10, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 10, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 10, 10, 11, 10, 11, 10, 10, 11, 2, 3, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 11, 10, 10, 11, 11, 11, 11, 10, 11, 10, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, -1, -1, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, -1, -1, 7, 6, 10, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 10, 10, 11, 10, 11, 10, 11, 11, 10, 11, 10, 10, 11, 11, 11, 10, 11, 10, 11, -1, -1, -1, -1, 7, 7, 6, 9, 8, 2, 3, 2, 3, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 10, 11, 11, 10, 11, 10, 11, 10, 11, 6, 7, 6, 7, 6, 8, 7, 6, 4, 5, 4, 5, 10, 11, 10, 11, -1, -1, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 11, 10, 11, 11, 11, 10, 11, 11, 11, 10, 11, 7, 6, 7, 6, 6, 8, 7, 6, 7, 6, 2, 3, -1, -1, -1, -1, 10, 11, -1, 10, 11, 11, 10, 11, 10, 11, 11, 10, 11, 10, 11, 11, 10, 11, 11, 10, 11, 11, 10, 11, 7, 6, 9, 8, 8, 9, 7, 6, 7, 6, 4, 5, -1, -1, -1, 10, 10, 11, 11, 11, 11, 10, 11, 10, 10, 10, 11, 10, 11, 11, 10, 10, 11, 11, 10, 11, 10, 11, 10, 11, 11, 11, 6, 9, 7, 6, 9, 8, 9, 8, 6, 7, 6, 7, 6, 7, 11, 11, 11, 10, 11, 10, 11, 10, 11, 11, 11, 11, 10, 11, 11, 11, 11, 10, 10, 11, 11, 10, 11, 10, 7, 6, 11, 6, 9, 8, 7, 6, 9, 9, 8, 9, 8, 7, 11, 11, 10, 11, 10, 11, 11, 10, 11, 10, 11, 65, 10, 11, 11, 10, 11, 10, 11, 9, 8, 10, 11, 10, 11, 10, 9, 8, 9, 8, 9, 8, 9, 8, 6, 9, 8, 7, 6, 11, 11, 11, 11, 11, 10, 11, 10, 11, 11, 10, 10, 11, 4, 5, 10, 11, 10, 11, 11, 11, 11, 9, 10, 11, 7, 6, 7, 6, 7, 6, 9, 7, 6, 7, 6, 7, 6, 9, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 10, 11, 11, 10, 2, 3, 8, 9, 8, 10, 11, 10, 11, 11, 10, 2, 3, 8, 3, 8, 9, 8, 6, 9, 8, 9, 8, 9, 8, -1, -1, -1, 10, 11, 10, 11, 10, 11, 11, 11, 11, 11, 11, 11, 65, 65, -1, -1, -1, 10, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 7, 6, 7, 6, 9, 8, 7, 6, -1, -1, -1, -1, -1, 10, 11, 10, 11, 11, 11, 10, 11, 11, 11, 2, 3, 65, 65, 65, 65, 4, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 9, 8, 9, 8, 2, 7, 6, 7, 6, -1, -1, -1, -1, -1, -1, 10, 11, 11, 10, 11, 10, 11, 10]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]}