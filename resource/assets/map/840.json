{"mW": 840, "mH": 720, "tW": 24, "tH": 24, "tiles": [["315", 0, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["111", 0, 3, 2], ["111", 2, 3, 2], ["111", 1, 3, 2], ["111", 3, 3, 2], ["484", 0, 1, 1], ["678", 0, 3, 2], ["678", 2, 3, 2], ["678", 1, 3, 2], ["678", 3, 3, 2], ["497", 0, 2, 1], ["1300", 2, 3, 2], ["1300", 0, 3, 2], ["1300", 3, 3, 2], ["1300", 1, 3, 2]], "layers": [{"type": 3, "obj": [[2, "3136", 733, 115, 72, 83, 0], [2, "268_1", 699, 85, 106, 82, 2], [2, "88_1", 661, 144, 88, 61, 2], [2, "87_1", 751, 158, 72, 57, 2], [2, "88_1", 9, 613, 88, 61, 0], [2, "268_1", -56, 558, 106, 82, 0], [2, "3136", 284, 94, 72, 83, 0], [2, "3136", 318, 70, 72, 83, 0], [2, "3136", 350, 101, 72, 83, 0], [2, "268_1", 306, 43, 106, 82, 2], [2, "88_1", 322, 112, 88, 61, 2], [2, "87_1", 303, 111, 72, 57, 2], [2, "3136", 126, 112, 72, 83, 0], [2, "3136", 160, 90, 72, 83, 0], [2, "3136", 214, 20, 72, 83, 0], [2, "3136", 254, 0, 72, 83, 0], [2, "3136", 723, -19, 72, 83, 0], [2, "3136", 769, -37, 72, 83, 0], [2, "3136", 769, -37, 72, 83, 0], [2, "3136", 607, 247, 72, 83, 0], [2, "3136", 558, 269, 72, 83, 0], [2, "3136", 573, 216, 72, 83, 0], [2, "3136", 573, 216, 72, 83, 0], [2, "3136", 573, 216, 72, 83, 0], [2, "3136", 39, 391, 72, 83, 0], [2, "3136", 73, 360, 72, 83, 0], [2, "3136", 90, 372, 72, 83, 0], [2, "3136", 51, 339, 72, 83, 0], [2, "3136", 537, 231, 72, 83, 0], [2, "3136", 174, 117, 72, 83, 0], [2, "3136", 216, -29, 72, 83, 0], [2, "3136", 256, -28, 72, 83, 0], [2, "3136", 91, 121, 72, 83, 0], [2, "3136", 101, 81, 72, 83, 0], [2, "3136", 130, 63, 72, 83, 0], [2, "3136", 173, 82, 72, 83, 0], [2, "3136", 546, 499, 72, 83, 0], [2, "3136", 573, 512, 72, 83, 0], [2, "3136", 527, 527, 72, 83, 0], [2, "3136", 268, 568, 72, 83, 0], [2, "3136", 304, 568, 72, 83, 0], [2, "3136", 304, 568, 72, 83, 0], [2, "3136", 352, 562, 72, 83, 0], [2, "3136", 289, 530, 72, 83, 0], [2, "3136", 321, 520, 72, 83, 0], [2, "967", 791, 597, 24, 41, 0], [2, "87_1", 106, 123, 72, 57, 2], [2, "268_1", 122, 92, 106, 82, 2], [2, "268_1", 218, -1, 106, 82, 2], [2, "268_1", 548, 234, 106, 82, 0], [2, "87_1", 569, 283, 72, 57, 2], [2, "268_1", 529, 497, 106, 82, 2], [2, "88_1", 728, -16, 88, 61, 0], [2, "88_1", 45, 390, 88, 61, 0], [2, "87_1", 65, 363, 72, 57, 2], [2, "268_1", 294, 545, 106, 82, 2], [2, "326", 434, 54, 18, 14, 0]]}, {"type": 4, "obj": [[2, "310", 9, -10, 44, 33, 0], [2, "310", 9, -10, 44, 33, 0], [2, "310", 692, -9, 44, 33, 0], [2, "3095", 601, -16, 62, 42, 0], [2, "310", 472, -3, 44, 33, 0], [2, "310", -8, 4, 44, 33, 0], [2, "3095", 156, -4, 62, 42, 0], [2, "3095", 156, -4, 62, 42, 0], [2, "310", 708, 7, 44, 33, 0], [2, "3095", 303, 8, 62, 42, 0], [2, "3095", 303, 8, 62, 42, 0], [2, "310", 127, 25, 44, 33, 0], [2, "310", 802, 25, 44, 33, 0], [2, "89", 146, -32, 48, 95, 2], [2, "89", 146, -32, 48, 95, 2], [2, "968", 485, 42, 24, 38, 0], [2, "3095", 779, 49, 62, 42, 0], [2, "3095", 779, 49, 62, 42, 0], [2, "89", 771, 13, 48, 95, 0], [2, "970", 634, 67, 48, 47, 0], [2, "970", 665, 80, 48, 47, 2], [2, "978", 499, 87, 66, 56, 2], [2, "89", 244, 53, 48, 95, 0], [2, "3095", 245, 127, 62, 42, 0], [2, "3095", 245, 127, 62, 42, 0], [4, 1, 565, 175, 0, 4024], [2, "1465", 427, 59, 52, 122, 2], [2, "3095", 370, 150, 62, 42, 0], [4, 3, 424, 197, 0, 4005], [2, "1465", 59, 82, 52, 122, 0], [2, "89", 329, 129, 48, 95, 0], [2, "3095", 785, 189, 62, 42, 0], [2, "3095", 686, 199, 62, 42, 0], [2, "3095", 302, 200, 62, 42, 0], [2, "89", 161, 152, 48, 95, 0], [2, "1465", 654, 130, 52, 122, 0], [2, "967", 102, 212, 24, 41, 0], [2, "3095", 131, 213, 62, 42, 0], [2, "3095", 269, 213, 62, 42, 0], [2, "3095", 269, 213, 62, 42, 0], [2, "422", 715, 242, 16, 14, 0], [2, "422", 715, 242, 16, 14, 0], [2, "89", 720, 170, 48, 95, 0], [2, "89", 720, 170, 48, 95, 0], [2, "327", 746, 250, 30, 22, 0], [2, "969", 83, 255, 36, 30, 2], [2, "969", 42, 268, 36, 30, 2], [2, "969", 65, 268, 36, 30, 2], [2, "310", 799, 284, 44, 33, 0], [4, 2, 97, 329, 0, 4024], [2, "690", 451, 263, 36, 85, 0], [2, "3095", 249, 348, 62, 42, 0], [2, "89", 582, 310, 48, 95, 0], [2, "690", 302, 345, 36, 85, 0], [2, "3095", 669, 402, 62, 42, 0], [2, "310", 102, 462, 44, 33, 0], [2, "89", 81, 415, 48, 95, 0], [2, "690", 451, 435, 36, 85, 0], [2, "1465", 101, 403, 52, 122, 2], [2, "3095", 569, 485, 62, 42, 0], [2, "3095", 65, 521, 62, 42, 0], [4, 5, 222, 580, 0, 4005], [4, 4, 515, 604, 0, 4022], [2, "3095", 421, 582, 62, 42, 0], [2, "3095", 453, 591, 62, 42, 0], [2, "89", 556, 564, 48, 95, 0], [2, "3095", 783, 620, 62, 42, 0], [2, "968", 788, 630, 24, 38, 0], [2, "3095", 673, 656, 62, 42, 0], [2, "89", 330, 604, 48, 95, 0], [2, "3095", 208, 660, 62, 42, 0], [2, "3095", 689, 660, 62, 42, 0], [2, "3095", 697, 674, 62, 42, 0], [2, "3095", 320, 676, 62, 42, 0], [2, "89", -10, 631, 48, 95, 2], [2, "978", 784, 672, 66, 56, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 20, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 20, 19, 31, 13, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 16, 16, -1, -1, -1, 17, 16, 16, 15, -1, -1, -1, -1, -1, -1, 20, 19, 13, 13, 13, 13, 13, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 10, 20, 19, 25, 25, -1, 10, 10, 20, 26, 22, 23, -1, -1, -1, 29, 32, 31, 13, 13, 13, 13, 13, 13, 13, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 13, 13, 13, 13, -1, -1, 13, 13, 13, -1, -1, -1, -1, -1, -1, -1, 29, 28, 28, 13, 13, 13, 13, 25, 26, -1, -1, 17, -1, -1, -1, -1, -1, -1, -1, 13, 13, 13, 13, -1, 13, 13, 13, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 23, -1, -1, 24, 13, 13, 13, 13, 13, 13, 13, 13, 13, 31, 31, 13, 13, 25, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 13, -1, -1, -1, -1, -1, 24, 31, 13, 13, 13, 13, 13, 19, 25, 26, 28, 28, 28, 28, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 13, 13, 13, 13, -1, -1, -1, 29, 28, 32, 31, 13, 13, 25, 26, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 16, 13, 13, 13, 13, 25, 30, -1, -1, -1, -1, -1, 29, 28, 28, 28, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 28, 28, 28, 13, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 16, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 13, 13, -1, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 31, 13, 13, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 22, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 10, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 13, 25, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 28, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 16, 10, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 13, 13, 14, 11, -1, -1, -1, -1, -1, -1, -1, 9, 20, 19, 13, 13, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19]}, {"type": 3, "obj": [[2, "660", 531, 454, 30, 33, 0], [2, "662", 337, 421, 30, 49, 2], [2, "307", 295, 347, 42, 19, 0], [2, "420", 468, 291, 16, 13, 0], [2, "479", 598, 172, 36, 18, 0], [2, "479", 473, 515, 36, 18, 0], [2, "478", 721, 301, 24, 16, 0], [2, "553", 606, 453, 14, 8, 0], [2, "553", 335, 291, 14, 8, 0], [2, "479", 121, 290, 36, 18, 2], [2, "478", 491, 350, 24, 16, 0], [2, "478", 51, 612, 24, 16, 0], [2, "422", 456, 270, 16, 14, 0], [2, "327", 79, 698, 30, 22, 0], [2, "479", 453, 553, 36, 18, 2], [2, "479", 473, 199, 36, 18, 0], [2, "479", 674, 93, 36, 18, 2], [2, "479", 753, 132, 36, 18, 2], [2, "329", 765, 631, 42, 37, 2], [2, "329", 305, 173, 42, 37, 2], [2, "654", 462, 370, 96, 45, 3], [2, "654", 368, 370, 96, 45, 1], [2, "654", 368, 414, 96, 45, 0], [2, "658", 483, 466, 48, 29, 0], [2, "657", 483, 449, 48, 27, 0], [2, "661", 336, 417, 30, 41, 2], [2, "656", 462, 475, 22, 22, 0], [2, "662", 561, 425, 30, 49, 0], [2, "658", 392, 469, 48, 29, 2], [2, "660", 365, 453, 30, 33, 2], [2, "661", 562, 375, 30, 41, 1], [2, "659", 532, 363, 30, 41, 1], [2, "657", 484, 355, 48, 27, 1], [2, "655", 444, 355, 22, 16, 3], [2, "657", 396, 356, 48, 27, 3], [2, "659", 365, 365, 30, 41, 3], [2, "666", 334, 451, 60, 36, 2], [2, "666", 335, 450, 60, 36, 2], [2, "666", 335, 446, 60, 36, 2], [2, "666", 342, 446, 60, 36, 2], [2, "666", 327, 455, 60, 36, 2], [2, "666", 333, 455, 60, 36, 2], [2, "666", 324, 463, 60, 36, 2], [2, "654", 462, 414, 96, 45, 2], [2, "422", 389, 550, 16, 14, 0], [2, "661", 561, 414, 30, 41, 0], [2, "661", 336, 378, 30, 41, 3], [2, "478", 559, 201, 24, 16, 0], [2, "422", 776, 262, 16, 14, 2], [2, "327", 675, 451, 30, 22, 0], [2, "422", 605, 440, 16, 14, 0], [2, "422", 13, 69, 16, 14, 2], [2, "478", 575, 133, 24, 16, 0], [2, "479", 308, 627, 36, 18, 0], [2, "479", 355, 267, 36, 18, 2], [2, "479", 769, 507, 36, 18, 2], [2, "422", 381, 352, 16, 14, 2], [2, "422", 466, 335, 16, 14, 2], [2, "479", 317, 503, 36, 18, 0], [2, "422", 199, 420, 16, 14, 0], [2, "422", 247, 435, 16, 14, 0], [2, "328", 378, 201, 32, 29, 0], [2, "328", 177, 426, 32, 29, 0], [2, "328", 22, 367, 32, 29, 0], [2, "327", 195, 411, 30, 22, 0], [2, "421", 112, 470, 14, 11, 0], [2, "420", 148, 253, 16, 13, 0], [2, "420", 157, 508, 16, 13, 0], [2, "420", 413, 458, 16, 13, 0], [2, "327", 453, 569, 30, 22, 0], [2, "422", 625, 449, 16, 14, 0], [2, "327", 355, 211, 30, 22, 0], [2, "328", 677, 460, 32, 29, 0], [2, "328", 494, 306, 32, 29, 0], [2, "479", 85, 608, 36, 18, 0], [2, "479", 742, 36, 36, 18, 2], [2, "307", 450, 320, 42, 19, 0], [2, "420", 746, 703, 16, 13, 0], [2, "327", 757, 690, 30, 22, 0], [2, "479", 486, 73, 36, 18, 2], [2, "329", 663, 427, 42, 37, 0], [2, "328", 316, 332, 32, 29, 0], [2, "420", 194, 671, 16, 13, 0], [2, "420", 309, 716, 16, 13, 0], [2, "422", 166, 608, 16, 14, 2], [2, "329", 742, 215, 42, 37, 0], [2, "422", 373, 500, 16, 14, 0], [2, "420", 316, 336, 16, 13, 0], [2, "479", 256, 440, 36, 18, 2], [2, "479", 36, 119, 36, 18, 2], [2, "422", 296, 346, 16, 14, 0], [2, "420", 227, 259, 16, 13, 0], [2, "659", 362, 426, 30, 41, 2], [2, "657", 392, 449, 48, 27, 2], [2, "655", 461, 460, 22, 16, 0], [2, "655", 440, 460, 22, 16, 2], [2, "328", 815, 138, 32, 29, 0], [2, "965", 601, 70, 40, 33, 0], [2, "965", 474, 70, 40, 33, 0], [2, "969", 596, 36, 36, 30, 2], [2, "969", 580, 48, 36, 30, 2], [2, "969", 463, 434, 36, 30, 0], [2, "656", 440, 476, 22, 22, 2], [2, "22", 130, 524, 62, 38, 0], [2, "327", 362, 11, 30, 22, 0], [2, "328", 383, -6, 32, 29, 0], [2, "969", 212, 288, 36, 30, 2], [2, "969", 200, 298, 36, 30, 2], [2, "969", 636, 441, 36, 30, 0], [2, "969", 648, 459, 36, 30, 0], [2, "965", 605, 417, 40, 33, 0], [2, "420", 71, 566, 16, 13, 0], [2, "969", 32, 72, 36, 30, 2], [2, "655", 466, 355, 22, 16, 1], [2, "659", 531, 427, 30, 41, 0], [2, "479", 532, 343, 36, 18, 0], [2, "479", 565, 596, 36, 18, 2], [2, "422", 555, 478, 16, 14, 0], [2, "327", 630, 551, 30, 22, 0], [2, "549", 543, 426, 34, 28, 0], [2, "551", 512, 372, 30, 21, 0], [2, "553", 493, 374, 14, 8, 0], [2, "965", 37, 45, 40, 33, 0], [2, "965", 70, 65, 40, 33, 0], [2, "969", 11, 89, 36, 30, 2], [2, "969", 174, 613, 36, 30, 2], [2, "969", 158, 625, 36, 30, 2], [2, "965", 234, 572, 40, 33, 0], [2, "965", 780, 658, 40, 33, 0], [2, "965", 234, 572, 40, 33, 0], [2, "1456", 621, 466, 24, 32, 0], [2, "1457", 613, 477, 22, 30, 0], [2, "1457", 547, 622, 22, 30, 0], [2, "1457", 501, 542, 22, 30, 0], [2, "1457", 673, 665, 22, 30, 0], [2, "1456", 655, 670, 24, 32, 0], [2, "1456", 179, 571, 24, 32, 0], [2, "1457", 415, 312, 22, 30, 0], [2, "1457", 182, 413, 22, 30, 0], [2, "1456", 71, 470, 24, 32, 0], [2, "1457", 27, 356, 22, 30, 0], [2, "1456", 7, 299, 24, 32, 0], [2, "1457", 160, 662, 22, 30, 0], [2, "1457", 809, 423, 22, 30, 0], [2, "1456", 757, 61, 24, 32, 0], [2, "1456", 816, 260, 24, 32, 0], [2, "1457", 620, 30, 22, 30, 0], [2, "1457", 367, 194, 22, 30, 0], [2, "1457", 247, 181, 22, 30, 0], [2, "1456", 185, 211, 24, 32, 0], [2, "1456", 96, 9, 24, 32, 0], [2, "1457", 391, 14, 22, 30, 0], [2, "1457", 817, 132, 22, 30, 0], [2, "1457", 577, 338, 22, 30, 0], [2, "965", 323, 252, 40, 33, 0], [2, "965", 35, 283, 40, 33, 0], [2, "965", 206, 473, 40, 33, 0], [2, "965", 671, 528, 40, 33, 0], [2, "965", 93, 244, 40, 33, 0], [2, "969", 491, 629, 36, 30, 0], [2, "551", 410, 185, 30, 21, 0], [2, "553", 442, 188, 14, 8, 0], [2, "553", 513, 54, 14, 8, 0], [2, "553", 701, 253, 14, 8, 0], [2, "553", 534, 318, 14, 8, 0], [2, "553", 519, 612, 14, 8, 0], [2, "553", 184, 657, 14, 8, 0], [2, "553", 184, 657, 14, 8, 0], [2, "553", 109, 610, 14, 8, 0], [2, "553", 196, 494, 14, 8, 0], [2, "553", 87, 319, 14, 8, 0], [2, "553", 42, 314, 14, 8, 0], [2, "553", 60, 197, 14, 8, 0], [2, "553", 656, 526, 14, 8, 0], [2, "553", 761, 662, 14, 8, 0], [2, "550", 509, 60, 42, 28, 0], [2, "550", 114, 191, 42, 28, 0], [2, "550", 126, 480, 42, 28, 0], [2, "1456", 468, 135, 24, 32, 0], [2, "1456", 414, 149, 24, 32, 0], [2, "326", 249, 693, 18, 14, 0]]}, {"type": 3, "obj": [[2, "309", 462, 491, 46, 33, 0], [2, "307", 425, 497, 42, 19, 0], [2, "306", 446, 510, 46, 25, 0], [2, "307", 356, 489, 42, 19, 0], [2, "307", 335, 516, 42, 19, 0], [2, "307", 300, 425, 42, 19, 0], [2, "307", 283, 440, 42, 19, 0], [2, "307", 314, 441, 42, 19, 0], [2, "307", 389, 489, 42, 19, 0], [2, "306", 374, 503, 46, 25, 0], [2, "308", 401, 514, 52, 22, 0], [2, "306", 396, 500, 46, 25, 0], [2, "306", 368, 525, 46, 25, 0], [2, "306", 337, 538, 46, 25, 0], [2, "306", 326, 478, 46, 25, 0], [2, "306", 332, 501, 46, 25, 0], [2, "306", 264, 465, 46, 25, 0], [2, "307", 296, 477, 42, 19, 0], [2, "307", 308, 495, 42, 19, 0], [2, "307", 325, 528, 42, 19, 0], [2, "308", 286, 458, 52, 22, 0], [2, "308", 241, 454, 52, 22, 0], [2, "306", 489, 491, 46, 25, 0], [2, "306", 514, 481, 46, 25, 0], [2, "306", 539, 470, 46, 25, 0], [2, "306", 512, 500, 46, 25, 0], [2, "307", 486, 516, 42, 19, 0], [2, "308", 542, 496, 52, 22, 0], [2, "308", 539, 514, 52, 22, 0], [2, "306", 467, 531, 46, 25, 0], [2, "306", 402, 545, 46, 25, 0], [2, "306", 430, 553, 46, 25, 0], [2, "308", 464, 550, 52, 22, 0], [2, "308", 358, 545, 52, 22, 0], [2, "306", 274, 486, 46, 25, 0], [2, "307", 295, 509, 42, 19, 0], [2, "307", 305, 523, 42, 19, 0], [2, "307", 407, 571, 42, 19, 0], [2, "308", 477, 566, 52, 22, 0], [2, "306", 542, 580, 46, 25, 0], [2, "307", 545, 603, 42, 19, 0], [2, "307", 522, 570, 42, 19, 0], [2, "307", 513, 635, 42, 19, 0], [2, "307", 594, 619, 42, 19, 0], [2, "308", 575, 593, 52, 22, 0], [2, "308", 551, 614, 52, 22, 0], [2, "308", 578, 543, 52, 22, 0], [2, "307", 582, 565, 42, 19, 0], [2, "307", 516, 618, 42, 19, 0], [2, "307", 624, 469, 42, 19, 0], [2, "307", 588, 440, 42, 19, 0], [2, "306", 568, 447, 46, 25, 0], [2, "306", 560, 466, 46, 25, 0], [2, "306", 601, 454, 46, 25, 0], [2, "306", 582, 465, 46, 25, 0], [2, "306", 583, 421, 46, 25, 0], [2, "306", 611, 406, 46, 25, 0], [2, "306", 627, 420, 46, 25, 0], [2, "307", 426, 331, 42, 19, 0], [2, "306", 448, 338, 46, 25, 0], [2, "306", 478, 326, 46, 25, 0], [2, "306", 418, 343, 46, 25, 0], [2, "306", 278, 418, 46, 25, 0], [2, "306", 298, 403, 46, 25, 0], [2, "306", 223, 436, 46, 25, 0], [2, "306", 196, 444, 46, 25, 0], [2, "306", 207, 422, 46, 25, 0], [2, "307", 597, 530, 42, 19, 0], [2, "307", 639, 484, 42, 19, 0], [2, "307", 626, 497, 42, 19, 0], [2, "308", 573, 406, 52, 22, 0], [2, "308", 481, 345, 52, 22, 0], [2, "308", 379, 319, 52, 22, 0], [2, "306", 269, 387, 46, 25, 0], [2, "307", 306, 390, 42, 19, 0], [2, "307", 385, 337, 42, 19, 0], [2, "307", 375, 352, 42, 19, 0], [2, "307", 336, 375, 42, 19, 0], [2, "307", 345, 360, 42, 19, 0], [2, "306", 514, 348, 46, 25, 0], [2, "306", 521, 331, 46, 25, 0], [2, "306", 541, 351, 46, 25, 0], [2, "308", 551, 367, 52, 22, 0], [2, "308", 615, 394, 52, 22, 0], [2, "307", 595, 369, 42, 19, 0], [2, "307", 628, 366, 42, 19, 0], [2, "308", 612, 351, 52, 22, 0], [2, "308", 70, 181, 52, 22, 0], [2, "308", 103, 188, 52, 22, 0], [2, "308", 98, 171, 52, 22, 0], [2, "308", 98, 151, 52, 22, 0], [2, "307", 100, 135, 42, 19, 0], [2, "307", 76, 157, 42, 19, 0], [2, "307", 203, 261, 42, 19, 0], [2, "307", 768, 663, 42, 19, 0], [2, "307", 791, 678, 42, 19, 0], [2, "307", 763, 676, 42, 19, 0], [2, "307", 808, 703, 42, 19, 0], [2, "307", 808, 703, 42, 19, 0], [2, "307", 645, 57, 42, 19, 0], [2, "307", 668, 62, 42, 19, 0], [2, "307", 474, 167, 42, 19, 0], [2, "307", 61, 89, 42, 19, 0], [2, "307", 37, 78, 42, 19, 0], [2, "307", 10, 81, 42, 19, 0], [2, "307", 31, 65, 42, 19, 0], [2, "307", 31, 65, 42, 19, 0], [2, "307", 26, 52, 42, 19, 0], [2, "307", 26, 52, 42, 19, 0], [2, "307", 67, 63, 42, 19, 0], [2, "307", 67, 63, 42, 19, 0], [2, "307", 82, 76, 42, 19, 0], [2, "308", 82, 85, 52, 22, 0], [2, "308", 31, 93, 52, 22, 0]]}, {"type": 2, "data": [88, 88, 95, 101, 100, 82, 82, 82, 94, 87, 112, 93, 94, 100, 100, 100, 100, 100, 100, 100, 83, 85, 84, 93, 94, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 100, 95, 92, 93, 100, 82, 82, 82, 95, 96, 115, 90, 101, 82, 100, 100, 100, 100, 95, 101, 100, 88, 87, 90, 91, 101, 82, 82, 82, 82, 82, 82, 82, 82, 82, 100, 99, 106, 90, 91, 91, 91, 91, 92, 125, 86, 85, 89, 82, 82, 82, 82, 95, 92, 98, 97, 97, 96, 106, 105, 90, 101, 100, 82, 82, 82, 82, 82, 82, 82, 97, 96, 109, 122, 125, 126, 127, 125, 86, 85, 89, 88, 82, 82, 82, 82, 82, 83, 85, 84, 122, 123, 124, 109, 108, 107, 98, 97, 101, 100, 82, 82, 82, 82, 82, 106, 105, 104, 125, 126, 86, 85, 79, 89, 88, 82, 82, 82, 82, 82, 82, 82, 82, 82, 83, 84, 123, 124, 121, 120, 119, 125, 126, 98, 97, 101, 82, 82, 82, 82, 109, 108, 107, 122, 86, 89, 88, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 83, 84, 106, 105, 104, 110, 111, 112, 126, 127, 93, 82, 82, 82, 82, 104, 86, 85, 79, 89, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 94, 94, 99, 109, 108, 107, 113, 114, 115, 109, 108, 93, 82, 82, 82, 82, 107, 93, 88, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 88, 82, 94, 95, 97, 96, 121, 120, 119, 125, 126, 127, 86, 85, 89, 82, 82, 82, 82, 119, 90, 91, 101, 100, 82, 82, 82, 82, 82, 82, 82, 82, 82, 88, 88, 88, 95, 91, 92, 126, 127, 118, 117, 116, 122, 123, 86, 89, 88, 82, 82, 82, 82, 82, 116, 125, 126, 98, 101, 100, 82, 82, 82, 82, 82, 82, 82, 88, 88, 95, 91, 92, 116, 122, 123, 124, 106, 105, 104, 110, 86, 89, 94, 82, 82, 82, 82, 82, 82, 118, 117, 116, 109, 98, 101, 100, 95, 101, 100, 82, 100, 94, 95, 91, 92, 106, 105, 104, 110, 111, 112, 110, 111, 112, 113, 81, 82, 82, 82, 82, 82, 94, 94, 94, 108, 106, 105, 104, 110, 98, 97, 96, 98, 97, 97, 97, 91, 92, 114, 115, 109, 126, 126, 78, 79, 80, 113, 114, 115, 110, 81, 82, 95, 97, 96, 90, 101, 94, 94, 79, 80, 108, 107, 113, 114, 115, 125, 126, 127, 121, 120, 119, 125, 126, 127, 78, 79, 79, 89, 82, 83, 84, 105, 104, 113, 98, 97, 92, 105, 104, 110, 90, 101, 100, 82, 83, 84, 119, 125, 126, 127, 122, 123, 124, 118, 117, 116, 122, 123, 86, 89, 82, 82, 82, 82, 82, 83, 79, 80, 113, 114, 115, 109, 108, 107, 113, 114, 98, 97, 82, 82, 99, 116, 122, 123, 124, 121, 120, 119, 86, 85, 84, 110, 86, 89, 82, 82, 82, 82, 82, 82, 82, 82, 83, 79, 80, 127, 121, 120, 119, 113, 114, 127, 113, 82, 82, 99, 121, 120, 119, 78, 79, 80, 86, 89, 88, 83, 85, 89, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 83, 79, 80, 117, 116, 122, 123, 124, 121, 94, 95, 92, 104, 110, 111, 93, 94, 83, 89, 94, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 99, 105, 104, 110, 111, 112, 118, 91, 92, 108, 107, 113, 114, 90, 91, 101, 82, 82, 82, 82, 94, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 83, 85, 84, 113, 114, 78, 79, 127, 121, 120, 119, 86, 85, 79, 79, 89, 82, 82, 82, 94, 100, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 88, 87, 125, 126, 81, 82, 124, 118, 86, 85, 89, 88, 82, 95, 97, 97, 101, 100, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 87, 122, 123, 81, 82, 112, 106, 81, 88, 82, 82, 95, 96, 106, 105, 98, 97, 101, 100, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 94, 95, 96, 110, 111, 90, 101, 115, 109, 81, 82, 82, 82, 83, 80, 109, 108, 110, 111, 98, 101, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 95, 91, 92, 107, 113, 114, 115, 109, 121, 120, 81, 94, 94, 88, 82, 83, 80, 107, 113, 114, 115, 93, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 94, 94, 87, 111, 112, 119, 125, 126, 127, 121, 110, 111, 90, 101, 100, 94, 82, 82, 83, 84, 121, 120, 119, 90, 101, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 94, 94, 87, 114, 115, 116, 122, 123, 124, 118, 113, 114, 115, 98, 97, 101, 100, 94, 94, 87, 118, 117, 116, 107, 98, 91, 101, 82, 82, 82, 82, 82, 82, 82, 82, 94, 95, 92, 127, 105, 104, 110, 111, 112, 106, 125, 126, 127, 106, 105, 98, 97, 101, 95, 96, 106, 105, 104, 110, 111, 112, 98, 91, 101, 82, 82, 82, 82, 82, 94, 94, 99, 123, 124, 108, 107, 113, 86, 85, 85, 122, 123, 124, 109, 108, 107, 107, 111, 126, 112, 109, 108, 107, 113, 114, 115, 109, 108, 107, 98, 101, 100, 100, 94, 94, 94, 99, 127, 121, 120, 86, 85, 89, 88, 82, 125, 126, 127, 125, 126, 127, 78, 79, 80, 112, 121, 120, 119, 125, 126, 127, 121, 120, 119, 125, 98, 97, 101, 100, 95, 91, 92, 124, 86, 85, 89, 88, 82, 82, 82, 122, 123, 124, 122, 123, 124, 81, 82, 83, 79, 85, 84, 112, 86, 85, 84, 118, 117, 116, 122, 123, 124, 106, 105, 104, 110, 86, 85, 89, 88, 82, 82, 82, 82, 82, 79, 85, 84, 105, 78, 79, 89, 88, 88, 82, 88, 87, 111, 81, 88, 83, 84, 106, 105, 104, 110, 111, 112, 108, 107, 86, 89, 88, 82, 82, 82, 82, 82, 82, 88]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1]}