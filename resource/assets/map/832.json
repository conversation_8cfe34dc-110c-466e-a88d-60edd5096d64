{"mW": 1056, "mH": 960, "tW": 24, "tH": 24, "tiles": [["1408", 0, 3, 2], ["1408", 2, 3, 2], ["1408", 1, 3, 2], ["1408", 3, 3, 2], ["1803", 0, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 2, "data": [43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43]}, {"type": 3, "obj": [[2, "818_1", 86, 12, 30, 37, 2], [2, "1401", 200, 7, 62, 92, 0], [2, "1396", 175, 106, 50, 41, 2], [2, "1401", 175, 21, 62, 92, 0], [2, "1396", 162, 111, 50, 41, 2], [2, "1401", 150, 33, 62, 92, 0], [2, "1396", 124, 130, 50, 41, 2], [2, "1396", 82, 151, 50, 41, 2], [2, "1403", 41, 102, 18, 22, 0], [2, "1403", 31, 119, 18, 22, 0], [2, "1403", 24, 133, 18, 22, 0], [2, "1402", 37, 92, 14, 29, 0], [2, "1402", 26, 111, 14, 29, 0], [2, "1402", 19, 123, 14, 29, 0], [2, "1401", 125, 46, 62, 92, 0], [2, "1401", 101, 57, 62, 92, 0], [2, "1403", 85, 12, 18, 22, 0], [2, "1402", 81, 3, 14, 29, 0], [2, "1403", 75, 29, 18, 22, 0], [2, "1403", 68, 41, 18, 22, 0], [2, "1402", 68, 23, 14, 29, 0], [2, "1402", 63, 31, 14, 29, 0], [2, "1403", 258, 77, 18, 22, 2], [2, "1403", 267, 90, 18, 22, 2], [2, "1403", 276, 103, 18, 22, 2], [2, "1402", 266, 69, 14, 29, 2], [2, "1402", 276, 84, 14, 29, 2], [2, "1402", 283, 93, 14, 29, 2], [2, "1401", 187, 48, 62, 92, 2], [2, "1401", 204, 57, 62, 92, 2], [2, "1396", 160, 36, 50, 41, 2], [2, "1396", 184, 123, 50, 41, 0], [2, "818_1", 85, 33, 30, 37, 2], [2, "818_1", 93, 37, 30, 37, 2], [2, "818_1", 94, 59, 30, 37, 2], [2, "818_1", 75, 48, 30, 37, 2], [2, "1401", 76, 70, 62, 92, 0], [2, "1401", 51, 81, 62, 92, 0], [2, "1405", 51, 22, 44, 94, 0], [2, "818_1", 125, 60, 30, 37, 0], [2, "1396", 125, 52, 50, 41, 2], [2, "1401", 147, -34, 62, 92, 0], [2, "1401", 122, -22, 62, 92, 0], [2, "1401", 95, -9, 62, 92, 0], [2, "1279", 117, 157, 42, 58, 2]]}, {"type": 4, "obj": [[2, "1159_1", 704, 6, 36, 37, 2], [2, "1231_4", 459, -103, 114, 162, 0], [2, "1231_4", 721, -86, 114, 162, 0], [2, "1159_1", 506, 42, 36, 37, 2], [2, "1231_4", 171, -81, 114, 162, 0], [2, "1231_4", 533, -81, 114, 162, 0], [2, "1231_4", 533, -81, 114, 162, 0], [2, "1159_1", 295, 46, 36, 37, 2], [2, "1159_1", 788, 49, 36, 37, 2], [2, "1231_4", 578, -43, 114, 162, 0], [2, "1159_1", 605, 100, 36, 37, 2], [2, "1231_3", 968, 3, 114, 162, 0], [2, "1159_1", 496, 162, 36, 37, 2], [2, "1159_1", 986, 177, 36, 37, 0], [2, "1231_3", 869, 66, 114, 162, 0], [2, "1159_1", 879, 223, 36, 37, 2], [2, "1159_1", 929, 266, 36, 37, 2], [2, "1159_1", 1023, 287, 36, 37, 2], [2, "1231_3", 641, 165, 114, 162, 0], [2, "1159_1", 202, 390, 36, 37, 2], [2, "1231_3", 833, 276, 114, 162, 0], [2, "1159_1", 656, 415, 36, 37, 2], [2, "1231_3", 783, 312, 114, 162, 0], [2, "1231_3", 889, 315, 114, 162, 0], [2, "1231_4", -52, 325, 114, 162, 0], [2, "1231_3", -5, 347, 114, 162, 0], [2, "1159_1", 477, 478, 36, 37, 2], [2, "1231_4", 839, 369, 114, 162, 0], [2, "1231_4", 343, 392, 114, 162, 0], [2, "1159_1", 888, 519, 36, 37, 2], [2, "1159_1", 395, 537, 36, 37, 2], [2, "1159_1", 914, 538, 36, 37, 2], [2, "1159_1", 914, 538, 36, 37, 2], [2, "1182_1", 866, 541, 94, 46, 0], [2, "1159_1", 957, 563, 36, 37, 0], [2, "1159_1", 888, 570, 36, 37, 0], [2, "1159_1", 43, 582, 36, 37, 2], [2, "1159_1", 472, 590, 36, 37, 2], [2, "1231_4", 85, 478, 114, 162, 0], [2, "1231_4", 526, 517, 114, 162, 0], [2, "1231_3", 126, 518, 114, 162, 0], [2, "1159_1", 192, 658, 36, 37, 2], [2, "1231_4", 481, 541, 114, 162, 0], [2, "1159_1", 157, 671, 36, 37, 2], [2, "1159_1", 219, 678, 36, 37, 2], [2, "1159_1", 544, 695, 36, 37, 2], [2, "1412", 461, 735, 32, 28, 2], [2, "1231_4", 629, 615, 114, 162, 0], [2, "1159_1", 449, 742, 36, 37, 2], [2, "1159_1", 386, 751, 36, 37, 2], [2, "1182_1", 556, 757, 94, 46, 0], [2, "1332", 606, 754, 106, 57, 2], [2, "1159_1", 667, 780, 36, 37, 2], [2, "1159_1", 580, 786, 36, 37, 2], [2, "1332", 275, 769, 106, 57, 0], [2, "1231_4", 808, 733, 114, 162, 0], [2, "1159_1", 281, 877, 36, 37, 2], [2, "1231_4", 850, 769, 114, 162, 0], [2, "1159_1", 283, 908, 36, 37, 2], [2, "1231_3", 164, 791, 114, 162, 0], [2, "1159_1", 896, 918, 36, 37, 2]]}, {"type": 2, "data": [-1, -1, -1, 37, 37, 37, 37, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, -1, -1, -1, -1, -1, -1, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 44, 43, 43, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, 48, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 50, 43, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, 48, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, 43, 43, 43, 43, -1, -1, 41, 40, 40, -1, -1, -1, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, 41, 40, 44, 43, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, 44, 43, 49, 50, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 55, 49, 50, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 46, 46, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 52, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 40, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 43, 43, 43, 43, -1, -1, -1, -1, -1, -1, 40, 40, -1, 40, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 43, 43, 43, 43, 43, 43, 34, 39, -1, -1, 55, 55, 55, 49, 50, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 56, 55, 43, 43, 43, 43, 37, 42, -1, -1, 49, 50, 52, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 56, 43, 43, 50, 52, 46, 47, -1, -1, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 56, 49, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 34, 34, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, 48, 49, 43, 43, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 4, 5, -1, -1, -1, -1, 45, 46, 46, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 4, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 43, 55, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 22, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 34, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 55, 55, 54, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 50, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 49, 40, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 49, 49, 43, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 37, 37, 37, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 37, 37, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "1183_1", 886, 186, 58, 31, 0], [2, "1182_1", -14, 239, 94, 46, 0], [2, "1182_1", 41, 391, 94, 46, 0], [2, "1182_1", 120, 389, 94, 46, 0], [2, "1414", 209, 219, 46, 30, 0], [2, "1414", 244, 203, 46, 30, 0], [2, "1368_1", 702, 46, 50, 42, 0], [2, "1368_1", 727, 880, 50, 42, 0], [2, "1368_1", 252, 647, 50, 42, 0], [2, "1368_1", 569, 399, 50, 42, 0], [2, "1414_1", 919, 253, 46, 30, 0], [2, "1414", 62, 263, 46, 30, 2], [2, "1414", 89, 255, 46, 30, 0], [2, "1414", 186, 389, 46, 30, 0], [2, "1414", 779, 510, 46, 30, 0], [2, "1231_4", -44, 54, 114, 162, 0], [2, "902_1", 264, 189, 28, 25, 2], [2, "902_1", 153, 217, 28, 25, 2], [2, "902_2", 30, 225, 28, 25, 0], [2, "902_1", 125, 231, 28, 25, 2], [2, "902_2", 58, 239, 28, 25, 0], [2, "902_2", 69, 245, 28, 25, 0], [2, "902_1", 97, 245, 28, 25, 2], [2, "902_1", 174, 207, 28, 25, 2], [2, "902_1", 164, 212, 28, 25, 2], [2, "902_1", 237, 202, 28, 25, 2], [2, "902_1", 228, 207, 28, 25, 2], [2, "818_1", 30, 203, 30, 37, 2], [2, "818_1", 60, 218, 30, 37, 2], [2, "818_1", 60, 197, 30, 37, 2], [2, "818_1", 30, 182, 30, 37, 2], [2, "818_1", 60, 155, 30, 37, 2], [2, "818_1", 30, 140, 30, 37, 2], [2, "818_1", 30, 161, 30, 37, 2], [2, "818_1", 60, 176, 30, 37, 2], [2, "818_1", 30, 119, 30, 37, 2], [2, "818_1", 60, 134, 30, 37, 2], [2, "818_1", 44, 105, 30, 37, 2], [2, "818_1", 74, 120, 30, 37, 2], [2, "818_1", 50, 87, 30, 37, 2], [2, "923", 38, 126, 44, 125, 0], [2, "1463_1", 18, 193, 22, 59, 0], [2, "1208", 30, 218, 52, 56, 0], [2, "1463_1", 18, 147, 22, 59, 0], [2, "1463_1", 82, 222, 22, 59, 0], [2, "1463_1", 82, 176, 22, 59, 0], [2, "818_1", 162, 190, 30, 37, 0], [2, "902_1", 201, 208, 28, 25, 0], [2, "818_1", 134, 204, 30, 37, 0], [2, "818_1", 104, 219, 30, 37, 0], [2, "818_1", 162, 169, 30, 37, 0], [2, "818_1", 134, 183, 30, 37, 0], [2, "818_1", 104, 198, 30, 37, 0], [2, "818_1", 104, 177, 30, 37, 0], [2, "818_1", 134, 162, 30, 37, 0], [2, "818_1", 162, 148, 30, 37, 0], [2, "818_1", 104, 157, 30, 37, 0], [2, "818_1", 134, 142, 30, 37, 0], [2, "818_1", 162, 128, 30, 37, 0], [2, "1208", 104, 216, 52, 56, 2], [2, "818_1", 199, 185, 30, 37, 2], [2, "1208", 153, 191, 52, 56, 2], [2, "818_1", 199, 164, 30, 37, 2], [2, "818_1", 199, 143, 30, 37, 2], [2, "1463_1", 184, 175, 22, 59, 0], [2, "1463_1", 184, 129, 22, 59, 0], [2, "818_1", 231, 182, 30, 37, 0], [2, "818_1", 261, 168, 30, 37, 0], [2, "818_1", 231, 161, 30, 37, 0], [2, "818_1", 261, 147, 30, 37, 0], [2, "818_1", 261, 126, 30, 37, 0], [2, "818_1", 261, 105, 30, 37, 0], [2, "818_1", 231, 119, 30, 37, 0], [2, "818_1", 231, 140, 30, 37, 0], [2, "818_1", 236, 95, 30, 37, 0], [2, "818_1", 251, 89, 30, 37, 0], [2, "818_1", 240, 74, 30, 37, 0], [2, "1463_1", 196, 182, 22, 59, 0], [2, "1463_1", 196, 136, 22, 59, 0], [2, "1231_4", -34, -77, 114, 162, 0], [2, "1231_4", 12, -34, 114, 162, 0], [2, "325_3", 323, 256, 50, 37, 0], [2, "325_3", 352, 231, 50, 37, 0], [2, "1414", 120, 244, 46, 30, 0], [2, "1414", 870, 215, 46, 30, 0], [2, "1414", 216, 386, 46, 30, 0], [2, "1414", 135, 422, 46, 30, 0], [2, "1414", 172, 413, 46, 30, 0], [2, "1414", 148, 229, 46, 30, 0], [2, "1412", 138, 227, 32, 28, 0], [2, "1414", 238, 50, 46, 30, 0], [2, "1463_1", 280, 110, 22, 59, 0], [2, "1414", 831, 503, 46, 30, 2], [2, "1414", 870, 507, 46, 30, 2], [2, "1414", 877, 526, 46, 30, 2], [2, "1414", 875, 549, 46, 30, 2], [2, "1416", 848, 555, 24, 17, 0], [2, "1416", 782, 580, 24, 17, 0], [2, "1414", 521, 729, 46, 30, 2], [2, "1414", 486, 741, 46, 30, 0], [2, "1414", 530, 750, 46, 30, 2], [2, "1416", 447, 767, 24, 17, 0], [2, "1416", 507, 776, 24, 17, 0], [2, "1416", 479, 781, 24, 17, 2], [2, "1368_2", 328, 755, 50, 42, 0], [2, "1159_1", 437, 721, 36, 37, 0], [2, "1159_1", 766, 496, 36, 37, 0], [2, "1159_1", 759, 512, 36, 37, 2], [2, "1416", 814, 533, 24, 17, 0], [2, "1416", 818, 535, 24, 17, 0], [2, "1416", 221, 420, 24, 17, 0], [2, "1416", 163, 452, 24, 17, 0], [2, "1416", 53, 284, 24, 17, 0], [2, "1416", 332, 86, 24, 17, 0], [2, "1414", 229, 368, 46, 30, 0], [2, "1414", 237, 410, 46, 30, 0], [2, "1414", 40, 458, 46, 30, 2], [2, "1414", 8, 447, 46, 30, 2], [2, "1416", 403, 344, 24, 17, 0], [2, "1416", 461, 309, 24, 17, 0], [2, "1182_1", 325, 534, 94, 46, 0], [2, "1159_1", 446, 529, 36, 37, 0], [2, "1184_1", 331, 563, 50, 36, 0], [2, "1159_1", 312, 552, 36, 37, 0], [2, "1159_1", 365, 508, 36, 37, 0], [2, "1368_1", 692, 741, 50, 42, 0], [2, "1184_1", 152, 622, 50, 36, 0], [2, "1414_1", 174, 630, 46, 30, 0], [2, "1414_1", 141, 640, 46, 30, 2], [2, "1412_1", 198, 616, 32, 28, 2], [2, "1159_1", 170, 638, 36, 37, 0], [2, "1182_1", 550, 566, 94, 46, 0], [2, "1159_1", 561, 585, 36, 37, 2], [2, "1159_1", 627, 572, 36, 37, 0], [2, "1159_1", 577, 427, 36, 37, 2], [2, "1159_1", 587, 450, 36, 37, 2], [2, "1159_1", 677, 495, 36, 37, 2], [2, "1159_1", 782, 273, 36, 37, 2], [2, "1159_1", 617, 357, 36, 37, 0], [2, "1159_1", 326, 471, 36, 37, 0], [2, "1159_1", 781, 447, 36, 37, 0], [2, "1159_1", 887, 808, 36, 37, 0], [2, "1183_1", 912, 241, 58, 31, 0], [2, "1416", 731, 399, 24, 17, 0], [2, "1416", 556, 371, 24, 17, 0], [2, "1416", 877, 256, 24, 17, 0], [2, "1416", 996, 218, 24, 17, 0], [2, "1416", 1002, 560, 24, 17, 0], [2, "1416", 988, 662, 24, 17, 0], [2, "1416", 705, 709, 24, 17, 0], [2, "1416", 777, 791, 24, 17, 0], [2, "1416", 120, 821, 24, 17, 0], [2, "1416", 200, 657, 24, 17, 0], [2, "1416", 373, 608, 24, 17, 0], [2, "1416", 400, 454, 24, 17, 0], [2, "1416", 49, 528, 24, 17, 0], [2, "1416", 3, 584, 24, 17, 0], [2, "1159_1", 875, 121, 36, 37, 2], [2, "1159_1", 638, 194, 36, 37, 0], [2, "1159_1", 731, 67, 36, 37, 0], [2, "1159_1", 694, 65, 36, 37, 2], [2, "1159_1", 761, 9, 36, 37, 0], [2, "1416", 726, 13, 24, 17, 0], [2, "1416", 765, 215, 24, 17, 0], [2, "1416", 902, 91, 24, 17, 0], [2, "1414_1", 680, -12, 46, 30, 2], [2, "1414_1", 705, -7, 46, 30, 2], [2, "1414_1", 727, -13, 46, 30, 0], [2, "1368_1", 1017, 352, 50, 42, 0], [2, "1159_1", 262, 609, 36, 37, 2], [2, "1159_1", 226, 744, 36, 37, 2], [2, "1159_1", 395, 661, 36, 37, 0], [2, "1159_1", 370, 674, 36, 37, 0], [2, "1159_1", 27, 669, 36, 37, 0], [2, "1159_1", 262, 609, 36, 37, 2], [2, "1159_1", 317, 712, 36, 37, 0], [2, "1159_1", 288, 673, 36, 37, 0], [2, "1159_1", 256, 676, 36, 37, 0], [2, "1159_1", 287, 638, 36, 37, 2], [2, "1159_1", 348, 605, 36, 37, 2], [2, "1159_1", 388, 620, 36, 37, 2], [2, "1159_1", 414, 638, 36, 37, 2], [2, "1159_1", 2, 743, 36, 37, 0], [2, "1159_1", 1009, 596, 36, 37, 2], [2, "1159_1", 988, 610, 36, 37, 2], [2, "1159_1", 1011, 619, 36, 37, 2], [2, "1159_1", 997, 644, 36, 37, 2], [2, "1159_1", 1032, 650, 36, 37, 2], [2, "1159_1", 1001, 672, 36, 37, 0], [2, "1159_1", 92, 838, 36, 37, 0], [2, "1159_1", 551, 811, 36, 37, 2], [2, "1159_1", 582, 823, 36, 37, 2], [2, "1159_1", 667, 833, 36, 37, 2], [2, "1159_1", 704, 829, 36, 37, 2], [2, "1159_1", 714, 785, 36, 37, 0], [2, "1159_1", 676, 852, 36, 37, 0], [2, "1159_1", 679, 682, 36, 37, 0], [2, "1159_1", 724, 477, 36, 37, 0], [2, "1159_1", 997, 356, 36, 37, 2], [2, "1159_1", 1018, 383, 36, 37, 2], [2, "1159_1", 935, 255, 36, 37, 0], [2, "1159_1", 974, 223, 36, 37, 0], [2, "1159_1", 963, 176, 36, 37, 2], [2, "1159_1", 948, 197, 36, 37, 0], [2, "1414", 344, 57, 46, 30, 2], [2, "1414", 303, 54, 46, 30, 2], [2, "1416", 408, 108, 24, 17, 0], [2, "1159_1", 169, 399, 36, 37, 0], [2, "1159_1", 147, 419, 36, 37, 0], [2, "1159_1", 88, 416, 36, 37, 2], [2, "1159_1", 115, 417, 36, 37, 2], [2, "1159_1", -8, 397, 36, 37, 2], [2, "1159_1", 556, 917, 36, 37, 0], [2, "1159_1", 587, 905, 36, 37, 0], [2, "1159_1", 727, 907, 36, 37, 0], [2, "1368_1", 996, 850, 50, 42, 0], [2, "1159_1", 923, 903, 36, 37, 0], [2, "1159_1", 930, 812, 36, 37, 2], [2, "1159_1", 954, 831, 36, 37, 2], [2, "1159_1", 924, 827, 36, 37, 2], [2, "1159_1", 1020, 879, 36, 37, 2], [2, "1368_1", 1011, 624, 50, 42, 0], [2, "1159_1", 260, 864, 36, 37, 2], [2, "1159_1", 162, 817, 36, 37, 2], [2, "1416", 114, 900, 24, 17, 0], [2, "1159_1", 63, 908, 36, 37, 0], [2, "1159_1", 423, 421, 36, 37, 2], [2, "1159_1", 771, 167, 36, 37, 2], [2, "1159_1", 60, 532, 36, 37, 0], [2, "923", 238, 88, 44, 125, 2], [2, "891", 233, 142, 54, 75, 0], [2, "890", 214, 137, 94, 105, 0], [2, "1159_1", 324, 37, 36, 37, 2], [2, "1414", 272, 192, 46, 30, 0], [2, "1368_1", 928, 240, 50, 42, 0], [2, "1416", 252, 239, 24, 17, 0], [2, "1416", 290, 237, 24, 17, 0], [2, "1416", 360, 722, 24, 17, 0], [2, "1416", 416, 897, 24, 17, 0], [2, "1416", 641, 863, 24, 17, 0], [2, "1416", 704, 896, 24, 17, 0], [2, "1416", 974, 884, 24, 17, 0], [2, "1416", 933, 739, 24, 17, 0], [2, "1416", 746, 613, 24, 17, 0], [2, "1416", 604, 605, 24, 17, 0], [2, "1416", 492, 463, 24, 17, 0], [2, "1159_1", 791, 726, 36, 37, 2], [2, "1159_1", 877, 399, 36, 37, 2], [2, "1159_1", 903, 257, 36, 37, 2], [2, "1414", 259, 64, 46, 30, 2], [2, "1159_1", 282, 70, 36, 37, 2], [2, "1159_1", 264, 28, 36, 37, 2], [2, "1159_1", 297, 165, 36, 37, 2], [2, "1159_1", 173, 218, 36, 37, 2], [2, "1159_1", 276, 235, 36, 37, 2], [2, "325_3", 275, 222, 50, 37, 0], [2, "1414_1", 269, 231, 46, 30, 0], [2, "1416", 331, 228, 24, 17, 0], [2, "1159_1", 13, 237, 36, 37, 2], [2, "1159_1", -12, 209, 36, 37, 2], [2, "1159_1", 37, 239, 36, 37, 2], [2, "1159_1", 216, 364, 36, 37, 2], [2, "1159_1", 244, 341, 36, 37, 2], [2, "1183_1", 495, 38, 58, 31, 0], [2, "1159_1", 480, 39, 36, 37, 2], [2, "1159_1", 535, 38, 36, 37, 2], [2, "1159_1", 650, 112, 36, 37, 2], [2, "1159_1", 465, 146, 36, 37, 2], [2, "1159_1", 489, 121, 36, 37, 2], [2, "1159_1", 175, 292, 36, 37, 2], [2, "1159_1", 69, 347, 36, 37, 2], [2, "1159_1", 141, 370, 36, 37, 2], [2, "1368_1", 171, 364, 50, 42, 0], [2, "1184_1", 568, 679, 50, 36, 0], [2, "1183_1", 541, 679, 58, 31, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 4, 4, 13, 14, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 11, 10, 10, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 7, 11, 4, 4, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 10, 10, 10, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, 8, 7, 10, 16, 4, 4, 4, 4, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 35, -1, -1, -1, -1, 15, 16, 10, 10, 10, 10, 21, -1, -1, -1, -1, -1, -1, 8, 11, 10, 16, 4, 4, 4, 4, 4, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 50, 47, -1, -1, -1, -1, 12, 15, 10, 10, 16, 17, 14, -1, -1, -1, -1, -1, -1, 11, 4, 10, 16, 16, 4, 4, 4, 4, 4, 5, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 46, 46, 47, -1, -1, -1, -1, -1, -1, 15, 16, 17, 13, 14, -1, -1, -1, -1, -1, -1, -1, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 16, 4, 4, 4, 4, 4, 4, 4, 16, 17, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 12, 23, 16, 4, 4, 4, 4, 16, 17, 13, 14, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, -1, -1, -1, 12, 23, 4, 4, 4, 4, 17, 14, -1, -1, 11, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 48, 55, 55, -1, -1, -1, 20, 19, 23, 4, 4, 21, -1, -1, 0, 11, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 52, 52, -1, -1, -1, -1, -1, 15, 4, 4, 21, -1, -1, 3, 4, 4, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 6, -1, 15, 16, 4, -1, -1, -1, -1, 8, 7, 6, 10, 10, 10, 10, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 10, 4, 5, 1, 2, -1, -1, 8, 7, 7, 11, 4, 4, 4, 5, 2, 12, 23, 22, -1, -1, -1, -1, 16, 16, 16, -1, 10, 10, 16, 21, -1, -1, -1, -1, 8, 7, 6, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 5, 7, 7, 11, 10, 10, 16, 16, 16, 4, 4, 5, -1, 20, 19, -1, -1, -1, 10, 13, 13, 13, 23, 16, 17, 13, 14, -1, -1, -1, 8, 11, 10, 5, 1, 2, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 4, 4, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, 20, 13, 14, -1, -1, -1, -1, 8, 11, 4, 4, 4, 4, 5, 2, -1, -1, 23, 22, 22, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 4, 10, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 4, 4, 4, 4, 4, 4, 21, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 16, 4, 16, 16, 16, 16, 4, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 5, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 9, -1, 7, 7, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 5, 7, 7, 7, 2, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 9, -1, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 16, 16, 16, 4, 4, 4, 4, 10, 10, 2, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 4, 16, 9, -1, 4, 4, 4, 4, 4, 4, 5, 7, 4, 1, 2, -1, -1, 15, 16, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 22, 9, -1, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 16, 17, 19, 14, -1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 7, 6, 12, 23, 22, 4, 4, 4, 4, 4, 4, 4, 17, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, 20, 19, 13, 13, 13, 13, 14, 8, -1, -1, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 4, 10, 9, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 23, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 2, -1, -1, 20, 23, 22, 22, 22, 22, 22, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 16, 17, 19, 23, 22, 4, 22, 16, 9, -1, -1, 8, 11, 4, 4, 4, 4, 4, 4, 5, 7, 7, 7, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 4, 4, 4, 13, 14, -1, 20, 19, 4, 19, 13, 14, 8, 7, 11, 10, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 10, 16, 17, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, 4, 4, 4, 22, 22, 22, 16, 16, 16, 16, 16, 16, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 17, 13, 14, -1, -1, -1, -1, -1, 0, 1, 1, 11, 10, 4, 4, 4, 4, 4, 4, 16, 22, 22, 22, 22, 22, 16, 16, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 13, 14, -1, -1, -1, -1, -1, -1, -1, 15, 4, 4, 4, 4, 4, 4, 4, 4, 17, 23, 22, 4, 4, 4, 16, 16, 16, 4, 4, 4, 4, 4, 4, 16, 16, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 22, 4, 4, 4, 4, 4, 4, 17, 14, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 16, 16, 16, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 1, 2, 20, 19, 23, 22, 4, 4, 17, 13, 14, -1, -1, -1, 20, 19, 23, 22, 4, 4, 4, 4, 4, 4, 16, 16, 16, 16, 16, 16, 16, 16, 5, 6, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, 4, 5, 1, 2, 20, 19, 19, 19, 14, -1, -1, -1, -1, -1, -1, -1, 20, 19, 23, 22, 4, 4, 16, 16, 16, 17, 19, 13, 23, 16, 16, 16, 16, 5, 2, -1, -1, -1, -1, 0, 11, 10, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 20, 19, 4, 16, 16, 22, 22, 9, -1, -1, 20, 19, 19, 23, 16, 4, 5, 6, -1, 0, 1, 11, 4, 4, 4, 4, 4, 4, -1, 4, 4, 5, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 4, 4, 4, 4, 5, 2, -1, -1, -1, -1, 3, 4, 4, 4, 4, -1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, -1, -1, -1, -1, -1, 8, 7, 11, 10, 4, 4, 4, 4, 4, 4, 5, 1, 2, -1, -1, 3, 4, 4, 4, 4, 4, 11, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 2, -1, -1, -1, -1, -1, 8, 11, 10, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 7, 7, 11, 4, 4, 4, 4, 4]}, {"type": 2, "data": [24, 25, 26, 24, 25, 24, 25, 26, 24, 25, 24, 25, 26, 26, 24, 24, 25, 24, 25, 26, 24, 25, 24, 25, 26, 26, 24, 25, 24, 25, 26, 24, 25, 24, 25, 26, 24, 25, 26, 24, 24, 25, 26, 25, 27, 28, 29, 27, 28, 27, 28, 29, 27, 28, 27, 28, 29, 29, 27, 27, 28, 27, 28, 24, 25, 26, 27, 28, 29, 26, 27, 28, 27, 28, 29, 24, 25, 26, 28, 29, 27, 28, 24, 27, 27, 28, 29, 28, 30, 31, 24, 25, 26, 30, 31, 32, 30, 31, 30, 31, 32, 32, 30, 30, 31, 30, 31, 27, 28, 29, 30, 31, 32, 29, 30, 31, 30, 31, 32, 27, 28, 29, 31, 32, 30, 31, 24, 30, 30, 31, 32, 31, 24, 25, 27, 28, 24, 25, 26, 31, 24, 25, 26, 30, 31, 32, 30, 31, 32, 27, 28, 30, 31, 24, 25, 26, 25, 26, 27, 28, 29, 29, 30, 30, 31, 32, 24, 25, 26, 26, 27, 30, 31, 32, 24, 25, 27, 28, 30, 31, 27, 28, 29, 24, 27, 28, 29, 30, 31, 32, 32, 32, 29, 30, 31, 32, 24, 27, 28, 29, 28, 29, 30, 31, 32, 32, 24, 30, 31, 32, 27, 28, 29, 29, 24, 30, 24, 25, 27, 28, 24, 24, 25, 26, 30, 31, 32, 27, 30, 31, 32, 24, 24, 25, 26, 31, 24, 24, 25, 26, 27, 30, 31, 32, 31, 32, 30, 31, 32, 29, 27, 24, 25, 26, 30, 31, 32, 32, 27, 28, 27, 28, 30, 31, 24, 27, 28, 29, 24, 25, 26, 30, 24, 25, 26, 27, 27, 28, 29, 25, 27, 27, 28, 29, 30, 31, 32, 27, 28, 24, 25, 26, 25, 26, 24, 25, 26, 24, 25, 26, 27, 28, 30, 31, 30, 24, 25, 26, 27, 30, 31, 32, 27, 28, 29, 25, 27, 28, 29, 24, 25, 26, 32, 28, 30, 30, 24, 25, 26, 24, 25, 26, 26, 27, 28, 29, 24, 25, 27, 28, 29, 27, 28, 29, 30, 31, 32, 24, 25, 26, 25, 26, 30, 31, 32, 24, 30, 31, 24, 25, 26, 31, 32, 27, 28, 29, 29, 31, 32, 24, 25, 26, 29, 27, 24, 25, 24, 30, 31, 32, 27, 28, 30, 31, 32, 30, 24, 25, 26, 24, 25, 24, 25, 26, 26, 26, 24, 25, 24, 25, 26, 24, 27, 24, 25, 26, 28, 30, 31, 32, 32, 31, 32, 27, 28, 29, 32, 30, 27, 28, 27, 30, 31, 32, 30, 31, 32, 31, 30, 31, 27, 28, 29, 27, 28, 27, 28, 29, 29, 29, 27, 24, 27, 28, 29, 27, 30, 27, 28, 29, 25, 26, 25, 26, 25, 26, 26, 30, 31, 32, 32, 30, 30, 31, 30, 24, 25, 30, 27, 28, 29, 29, 24, 25, 30, 31, 32, 30, 31, 30, 31, 32, 32, 32, 30, 27, 30, 31, 32, 26, 31, 30, 31, 32, 28, 29, 28, 29, 28, 29, 29, 30, 31, 32, 24, 24, 25, 24, 25, 27, 28, 29, 30, 31, 32, 25, 27, 24, 25, 26, 26, 24, 24, 25, 26, 24, 25, 26, 24, 25, 26, 27, 28, 29, 32, 30, 24, 30, 31, 32, 31, 32, 31, 32, 32, 24, 25, 26, 27, 27, 28, 27, 28, 30, 31, 32, 32, 26, 24, 25, 30, 27, 28, 29, 29, 27, 27, 28, 29, 24, 24, 25, 27, 28, 29, 30, 31, 32, 26, 30, 27, 28, 29, 24, 25, 24, 24, 25, 26, 27, 28, 29, 30, 24, 25, 26, 24, 25, 26, 26, 28, 29, 27, 28, 30, 30, 24, 25, 26, 25, 26, 31, 32, 26, 24, 25, 30, 31, 32, 29, 27, 28, 24, 25, 26, 31, 32, 27, 28, 27, 27, 28, 29, 30, 24, 25, 24, 27, 28, 29, 27, 28, 29, 25, 26, 32, 30, 31, 32, 28, 27, 28, 29, 28, 29, 27, 24, 25, 27, 28, 30, 31, 32, 32, 30, 31, 27, 28, 29, 28, 29, 30, 31, 30, 30, 24, 25, 26, 27, 28, 27, 30, 31, 32, 30, 31, 32, 28, 24, 25, 24, 24, 25, 26, 30, 31, 32, 31, 24, 25, 26, 24, 25, 26, 24, 24, 25, 26, 25, 26, 30, 31, 32, 31, 32, 24, 25, 26, 26, 27, 24, 25, 26, 31, 30, 31, 32, 30, 31, 32, 30, 31, 27, 28, 27, 27, 28, 29, 24, 25, 26, 24, 25, 26, 24, 27, 28, 29, 27, 27, 24, 25, 26, 29, 27, 24, 25, 26, 25, 27, 28, 29, 29, 30, 27, 28, 29, 32, 24, 25, 26, 24, 24, 25, 24, 25, 26, 24, 30, 30, 31, 32, 26, 28, 29, 27, 28, 29, 27, 30, 31, 32, 30, 30, 24, 25, 26, 32, 30, 27, 28, 29, 28, 30, 31, 32, 24, 25, 30, 31, 32, 26, 27, 28, 24, 25, 26, 28, 27, 28, 29, 27, 30, 31, 27, 24, 25, 26, 24, 25, 26, 32, 30, 24, 25, 26, 24, 25, 26, 28, 24, 25, 26, 30, 31, 24, 25, 26, 24, 25, 26, 28, 29, 27, 28, 29, 30, 31, 27, 28, 29, 31, 30, 31, 32, 30, 31, 30, 30, 27, 28, 29, 25, 24, 25, 26, 24, 25, 26, 29, 27, 28, 29, 31, 27, 24, 25, 26, 24, 25, 26, 25, 27, 28, 29, 31, 32, 24, 25, 26, 24, 25, 26, 24, 25, 26, 30, 31, 32, 25, 24, 25, 26, 30, 31, 32, 28, 27, 24, 25, 26, 24, 25, 26, 24, 25, 24, 25, 30, 27, 28, 29, 27, 28, 29, 24, 24, 25, 26, 24, 25, 26, 26, 24, 25, 24, 25, 26, 25, 26, 24, 24, 25, 26, 27, 28, 29, 24, 25, 26, 31, 30, 27, 28, 29, 27, 28, 29, 24, 25, 27, 24, 25, 30, 31, 24, 25, 24, 25, 26, 27, 28, 29, 27, 28, 29, 29, 27, 24, 27, 28, 29, 28, 29, 27, 24, 25, 26, 30, 31, 32, 27, 24, 24, 25, 26, 26, 24, 25, 26, 25, 26, 27, 24, 25, 26, 28, 29, 24, 25, 26, 27, 28, 29, 26, 25, 26, 24, 25, 26, 24, 25, 26, 30, 31, 32, 24, 25, 26, 26, 25, 26, 30, 31, 32, 30, 27, 24, 25, 26, 24, 25, 26, 29, 28, 29, 24, 25, 26, 29, 24, 24, 25, 26, 29, 30, 31, 32, 29, 28, 29, 27, 28, 29, 27, 28, 29, 31, 24, 25, 27, 28, 29, 29, 26, 29, 24, 25, 26, 26, 24, 25, 26, 29, 27, 28, 29, 32, 31, 32, 27, 28, 29, 32, 27, 27, 28, 29, 32, 30, 31, 24, 25, 26, 32, 30, 31, 24, 30, 31, 32, 26, 27, 28, 30, 31, 32, 32, 26, 32, 27, 28, 29, 24, 25, 26, 29, 32, 30, 31, 24, 25, 24, 25, 30, 31, 32, 26, 30, 30, 31, 32, 27, 28, 30, 27, 28, 24, 25, 26, 29, 27, 24, 25, 26, 29, 30, 31, 24, 25, 26, 24, 24, 25, 26, 31, 32, 27, 28, 29, 32, 24, 25, 26, 24, 25, 26, 28, 27, 27, 28, 29, 24, 27, 28, 29, 30, 31, 32, 30, 31, 27, 28, 29, 24, 25, 27, 28, 29, 32, 24, 24, 27, 28, 29, 27, 27, 28, 29, 26, 24, 30, 31, 32, 24, 27, 28, 29, 27, 28, 29, 31, 30, 30, 31, 32, 27, 24, 25, 26, 26, 24, 30, 24, 25, 26, 24, 25, 26, 28, 30, 31, 32, 25, 27, 27, 30, 31, 32, 30, 30, 31, 32, 29, 24, 25, 26, 25, 26, 24, 25, 26, 26, 31, 32, 25, 24, 25, 24, 25, 26, 26, 28, 29, 26, 27, 28, 27, 28, 29, 27, 28, 29, 31, 32, 29, 27, 28, 30, 30, 31, 32, 30, 31, 32, 24, 25, 26, 27, 28, 29, 28, 29, 27, 28, 29, 29, 25, 26, 26, 24, 25, 27, 28, 29, 29, 31, 24, 25, 26, 25, 30, 31, 32, 30, 31, 32, 24, 25, 26, 30, 31, 32, 24, 25, 26, 24, 25, 26, 27, 28, 29, 30, 31, 32, 31, 32, 30, 31, 32, 32, 26, 29, 29, 27, 28, 30, 31, 32, 32, 30, 27, 24, 25, 26, 29, 28, 30, 31, 32, 24, 27, 28, 29, 24, 25, 26, 27, 28, 29, 27, 24, 25, 26, 31, 24, 25, 26, 27, 30, 31, 32, 30, 31, 32, 24, 25, 26, 30, 31, 32, 24, 25, 26, 32, 30, 27, 28, 29, 32, 31, 32, 24, 25, 24, 25, 26, 24, 25, 26, 24, 30, 31, 32, 30, 27, 28, 29, 24, 25, 26, 24, 25, 26, 24, 25, 26, 25, 26, 27, 28, 29, 24, 25, 26, 27, 28, 29, 26, 26, 30, 31, 32, 26, 30, 31, 27, 28, 24, 25, 26, 24, 25, 26, 27, 28, 29, 28, 29, 30, 31, 32, 27, 28, 29, 24, 25, 26, 27, 28, 29, 28, 29, 30, 31, 32, 24, 25, 26, 30, 31, 32, 24, 24, 25, 27, 28, 29, 24, 25, 30, 31, 27, 28, 29, 27, 28, 29, 30, 31, 32, 24, 25, 26, 24, 25, 30, 31, 32, 27, 28, 29, 26, 31, 32, 31, 32, 24, 25, 26, 27, 28, 29, 24, 25, 24, 25, 27, 28, 30, 31, 32, 27, 28, 29, 28, 30, 31, 32, 30, 31, 32, 25, 26, 24, 27, 28, 29, 27, 28, 29, 27, 28, 30, 31, 32, 29, 24, 25, 26, 27, 27, 28, 29, 30, 31, 32, 27, 28, 27, 28, 30, 31, 32, 28, 30, 30, 31, 32, 31, 32, 30, 31, 32, 29, 27, 28, 29, 27, 30, 31, 32, 30, 31, 32, 30, 31, 32, 30, 31, 32, 27, 28, 29, 30, 30, 31, 32, 24, 25, 24, 25, 24, 24, 25, 26, 24, 24, 25, 26, 24, 24, 25, 26, 24, 24, 25, 24, 25, 26, 24, 24, 25, 26, 24, 25, 24, 25, 26, 25, 24, 25, 26, 25, 26, 24, 25, 26, 24, 25, 26, 26, 27, 28, 27, 28, 27, 27, 28, 29, 27, 27, 28, 29, 27, 27, 28, 29, 24, 25, 26, 27, 24, 24, 27, 27, 28, 29, 27, 28, 27, 28, 29, 28, 27, 28, 29, 28, 29, 27, 28, 29, 27, 24, 25, 26, 24, 25, 26, 31, 24, 30, 31, 32, 30, 30, 31, 32, 24, 25, 26, 26, 24, 24, 25, 26, 24, 25, 26, 24, 25, 26, 24, 25, 26, 24, 25, 26, 30, 24, 25, 26, 24, 25, 26, 24, 30, 27, 28, 29]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}