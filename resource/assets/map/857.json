{"mW": 1440, "mH": 840, "tW": 24, "tH": 24, "tiles": [["1317", 0, 3, 2], ["1317", 2, 3, 2], ["1317", 1, 3, 2], ["1317", 3, 3, 2], ["3775", 0, 1, 1], ["3778", 0, 3, 3], ["3861", 0, 1, 1], ["3862", 0, 1, 1], ["3863", 0, 1, 1], ["3864", 0, 1, 1], ["3865", 0, 1, 1], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "3796", 1344, 37, 58, 43, 0], [2, "3826", 786, 415, 24, 35, 0], [2, "3826", 682, 370, 24, 35, 0], [2, "3825", 682, 347, 24, 35, 0], [2, "3825", 786, 392, 24, 35, 0], [2, "3794", 1229, 572, 84, 64, 0], [2, "3781", 1220, -29, 32, 62, 0], [2, "3781", 1430, -3, 32, 62, 0], [2, "3781", 1430, -48, 32, 62, 0], [2, "3808", 1201, 14, 18, 18, 0], [2, "3808", 1339, 82, 18, 18, 0], [2, "3859", 1185, -2, 42, 44, 0], [2, "3859", 1323, 66, 42, 44, 0], [2, "3781", 1352, -2, 32, 62, 0], [2, "3781", 1352, -47, 32, 62, 0], [2, "3860", 970, 52, 42, 44, 0], [2, "3860", 1251, 208, 42, 44, 0], [2, "3795", 1337, 530, 52, 37, 0], [2, "3795", 1301, 548, 52, 37, 0], [2, "3795", 1264, 568, 52, 37, 0], [2, "3795", 1352, 543, 52, 37, 0], [2, "3795", 1315, 563, 52, 37, 0], [2, "3795", 1278, 583, 52, 37, 0], [2, "3795", 1367, 559, 52, 37, 0], [2, "3795", 1329, 577, 52, 37, 0], [2, "3795", 1293, 600, 52, 37, 0], [2, "3796", 1376, 568, 58, 43, 0], [2, "3796", 1340, 587, 58, 43, 0], [2, "3796", 1301, 607, 58, 43, 0], [2, "3200", 1356, 505, 24, 37, 0], [2, "3199", 1333, 531, 30, 23, 0], [2, "3199", 1311, 541, 30, 23, 0], [2, "3199", 1287, 553, 30, 23, 0], [2, "3199", 1265, 565, 30, 23, 0], [2, "3198", 1254, 554, 28, 38, 0], [2, "3156", 650, 258, 194, 149, 0], [2, "3821", 258, 759, 90, 132, 0], [2, "3860", 237, 666, 42, 44, 0], [2, "3860", 251, 635, 42, 44, 0], [2, "3860", 469, 74, 42, 44, 0], [2, "3860", 451, 39, 42, 44, 0], [2, "3859", 438, 97, 42, 44, 0], [2, "3857", 424, 11, 72, 99, 0], [2, "3796", 1215, -26, 58, 43, 2], [2, "3796", 1252, -6, 58, 43, 2], [2, "3796", 1290, 15, 58, 43, 2], [2, "3796", 1327, 35, 58, 43, 2], [2, "3796", 1235, -41, 58, 43, 2], [2, "3796", 1272, -21, 58, 43, 2], [2, "3796", 1310, 0, 58, 43, 2], [2, "3796", 1347, 20, 58, 43, 2], [2, "3796", 1261, -60, 58, 43, 2], [2, "3796", 1298, -40, 58, 43, 2], [2, "3796", 1336, -19, 58, 43, 2], [2, "3796", 1373, 1, 58, 43, 2], [2, "3796", 1446, 43, 58, 43, 2], [2, "3796", 1284, -79, 58, 43, 2], [2, "3796", 1321, -59, 58, 43, 2], [2, "3796", 1359, -38, 58, 43, 2], [2, "3796", 1396, -18, 58, 43, 2], [2, "3796", 1469, 24, 58, 43, 2], [2, "3796", 1416, -3, 58, 43, 0], [2, "3796", 1380, 17, 58, 43, 0]]}, {"type": 4, "obj": [[2, "3809", 1335, 50, 14, 14, 0], [2, "3857", 846, -8, 72, 99, 0], [2, "3857", 868, -7, 72, 99, 0], [2, "3681", 1190, 26, 38, 76, 0], [2, "3781", 1352, 43, 32, 62, 0], [4, 11, 421, 106, 0, 4044], [2, "3804", 976, 73, 70, 51, 0], [2, "3798", 1267, 47, 78, 77, 0], [4, 16, 881, 128, 0, 4045], [2, "3802", 1316, 111, 34, 32, 0], [2, "3681", 1328, 93, 38, 76, 0], [2, "1645", 803, 83, 52, 88, 0], [2, "3824", 450, 82, 52, 107, 0], [4, 23, 1347, 193, 0, 4044], [2, "3823", 430, 157, 34, 41, 0], [2, "3823", 490, 157, 34, 41, 2], [2, "3857", 705, 108, 72, 99, 0], [2, "3806", 278, 175, 26, 41, 0], [4, 12, 230, 232, 0, 4045], [2, "3853", 91, 199, 38, 39, 0], [2, "3854", 392, 193, 32, 47, 0], [4, 4, 595, 241, 0, 4045], [2, "3857", 382, 145, 72, 99, 0], [2, "3803", 166, 195, 40, 52, 0], [2, "3846", 183, 233, 60, 31, 0], [2, "3857", 115, 168, 72, 99, 0], [2, "3804", 1259, 232, 70, 51, 0], [2, "3833", 405, 261, 36, 26, 0], [4, 21, 795, 298, 0, 4044], [2, "3852", 160, 265, 42, 48, 0], [2, "3139", 837, 238, 52, 83, 0], [2, "3838", 184, 291, 38, 31, 0], [2, "3833", 11, 312, 36, 26, 0], [2, "3835", 321, 266, 32, 77, 0], [2, "3854", 27, 309, 32, 47, 0], [4, 8, 1165, 374, 0, 4045], [4, 9, 297, 377, 0, 4044], [2, "3853", 262, 349, 38, 39, 0], [2, "3139", 989, 320, 52, 83, 0], [2, "3806", 1026, 373, 26, 41, 0], [2, "3835", 1255, 338, 32, 77, 0], [4, 19, 1319, 417, 0, 4044], [4, 13, 554, 418, 0, 4044], [2, "3825", 681, 392, 24, 35, 0], [2, "3803", 639, 377, 40, 52, 0], [4, 15, 950, 429, 0, 4044], [2, "3835", 1396, 364, 32, 77, 0], [2, "3823", 666, 413, 34, 41, 0], [2, "3853", 893, 419, 38, 39, 0], [2, "3806", 935, 419, 26, 41, 0], [2, "3855", 151, 407, 38, 56, 0], [2, "3825", 786, 438, 24, 35, 0], [4, 7, 72, 493, 0, 4044], [4, 5, 366, 494, 0, 4044], [2, "3852", 831, 447, 42, 48, 0], [2, "3823", 767, 463, 34, 41, 0], [2, "3803", 798, 459, 40, 52, 0], [4, 20, 1052, 513, 0, 4045], [2, "3857", 994, 429, 72, 99, 0], [2, "3837", 845, 493, 76, 42, 0], [2, "3806", 1130, 519, 26, 41, 0], [2, "3857", 1018, 478, 72, 99, 0], [2, "3857", 1018, 478, 72, 99, 0], [4, 1, 725, 583, 0, 4044], [2, "3835", 233, 510, 32, 77, 0], [2, "3806", 212, 552, 26, 41, 2], [2, "3849", 219, 576, 34, 25, 0], [2, "3833", 587, 600, 36, 26, 0], [2, "3834", 604, 615, 36, 26, 0], [2, "3821", 397, 519, 90, 132, 2], [2, "3790", 1413, 583, 20, 74, 0], [4, 2, 938, 661, 0, 4045], [2, "3790", 1227, 599, 20, 74, 0], [4, 17, 527, 674, 0, 4044], [4, 6, 206, 683, 0, 4045], [4, 10, 1178, 701, 0, 4045], [2, "3853", 229, 666, 38, 39, 0], [2, "3838", 476, 674, 38, 31, 0], [2, "3855", 1134, 650, 38, 56, 0], [2, "3855", 126, 654, 38, 56, 0], [2, "3790", 1302, 636, 20, 74, 0], [2, "3833", 139, 685, 36, 26, 0], [2, "1645", 248, 623, 52, 88, 0], [2, "3855", 278, 668, 38, 56, 0], [2, "3853", 325, 685, 38, 39, 0], [2, "3842", 231, 697, 58, 31, 0], [2, "3846", 455, 697, 60, 31, 0], [2, "3853", 122, 692, 38, 39, 0], [2, "3852", 199, 685, 42, 48, 0], [4, 14, 614, 738, 0, 4045], [2, "3849", 247, 715, 34, 25, 0], [2, "3832", 221, 724, 30, 20, 0], [2, "3855", 623, 688, 38, 56, 0], [2, "3837", 276, 705, 76, 42, 0], [2, "3835", 1192, 673, 32, 77, 0], [2, "3857", 143, 656, 72, 99, 0], [2, "3852", 43, 710, 42, 48, 0], [2, "3840", 583, 724, 28, 38, 0], [2, "3835", 1388, 690, 32, 77, 0], [2, "3851", 413, 738, 44, 32, 0], [2, "3854", 532, 742, 32, 47, 0], [4, 3, 778, 794, 0, 4044], [2, "3857", 803, 724, 72, 99, 0], [2, "3857", 1229, 726, 72, 99, 0], [2, "3857", 832, 743, 72, 99, 0]]}, {"type": 3, "obj": [[2, "1645", 61, 495, 52, 88, 0], [2, "3853", 571, 381, 38, 39, 0], [2, "3849", 655, 358, 34, 25, 0], [2, "3818", 1359, 457, 36, 48, 2], [2, "3844", 1005, 772, 78, 71, 0], [2, "3850", 690, 357, 34, 25, 0], [2, "3849", 797, 305, 34, 25, 0], [2, "3849", 752, 330, 34, 25, 0], [2, "3835", 1327, 414, 32, 77, 0], [2, "3846", 1290, 467, 60, 31, 0], [2, "3827", 865, 142, 32, 30, 0], [2, "3814", 804, 168, 50, 34, 0], [2, "3833", 860, 23, 36, 26, 0], [2, "3834", 908, -1, 36, 26, 0], [2, "3828", 889, -3, 22, 38, 0], [2, "3847", 950, 8, 62, 63, 0], [2, "1646", 847, 151, 26, 28, 0], [2, "3853", 995, 38, 38, 39, 0], [2, "3853", 1353, 242, 38, 39, 0], [2, "3852", 1373, 246, 42, 48, 0], [2, "3859", 810, 167, 42, 44, 0], [2, "3860", 745, 21, 42, 44, 0], [2, "3860", 1203, 439, 42, 44, 0], [2, "3860", 1219, 411, 42, 44, 0], [2, "3816", 1203, 374, 22, 17, 0], [2, "3810", 1229, 379, 20, 10, 0], [2, "3833", 872, 23, 36, 26, 0], [2, "3833", 1121, 556, 36, 26, 0], [2, "3852", 1077, 559, 42, 48, 0], [2, "3814", 1378, 502, 50, 34, 0], [2, "3807", 1333, 471, 42, 40, 0], [2, "3827", 1285, 481, 32, 30, 0], [2, "3838", 1231, 505, 38, 31, 0], [2, "3835", 723, 43, 32, 77, 0], [2, "3833", 760, 180, 36, 26, 0], [2, "3851", 713, 335, 44, 32, 0], [2, "3832", 1040, 609, 30, 20, 0], [2, "3835", 1008, 573, 32, 77, 0], [2, "3827", 810, 288, 32, 30, 0], [2, "3853", 771, 303, 38, 39, 0], [2, "3860", 692, 200, 42, 44, 0], [2, "3855", 1237, 722, 38, 56, 0], [2, "3847", 1265, 738, 62, 63, 0], [2, "3849", 651, 376, 34, 25, 0], [2, "3849", 900, 438, 34, 25, 0], [2, "3849", 876, 451, 34, 25, 0], [2, "3850", 853, 459, 34, 25, 0], [2, "3854", 678, 317, 32, 47, 0], [2, "3847", 948, 725, 62, 63, 0], [2, "3837", 879, 767, 76, 42, 0], [2, "3844", 926, 772, 78, 71, 2], [2, "3855", 843, 789, 38, 56, 0], [2, "3846", 864, 814, 60, 31, 0], [2, "3846", 907, 819, 60, 31, 0], [2, "3846", 907, 819, 60, 31, 0], [2, "3841", 977, 754, 76, 41, 0], [2, "3845", 974, 786, 58, 62, 0], [2, "3842", 1002, 726, 58, 31, 0], [2, "3843", 1043, 740, 50, 28, 0], [2, "3837", 1052, 801, 76, 42, 0], [2, "3840", 1041, 708, 28, 38, 0], [2, "3840", 1082, 769, 28, 38, 2], [2, "3852", 1113, 799, 42, 48, 0], [2, "3815", 925, 726, 20, 17, 0], [2, "3816", 1064, 735, 22, 17, 0], [2, "3807", 1027, 788, 42, 40, 0], [2, "3802", 954, 765, 34, 32, 0], [2, "3859", 947, 762, 42, 44, 0], [2, "3860", 1021, 732, 42, 44, 0], [2, "3812", 1094, 620, 24, 19, 0], [2, "3833", 888, 707, 36, 26, 0], [2, "3834", 1004, 706, 36, 26, 0], [2, "3826", 681, 414, 24, 35, 0], [2, "3826", 786, 461, 24, 35, 0], [2, "3835", 632, 340, 32, 77, 0], [2, "3801", 771, 105, 26, 23, 0], [2, "3805", 1341, 102, 98, 117, 2], [2, "3799", 853, 596, 64, 42, 0], [2, "3833", 863, 577, 36, 26, 0], [2, "3840", 906, 569, 28, 38, 2], [2, "3846", 1163, 743, 60, 31, 0], [2, "3846", 1303, 677, 60, 31, 0], [2, "3849", 1395, 644, 34, 25, 0], [2, "3849", 1370, 655, 34, 25, 0], [2, "3849", 1370, 655, 34, 25, 0], [2, "3788", 1280, 628, 30, 73, 0], [2, "3788", 1250, 613, 30, 73, 0], [2, "3788", 1240, 608, 30, 73, 0], [2, "3788", 1310, 628, 30, 73, 2], [2, "3788", 1339, 613, 30, 73, 2], [2, "3788", 1369, 598, 30, 73, 2], [2, "3788", 1399, 583, 30, 73, 2], [2, "3850", 1348, 668, 34, 25, 0], [2, "3853", 1380, 642, 38, 39, 0], [2, "3849", 1236, 662, 34, 25, 2], [2, "3849", 1260, 675, 34, 25, 2], [2, "3849", 1277, 690, 34, 25, 2], [2, "3833", 1257, 660, 36, 26, 0], [2, "3811", 850, 423, 36, 26, 0], [2, "3810", 876, 342, 20, 10, 0], [2, "3816", 1403, 686, 22, 17, 0], [2, "3842", 1188, 766, 58, 31, 0], [2, "3843", 1227, 777, 50, 28, 0], [2, "3846", 1382, 754, 60, 31, 0], [2, "3853", 1415, 727, 38, 39, 0], [2, "3833", 1119, 342, 36, 26, 0], [2, "3828", 1406, 267, 22, 38, 0], [2, "3827", 1353, 270, 32, 30, 0], [2, "3838", 1373, 274, 38, 31, 0], [2, "3799", 803, 660, 64, 42, 0], [2, "3837", 699, 786, 76, 42, 0], [2, "3845", 506, 803, 58, 62, 0], [2, "3845", 676, 799, 58, 62, 0], [2, "3846", 727, 815, 60, 31, 0], [2, "3859", 904, 773, 42, 44, 0], [2, "3860", 909, 547, 42, 44, 0], [2, "3860", 424, 455, 42, 44, 0], [2, "3827", 905, 797, 32, 30, 0], [2, "3841", 630, 709, 76, 41, 0], [2, "3832", 538, 523, 30, 20, 0], [2, "3849", 501, 692, 34, 25, 0], [2, "3853", 226, 806, 38, 39, 0], [2, "1646", 580, 617, 26, 28, 0], [2, "1646", 74, 611, 26, 28, 0], [2, "1646", 179, 505, 26, 28, 0], [2, "1646", 933, 792, 26, 28, 0], [2, "1646", 1157, 726, 26, 28, 0], [2, "1646", 1212, 741, 26, 28, 0], [2, "1646", 1363, 763, 26, 28, 0], [2, "1646", 1090, 399, 26, 28, 0], [2, "1646", 1304, 394, 26, 28, 0], [2, "1646", 946, 41, 26, 28, 0], [2, "3849", 777, 157, 34, 25, 0], [2, "3811", 580, 550, 36, 26, 0], [2, "3813", 504, 623, 48, 36, 0], [2, "3812", 134, 781, 24, 19, 0], [2, "3811", 96, 647, 36, 26, 0], [2, "3810", 620, 542, 20, 10, 0], [2, "3810", 552, 739, 20, 10, 0], [2, "3810", 213, 631, 20, 10, 0], [2, "3815", 62, 631, 20, 17, 0], [2, "3816", 352, 766, 22, 17, 0], [2, "3808", 169, 579, 18, 18, 0], [2, "3809", 55, 654, 14, 14, 0], [2, "3814", 89, 361, 50, 34, 0], [2, "3802", 291, 473, 34, 32, 0], [2, "3821", 88, 499, 90, 132, 2], [2, "3859", 129, 582, 42, 44, 0], [2, "3860", 26, 402, 42, 44, 0], [2, "3860", 321, 336, 42, 44, 0], [2, "3845", 325, 498, 58, 62, 2], [2, "3821", 317, 500, 90, 132, 0], [2, "3859", 325, 584, 42, 44, 0], [2, "3860", 838, 738, 42, 44, 0], [2, "3860", 305, 32, 42, 44, 0], [2, "3859", 502, 420, 42, 44, 0], [2, "3821", 88, 19, 90, 132, 2], [2, "3821", 24, 47, 90, 132, 0], [2, "3859", 33, 132, 42, 44, 0], [2, "3859", 124, 70, 42, 44, 0], [2, "3860", 129, 101, 42, 44, 0], [2, "1646", 161, 238, 26, 28, 0], [2, "1646", 6, 136, 26, 28, 0], [2, "1646", 302, 180, 26, 28, 0], [2, "3810", 1117, 609, 20, 10, 0], [2, "3846", 396, 277, 60, 31, 0], [2, "3850", 371, 295, 34, 25, 0], [2, "3802", 111, 141, 34, 32, 2], [2, "3847", 534, 57, 62, 63, 0], [2, "3833", 604, 113, 36, 26, 0], [2, "3834", 598, 143, 36, 26, 0], [2, "3840", 643, 143, 28, 38, 2], [2, "3840", 599, 57, 28, 38, 2], [2, "3799", 389, 380, 64, 42, 0], [2, "3840", 433, 352, 28, 38, 2], [2, "3841", 344, 34, 76, 41, 0], [2, "3828", 391, 84, 22, 38, 0], [2, "3829", 380, 106, 18, 30, 0], [2, "3830", 577, 151, 16, 27, 0], [2, "3852", 478, 39, 42, 48, 0], [2, "3835", 528, -1, 32, 77, 0], [2, "3844", 429, 28, 78, 71, 2], [2, "3842", 326, 67, 58, 31, 0], [2, "3853", 554, 116, 38, 39, 0], [2, "3849", 363, 235, 34, 25, 0], [2, "3855", 424, 491, 38, 56, 0], [2, "3802", 401, 370, 34, 32, 0], [2, "3813", 49, 272, 48, 36, 0], [2, "3811", 225, 349, 36, 26, 0], [2, "3812", 209, 175, 24, 19, 0], [2, "3811", 546, 177, 36, 26, 0], [2, "3812", 507, 94, 24, 19, 0], [2, "1645", 334, 102, 52, 88, 2], [2, "3838", 333, 171, 38, 31, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 61, -1, 46, 46, 45, -1, -1, -1, -1, 51, 61, 49, 49, 49, 49, 49, 49, 49, 49, 49, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 56, 39, 55, 55, 55, 55, 55, 62, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 62, 61, 52, 53, -1, -1, -1, -1, 59, 58, 62, 61, 49, -1, 49, 43, 43, 49, 49, 47, -1, -1, 49, 49, 43, 43, 40, 40, 40, 41, -1, -1, -1, 47, 46, 55, 56, 52, 52, 52, 52, 52, 59, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 62, 61, -1, 49, 49, 49, 49, 49, 49, 49, 49, -1, 43, 43, 43, 55, 56, 53, -1, -1, -1, 50, 49, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 55, 44, 46, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 62, 61, 49, 49, 49, 62, 61, 49, 49, 49, 49, 55, 56, 52, 53, -1, -1, -1, -1, -1, 55, 53, -1, -1, -1, -1, -1, -1, -1, 47, 46, 45, -1, -1, -1, -1, 42, 55, 55, 55, 56, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, -1, -1, -1, 59, 58, 62, 61, 55, 56, 52, 53, -1, -1, -1, 62, 47, 46, 45, 48, -1, -1, -1, -1, -1, -1, -1, -1, 54, 49, 48, -1, -1, -1, -1, 42, 55, 56, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 52, 53, -1, -1, -1, -1, -1, 59, 42, 55, 55, 48, -1, -1, -1, -1, -1, -1, -1, -1, 54, 49, 40, 41, -1, -1, -1, -1, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 40, 40, 41, -1, -1, 51, 52, 52, 53, -1, -1, -1, -1, -1, -1, 47, 46, 50, 55, 43, 44, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 55, 55, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 50, 49, 49, 55, 55, 61, 43, 44, 40, 46, 46, 46, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 40, 40, 41, -1, -1, -1, -1, -1, -1, -1, 51, 52, 52, 53, -1, 8, 7, 6, -1, -1, -1, 39, 40, 46, 45, 42, 43, 43, 55, 56, 52, 58, 62, 61, 43, 55, 49, 55, 44, 45, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 61, 43, 44, -1, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, 8, 11, 10, 9, -1, -1, -1, 42, 43, 49, 48, -1, 52, 52, 52, 53, -1, -1, 59, 62, 61, 49, 49, 55, 56, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 62, 61, 60, 43, 44, 41, 0, 1, 1, 1, 2, -1, -1, 12, 13, 19, 18, -1, -1, -1, 59, 52, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, -1, -1, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 53, 43, 43, 48, 20, 13, 13, 23, 5, 7, 6, -1, 47, 50, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 48, -1, -1, -1, 20, 23, 22, 21, -1, 50, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 46, 40, 41, -1, -1, 47, 46, 46, 46, 45, -1, -1, -1, -1, 49, 49, 48, -1, -1, -1, -1, 15, 22, 21, 54, 50, 56, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 40, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 61, 43, 44, 46, 46, 50, 49, 55, 56, 48, -1, -1, -1, -1, 62, 61, 44, 40, 41, -1, -1, 20, 19, 18, 54, 43, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, 43, 55, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 62, 61, 43, 49, 55, 56, 52, 53, -1, -1, -1, -1, -1, 59, 58, 62, 61, 48, -1, -1, -1, -1, -1, 54, 43, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 46, 42, 49, 55, 62, 61, 43, 44, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 52, 52, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 49, 46, 46, 40, 41, -1, -1, 51, 62, 44, 45, -1, -1, -1, -1, -1, -1, -1, 47, 50, 49, 50, 49, 60, 59, 58, 62, 61, 43, 44, 40, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 49, 49, 49, 43, 44, -1, -1, -1, 59, 62, 44, 45, -1, -1, -1, -1, -1, -1, 54, 43, 43, 55, 56, 53, -1, -1, 59, 58, 62, 61, 43, 44, 48, 39, 46, 46, 46, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 61, 55, 56, -1, -1, 47, 46, 50, 49, 49, 49, -1, 56, -1, -1, -1, -1, 59, 58, -1, -1, -1, -1, 40, 41, -1, 51, 55, 56, 52, 53, -1, -1, -1, -1, 47, 50, 61, 49, 55, 48, 42, 55, 56, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 52, 53, -1, -1, 50, 49, 43, 43, -1, -1, 48, 48, -1, -1, -1, -1, -1, 42, -1, -1, -1, -1, 43, 44, -1, -1, 52, 53, -1, -1, -1, -1, -1, -1, 54, 49, 49, 49, 55, 56, -1, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, -1, -1, -1, 48, -1, -1, -1, -1, 39, 50, -1, -1, -1, -1, -1, -1, 40, 45, -1, -1, 8, 7, 7, 7, 2, -1, 54, 49, 49, 56, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, 43, -1, -1, -1, 48, -1, -1, -1, -1, 54, 55, 55, -1, -1, -1, -1, -1, 43, 48, -1, -1, 15, 16, 16, 16, 9, -1, 54, 55, 56, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 43, 43, -1, -1, 55, 56, 53, -1, -1, -1, -1, 51, 62, 43, 43, -1, -1, 43, 55, 56, 53, 8, 7, 11, 16, 17, 19, 14, -1, 54, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 41, 43, 43, 43, 55, 56, 52, 53, -1, -1, -1, -1, -1, -1, 51, 52, 52, 62, 61, 61, 61, 60, -1, 15, 16, 16, 17, 14, -1, -1, 47, 46, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 50, -1, -1, -1, -1, -1, -1, 43, 44, 43, 55, 56, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 58, 58, 57, -1, 15, 16, 22, 21, -1, -1, 47, 50, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 40, 45, -1, -1, -1, -1, 54, 61, -1, -1, -1, -1, -1, -1, 55, 61, 52, 52, 53, -1, -1, -1, -1, -1, 47, 46, -1, -1, 46, 45, -1, -1, -1, -1, -1, -1, -1, -1, 12, 13, 19, 18, -1, -1, 54, 50, 46, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 44, 41, -1, -1, -1, 59, 58, 62, 61, -1, -1, 55, 61, 61, 61, 0, 7, 7, 7, 7, 6, -1, -1, 54, 61, -1, 49, 49, 49, -1, -1, -1, -1, 47, 46, -1, 45, -1, -1, -1, -1, 47, 46, 54, 56, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 43, 44, 40, 41, -1, -1, -1, 59, 58, 52, 62, 61, 61, 61, 61, 3, 10, 10, 10, 10, 9, -1, -1, 59, 62, 61, 55, 55, -1, -1, -1, -1, -1, 51, 52, 52, 53, -1, -1, -1, 47, 46, 43, -1, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 61, 49, 49, 43, 44, 40, 41, -1, -1, -1, -1, 59, 58, 62, 61, 61, 12, 13, 23, 10, 10, 5, 7, 6, -1, 59, 58, 52, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 46, 50, 49, 55, 56, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 59, 58, 62, 61, 49, 55, 43, 44, -1, -1, -1, -1, -1, -1, 59, 58, 46, -1, -1, 20, 19, 23, 10, 10, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 46, 50, 43, 56, 53, 52, 53, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 46, -1, -1, -1, -1, -1, 59, 58, 62, 61, 61, 55, 56, -1, -1, -1, -1, 47, 46, 50, 49, 40, 41, -1, -1, 20, 13, 13, 14, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 46, 50, 49, 49, 52, 53, -1, -1, -1, -1, -1, 40, 41, -1, -1, -1, -1, -1, 50, 49, 46, -1, -1, -1, 40, 41, -1, 59, 58, 58, 52, 53, -1, -1, 47, 46, 50, 49, 43, 43, 43, 44, 40, 41, -1, -1, -1, -1, 39, 40, 40, 40, 40, -1, 46, 45, -1, -1, 47, 50, 49, -1, 52, 52, 53, -1, -1, -1, -1, -1, -1, 43, 44, 40, 41, -1, -1, -1, 50, 49, 49, -1, -1, -1, 43, 44, -1, -1, -1, -1, -1, -1, -1, 39, 50, 49, 43, 43, 43, 43]}, {"type": 3, "obj": [[2, "1503", 1376, 87, 38, 24, 0], [2, "1503", 1393, 49, 38, 24, 0], [2, "3800", 567, 77, 64, 100, 2], [2, "3800", 507, 40, 100, 64, 5], [2, "3800", 425, 19, 100, 64, 5], [2, "3800", 591, 130, 64, 100, 0], [2, "3800", 568, 195, 64, 100, 0], [2, "3800", 388, 10, 64, 100, 0], [2, "3800", 348, 38, 64, 100, 0], [2, "3800", 710, 622, 64, 100, 0], [2, "3800", 1316, 411, 100, 64, 5], [2, "3800", 1093, 395, 100, 64, 5], [2, "3800", 1168, 403, 100, 64, 4], [2, "3800", 695, 157, 64, 100, 2], [2, "3800", 58, 429, 64, 100, 2], [2, "3800", 474, 265, 100, 64, 4], [2, "3800", 371, 287, 100, 64, 4], [2, "3800", 580, 756, 100, 64, 4], [2, "3800", 872, 474, 100, 64, 4], [2, "3800", 794, 513, 100, 64, 4], [2, "3800", 995, 585, 64, 100, 2], [2, "3800", 1070, 651, 64, 100, 2], [2, "3800", 1318, 768, 100, 64, 4], [2, "3800", 1293, 382, 64, 100, 0], [2, "3800", 1026, 518, 64, 100, 0], [2, "3800", 656, 680, 64, 100, 0], [2, "3800", 721, 130, 64, 100, 2], [2, "3800", 751, 230, 64, 100, 0], [2, "3800", -14, 527, 64, 100, 2], [2, "3800", 547, 755, 64, 100, 0], [2, "3835", 305, 77, 32, 77, 0], [2, "3844", 149, 19, 78, 71, 2], [2, "3805", 475, 371, 98, 117, 0], [2, "3851", 433, 531, 44, 32, 0], [2, "3852", 54, 514, 42, 48, 0], [2, "3840", 306, 423, 28, 38, 2], [2, "3800", 277, 322, 64, 100, 0], [2, "3800", 251, 375, 64, 100, 2], [2, "3840", 794, 561, 28, 38, 2], [2, "3835", 554, 367, 32, 77, 0], [2, "3858", 1063, 405, 74, 45, 0], [2, "3858", 920, 148, 74, 45, 0], [2, "3858", 881, 179, 74, 45, 0], [2, "3858", 910, 208, 74, 45, 0], [2, "3854", 946, 625, 32, 47, 0], [2, "3845", 1045, 626, 58, 62, 0], [2, "3841", 736, 238, 76, 41, 0], [2, "3835", 1023, 538, 32, 77, 0], [2, "1503", 1235, 26, 38, 24, 0], [2, "3837", 791, 251, 76, 42, 0], [2, "3844", 772, 108, 78, 71, 0], [2, "3866", 1069, 359, 40, 45, 2], [2, "3786", 817, 276, 60, 33, 0], [2, "3786", 769, 299, 60, 33, 0], [2, "3786", 723, 323, 60, 33, 0], [2, "3786", 675, 346, 60, 33, 0], [2, "3866", 762, 198, 40, 45, 0], [2, "3866", 881, 253, 40, 45, 0], [2, "3866", 772, 202, 40, 45, 0], [2, "3866", 801, 217, 40, 45, 0], [2, "3866", 830, 231, 40, 45, 0], [2, "3866", 859, 246, 40, 45, 0], [2, "3866", 1416, 698, 40, 45, 2], [2, "3783", 864, 251, 68, 57, 0], [2, "3784", 1390, 188, 66, 34, 0], [2, "3784", 1354, 205, 66, 34, 0], [2, "3784", 1071, 46, 66, 34, 0], [2, "3866", 977, 21, 40, 45, 0], [2, "3866", 1006, 36, 40, 45, 0], [2, "3866", 1035, 51, 40, 45, 0], [2, "1503", 1393, 66, 38, 24, 0], [2, "1503", 1257, 55, 38, 24, 0], [2, "1503", 1247, 41, 38, 24, 0], [2, "3783", 1030, 65, 68, 57, 0], [2, "3782", 1212, 17, 48, 72, 0], [2, "3782", 1105, -36, 48, 72, 0], [2, "3782", 1212, 17, 48, 72, 0], [2, "3782", 1344, 88, 48, 72, 0], [2, "3782", 1451, 141, 48, 72, 0], [2, "3782", 1295, -27, 48, 72, 0], [2, "3782", 1421, 42, 48, 72, 0], [2, "3779", 1060, 79, 102, 80, 0], [2, "3779", 1123, 116, 102, 80, 0], [2, "3779", 1188, 152, 102, 80, 0], [2, "3779", 1253, 187, 102, 80, 0], [2, "3783", 1313, 224, 68, 57, 0], [2, "3820", 1091, 187, 76, 50, 0], [2, "3820", 1008, 239, 76, 50, 0], [2, "924", 979, 104, 58, 48, 0], [2, "924", 1265, 263, 58, 48, 0], [2, "3802", 1231, 71, 34, 32, 0], [2, "1503", 1331, 60, 38, 24, 0], [2, "1503", 1372, 1, 38, 24, 0], [2, "3802", 1400, 82, 34, 32, 2], [2, "3802", 1174, 4, 34, 32, 0], [2, "3808", 1264, 53, 18, 18, 0], [2, "3809", 1410, 70, 14, 14, 0], [2, "3808", 1037, 249, 18, 18, 0], [2, "3808", 1119, 197, 18, 18, 0], [2, "3805", 1106, -26, 98, 117, 2], [2, "3809", 1143, 44, 14, 14, 0], [2, "3809", 1394, 185, 14, 14, 0], [2, "3177", 1262, 104, 60, 29, 0], [2, "3177", 1243, 114, 60, 29, 0], [2, "3177", 1206, 133, 60, 29, 0], [2, "3177", 1225, 123, 60, 29, 0], [2, "572", 1237, 284, 56, 42, 2], [2, "572", 952, 125, 56, 42, 2], [2, "3866", 1370, 239, 40, 45, 0], [2, "3866", 1399, 254, 40, 45, 0], [2, "3866", 1428, 269, 40, 45, 0], [2, "3786", 991, -3, 60, 33, 0], [2, "3786", 1039, -26, 60, 33, 0], [2, "3779", 895, 261, 102, 80, 0], [2, "3779", 959, 295, 102, 80, 0], [2, "3783", 1018, 332, 68, 57, 0], [2, "3819", 1259, 98, 62, 38, 0], [2, "3786", 902, -9, 60, 33, 0], [2, "3786", 854, 14, 60, 33, 0], [2, "3786", 808, 38, 60, 33, 0], [2, "3786", 760, 61, 60, 33, 0], [2, "3866", 762, 77, 40, 45, 0], [2, "3866", 791, 92, 40, 45, 0], [2, "3866", 820, 107, 40, 45, 0], [2, "3866", 849, 122, 40, 45, 0], [2, "3786", 844, 137, 60, 33, 0], [2, "3786", 796, 160, 60, 33, 0], [2, "3786", 774, 172, 60, 33, 0], [2, "3866", 1413, 393, 40, 45, 2], [2, "3866", 1297, 356, 40, 45, 0], [2, "3866", 1326, 371, 40, 45, 0], [2, "3866", 1355, 385, 40, 45, 0], [2, "3866", 1384, 400, 40, 45, 0], [2, "3866", 1266, 363, 40, 45, 2], [2, "3866", 1237, 379, 40, 45, 2], [2, "3866", 1206, 394, 40, 45, 2], [2, "3786", 1123, 363, 60, 33, 2], [2, "3786", 1075, 339, 60, 33, 2], [2, "3786", 1027, 315, 60, 33, 2], [2, "3786", 979, 291, 60, 33, 2], [2, "3786", 931, 268, 60, 33, 2], [2, "3786", 883, 244, 60, 33, 2], [2, "3786", 836, 220, 60, 33, 2], [2, "3786", 788, 196, 60, 33, 2], [2, "3786", 1168, 385, 60, 33, 2], [2, "3786", 1268, 472, 60, 33, 0], [2, "3786", 1220, 495, 60, 33, 0], [2, "3786", 1174, 519, 60, 33, 0], [2, "3786", 1126, 542, 60, 33, 0], [2, "3786", 1079, 565, 60, 33, 0], [2, "3786", 1031, 588, 60, 33, 0], [2, "3786", 1314, 464, 60, 33, 2], [2, "3786", 1362, 488, 60, 33, 2], [2, "3786", 1410, 512, 60, 33, 2], [2, "3866", 1017, 612, 40, 45, 0], [2, "3866", 1046, 627, 40, 45, 0], [2, "3866", 1075, 642, 40, 45, 0], [2, "3866", 1104, 657, 40, 45, 0], [2, "3866", 1134, 672, 40, 45, 0], [2, "3866", 1163, 687, 40, 45, 0], [2, "3866", 1192, 702, 40, 45, 0], [2, "3866", 1221, 717, 40, 45, 0], [2, "3866", 1249, 732, 40, 45, 0], [2, "3866", 1278, 747, 40, 45, 0], [2, "3866", 1387, 713, 40, 45, 2], [2, "3866", 1357, 728, 40, 45, 2], [2, "3866", 1327, 743, 40, 45, 2], [2, "3866", 1309, 753, 40, 45, 2], [2, "3866", 1072, 350, 40, 45, 0], [2, "3866", 1101, 365, 40, 45, 0], [2, "3866", 1130, 379, 40, 45, 0], [2, "3866", 1159, 394, 40, 45, 0], [2, "3866", 1178, 403, 40, 45, 0], [2, "3786", 627, 370, 60, 33, 0], [2, "3866", 1040, 375, 40, 45, 2], [2, "3866", 1011, 391, 40, 45, 2], [2, "3866", 983, 406, 40, 45, 2], [2, "3866", 954, 422, 40, 45, 2], [2, "3866", 924, 437, 40, 45, 2], [2, "3866", 895, 453, 40, 45, 2], [2, "3866", 867, 469, 40, 45, 2], [2, "3866", 838, 485, 40, 45, 2], [2, "3780", 951, 417, 108, 58, 2], [2, "3780", 992, 438, 108, 58, 2], [2, "3780", 1035, 459, 108, 58, 2], [2, "3780", 1079, 481, 108, 58, 2], [2, "3780", 1122, 503, 108, 58, 2], [2, "3787", 1022, 404, 32, 22, 0], [2, "3787", 1050, 418, 32, 22, 0], [2, "3787", 1077, 432, 32, 22, 0], [2, "3787", 1105, 446, 32, 22, 0], [2, "3787", 1133, 460, 32, 22, 0], [2, "3787", 1160, 474, 32, 22, 0], [2, "3787", 1192, 490, 32, 22, 0], [2, "3787", 1220, 504, 32, 22, 0], [2, "3787", 930, 450, 32, 22, 0], [2, "3787", 958, 464, 32, 22, 0], [2, "3787", 985, 478, 32, 22, 0], [2, "3787", 1013, 492, 32, 22, 0], [2, "3787", 1041, 506, 32, 22, 0], [2, "3787", 1068, 520, 32, 22, 0], [2, "3787", 1100, 536, 32, 22, 0], [2, "3787", 1128, 550, 32, 22, 0], [2, "3806", 1225, 473, 26, 41, 0], [2, "3844", 727, 67, 78, 71, 2], [2, "3845", 787, 18, 58, 62, 0], [2, "3844", 743, 104, 78, 71, 2], [2, "3845", 746, 190, 58, 62, 2], [2, "3846", 777, 232, 60, 31, 0], [2, "3829", 845, 255, 18, 30, 0], [2, "3844", 1048, 350, 78, 71, 0], [2, "3845", 1108, 366, 58, 62, 0], [2, "3837", 1275, 379, 76, 42, 2], [2, "3842", 1255, 407, 58, 31, 0], [2, "3843", 1326, 404, 50, 28, 0], [2, "1503", 1304, 39, 38, 24, 0], [2, "1503", 1322, 52, 38, 24, 0], [2, "3849", 1205, 740, 34, 25, 2], [2, "3849", 1227, 754, 34, 25, 2], [2, "3841", 938, 664, 76, 41, 0], [2, "3842", 909, 697, 58, 31, 0], [2, "3845", 957, 687, 58, 62, 0], [2, "3844", 894, 716, 78, 71, 0], [2, "3836", 1103, 683, 92, 66, 0], [2, "3836", 688, 113, 92, 66, 0], [2, "3866", 809, 500, 40, 45, 2], [2, "3779", 615, 451, 102, 80, 0], [2, "3779", 679, 485, 102, 80, 0], [2, "3786", 581, 394, 60, 33, 0], [2, "3837", 1299, 770, 76, 42, 2], [2, "3852", 1343, 736, 42, 48, 0], [2, "3849", 1016, 646, 34, 25, 2], [2, "3849", 1082, 677, 34, 25, 2], [2, "3840", 1083, 649, 28, 38, 0], [2, "3853", 1186, 411, 38, 39, 0], [2, "3853", 1336, 367, 38, 39, 0], [2, "3849", 1404, 430, 34, 25, 0], [2, "3850", 1370, 423, 34, 25, 2], [2, "3858", 980, 253, 74, 45, 0], [2, "3858", 923, 60, 74, 45, 0], [2, "3858", 871, 49, 74, 45, 0], [2, "3858", 910, 208, 74, 45, 0], [2, "3858", 1045, 261, 74, 45, 0], [2, "3858", 1074, 318, 74, 45, 0], [2, "3858", 1079, 286, 74, 45, 0], [2, "3858", 1079, 286, 74, 45, 0], [2, "3858", 1111, 229, 74, 45, 0], [2, "3858", 1170, 229, 74, 45, 0], [2, "3858", 1135, 92, 74, 45, 0], [2, "3858", 1002, 138, 74, 45, 0], [2, "3858", 1213, 332, 74, 45, 0], [2, "3858", 1250, 337, 74, 45, 0], [2, "3858", 1360, 309, 74, 45, 0], [2, "3858", 752, 48, 74, 45, 0], [2, "3858", 732, 169, 74, 45, 0], [2, "3858", 745, 275, 74, 45, 0], [2, "3858", 684, 151, 74, 45, 0], [2, "3858", 826, 5, 74, 45, 0], [2, "3858", 901, 457, 74, 45, 0], [2, "3858", 609, 821, 74, 45, 0], [2, "3858", 1119, 728, 74, 45, 0], [2, "3858", 1048, 758, 74, 45, 0], [2, "3858", 1251, 782, 74, 45, 0], [2, "3858", 836, 789, 74, 45, 0], [2, "3858", 1045, 532, 74, 45, 0], [2, "3858", 1293, 420, 74, 45, 0], [2, "3858", 1264, 454, 74, 45, 0], [2, "3858", 1092, 473, 74, 45, 0], [2, "3858", 1092, 473, 74, 45, 0], [2, "3858", 932, 367, 74, 45, 0], [2, "3858", 707, 377, 74, 45, 0], [2, "3858", 1296, 495, 74, 45, 0], [2, "3858", 1296, 495, 74, 45, 0], [2, "3858", 1293, 524, 74, 45, 0], [2, "3858", 1333, 702, 74, 45, 0], [2, "3858", 1175, 621, 74, 45, 0], [2, "3779", -46, 768, 102, 80, 0], [2, "3779", 17, 803, 102, 80, 0], [2, "3779", 185, 428, 102, 80, 2], [2, "3800", 759, 506, 64, 100, 0], [2, "3800", 730, 531, 64, 100, 0], [2, "3800", 545, 411, 64, 100, 0], [2, "3847", 578, 438, 62, 63, 0], [2, "3849", 618, 453, 34, 25, 0], [2, "3851", 591, 412, 44, 32, 0], [2, "3835", 531, 437, 32, 77, 0], [2, "3840", 537, 418, 28, 38, 0], [2, "3853", 513, 486, 38, 39, 0], [2, "3853", 711, 594, 38, 39, 0], [2, "3846", 713, 624, 60, 31, 0], [2, "3844", 461, 496, 78, 71, 0], [2, "3844", 131, 453, 78, 71, 2], [2, "3836", 669, 636, 92, 66, 2], [2, "3843", 669, 689, 50, 28, 0], [2, "3838", 625, 730, 38, 31, 0], [2, "3837", 580, 743, 76, 42, 0], [2, "3837", 538, 760, 76, 42, 0], [2, "3846", 505, 787, 60, 31, 0], [2, "3844", 454, 776, 78, 71, 0], [2, "3858", 704, 673, 74, 45, 0], [2, "3858", 636, 789, 74, 45, 0], [2, "3858", 783, 813, 74, 45, 0], [2, "3858", 802, 526, 74, 45, 0], [2, "3858", 896, 398, 74, 45, 0], [2, "3858", 720, 465, 74, 45, 0], [2, "3858", 1183, 525, 74, 45, 0], [2, "3858", 611, 417, 74, 45, 0], [2, "3858", 536, 480, 74, 45, 0], [2, "3858", 676, 551, 74, 45, 0], [2, "3858", 650, 584, 74, 45, 0], [2, "3858", 614, 552, 74, 45, 0], [2, "3858", 564, 669, 74, 45, 0], [2, "3858", 523, 514, 74, 45, 0], [2, "3800", 91, 490, 64, 100, 0], [2, "3800", 325, 283, 64, 100, 0], [2, "3800", -8, 320, 64, 100, 2], [2, "3800", 26, 347, 64, 100, 2], [2, "3807", 185, 479, 42, 40, 0], [2, "3803", 142, 496, 40, 52, 2], [2, "3840", 81, 507, 28, 38, 0], [2, "3852", 276, 438, 42, 48, 0], [2, "3845", 52, 545, 58, 62, 2], [2, "3847", 18, 580, 62, 63, 0], [2, "3849", 60, 601, 34, 25, 0], [2, "3850", 21, 634, 34, 25, 0], [2, "3837", -25, 640, 76, 42, 2], [2, "3833", 28, 565, 36, 26, 0], [2, "3853", 18, 386, 38, 39, 0], [2, "3836", 51, 408, 92, 66, 0], [2, "3852", 121, 426, 42, 48, 0], [2, "3844", 77, 450, 78, 71, 0], [2, "3854", -12, 601, 32, 47, 0], [2, "3845", -26, 458, 58, 62, 0], [2, "3843", -13, 510, 50, 28, 2], [2, "3858", -12, 593, 74, 45, 0], [2, "3858", 61, 481, 74, 45, 0], [2, "3829", 548, 492, 18, 30, 0], [2, "3828", 567, 479, 22, 38, 0], [2, "3827", 476, 551, 32, 30, 0], [2, "3837", 410, 554, 76, 42, 0], [2, "3833", 592, 386, 36, 26, 0], [2, "3847", 308, 457, 62, 63, 2], [2, "3867", 443, 637, 92, 53, 2], [2, "3867", 132, 618, 92, 53, 2], [2, "3867", 239, 488, 92, 53, 2], [2, "3867", 229, 528, 92, 53, 0], [2, "3867", 220, 575, 92, 53, 0], [2, "3867", 213, 627, 92, 53, 0], [2, "3867", 254, 597, 92, 53, 0], [2, "3867", 298, 619, 92, 53, 2], [2, "3867", 325, 648, 92, 53, 2], [2, "3867", 372, 628, 92, 53, 2], [2, "3867", 384, 677, 92, 53, 0], [2, "3867", 378, 720, 92, 53, 0], [2, "3867", 332, 766, 92, 53, 0], [2, "3867", 329, 807, 92, 53, 0], [2, "3867", 197, 789, 92, 53, 2], [2, "3867", 28, 750, 92, 53, 0], [2, "3867", 47, 702, 92, 53, 0], [2, "3867", 93, 733, 92, 53, 2], [2, "3867", 165, 757, 92, 53, 0], [2, "3867", 107, 662, 92, 53, 0], [2, "3858", 793, 381, 74, 45, 0], [2, "3858", 561, 707, 74, 45, 0], [2, "3858", 336, 730, 74, 45, 0], [2, "3858", 416, 800, 74, 45, 0], [2, "3858", 81, 782, 74, 45, 0], [2, "3858", 47, 623, 74, 45, 0], [2, "3858", 157, 679, 74, 45, 0], [2, "3858", 145, 712, 74, 45, 0], [2, "3858", 175, 733, 74, 45, 0], [2, "3858", 175, 733, 74, 45, 0], [2, "3858", 233, 774, 74, 45, 0], [2, "3858", 160, 534, 74, 45, 0], [2, "3858", -10, 674, 74, 45, 0], [2, "3858", 277, 674, 74, 45, 0], [2, "3858", 34, 802, 74, 45, 0], [2, "3858", 467, 571, 74, 45, 0], [2, "3858", 513, 646, 74, 45, 0], [2, "3858", 464, 751, 74, 45, 0], [2, "3858", 193, 415, 74, 45, 0], [2, "1645", 375, 483, 52, 88, 0], [2, "1645", 698, 728, 52, 88, 2], [2, "3809", 517, 441, 14, 14, 0], [2, "3809", 530, 444, 14, 14, 0], [2, "3800", 54, 16, 64, 100, 0], [2, "3805", 181, 13, 98, 117, 2], [2, "3844", -6, 59, 78, 71, 0], [2, "3845", -29, 86, 58, 62, 0], [2, "3808", 214, 85, 18, 18, 0], [2, "3836", 544, 211, 92, 66, 2], [2, "3837", 486, 254, 76, 42, 0], [2, "3845", 446, 246, 58, 62, 0], [2, "3800", 236, 79, 64, 100, 2], [2, "3847", 269, 137, 62, 63, 0], [2, "1645", 1, 28, 52, 88, 0], [2, "3835", 43, 17, 32, 77, 0], [2, "3840", 272, 80, 28, 38, 2], [2, "3853", 240, 147, 38, 39, 0], [2, "3867", 124, 128, 92, 53, 2], [2, "3867", 133, 177, 92, 53, 2], [2, "3867", -22, 164, 92, 53, 0], [2, "3867", 9, 196, 92, 53, 2], [2, "3867", 73, 235, 92, 53, 2], [2, "3867", 128, 214, 92, 53, 0], [2, "3867", 123, 262, 92, 53, 0], [2, "3867", 143, 397, 92, 53, 2], [2, "3867", 131, 358, 92, 53, 0], [2, "3867", 136, 311, 92, 53, 2], [2, "3867", 213, 285, 92, 53, 0], [2, "3867", 265, 241, 92, 53, 0], [2, "3867", 333, 203, 92, 53, 0], [2, "3853", 313, 167, 38, 39, 0], [2, "3800", 862, 736, 64, 100, 0], [2, "3800", 1115, 729, 100, 64, 5], [2, "3867", 1187, 549, 92, 53, 2], [2, "3858", 1114, 575, 74, 45, 0], [2, "3858", 1160, 573, 74, 45, 0], [2, "3858", 1172, 657, 74, 45, 0], [2, "3858", 780, 562, 74, 45, 0], [2, "3858", 199, 320, 74, 45, 0], [2, "3858", 188, 373, 74, 45, 0], [2, "3858", 290, 396, 74, 45, 0], [2, "3858", 234, 190, 74, 45, 0], [2, "3858", 109, 144, 74, 45, 0], [2, "3858", 90, 173, 74, 45, 0], [2, "3858", 131, 190, 74, 45, 0], [2, "3858", 64, 290, 74, 45, 0], [2, "3858", 287, 286, 74, 45, 0], [2, "3858", 415, 203, 74, 45, 0], [2, "3858", 481, 227, 74, 45, 0], [2, "3858", 509, 97, 74, 45, 0], [2, "3858", 363, 113, 74, 45, 0], [2, "3843", 559, 257, 50, 28, 0], [2, "3820", 440, 156, 76, 50, 0], [2, "3858", 537, 31, 74, 45, 0], [2, "3858", 440, 307, 74, 45, 0], [2, "3858", 610, 354, 74, 45, 0], [2, "3858", 264, 103, 74, 45, 0], [2, "1503", 1364, 72, 38, 24, 0], [2, "1503", 1374, 55, 38, 24, 0], [2, "3809", 1390, 71, 14, 14, 0], [2, "3809", 1282, 38, 14, 14, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 25, 26, 27, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, -1, -1, -1, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 30, 28, 29, 30, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 32, 33, 31, 32, 33, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 25, 26, 27, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 30, 25, 26, 27, 31, 32, 33, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 31, 32, 33, 28, 29, 30, 25, 26, 27, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 27, 25, 26, 27, 31, 32, 33, 28, 29, 30, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 30, 28, 29, 30, 25, 26, 27, 31, 32, 33, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 27, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 28, 29, 30, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 30, 31, 32, 33, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 31, 32, 33, 25, 26, 27, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, -1, -1, -1, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 26, -1, -1, -1, -1, 28, 29, 30, 28, 29, -1, -1, -1, -1, 28, 29, 30, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 27, 32, 33, 31, 32, 33, 31, 32, 33, 25, 26, 27, 25, 26, 27, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 29, 30, 26, 27, 28, 29, 30, 25, 26, 27, 25, 26, 27, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 31, 32, 33, 29, 30, 31, 32, 33, 28, 29, 30, 28, 29, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 31, 32, 33, 25, 26, 27, 31, 32, 33, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 26, 27, 28, 29, 30, 25, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 27, 25, 26, 27, 25, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 31, 32, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 25, 26, 27, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 28, 29, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 33, 31, 32, 33, 31, 32, 33, 31, 32, 33, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 27, 25, 26, 27, 25, 26, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 28, 29, 30, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 34, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 35, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 8, 7, 7, 7, 7, 6, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 15, 10, 16, 16, 16, 21, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 15, 16, 16, 16, 17, 14, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 20, 19, 23, 16, 21, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 15, 16, 21, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 15, 16, 21, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 20, 19, 18, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 25, 26, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 8, 7, 1, 2, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 0, 1, 7, 11, 10, 10, 9, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 37, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 3, 4, 10, 10, 17, 13, 14, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 12, 13, 19, 19, 14, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 8, 7, 6, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 15, 10, 5, 1, 1, 6, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 20, 23, 22, 10, 10, 9, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 20, 19, 19, 19, 18, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}