{"mW": 840, "mH": 840, "tW": 24, "tH": 24, "tiles": [["106_4", 0, 3, 3], ["139", 0, 1, 1], ["141", 0, 1, 1], ["142", 0, 3, 4], ["140", 2, 3, 4], ["142", 2, 3, 4], ["140", 0, 3, 4], ["140", 1, 3, 4], ["142", 1, 3, 4], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["311_1", 0, 3, 2], ["311_1", 2, 3, 2], ["311_1", 1, 3, 2], ["311_1", 3, 3, 2], ["142", 3, 3, 4], ["203_13", 1, 2, 1]], "layers": [{"type": 3, "obj": [[2, "81", -76, 442, 6, 17, 0], [2, "224", 770, 657, 124, 194, 0], [2, "224", 478, 740, 124, 194, 0]]}, {"type": 4, "obj": [[4, 5, 60, 215, 0, 4024], [2, "219_2", 821, 393, 36, 30, 2], [2, "224", 780, 259, 124, 194, 2], [4, 3, 780, 455, 0, 4024], [2, "224", -56, 284, 124, 194, 0], [2, "219_2", 809, 459, 36, 30, 0], [2, "224", -44, 351, 124, 194, 0], [2, "224", -85, 357, 124, 194, 0], [2, "219_2", 611, 543, 36, 30, 0], [2, "4_2", 554, 455, 122, 119, 0], [2, "263_1", 776, 567, 34, 34, 0], [2, "224", 766, 413, 124, 194, 0], [2, "2", 350, 559, 90, 66, 2], [2, "2", 306, 609, 90, 66, 0], [2, "2", 408, 612, 90, 66, 2], [2, "219_2", 38, 667, 36, 30, 0], [2, "224", -24, 515, 124, 194, 0], [2, "224", 82, 520, 124, 194, 0], [2, "219_2", 7, 686, 36, 30, 0], [2, "219_2", 143, 686, 36, 30, 2], [4, 2, 281, 719, 1, 4024], [2, "263_1", 493, 691, 34, 34, 0], [2, "14", 73, 702, 32, 30, 2], [2, "14", 45, 709, 32, 30, 2], [4, 1, 232, 739, 0, 4024], [2, "219_2", 70, 713, 36, 30, 0], [2, "224", 46, 553, 124, 194, 0], [2, "219_2", 102, 720, 36, 30, 2], [2, "14", 49, 724, 32, 30, 2], [2, "263_1", 329, 802, 34, 34, 0], [2, "219_2", 305, 808, 36, 30, 2]]}, {"type": 3, "obj": [[2, "213_2", 639, 679, 64, 45, 0], [2, "214_2", 736, 707, 54, 40, 0], [2, "213_2", 685, 699, 64, 45, 0], [2, "208_2", 683, 686, 78, 40, 0], [2, "213_2", 311, 620, 64, 45, 2], [2, "213_2", 518, 604, 64, 45, 2], [2, "214_2", 605, 610, 54, 40, 0], [2, "214_2", 503, 630, 54, 40, 2], [2, "214_2", 574, 816, 54, 40, 2], [2, "208_2", 550, 791, 78, 40, 2], [2, "213_2", 346, 218, 64, 45, 0], [2, "214_2", 280, 174, 54, 40, 0], [2, "214_2", 296, 117, 54, 40, 0], [2, "213_2", 573, 241, 64, 45, 2], [2, "213_2", 167, 637, 64, 45, 0], [2, "213_2", 245, 295, 64, 45, 2], [2, "213_2", 216, 647, 64, 45, 2], [2, "214_2", 201, 305, 54, 40, 0], [2, "213_2", 311, 601, 64, 45, 0], [2, "213_2", 364, 599, 64, 45, 2], [2, "208_2", 324, 578, 78, 40, 0], [2, "214_2", 687, 138, 54, 40, 2], [2, "213_2", 620, 84, 64, 45, 2], [2, "213_2", 369, 238, 64, 45, 0], [2, "214_2", 665, 192, 54, 40, 2], [2, "213_2", 528, 260, 64, 45, 0], [2, "208_2", 583, 185, 78, 40, 2], [2, "208_2", 609, 117, 78, 40, 3], [2, "208_2", 562, 76, 78, 40, 3], [2, "205_2", 628, 68, 54, 40, 2], [2, "208_2", 619, 127, 78, 40, 2], [2, "174", 64, 762, 68, 33, 0], [2, "174", 184, 688, 68, 33, 0], [2, "174", 39, 742, 68, 33, 2], [2, "174", -3, 801, 68, 33, 0], [2, "152_2", 646, 160, 76, 40, 0], [2, "164", 690, 163, 60, 30, 2], [2, "152_2", 667, 112, 76, 40, 0], [2, "166", 649, 56, 30, 35, 2], [2, "208_2", 562, 89, 78, 40, 2], [2, "165", 705, 101, 42, 37, 2], [2, "208_2", 363, 213, 78, 40, 1], [2, "213_2", 187, 624, 64, 45, 2], [2, "152_2", 182, 603, 76, 40, 0], [2, "213_2", 187, 314, 64, 45, 2], [2, "213_2", 209, 284, 64, 45, 0], [2, "213_2", 200, 330, 64, 45, 2], [2, "208_2", 240, 273, 78, 40, 2], [2, "205_2", 200, 274, 54, 40, 0], [2, "152_2", 183, 291, 76, 40, 2], [2, "213_2", 596, 575, 64, 45, 0], [2, "213_2", 638, 565, 64, 45, 2], [2, "208_2", 614, 549, 78, 40, 2], [2, "205_2", 653, 541, 54, 40, 2], [2, "208_2", 553, 550, 78, 40, 2], [2, "213_2", 556, 571, 64, 45, 2], [2, "213_2", 444, 569, 64, 45, 0], [2, "213_2", 490, 565, 64, 45, 2], [2, "152_2", 553, 549, 76, 40, 0], [2, "208_2", 482, 551, 78, 40, 3], [2, "208_2", 442, 546, 78, 40, 0], [2, "213_2", 515, 574, 64, 45, 2], [2, "205_2", 521, 557, 54, 40, 2], [2, "213_2", 643, 577, 64, 45, 2], [2, "63", 809, 731, 16, 31, 2], [2, "64", 825, 731, 14, 15, 0], [2, "64", 819, 747, 14, 15, 2], [2, "62", 828, 748, 16, 27, 0], [2, "208_2", 112, 605, 78, 40, 2], [2, "213_2", 124, 627, 64, 45, 2], [2, "213_2", 13, 623, 64, 45, 0], [2, "213_2", 58, 618, 64, 45, 2], [2, "152_2", 121, 607, 76, 40, 0], [2, "208_2", 50, 603, 78, 40, 3], [2, "208_2", 10, 598, 78, 40, 0], [2, "213_2", 81, 631, 64, 45, 2], [2, "205_2", 90, 615, 54, 40, 2], [2, "166", 112, 605, 30, 35, 2], [2, "174", 374, 801, 68, 33, 0], [2, "174", 185, 805, 68, 33, 0], [2, "174", 17, 503, 68, 33, 2], [2, "219_2", 622, 610, 36, 30, 0], [2, "213_2", 622, 213, 64, 45, 2], [2, "152_2", 615, 191, 76, 40, 2], [2, "213_2", 237, 318, 64, 45, 2], [2, "213_2", 794, 648, 64, 45, 0], [2, "36", 647, 570, 140, 103, 0], [2, "213_2", 757, 613, 64, 45, 0], [2, "213_2", 797, 618, 64, 45, 2], [2, "208_2", 770, 599, 78, 40, 3], [2, "208_2", 754, 588, 78, 40, 0], [2, "213_2", 821, 632, 64, 45, 2], [2, "205_2", 834, 605, 54, 40, 2], [2, "213_2", 732, 639, 64, 45, 0], [2, "205_2", 735, 622, 54, 40, 0], [2, "208_2", 215, 247, 78, 40, 0], [2, "205_2", 196, 223, 54, 40, 2], [2, "213_2", 388, 589, 64, 45, 2], [2, "208_2", 349, 577, 78, 40, 3], [2, "205_2", 398, 560, 54, 40, 2], [2, "213_2", 790, 744, 64, 45, 2], [2, "214_2", 793, 724, 54, 40, 0], [2, "213_2", 815, 693, 64, 45, 2], [2, "214_2", 722, 687, 54, 40, 0], [2, "213_2", 712, 662, 64, 45, 0], [2, "213_2", 762, 684, 64, 45, 0], [2, "213_2", 752, 671, 64, 45, 0], [2, "214_2", 765, 714, 54, 40, 0], [2, "208_2", 745, 646, 78, 40, 0], [2, "208_2", 770, 665, 78, 40, 0], [2, "152_2", 707, 639, 76, 40, 2], [2, "205_2", 823, 679, 54, 40, 2], [2, "213_2", 792, 719, 64, 45, 0], [2, "208_2", 779, 709, 78, 40, 2], [2, "205_2", 780, 733, 54, 40, 0], [2, "214_2", 812, 798, 54, 40, 0], [2, "219_2", 417, 612, 36, 30, 2], [2, "174", -3, 523, 68, 33, 1], [2, "174", 75, 538, 68, 33, 2], [2, "174", 53, 571, 68, 33, 0], [2, "174", 705, 403, 68, 33, 1], [2, "174", 652, 539, 68, 33, 0], [2, "174", 556, 510, 68, 33, 0], [2, "174", 219, 409, 68, 33, 0], [2, "220_2", 192, 662, 40, 29, 0], [2, "62", 691, 545, 16, 27, 0], [2, "62", 777, 563, 16, 27, 0], [2, "213_2", 802, 773, 64, 45, 0], [2, "208_2", 797, 764, 78, 40, 0], [2, "213_2", 78, 150, 64, 45, 2], [2, "214_2", 173, 214, 54, 40, 2], [2, "214_2", 127, 161, 54, 40, 2], [2, "208_2", -55, 123, 78, 40, 1], [2, "208_2", 11, 120, 78, 40, 1], [2, "208_2", 169, 234, 78, 40, 3], [2, "205_2", 43, 158, 54, 40, 2], [2, "208_2", 95, 174, 78, 40, 0], [2, "208_2", 57, 125, 78, 40, 0], [2, "208_2", 88, 121, 78, 40, 0], [2, "152_2", 151, 197, 76, 40, 2], [2, "205_2", 128, 140, 54, 40, 2], [2, "1322_1", 300, 371, 64, 38, 0], [2, "1323_1", 223, 372, 94, 24, 0], [2, "1323_1", 349, 394, 94, 24, 0], [2, "1322_1", 538, 395, 64, 38, 0], [2, "1323_1", 458, 395, 94, 24, 0], [2, "1324_1", 589, 403, 40, 43, 0], [2, "1323_1", 625, 397, 94, 24, 0], [2, "1324_1", 706, 355, 40, 43, 0], [2, "1323_1", 742, 349, 94, 24, 0], [2, "214_2", 586, 228, 54, 40, 2], [2, "205_2", 588, 206, 54, 40, 2], [2, "213_2", 528, 239, 64, 45, 2], [2, "213_2", 491, 244, 64, 45, 2], [2, "152_2", 532, 219, 76, 40, 0], [2, "205_2", 497, 227, 54, 40, 2], [2, "213_2", 445, 249, 64, 45, 2], [2, "213_2", 420, 245, 64, 45, 2], [2, "208_2", 431, 225, 78, 40, 3], [2, "208_2", 397, 223, 78, 40, 0], [2, "208_2", 464, 63, 78, 40, 1], [2, "213_2", 338, 73, 64, 45, 0], [2, "205_2", 397, 87, 54, 40, 2], [2, "208_2", 350, 107, 78, 40, 1], [2, "208_2", 405, 70, 78, 40, 3], [2, "205_2", 336, 53, 54, 40, 0], [2, "208_2", 313, 99, 78, 40, 0], [2, "208_2", 370, 60, 78, 40, 0], [2, "205_2", 532, 55, 54, 40, 2], [2, "208_2", 318, 128, 78, 40, 0], [2, "152_2", 293, 91, 76, 40, 2], [2, "152_2", 273, 143, 76, 40, 2], [2, "213_2", 307, 201, 64, 45, 0], [2, "166", 336, 45, 30, 35, 0], [2, "165", 283, 83, 42, 37, 0], [2, "164", 257, 144, 60, 30, 0], [2, "213_2", 262, 630, 64, 45, 2], [2, "214_2", 170, 343, 54, 40, 2], [2, "208_2", 136, 314, 78, 40, 0], [2, "205_2", 173, 323, 54, 40, 2], [2, "208_2", 156, 352, 78, 40, 0], [2, "1322_1", 437, 393, 64, 38, 0], [2, "1324_1", 814, 308, 40, 43, 0], [2, "220_2", 422, 387, 40, 29, 0], [2, "219_2", 574, 409, 36, 30, 0], [2, "219_2", 813, 333, 36, 30, 2], [2, "62", 270, 660, 16, 27, 0], [2, "205_2", 286, 598, 54, 40, 2], [2, "208_2", 236, 611, 78, 40, 2], [2, "205_2", 501, 605, 54, 40, 2], [2, "213_2", 447, 634, 64, 45, 2], [2, "208_2", 438, 623, 78, 40, 2], [2, "205_2", 425, 659, 54, 40, 2], [2, "208_2", 192, 627, 78, 40, 3], [2, "208_2", 428, 688, 78, 40, 2], [2, "208_2", 534, 753, 78, 40, 3], [2, "208_2", 481, 712, 78, 40, 3], [2, "208_2", 523, 810, 78, 40, 2], [2, "208_2", 614, 633, 78, 40, 0], [2, "208_2", 631, 669, 78, 40, 0], [2, "5", 395, 671, 42, 66, 2], [2, "5", 352, 671, 42, 66, 0], [2, "220_2", 552, 819, 40, 29, 0], [2, "213_2", -37, 628, 64, 45, 2], [2, "208_2", -50, 610, 78, 40, 3], [2, "220_2", -10, 654, 40, 29, 0], [2, "219_2", 733, 342, 36, 30, 0], [2, "955_2", 735, 371, 20, 18, 0], [2, "219_2", 363, 717, 36, 30, 0], [2, "59", 618, 657, 84, 49, 0], [2, "57", 611, 667, 72, 44, 0], [2, "61", 627, 720, 16, 17, 0], [2, "60", 614, 732, 16, 16, 0], [2, "61", 600, 742, 16, 17, 0], [2, "57", 600, 670, 72, 44, 0], [2, "58", 595, 676, 66, 39, 0], [2, "56", 579, 680, 76, 47, 0], [2, "57", 568, 689, 72, 44, 0], [2, "56", 555, 696, 76, 47, 0], [2, "49", 625, 636, 20, 34, 0], [2, "49", 678, 666, 20, 34, 0], [2, "50", 661, 679, 8, 23, 0], [2, "50", 607, 652, 8, 23, 0], [2, "51", 627, 687, 38, 35, 0], [2, "50", 626, 703, 8, 23, 0], [2, "51", 574, 657, 38, 35, 0], [2, "50", 576, 673, 8, 23, 0], [2, "53", 615, 653, 18, 9, 0], [2, "53", 667, 684, 18, 9, 0], [2, "51", 543, 681, 38, 35, 0], [2, "55", 546, 706, 70, 46, 0], [2, "54", 520, 722, 86, 53, 0], [2, "49", 524, 703, 20, 34, 0], [2, "51", 592, 714, 38, 35, 0], [2, "49", 580, 732, 20, 34, 0], [2, "711_1", 224, 194, 92, 34, 2], [2, "152_2", 305, 177, 76, 40, 0], [2, "711_1", 272, 235, 92, 34, 2], [2, "205_2", 333, 201, 54, 40, 0], [2, "707_1", 305, 203, 6, 25, 0], [2, "707_1", 305, 224, 6, 25, 0], [2, "707_1", 305, 239, 6, 25, 0], [2, "708_1", 282, 213, 24, 18, 0], [2, "708_1", 309, 228, 24, 18, 0], [2, "708_1", 324, 235, 24, 18, 0], [2, "708_1", 245, 226, 24, 18, 0], [2, "707_1", 269, 216, 6, 25, 0], [2, "707_1", 269, 252, 6, 25, 0], [2, "707_1", 268, 237, 6, 25, 0], [2, "708_1", 272, 241, 24, 18, 0], [2, "708_1", 287, 248, 24, 18, 0], [2, "62", 361, 211, 16, 27, 0], [2, "62", 310, 174, 16, 27, 0], [2, "62", 208, 209, 16, 27, 0], [2, "62", 259, 256, 16, 27, 0], [2, "220_2", 257, 265, 40, 29, 0], [2, "113_1", 206, 186, 26, 33, 0], [2, "1302_1", 226, 269, 40, 29, 0], [2, "52", 270, 187, 46, 22, 0], [2, "52", 319, 223, 46, 22, 0], [2, "50", 267, 196, 8, 23, 0], [2, "50", 317, 237, 8, 23, 0], [2, "52", 275, 243, 46, 22, 0], [2, "52", 224, 203, 46, 22, 0], [2, "4_2", 354, 597, 122, 119, 2], [2, "955_2", 297, 739, 20, 18, 0], [2, "955_2", 258, 605, 20, 18, 0], [2, "955_2", 677, 524, 20, 18, 0], [2, "955_2", 8, 753, 20, 18, 0], [2, "955_2", 396, 126, 20, 18, 0], [2, "385", 725, 722, 72, 48, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 84, 85, -1, -1, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 99, 99, 87, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 95, 106, 105, 100, 101, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 103, 96, 101, -1, -1, -1, -1, -1, -1, 36, 35, 11, 81, 47, -1, -1, -1, -1, -1, -1, -1, 12, 12, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -124, -124, -125, 71, 73, 59, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 23, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -115, 35, -1, -1, -1, -1, -1, -1, 71, 72, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 81, 10, 10, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 17, 10, 45, 45, 45, -124, -125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 10, -119, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 10, -122, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, -124, -125, -1, -1, 99, 99, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 83, 90, -1, -1, 71, 76, 10, -115, 35, -1, -1, 11, 12, 13, 36, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 71, -1, -1, -1, 86, 105, 99, 90, 89, 77, 10, 10, -115, -114, 11, 16, 10, 10, 10, 10, 12, 37, 35, 80, 12, 12, 12, 12, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, 91, 94, 101, 87, 100, 101, 74, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 47, 48, 24, 23, -115, -115, -114, -1, -1, -1, 11, 12, 36, 35, 103, 102, 102, 87, -1, -1, 71, 72, 44, 21, 10, 10, 10, 10, 10, 10, 10, -123, -124, -124, 73, 59, 56, 60, 34, 45, -124, -124, -125, -1, -1, 71, 10, 10, -115, 36, -114, 95, 87, 89, 89, -1, -1, -1, -1, 71, 72, 73, 10, 10, 45, 43, -1, -1, -1, 71, 72, 73, -124, -124, -125, -1, 91, 90, 89, 90, 89, 71, 73, 10, 10, 38, -1, 87, 92, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 95, 96, 97, 99, 89, 71, 10, 10, 41, -1, 87, 87, 104, -1, -1, -1, -1, 74, -1, -1, -1, -1, -1, -1, -1, 105, -1, 99, 99, 99, -1, 99, 99, 99, 99, 99, 99, 99, 99, 99, -1, -1, -1, -1, -1, 95, 96, 97, 84, 85, -1, -1, -1, -1, -1, -1, -1, 38, -1, 103, 99, 99, 99, 99, 99, 99, 99, 99, -1, -1, -1, 99, 99, 100, 96, -1, -1, -1, -1, -1, 87, -1, 93, 99, 92, -1, 99, 99, 99, -1, -1, 10, 10, 37, 99, 99, 100, 95, 102, 106, 99, 100, -1, -1, -1, -1, 100, 96, 97, -1, -1, -1, -1, -1, -1, 93, 93, 99, 99, 99, 99, 99, 99, 99, 71, 21, 67, -1, 93, 93, 93, 36, 35, -1, 103, 102, 101, -1, -1, -1, 103, 101, 47, 33, 80, 36, 35, 87, 87, 87, 106, 99, 99, 100, 106, 99, 99, 99, 99, 99, -1, 96, 96, 106, 93, 93, -1, -1, -1, 37, -1, -1, -1, -1, -1, 12, -1, 50, 30, 38, -124, -125, 83, 87, 87, 95, 96, 87, 88, 94, 105, 105, 105, 96, 97, -1, -1, 91, 94, 93, -1, 9, -1, -1, -1, 23, -1, -1, -1, -1, 47, -1, 9, 26, 83, 99, -1, 95, 98, 99, 83, 84, 84, 100, 97, 95, 96, 96, 97, -1, -1, -1, 95, 96, 102, 102, -1, 96, 97, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, 86, 96, 106, 105, 95, 96, 95, 96, 96, 101, -1, 11, 11, 36, 36, 36, 36, 36, 35, 36, 35, -1, -1, -1, 11, 12, 13, 12, 13, 10, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 14, 47, 48, 24, 24, 23, 10, 10, 10, 38, -1, -1, 11, 81, 47, 48, 23, 10, 10, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, 95, -1, 36, 36, 37, 81, 10, 50, 33, 61, 33, 34, 10, 10, 10, -1, -1, -1, -1, 75, 59, 32, 32, 10, 10, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 12, 13, 10, 10, 10, 59, 32, 34, 10, 10, 10, -125, 83, 90, 89, -1, -1, -1, 22, 21, 76, 76, 10, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "313", 783, 567, 70, 44, 0], [2, "313", 211, 572, 70, 44, 2], [2, "313", 649, 636, 70, 44, 0], [2, "313", 369, 703, 70, 44, 2], [2, "313", 305, 703, 70, 44, 0], [2, "313", 428, 601, 70, 44, 2], [2, "313", 0, 719, 70, 44, 2]]}, {"type": 2, "data": [-113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 4, 5, 4, 8, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 0, 1, 2, 6, 7, 8, 7, 8, 7, 8, 8, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 3, 4, 5, 7, 8, 121, 2, 122, 117, 112, 112, 113, 0, 1, -113, -113, -113, -113, -113, -113, -113, 6, 7, 8, 3, 4, 3, -113, -113, -113, -113, -113, -113, -113, -113, 2, 7, 8, 8, 107, 108, 113, 115, -126, 124, 126, 125, 0, 6, 7, -113, -113, -113, -113, -113, -113, 3, 4, 5, 6, 7, 6, -113, -113, -113, -113, -113, -113, -113, 4, 5, 107, 108, 109, 120, 126, 125, 127, 126, 125, 4, 5, 3, 4, 5, 3, -113, -113, -113, -113, -113, 6, 7, 8, 4, 5, 4, 5, -113, -113, -113, -113, -113, -113, 7, 8, 8, 119, 125, 2, 0, 1, 2, 2, 1, 0, 1, 107, 108, 109, -113, -113, -113, -113, -113, -113, 1, 2, 0, 0, 1, 2, 2, 6, 7, -113, -113, -113, -113, -113, 8, 1, 2, 0, 1, 2, 4, 5, 8, 0, 3, 4, 119, -113, -113, -113, -113, -113, -113, -113, -113, 0, 1, 2, 2, 0, 1, 2, 2, 1, 115, -113, -113, -113, -113, -113, -113, -113, -113, 4, -113, 0, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 3, 4, 5, 5, 3, 4, 5, 5, 0, 110, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 6, 7, 8, 8, 1, 2, 8, 8, 1, 127, 126, 125, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 5, 3, 4, 5, 4, 5, 0, 1, -1, 5, 3, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 3, 6, 7, 8, 7, 8, 115, 114, 111, -1, -1, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -1, 108, 3, 4, 5, 4, 5, 5, 111, 111, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 124, -126, -127, 123, 0, 1, 2, 2, 7, 4, 5, 4, 119, 126, -126, -127, -127, -126, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 126, 125, 127, -126, 123, 3, 4, 5, 5, 4, 7, 8, 7, 8, 5, 127, 126, 125, 4, 3, 127, 126, 119, 120, -126, -127, -127, 124, 126, -113, -113, 114, 113, -127, -128, 2, 107, 108, 118, 123, 6, 7, 8, 8, 7, 8, 3, 4, 7, 8, 3, 3, 4, 7, 6, 7, 0, 1, 2, 127, 126, 126, 125, 4, 5, 4, 5, -127, 124, 125, 5, 122, -127, -127, -127, 7, 8, 7, 6, 7, 8, 6, 7, 6, 7, 6, 6, 7, 3, 4, 4, 3, 4, 5, 115, 114, 113, 0, 1, 2, 7, 8, 124, 125, 7, 8, 119, 120, -126, -127, 1, 1, 2, 0, 1, 119, 125, 1, 2, 0, 1, 2, 2, 6, 7, 7, 6, 7, 8, 127, 126, 125, 3, 4, 5, 127, 126, -128, 7, 8, 0, 1, 107, 108, 117, 4, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 5, 0, 107, 108, 109, 3, 4, 5, 1, 0, 6, 7, 8, 0, 107, 112, 108, 109, 3, 4, 119, -126, 117, 1, 2, 1, 6, 7, 8, 6, 7, 8, 6, 7, 8, 8, 3, 110, 111, 112, 113, 7, 0, 1, 2, 3, 107, 108, 108, 118, 111, 111, 109, 4, 7, 8, 127, -126, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 2, 6, 7, 8, 117, 116, 0, 1, 2, 5, 0, 110, 111, 111, 111, 111, 117, 112, 113, 4, 115, 114, 118, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 5, 119, 7, 126, 0, 0, 1, 2, 0, 1, 2, 2, 5, 4, 5, 2, -128, 111, 111, 112, 118, 117, -127, 6, 7, 8, 113, 7, 8, 6, 7, 8, 6, 7, 8, 8, 7, 8, 7, 3, 0, 1, 0, 3, 4, 4, -113, -113, -113, -113, -113, 126, 125, 6, 0, 0, 126, 125, 0, 1, 117, 116, 1, 6, 7, 0, 1, 4, 5, 3, 0, 1, 0, 1, 0, 3, 4, 3, 2, 2, -1, -113, -113, -113, -113, -113, -128, 1, 0, 3, 0, 1, 2, 3, 4, 5, 3, 4, -127, -127, -127, -127, -127, 0, 1, 3, 123, 0, -126, -127, -128, 2, -113, -113, -113, -113, -113, -113, -113, -1, 126, 125, 0, 3, 6, 3, 4, 0, 6, 107, 108, 113, 1, 2, -127, -127, 126, 125, 3, 124, 123, 123, 123, 123, -127, -128, 2, -113, -113, -113, -113, -113, -113, -113, -1, -1, -1, 2, 6, 0, 6, 7, 3, 5, 110, 111, 116, 4, 117, 124, 124, 125, 127, 126, 125, 127, -126, 123, 127, 126, 125, 8, 8, 5, -113, -113, -113, -113, -113, -113, -1, -1, -1, 109, 3, 122, 123, 6, 109, 6, -126, -127, -128, 126, 126, 125, 6, 7, 6, 7, 8, 6, 122, 123, 124, 5, 8, 7, 127, 126, 125, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 120, -113, 112, 113, 127, 126, 125, 2, 3, 3, 125, 0, 1, 2, 2, 3, 119, 120, 121, 8, 2, 0, 1, 127, 126, 125, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 117, 116, 5, 3, 4, 5, 3, 4, 5, 3, 4, 107, 108, 114, 113, 5, 3, 4, 5, 3, 4, 5, 3, 4, -1, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 126, 125, 8, 6, 7, 8, 6, 7, 8, 6, 7, 110, 117, 117, 116, 114, 114, 107, 114, 113, 7, 8, 6, 7, 8, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, 119, 120, 0, 1, 126, 111, 111, 117, 116, 7, 8, 0, 1, 2, -113, -113, -113, -113, -113, -113, -113, -113, -113, -113]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]}