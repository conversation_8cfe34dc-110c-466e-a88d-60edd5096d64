{"mW": 600, "mH": 600, "tW": 24, "tH": 24, "tiles": [["3327", 0, 2, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["3315", 0, 3, 2], ["3315", 2, 3, 2], ["3315", 1, 3, 2], ["3315", 3, 3, 2]], "layers": [{"type": 4, "obj": [[2, "3331", 44, 24, 54, 59, 0], [2, "3341", 361, 22, 44, 86, 2], [2, "3340", 191, 52, 60, 123, 2], [2, "3341", 20, 95, 44, 86, 2], [2, "3341", 171, 104, 44, 86, 2], [2, "3329", 519, 135, 50, 59, 0], [2, "3341", 418, 361, 44, 86, 0], [2, "3340", 73, 348, 60, 123, 0], [2, "3331", 95, 422, 54, 59, 0], [2, "3331", 524, 433, 54, 59, 0], [2, "3340", 489, 382, 60, 123, 0], [2, "3329", 63, 474, 50, 59, 0], [2, "3330", 313, 514, 60, 43, 0]]}, {"type": 3, "obj": [[2, "3328", 415, 8, 76, 54, 0], [2, "3328", 305, 520, 76, 54, 0], [2, "3328", 40, 455, 76, 54, 0], [2, "3348", 422, 285, 38, 22, 0], [2, "3348", 393, 300, 38, 22, 0], [2, "3348", 364, 315, 38, 22, 0], [2, "3348", 335, 330, 38, 22, 0], [2, "3348", 309, 344, 38, 22, 0], [2, "3348", 300, 349, 38, 22, 0], [2, "3348", 434, 279, 38, 22, 0], [2, "3348", 152, 272, 38, 22, 0], [2, "3348", 162, 266, 38, 22, 0], [2, "3348", 188, 252, 38, 22, 0], [2, "3348", 217, 237, 38, 22, 0], [2, "3348", 246, 222, 38, 22, 0], [2, "3348", 275, 207, 38, 22, 0], [2, "3348", 284, 202, 38, 22, 0], [2, "3348", 151, 283, 38, 22, 2], [2, "3348", 179, 297, 38, 22, 2], [2, "3348", 207, 311, 38, 22, 2], [2, "3348", 235, 325, 38, 22, 2], [2, "3348", 252, 334, 38, 22, 2], [2, "3348", 279, 348, 38, 22, 2], [2, "3348", 308, 204, 38, 22, 2], [2, "3348", 336, 218, 38, 22, 2], [2, "3348", 364, 232, 38, 22, 2], [2, "3348", 392, 246, 38, 22, 2], [2, "3348", 408, 255, 38, 22, 2], [2, "3348", 436, 269, 38, 22, 2], [2, "3349", 276, 215, 66, 34, 0], [2, "3349", 303, 229, 66, 34, 0], [2, "3349", 331, 243, 66, 34, 0], [2, "3349", 360, 256, 66, 34, 0], [2, "3349", 388, 270, 66, 34, 0], [2, "3349", 239, 232, 66, 34, 0], [2, "3349", 266, 246, 66, 34, 0], [2, "3349", 294, 260, 66, 34, 0], [2, "3349", 323, 273, 66, 34, 0], [2, "3349", 351, 287, 66, 34, 0], [2, "3349", 204, 250, 66, 34, 0], [2, "3349", 231, 264, 66, 34, 0], [2, "3349", 259, 278, 66, 34, 0], [2, "3349", 288, 291, 66, 34, 0], [2, "3349", 316, 305, 66, 34, 0], [2, "3349", 168, 269, 66, 34, 0], [2, "3349", 195, 283, 66, 34, 0], [2, "3349", 223, 297, 66, 34, 0], [2, "3349", 252, 310, 66, 34, 0], [2, "3349", 280, 324, 66, 34, 0], [2, "3351", 235, 228, 154, 105, 0], [2, "3350", 284, 169, 66, 34, 0], [2, "3350", 312, 183, 66, 34, 0], [2, "3350", 340, 198, 66, 34, 0], [2, "3350", 368, 212, 66, 34, 0], [2, "3350", 397, 227, 66, 34, 0], [2, "3350", 425, 241, 66, 34, 0], [2, "3350", 453, 255, 66, 34, 0], [2, "3350", 475, 266, 66, 34, 0], [2, "3350", 110, 288, 66, 34, 0], [2, "3350", 140, 302, 66, 34, 0], [2, "3350", 169, 317, 66, 34, 0], [2, "3350", 197, 332, 66, 34, 0], [2, "3350", 225, 346, 66, 34, 0], [2, "3350", 254, 361, 66, 34, 0], [2, "3350", 269, 370, 66, 34, 0], [2, "3350", 248, 187, 66, 34, 0], [2, "3350", 211, 205, 66, 34, 0], [2, "3350", 178, 224, 66, 34, 0], [2, "3350", 142, 242, 66, 34, 0], [2, "3350", 108, 260, 66, 34, 0], [2, "3350", 82, 273, 66, 34, 0], [2, "3350", 441, 284, 66, 34, 0], [2, "3350", 405, 302, 66, 34, 0], [2, "3350", 368, 320, 66, 34, 0], [2, "3350", 333, 337, 66, 34, 0], [2, "3350", 297, 355, 66, 34, 0], [2, "3350", 270, 369, 66, 34, 0], [2, "3330", 50, 43, 60, 43, 0], [2, "3330", 10, 69, 60, 43, 0], [2, "3330", 65, 72, 60, 43, 0], [2, "3330", 382, 80, 60, 43, 0], [2, "3330", 486, 11, 60, 43, 0], [2, "3330", 93, 461, 60, 43, 0], [2, "3330", 112, 492, 60, 43, 0], [2, "3330", 524, 472, 60, 43, 0], [2, "3330", 499, 496, 60, 43, 0], [2, "3328", 364, 419, 76, 54, 0], [2, "3328", 543, 504, 76, 54, 0], [2, "3328", -14, 15, 76, 54, 0], [2, "3333", 419, 446, 32, 33, 0], [2, "3333", 406, 33, 32, 33, 0], [2, "3333", 53, 85, 32, 33, 0], [2, "3334", -4, 132, 24, 31, 0], [2, "3334", 14, 137, 24, 31, 0], [2, "3334", 32, 137, 24, 31, 0], [2, "3334", 234, 41, 24, 31, 0], [2, "3334", 222, 47, 24, 31, 0], [2, "3334", 253, 43, 24, 31, 0], [2, "3334", 267, 52, 24, 31, 0], [2, "3334", 157, 514, 24, 31, 0], [2, "3334", 137, 522, 24, 31, 0], [2, "3334", 377, 539, 24, 31, 0], [2, "3334", 377, 539, 24, 31, 0], [2, "3334", 388, 555, 24, 31, 0], [2, "3334", 377, 574, 24, 31, 0], [2, "3343", 567, 164, 14, 15, 0], [2, "3342", 355, 452, 14, 15, 0], [2, "3342", 48, 496, 14, 15, 0], [2, "3342", 48, 496, 14, 15, 0], [2, "3342", 35, 487, 14, 15, 0], [2, "3342", 305, 557, 14, 15, 0], [2, "3342", 532, 529, 14, 15, 0], [2, "3342", 532, 529, 14, 15, 0], [2, "3342", 518, 530, 14, 15, 0], [2, "3344", 540, 538, 14, 15, 0], [2, "3344", 572, 551, 14, 15, 0], [2, "3343", 315, 563, 14, 15, 0], [2, "3343", 399, 467, 14, 15, 0], [2, "3343", 527, 541, 14, 15, 0], [2, "3343", 112, 522, 14, 15, 0], [2, "3343", 201, 163, 14, 15, 0], [2, "3343", 201, 163, 14, 15, 0], [2, "3342", 368, 94, 14, 15, 0], [2, "3342", 368, 94, 14, 15, 0], [2, "3342", 419, 114, 14, 15, 0], [2, "3342", 483, 44, 14, 15, 0], [2, "3344", 499, 47, 14, 15, 0], [2, "3344", 82, 106, 14, 15, 0], [2, "3344", 5, 60, 14, 15, 0], [2, "3343", 20, 60, 14, 15, 0], [2, "3342", 15, 159, 14, 15, 0], [2, "3333", 237, 56, 32, 33, 0], [2, "3333", 149, 527, 32, 33, 0], [2, "1409", 505, 153, 26, 27, 0], [2, "1409", 460, 45, 26, 27, 0], [2, "1409", 105, 95, 26, 27, 0], [2, "1409", 223, 156, 26, 27, 0], [2, "1409", 117, 526, 26, 27, 0], [2, "1409", 355, 560, 26, 27, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, 35, 34, 34, 34, 34, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 35, 34, 34, 34, 34, 34, 34, 33, -1, 34, 34, -1, 32, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, 24, 35, 34, 34, 34, 34, 34, 40, 41, 38, -1, 47, 46, 34, 34, 34, 33, -1, -1, -1, -1, -1, -1, -1, -1, 27, 28, 34, 34, 41, 37, 37, 37, 38, -1, -1, 44, 43, 34, 34, 41, 38, -1, -1, -1, -1, -1, -1, -1, -1, 36, 37, 37, -1, -1, -1, -1, -1, -1, -1, -1, 24, 25, 36, 37, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 28, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 30, 37, 37, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 34, 34, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 43, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, 40, 40, -1, -1, -1, -1, -1, -1, -1, -1, 32, 31, 35, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 46, 40, 40, 45, -1, -1, -1, -1, 31, 31, -1, 27, 28, 28, -1, -1, 31, 30, -1, -1, -1, -1, -1, -1, 44, 43, 37, 37, 38, -1, 32, 31, -1, -1, 40, -1, 44, 43, 47, 46, -1, 34, 33, -1, -1, -1, -1, 32, 31, 31, 25, 26, -1, -1, -1, 39, -1, -1, 40, 40, -1, -1, -1, 44, 43, 47, 46, 33, -1, -1, -1, -1, 39, 40, -1, 28, 29, 30, -1, -1, 44, 47, 46, 40, 40, -1, -1, -1, -1, -1, 44, 43, 38, -1, -1, -1, -1, 39, 46, 40, 40, 40, 33, -1, -1, -1, 44, 43, 43, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 43, 47, 40, 40, 33, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 0, 2, 3, 2, 3, 2, 3, 2, 3, 0, 1, 0, 1, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0]}