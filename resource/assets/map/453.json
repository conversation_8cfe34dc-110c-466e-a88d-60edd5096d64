{"mW": 960, "mH": 960, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2], ["1681", 0, 3, 3]], "layers": [{"type": 3, "obj": [[2, "1476", -48, 851, 18, 32, 0], [2, "1476", -31, 849, 18, 32, 0], [2, "1664", -19, 885, 18, 64, 0], [2, "1228", 110, 789, 60, 75, 2], [2, "1228", 89, 799, 60, 75, 2], [2, "1225_1", 89, 774, 48, 44, 2], [2, "1662", 40, 846, 48, 50, 0], [2, "1662", 40, 823, 48, 50, 0], [2, "1662", 40, 800, 48, 50, 0], [2, "1672", 42, 726, 44, 88, 0], [2, "1662", 164, 907, 48, 50, 0], [2, "1662", 164, 884, 48, 50, 0], [2, "1662", 164, 861, 48, 50, 0], [2, "1672", 166, 787, 44, 88, 0], [2, "1664", -2, 890, 18, 64, 0], [2, "1664", 15, 898, 18, 64, 0], [2, "1664", 33, 906, 18, 64, 0], [2, "1664", 50, 914, 18, 64, 0], [2, "1476", 5, 866, 18, 32, 0], [2, "1476", -13, 858, 18, 32, 0], [2, "1476", 41, 887, 18, 32, 0], [2, "1476", 23, 874, 18, 32, 0], [2, "1476", 59, 895, 18, 32, 0], [2, "1476", 77, 903, 18, 32, 0], [2, "1476", 95, 900, 18, 32, 2], [2, "1664", 113, 916, 18, 64, 2], [2, "1666", 113, 968, 18, 20, 2], [2, "1666", 131, 961, 18, 20, 2], [2, "1664", 131, 906, 18, 64, 2], [2, "1666", 163, 942, 18, 20, 2], [2, "1664", 163, 887, 18, 64, 2], [2, "1664", 148, 897, 18, 64, 2], [2, "1666", 148, 951, 18, 20, 2], [2, "1476", 113, 891, 18, 32, 2], [2, "1476", 149, 873, 18, 32, 2], [2, "1476", 131, 882, 18, 32, 2], [2, "1476", 160, 864, 18, 32, 2], [2, "1642", 123, 860, 44, 125, 2], [2, "1476", -2, 924, 18, 32, 0], [2, "1476", 16, 933, 18, 32, 0], [2, "1642", -4, 859, 44, 125, 0], [2, "1663", 111, 862, 62, 59, 2], [2, "1228", -41, 793, 60, 75, 0], [2, "1228", -17, 803, 60, 75, 0], [2, "1228", 7, 815, 60, 75, 0], [2, "1229_1", -3, 770, 48, 39, 0], [2, "1229_1", 26, 784, 48, 39, 0], [2, "1229_1", 55, 797, 48, 39, 0], [2, "1228", 32, 825, 60, 75, 0], [2, "1228", 57, 837, 60, 75, 0], [2, "1228", 82, 849, 60, 75, 0], [2, "1229_1", 84, 811, 48, 39, 0], [2, "1229_1", 114, 824, 48, 39, 0], [2, "1230_1", 143, 849, 58, 34, 2], [2, "1664", 97, 920, 18, 64, 2], [2, "1666", 98, 975, 18, 20, 2], [2, "1663", -13, 859, 62, 59, 0], [2, "1666", -21, 935, 18, 20, 0], [2, "1666", 38, 965, 18, 20, 0], [2, "1666", 44, 968, 18, 20, 0], [2, "1227", 55, 830, 46, 62, 2], [2, "1227", 164, 784, 46, 62, 2], [2, "1227", 40, 722, 46, 62, 2], [2, "1662", -62, 907, 48, 50, 0], [2, "1662", -61, 883, 48, 50, 0], [2, "1662", -60, 860, 48, 50, 0], [2, "1228", 37, 823, 60, 75, 2], [2, "1229_1", 49, 798, 48, 39, 2], [2, "1230_1", -27, 836, 58, 34, 0], [2, "1228", 12, 832, 60, 75, 2], [2, "1226_1", -3, 812, 70, 47, 2], [2, "1662", 54, 955, 48, 50, 0], [2, "1662", 54, 932, 48, 50, 0], [2, "1662", 54, 909, 48, 50, 0], [2, "1672", 56, 835, 44, 88, 0], [2, "1227", 55, 827, 46, 62, 2], [2, "1672", -59, 786, 44, 88, 0], [2, "1227", -62, 779, 46, 62, 2], [2, "1664", 681, 936, 18, 64, 2], [2, "1664", 699, 927, 18, 64, 2], [2, "1664", 717, 918, 18, 64, 2], [2, "1693", 671, 915, 66, 36, 2], [2, "1671", 660, 931, 22, 74, 2], [2, "1664", 615, 970, 18, 64, 2], [2, "1664", 633, 961, 18, 64, 2], [2, "1664", 651, 952, 18, 64, 2], [2, "1693", 605, 949, 66, 36, 2], [2, "1671", 594, 965, 22, 74, 2], [2, "1672", 311, 608, 44, 88, 0], [2, "1502", 311, 687, 18, 25, 0], [2, "1672", 205, 558, 44, 88, 0], [2, "1502", 206, 639, 18, 25, 0], [2, "1283", 235, 626, 84, 72, 2], [2, "1663", 458, 13, 62, 59, 2]]}, {"type": 4, "obj": [[4, 10, 227, 174, 0, 4035], [4, 11, 306, 236, 0, 4035], [2, "1663", 818, 187, 62, 59, 0], [2, "1663", 807, 191, 62, 59, 0], [4, 12, 714, 397, 0, 4035], [4, 9, 802, 444, 0, 4035], [4, 6, 137, 523, 0, 4035], [4, 5, 394, 570, 0, 4035], [2, "891", 245, 637, 54, 75, 2], [2, "1662", 309, 681, 48, 50, 0], [4, 8, 589, 760, 0, 4035]]}, {"type": 3, "obj": [[2, "208_3", 325, 110, 78, 40, 0], [2, "313_2", 826, 293, 70, 44, 2], [2, "313_2", 868, 209, 70, 44, 2], [2, "313_2", 387, 44, 70, 44, 0], [2, "313_2", 590, 182, 70, 44, 0], [2, "208_3", 714, 934, 78, 40, 2], [2, "313_2", 905, 250, 70, 44, 0], [2, "208_3", 286, 569, 78, 40, 0], [2, "214_3", 431, 662, 54, 40, 2], [2, "1150", 39, 371, 48, 85, 0], [2, "1150", 4, 372, 48, 85, 2], [2, "216", 70, 445, 46, 46, 0], [2, "1150", 239, 467, 48, 85, 2], [2, "1150", 279, 465, 48, 85, 0], [2, "216", 399, 476, 46, 46, 0], [2, "1152", 395, 520, 38, 26, 0], [2, "1149", 371, 445, 40, 82, 0], [2, "1149", 341, 444, 40, 82, 2], [2, "1150", 314, 452, 48, 85, 0], [2, "216", 337, 436, 46, 46, 0], [2, "213_3", 290, 442, 64, 45, 2], [2, "152_3", 279, 428, 76, 40, 2], [2, "207_2", 284, 465, 38, 27, 2], [2, "1150", 148, 446, 48, 85, 2], [2, "1149", 186, 472, 40, 82, 2], [2, "1149", 209, 475, 40, 82, 0], [2, "214_3", 179, 468, 54, 40, 0], [2, "206", 176, 443, 66, 40, 2], [2, "216", 20, 364, 46, 46, 0], [2, "214_3", 277, 462, 54, 40, 2], [2, "213_3", 220, 474, 64, 45, 2], [2, "208_3", 253, 441, 78, 40, 2], [2, "206", 216, 459, 66, 40, 0], [2, "214_3", 48, 381, 54, 40, 0], [2, "214_3", 64, 428, 54, 40, 0], [2, "213_3", 106, 449, 64, 45, 0], [2, "208_3", 48, 359, 78, 40, 1], [2, "205_3", 77, 382, 54, 40, 2], [2, "208_3", 64, 412, 78, 40, 1], [2, "206", 107, 437, 66, 40, 2], [2, "205_3", 15, 338, 54, 40, 0], [2, "213_3", 388, 462, 64, 45, 2], [2, "206", 386, 457, 66, 40, 0], [2, "313_2", 111, 361, 70, 44, 2], [2, "1150", 496, 520, 48, 85, 2], [2, "313_2", 467, 442, 70, 44, 2], [2, "313_2", 423, 464, 70, 44, 2], [2, "313_2", 468, 481, 70, 44, 2], [2, "1152", 476, 545, 38, 26, 0], [2, "214_3", 501, 544, 54, 40, 0], [2, "1150", 530, 563, 48, 85, 2], [2, "214_3", 544, 562, 54, 40, 0], [2, "214_3", 558, 595, 54, 40, 0], [2, "208_3", 557, 577, 78, 40, 1], [2, "208_3", 544, 613, 78, 40, 2], [2, "1144", 404, 489, 114, 70, 2], [2, "206", 495, 514, 66, 40, 0], [2, "208_3", 538, 539, 78, 40, 0], [2, "207_2", 584, 594, 38, 27, 2], [2, "208_3", 79, 580, 78, 40, 2], [2, "205_3", 147, 588, 54, 40, 2], [2, "216", 71, 583, 46, 46, 0], [2, "214_3", 75, 571, 54, 40, 2], [2, "208_3", 49, 557, 78, 40, 2], [2, "205_3", 78, 525, 54, 40, 2], [2, "205_3", 66, 592, 54, 40, 0], [2, "208_3", 19, 510, 78, 40, 3], [2, "208_3", 200, 587, 78, 40, 2], [2, "205_3", 201, 621, 54, 40, 0], [2, "208_3", -44, 502, 78, 40, 2], [2, "208_3", 161, 594, 78, 40, 2], [2, "313_2", 708, 925, 70, 44, 2], [2, "313_2", 324, 692, 70, 44, 2], [2, "313_2", 164, 802, 70, 44, 2], [2, "313_2", 374, 96, 70, 44, 0], [2, "313_2", 455, 97, 70, 44, 2], [2, "313_2", 496, 70, 70, 44, 2], [2, "1662", 486, 60, 48, 50, 2], [2, "313_2", 772, 229, 70, 44, 0], [2, "313_2", 894, 298, 70, 44, 2], [2, "1662", 810, 214, 48, 50, 0], [2, "1662", 848, 232, 48, 50, 0], [2, "313_2", 862, 273, 70, 44, 2], [2, "313_2", 810, 252, 70, 44, 0], [2, "313_2", 547, 868, 70, 44, 0], [2, "313_2", 614, 899, 70, 44, 0], [2, "313_2", 471, 834, 70, 44, 2], [2, "313_2", 427, 814, 70, 44, 0], [2, "313_2", 52, 626, 70, 44, 0], [2, "313_2", -16, 594, 70, 44, 2], [2, "313_2", 83, 654, 70, 44, 2], [2, "313_2", 150, 675, 70, 44, 0], [2, "313_2", 350, 773, 70, 44, 2], [2, "313_2", 192, 696, 70, 44, 2], [2, "1664", -17, 535, 18, 64, 0], [2, "1664", 1, 544, 18, 64, 0], [2, "1693", -37, 524, 66, 36, 0], [2, "1671", 18, 544, 22, 74, 0], [2, "1664", 33, 563, 18, 64, 0], [2, "1664", 51, 572, 18, 64, 0], [2, "1664", 69, 581, 18, 64, 0], [2, "1693", 31, 561, 66, 36, 0], [2, "1671", 86, 581, 22, 74, 0], [2, "1664", 101, 600, 18, 64, 0], [2, "1664", 119, 609, 18, 64, 0], [2, "1664", 137, 618, 18, 64, 0], [2, "1693", 99, 598, 66, 36, 0], [2, "1671", 154, 616, 22, 74, 0], [2, "1664", 169, 635, 18, 64, 0], [2, "1664", 187, 644, 18, 64, 0], [2, "1664", 205, 653, 18, 64, 0], [2, "1693", 167, 633, 66, 36, 0], [2, "313_2", 250, 735, 70, 44, 2], [2, "1662", 792, 218, 48, 50, 0], [2, "214_3", 599, 828, 54, 40, 2], [2, "216", 701, 806, 46, 46, 0], [2, "1150", 744, 793, 48, 85, 2], [2, "1149", 775, 816, 40, 82, 2], [2, "1150", 798, 826, 48, 85, 2], [2, "214_3", 796, 812, 54, 40, 2], [2, "1149", 693, 880, 40, 82, 0], [2, "1150", 555, 658, 48, 85, 2], [2, "216", 526, 644, 46, 46, 0], [2, "1149", 588, 676, 40, 82, 2], [2, "214_3", 750, 801, 54, 40, 2], [2, "1149", 608, 678, 40, 82, 0], [2, "214_3", 634, 737, 54, 40, 0], [2, "1152", 599, 775, 38, 26, 0], [2, "214_3", 589, 680, 54, 40, 0], [2, "208_3", 351, 428, 78, 40, 0], [2, "206", 586, 655, 66, 40, 2], [2, "208_3", 629, 682, 78, 40, 0], [2, "208_3", 633, 716, 78, 40, 1], [2, "208_3", 657, 743, 78, 40, 0], [2, "208_3", 745, 779, 78, 40, 0], [2, "207_2", 813, 799, 38, 27, 2], [2, "214_3", 654, 767, 54, 40, 0], [2, "213_3", 695, 786, 64, 45, 0], [2, "208_3", 654, 751, 78, 40, 1], [2, "206", 697, 776, 66, 40, 2], [2, "1152", 400, 545, 38, 26, 0], [2, "216", 413, 703, 46, 46, 0], [2, "214_3", 414, 691, 54, 40, 2], [2, "208_3", 395, 664, 78, 40, 2], [2, "207_2", 413, 621, 38, 27, 2], [2, "205_3", 430, 634, 54, 40, 2], [2, "205_3", 403, 699, 54, 40, 0], [2, "1152", 368, 564, 38, 26, 0], [2, "1152", 402, 583, 38, 26, 0], [2, "313_2", 318, 581, 70, 44, 0], [2, "313_2", 359, 602, 70, 44, 2], [2, "1150", 540, 654, 48, 85, 2], [2, "313_2", 316, 616, 70, 44, 0], [2, "208_3", 640, 864, 78, 40, 2], [2, "205_3", 604, 846, 54, 40, 2], [2, "208_3", 612, 847, 78, 40, 2], [2, "205_3", 417, 722, 54, 40, 2], [2, "208_3", 449, 731, 78, 40, 2], [2, "208_3", 544, 784, 78, 40, 0], [2, "208_3", 574, 803, 78, 40, 2], [2, "208_3", 499, 762, 78, 40, 0], [2, "1662", 830, 236, 48, 50, 0], [2, "1657", 877, 243, 48, 47, 0], [2, "313_2", 207, 725, 70, 44, 2], [2, "1662", 204, 678, 48, 50, 0], [2, "1662", 204, 654, 48, 50, 0], [2, "1662", 204, 631, 48, 50, 0], [2, "1502", 320, 700, 18, 25, 0], [2, "1671", 232, 658, 22, 74, 0], [2, "891", 245, 677, 54, 75, 2], [2, "891", 245, 670, 54, 75, 2], [2, "1671", 298, 686, 22, 74, 0], [2, "313_2", 303, 754, 70, 44, 2], [2, "1662", 309, 728, 48, 50, 0], [2, "1662", 309, 704, 48, 50, 0], [2, "1488", 850, 251, 64, 53, 0], [2, "1502", 836, 235, 18, 25, 0], [2, "1502", 796, 216, 18, 25, 0], [2, "1662", 445, 78, 48, 50, 2], [2, "1502", 510, 59, 18, 25, 2], [2, "1502", 465, 79, 18, 25, 2], [2, "1657", 395, 72, 48, 47, 2], [2, "1488", 403, 86, 64, 53, 0], [2, "313_2", 672, -6, 70, 44, 2], [2, "313_2", 658, 29, 70, 44, 2], [2, "313_2", 815, 29, 70, 44, 0], [2, "313_2", 864, 444, 70, 44, 2], [2, "313_2", 250, 773, 70, 44, 2], [2, "313_2", 207, 763, 70, 44, 2], [2, "313_2", 218, 798, 70, 44, 2], [2, "313_2", 183, 833, 70, 44, 2], [2, "313_2", 167, 927, 70, 44, 2], [2, "1664", 344, 719, 18, 64, 0], [2, "1664", 362, 728, 18, 64, 0], [2, "1664", 380, 736, 18, 64, 0], [2, "1693", 342, 717, 66, 36, 0], [2, "1671", 396, 733, 22, 74, 0], [2, "1664", 411, 752, 18, 64, 0], [2, "1664", 429, 761, 18, 64, 0], [2, "1664", 447, 769, 18, 64, 0], [2, "1693", 409, 750, 66, 36, 0], [2, "1671", 463, 766, 22, 74, 0], [2, "1664", 478, 785, 18, 64, 0], [2, "1664", 496, 794, 18, 64, 0], [2, "1664", 514, 802, 18, 64, 0], [2, "1693", 476, 783, 66, 36, 0], [2, "1671", 530, 799, 22, 74, 0], [2, "1664", 544, 818, 18, 64, 0], [2, "1664", 562, 827, 18, 64, 0], [2, "1664", 580, 835, 18, 64, 0], [2, "1693", 542, 816, 66, 36, 0], [2, "1671", 596, 832, 22, 74, 0], [2, "1664", 611, 851, 18, 64, 0], [2, "1664", 629, 860, 18, 64, 0], [2, "1664", 647, 868, 18, 64, 0], [2, "1693", 609, 849, 66, 36, 0], [2, "1671", 663, 865, 22, 74, 0], [2, "1664", 674, 884, 18, 64, 0], [2, "1664", 692, 893, 18, 64, 0], [2, "1664", 710, 901, 18, 64, 0], [2, "1693", 672, 882, 66, 36, 0], [2, "1671", 726, 898, 22, 74, 0], [2, "1488", 510, 863, 64, 53, 0], [2, "313_2", 372, 806, 70, 44, 2], [2, "1488", 630, -8, 64, 53, 2], [2, "1152", 435, 563, 38, 26, 0], [2, "1150", 860, 836, 48, 85, 0], [2, "1150", 825, 837, 48, 85, 2], [2, "216", 891, 917, 46, 46, 0], [2, "216", 841, 829, 46, 46, 0], [2, "214_3", 869, 846, 54, 40, 0], [2, "214_3", 885, 900, 54, 40, 0], [2, "208_3", 869, 824, 78, 40, 1], [2, "205_3", 898, 854, 54, 40, 2], [2, "208_3", 885, 884, 78, 40, 1], [2, "205_3", 836, 803, 54, 40, 0], [2, "213_3", 926, 919, 64, 45, 0], [2, "206", 928, 909, 66, 40, 2], [2, "313_2", 903, 826, 70, 44, 2], [2, "313_2", 642, 615, 70, 44, 2], [2, "313_2", 611, 591, 70, 44, 2], [2, "313_2", 604, 626, 70, 44, 2], [2, "313_2", 664, 654, 70, 44, 0], [2, "1150", -21, 376, 48, 85, 0], [2, "208_3", -52, 356, 78, 40, 2], [2, "207_2", 258, 579, 38, 27, 2], [2, "313_2", 331, 654, 70, 44, 0], [2, "208_3", 524, 628, 78, 40, 0], [2, "205_3", 543, 609, 54, 40, 2], [2, "313_2", 126, 16, 70, 44, 2], [2, "313_2", 112, 51, 70, 44, 2], [2, "1488", 84, 14, 64, 53, 0], [2, "313_2", 613, 210, 70, 44, 0], [2, "313_2", 644, 186, 70, 44, 2], [2, "313_2", 569, 224, 70, 44, 2], [2, "313_2", 819, 451, 70, 44, 2], [2, "313_2", 356, 132, 70, 44, 2], [2, "313_2", 369, 308, 70, 44, 2], [2, "1662", 487, 36, 48, 50, 2], [2, "1662", 446, 54, 48, 50, 2], [2, "1646", 927, 276, 26, 28, 0], [2, "1646", 835, 434, 26, 28, 0], [2, "1646", 644, 652, 26, 28, 0], [2, "1646", 886, 828, 26, 28, 0], [2, "1646", 204, 443, 26, 28, 0], [2, "1646", 407, 304, 26, 28, 0], [2, "1646", 387, 162, 26, 28, 0], [2, "1646", 171, 32, 26, 28, 0], [2, "1646", 772, 343, 26, 28, 0], [2, "1646", 782, 477, 26, 28, 2], [2, "207_2", 183, 256, 38, 27, 2], [2, "207_2", 237, 327, 38, 27, 2], [2, "207_2", 126, 183, 38, 27, 2], [2, "208_3", 177, 86, 78, 40, 0], [2, "208_3", 351, 253, 78, 40, 1], [2, "206", 654, 452, 66, 40, 2], [2, "207_2", 682, 493, 38, 27, 2], [2, "208_3", 714, 482, 78, 40, 0], [2, "208_3", 643, 344, 78, 40, 1], [2, "207_2", 819, 372, 38, 27, 2]]}, {"type": 2, "data": [22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 19, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 40, 40, 40, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 35, 31, 37, 37, 37, 37, 31, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 27, -1, -1, -1, -1, -1, -1, 38, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 24, -1, -1, -1, -1, -1, -1, 38, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 25, 24, -1, -1, -1, -1, -1, 38, 41, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 27, -1, -1, -1, -1, -1, -1, 33, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 27, -1, -1, -1, 26, 25, 25, 34, 34, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 19, 20, 26, 25, 29, 28, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 29, 28, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 34, 34, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 37, 37, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 37, 22, 22, 22, 22, 22, 22, 22, 22, 22, 41, 40, 37, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 27, -1, -1, 38, 37, 37, 41, 22, 22, 22, 22, 22, 38, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 27, 20, -1, -1, -1, -1, 21, 22, 22, 22, 22, 22, -1, 38, 37, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 20, -1, -1, -1, 26, 21, 22, 22, 22, 22, 22, -1, -1, -1, 41, 40, 37, 37, 37, 37, 22, 22, 37, 34, 35, 37, 37, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 19, 20, -1, 26, 29, 28, 22, 22, 22, 22, 22, -1, -1, -1, 38, 37, 41, 40, 41, 40, 37, 37, 30, 31, 32, 41, 40, 38, 37, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 25, 29, 28, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, 38, 37, 38, 37, 41, 40, -1, -1, -1, 38, 37, 38, 27, 38, 37, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 19, 20, -1, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 38, 33, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 20, -1, -1, -1, -1, 28, 27, -1, -1, -1, -1, 26, 25, 19, 19, 20, -1, -1, 38, 41, 40, 33, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 21, 22, 23, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, 30, 41, 40, 22, 23, 25, 24, -1, 38, 37, 33, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 41, 40, 40, 22, 23, 19, 20, -1, -1, -1, -1, -1, -1, -1, 38, 40, 33, 23, 28, 27, -1, -1, 33, 34, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 38, 37, 34, 40, 34, 22, 23, 19, 20, -1, -1, -1, -1, -1, -1, -1, 33, 28, 28, 27, -1, -1, 30, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, 38, 37, 41, 40, 40, 22, 19, 20, 19, 19, 20, -1, -1, -1, 33, 28, 34, 35, -1, -1, 30, 38, 37, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, 38, 37, 37, 40, 22, 23, 22, 22, 23, -1, -1, -1, 38, 37, 31, 32, -1, -1, 38, 37, 41, 38, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, 38, 37, -1, -1, 22, 22, 22, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, 33, 34, 40, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 22, 23, 19, 19, 20, -1, -1, -1, -1, -1, 30, 31, 37, 38, 41, 40, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 22, 22, 23, 19, 20, -1, -1, -1, -1, -1, -1, 38, 38, 37, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, -1, 41, 40, 22, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, 22, 23, 37, 22, 22, 22, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 40, 22, 23, 19, 20, -1, -1, -1, -1, -1, -1, -1, 41, 19, 37, 37, 37, 22, 22, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 22, 23, 19, 20, -1, -1, -1, -1, -1, 38, 37, 41, 40, -1, 37, 37, 22, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 22, 23, 19, 20, -1, -1, -1, -1, -1, 38, 37, 41, 40, 19, 37, 22, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 22, 23, 19, 20, -1, -1, -1, -1, -1, 38, 37, 41, 25, 22, 22, -1, -1, -1, -1, 19, 19, 19, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, 22, 23, 19, 20, -1, -1, -1, -1, -1, 33, 40, 37, 37, -1, -1, -1, -1, 22, 22, 22, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, -1, -1, 40, 39, -1, -1, -1, -1, -1, 30, 31, 28, 25]}, {"type": 2, "data": [-1, -1, -1, 18, 19, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 31, 41, 23, 25, 25, 24, -1, -1, -1, 42, 53, 51, -1, -1, -1, -1, 33, 34, 22, 28, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 25, 24, -1, -1, -1, -1, 38, 37, 37, 36, 50, 49, 48, -1, 45, 59, 61, 60, -1, -1, -1, 30, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 28, 27, -1, -1, -1, -1, -1, -1, -1, 42, 53, 52, 47, 49, 63, -1, -1, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 19, -1, 40, 39, -1, 62, -1, -1, -1, -1, -1, -1, -1, -1, 57, 52, 52, 52, 52, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 21, 22, 38, 37, 36, 58, 58, 49, 48, -1, -1, -1, -1, 50, 49, 53, 58, 59, 61, 61, 60, -1, -1, 12, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 65, 52, 47, 49, 48, -1, 50, 53, 52, 52, 59, 56, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 61, 65, 58, 47, 49, 53, 52, 59, 61, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 55, 65, 58, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 53, 52, 52, 58, 47, 48, -1, -1, 26, 25, 59, 25, 24, -1, -1, 50, 49, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 49, 49, 49, 53, 52, 52, 52, 64, 52, 63, -1, 18, 29, 28, 63, 28, 27, 25, 24, 53, 64, 63, -1, -1, 42, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 53, 52, 52, 52, 52, 59, 61, 55, 65, 64, 51, 50, 49, -1, 57, 47, 28, 27, 28, 27, 55, 61, 60, -1, -1, 45, 46, 47, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 53, 52, 58, 59, 55, 55, 56, -1, -1, 57, 64, 47, 53, 58, 58, 58, 55, 61, 60, 28, 27, -1, -1, -1, -1, -1, 54, 55, 61, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 52, 52, 58, 63, -1, -1, -1, -1, -1, 54, 55, 65, 58, 58, 58, 59, 65, 43, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 52, 52, 58, 51, -1, -1, -1, -1, -1, -1, -1, 62, 61, 61, 61, 60, 54, 41, 40, 39, -1, 18, 19, 20, -1, -1, -1, -1, 42, 43, 44, -1, -1, -1, -1, -1, -1, -1, -1, 54, 65, 58, 58, 47, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 36, -1, 29, 40, 27, -1, -1, -1, -1, 45, 46, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 65, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 36, -1, -1, -1, -1, -1, 54, 55, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 52, 52, 47, 48, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 64, 52, 52, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 40, 40, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, 50, 53, 52, -1, -1, -1, 42, 53, 64, 52, 52, 63, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 41, 40, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, 45, 52, 59, 55, 56, -1, 53, 53, 52, 64, 59, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 37, 37, 41, 40, -1, -1, -1, -1, -1, -1, 57, 52, 51, -1, -1, -1, -1, 52, 52, 61, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 41, 40, -1, -1, -1, -1, 42, 34, 35, 51, 41, 40, 39, -1, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, 38, 37, 41, 40, 40, 34, 35, 31, 32, 47, 38, 37, 41, 40, 39, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, 43, 44, -1, -1, -1, -1, 37, 36, -1, -1, -1, 18, 19, 19, 38, 37, 37, 31, 32, 65, 52, 52, 51, -1, 38, 37, 41, 40, 39, -1, -1, -1, -1, -1, -1, -1, 42, 43, 53, 46, 46, 47, 49, 48, -1, -1, -1, -1, -1, -1, -1, 30, 31, -1, -1, -1, -1, -1, -1, 57, 52, 52, 51, -1, -1, -1, 38, 33, 34, -1, -1, -1, -1, -1, -1, -1, 57, 58, 52, 52, 52, 52, 52, 51, -1, -1, 41, 40, -1, 18, 19, -1, 38, 37, 36, -1, -1, -1, -1, -1, 52, 52, 47, 48, -1, -1, -1, 30, 31, 18, 19, 26, 25, 24, -1, -1, 54, 55, 65, 64, 52, 52, 64, 63, -1, -1, 38, 41, 40, 21, 22, 40, -1, 25, 25, 25, 24, 42, -1, -1, -1, 46, -1, -1, -1, -1, -1, -1, 33, 34, 31, 41, 40, 40, 25, -1, -1, -1, 62, 65, 52, 52, 52, 47, 48, -1, 18, 19, 37, 41, 40, 40, 55, 28, 28, 28, 27, 54, -1, 61, 61, -1, -1, -1, -1, -1, -1, 33, 30, 31, -1, 38, 37, 37, 28, 28, 27, -1, -1, 57, 52, 52, 52, 52, 51, -1, 38, 37, 36, 38, 37, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 52, 52, 64, 63, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, -1, 38, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 65, 58, 59, 61, 60, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, -1, -1, -1, -1, 41, 40, -1, -1, -1, -1, -1, -1, -1, 62, 61, 60, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 42, 43, 37, 58, 59, 40, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, -1, 38, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 46, 64, 59, 33, 34, 35, 40, -1, -1, -1, -1, -1, -1, 28, 27, -1, -1, 38, 41, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 49, 49, 53, 46, 46, 63, 38, 37, 36, 37, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, 41, 40, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 46, -1, 46, 59, 61, 60, -1, -1, -1, -1, 38, 37, 41, 40, -1, -1, -1, -1, -1, -1, -1, 38, 37, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 46, 63, -1, -1, -1, 50, 49, 48, -1, -1, 38, 41, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, 61, 60, -1, -1, 42, 53, 52, 47, 44, -1, -1, 30, 41, 40, 40, 40, -1, -1, -1, -1, -1, 25, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 54, 65, 64, 64, 63, -1, -1, -1, 33, 34, 40, 37, 41, 40, -1, -1, -1, 28, 25, 24, -1, -1, 41, 40, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 24, -1, -1, -1, 62, 61, 61, 60, -1, -1, -1, 30, 31, 37, 36, 38, 37, 41, 40, -1, -1, 28, 27, 24, -1, 38, 41, 40, -1, -1, -1, -1, 40, 40, -1, -1, -1, -1, 34, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 37, -1, -1, -1, 28, 25, 24, -1, 38, 41, 40, -1, -1]}, {"type": 2, "data": [9, 10, 11, 9, 10, 11, 9, 10, 11, 12, 9, 10, 9, 10, 11, 9, 10, 9, 10, 11, 11, 9, 10, 11, 11, 13, 14, 10, 11, 9, 10, 11, 16, 17, 9, 10, 11, 9, 10, 11, 12, 13, 14, 12, 13, 14, 12, 13, 9, 10, 9, 10, 11, 10, 11, 12, 13, 12, 13, 14, 14, 12, 13, 14, 14, 16, 17, 13, 14, 12, 9, 10, 11, 13, 12, 13, 14, 12, 13, 14, 15, 16, 17, 15, 16, 9, 10, 11, 12, 13, 9, 10, 11, 10, 11, 15, 16, 15, 16, 17, 17, 15, 16, 17, 17, 9, 15, 16, 17, 15, 12, 13, 14, 15, 15, 16, 17, 15, 16, 17, 15, 16, 9, 10, 11, 9, 10, 11, 9, 10, 12, 13, 14, 9, 9, 10, 11, 11, 16, 17, 12, 13, 12, 9, 10, 11, 13, 9, 10, 11, 15, 16, 17, 10, 11, 9, 10, 15, 16, 17, 9, 10, 12, 13, 14, 12, 13, 14, 12, 13, 15, 16, 17, 12, 12, 13, 14, 14, 9, 10, 11, 16, 15, 12, 13, 14, 10, 11, 13, 14, 15, 16, 17, 13, 14, 9, 10, 11, 9, 10, 12, 13, 15, 16, 17, 15, 66, 67, 68, 66, 67, 68, 66, 67, 15, 16, 17, 17, 10, 11, 11, 9, 10, 11, 11, 17, 13, 14, 16, 17, 11, 9, 15, 16, 17, 12, 13, 14, 12, 13, 15, 16, 17, 17, 9, 10, 69, 70, 71, 69, 70, 71, 69, 70, 71, 69, 70, 12, 13, 14, 14, 12, 13, 14, 14, 15, 16, 17, 12, 13, 9, 12, 13, 14, 15, 15, 16, 17, 15, 16, 15, 15, 16, 17, 12, 13, 72, 73, 74, 72, 73, 74, 72, 73, 74, 72, 73, 15, 16, 17, 10, 15, 16, 17, 17, 10, 11, 11, 15, 16, 12, 15, 16, 17, 12, 9, 10, 11, 10, 10, 12, 13, 12, 13, 15, 16, 17, 67, 68, 66, 67, 68, 66, 67, 68, 66, 67, 15, 16, 17, 13, 14, 12, 13, 9, 10, 11, 11, 10, 9, 10, 9, 10, 11, 15, 9, 10, 11, 13, 9, 11, 16, 15, 16, 17, 15, 16, 70, 71, 69, 70, 71, 69, 70, 71, 69, 70, 12, 13, 14, 16, 17, 15, 16, 12, 13, 14, 9, 10, 11, 13, 12, 13, 14, 10, 11, 13, 14, 9, 12, 12, 13, 12, 13, 14, 13, 14, 72, 73, 72, 73, 74, 72, 73, 74, 72, 73, 15, 16, 17, 9, 10, 11, 9, 10, 9, 10, 12, 13, 14, 16, 15, 16, 17, 13, 9, 10, 11, 9, 15, 15, 16, 15, 16, 17, 10, 11, 14, 13, 66, 67, 68, 66, 67, 68, 12, 13, 12, 13, 14, 12, 13, 14, 12, 13, 12, 13, 15, 16, 17, 10, 11, 9, 9, 10, 11, 13, 14, 12, 13, 9, 10, 11, 10, 11, 13, 9, 10, 11, 69, 70, 71, 69, 70, 71, 11, 9, 15, 16, 17, 10, 11, 9, 10, 11, 9, 9, 10, 11, 12, 13, 14, 10, 11, 9, 10, 11, 9, 15, 16, 12, 13, 14, 13, 14, 16, 12, 9, 10, 11, 73, 74, 72, 9, 10, 11, 11, 13, 14, 12, 13, 14, 9, 10, 11, 9, 10, 9, 10, 11, 9, 9, 10, 9, 10, 11, 14, 12, 13, 14, 9, 10, 11, 13, 14, 9, 10, 12, 13, 14, 10, 12, 9, 12, 13, 14, 14, 16, 17, 15, 16, 17, 12, 13, 14, 12, 13, 12, 9, 10, 11, 12, 13, 12, 13, 9, 10, 11, 16, 17, 12, 13, 14, 10, 11, 11, 13, 15, 16, 17, 13, 9, 10, 15, 16, 17, 17, 11, 10, 11, 9, 10, 15, 16, 17, 15, 16, 11, 67, 68, 66, 67, 68, 15, 10, 12, 13, 14, 11, 9, 0, 1, 2, 9, 10, 11, 9, 10, 17, 11, 16, 12, 13, 14, 15, 12, 13, 14, 13, 14, 12, 13, 12, 13, 12, 13, 15, 14, 70, 71, 69, 70, 71, 69, 70, 9, 10, 11, 14, 12, 3, 4, 5, 12, 13, 14, 10, 11, 13, 14, 17, 15, 16, 17, 15, 15, 16, 17, 16, 17, 15, 9, 15, 16, 15, 9, 10, 17, 73, 74, 72, 73, 74, 72, 73, 12, 13, 14, 9, 10, 6, 7, 8, 15, 16, 17, 13, 14, 17, 13, 14, 12, 9, 10, 11, 13, 14, 9, 10, 11, 10, 11, 11, 9, 10, 9, 10, 11, 67, 68, 66, 67, 68, 66, 67, 9, 10, 11, 9, 10, 0, 1, 2, 3, 4, 15, 16, 17, 11, 13, 14, 15, 12, 13, 14, 16, 17, 12, 13, 14, 13, 9, 10, 12, 13, 12, 13, 14, 11, 71, 69, 70, 71, 69, 70, 12, 13, 14, 12, 13, 3, 4, 5, 6, 7, 6, 7, 8, 2, 0, 1, 2, 10, 0, 1, 2, 0, 1, 2, 17, 9, 10, 11, 15, 16, 15, 16, 17, 14, 74, 72, 73, 74, 9, 10, 11, 16, 17, 15, 16, 6, 7, 8, 6, 7, 8, 3, 4, 5, 3, 4, 0, 1, 0, 1, 2, 3, 4, 5, 9, 12, 13, 14, 15, 16, 17, 15, 16, 17, 12, 15, 16, 17, 12, 13, 14, 10, 11, 12, 13, 12, 13, 14, 9, 10, 0, 1, 2, 0, 0, 1, 0, 1, 0, 1, 2, 6, 7, 8, 0, 15, 16, 17, 15, 16, 17, 15, 16, 17, 15, 16, 15, 16, 15, 16, 17, 13, 14, 9, 10, 11, 10, 11, 12, 13, 3, 4, 5, 3, 3, 4, 3, 4, 3, 4, 5, 1, 2, 2, 3, 4, 5, 12, 9, 10, 15, 16, 17, 15, 15, 16, 17, 9, 10, 11, 15, 16, 17, 12, 13, 14, 13, 14, 12, 13, 6, 7, 8, 6, 6, 7, 6, 7, 6, 7, 8, 4, 5, 5, 6, 7, 8, 1, 2, 13, 9, 10, 11, 17, 9, 10, 11, 12, 13, 14, 9, 10, 11, 15, 16, 17, 10, 11, 15, 16, 17, 11, 12, 9, 10, 11, 11, 14, 12, 9, 6, 7, 8, 8, 4, 5, 3, 4, 5, 10, 9, 10, 11, 11, 12, 13, 14, 15, 16, 17, 12, 13, 14, 13, 14, 12, 13, 14, 9, 10, 11, 11, 15, 9, 10, 11, 14, 17, 15, 12, 13, 14, 12, 6, 7, 8, 6, 7, 8, 11, 12, 13, 9, 10, 11, 10, 11, 9, 10, 11, 9, 10, 11, 16, 17, 15, 9, 10, 11, 13, 14, 14, 10, 12, 13, 14, 17, 15, 16, 15, 16, 17, 15, 16, 0, 1, 2, 2, 13, 14, 11, 16, 12, 13, 14, 13, 14, 12, 13, 14, 12, 13, 14, 10, 11, 9, 12, 13, 14, 9, 9, 10, 11, 9, 9, 10, 11, 10, 11, 11, 9, 10, 11, 9, 3, 4, 5, 5, 1, 2, 14, 9, 15, 16, 17, 9, 10, 11, 10, 11, 15, 16, 17, 13, 9, 12, 15, 16, 17, 10, 11, 13, 14, 12, 12, 13, 14, 13, 14, 14, 12, 13, 14, 12, 6, 7, 8, 8, 4, 5, 17, 12, 13, 14, 10, 12, 13, 14, 13, 14, 11, 17, 15, 16, 12, 15, 16, 17, 12, 13, 14, 9, 10, 11, 9, 10, 11, 16, 9, 10, 11, 16, 17, 15, 6, 7, 8, 0, 1, 2, 2, 1, 2, 17, 13, 15, 16, 17, 16, 17, 9, 10, 11, 11, 15, 9, 10, 11, 15, 16, 17, 12, 13, 14, 12, 13, 14, 11, 12, 9, 10, 11, 9, 10, 11, 9, 10, 3, 4, 0, 1, 2, 5, 15, 16, 12, 13, 14, 15, 16, 12, 13, 14, 14, 9, 12, 13, 9, 10, 11, 10, 15, 16, 17, 15, 16, 17, 14, 15, 9, 10, 11, 12, 13, 14, 12, 13, 6, 7, 3, 4, 5, 8, 13, 14, 15, 16, 17, 12, 13, 15, 16, 17, 17, 12, 15, 16, 12, 13, 14, 13, 9, 10, 11, 15, 16, 17, 9, 10, 11, 13, 14, 15, 16, 17, 9, 10, 15, 16, 6, 7, 8, 0, 1, 2, 9, 10, 11, 15, 9, 10, 11, 9, 10, 11, 13, 14, 15, 16, 17, 16, 12, 13, 14, 9, 10, 11, 9, 10, 11, 16, 17, 12, 13, 14, 12, 13, 15, 16, 15, 16, 17, 3, 4, 0, 1, 2, 14, 16, 12, 13, 14, 12, 13, 14, 16, 17, 9, 10, 11, 9, 10, 11, 17, 12, 13, 14, 12, 13, 14, 9, 10, 15, 16, 17, 15, 16, 17, 16, 17, 9, 10, 6, 7, 3, 0, 1, 2, 2, 15, 16, 17, 15, 16, 17, 9, 10, 11, 13, 14, 12, 13, 14, 9, 15, 16, 17, 15, 9, 10, 12, 13, 9, 10, 11, 13, 14, 9, 10, 11, 9, 10, 11, 10, 6, 3, 4, 5, 5, 11, 16, 12, 9, 10, 11, 12, 13, 14, 16, 17, 9, 10, 9, 12, 13, 9, 10, 11, 9, 9, 15, 16, 12, 13, 14, 10, 11, 12, 13, 14, 12, 13, 14, 13, 14, 6, 0, 1, 0, 1, 2, 15, 12, 13, 14, 15, 16, 9, 10, 11, 12, 13, 12, 15, 16, 12, 13, 14, 12, 12, 13, 14, 9, 10, 11, 13, 14, 15, 16, 17, 15, 16, 9, 10, 11, 9, 3, 4, 3, 4, 5, 0, 1, 2, 17, 15, 16, 12, 13, 14, 15, 16, 15, 16, 17, 15, 16, 17, 15, 15, 16, 17, 12, 13, 14, 9, 10, 11, 11, 16, 17, 12, 12, 13, 14, 12, 6, 7, 6, 7, 0, 3, 4, 5, 17]}], "blocks": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}