{"mW": 720, "mH": 960, "tW": 24, "tH": 24, "tiles": [["135", 0, 1, 1], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["203_6", 0, 2, 1], ["203_7", 0, 2, 1], ["203_8", 0, 2, 1], ["203_5", 0, 2, 1], ["106_1", 0, 3, 3], ["255", 0, 1, 1], ["184", 0, 1, 1], ["304", 0, 3, 2], ["304", 2, 3, 2], ["304", 1, 3, 2], ["304", 3, 3, 2], ["302", 0, 2, 2], ["490", 0, 3, 2], ["490", 2, 3, 2], ["490", 1, 3, 2], ["490", 3, 3, 2], ["491", 0, 3, 2], ["491", 2, 3, 2], ["491", 1, 3, 2], ["491", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "88", 116, 536, 88, 61, 0], [2, "87", 156, 291, 72, 57, 0], [2, "88", 121, 248, 88, 61, 0], [2, "87", 106, 280, 72, 57, 0], [2, "87", 45, 493, 72, 57, 0], [2, "86", -16, 600, 50, 49, 0], [2, "87", 97, 216, 72, 57, 0], [2, "88", 11, 218, 88, 61, 2], [2, "88", 65, 229, 88, 61, 2], [2, "88", 355, 312, 88, 61, 2], [2, "88", 340, 342, 88, 61, 0], [2, "240", 24, 302, 12, 13, 0], [2, "240", 36, 308, 12, 13, 0], [2, "258", 31, 298, 16, 39, 0], [2, "233", 66, 621, 44, 54, 0], [2, "233", 45, 627, 44, 54, 0], [2, "232", 84, 660, 46, 45, 0], [2, "232", 56, 671, 46, 45, 0], [2, "237", 78, 653, 36, 22, 0], [2, "237", 54, 664, 36, 22, 0], [2, "236", 64, 652, 26, 22, 0], [2, "233", 18, 637, 44, 54, 0], [2, "239", 16, 615, 80, 36, 0], [2, "232", 33, 680, 46, 45, 0], [2, "237", 28, 676, 36, 22, 0], [2, "9", 408, 736, 20, 16, 0], [2, "268", 405, 305, 106, 82, 2], [2, "88", -29, 452, 88, 61, 0], [2, "259", 21, 277, 18, 32, 0], [2, "236", 83, 285, 26, 22, 0], [2, "238", 114, 226, 30, 42, 0], [2, "233", 85, 254, 44, 54, 0], [2, "233", 64, 260, 44, 54, 0], [2, "232", 103, 293, 46, 45, 0], [2, "260", 3, 306, 26, 34, 0], [2, "233", 37, 270, 44, 54, 0], [2, "232", 75, 304, 46, 45, 0], [2, "232", 52, 313, 46, 45, 0], [2, "237", 97, 286, 36, 22, 0], [2, "237", 78, 295, 36, 22, 0], [2, "237", 52, 307, 36, 22, 0], [2, "239", 35, 248, 80, 36, 0], [2, "87", 28, 943, 72, 57, 2], [2, "88", 96, 925, 88, 61, 0], [2, "268", 47, 885, 106, 82, 2], [2, "88", 43, 180, 88, 61, 2], [2, "268", -9, 414, 106, 82, 2], [2, "88", 30, 476, 88, 61, 0], [2, "87", 155, 555, 72, 57, 0], [2, "268", 53, 497, 106, 82, 0], [2, "87", 106, 519, 72, 57, 0], [2, "88", 47, 552, 88, 61, 0], [2, "88", 137, 545, 88, 61, 0], [2, "87", -44, 499, 72, 57, 2], [2, "88", 47, 447, 88, 61, 2], [2, "88", 369, 337, 88, 61, 2], [2, "88", 600, 304, 88, 61, 2], [2, "88", 630, 294, 88, 61, 2], [2, "238", 95, 593, 30, 42, 0], [2, "268", 92, 487, 106, 82, 2], [2, "87", 439, 372, 72, 57, 0], [2, "88", 567, 805, 88, 61, 2], [2, "88", 583, 788, 88, 61, 2], [2, "87", 660, 831, 72, 57, 2], [2, "87", 633, 637, 72, 57, 2], [2, "268", 652, 572, 106, 82, 2], [2, "88", 707, 623, 88, 61, 2], [2, "88", 640, 780, 88, 61, 2], [2, "268", 609, 762, 106, 82, 2]]}, {"type": 4, "obj": [[2, "245", 305, 27, 22, 22, 2], [2, "244", 321, 12, 20, 38, 2], [2, "245", 290, 42, 22, 22, 2], [2, "244", 276, 48, 20, 38, 2], [2, "244", 277, 95, 20, 38, 2], [2, "245", 285, 113, 22, 22, 0], [2, "276", 361, 10, 92, 134, 0], [2, "245", 304, 129, 22, 22, 0], [2, "244", 317, 126, 20, 38, 0], [2, "263", 413, 215, 34, 34, 0], [2, "245", 460, 228, 22, 22, 0], [2, "244", 453, 218, 20, 38, 0], [2, "276", 574, 127, 92, 134, 0], [2, "245", 478, 244, 22, 22, 0], [2, "245", 497, 261, 22, 22, 0], [2, "244", 511, 263, 20, 38, 0], [2, "263", 537, 267, 34, 34, 0], [2, "85", 328, 249, 48, 53, 2], [2, "245", 583, 281, 22, 22, 0], [2, "244", 573, 268, 20, 38, 0], [2, "245", 601, 297, 22, 22, 0], [2, "244", 709, 286, 20, 38, 2], [2, "245", 693, 304, 22, 22, 2], [2, "244", 613, 295, 20, 38, 0], [2, "225", -6, 390, 16, 17, 0], [2, "225", 10, 398, 16, 17, 0], [2, "225", 26, 406, 16, 17, 0], [2, "225", 42, 414, 16, 17, 0], [2, "226", 88, 421, 16, 16, 0], [2, "225", 74, 421, 16, 17, 2], [2, "225", 58, 422, 16, 17, 0], [2, "226", 99, 432, 16, 16, 0], [2, "24", 394, 418, 28, 38, 0], [2, "24", 613, 418, 28, 38, 2], [2, "24", 431, 419, 28, 38, 2], [2, "24", 647, 419, 28, 38, 0], [2, "226", 110, 443, 16, 16, 0], [2, "89", 408, 365, 48, 95, 0], [2, "263", 658, 427, 34, 34, 0], [2, "226", 121, 454, 16, 16, 0], [2, "24", 431, 438, 28, 38, 0], [2, "24", 396, 440, 28, 38, 2], [2, "226", 132, 465, 16, 16, 0], [2, "279", 141, 475, 8, 15, 0], [2, "90", 413, 465, 28, 36, 0], [2, "267", 579, 439, 46, 72, 0], [2, "90", 435, 477, 28, 36, 1], [2, "267", 619, 456, 46, 72, 0], [2, "267", 661, 476, 46, 72, 0], [2, "219_1", 16, 550, 36, 30, 2], [2, "89", 17, 496, 48, 95, 0], [2, "220_1", 17, 569, 40, 29, 0], [2, "73", 334, 537, 46, 72, 0], [2, "14_1", 29, 582, 32, 30, 2], [2, "219_1", 345, 585, 36, 30, 2], [2, "263", 338, 587, 34, 34, 0], [2, "14_1", 138, 614, 32, 30, 2], [2, "220_1", 109, 617, 40, 29, 2], [2, "24", -4, 612, 28, 38, 2], [2, "219_1", 121, 627, 36, 30, 2], [2, "89", 116, 562, 48, 95, 0], [2, "220_1", 121, 629, 40, 29, 0], [2, "14_1", 151, 631, 32, 30, 0], [2, "24", 25, 625, 28, 38, 2], [2, "14_1", 135, 641, 32, 30, 2], [2, "73", 506, 630, 46, 72, 0], [2, "219_1", 525, 674, 36, 30, 2], [2, "47", 211, 654, 54, 63, 0], [2, "24", 682, 685, 28, 38, 0], [2, "89", 691, 640, 48, 95, 2], [2, "24", 681, 706, 28, 38, 2], [2, "24", 713, 720, 28, 38, 2], [2, "86", -4, 731, 50, 49, 0], [2, "86", 368, 742, 50, 49, 0], [2, "24", 419, 765, 28, 38, 2], [2, "72", 351, 767, 42, 44, 0], [2, "1", 392, 700, 40, 111, 0], [2, "24", 449, 780, 28, 38, 2], [2, "72", 322, 782, 42, 44, 0], [2, "24", 288, 793, 28, 38, 0], [2, "72", 255, 810, 42, 44, 0], [2, "72", 224, 824, 42, 44, 0], [2, "24", 658, 863, 28, 38, 2], [2, "86", 176, 859, 50, 49, 0], [2, "24", 169, 878, 28, 38, 2], [2, "24", 196, 879, 28, 38, 0], [2, "89", 630, 822, 48, 95, 0], [2, "24", 691, 880, 28, 38, 2], [2, "24", 678, 918, 28, 38, 0], [2, "86", 685, 908, 50, 49, 0], [2, "24", 674, 938, 28, 38, 2]]}, {"type": 3, "obj": [[2, "10", 560, 23, 50, 26, 3], [2, "10", 584, 11, 50, 26, 1], [2, "249", 528, 1, 48, 72, 0], [2, "250", 536, -45, 32, 62, 0], [2, "92", 560, 47, 40, 45, 0], [2, "92", 587, 61, 40, 45, 0], [2, "92", 616, 76, 40, 45, 0], [2, "92", 644, 90, 40, 45, 0], [2, "92", 673, 104, 40, 45, 0], [2, "10", 609, 16, 50, 26, 0], [2, "10", 660, 41, 50, 26, 2], [2, "10", 635, 29, 50, 26, 1], [2, "10", 685, 53, 50, 26, 1], [2, "10", 584, 29, 50, 26, 2], [2, "10", 610, 42, 50, 26, 1], [2, "10", 635, 54, 50, 26, 2], [2, "10", 660, 66, 50, 26, 1], [2, "10", 561, 40, 50, 26, 2], [2, "10", 610, 67, 50, 26, 1], [2, "10", 585, 55, 50, 26, 2], [2, "10", 635, 79, 50, 26, 0], [2, "10", 661, 92, 50, 26, 3], [2, "10", 686, 79, 50, 26, 1], [2, "10", 687, 105, 50, 26, 0], [2, "253", 632, 445, 92, 53, 0], [2, "244", 493, 370, 20, 38, 0], [2, "87", -15, 276, 72, 57, 0], [2, "88", -12, 248, 88, 61, 0], [2, "272", 311, 170, 72, 54, 0], [2, "271", 259, 198, 64, 50, 0], [2, "245", 387, 289, 22, 22, 0], [2, "244", 404, 297, 20, 38, 0], [2, "245", 505, 384, 22, 22, 0], [2, "245", 524, 400, 22, 22, 0], [2, "245", 630, 400, 22, 22, 2], [2, "25_1", -46, 365, 76, 45, 0], [2, "227", 165, 405, 6, 16, 0], [2, "228", 160, 445, 6, 9, 0], [2, "28", 69, 446, 12, 27, 0], [2, "28", 194, 387, 12, 27, 0], [2, "219_1", 248, 350, 36, 30, 0], [2, "36_1", 261, 358, 140, 103, 0], [2, "220_1", 257, 367, 40, 29, 0], [2, "48", 353, 898, 52, 38, 2], [2, "18_1", 300, 899, 88, 64, 2], [2, "18_1", 471, 875, 88, 64, 2], [2, "18_1", 215, 827, 88, 64, 0], [2, "48", 561, 918, 52, 38, 0], [2, "48", 452, 914, 52, 38, 2], [2, "48", 269, 932, 52, 38, 2], [2, "35", 341, 919, 22, 16, 2], [2, "26", 328, 890, 10, 25, 0], [2, "26", 342, 895, 10, 25, 0], [2, "26", 509, 871, 10, 25, 0], [2, "41", 526, 898, 12, 11, 0], [2, "41", 489, 876, 12, 11, 2], [2, "41", 497, 890, 12, 11, 2], [2, "41", 364, 914, 12, 11, 0], [2, "41", 314, 903, 12, 11, 2], [2, "244", 644, 393, 20, 38, 0], [2, "245", 658, 407, 22, 22, 0], [2, "245", 677, 423, 22, 22, 0], [2, "244", 693, 424, 20, 38, 0], [2, "244", 600, 386, 20, 38, 0], [2, "244", 539, 400, 20, 38, 0], [2, "220_1", 218, 348, 40, 29, 0], [2, "220_1", 172, 338, 40, 29, 0], [2, "219_1", 187, 343, 36, 30, 0], [2, "219_1", 333, 433, 36, 30, 2], [2, "219_1", 355, 413, 36, 30, 2], [2, "25_1", 68, 339, 76, 45, 0], [2, "25_1", 22, 362, 76, 45, 0], [2, "25_1", -23, 384, 76, 45, 0], [2, "25_1", 96, 354, 76, 45, 0], [2, "25_1", 50, 377, 76, 45, 0], [2, "25_1", 5, 399, 76, 45, 0], [2, "25_1", 125, 369, 76, 45, 0], [2, "25_1", 79, 392, 76, 45, 0], [2, "25_1", 34, 414, 76, 45, 0], [2, "25_1", 135, 374, 76, 45, 0], [2, "25_1", 92, 395, 76, 45, 0], [2, "25_1", 44, 419, 76, 45, 0], [2, "228", 147, 439, 6, 9, 0], [2, "228", 147, 432, 6, 9, 0], [2, "227", 147, 420, 6, 16, 0], [2, "228", 172, 452, 6, 9, 0], [2, "227", 172, 442, 6, 16, 0], [2, "227", 160, 431, 6, 16, 0], [2, "257", 2, 671, 14, 66, 0], [2, "257", 9, 661, 14, 66, 0], [2, "257", 15, 644, 14, 66, 0], [2, "257", 20, 657, 14, 66, 0], [2, "257", 26, 661, 14, 66, 0], [2, "257", 33, 664, 14, 66, 0], [2, "257", 111, 686, 14, 66, 0], [2, "257", -12, 689, 14, 66, 0], [2, "257", -2, 691, 14, 66, 0], [2, "257", 5, 695, 14, 66, 0], [2, "257", 12, 698, 14, 66, 0], [2, "257", 19, 702, 14, 66, 0], [2, "257", 26, 706, 14, 66, 0], [2, "257", 33, 709, 14, 66, 0], [2, "257", 40, 713, 14, 66, 0], [2, "257", 101, 689, 14, 66, 0], [2, "257", 94, 692, 14, 66, 0], [2, "257", 87, 695, 14, 66, 0], [2, "257", 80, 699, 14, 66, 0], [2, "257", 73, 702, 14, 66, 0], [2, "257", 66, 705, 14, 66, 0], [2, "257", 59, 708, 14, 66, 0], [2, "257", 52, 712, 14, 66, 0], [2, "257", 46, 715, 14, 66, 0], [2, "257", 46, 718, 14, 66, 0], [2, "234", 4, 711, 34, 63, 0], [2, "235", 93, 707, 18, 29, 0], [2, "225", 95, 728, 16, 17, 2], [2, "241", 113, 704, 14, 11, 0], [2, "241", 46, 726, 14, 11, 0], [2, "234", 59, 718, 34, 63, 2], [2, "34", 63, 726, 30, 53, 0], [2, "229", 51, 709, 60, 42, 0], [2, "230", 75, 747, 6, 40, 0], [2, "230", 103, 737, 6, 40, 0], [2, "231", 75, 740, 6, 6, 0], [2, "231", 102, 730, 6, 6, 0], [2, "240", 6, 693, 12, 13, 0], [2, "240", 18, 699, 12, 13, 0], [2, "240", 30, 705, 12, 13, 0], [2, "240", -5, 687, 12, 13, 0], [2, "240", 17, 675, 12, 13, 0], [2, "240", 5, 669, 12, 13, 0], [2, "258", 12, 665, 16, 39, 0], [2, "260", -16, 673, 26, 34, 0], [2, "259", 2, 644, 18, 32, 0], [2, "24", 168, 712, 28, 38, 2], [2, "24", 169, 736, 28, 38, 0], [2, "24", 65, 787, 28, 38, 0], [2, "24", 32, 804, 28, 38, 0], [2, "24", -1, 799, 28, 38, 2], [2, "245", 615, 400, 22, 22, 0], [2, "263", 562, 394, 34, 34, 2], [2, "262", 83, 138, 48, 39, 0], [2, "251", 533, 58, 68, 57, 0], [2, "264", 573, 40, 66, 34, 0], [2, "265", 632, -1, 68, 77, 0], [2, "264", 645, 80, 66, 34, 0], [2, "249", 670, 30, 48, 72, 0], [2, "250", 678, -15, 32, 62, 0], [2, "249", 593, -7, 48, 72, 0], [2, "250", 601, -52, 32, 62, 0], [2, "89", 626, 354, 48, 95, 0], [2, "88", 652, 346, 88, 61, 0], [2, "87", 575, 353, 72, 57, 2], [2, "269", 340, 189, 110, 58, 0], [2, "269", 298, 210, 110, 58, 0], [2, "269", 271, 224, 110, 58, 0], [2, "272", 375, 204, 72, 54, 0], [2, "271", 323, 232, 64, 50, 0], [2, "244", 353, 253, 20, 38, 0], [2, "245", 368, 273, 22, 22, 0], [2, "249", 697, 91, 48, 72, 0], [2, "250", 705, 46, 32, 62, 0], [2, "250", 705, 1, 32, 62, 0], [2, "263", 238, 382, 34, 34, 0], [2, "43_1", 563, 84, 82, 58, 0], [2, "43_1", 572, 74, 82, 58, 0], [2, "251", 607, 97, 68, 57, 0], [2, "227", -19, 366, 6, 16, 0], [2, "227", -3, 359, 6, 16, 0], [2, "227", 129, 466, 6, 16, 0], [2, "228", 116, 465, 6, 9, 0], [2, "227", 116, 451, 6, 16, 0], [2, "228", 103, 452, 6, 9, 0], [2, "227", 103, 440, 6, 16, 0], [2, "228", 103, 459, 6, 9, 0], [2, "227", 85, 429, 6, 16, 0], [2, "227", 2, 403, 6, 16, 2], [2, "227", 16, 410, 6, 16, 2], [2, "227", 30, 417, 6, 16, 2], [2, "227", 44, 423, 6, 16, 2], [2, "227", 57, 429, 6, 16, 2], [2, "227", 69, 435, 6, 16, 2], [2, "227", 124, 338, 6, 16, 2], [2, "227", 138, 345, 6, 16, 2], [2, "227", 152, 352, 6, 16, 2], [2, "227", 166, 358, 6, 16, 2], [2, "227", 179, 364, 6, 16, 2], [2, "227", 191, 370, 6, 16, 2], [2, "225", 116, 325, 16, 17, 0], [2, "225", 132, 333, 16, 17, 0], [2, "225", 148, 341, 16, 17, 0], [2, "225", 164, 349, 16, 17, 0], [2, "225", 180, 357, 16, 17, 0], [2, "227", 195, 373, 6, 16, 0], [2, "227", 184, 380, 6, 16, 0], [2, "227", 169, 386, 6, 16, 0], [2, "227", 153, 393, 6, 16, 0], [2, "227", 134, 404, 6, 16, 0], [2, "225", 180, 368, 16, 17, 2], [2, "225", 163, 377, 16, 17, 2], [2, "225", 147, 385, 16, 17, 2], [2, "261", 193, 363, 10, 15, 0], [2, "225", 134, 391, 16, 17, 2], [2, "226", 132, 400, 16, 16, 0], [2, "226", 143, 411, 16, 16, 0], [2, "226", 154, 422, 16, 16, 0], [2, "226", 165, 433, 16, 16, 0], [2, "226", 176, 444, 16, 16, 0], [2, "225", -25, 358, 16, 17, 2], [2, "225", -9, 350, 16, 17, 2], [2, "257", 21, 304, 14, 66, 0], [2, "257", 28, 294, 14, 66, 0], [2, "257", 34, 277, 14, 66, 0], [2, "257", 39, 290, 14, 66, 0], [2, "257", 45, 294, 14, 66, 0], [2, "257", 52, 297, 14, 66, 0], [2, "257", 130, 319, 14, 66, 0], [2, "257", 7, 322, 14, 66, 0], [2, "257", 17, 324, 14, 66, 0], [2, "257", 24, 328, 14, 66, 0], [2, "257", 31, 331, 14, 66, 0], [2, "257", 38, 335, 14, 66, 0], [2, "257", 45, 339, 14, 66, 0], [2, "257", 52, 342, 14, 66, 0], [2, "257", 59, 346, 14, 66, 0], [2, "257", 120, 322, 14, 66, 0], [2, "257", 113, 325, 14, 66, 0], [2, "257", 106, 328, 14, 66, 0], [2, "257", 99, 332, 14, 66, 0], [2, "257", 92, 335, 14, 66, 0], [2, "257", 85, 338, 14, 66, 0], [2, "257", 78, 341, 14, 66, 0], [2, "257", 71, 345, 14, 66, 0], [2, "257", 65, 348, 14, 66, 0], [2, "257", 65, 351, 14, 66, 0], [2, "234", 23, 344, 34, 63, 0], [2, "235", 112, 340, 18, 29, 0], [2, "225", 114, 361, 16, 17, 2], [2, "241", 132, 337, 14, 11, 0], [2, "241", 65, 359, 14, 11, 0], [2, "234", 78, 351, 34, 63, 2], [2, "34", 82, 359, 30, 53, 0], [2, "229", 70, 342, 60, 42, 0], [2, "230", 94, 380, 6, 40, 0], [2, "230", 122, 369, 6, 40, 0], [2, "231", 94, 373, 6, 6, 0], [2, "231", 121, 363, 6, 6, 0], [2, "240", 25, 326, 12, 13, 0], [2, "240", 37, 332, 12, 13, 0], [2, "240", 49, 338, 12, 13, 0], [2, "240", 14, 320, 12, 13, 0], [2, "11", 212, 363, 32, 29, 0], [2, "279", 185, 454, 8, 15, 0], [2, "219_1", 367, 403, 36, 30, 2], [2, "85", 336, 423, 48, 53, 0], [2, "18_1", 313, 785, 88, 64, 0], [2, "22", 328, 505, 62, 38, 0], [2, "21", 310, 537, 28, 24, 0], [2, "21", 598, 700, 28, 24, 2], [2, "21", 175, 683, 28, 24, 0], [2, "253", 354, 639, 92, 53, 0], [2, "253", 500, 558, 92, 53, 2], [2, "29_1", 143, 460, 42, 27, 0], [2, "29_1", 132, 449, 42, 27, 0], [2, "29_1", 122, 437, 42, 27, 0], [2, "29_1", 111, 426, 42, 27, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, -1, -1, 100, 99, 98, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 104, 105, 106, 8, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 6, 0, 0, -1, 98, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 7, 2, 7, 6, 113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 110, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 13, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 39, -1, -1, -1, -1, -1, 16, 11, -1, -1, 52, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 39, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, -1, -1, -1, -1, -1, 0, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, -1, -1, -1, -1, -1, 0, 0, 0, 0, 0, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, -1, -1, -1, -1, -1, -1, 0, 1, 2, 102, -1, -1, -1, 0, 0, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, -1, -1, 0, 0, 1, 2, 109, 115, 85, -1, -1, -1, 0, 0, 0, 0, -1, -1, -1, -1, -1, -1, 18, 17, -1, -1, -1, 0, 0, 0, 0, 0, 1, 2, 3, -1, 107, 85, 82, 88, -1, 0, 0, 0, 0, -1, -1, -1, -1, -1, -1, -1, 8, -1, -1, 1, 1, 0, 0, 0, 0, 0, 0, 39, 93, 94, 107, 77, 38, 38, 88, 91, 109, 7, 6, 123, 122, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, 0, 0, 0, 39, 39, 99, 103, 77, 38, 38, 76, 79, 113, -1, -1, -1, -122, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 39, 0, 39, 39, 39, 39, 39, 85, 82, 68, 75, 79, 39, 110, -1, -1, -1, -1, -1, -1, -1, -1, 8, -1, -1, -1, -1, -1, 0, 0, 0, 0, 39, 39, 39, 39, 85, 86, 38, 71, 39, 39, 39, -1, -1, -1, -1, -1, -1, 92, 93, 93, 94, -1, -1, -1, -1, -1, 0, 0, 0, 0, 0, 0, -1, -1, -1, 38, 38, 88, 91, 39, 39, 39, -1, -1, -1, -1, -1, 112, 111, 110, 97, 99, 98, -1, -1, 39, 0, 0, 0, 0, 0, 39, -1, -1, -1, 74, 38, 38, 83, 39, 39, -1, -1, -1, -1, -1, -1, -1, -1, 104, 105, 115, 97, 99, 98, 39, 0, 0, 0, 0, 0, 0, 39, 39, 39, 73, 74, 68, 79, 101, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 112, 111, 110, 39, 39, 0, 0, 0, 0, 39, 39, 39, 39, 39, 85, 86, 80, 91, 97, 93, 98, -1, -1, -1, -1, -1, -1, -1, 92, 93, 94, -1, -1, -1, -1, 0, 0, 0, 0, 39, 39, 109, 111, 115, 73, 70, 38, 88, 87, 91, 97, -1, 91, -1, -1, -1, -1, -1, -1, 39, 97, 99, 98, 39, -1, 0, 39, 39, 39, -1, 109, 106, -1, 104, 39, 75, 74, 38, 38, 88, 87, 87, 91, 39, 97, 98, -1, -1, -1, -1, 112, 111, 110, -1, 39, 39, 39, 39, 11, 12, 13, -1, -1, 92, 39, 85, 86, 68, 69, 70, 38, 38, 88, 87, 87, 91, 39, 39, 99, 98, -1, -1, -1, -1, -1, 0, 39, 39, 39, 39, 11, 12, 13, 103, 84, 89, 38, 71, 72, 73, 75, 74, -1, 38, 38, 88, 87, 87, 87, 91, 97, 99, 98, -1, 0, 0, 39, 39, 0, 0, 1, 2, 92, 85, 87, 86, 68, 79, 39, 39, 39, 77, 75, 74, 38, 38, 38, 38, 38, 88, 87, 91, 84, 99, 98, 0, 0, 0, 1, 2, 3, 92, 85, 86, 38, 38, 71, 39, 39, 39, 11, 12, 78, 73, 74, 38, 38, 76, 75, 74, 38, 80, 81, -1, -1, 0, 1, 2, -1, 102, 102, 85, 82, 38, 38, 76, 79, 39, 0, 0, -1, 0, -123, -122, 73, 75, 75, 79, 39, 73, 75, 74, 38, 38, 38, 39, 39, 39, 85, 87, 87, 82, 38, 38, 68, 79, 39, 0, 0, 0, 1, 2, 3, -1, 104, 105, 105, 105, 115, 39, 39, -1, -1, -1, -1, 39, 39, 85, 82, 38, 38, 38, 38, 68, 79, 39, 39, 0, 1, 2, 3, -1, 100, 99, 99, 98, -1, -1, -1, 112, 111, 110, 39, 39, 39, 87, 87, 82, 68, 69, 70, 68, 69, 79, 0, 0, 1, 2, 3, -1, -1, 100, 103, 109, 111, 110, 18, 13, -1, -1, -1, -1, -1, 39, 39, 38, 38, 38, -1, 39, 73, 79, 0, 0, 1, 2, 3, -1, -1, -1, -1, 104, 111, 110, 18, 17, 16, 11, 12, 13, -1, -1, -1, -1, -1, 68, 69, 70, 39, -1, 0, 8, 7, 2, 3, -1, -1, 18, 17, 16, -1, 11, 12, 13, 16, 7, 0, 1, 2, 3, 13, -1, -1, -1, -1, 79, 96, 70, -1, 0, 0, 0, -1, -1, -1, -1, -1, 7, 6, 11, 12, 1, 2, 3, 8, 7, 2, 3, 8, 2, 3, -1, -1, -1, 0]}, {"type": 3, "obj": [[2, "308", 27, 768, 52, 22, 0], [2, "309", 123, 722, 46, 33, 0], [2, "253", 353, 549, 92, 53, 2], [2, "213_1", 99, 117, 64, 45, 0], [2, "214_1", 317, 80, 54, 40, 0], [2, "213_1", 313, 44, 64, 45, 0], [2, "214_1", 398, 384, 54, 40, 0], [2, "213_1", 439, 377, 64, 45, 2], [2, "213_1", 252, 323, 64, 45, 2], [2, "214_1", 275, 341, 54, 40, 0], [2, "214_1", 307, 353, 54, 40, 0], [2, "213_1", 327, 360, 64, 45, 2], [2, "214_1", 188, 320, 54, 40, 0], [2, "213_1", 136, 313, 64, 45, 0], [2, "214_1", 211, 332, 54, 40, 0], [2, "205_1", 488, 395, 54, 40, 2], [2, "208_1", 648, 407, 78, 40, 0], [2, "208_1", 531, 400, 78, 40, 1], [2, "208_1", 590, 402, 78, 40, 3], [2, "213_1", 488, 271, 64, 45, 2], [2, "214_1", 355, 226, 54, 40, 0], [2, "213_1", 615, 331, 64, 45, 0], [2, "214_1", 681, 318, 54, 40, 2], [2, "214_1", 586, 323, 54, 40, 0], [2, "214_1", 505, 322, 54, 40, 0], [2, "214_1", 452, 299, 54, 40, 0], [2, "214_1", 401, 273, 54, 40, 0], [2, "213_1", 396, 237, 64, 45, 0], [2, "213_1", 502, 290, 64, 45, 0], [2, "213_1", 552, 297, 64, 45, 2], [2, "213_1", 548, 299, 64, 45, 0], [2, "213_1", 605, 314, 64, 45, 0], [2, "214_1", 549, 322, 54, 40, 0], [2, "214_1", 670, 339, 54, 40, 2], [2, "213_1", 627, 304, 64, 45, 0], [2, "214_1", 329, 298, 54, 40, 2], [2, "214_1", 305, 322, 54, 40, 2], [2, "208_1", 379, 48, 78, 40, 3], [2, "208_1", 412, 38, 78, 40, 3], [2, "208_1", 468, 37, 78, 40, 3], [2, "208_1", 297, 120, 78, 40, 0], [2, "214_1", 246, 89, 54, 40, 0], [2, "214_1", 194, 179, 54, 40, 2], [2, "208_1", 189, 204, 78, 40, 3], [2, "208_1", -35, 93, 78, 40, 1], [2, "208_1", 32, 90, 78, 40, 1], [2, "214_1", 152, 135, 54, 40, 2], [2, "205_1", 151, 112, 54, 40, 2], [2, "208_1", 76, 93, 78, 40, 0], [2, "208_1", 108, 86, 78, 40, 0], [2, "205_1", 60, 128, 54, 40, 2], [2, "208_1", 115, 145, 78, 40, 0], [2, "152_1", 171, 165, 76, 40, 2], [2, "214_1", 264, 141, 54, 40, 0], [2, "214_1", 299, 158, 54, 40, 0], [2, "208_1", 327, 84, 78, 40, 1], [2, "208_1", 291, 76, 78, 40, 0], [2, "152_1", 260, 109, 76, 40, 2], [2, "152_1", 242, 58, 76, 40, 2], [2, "208_1", 346, 35, 78, 40, 0], [2, "205_1", 368, 63, 54, 40, 2], [2, "205_1", 462, 40, 54, 40, 0], [2, "205_1", 308, 24, 54, 40, 0], [2, "208_1", 269, 279, 78, 40, 3], [2, "208_1", 307, 269, 78, 40, 3], [2, "208_1", 126, 294, 78, 40, 2], [2, "208_1", 191, 299, 78, 40, 0], [2, "214_1", 383, 330, 54, 40, 2], [2, "208_1", 346, 345, 78, 40, 3], [2, "208_1", 308, 335, 78, 40, 0], [2, "152_1", 359, 314, 76, 40, 2], [2, "152_1", 426, 355, 76, 40, 0], [2, "205_1", 396, 360, 54, 40, 2], [2, "205_1", 383, 210, 54, 40, 0], [2, "208_1", 396, 219, 78, 40, 2], [2, "208_1", 480, 248, 78, 40, 0], [2, "208_1", 505, 266, 78, 40, 0], [2, "208_1", 611, 292, 78, 40, 2], [2, "205_1", 681, 292, 54, 40, 2], [2, "214_1", 306, 185, 54, 40, 0], [2, "152_1", 299, 141, 76, 40, 0], [2, "253", 648, 915, 92, 53, 0], [2, "253", 627, 888, 92, 53, 0], [2, "253", 278, 764, 92, 53, 0], [2, "253", 69, 730, 92, 53, 2], [2, "253", 6, 764, 92, 53, 2], [2, "253", 62, 696, 92, 53, 1], [2, "253", -57, 730, 92, 53, 2], [2, "253", 59, 758, 92, 53, 2], [2, "253", 152, 388, 92, 53, 2], [2, "152_1", 549, 280, 76, 40, 0], [2, "205_1", 321, 330, 54, 40, 2], [2, "253", 627, 518, 92, 53, 2], [2, "253", 550, 490, 92, 53, 2], [2, "253", 589, 666, 92, 53, 2], [2, "253", 514, 634, 92, 53, 2], [2, "213_1", 442, 263, 64, 45, 0], [2, "152_1", 443, 241, 76, 40, 2], [2, "214_1", 610, 347, 54, 40, 0], [2, "214_1", 365, 390, 54, 40, 2], [2, "253", 543, 533, 92, 53, 0], [2, "208_1", 253, 306, 78, 40, 0], [2, "208_1", 277, 314, 78, 40, 1], [2, "253", 225, 855, 92, 53, 0], [2, "253", 118, 456, 92, 53, 0], [2, "253", 643, 719, 92, 53, 2], [2, "286", 404, 138, 24, 24, 3], [2, "286", 381, 137, 24, 24, 1], [2, "286", 524, 100, 24, 24, 1], [2, "286", 537, 293, 24, 24, 1], [2, "287", 548, 101, 24, 24, 3], [2, "286", 550, 115, 24, 24, 1], [2, "287", 574, 116, 24, 24, 3], [2, "286", 580, 131, 24, 24, 1], [2, "287", 604, 132, 24, 24, 3], [2, "287", 269, 240, 24, 24, 3], [2, "286", 245, 240, 24, 24, 1], [2, "286", 273, 253, 24, 24, 1], [2, "287", 297, 253, 24, 24, 3], [2, "286", 399, 183, 24, 24, 2], [2, "287", 375, 183, 24, 24, 0], [2, "287", 411, 198, 24, 24, 0], [2, "286", 435, 198, 24, 24, 2], [2, "286", 555, 292, 24, 24, 3], [2, "286", 411, 238, 24, 24, 1], [2, "286", 429, 237, 24, 24, 3], [2, "305", 558, 146, 30, 24, 0], [2, "306", 363, 162, 46, 25, 0], [2, "308", 412, 156, 52, 22, 2], [2, "308", 455, 129, 52, 22, 0], [2, "309", 454, 190, 46, 33, 0], [2, "308", 526, 167, 52, 22, 0], [2, "307", 478, 190, 42, 19, 0], [2, "309", 500, 106, 46, 33, 0], [2, "306", 520, 185, 46, 25, 0], [2, "308", 489, 620, 52, 22, 0], [2, "307", 497, 164, 42, 19, 0], [2, "305", 461, 148, 30, 24, 0], [2, "307", 401, 139, 42, 19, 0], [2, "308", 264, 255, 52, 22, 0], [2, "307", 232, 279, 42, 19, 0], [2, "309", 211, 248, 46, 33, 0], [2, "306", 191, 457, 46, 25, 0], [2, "308", 456, 646, 52, 22, 0], [2, "308", 428, 589, 52, 22, 0], [2, "309", 484, 589, 46, 33, 0], [2, "306", 383, 607, 46, 25, 0], [2, "253", 286, 822, 92, 53, 0], [2, "309", 393, 811, 46, 33, 0], [2, "309", 374, 624, 46, 33, 0], [2, "308", 168, 284, 52, 22, 0], [2, "307", 430, 654, 42, 19, 0], [2, "305", 372, 823, 30, 24, 0], [2, "307", 389, 470, 42, 19, 0], [2, "309", 170, 252, 46, 33, 0], [2, "307", 436, 144, 42, 19, 0]]}, {"type": 2, "data": [27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 33, 29, 30, 31, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 27, 32, 33, 32, 33, 34, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 29, 30, 31, 29, 30, 31, 29, 30, 63, 62, 56, 31, 35, 36, 35, 36, 37, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 33, 33, 32, 33, 34, 32, 33, 34, 32, 33, 60, 59, 56, 34, 32, 33, 32, 33, 32, 29, 30, 31, 35, 36, 22, 22, 22, 22, 22, 22, 22, 29, 35, 63, 62, 62, 41, 46, 33, 34, 33, 66, 65, 64, 65, 36, 35, 36, 35, 32, 33, 34, 32, 33, 32, 35, 36, 23, 23, 23, 23, 32, 29, 60, 59, 63, 57, 58, 34, 37, 67, 64, 65, 64, 65, 30, 56, 56, 30, 35, 36, 37, 40, 36, 35, 36, 23, 23, 23, 23, 23, 35, 32, 33, 34, 32, 65, 36, 66, 65, 64, 66, 67, 64, 30, 31, 63, 56, 56, 29, 30, 31, 52, 53, 58, 35, 36, 23, 23, 23, 23, 23, 35, -1, -1, 66, 64, 65, 66, 67, 67, 30, 31, 32, 33, 34, 60, 53, 63, 32, 33, 34, 32, 33, 34, 32, 33, 36, 23, 23, 23, 23, 23, 35, 36, 64, 66, 67, 29, 30, 31, 33, 34, 35, 36, 37, 32, 33, 55, 35, 36, 37, 35, 36, 37, 35, 36, 35, 36, 23, 23, 23, 23, 23, 33, 66, 67, 66, 32, 33, 34, 36, 37, 37, 31, 29, 46, 31, 32, 29, 30, 31, 29, 30, 31, 29, 29, 30, 31, 64, 65, 65, -1, 23, 23, 33, 35, 36, 35, 36, 37, 37, 32, 52, 63, 57, 58, 34, 32, 32, 33, 34, 32, 33, 34, -1, 32, 29, 67, 66, 64, 65, -1, 23, 23, 23, 23, 23, 33, 35, 36, 29, 35, 32, 60, 54, 32, 48, 47, 35, 36, 37, 35, 36, 37, 35, 36, 29, 66, 67, 66, 67, -1, 23, 23, 23, 23, 23, 23, 23, 33, 32, 33, 34, 32, 40, 47, 51, 50, 29, 30, 31, 29, 30, 31, 29, 30, 32, 33, 34, 33, 34, -1, 31, 23, 23, 23, 23, 23, 23, 23, 35, 36, 37, 35, 36, 37, 35, 36, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 35, 36, 37, 35, 36, 37, 35, 56, 56, 56, 56, 56, 35, 36, 37, 35, 36, 35, 36, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 29, 30, 31, 29, 30, 31, 48, 48, 62, 53, 63, 56, 56, 30, 31, 29, 30, 31, 29, 30, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 32, 33, 34, 32, 33, 34, 52, 63, 62, 61, 52, 59, 32, 33, 34, 50, 50, 50, 63, 57, 63, 63, 33, 50, 57, 59, 33, 32, 25, 25, 35, 36, 37, 35, 36, 37, 35, 60, 59, 58, 36, 37, 35, 36, 50, 50, 50, 41, 47, 61, 52, 58, 52, 53, 54, 55, 56, 47, 47, 62, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 52, 59, 63, 44, 50, 49, 33, 29, 30, 48, 47, 52, 53, 62, 62, 62, 32, 33, 34, 32, 33, 34, 59, 53, 54, 32, 33, 34, 32, 33, 34, 32, 60, 53, 53, 54, 32, 32, 33, 52, 63, 62, 62, 62, 62, 62, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 47, 46, 32, 32, 32, 33, 35, 36, 37, 60, 59, 63, 62, 51, 62, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 52, 59, 58, 31, 29, 30, 31, 29, 30, 31, 29, 30, 60, 59, 62, 62, 48, 47, 46, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 52, 58, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 60, 59, 55, 56, 57, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 40, 41, 47, 46, 37, 35, 36, 37, 64, 65, 37, 35, 36, 37, 35, 36, 37, 52, 53, 54, 29, 48, 47, 46, 30, 31, 29, 30, 31, 29, 52, 59, 53, 54, 64, 66, 64, 65, 66, 29, 30, 31, 29, 30, 31, 29, 30, 46, 33, 34, 32, 55, 56, 57, 47, 46, 32, 33, 34, 32, 33, 34, 32, 66, 66, 64, 66, 67, 65, 32, 33, 34, 32, 33, 34, 32, 33, 45, 46, 37, 35, 52, 53, 54, 53, 54, 35, 36, 37, 35, 36, 37, 35, 36, 37, 66, 29, 30, 67, 35, 36, 37, 35, 36, 37, 35, 36, 29, 30, 31, 29, 29, 30, 31, 48, 32, 31, 47, 46, 30, 31, 29, 30, 31, 29, 30, 32, 33, 40, 30, 31, 29, 30, 31, 29, 30, 31, 32, 33, 34, 32, 32, 33, 34, 52, 58, 34, 53, 54, 33, 34, 32, 33, 34, 32, 33, 34, 33, 52, 53, 54, 32, 40, 42, 32, 33, 34, 35, 36, 37, 35, 35, 46, 37, 35, 36, 37, 36, 35, 36, 37, 35, 36, 37, 35, 36, 37, 36, 35, 36, 37, 35, 52, 54, 35, 40, 41, 50, 30, 31, 29, 62, 61, 33, 34, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 60, 59, 50, 50, 54, 60, 59, 58, 36, 37, 34, 32, 33, 34, 32, 33, 34, 32, 47, 46, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 60, 53, 54, 33, 34, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 62, 62, 61, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 33, 34, 36, 37, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 52, 59, 53, 54, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 36, 37, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 36, 37, 35, 36, 37, 35, 48, 47, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 32, 48, 47, 46, 37, 35, 29, 30, 31, 29, 30, 31, 52, 63, 59, 58, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 30, 31, 29, 52, 63, 62, 45, 31, 32, 33, 34, 32, 33, 34, 47, 46, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 34, 32, 33, 60, 63, 44, 34, 40, 41, 41, 35, 36, 37, 50, 49, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 35, 36, 37, 55, 44, 44]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1]}