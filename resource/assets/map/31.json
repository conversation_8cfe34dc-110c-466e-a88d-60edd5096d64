{"mW": 672, "mH": 576, "tW": 24, "tH": 24, "tiles": [["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2], ["203_2", 0, 2, 1], ["203_1", 0, 2, 1], ["203_2", 3, 2, 1]], "layers": [{"type": 2, "data": [38, 38, 38, 38, 38, 38, 38, 38, 38, 30, 31, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 31, 34, 26, 32, 40, 40, 34, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 30, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 14, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 38, 31, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 19, 18, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 23, 30, 22, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, 26, 39, 38, 38, 38, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, 32, 32, 34, 33, -1, -1, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 15, 18, 10, 18, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 22, 22, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 38, 22, 37, -1, -1, -1, -1, -1, -1, -1, 19, 18, 10, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 31, 26, 34, 33, -1, -1, -1, -1, -1, 19, 18, 23, 30, 30, 15, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 26, 27, -1, -1, -1, -1, -1, -1, -1, -1, 35, 39, 38, 38, 30, 31, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, 34, 32, 40, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 4, "obj": [[2, "1310_2", 215, 13, 18, 29, 2], [2, "1310_2", 190, 17, 18, 29, 2], [2, "1310_2", 157, 20, 18, 29, 2], [2, "1310_2", 231, 23, 18, 29, 2], [2, "1310_2", 49, 116, 18, 29, 2], [2, "1310_2", 89, 161, 18, 29, 2], [2, "1310_2", 56, 165, 18, 29, 2], [2, "1310_2", 22, 193, 18, 29, 2], [2, "1308_2", 451, 258, 22, 37, 0], [2, "1308_2", 575, 266, 22, 37, 0], [2, "1310_2", 499, 279, 18, 29, 2], [2, "1308_2", 548, 271, 22, 37, 0], [2, "1310_2", 489, 287, 18, 29, 0], [2, "1308_2", 520, 280, 22, 37, 0], [2, "1308_2", 580, 288, 22, 37, 0], [2, "1308_2", 85, 396, 22, 37, 0], [2, "1308_2", 56, 400, 22, 37, 0], [2, "1308_2", 254, 419, 22, 37, 0], [2, "1308_2", 367, 428, 22, 37, 0], [2, "1310_2", 291, 441, 18, 29, 2], [2, "1308_2", 307, 437, 22, 37, 0], [2, "1310_2", 274, 457, 18, 29, 2], [2, "1308_2", 327, 449, 22, 37, 2]]}, {"type": 3, "obj": [[2, "213_3", 121, 541, 64, 45, 2], [2, "208_3", 205, 437, 78, 40, 2], [2, "214_3", 625, 156, 54, 40, 0], [2, "208_3", 46, 92, 78, 40, 1], [2, "214_3", 240, 483, 54, 40, 2], [2, "214_3", 216, 529, 54, 40, 2], [2, "213_3", 157, 540, 64, 45, 2], [2, "214_3", 232, 104, 54, 40, 2], [2, "214_3", 264, 53, 54, 40, 0], [2, "213_3", 564, 149, 64, 45, 2], [2, "213_3", 314, 59, 64, 45, 0], [2, "214_3", 541, 518, 54, 40, 0], [2, "214_3", 315, 91, 54, 40, 0], [2, "1309_2", 359, 81, 20, 32, 0], [2, "313_2", 118, 103, 70, 44, 2], [2, "313_2", 481, 870, 70, 44, 2], [2, "207_2", 360, 93, 38, 27, 2], [2, "208_3", 305, 57, 78, 40, 3], [2, "208_3", 269, -8, 78, 40, 1], [2, "208_3", 168, 70, 78, 40, 3], [2, "208_3", 535, 242, 78, 40, 2], [2, "208_3", 523, 537, 78, 40, 2], [2, "207_2", 362, 51, 38, 27, 2], [2, "208_3", 599, 505, 78, 40, 0], [2, "152_3", 537, 498, 76, 40, 0], [2, "205_3", 111, 63, 54, 40, 0], [2, "208_3", 4, 194, 78, 40, 1], [2, "207_2", 566, 195, 38, 27, 2], [2, "205_3", 572, 211, 54, 40, 2], [2, "207_2", 558, 172, 38, 27, 2], [2, "208_3", 315, 117, 78, 40, 2], [2, "208_3", 283, 132, 78, 40, 0], [2, "207_2", 256, 133, 38, 27, 0], [2, "313_2", 443, 881, 70, 44, 2], [2, "955_4", 51, 473, 20, 18, 0], [2, "955_4", 482, 494, 20, 18, 0], [2, "1303_2", 345, 27, 34, 20, 2], [2, "1151", 566, 194, 38, 33, 2], [2, "152_3", 254, 24, 76, 40, 2], [2, "208_3", 22, 161, 78, 40, 1], [2, "214_3", 42, 537, 54, 40, 0], [2, "152_3", 210, 92, 76, 40, 2], [2, "1151", 226, 128, 38, 33, 0], [2, "166_2", 271, 19, 30, 35, 0], [2, "208_3", 196, 500, 78, 40, 2], [2, "208_3", 144, 518, 78, 40, 2], [2, "205_3", 238, 464, 54, 40, 0], [2, "208_3", -4, 261, 78, 40, 1], [2, "214_3", 75, 391, 54, 40, 0], [2, "208_3", 71, 364, 78, 40, 0], [2, "208_3", 73, 414, 78, 40, 1], [2, "208_3", -5, 423, 78, 40, 1], [2, "166_2", 249, 493, 30, 35, 2], [2, "214_3", -6, 545, 54, 40, 0], [2, "207_2", -7, 527, 38, 27, 0], [2, "208_3", 15, 509, 78, 40, 2], [2, "1302_3", -6, 454, 40, 29, 2], [2, "1303_2", -7, 479, 34, 20, 0], [2, "1151", -7, 501, 38, 33, 2], [2, "1303_2", 279, 170, 34, 20, 0], [2, "955_4", 653, 538, 20, 18, 0], [2, "1302_3", 156, 57, 40, 29, 2], [2, "1305_2", 185, 122, 20, 14, 0], [2, "1151", 108, 213, 38, 33, 0], [2, "1303_2", 364, 270, 34, 20, 0], [2, "955_4", 231, 315, 20, 18, 0], [2, "208_3", 622, 130, 78, 40, 0], [2, "1151", 395, 442, 38, 33, 0], [2, "1301_2", 76, 380, 24, 49, 2], [2, "181_3", 50, 19, 104, 100, 2], [2, "955_4", 110, 427, 20, 18, 0], [2, "214_3", 85, 543, 54, 40, 0], [2, "208_3", 81, 517, 78, 40, 0], [2, "208_3", 91, 374, 78, 40, 2], [2, "1303_2", 118, 355, 34, 20, 0], [2, "714", 248, 38, 54, 132, 0], [2, "1366", 504, 332, 52, 39, 0], [2, "710", 512, 293, 38, 62, 0], [2, "713", 272, 79, 18, 27, 2], [2, "1302_3", 295, 144, 40, 29, 0], [2, "1366", 52, 209, 52, 39, 0], [2, "710", 59, 169, 38, 62, 2], [2, "1303_2", 639, 52, 34, 20, 2], [2, "1151", 631, 15, 38, 33, 2], [2, "313_2", 114, 140, 70, 44, 2], [2, "313_2", 177, 146, 70, 44, 2], [2, "313_2", 160, 181, 70, 44, 2], [2, "313_2", 167, 218, 70, 44, 2], [2, "313_2", 267, 219, 70, 44, 2], [2, "313_2", 317, 233, 70, 44, 2], [2, "313_2", 275, 256, 70, 44, 2], [2, "166_2", 193, 512, 30, 35, 2], [2, "152_3", 553, 135, 76, 40, 2], [2, "166_2", 575, 125, 30, 35, 0], [2, "207_2", 49, 250, 38, 27, 0], [2, "1302_3", 39, 266, 40, 29, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, 61, 60, 64, -1, -1, -1, -1, -1, -1, 41, 51, 51, 47, -1, -1, 54, 55, -1, -1, -1, -1, 65, 66, 66, 72, 66, 67, -1, -1, -1, 61, -1, -1, -1, -1, -1, -1, 56, 51, 51, 46, 47, -1, 45, 46, 42, 43, -1, -1, 68, 69, 75, 75, 69, 70, 71, -1, -1, -1, -1, -1, -1, 63, 62, -1, 53, -1, -1, 60, 59, -1, -1, -1, 45, 46, 47, -1, 77, 88, 69, 69, 82, 84, 83, -1, -1, -1, -1, -1, 57, 58, 59, -1, -1, -1, -1, -1, 72, 41, 42, 43, -1, -1, 50, -1, -1, 80, 81, 82, 83, -1, -1, -1, -1, 41, -1, -1, 54, 42, 43, -1, -1, -1, -1, -1, 78, 53, 64, 46, 47, -1, 59, -1, -1, 77, 78, 79, -1, -1, -1, 41, 42, 52, -1, -1, 42, -1, -1, -1, -1, -1, -1, -1, 53, 54, 58, -1, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, 57, 58, -1, 52, -1, 57, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 54, 55, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 45, 45, 45, -1, 57, 49, 48, 48, 47, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 45, 45, 45, -1, 54, 61, 51, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 73, 72, 71, -1, -1, 61, 60, 64, 63, -1, -1, -1, -1, -1, -1, -1, 73, 75, 72, 71, -1, -1, 71, -1, -1, 73, 72, 72, 76, 84, 83, 62, 55, -1, -1, 61, 60, -1, -1, -1, -1, -1, 65, 72, 76, 75, 75, 70, 71, -1, -1, -1, -1, 80, 81, 82, 84, 84, 83, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 68, 75, 75, 75, 75, 75, 74, -1, -1, -1, -1, 77, 78, 79, -1, -1, -1, -1, 73, 72, 72, 71, -1, -1, -1, -1, -1, 65, 76, 75, 75, 75, 75, 81, 70, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 85, 84, 84, 83, -1, 51, 46, 47, -1, 77, 78, 88, 75, 75, 82, 78, 78, 79, -1, -1, -1, 65, 66, 67, -1, -1, 41, 42, -1, -1, -1, -1, -1, -1, 51, 50, -1, -1, -1, 85, 88, 75, 74, -1, -1, -1, -1, -1, 65, 76, 69, 70, 67, -1, 61, 60, 59, -1, -1, 73, 72, 45, 57, 57, 48, 48, 47, -1, 80, 75, 70, 71, -1, -1, -1, -1, 85, 88, 81, 75, 74, -1, -1, -1, -1, 65, 66, 76, 75, 45, 57, 57, 51, 51, -1, -1, 77, 84, 82, 83, 43, -1, -1, -1, -1, 77, 78, 84, 83, -1, -1, -1, -1, 68, 69, 75, 75, 57, 58, 55, -1, -1, -1, -1, -1, -1, -1, -1, 45, -1, -1, 45, 45, -1, -1, -1, 0, -1, -1, -1, -1, 77, 78, 84, 84, 54, 55, -1, -1, -1, -1, -1, -1, 44, 41, 42, 45, 45, 45, 45, 48, 47, -1, -1, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 45, -1, 57, 57, 57, 51, 50, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 57, 58, 60, 60, 59, -1, -1, -1, -1, -1, 80, -1, -1, -1, -1, -1, 42, 55, -1, 80, -1, -1, -1, -1, -1, 41, 42, 57, 57, 62, -1, -1, -1, -1, -1, -1, -1, 49, 48, 48, -1, -1, -1, -1, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 57, 57, 62, -1, -1, -1, -1, -1, -1, 41, 52, 51, 51, -1, -1, -1, -1]}, {"type": 3, "obj": [[2, "313_2", 333, 497, 70, 44, 2], [2, "313_2", 292, 492, 70, 44, 0], [2, "313_2", 329, 469, 70, 44, 2], [2, "313_2", 483, 310, 70, 44, 0], [2, "313_2", 523, 321, 70, 44, 2], [2, "955_4", 457, 352, 20, 18, 0], [2, "313_2", 38, 209, 70, 44, 2], [2, "594", 307, 458, 52, 46, 2], [2, "597", 355, 477, 34, 26, 0], [2, "597", 340, 501, 34, 26, 2], [2, "313_2", 470, 344, 70, 44, 0], [2, "313_2", 523, 356, 70, 44, 2], [2, "1303_2", 546, 366, 34, 20, 0], [2, "313_2", 388, 102, 70, 44, 2], [2, "1303_2", 414, 118, 34, 20, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, 91, 91, 91, 91, 91, 91, 91, 0, 0, 0, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, -1, -1, -1, -1, -1, 91, 91, 91, 91, 91, 91, 91, 3, 3, 3, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, -1, -1, -1, -1, -1, 92, 92, 92, 92, 92, 92, 92, 92, 6, 6, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, 7, 8, 6, -1, -1, -1, -1, -1, -1, 0, 1, 2, 89, 89, 89, 89, 89, 2, 2, 0, 1, 0, 0, 3, 4, 3, 4, 5, 3, 4, 5, -1, -1, -1, 0, 1, 2, 0, 1, 0, 0, 90, 90, 90, 90, 5, 5, 3, 4, 0, 1, 2, 7, 6, 0, 1, 2, 0, 1, -1, -1, 0, 3, 4, 5, 3, 0, 1, 2, -1, 94, 94, 94, 94, 8, 6, 7, 0, 1, 0, 1, 2, 0, 1, 2, 3, 4, -1, -1, 1, 2, 7, 8, 6, 3, 4, 5, 0, 1, 2, -1, 0, 0, 1, 2, 3, 4, 3, 4, 5, 3, 4, 5, 6, 7, 0, 1, 2, 5, 8, 5, 1, 6, 7, 8, 3, 4, 0, 1, 3, 3, 4, 5, 6, 7, 6, 7, 8, 6, 7, 0, 1, 2, 3, 4, 5, 8, 7, 8, 4, 5, 7, 0, 6, 0, 3, 4, 0, 6, 7, 8, 2, 8, 0, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1, 1, 2, 7, 8, 6, 3, 4, 5, 6, 7, 8, 0, 7, 8, 5, 1, 3, 4, 3, 4, 5, 6, 7, 8, 3, 4, 3, 4, 4, 5, 5, 5, 1, 6, 7, 8, 3, 4, 5, 2, 6, 7, 8, 4, 6, 7, 6, 7, 8, 6, 7, 8, 3, 4, 2, 6, 7, 8, 8, 8, 0, 0, 1, 2, 6, 7, 8, 5, 3, 4, 5, 7, 6, 7, 8, 6, 7, 8, 5, 5, 5, 1, 0, 1, 0, 0, 1, 2, 3, 3, 4, 5, 1, 2, 1, 2, 1, 0, 1, 2, 0, 6, 7, 8, 6, 7, 8, 1, 1, 2, 3, 4, 3, 3, 4, 5, 8, 6, 7, 8, 4, 5, 4, 5, 0, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 2, 5, 0, 1, 2, 6, 7, 8, 1, 6, 7, 8, 7, 8, 7, 8, 3, 6, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 1, 3, 4, 5, 5, 2, 7, 8, 3, 6, 0, 0, 1, 2, 5, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, 1, 2, 0, 1, 0, 1, 2, 8, 5, 7, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 3, 4, 5, 1, 2, 3, 4, 3, 4, 5, 7, 8, 1, 3, 3, 4, 5, 6, 7, 8, 3, 4, 5, 3, 4, 5, 3, 4, 6, 7, 8, 4, 5, 6, 7, 6, 7, 8, 7, 8, 4, 0, 1, 2, 0, 1, 2, 8, 6, 7, 8, 6, 7, 8, 6, 7, 5, 5, 6, 7, 8, 8, 8, 5, 3, 4, 0, 1, 2, 3, 4, 5, 3, 4, 5, 3, 0, 1, 2, 1, 2, 7, 0, 3, 8, 8, 0, 1, 2, 1, 2, 1, 6, 7, 3, 4, 5, 6, 7, 8, 6, 7, 8, 6, 3, 4, 5, 4, 5, 1, 3, 6, 2, 4, 3, 4, 0, 1, 2, 0, 0, 0, 1, 2, 8, 6, 7, 8, 3, 4, 5, 6, 6, 7, 8, 7, 8, 4, 6, 0, 5, 7, 6, 7, 3, 4, 5, 3, 3, 3, 4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 1, 2, 8, 3, 8, 7, 8, 4, 6, 7, 8, 6, 6, 6, 7, 8, 0, 1, 2, 3, 4, 5, 3, 4, 5, 3, 4, 5, 4, 0, 1, 6]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}