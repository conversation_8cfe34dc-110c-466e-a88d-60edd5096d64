{"mW": 840, "mH": 672, "tW": 24, "tH": 24, "tiles": [["106_4", 0, 3, 3], ["139", 0, 1, 1], ["141", 0, 1, 1], ["142", 0, 3, 4], ["140", 2, 3, 4], ["142", 2, 3, 4], ["140", 0, 3, 4], ["140", 1, 3, 4], ["142", 1, 3, 4], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["311_1", 0, 3, 2], ["311_1", 2, 3, 2], ["311_1", 1, 3, 2], ["311_1", 3, 3, 2], ["142", 3, 3, 4], ["140", 3, 3, 4]], "layers": [{"type": 3, "obj": [[2, "1114", 87, 189, 46, 45, 2], [2, "1114", 68, 199, 46, 45, 2], [2, "1114", 49, 206, 46, 45, 2], [2, "1101", 82, 155, 64, 55, 0], [2, "1101", 71, 160, 64, 55, 0], [2, "1101", 60, 164, 64, 55, 0], [2, "1101", 49, 168, 64, 55, 0], [2, "1101", 40, 174, 64, 55, 0], [2, "1101", 29, 179, 64, 55, 0], [2, "1101", 18, 183, 64, 55, 0], [2, "1101", 7, 187, 64, 55, 0], [2, "1114", -4, 192, 46, 45, 0], [2, "1124", -22, 193, 26, 26, 0], [2, "1125", -14, 158, 62, 46, 0], [2, "224", 646, 569, 124, 194, 0], [2, "2", 560, 410, 90, 66, 2], [2, "2", 609, 460, 90, 66, 2], [2, "2", 523, 462, 90, 66, 0], [2, "1125", 55, 126, 62, 46, 0], [2, "1126", 33, 142, 44, 41, 0], [2, "1113", 97, 136, 58, 76, 0], [2, "1113", 12, 174, 58, 76, 0]]}, {"type": 4, "obj": [[4, 0, 680, 68, 1, 4024], [2, "263_1", 538, 52, 34, 34, 0], [2, "224", -35, -54, 124, 194, 2], [2, "219_2", -1, 116, 36, 30, 0], [2, "224", 773, 39, 124, 194, 2], [2, "115", 68, 261, 16, 37, 2], [2, "47", 289, 251, 54, 63, 2], [2, "117", 55, 293, 22, 27, 0], [2, "4_2", 307, 202, 122, 119, 0], [2, "219_2", 366, 297, 36, 30, 0], [4, 3, 83, 335, 0, 4019], [2, "224", 760, 223, 124, 194, 0], [2, "219_2", 821, 393, 36, 30, 2], [2, "224", 712, 245, 124, 194, 0], [4, 2, 428, 447, 1, 4024], [2, "263_1", 737, 414, 34, 34, 0], [2, "829", 743, 403, 42, 54, 0], [2, "829", 721, 417, 42, 54, 0], [2, "219_2", 720, 444, 36, 30, 0], [2, "224", -37, 320, 124, 194, 0], [2, "4_2", 556, 453, 122, 119, 2], [2, "224", -25, 387, 124, 194, 0], [2, "219_2", 567, 551, 36, 30, 0], [2, "114", 597, 553, 18, 32, 0], [2, "224", -66, 393, 124, 194, 0], [2, "263_1", -4, 554, 34, 34, 0], [4, 1, 31, 613, 0, 4024], [2, "263_1", 383, 630, 34, 34, 0], [2, "224", 321, 471, 124, 194, 2], [2, "829", 662, 614, 42, 54, 0], [2, "219_2", 347, 640, 36, 30, 0], [2, "829", 556, 622, 42, 54, 2], [2, "829", 640, 625, 42, 54, 0], [2, "829", 578, 635, 42, 54, 2], [2, "829", 608, 638, 42, 54, 0]]}, {"type": 3, "obj": [[2, "1105_1", 88, 185, 104, 75, 0], [2, "1115", 128, 199, 14, 70, 0], [2, "213_2", 57, 442, 64, 45, 0], [2, "213_2", 505, 11, 64, 45, 2], [2, "214_2", 429, 59, 54, 40, 2], [2, "214_2", 28, 53, 54, 40, 0], [2, "213_2", 91, 12, 64, 45, 0], [2, "214_2", 52, 107, 54, 40, 0], [2, "214_2", 76, 121, 54, 40, 0], [2, "205_2", 150, 26, 54, 40, 2], [2, "208_2", 78, 86, 78, 40, 0], [2, "152_2", 76, 106, 76, 40, 0], [2, "208_2", 103, 46, 78, 40, 1], [2, "208_2", 158, 9, 78, 40, 3], [2, "205_2", 89, -8, 54, 40, 0], [2, "208_2", 66, 38, 78, 40, 0], [2, "152_2", 40, 75, 76, 40, 2], [2, "152_2", 22, 19, 76, 40, 2], [2, "208_2", 123, -1, 78, 40, 0], [2, "213_2", 102, 454, 64, 45, 2], [2, "213_2", 287, 354, 64, 45, 2], [2, "214_2", 461, 21, 54, 40, 0], [2, "213_2", 206, 406, 64, 45, 0], [2, "213_2", 254, 404, 64, 45, 2], [2, "208_2", 206, 373, 78, 40, 1], [2, "208_2", 204, 381, 78, 40, 0], [2, "208_2", 200, 362, 78, 40, 1], [2, "152_2", 274, 338, 76, 40, 2], [2, "208_2", 17, 155, 78, 40, 3], [2, "174", 74, 493, 68, 33, 0], [2, "205_2", 74, 140, 54, 40, 0], [2, "36", 114, 392, 140, 103, 2], [2, "213_2", 77, 429, 64, 45, 2], [2, "152_2", 66, 408, 76, 40, 0], [2, "213_2", 447, 30, 64, 45, 2], [2, "213_2", 469, 0, 64, 45, 0], [2, "213_2", 473, 48, 64, 45, 2], [2, "208_2", 500, -11, 78, 40, 2], [2, "205_2", 460, -10, 54, 40, 0], [2, "152_2", 443, 7, 76, 40, 2], [2, "205_2", 428, 42, 54, 40, 2], [2, "208_2", 80, 440, 78, 40, 3], [2, "213_2", 486, 380, 64, 45, 0], [2, "213_2", 528, 370, 64, 45, 2], [2, "208_2", 503, 356, 78, 40, 2], [2, "205_2", 542, 346, 54, 40, 2], [2, "213_2", 446, 376, 64, 45, 2], [2, "213_2", 335, 371, 64, 45, 0], [2, "213_2", 380, 370, 64, 45, 2], [2, "152_2", 443, 356, 76, 40, 0], [2, "208_2", 372, 350, 78, 40, 3], [2, "208_2", 332, 345, 78, 40, 0], [2, "213_2", 405, 379, 64, 45, 2], [2, "205_2", 412, 360, 54, 40, 2], [2, "208_2", 2, 410, 78, 40, 2], [2, "213_2", 14, 430, 64, 45, 2], [2, "152_2", 11, 410, 76, 40, 0], [2, "213_2", -29, 434, 64, 45, 2], [2, "205_2", -20, 418, 54, 40, 2], [2, "166", 2, 408, 30, 35, 2], [2, "174", 141, 345, 68, 33, 0], [2, "174", 744, 84, 68, 33, 0], [2, "208_2", -23, 159, 78, 40, 1], [2, "219_2", 597, 530, 36, 30, 2], [2, "208_2", 408, 70, 78, 40, 0], [2, "213_2", 506, 34, 64, 45, 2], [2, "213_2", 278, 394, 64, 45, 2], [2, "208_2", 244, 386, 78, 40, 3], [2, "205_2", 285, 364, 54, 40, 2], [2, "174", 161, 433, 68, 33, 1], [2, "174", 239, 448, 68, 33, 2], [2, "174", 217, 481, 68, 33, 0], [2, "219_2", 307, 417, 36, 30, 2], [2, "174", 446, 440, 68, 33, 0], [2, "174", 457, 321, 68, 33, 0], [2, "174", 329, 310, 68, 33, 0], [2, "174", 479, 125, 68, 33, 0], [2, "220_2", 96, 477, 40, 29, 0], [2, "62", 154, 470, 16, 27, 0], [2, "62", 698, 652, 16, 27, 0], [2, "213_2", 497, 113, 64, 45, 2], [2, "214_2", 592, 177, 54, 40, 2], [2, "214_2", 546, 124, 54, 40, 2], [2, "208_2", 430, 83, 78, 40, 1], [2, "208_2", 588, 197, 78, 40, 3], [2, "205_2", 462, 121, 54, 40, 2], [2, "208_2", 514, 137, 78, 40, 0], [2, "208_2", 476, 88, 78, 40, 0], [2, "208_2", 507, 84, 78, 40, 0], [2, "152_2", 570, 160, 76, 40, 2], [2, "205_2", 547, 103, 54, 40, 2], [2, "1112", 0, 215, 46, 83, 0], [2, "1112", 53, 219, 46, 83, 2], [2, "1112", 83, 207, 46, 83, 2], [2, "1116", 80, 228, 34, 60, 0], [2, "1111", 108, 222, 30, 40, 0], [2, "1115", 46, 235, 14, 70, 0], [2, "1105_1", 583, -23, 104, 75, 2], [2, "219_2", -15, 266, 36, 30, 0], [2, "48", 2, 284, 52, 38, 2], [2, "1109", 30, 286, 18, 21, 0], [2, "11", 133, 266, 32, 29, 0], [2, "219_2", 566, 398, 36, 30, 2], [2, "21_1", 720, 426, 28, 24, 0], [2, "8", -2, 307, 38, 29, 0], [2, "11", 673, 27, 32, 29, 0], [2, "763", 714, 631, 32, 31, 2], [2, "763", 774, 575, 32, 31, 2], [2, "763", 804, 573, 32, 31, 2], [2, "763", 798, 592, 32, 31, 2], [2, "219_2", 813, 597, 36, 30, 2], [2, "219_2", 724, 436, 36, 30, 2], [2, "116", 114, 125, 46, 39, 0], [2, "116", 160, 39, 46, 39, 0], [2, "22_2", 122, 65, 62, 38, 2], [2, "113", 145, 47, 26, 33, 0], [2, "219_2", 175, 55, 36, 30, 2], [2, "219_2", 537, 646, 36, 30, 2], [2, "174", 434, 640, 68, 33, 0], [2, "174", -6, 592, 68, 33, 1], [2, "174", 72, 607, 68, 33, 2], [2, "174", 50, 640, 68, 33, 0], [2, "174", -14, 141, 68, 33, 2], [2, "219_2", 408, 47, 36, 30, 2], [2, "263_1", 94, 388, 34, 34, 0], [2, "263_1", 238, 424, 34, 34, 0], [2, "219_2", 149, 640, 36, 30, 0], [2, "910", 785, 456, 50, 68, 0], [2, "880", 809, 436, 66, 94, 2], [2, "890", 767, 441, 94, 105, 2], [2, "812", 800, 507, 64, 64, 2], [2, "263_1", 99, 82, 34, 34, 2], [2, "219_2", 717, -19, 36, 30, 0], [2, "224", 655, -171, 124, 194, 0], [2, "224", 761, -166, 124, 194, 0], [2, "219_2", 686, 0, 36, 30, 0], [2, "219_2", 822, 0, 36, 30, 2], [2, "14", 752, 16, 32, 30, 2], [2, "263_1", 704, 14, 34, 34, 0], [2, "14", 724, 23, 32, 30, 2], [2, "219_2", 749, 27, 36, 30, 0], [2, "224", 725, -133, 124, 194, 0], [2, "219_2", 781, 34, 36, 30, 2], [2, "14", 728, 38, 32, 30, 2], [2, "263_1", 521, 338, 34, 34, 0], [2, "7", 754, 52, 28, 27, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 10, 47, 24, 23, 10, -122, -1, -1, -1, -1, -1, 99, 99, 99, 99, 90, 100, 102, 106, 84, 85, 83, 84, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 77, 76, 53, 9, 26, 45, -125, -1, -1, -1, -1, -1, 99, 100, 106, 99, 93, 92, 83, 94, 102, 101, 95, 96, -1, -1, -1, -1, -1, -1, -1, -1, 89, -1, 11, 13, 70, 69, 33, -113, 41, -1, -1, -1, -1, -1, -1, 96, 97, 95, 96, 102, 97, 95, 96, 97, -1, -1, -1, -1, 83, 84, -1, -1, 91, 90, 89, 101, 11, 81, 10, 58, 60, -113, 10, 42, -1, -1, 85, -1, -1, 84, 85, -1, -1, 11, 12, 36, 36, 35, -1, -1, -1, -1, 89, 94, 84, -1, 103, 102, 101, -1, 11, 81, 10, 10, 10, 10, 10, 10, 38, -1, -1, -1, -1, -1, 87, 88, -1, -1, 14, 47, 48, 23, -115, 36, 36, 35, -1, 88, 105, 105, 84, -1, -1, -1, -1, 77, 10, 47, 23, 10, 45, -123, -124, -125, -1, -1, -1, -1, -1, 11, 36, 84, 85, 74, 59, 57, -103, 25, 49, 23, 41, -1, 102, 102, 101, -1, 103, 100, 101, 11, 81, 47, 69, 30, 10, -115, 35, -1, -1, -1, -1, 103, -1, -1, 14, 10, 87, 92, 71, 21, 58, 57, 9, 9, -107, -115, 35, 11, 80, 12, 103, 102, 101, -1, 14, 10, 65, 9, -103, 23, 10, -122, 11, 12, 36, 35, 11, 36, 35, 71, -124, -124, -124, 92, 71, 21, 59, 57, 33, 34, 10, -115, 74, 10, 47, 24, 24, -1, -1, 74, 10, 59, 60, 60, -113, 45, -125, 77, 10, 10, -115, 81, 10, 41, -1, -1, -1, -1, 88, 89, 71, 21, 66, -103, 23, 10, 10, 74, 10, 50, 33, 60, 84, 89, 71, 72, 21, 10, 10, 45, -125, -1, 71, 72, 72, 21, 10, 45, -125, -1, -1, -1, -1, -1, -1, -1, 17, 59, 60, 34, 10, -124, 71, 21, 58, 29, 10, 90, 88, 89, -1, 71, 72, -124, -125, -1, -1, -1, -1, -1, 71, -124, -125, -1, -1, -1, -1, -1, -1, -1, 11, 81, 10, 10, 10, 10, -125, 93, 74, 10, -1, 102, 106, 102, 101, -1, -1, -1, -1, -1, -1, 85, -1, -1, -1, 11, 12, 36, 35, -1, -1, -1, -1, -1, -1, 18, 10, 10, 10, -124, 45, 44, 87, 105, 93, 92, 83, 101, 11, 12, 12, 36, 35, 83, 84, -1, 88, 85, -1, 11, 81, 10, 10, -115, 35, -1, -1, -1, -1, -1, 71, 21, 10, 45, -124, -125, -1, 99, 100, 96, 97, -1, -1, 14, 47, 23, 10, -119, 95, 96, 96, 96, 97, -1, 71, 72, 72, -124, -124, -125, -1, -1, -1, -1, -1, -1, 71, -124, -125, -1, -1, 89, 96, 97, -1, -1, 11, 12, 70, 69, -113, 45, -125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 90, 89, -1, 11, 12, -116, -117, -1, -1, -1, 83, 84, -1, -1, -1, -1, 74, 10, 59, -113, 45, -125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 95, 84, 85, 74, 10, 10, 38, -1, -1, -1, 86, 87, -1, -1, -1, -1, 71, 72, 72, -124, -125, -1, -1, 87, 87, 87, 87, -1, -1, -1, -1, -1, -1, -1, -1, -1, 87, 92, 71, 21, 10, -122, -1, -1, 91, 94, 89, 90, 89, 11, 12, 36, -1, -1, 95, 96, 97, -1, 87, 87, 87, 100, 102, 102, 106, 87, 87, 87, 87, 105, 100, 102, 101, -1, 77, 10, -118, 83, 84, 84, 105, 105, -1, -1, 71, 76, 10, -115, 35, -1, -1, 11, 12, 13, 36, 36, -114, -1, -1, 103, 102, 102, 102, 102, 102, 101, -1, -1, -1, 71, 72, 46, -1, 98, 105, 105, 87, 99, 90, 89, 77, 10, 10, -115, -114, 11, 47, 23, 10, 10, 10, 38, -1, -1, 11, 12, 12, 12, 36, 35, -1, -1, -1, -1, -1, -1, -1, -1, 95, 106, 90, 87, 106, 105, 106, 100, 102, 106, 105, 100, 14, 53, -103, 24, 23, 10, 38, -1, -1, 14, 47, 48, 24, 23, -115, -116, -114, -1, -1, -1, 11, 12, 36, 35, 98, 93, 106, 87, 87, 102, 101, -1, 103, 102, 101, 14, 53, 9, 33, 34, 10, -122, -1, -1, 74, 59, 60, 60, 34, 45, -124, -124, -125, -1, -1, 71, 21, 10, 38, 103, 106, 105, 87, 88, 89, -1, -1, -1, 11, 12, 16, 66, 9, 29, 45, 44, -125, -1, -1, 71, 72, 73, -124, -124, 46, 91, 90, 90, 84, 85, 11, 81, 10, -122, -1, -1, 105, 87, 87, 92, -1, -1, -1, 74, 10, 47, 69, 9, 29, -115, 36, 35, -1, -1, -1, 11, 12, 35, -1, -1, 103, 102, 96, 102, 101, 14, 10, 45, -125, -1, 103, 102, 87, 87, 104, -1, -1, -1, 74, 10, 59, 60, 61, 34, 10, 10, -118, -1, -1, 11, 81, 10, -115, 12, 37, 36, 35, -1, -1, -1, 71, -124, -125, -1, 98, -1, 93, 100, 96, 97, -1, -1, -1, 71, 73, 21, 10, 10, 10, 45, -124, -125, -1, -1, 14, 10, 10, 10, 10, 45, -124, -125, -1, -1, -1, 83, 84, 85, -1, 95, 96, 106, 104, 91, 90, 90, 89, -1, -1, -1, 71, 72, 72, -124, -125, -1, -1, -1, -1, 71, 72, 72, 72, 72, -125, -1, -1, -1, -1, -1, 83, 84, 90, 89, -1, -1, 98, 88, 94, 99, 99, 88, 84, 90, 89, -1, -1, -1, -1, -1, 83, 84, 84, 90, 89, -1, -1, -1, -1, -1, 83, 84, 89, 91, 90, -1, 94, 105, 88, 90, 90, 94]}, {"type": 3, "obj": [[2, "313", 798, 406, 70, 44, 0], [2, "313", 576, 543, 70, 44, 2], [2, "313", 202, 578, 70, 44, 2], [2, "313", 548, 364, 70, 44, 2], [2, "313", 480, 331, 70, 44, 2], [2, "313", 531, 334, 70, 44, 0], [2, "313", 745, 405, 70, 44, 0], [2, "313", 90, 461, 70, 44, 2], [2, "313", 351, 641, 70, 44, 2], [2, "313", 649, 636, 70, 44, 0], [2, "313", 534, 544, 70, 44, 2], [2, "313", 788, 50, 70, 44, 0], [2, "313", 546, 54, 70, 44, 2], [2, "1102_1", 40, 257, 114, 63, 2], [2, "313", 84, 290, 70, 44, 2], [2, "313", 364, 281, 70, 44, 2], [2, "313", 793, 510, 70, 44, 2], [2, "313", 760, 491, 70, 44, 0], [2, "313", 781, 552, 70, 44, 0], [2, "208_2", 771, 632, 78, 40, 3], [2, "208_2", 739, 633, 78, 40, 0], [2, "313", 723, 441, 70, 44, 2], [2, "313", 232, 435, 70, 44, 2], [2, "313", 422, 325, 70, 44, 2], [2, "313", 422, 291, 70, 44, 2], [2, "313", 377, 316, 70, 44, 2], [2, "313", 322, 307, 70, 44, 2], [2, "313", 272, 318, 70, 44, 2], [2, "313", 222, 342, 70, 44, 2], [2, "313", 348, 278, 70, 44, 0], [2, "313", 142, 61, 70, 44, 2]]}, {"type": 2, "data": [124, 120, 121, 0, 1, 2, 6, 7, 107, 108, 109, 2, 0, 1, 2, 0, 1, 3, 4, 0, 1, 2, 4, 111, 111, 111, 124, -126, 111, 111, 111, 111, 111, 111, 111, -128, 4, 5, 3, 0, 1, 2, 4, 127, 126, 125, 5, 3, 4, 5, 3, 4, 6, 7, 3, 0, 1, 2, 111, 111, -127, -128, 127, 126, -126, 111, 111, 111, 111, 111, 125, 7, 8, 6, 3, 4, 5, 7, 8, 0, 1, 2, 6, 7, 8, 6, 7, 5, 5, 6, 3, 4, 5, 111, 111, 126, 125, 6, 7, 119, 126, -126, 111, 111, 111, 0, 1, 2, 3, 107, 108, 114, 113, 8, 3, 4, 8, 0, 1, 2, 2, 0, 1, 5, 7, 111, 111, 111, 111, 111, 7, 8, 5, 6, 3, 4, 119, 120, 111, 111, 109, 4, 5, 6, 7, 119, 126, 125, 8, 6, 8, 8, 3, 4, 5, 0, 1, 2, 5, 113, 111, 111, 111, 111, 112, 109, 7, 0, 1, 2, 7, 0, 1, 2, 111, 112, 114, 113, 0, 1, 2, 114, 113, 1, 6, 7, 0, 1, 2, 1, 2, 4, 5, 122, 123, 2, 122, 117, 112, 112, 112, 113, 3, 4, 5, 5, 3, 4, 5, 111, 126, 120, 121, 3, 4, 124, 126, 125, 0, 1, 2, 3, 4, 5, 4, 5, 7, 8, 110, 123, -127, -127, -126, 124, 126, 117, 116, 6, 7, 8, 8, 6, 7, 8, 6, 3, 4, 5, 6, 7, 8, 7, 6, 3, 4, 5, 6, 7, 8, 7, 8, 107, 108, 118, 124, -126, -127, -127, -127, 4, 5, 3, 4, 5, 3, 6, 6, 7, 3, 3, 6, 7, 8, 4, 5, 4, 5, 4, 6, 7, 8, 4, 5, 4, 7, 8, 119, 126, 126, 125, 127, 126, -126, -127, 124, 1, 107, 108, 109, 6, 0, 3, 107, 108, 109, 1, 2, 0, 0, 1, 2, 2, 2, 6, 7, 8, 7, 8, 7, 0, 1, 2, 0, 1, 2, 4, 5, 127, 126, 125, 4, 119, 120, 121, 2, 6, 6, 119, 120, 121, 0, 1, 2, 2, 0, 1, 114, 113, 1, 115, 114, 113, 4, 5, 0, 1, 2, 107, 108, 109, 0, 1, 0, 1, 2, 0, 1, 2, 0, 0, 1, 2, 3, 4, 5, 3, 4, 5, 5, 3, 4, 126, 125, 0, 110, -127, 112, 114, 114, 113, 4, 5, 110, 111, 112, 113, 1, 3, 4, 5, 2, 4, 5, 3, 3, 0, 1, 2, 7, 107, -126, 7, 8, 8, 1, 2, 0, 1, 2, 127, 126, -126, 123, 123, -128, 7, 8, 6, 123, 123, 112, 113, 6, 7, 8, 5, 7, 8, 6, 6, 3, 4, 5, 8, 110, 118, 123, -127, -128, 4, 5, 3, 4, 0, 1, 2, 119, 120, 120, 125, 4, 0, 123, 2, 123, 123, 116, 8, 6, 7, 8, 1, 2, 0, 1, 6, 7, 8, 0, 122, 124, 120, 126, 125, 7, 8, 0, 1, 3, 4, 5, 3, 4, 5, 6, 7, 3, 4, 5, 0, 123, 2, 3, 4, 114, 113, 4, 0, 3, 4, 5, 7, 8, 1, 119, 124, -128, 4, 5, 115, 114, 3, 4, 6, 7, 8, 6, 7, 8, 1, 2, 2, 0, 0, 1, 2, 5, 6, 7, 8, 116, 1, 3, 6, 7, 8, 1, 1, 4, 1, -128, 115, 114, 113, 119, -126, 6, 7, 8, 124, -126, 6, 7, 3, 4, 5, 0, 0, 1, 2, 5, 8, 6, 7, 8, 112, 114, 113, 7, 8, 0, 1, 107, 108, 109, 112, 118, 117, 116, 4, 127, 126, 126, 126, 125, 127, 126, 125, 6, 120, -126, -127, -128, 4, 107, 117, 117, -127, 117, 124, 123, 123, -128, 1, 107, 108, 118, 5, -127, -127, 4, 5, 8, 8, 7, 8, 3, 4, 3, 4, 3, 3, -127, -128, 6, 127, -126, 112, 108, 118, 111, 124, -126, -127, 112, 118, 117, 116, 4, 127, 126, -126, -127, -127, -127, 7, 8, 7, 6, 7, 8, 6, -127, -128, 7, 6, 6, 126, 125, 4, 4, 122, 117, 124, -126, 124, 125, 127, 126, 120, -126, -127, -128, 7, 8, 122, 119, 120, -126, -127, 1, 1, 124, 120, -126, 123, 124, 126, 125, 0, 1, 2, 2, 6, 7, 7, 119, 120, 125, 127, 125, 7, 8, 8, 8, 127, 126, -128, 112, 8, 119, -126, -127, 108, 117, 109, 126, 125, 4, 127, 120, 121, 4, 5, 3, 4, 5, 5, 0, 107, 108, 109, 3, 4, 5, 1, 0, 107, 108, 2, 0, 107, 112, 108, 113, 3, 127, 126, -126, 117, 112, 113, 1, 7, 7, 8, 6, 7, 8, 6, 7, 8, 8, 3, 110, 111, 112, 113, 7, 0, 1, 107, 118, 123, 123, 112, 118, 111, 123, 112, 113, 7, 8, 127, -126, 117, 112, 114, 113, 1, 2, 0, 1, 2, 0, 1, 2, 2, 107, 118, 123, 117, 116, 0, 1, 2, 122, 123, 110, 111, 111, 111, 111, 117, 117, -128, 4, 115, 115, 118, 124, 120, 126, 125, 4, 5, 3, 4, 5, 3, 4, 5, 5, 119, 120, 126, 126, 125, 1, 2, 2, 122, 123, 124, 126, 126, -126, 123, 117, 111, 112, 114, 108, 117, -127, -128, 107, 114, 113, 7, 8, 6, 7, 8, 6, 7, 8, 8, 7, 8, 7, 3, 3, 4, 5, 5, 119, 120, 125, 107, 108, 118, 111, 124, 124, 124, 126, 120, -126, -126, 112, 118, 117, 116, 1, 107, 108, 109, 1, 4, 5, 3, 4, 5, 3, 107, 108, 109, 7, 8, 8, 3, 4, 107, -127, -127, -127, -127, -128, 1, 0, 3, 0, 1, 110, -127, -127, -128, 3, 4, 110, -127, 112, 114, 114, 113, 1, 2, 8, 127, 127, 126, 112, 114, 113, 107, 108, 108, 118, 126, 126, 126, 126, 125, 0, 3, 6, 3, 4, 0]}], "blocks": [1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}