{"mW": 960, "mH": 480, "tW": 24, "tH": 24, "tiles": [["1146", 0, 3, 3], ["106_5", 0, 3, 3], ["1316", 0, 4, 2], ["1316", 2, 4, 2], ["1316", 1, 4, 2], ["1316", 3, 4, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["75", 0, 3, 2], ["75", 2, 3, 2], ["75", 1, 3, 2], ["75", 3, 3, 2]], "layers": [{"type": 2, "data": [47, 47, 47, 47, 39, 47, 39, 40, 37, 47, 40, 41, 49, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 41, 42, 44, 41, 49, 48, 39, 31, 31, 47, 47, 31, 47, 47, 47, 47, 47, 47, 47, 47, 35, 43, 35, 36, 34, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 44, 49, 37, 39, 40, 43, 49, 43, 48, 47, 47, 47, 47, 31, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 35, 36, -1, -1, -1, 44, 43, 37, 47, 45, 35, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, 47, 47, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 22, 47, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 47, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 39, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 24, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 47, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 47, 47, 31, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 31, 31, 46, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 39, 40, 42, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 39, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 40, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, 27, 27, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 4, "obj": [[2, "1310_2", 60, 76, 18, 29, 2], [2, "1310_2", 58, 378, 18, 29, 2], [2, "1310_2", 70, 382, 18, 29, 2], [2, "1310_2", 32, 393, 18, 29, 2], [2, "1309_2", 12, 406, 20, 32, 2], [2, "1310_2", 47, 426, 18, 29, 0], [2, "1310_2", 19, 448, 18, 29, 0]]}, {"type": 3, "obj": [[2, "1302_3", 315, 133, 40, 29, 0], [2, "1303_2", 363, 173, 34, 20, 0], [2, "313_2", 669, 298, 70, 44, 2], [2, "1150", 626, 388, 48, 85, 2], [2, "1150", 703, 412, 48, 85, 2], [2, "1150", 746, 408, 48, 85, 0], [2, "1150", 673, 415, 48, 85, 2], [2, "1149", 811, 372, 40, 82, 0], [2, "1149", 787, 395, 40, 82, 0], [2, "214_3", 915, 347, 54, 40, 2], [2, "214_3", 914, 210, 54, 40, 2], [2, "216", 549, 135, 46, 46, 0], [2, "214_3", 582, 139, 54, 40, 2], [2, "214_3", 351, 428, 54, 40, 2], [2, "214_3", 456, 170, 54, 40, 2], [2, "213_3", 268, 425, 64, 45, 2], [2, "214_3", 324, 433, 54, 40, 2], [2, "214_3", 62, 304, 54, 40, 0], [2, "216", 92, 322, 46, 46, 2], [2, "213_3", 155, 163, 64, 45, 0], [2, "1150", 793, 10, 48, 85, 0], [2, "216", 116, 156, 46, 46, 2], [2, "216", 440, 206, 46, 46, 0], [2, "1150", 37, 115, 48, 85, 0], [2, "1150", 889, 22, 48, 85, 2], [2, "1148", 479, 315, 46, 108, 0], [2, "1148", 520, 332, 46, 108, 0], [2, "1150", 554, 358, 48, 85, 2], [2, "1149", 320, -12, 40, 82, 0], [2, "1150", 286, -6, 48, 85, 0], [2, "1149", 736, 40, 40, 82, 2], [2, "1149", 921, 54, 40, 82, 2], [2, "1150", 10, 174, 48, 85, 0], [2, "214_3", 422, 233, 54, 40, 2], [2, "214_3", 449, 356, 54, 40, 2], [2, "216", 527, 343, 46, 46, 0], [2, "213_3", 492, 135, 64, 45, 2], [2, "213_3", 114, 136, 64, 45, 0], [2, "213_3", 64, 120, 64, 45, 0], [2, "213_3", 856, 329, 64, 45, 2], [2, "1149", 589, 380, 40, 82, 2], [2, "1150", 763, 39, 48, 85, 0], [2, "216", 630, 144, 46, 46, 2], [2, "214_3", 632, 171, 54, 40, 0], [2, "214_3", 669, 63, 54, 40, 2], [2, "214_3", 395, 23, 54, 40, 0], [2, "216", 71, 142, 46, 46, 0], [2, "1310_2", 48, 168, 18, 29, 0], [2, "1150", 160, 3, 48, 85, 0], [2, "1149", 0, 221, 40, 82, 0], [2, "1150", 253, -5, 48, 85, 2], [2, "1150", 234, -5, 48, 85, 0], [2, "1150", 192, 0, 48, 85, 2], [2, "1308_2", 188, 6, 22, 37, 0], [2, "1310_2", 361, -12, 18, 29, 0], [2, "1154", 230, 29, 28, 51, 0], [2, "1153", 893, 46, 34, 54, 2], [2, "216", 821, 334, 46, 46, 2], [2, "214_3", 929, 449, 54, 40, 0], [2, "214_3", 599, 262, 54, 40, 0], [2, "216", 479, 296, 46, 46, 2], [2, "1147", 496, 441, 50, 42, 0], [2, "1149", 609, 380, 40, 82, 0], [2, "214_3", 642, 429, 54, 40, 0], [2, "1152", 594, 465, 38, 26, 0], [2, "1152", 565, 444, 38, 26, 0], [2, "1149", 7, 421, 40, 82, 0], [2, "1150", 855, 5, 48, 85, 2], [2, "1147", 831, 62, 50, 42, 0], [2, "214_3", 590, 379, 54, 40, 0], [2, "1150", 31, 55, 48, 85, 0], [2, "1147", 76, 211, 50, 42, 0], [2, "216", 21, 374, 46, 46, 0], [2, "425_2", 477, 412, 30, 36, 0], [2, "425_2", 807, 92, 30, 36, 0], [2, "426_2", 131, 208, 26, 22, 0], [2, "1147", 873, 394, 50, 42, 0], [2, "426_2", 887, 444, 26, 22, 0], [2, "313_2", 700, 322, 70, 44, 2], [2, "208_3", 140, 45, 78, 40, 2], [2, "181_3", 72, 18, 104, 100, 2], [2, "207_2", 281, 73, 38, 27, 0], [2, "152_3", 481, 121, 76, 40, 2], [2, "208_3", 305, 54, 78, 40, 0], [2, "208_3", 90, 307, 78, 40, 0], [2, "208_3", 791, 314, 78, 40, 2], [2, "208_3", 395, 217, 78, 40, 2], [2, "208_3", 428, 328, 78, 40, 2], [2, "208_3", 729, 108, 78, 40, 0], [2, "208_3", 815, 104, 78, 40, 2], [2, "208_3", 645, 32, 78, 40, 3], [2, "208_3", 873, 206, 78, 40, 2], [2, "208_3", 525, 327, 78, 40, 0], [2, "208_3", 621, 225, 78, 40, 2], [2, "152_3", 628, 152, 76, 40, 0], [2, "208_3", 880, 117, 78, 40, 0], [2, "207_2", 801, 121, 38, 27, 0], [2, "208_3", 890, 143, 78, 40, 3], [2, "205_3", 913, 177, 54, 40, 2], [2, "207_2", 654, 49, 38, 27, 2], [2, "208_3", 594, 11, 78, 40, 0], [2, "207_2", 667, 65, 38, 27, 0], [2, "208_3", 555, 11, 78, 40, 2], [2, "207_2", 542, 22, 38, 27, 0], [2, "208_3", 552, 114, 78, 40, 0], [2, "1144", 609, 96, 114, 70, 0], [2, "208_3", 385, 44, 78, 40, 2], [2, "207_2", 359, 65, 38, 27, 2], [2, "208_3", 453, 10, 78, 40, 0], [2, "205_3", 516, 14, 54, 40, 0], [2, "152_3", 391, 3, 76, 40, 0], [2, "206", 58, 108, 66, 40, 0], [2, "206", 663, 188, 66, 40, 0], [2, "208_3", 750, 385, 78, 40, 2], [2, "208_3", 899, 242, 78, 40, 3], [2, "208_3", 599, 245, 78, 40, 1], [2, "205_3", 140, 210, 54, 40, 0], [2, "208_3", 91, 239, 78, 40, 1], [2, "152_3", 62, 273, 76, 40, 2], [2, "164_1", 41, 273, 60, 30, 0], [2, "165_1", 80, 233, 42, 37, 0], [2, "166_2", 141, 198, 30, 35, 0], [2, "207_2", 426, 170, 38, 27, 2], [2, "205_3", 432, 186, 54, 40, 2], [2, "207_2", 469, 157, 38, 27, 2], [2, "165_1", 935, 168, 42, 37, 2], [2, "166_2", 894, 108, 30, 35, 2], [2, "166_2", 600, 242, 30, 35, 0], [2, "313_2", 283, 136, 70, 44, 0], [2, "313_2", 317, 161, 70, 44, 2], [2, "206", 771, 353, 66, 40, 0], [2, "208_3", 585, 280, 78, 40, 2], [2, "205_3", 548, 307, 54, 40, 0], [2, "206", 585, 355, 66, 40, 2], [2, "1151", 662, 64, 38, 33, 2], [2, "208_3", 627, 384, 78, 40, 0], [2, "208_3", 641, 411, 78, 40, 1], [2, "1151", 636, 258, 38, 33, 0], [2, "1310_2", 717, 19, 18, 29, 0], [2, "1310_2", 839, 23, 18, 29, 0], [2, "1310_2", 641, -2, 18, 29, 0], [2, "1308_2", 728, 23, 22, 37, 0], [2, "313_2", 662, 333, 70, 44, 2], [2, "313_2", 283, 186, 70, 44, 2], [2, "166_2", 643, 410, 30, 35, 0], [2, "1305_2", 722, 232, 20, 14, 0], [2, "1302_3", 692, 210, 40, 29, 0], [2, "1303_2", 700, 241, 34, 20, 0], [2, "955_4", 850, 301, 20, 18, 0], [2, "955_4", 244, 388, 20, 18, 0], [2, "955_4", 409, 217, 20, 18, 0], [2, "955_4", 353, 94, 20, 18, 0], [2, "1303_2", 746, 390, 34, 20, 0], [2, "1303_2", 358, 373, 34, 20, 2], [2, "1303_2", 317, 92, 34, 20, 2], [2, "1303_2", 621, 51, 34, 20, 2], [2, "1303_2", 622, 318, 34, 20, 2], [2, "955_4", 588, 340, 20, 18, 0], [2, "426_2", 504, 225, 26, 22, 0], [2, "1147", 438, 466, 50, 42, 2], [2, "1301_2", 456, 4, 24, 49, 0], [2, "1151", 436, 165, 38, 33, 2], [2, "1303_2", 467, 42, 34, 20, 2], [2, "1153", 19, 198, 34, 54, 0], [2, "1153", 331, 1, 34, 54, 0], [2, "1153", 15, 439, 34, 54, 0], [2, "205_3", 108, 128, 54, 40, 2], [2, "208_3", 151, 147, 78, 40, 0], [2, "205_3", 168, 178, 54, 40, 0], [2, "213_3", 97, 359, 64, 45, 0], [2, "205_3", 99, 341, 54, 40, 2], [2, "214_3", 128, 391, 54, 40, 0], [2, "208_3", 123, 363, 78, 40, 0], [2, "213_3", 148, 409, 64, 45, 0], [2, "206", 143, 390, 66, 40, 0], [2, "214_3", 200, 424, 54, 40, 0], [2, "208_3", 191, 396, 78, 40, 0], [2, "208_3", 272, 411, 78, 40, 2], [2, "214_3", 247, 432, 54, 40, 2], [2, "206", 244, 412, 66, 40, 2], [2, "214_3", 435, 386, 54, 40, 2], [2, "205_3", 437, 364, 54, 40, 2], [2, "208_3", 413, 249, 78, 40, 0], [2, "207_2", 472, 308, 38, 27, 0], [2, "214_3", 398, 412, 54, 40, 2], [2, "152_3", 384, 387, 76, 40, 2], [2, "206", 339, 407, 66, 40, 2], [2, "205_3", 470, 272, 54, 40, 2], [2, "166_2", 457, 178, 30, 35, 2], [2, "165_1", 495, 260, 42, 37, 2], [2, "164_1", 459, 366, 60, 30, 2], [2, "165_1", 95, 333, 42, 37, 0], [2, "1301_2", 217, 148, 24, 49, 0], [2, "714", 322, -21, 54, 132, 0], [2, "713", 347, 22, 18, 27, 2], [2, "1303_2", 348, 110, 34, 20, 0], [2, "1302_3", 369, 85, 40, 29, 0], [2, "1147", 539, 246, 50, 42, 0], [2, "205_3", 579, 114, 54, 40, 2], [2, "1305_2", 717, 117, 20, 14, 2], [2, "208_3", 861, 312, 78, 40, 0], [2, "205_3", 918, 326, 54, 40, 2], [2, "214_3", 699, 431, 54, 40, 2], [2, "213_3", 735, 423, 64, 45, 2], [2, "208_3", 726, 401, 78, 40, 2], [2, "205_3", 699, 409, 54, 40, 2], [2, "313_2", 836, 133, 70, 44, 0], [2, "1151", 902, 155, 38, 33, 0], [2, "1154", 821, 397, 28, 51, 0], [2, "1154", 612, 412, 28, 51, 0], [2, "1153", 555, 376, 34, 54, 2], [2, "208_3", 927, 423, 78, 40, 0], [2, "1305_2", 225, 350, 20, 14, 0], [2, "1303_2", 162, 321, 34, 20, 2], [2, "955_4", 365, 332, 20, 18, 0], [2, "955_4", 230, 184, 20, 18, 0], [2, "544_1", 301, 255, 86, 107, 0], [2, "544_1", 217, 255, 86, 107, 2], [2, "178_4", 298, 268, 70, 37, 0], [2, "178_4", 235, 268, 70, 37, 2], [2, "178_4", 298, 305, 70, 37, 1], [2, "178_4", 235, 305, 70, 37, 3], [2, "208_3", 208, 66, 78, 40, 3], [2, "1310_2", 567, -5, 18, 29, 0], [2, "714", 834, 23, 54, 132, 0], [2, "713", 862, 68, 18, 27, 2], [2, "1151", 207, 198, 38, 33, 0], [2, "1152", 580, 208, 38, 26, 0], [2, "1152", 89, 449, 38, 26, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 66, 66, 66, 54, 66, 54, 72, 71, 70, 69, 68, 62, 63, -1, 72, 71, -1, -1, -1, -1, -1, -1, 9, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 67, 73, 72, -1, -1, 57, 54, 69, 68, 57, 56, 68, 57, 56, 70, 69, 68, 66, 66, -1, 66, 72, 72, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 63, 64, 70, 69, -1, 62, 63, 64, -1, -1, -1, 82, 81, 80, -1, -1, -1, 70, 69, -1, -1, 63, 69, 69, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 51, 52, -1, -1, -1, 74, 81, 80, -1, 74, 85, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 74, 75, 81, 80, -1, -1, -1, 62, 63, 64, -1, 74, 75, 85, 91, 88, -1, 86, 87, 93, 92, -1, -1, 57, 56, -1, -1, -1, -1, -1, -1, -1, 58, 57, -1, -1, -1, -1, -1, -1, 86, 87, 97, 79, 80, -1, -1, -1, -1, -1, -1, 86, 87, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 61, 60, -1, -1, -1, -1, 66, 66, -1, -1, 94, 93, 92, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 72, 72, 66, 67, 60, -1, -1, -1, 74, 75, 75, 76, -1, -1, -1, -1, -1, 66, -1, 66, 66, 67, 73, 72, 66, 66, 57, 57, 56, -1, -1, -1, -1, -1, -1, -1, -1, 92, -1, 72, 66, 67, 73, 60, 60, 60, -1, -1, 89, 90, 78, 79, 81, 75, 76, -1, -1, 66, -1, 66, 63, 64, 70, 69, 66, 66, -1, 69, 68, 74, 75, 80, -1, -1, -1, -1, -1, 66, 67, 63, 63, 64, 65, 72, 60, 60, -1, -1, 94, 97, 78, 78, 78, 84, 95, -1, -1, 66, -1, 63, 64, -1, -1, 65, 66, 66, -1, -1, -1, 77, 78, 83, -1, -1, -1, -1, -1, 63, 71, -1, -1, -1, 70, 69, 73, 72, 71, -1, -1, 89, 78, 78, 90, 91, 92, -1, -1, 66, 66, 71, -1, -1, -1, -1, -1, -1, -1, -1, 74, 85, 84, 79, 80, -1, 82, 81, 66, 67, 68, -1, -1, -1, -1, -1, 70, 69, 68, -1, -1, 89, 96, 91, 87, 88, -1, -1, -1, -1, 63, 56, 50, 51, -1, -1, -1, -1, -1, 74, 75, 75, 76, 74, 75, 75, 85, 84, 63, 64, -1, -1, -1, -1, 50, 51, 66, -1, -1, -1, -1, 86, 93, 92, -1, 82, 81, 81, 80, -1, -1, 59, 50, 51, -1, -1, -1, -1, -1, 77, 78, 78, 79, 85, 78, 78, 78, 96, 95, -1, -1, 71, -1, 50, 61, 54, -1, -1, 74, 75, 74, 81, 76, -1, -1, 86, 87, 87, 92, -1, -1, -1, 53, 54, -1, -1, -1, -1, -1, 77, 78, 78, 78, 78, 90, 91, 87, 93, 92, -1, -1, 68, -1, 53, 54, -1, 74, 75, 85, 96, 96, 84, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, 73, 72, -1, -1, -1, -1, 89, 90, 78, 78, 91, 87, 88, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 86, 97, 96, 96, 96, 96, 95, -1, -1, -1, 66, 66, 66, -1, -1, -1, -1, 70, 69, -1, -1, -1, -1, 86, 97, 78, 96, 95, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 94, 93, 97, 90, 91, 92, -1, -1, 66, 67, 73, 72, 66, 54, 66, 66, 56, 73, 72, -1, -1, -1, -1, 86, 87, 93, 92, -1, -1, -1, -1, -1, 71, 71, -1, -1, -1, -1, -1, -1, -1, -1, 86, 87, 92, -1, -1, 66, 63, 64, 62, 63, 63, 54, 66, 66, 59, 70, 73, 72, 71, -1, -1, -1, -1, -1, -1, -1, -1, -1, 66, 72, 71, -1, -1, -1, -1, -1, -1, 66, -1, -1, -1, -1, -1, -1, -1, 63, 56, -1, -1, -1, -1, 66, 66, 67, 68, -1, 70, 69, 73, 72, 66, 66, 66, 66, 67, -1, -1, 66, 72, 69, 68, -1, -1, -1, 50, 51, 66, 66, -1, -1, -1, -1, -1, -1, 66, 66, 59, -1, -1, -1, -1, 63, 63, 64, -1, -1, -1, -1, 70, 69, 63, 63, 63, 63, 64, -1, -1, 66, 55, 57, 56, -1, -1, -1, 53, 54, 66, 66, 54, 54, -1, 74, 75, 81, 63, 63, 64, -1, -1, -1, -1]}, {"type": 2, "data": [-1, -1, -1, 0, 1, 2, 0, 1, 2, 2, -1, 0, 1, 2, 0, 1, 0, 1, 3, 4, 5, 3, 4, 5, 3, 4, 5, 7, 8, 1, 2, -1, -1, -1, -1, 0, 1, 2, -1, -1, -1, -1, -1, 3, 4, 5, 3, 4, 5, 5, 0, 3, 4, 5, 3, 4, 3, 4, 13, 14, 8, 6, 7, 8, 6, 7, 8, 6, 3, 4, 5, 2, 0, 0, 1, 0, 1, 2, -1, -1, -1, -1, -1, 6, 7, 8, 6, 7, 8, 8, 3, 6, 7, 8, 6, 7, 6, 7, 16, 17, 17, 10, 9, 10, 11, 9, 10, 11, 6, 7, 0, 1, 2, 3, 4, 3, 4, 5, 0, 1, -1, -1, -1, 9, 10, 11, 9, 9, 10, 10, 11, 7, 8, 10, 11, 11, 9, 10, 9, 9, 12, 13, 12, 13, 14, 12, 13, 14, 14, 3, 3, 4, 0, 1, 0, 1, 2, 0, 1, 2, -1, -1, -1, 12, 13, 14, 12, 12, 13, 9, 10, 9, 12, 13, 14, 14, 12, 13, 9, 10, 11, 9, 13, 14, 9, 9, 10, 11, 17, 3, 6, 7, 3, 4, 3, 4, 5, 3, 4, 5, 0, 1, 2, 15, 16, 17, 9, 9, 10, 12, 13, 9, 10, 11, 9, 10, 11, 16, 9, 10, 11, 12, 16, 17, 12, 12, 13, 14, 9, 10, 11, 9, 6, 7, 6, 7, 8, 6, 7, 8, 3, 0, 1, 2, 0, 1, 12, 12, 13, 10, 11, 12, 13, 14, 12, 13, 14, 11, 12, 13, 14, 0, 1, 2, 15, 15, 2, 17, 12, 13, 9, 12, 13, 14, 15, 16, 15, 16, 17, 1, 6, 3, 4, 5, 3, 4, 5, 3, 4, 9, 10, 15, 16, 17, 15, 16, 17, 14, 15, 16, 0, 1, 2, 0, 1, 2, 0, 1, 15, 16, 12, 15, 16, 17, 12, 9, 10, 11, 11, 1, 3, 0, 1, 2, 6, 7, 8, 6, 9, 12, 13, 12, 13, 14, 10, 15, 9, 10, 11, 0, 3, 4, 5, 3, 4, 5, 3, 4, 9, 10, 11, 11, 11, 12, 15, 9, 10, 11, 11, 4, 6, 3, 4, 5, 6, 7, 8, 9, 10, 15, 16, 15, 16, 17, 13, 14, 12, 13, 14, 0, 6, 7, 8, 6, 7, 8, 6, 7, 12, 13, 14, 14, 14, 9, 10, 11, 13, 14, 14, 7, 0, 1, 2, 8, 4, 9, 9, 10, 11, 9, 10, 15, 16, 17, 16, 17, 15, 16, 17, 3, 4, 3, 4, 5, 2, 0, 1, 2, 15, 16, 17, 17, 11, 12, 13, 9, 9, 10, 11, 2, 3, 4, 5, 6, 7, 12, 12, 13, 14, 12, 13, 17, 11, 12, 13, 14, 12, 13, 14, 6, 7, 6, 7, 8, 5, 3, 4, 9, 15, 16, 17, 17, 9, 9, 10, 11, 12, 13, 14, 11, 6, 7, 8, 0, 11, 9, 15, 9, 10, 11, 16, 11, 14, 9, 10, 11, 10, 11, 10, 11, 17, 3, 4, 5, 8, 6, 9, 10, 11, 14, 12, 9, 10, 11, 13, 13, 15, 16, 17, 14, -1, 0, 1, 2, 4, 9, 10, 12, 13, 14, 13, 9, 10, 12, 13, 14, 13, 14, 10, 11, 2, 6, 7, 8, 10, 11, 12, 13, 14, 17, 9, 10, 11, 14, 16, 16, 17, 15, 16, 17, -1, 3, 4, 5, 7, 12, 13, 15, 16, 17, 16, 12, 13, 15, 16, 17, 9, 10, 11, 14, 5, 6, 7, 8, 13, 14, 15, 16, 17, 11, 12, 13, 14, 17, 3, 4, 6, 6, 7, 8, -1, 6, 7, 8, 0, 15, 16, 13, 14, 17, 17, 15, 16, 17, 12, 15, 12, 13, 14, 0, 0, 1, 2, 2, 16, 17, 9, 10, 11, 14, 15, 16, 17, 1, 2, 7, 0, 1, 0, 1, 1, 2, 3, 0, 3, 6, 6, 16, 17, 11, 16, 17, 16, 17, 15, 16, 15, 16, 17, 2, 3, 4, 5, 5, 0, 1, 2, 13, 14, 9, 10, 16, 10, 11, 5, 1, 2, 4, 3, 4, 4, 5, 6, 3, 6, 7, 7, 13, 13, 14, 17, 9, 10, 11, 12, 13, 14, 0, 1, 0, 1, 2, 8, 8, 3, 4, 5, 9, 10, 11, 11, 16, 13, 14, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 5, 0, 1, 2, 0, 1, 2, 13, 14, 14, 10, 10, 9, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 3, 4, 5, 8, 3, 4, 5, 3, 4, 5, 2, 17, 9, 13, 0, 1, 6, 7, 8, 6, 7, 8]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}