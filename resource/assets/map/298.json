{"mW": 960, "mH": 720, "tW": 24, "tH": 24, "tiles": [["302_4", 0, 2, 2], ["302_4", 2, 2, 2], ["1139", 0, 1, 1], ["1139", 2, 1, 1], ["1134", 0, 3, 2], ["1134", 2, 3, 2], ["1134", 1, 3, 2], ["1134", 3, 3, 2], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["137", 0, 5, 1], ["137", 2, 5, 1], ["137", 1, 5, 1], ["137", 3, 5, 1], ["1316", 0, 4, 2]], "layers": [{"type": 3, "obj": [[2, "1141", 217, 629, 54, 67, 2], [2, "1141", 261, 650, 54, 67, 2], [2, "1141", 304, 672, 54, 67, 2], [2, "1141", 347, 693, 54, 67, 2], [2, "1140", 215, 607, 58, 47, 2], [2, "1140", 259, 628, 58, 47, 2], [2, "1140", 303, 649, 58, 47, 2], [2, "1140", 346, 670, 58, 47, 2], [2, "1141", 921, 677, 54, 67, 0], [2, "1141", 878, 698, 54, 67, 0], [2, "1141", 835, 719, 54, 67, 0], [2, "1140", 921, 652, 58, 47, 0], [2, "1140", 878, 673, 58, 47, 0], [2, "1140", 835, 694, 58, 47, 0], [2, "1140", 388, 691, 58, 47, 2], [2, "1140", 484, 381, 58, 47, 0], [2, "1140", 442, 402, 58, 47, 0], [2, "1140", 399, 424, 58, 47, 0], [2, "1140", 356, 445, 58, 47, 0], [2, "1140", 313, 467, 58, 47, 0], [2, "1140", 270, 488, 58, 47, 0], [2, "1140", 226, 509, 58, 47, 0], [2, "1140", 184, 530, 58, 47, 0], [2, "1140", 141, 552, 58, 47, 0], [2, "1140", 98, 573, 58, 47, 0], [2, "1140", 61, 593, 58, 47, 0], [2, "1143", 658, 435, 32, 36, 2]]}, {"type": 4, "obj": [[2, "1140", 164, -30, 58, 47, 0], [2, "1143", 574, -19, 32, 36, 2], [2, "1143", 223, -16, 32, 36, 0], [2, "1140", 121, -8, 58, 47, 0], [2, "1143", 666, 20, 32, 36, 2], [2, "1140", 78, 14, 58, 47, 0], [2, "1143", 128, 31, 32, 36, 0], [2, "1143", 877, 42, 32, 36, 2], [2, "1140", 34, 36, 58, 47, 0], [2, "1138", 706, 44, 46, 60, 2], [2, "1140", -9, 58, 58, 47, 0], [2, "1143", 33, 73, 32, 36, 0], [2, "1143", 374, 76, 32, 36, 2], [2, "1140", 313, 71, 58, 47, 0], [2, "1143", 764, 82, 32, 36, 0], [2, "1140", 358, 75, 58, 47, 2], [2, "1140", 273, 91, 58, 47, 0], [2, "1138", 256, 79, 46, 60, 2], [2, "1140", 400, 96, 58, 47, 2], [2, "1137", 57, 85, 66, 59, 0], [2, "1143", 322, 110, 32, 36, 0], [2, "1138", 597, 89, 46, 60, 2], [2, "1140", 443, 119, 58, 47, 2], [2, "1143", 413, 131, 32, 36, 2], [2, "1138", 147, 124, 46, 60, 2], [2, "1140", 487, 141, 58, 47, 2], [2, "1140", -14, 144, 58, 47, 2], [2, "1140", 563, 152, 58, 47, 0], [2, "1140", -12, 162, 58, 47, 0], [2, "1140", 530, 163, 58, 47, 2], [2, "1143", 509, 178, 32, 36, 2], [2, "1143", 8, 184, 32, 36, 0], [2, "1138", 570, 207, 46, 60, 0], [2, "1138", 648, 243, 46, 60, 0], [2, "1143", 653, 312, 32, 36, 2], [2, "1143", 490, 319, 32, 36, 0], [2, "1143", 753, 359, 32, 36, 2], [2, "84_1", 220, 356, 66, 65, 2], [2, "1143", 882, 385, 32, 36, 0], [2, "1141", 489, 403, 54, 67, 0], [2, "1141", 447, 423, 54, 67, 0], [2, "84_1", 71, 429, 66, 65, 2], [2, "542_1", 646, 442, 26, 60, 2], [2, "542_1", 673, 442, 26, 60, 0], [2, "1141", 402, 445, 54, 67, 0], [2, "1143", 448, 489, 32, 36, 0], [2, "1143", 491, 494, 32, 36, 2], [2, "1135", 220, 422, 118, 114, 2], [2, "1141", 358, 469, 54, 67, 0], [2, "1143", 807, 500, 32, 36, 0], [2, "1141", 314, 491, 54, 67, 0], [2, "1143", 340, 541, 32, 36, 0], [2, "1141", 271, 512, 54, 67, 0], [2, "1143", 608, 546, 32, 36, 2], [2, "1143", 708, 546, 32, 36, 0], [2, "1141", 231, 531, 54, 67, 0], [2, "1141", 189, 551, 54, 67, 0], [2, "1141", 144, 573, 54, 67, 0], [2, "1141", 100, 597, 54, 67, 0], [2, "1141", 64, 615, 54, 67, 0], [2, "1136", 842, 640, 108, 95, 0]]}, {"type": 3, "obj": [[2, "1141", 350, 95, 54, 67, 2], [2, "1141", 371, 105, 54, 67, 2], [2, "1141", 208, -27, 54, 67, 0], [2, "1142", 222, -11, 30, 37, 0], [2, "1141", 314, 96, 54, 67, 0], [2, "1141", 271, 115, 54, 67, 0], [2, "1142", 318, 115, 30, 37, 0], [2, "683_1", 734, 124, 22, 34, 2], [2, "683_1", 742, 128, 22, 34, 2], [2, "683_1", 750, 132, 22, 34, 2], [2, "683_1", 757, 136, 22, 34, 2], [2, "683_1", 765, 140, 22, 34, 2], [2, "683_1", 773, 144, 22, 34, 2], [2, "683_1", 781, 148, 22, 34, 2], [2, "1141", 491, 462, 54, 67, 0], [2, "1141", 490, 443, 54, 67, 0], [2, "1141", 449, 487, 54, 67, 0], [2, "1141", 449, 464, 54, 67, 0], [2, "1141", 405, 507, 54, 67, 0], [2, "1141", 405, 485, 54, 67, 0], [2, "683_1", 491, 509, 22, 34, 0], [2, "683_1", 498, 513, 22, 34, 0], [2, "683_1", 506, 517, 22, 34, 0], [2, "683_1", 514, 521, 22, 34, 0], [2, "683_1", 522, 525, 22, 34, 0], [2, "43_6", 503, 537, 82, 58, 0], [2, "43_6", 551, 561, 82, 58, 0], [2, "43_6", 513, 526, 82, 58, 0], [2, "43_6", 561, 550, 82, 58, 0], [2, "1141", 540, 289, 54, 67, 0], [2, "1141", 493, 294, 54, 67, 0], [2, "1141", 451, 316, 54, 67, 0], [2, "1141", 543, 254, 54, 67, 0], [2, "1141", 500, 275, 54, 67, 0], [2, "1141", 457, 295, 54, 67, 0], [2, "43_6", 750, 544, 82, 58, 2], [2, "43_6", 703, 568, 82, 58, 2], [2, "683_1", 793, 528, 22, 34, 2], [2, "683_1", 801, 532, 22, 34, 2], [2, "683_1", 809, 536, 22, 34, 2], [2, "683_1", 816, 540, 22, 34, 2], [2, "683_1", 824, 544, 22, 34, 2], [2, "683_1", 832, 548, 22, 34, 2], [2, "683_1", 840, 552, 22, 34, 2], [2, "1138", 777, 489, 46, 60, 2], [2, "1141", 934, 360, 54, 67, 0], [2, "1141", 890, 382, 54, 67, 0], [2, "1141", 629, 303, 54, 67, 2], [2, "1141", 672, 324, 54, 67, 2], [2, "1141", 715, 347, 54, 67, 2], [2, "1141", 756, 367, 54, 67, 2], [2, "1141", 799, 389, 54, 67, 2], [2, "1141", 813, 397, 54, 67, 2], [2, "1141", 412, 125, 54, 67, 2], [2, "1141", 453, 147, 54, 67, 2], [2, "1141", 496, 169, 54, 67, 2], [2, "1141", 846, 403, 54, 67, 0], [2, "1141", 863, 24, 54, 67, 2], [2, "1141", 906, 45, 54, 67, 2], [2, "1141", 949, 64, 54, 67, 2], [2, "1141", 819, 28, 54, 67, 0], [2, "1141", 776, 48, 54, 67, 0], [2, "1141", 535, -48, 54, 67, 2], [2, "1141", 578, -26, 54, 67, 2], [2, "1141", 621, -5, 54, 67, 2], [2, "1141", 665, 15, 54, 67, 2], [2, "1141", 755, 77, 54, 67, 0], [2, "1141", 726, 86, 54, 67, 0], [2, "1141", 164, -5, 54, 67, 0], [2, "1141", 121, 17, 54, 67, 0], [2, "1141", 78, 39, 54, 67, 0], [2, "1141", 34, 61, 54, 67, 0], [2, "1141", -9, 83, 54, 67, 0], [2, "1141", -12, 169, 54, 67, 2], [2, "1141", -12, 187, 54, 67, 0], [2, "683_1", 264, 157, 22, 34, 2], [2, "683_1", 256, 161, 22, 34, 2], [2, "683_1", 248, 165, 22, 34, 2], [2, "683_1", 240, 169, 22, 34, 2], [2, "683_1", 231, 174, 22, 34, 2], [2, "683_1", 223, 178, 22, 34, 2], [2, "683_1", 215, 182, 22, 34, 2], [2, "683_1", 207, 186, 22, 34, 2], [2, "683_1", 198, 191, 22, 34, 2], [2, "683_1", 190, 195, 22, 34, 2], [2, "683_1", 181, 200, 22, 34, 2], [2, "683_1", 173, 204, 22, 34, 2], [2, "683_1", 164, 209, 22, 34, 2], [2, "683_1", 156, 213, 22, 34, 2], [2, "683_1", 147, 217, 22, 34, 2], [2, "683_1", 139, 221, 22, 34, 2], [2, "683_1", 130, 226, 22, 34, 2], [2, "683_1", 122, 230, 22, 34, 2], [2, "683_1", 114, 234, 22, 34, 2], [2, "683_1", 106, 238, 22, 34, 2], [2, "683_1", 97, 243, 22, 34, 2], [2, "683_1", 89, 247, 22, 34, 2], [2, "683_1", 34, 227, 22, 34, 2], [2, "683_1", 42, 231, 22, 34, 2], [2, "683_1", 50, 235, 22, 34, 2], [2, "683_1", 57, 239, 22, 34, 2], [2, "683_1", 65, 243, 22, 34, 2], [2, "683_1", 73, 247, 22, 34, 2], [2, "683_1", 81, 251, 22, 34, 2], [2, "1137", -10, 227, 66, 59, 0], [2, "1137", 462, 195, 66, 59, 0], [2, "954_3", 304, 168, 24, 25, 2], [2, "367_1", 89, 212, 14, 42, 2], [2, "11_2", 17, 124, 32, 29, 0], [2, "1142", 759, 96, 30, 37, 0], [2, "1142", 823, 48, 30, 37, 0], [2, "1142", 884, 47, 30, 37, 2], [2, "1142", 935, 69, 30, 37, 2], [2, "1142", 5, 199, 30, 37, 0], [2, "1142", 30, 85, 30, 37, 0], [2, "1142", 77, 61, 30, 37, 0], [2, "1142", 128, 36, 30, 37, 0], [2, "1142", 178, 11, 30, 37, 0], [2, "702_1", 769, 98, 18, 25, 2], [2, "702_1", 14, 202, 18, 25, 2], [2, "1142", 675, 28, 30, 37, 2], [2, "1142", 628, 6, 30, 37, 2], [2, "1142", 582, -14, 30, 37, 2], [2, "702_1", 676, 30, 18, 25, 0], [2, "702_1", 585, -13, 18, 25, 0], [2, "14_5", 425, 186, 32, 30, 2], [2, "14_5", 444, 196, 32, 30, 2], [2, "14_5", 437, 177, 32, 30, 2], [2, "1303_1", 121, 115, 34, 20, 0], [2, "702_1", 38, 88, 18, 25, 2], [2, "702_1", 135, 40, 18, 25, 2], [2, "1141", -5, 654, 54, 67, 2], [2, "1141", 37, 674, 54, 67, 2], [2, "1141", 706, 35, 54, 67, 2], [2, "1141", 729, 40, 54, 67, 2], [2, "683_1", 848, 556, 22, 34, 2], [2, "683_1", 856, 560, 22, 34, 2], [2, "683_1", 864, 564, 22, 34, 2], [2, "683_1", 871, 568, 22, 34, 2], [2, "683_1", 879, 572, 22, 34, 2], [2, "683_1", 887, 576, 22, 34, 2], [2, "683_1", 895, 580, 22, 34, 2], [2, "683_1", 901, 583, 22, 34, 2], [2, "683_1", 909, 587, 22, 34, 2], [2, "683_1", 917, 591, 22, 34, 2], [2, "683_1", 924, 595, 22, 34, 2], [2, "683_1", 932, 599, 22, 34, 2], [2, "683_1", 940, 603, 22, 34, 2], [2, "683_1", 948, 607, 22, 34, 2], [2, "43_6", 739, 535, 82, 58, 2], [2, "43_6", 697, 556, 82, 58, 2], [2, "683_1", 695, 579, 22, 34, 2], [2, "683_1", 687, 583, 22, 34, 2], [2, "683_1", 678, 588, 22, 34, 2], [2, "683_1", 670, 592, 22, 34, 2], [2, "1138", 679, 539, 46, 60, 2], [2, "1135", 847, 60, 118, 114, 2], [2, "683_1", 790, 152, 22, 34, 2], [2, "683_1", 798, 156, 22, 34, 2], [2, "683_1", 806, 160, 22, 34, 2], [2, "683_1", 813, 164, 22, 34, 2], [2, "683_1", 821, 168, 22, 34, 2], [2, "683_1", 829, 172, 22, 34, 2], [2, "683_1", 837, 176, 22, 34, 2], [2, "683_1", 844, 179, 22, 34, 2], [2, "683_1", 852, 183, 22, 34, 2], [2, "683_1", 860, 187, 22, 34, 2], [2, "683_1", 867, 191, 22, 34, 2], [2, "683_1", 875, 195, 22, 34, 2], [2, "683_1", 883, 199, 22, 34, 2], [2, "683_1", 891, 203, 22, 34, 2], [2, "683_1", 899, 207, 22, 34, 2], [2, "683_1", 907, 211, 22, 34, 2], [2, "683_1", 915, 215, 22, 34, 2], [2, "683_1", 923, 219, 22, 34, 2], [2, "1141", 939, 187, 54, 67, 0], [2, "1141", 935, 208, 54, 67, 2], [2, "1142", 944, 222, 30, 37, 2], [2, "702_1", 947, 224, 18, 25, 0], [2, "1302_2", 113, 84, 40, 29, 0], [2, "1140", 937, 163, 58, 47, 0], [2, "1140", 933, 183, 58, 47, 2], [2, "1143", 937, 213, 32, 36, 2], [2, "43_7", 788, 171, 82, 58, 0], [2, "84_1", 864, 200, 66, 65, 0], [2, "367_1", 888, 160, 14, 42, 0], [2, "367_1", 806, 125, 14, 42, 0], [2, "1137", 914, 249, 66, 59, 2], [2, "702_1", 888, 51, 18, 25, 0], [2, "11_2", 162, 43, 32, 29, 2], [2, "1142", 469, 165, 30, 37, 2], [2, "1137", 681, 57, 66, 59, 2], [2, "11_2", 397, 173, 32, 29, 0], [2, "702_1", 715, 556, 18, 25, 2], [2, "14_5", 755, 413, 32, 30, 2], [2, "14_5", 864, 590, 32, 30, 2], [2, "14_5", 887, 601, 32, 30, 2], [2, "14_5", 877, 584, 32, 30, 2], [2, "11_2", 912, 614, 32, 29, 0], [2, "1303_1", 322, 405, 34, 20, 0], [2, "1141", 629, 272, 54, 67, 2], [2, "1141", 672, 291, 54, 67, 2], [2, "1141", 715, 312, 54, 67, 2], [2, "1141", 758, 336, 54, 67, 2], [2, "1141", 802, 357, 54, 67, 2], [2, "1141", 934, 319, 54, 67, 0], [2, "1141", 890, 341, 54, 67, 0], [2, "1141", 847, 366, 54, 67, 0], [2, "929_1", 597, 251, 60, 31, 2], [2, "929_1", 641, 274, 60, 31, 2], [2, "929_1", 686, 296, 60, 31, 2], [2, "929_1", 730, 318, 60, 31, 2], [2, "929_1", 771, 339, 60, 31, 2], [2, "929_1", 923, 319, 60, 31, 0], [2, "929_1", 877, 341, 60, 31, 0], [2, "929_1", 843, 358, 60, 31, 0], [2, "929_1", 814, 360, 60, 31, 2], [2, "929_1", 539, 248, 60, 31, 0], [2, "929_1", 496, 269, 60, 31, 0], [2, "1137", 600, 326, 66, 59, 2], [2, "14_5", 654, 356, 32, 30, 0], [2, "1142", 661, 317, 30, 37, 2], [2, "1142", 710, 340, 30, 37, 2], [2, "1142", 761, 365, 30, 37, 2], [2, "1142", 811, 386, 30, 37, 2], [2, "702_1", 662, 324, 18, 25, 0], [2, "702_1", 762, 369, 18, 25, 0], [2, "1142", 879, 390, 30, 37, 0], [2, "1142", 933, 370, 30, 37, 0], [2, "702_1", 887, 394, 18, 25, 2], [2, "1141", 412, 336, 54, 67, 0], [2, "1141", 418, 318, 54, 67, 0], [2, "1141", 376, 338, 54, 67, 0], [2, "929_1", 409, 313, 60, 31, 0], [2, "929_1", 368, 334, 60, 31, 0], [2, "929_1", 351, 355, 60, 31, 2], [2, "929_1", 453, 290, 60, 31, 0], [2, "929_1", 395, 377, 60, 31, 2], [2, "929_1", 440, 399, 60, 31, 2], [2, "929_1", 483, 420, 60, 31, 2], [2, "702_1", 814, 507, 18, 25, 2], [2, "683_1", 632, 579, 22, 34, 0], [2, "683_1", 639, 583, 22, 34, 0], [2, "683_1", 647, 587, 22, 34, 0], [2, "683_1", 655, 591, 22, 34, 0], [2, "683_1", 663, 595, 22, 34, 0], [2, "1138", 622, 539, 46, 60, 0], [2, "702_1", 616, 556, 18, 25, 0], [2, "1141", 362, 528, 54, 67, 0], [2, "1141", 362, 506, 54, 67, 0], [2, "1141", 319, 550, 54, 67, 0], [2, "1141", 319, 528, 54, 67, 0], [2, "1141", 277, 572, 54, 67, 0], [2, "1141", 277, 550, 54, 67, 0], [2, "1141", 234, 593, 54, 67, 0], [2, "1141", 234, 571, 54, 67, 0], [2, "1141", 192, 613, 54, 67, 0], [2, "1141", 192, 591, 54, 67, 0], [2, "1141", 150, 635, 54, 67, 0], [2, "1141", 150, 613, 54, 67, 0], [2, "1141", 107, 656, 54, 67, 0], [2, "1141", 107, 634, 54, 67, 0], [2, "1141", 65, 676, 54, 67, 0], [2, "1141", 64, 654, 54, 67, 0], [2, "1145", 341, 642, 112, 61, 0], [2, "84_1", 893, 420, 66, 65, 0], [2, "1141", -6, 611, 54, 67, 2], [2, "1141", 22, 621, 54, 67, 2], [2, "1137", 401, 542, 66, 59, 0], [2, "14_5", 471, 526, 32, 30, 0], [2, "14_5", 470, 545, 32, 30, 0], [2, "14_5", 455, 532, 32, 30, 0], [2, "688_1", 364, 431, 46, 24, 2], [2, "688_1", 324, 412, 46, 24, 2], [2, "688_1", 292, 396, 46, 24, 2], [2, "688_1", 252, 400, 46, 24, 0], [2, "688_1", 212, 420, 46, 24, 0], [2, "688_1", 153, 450, 46, 24, 0], [2, "688_1", 113, 470, 46, 24, 0], [2, "688_1", 71, 491, 46, 24, 0], [2, "683_1", 58, 511, 22, 34, 2], [2, "683_1", 66, 515, 22, 34, 2], [2, "683_1", 74, 519, 22, 34, 2], [2, "683_1", 81, 523, 22, 34, 2], [2, "683_1", 89, 527, 22, 34, 2], [2, "683_1", 97, 531, 22, 34, 2], [2, "683_1", 105, 535, 22, 34, 2], [2, "683_1", 113, 539, 22, 34, 2], [2, "683_1", 121, 543, 22, 34, 2], [2, "683_1", 129, 547, 22, 34, 2], [2, "683_1", 136, 551, 22, 34, 2], [2, "683_1", 144, 555, 22, 34, 2], [2, "683_1", 152, 559, 22, 34, 2], [2, "683_1", 160, 563, 22, 34, 2], [2, "367_1", 295, 363, 14, 42, 0], [2, "367_1", 393, 418, 14, 42, 0], [2, "367_1", 67, 481, 14, 42, 0], [2, "367_1", 174, 538, 14, 42, 0], [2, "11_2", 104, 564, 32, 29, 0], [2, "1142", 531, 296, 30, 37, 0], [2, "43_7", 524, 319, 82, 58, 0], [2, "43_7", 549, 290, 82, 58, 0], [2, "43_7", 574, 262, 82, 58, 0], [2, "1142", 484, 322, 30, 37, 0], [2, "1142", 429, 346, 30, 37, 0], [2, "1142", 445, 496, 30, 37, 0], [2, "1142", 392, 521, 30, 37, 0], [2, "1142", 337, 548, 30, 37, 0], [2, "1142", 278, 576, 30, 37, 0], [2, "1142", 497, 473, 30, 37, 0], [2, "1138", 505, 484, 46, 60, 0], [2, "702_1", 500, 503, 18, 25, 0], [2, "702_1", 347, 550, 18, 25, 2], [2, "702_1", 453, 499, 18, 25, 2], [2, "702_1", 494, 327, 18, 25, 2], [2, "523_1", 267, 157, 14, 33, 0], [2, "524_1", 279, 162, 26, 39, 0], [2, "521_1", 260, 104, 34, 42, 0], [2, "522_1", 164, 199, 16, 39, 0], [2, "521_1", 156, 159, 34, 42, 0], [2, "1141", 532, 186, 54, 67, 2], [2, "1142", 516, 187, 30, 37, 2], [2, "702_1", 519, 186, 18, 25, 0], [2, "522_1", 269, 146, 16, 39, 0], [2, "43_6", 212, 170, 82, 58, 2], [2, "43_6", 170, 192, 82, 58, 2], [2, "524_1", 172, 213, 26, 39, 0], [2, "702_1", 326, 116, 18, 25, 2], [2, "1140", 619, -30, 58, 47, 2], [2, "1140", 662, -9, 58, 47, 2], [2, "1140", 861, -1, 58, 47, 2], [2, "1140", 818, 2, 58, 47, 0], [2, "1140", 706, 12, 58, 47, 2], [2, "1140", 904, 20, 58, 47, 2], [2, "1140", 776, 23, 58, 47, 0], [2, "1140", 749, 33, 58, 47, 2], [2, "1140", 948, 41, 58, 47, 2], [2, "1140", 755, 51, 58, 47, 0], [2, "1140", 727, 65, 58, 47, 0], [2, "523_1", 718, 120, 14, 33, 0], [2, "524_1", 730, 125, 26, 39, 0], [2, "521_1", 711, 67, 34, 42, 0], [2, "521_1", 605, 122, 34, 42, 0], [2, "522_1", 720, 109, 16, 39, 0], [2, "43_6", 663, 133, 82, 58, 2], [2, "43_6", 621, 155, 82, 58, 2], [2, "524_1", 623, 176, 26, 39, 0], [2, "522_1", 613, 164, 16, 39, 0], [2, "1141", 571, 167, 54, 67, 0], [2, "11_2", 259, -4, 32, 29, 2], [2, "702_1", 230, -6, 18, 25, 2], [2, "1142", 420, 138, 30, 37, 2], [2, "1142", 374, 117, 30, 37, 2], [2, "702_1", 424, 143, 18, 25, 0], [2, "1145", 841, 295, 112, 61, 2], [2, "11_2", 836, 344, 32, 29, 0], [2, "929_1", -18, 595, 60, 31, 2], [2, "929_1", 27, 617, 60, 31, 2], [2, "14_5", 411, 86, 32, 30, 2], [2, "14_5", 430, 96, 32, 30, 2], [2, "14_5", 423, 77, 32, 30, 2], [2, "954_3", 44, 264, 24, 25, 2], [2, "954_3", 382, 384, 24, 25, 2]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 38, 38, 46, 57, 56, 55, -1, 49, 50, 44, 44, 38, 38, -1, -1, -1, -1, -1, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 11, 11, 17, 16, -1, 10, 11, 11, 12, 56, 38, 38, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 50, 50, 51, -1, 57, 25, 26, 26, 27, 29, 28, -1, 22, 23, 14, 15, 16, 53, 57, 56, 38, 38, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 56, 47, 47, 48, 57, 50, 22, 23, 23, 24, -1, -1, -1, -1, 18, 26, 26, 19, -1, 54, 53, -1, -1, 38, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, 56, 56, 51, 47, 48, 10, 11, 16, 70, 56, -1, 57, 56, 44, -1, -1, 10, 21, 26, 17, 17, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, -1, -1, -1, 56, 56, 56, 56, 56, -1, -1, 13, 14, 19, 70, 53, -1, 44, 51, 57, 56, 56, 13, 21, 20, 30, 29, 33, 15, 17, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 56, -1, -1, 56, 56, 65, 64, 63, -1, -1, 10, 11, 12, -1, -1, -1, 44, 44, 57, 56, 25, 17, 20, 20, -1, -1, -1, 30, 29, 32, 32, 16, -1, -1, 56, 56, 56, -1, -1, -1, -1, -1, -1, -1, 56, 52, 65, 64, 63, -1, 13, 14, 15, -1, -1, 50, 51, 52, 54, 53, 57, 56, 26, 50, 44, -1, -1, -1, 30, 29, 28, 56, 56, 56, 56, 56, 56, -1, -1, -1, -1, -1, -1, -1, 50, 55, -1, 10, 11, 12, 30, 29, 28, 57, 56, 47, 48, -1, 18, 17, 16, 53, 29, 28, 57, 56, 50, 51, 50, 51, 56, 55, 53, 53, 52, -1, -1, -1, 57, 56, -1, -1, -1, -1, 47, 52, -1, 13, 14, 15, -1, -1, -1, 18, 17, 11, 11, 17, 21, 20, 19, -1, -1, -1, 54, 53, 47, 48, 47, 48, 10, 11, 12, 18, 17, 17, 16, -1, 54, 53, 57, 56, -1, -1, -1, 57, 56, -1, 50, 51, -1, 47, -1, 25, 20, 20, 20, 14, 14, 14, 15, 19, -1, -1, 42, 41, 42, 50, 51, 10, 21, 32, 15, 21, 14, 14, 15, 17, 16, -1, 49, 50, -1, 50, 50, 49, 50, 38, 51, 53, 47, 10, 11, 21, 20, 14, 14, 14, 32, 32, 27, 28, -1, 42, 45, 44, 45, 47, 48, 13, 14, 29, 33, 14, 14, 14, 14, 14, 15, 16, 46, 47, 75, 47, 47, 46, 47, 53, 52, -1, 10, 21, 14, 26, 14, 14, 14, 14, 27, 29, 28, -1, 42, 45, 44, 56, -1, -1, -1, 35, 36, -1, 22, 33, 26, 14, 14, 14, 14, 31, -1, 57, 56, -1, -1, 18, 11, 12, -1, -1, 13, 26, 26, 20, 20, 14, 26, 26, 31, -1, -1, 42, 45, 54, 53, 53, -1, -1, -1, 38, 39, 36, -1, 22, 23, 23, 29, 29, 28, -1, -1, 54, 34, 10, 11, 14, 20, 15, 16, -1, 22, 23, 23, 33, 14, 14, 27, 23, 24, 42, 41, 45, 45, -1, -1, -1, 56, -1, -1, -1, 38, 39, -1, 22, 34, 35, 36, 42, 41, 40, 34, 35, 37, 25, 26, 26, 27, 20, 19, -1, 34, 35, 36, 30, 29, 29, 28, -1, 42, 41, 44, -1, 50, 51, 10, 11, 16, 56, 56, -1, -1, -1, -1, -1, 37, 38, 39, 45, 44, 43, 37, 38, 39, 22, 23, 33, 32, 27, 28, -1, 34, 38, 39, 41, 40, 40, -1, 34, 45, 44, -1, -1, 47, 48, 13, 14, 15, 16, 49, 50, 51, 57, 56, 57, 56, 23, 37, 38, 39, 38, 39, -1, -1, 17, 16, 30, 29, 24, -1, -1, 37, 50, 50, 44, 44, 55, -1, 34, 35, 35, 58, 58, 59, 60, 30, 33, 32, 31, 46, 47, 39, 54, 53, 54, 53, 57, 56, 28, 39, 36, -1, 42, 41, 32, 31, 42, 41, 40, -1, 42, 57, 56, 50, 51, 53, 52, -1, 37, 38, 38, 55, 60, -1, 75, 74, 22, 29, 28, -1, -1, -1, -1, -1, 48, -1, 54, 53, -1, 38, 39, 35, 45, 44, 29, 28, 34, 38, 50, 35, 45, 50, 51, 47, 48, -1, 18, 17, 16, 34, 35, 52, 11, 11, 17, 16, -1, 42, -1, -1, -1, -1, -1, -1, -1, -1, 18, 17, 12, -1, -1, 38, 45, 44, -1, -1, 37, 38, 56, 56, 56, 47, 57, 56, 10, 11, 21, 20, 15, 16, 38, 34, 35, 25, 20, 19, 50, -1, -1, -1, 49, 50, 41, 40, -1, -1, 25, 14, 15, 17, 17, 16, -1, 38, 41, 40, 50, 38, 47, 50, 51, -1, 54, 57, 56, 14, 26, 26, 32, 31, 49, 50, 38, 22, -1, -1, 54, 18, 17, 16, 46, 47, 53, 52, -1, -1, 54, 26, 33, 32, 32, 31, 57, 56, 50, 50, 50, 50, 50, 47, 48, 42, 49, 54, 53, 26, 26, 26, 26, 31, 46, 47, 56, 50, 58, 59, 18, 21, 20, 19, -1, -1, -1, 17, 17, 16, -1, -1, 30, 29, 29, 28, 54, 53, 53, 57, 45, 50, 50, 39, 41, 45, 53, 52, 22, 23, 30, 30, 29, 28, -1, -1, 57, 56, 55, -1, 13, 14, 23, 24, -1, -1, -1, 14, 27, 28, 10, 17, 16, -1, -1, 57, 50, 50, -1, 49, 56, 51, 58, 53, 53, 53, 57, 73, 74, 73, -1, -1, 50, 50, 51, 51, 54, 53, 57, 56, 55, 56, 55, 26, 26, -1, -1, 14, 31, 25, 21, 26, 19, 42, 41, 45, 50, 50, -1, 49, 58, 59, 60, 75, 74, 73, 73, 56, 56, -1, -1, 50, 51, 47, 48, 10, 11, 11, 11, 12, 46, 56, 56, 55, -1, -1, 49, 50, 51, 22, 23, 23, 28, 49, 56, 41, 50, 50, 58, 59, 75, 75, 74, 73, 73, -1, -1, -1, -1, -1, 46, 47, 10, 11, 14, 14, 14, 33, 32, 31, 54, 57, 56, 52, -1, -1, 46, 47, 48, -1, 34, 35, 40, 54, 57, 44, 50, 50, 42, 41, 40, 73, -1, -1, -1, -1, -1, -1, -1, -1, 47, 48, 14, 14, 14, 14, 14, 14, 29, 28, 54, 54, 53, 52, -1, -1, 34, 40, 40, -1, 37, 53, 39, 35, 45, 53, 52, 50, 45, 44, 43, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 45, 44, 43, 42, 41, 40, -1, -1, -1, -1, -1, -1, 42, 45, 43, 18, 17, 16, -1, 46, 47, 48, 41, -1, -1, 73, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 37, 45, 44, 43, -1, 34, 35, 36, -1, -1, 45, 44, 43, 25, 26, 16, 16, 37, 38, 45, 44, -1, -1]}, {"type": 3, "obj": [[2, "1303_1", 494, 582, 34, 20, 2], [2, "1304_1", 890, 562, 22, 19, 2], [2, "1303_1", 855, 537, 34, 20, 0], [2, "1302_2", 913, 563, 40, 29, 2], [2, "1302_2", 559, 502, 40, 29, 0], [2, "1302_2", 456, 566, 40, 29, 0], [2, "1303_1", 73, 513, 34, 20, 0], [2, "1302_2", 61, 485, 40, 29, 0], [2, "1303_1", -10, 541, 34, 20, 2], [2, "1303_1", 202, 586, 34, 20, 0], [2, "1303_1", 178, 436, 34, 20, 0], [2, "1302_2", 674, 386, 40, 29, 2], [2, "1303_1", 711, 403, 34, 20, 0], [2, "1303_1", 128, 454, 34, 20, 2], [2, "1303_1", 379, 165, 34, 20, 2], [2, "1302_2", 922, 531, 40, 29, 2], [2, "1302_2", 885, 479, 40, 29, 0], [2, "1303_1", 785, 437, 34, 20, 0], [2, "1303_1", 822, 463, 34, 20, 0], [2, "1302_2", 859, 463, 40, 29, 0], [2, "1303_1", 900, 477, 34, 20, 0], [2, "1302_2", 837, 518, 40, 29, 0], [2, "1302_2", 922, 493, 40, 29, 2], [2, "1303_1", 897, 515, 34, 20, 0], [2, "1303_1", 900, 551, 34, 20, 0]]}, {"type": 2, "data": [83, 83, 83, 83, 83, 83, 83, 83, -1, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, -1, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, -1, -1, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, -1, 8, -1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 83, 83, 83, 83, 83, 83, 83, -1, 83, 83, 83, 83, -1, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 1, 83, 9, 8, 9, 8, 9, 8, 83, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 3, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 2, 3, 2, 7, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 1, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 5, 4, 1, 0, 2, 3, 2, 8, 9, 8, 9, 8, 9, 8, 9, 8, 3, 2, 3, 8, 9, 8, 9, 8, 9, 8, 9, -1, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 0, 1, 7, 6, 3, 2, 3, 7, 6, 7, 7, 8, 9, 8, 9, 8, 3, 0, 1, 5, 4, 7, 0, 8, 9, 8, 9, 8, 9, -1, 9, 8, 9, 8, 9, 8, 9, 8, -1, -1, 2, 3, 0, 0, 1, 1, 1, 2, 0, 5, 7, 2, 3, 2, 3, 2, 3, 2, 3, 5, 4, 5, 2, 3, 4, 8, 9, 8, 9, -1, 9, 8, 9, 8, 9, 8, 5, 4, 4, 0, 1, 0, 1, 2, 0, 1, 3, 0, 1, 7, 6, 5, 0, 1, 2, 3, 4, 6, 4, 7, 6, 5, 4, 7, 6, 7, 6, 8, 4, -1, -1, 8, 9, 8, 5, 4, 4, 6, 6, 2, 0, 2, 3, 3, 2, 3, 0, 1, 0, 1, 0, 7, 2, 3, 7, 7, 6, 7, 6, 5, 5, 4, 0, 1, 4, 0, 1, 7, 6, 0, 1, 0, 1, 0, 7, 6, 6, 2, 3, 1, 2, 3, 0, 1, 3, 0, 2, 3, 2, 3, 2, 0, 0, 1, 1, 5, 4, 5, 4, 7, 7, 6, 2, 3, 6, 2, 3, 7, -1, 5, 4, 2, 3, 2, 3, 2, 3, 1, 2, 0, 1, 1, 5, 4, 5, 4, 0, 1, 3, 1, 0, 2, 2, 3, 3, 7, 6, 7, 5, 4, 0, 1, 5, 4, 0, 1, 6, 5, 4, 7, 6, 0, 1, 5, 4, 2, 0, 1, 0, 2, 3, 3, 7, 6, 7, 6, 2, 3, 2, 3, 2, 3, 0, 1, 1, 7, 6, 6, 7, 6, 2, 3, 0, 1, 2, 3, 4, 7, 6, 5, 4, 4, 5, 7, 0, 1, 2, 3, 2, 0, 1, 5, 4, 1, 3, 2, 3, 0, 1, 1, 0, 1, 0, 1, 0, 1, 5, 4, 7, 6, 5, 4, 2, 3, 4, 7, 6, 4, 4, 7, 5, 4, 7, 6, 2, 3, 4, 0, 5, 4, 3, 7, 6, 3, 2, 3, 2, 2, 3, 3, 2, 3, 2, 3, 2, 3, 7, 6, 5, 4, 7, 6, 7, 7, 6, 4, 5, 4, 6, 5, 7, 6, 7, 5, 4, 7, 6, 2, 7, 6, 5, 4, 2, 3, 0, 1, 0, 0, 0, 1, 2, 3, 0, 1, 2, 3, 1, 0, 0, 1, 5, 4, 5, 4, 7, 6, 7, 6, 4, 7, 6, 5, 4, 7, 0, 1, 0, 1, 0, 0, 8, 9, 8, 9, 2, 3, 2, 2, 2, 3, 0, 1, 2, 3, 0, 1, 3, 2, 2, 3, 7, 6, 6, 6, 5, 4, 5, 5, 4, 5, 4, 7, 6, 5, 2, 3, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 2, 0, 1, 0, 2, 3, 0, 2, 2, 3, 5, 4, 5, 4, 5, 0, 0, 0, 1, 0, 1, 7, 6, 5, 4, 5, 4, 7, 6, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 0, 1, 0, 1, 2, 0, 1, 2, 3, 5, 4, 7, 6, 5, 4, 7, 2, 2, 2, 3, 0, 1, 0, 1, 7, 6, 7, 6, 0, 8, 9, 8, 9, 8, 9, 8, 8, 8, 9, 8, 2, 3, 2, 3, 2, 2, 3, 5, 4, 5, 4, 7, 5, 4, 6, 7, 6, 4, 6, 4, 2, 3, 0, 1, 4, 6, 5, 0, 9, 8, 9, 8, 9, 8, 9, 8, 2, 3, 2, 3, 0, 1, -1, -1, -1, 5, 4, 7, 6, 7, 5, 4, 7, 6, 7, 6, 7, 6, 6, 6, 7, 6, 2, 3, 6, 4, 0, 8, 9, 8, 9, 8, 9, 8, 9, 8, 2, 3, 2, 3, 2, 3, 4, 5, 4, 7, 6, 7, 5, 4, 7, 6, 5, 4, 5, 4, 5, 4, 5, 5, 7, 6, 5, 4, 0, 1, 2, 8, 9, 8, 9, 8, 9, 8, 9, 3, 0, 0, 1, 5, 4, 5, 4, 7, 6, 5, 5, 4, 7, 6, 7, 6, 7, 6, 5, 4, 7, 6, 7, 7, 6, 5, 4, 6, 2, 3, 2, 8, 9, 8, 9, 3, 2, 9, 2, 3, 2, 2, 3, 7, 6, 7, 6, 6, 7, 7, 5, 4, 5, 5, 4, 4, 4, 5, 7, 6, 5, 5, 4, 5, 4, 7, 6, 5, 2, 3, 2, 8, 9, 2, 0, 1, 0, 5, 4, 0, 1, 4, 6, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 7, 6, 6, 6, 7, 6, 7, 7, 7, 6, 5, 4, 7, 6, 7, 0, 1, 0, 8, 5, 4, 2, 3, 2, 7, 6, 0, 1, 6, 5, 4, 5, 4, 7, 6, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 7, 6, 7, 6, 4, 5, 4, 2, 3, 2, 3, 7, 5, 5, 4, 83, 83, 7, 2, 3, 6, 7, 5, 4, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 5, 4, 5, 4, 5, 4, 7, 6, 0, 1, 0, 0, 1, 7, 83, 83, 83, 83, 83, 2, 3, 0, 1, 7, 6, 4, 5, 4, 5, 4, 5, 4, 5, 5, 4, 5, 5, 4, 5, 4, 7, 6, 5, 4, 7, 6, 5, 4, 2, 3, 2, 2, 83, 83, 83, 83, 83, 83, 83, 83, 83, 2, 3, -1, 7, 6, 7, 6, 7, 6, 7, 6, 7, 7, 6, 7, 7, 6, 7, 6, 7, 6, 7, 5, 4, -1, 7, 6]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1]}