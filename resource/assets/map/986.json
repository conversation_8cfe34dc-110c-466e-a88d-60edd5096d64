{"mW": 720, "mH": 720, "tW": 24, "tH": 24, "tiles": [["384", 0, 1, 3], ["384", 2, 1, 3], ["497", 0, 2, 1], ["203_9", 0, 2, 1], ["203_10", 0, 2, 1], ["203_11", 0, 2, 1], ["203_12", 0, 2, 1], ["203_12", 1, 2, 1], ["203_11", 1, 2, 1], ["203_10", 1, 2, 1], ["203_9", 1, 2, 1], ["381", 0, 3, 2], ["382", 0, 1, 3], ["383", 0, 2, 3], ["384", 0, 1, 3], ["384", 2, 1, 3], ["383", 2, 2, 3], ["382", 2, 1, 3], ["381", 2, 3, 2], ["369", 0, 3, 3], ["369", 2, 3, 3], ["369", 1, 3, 3], ["369", 3, 3, 3], ["315", 0, 3, 3], ["91", 0, 3, 2], ["91", 2, 3, 2], ["91", 1, 3, 2], ["91", 3, 3, 2], ["1134", 0, 3, 2], ["1134", 2, 3, 2], ["1134", 1, 3, 2], ["1134", 3, 3, 2]], "layers": [{"type": 3, "obj": [[2, "517", 504, 24, 16, 16, 0], [2, "525", 515, 58, 12, 26, 0], [2, "516", 523, 59, 58, 52, 0], [2, "525", 529, 44, 12, 26, 0], [2, "525", 560, 59, 12, 26, 0], [2, "515", 590, 94, 12, 23, 0], [2, "569", 606, 176, 34, 13, 0], [2, "570", 607, 155, 34, 21, 0], [2, "571", 607, 137, 34, 18, 0]]}, {"type": 4, "obj": [[2, "518", 504, 40, 16, 108, 0], [2, "518", 581, 81, 16, 108, 0], [2, "986", 590, 164, 32, 61, 0], [2, "986", 622, 164, 32, 61, 2]]}, {"type": 3, "obj": [[2, "385", 544, 38, 72, 48, 3], [2, "381", 669, 32, 72, 48, 2], [2, "382", 646, 7, 24, 72, 2], [2, "383", 598, 7, 48, 72, 2], [2, "384", 574, 9, 24, 72, 2], [2, "385", 430, 262, 72, 48, 2], [2, "381", 296, 112, 72, 48, 0], [2, "382", 368, 86, 24, 72, 0], [2, "383", 392, 85, 48, 72, 0], [2, "384", 439, 88, 24, 72, 0], [2, "381", 141, 309, 72, 48, 2], [2, "382", 118, 284, 24, 72, 2], [2, "383", 70, 284, 48, 72, 2], [2, "384", 46, 286, 24, 72, 2], [2, "381", 235, 227, 72, 48, 0], [2, "382", 307, 201, 24, 72, 0], [2, "383", 331, 200, 48, 72, 0], [2, "384", 378, 203, 24, 72, 0], [2, "381", 270, 373, 72, 48, 2], [2, "382", 247, 348, 24, 72, 2], [2, "383", 199, 348, 48, 72, 2], [2, "384", 175, 350, 24, 72, 2], [2, "548", 9, 430, 30, 24, 0], [2, "989", 407, 250, 36, 18, 0], [2, "989", 423, 258, 36, 18, 0], [2, "989", 439, 266, 36, 18, 0], [2, "989", 455, 274, 36, 18, 0], [2, "989", 471, 282, 36, 18, 0], [2, "549", 194, 398, 34, 28, 2], [2, "549", 186, 410, 34, 28, 2], [2, "384", 519, 490, 24, 72, 0], [2, "322", 522, 549, 52, 101, 2], [2, "385", 302, 469, 72, 48, 0], [2, "381", 447, 498, 72, 48, 0], [2, "385", 437, 481, 72, 48, 2], [2, "385", 388, 518, 72, 48, 1], [2, "385", 425, 515, 72, 48, 3], [2, "383", 388, 477, 48, 72, 0], [2, "552", 493, 555, 22, 15, 0], [2, "381", 454, 510, 72, 48, 2], [2, "382", 431, 490, 24, 72, 2], [2, "365", 452, 469, 48, 94, 0], [2, "366", 415, 515, 32, 48, 0], [2, "385", 461, 517, 72, 48, 0], [2, "328", 436, 544, 32, 29, 0], [2, "329", 483, 526, 42, 37, 0], [2, "326", 503, 565, 18, 14, 0], [2, "423", 443, 583, 4, 6, 0], [2, "383", 343, 476, 48, 72, 2], [2, "324", 338, 532, 70, 35, 2], [2, "328", 385, 536, 32, 29, 0], [2, "21", 357, 527, 28, 24, 0], [2, "385", 294, 486, 72, 48, 0], [2, "331", 491, 471, 104, 108, 0], [2, "326", 469, 567, 18, 14, 2], [2, "327", 512, 582, 30, 22, 2], [2, "174_2", 414, 580, 68, 33, 0], [2, "381", 272, 495, 72, 48, 0], [2, "989", 644, 50, 36, 18, 2], [2, "989", 628, 58, 36, 18, 2], [2, "989", 612, 66, 36, 18, 2], [2, "989", 596, 74, 36, 18, 2], [2, "989", 450, 147, 36, 18, 0], [2, "989", 466, 155, 36, 18, 0], [2, "989", 482, 163, 36, 18, 0], [2, "989", 498, 171, 36, 18, 0], [2, "989", 515, 179, 36, 18, 0], [2, "989", 531, 187, 36, 18, 0], [2, "989", 547, 195, 36, 18, 0], [2, "989", 563, 203, 36, 18, 0], [2, "989", 558, 201, 36, 18, 0], [2, "989", 574, 209, 36, 18, 0], [2, "989", 590, 217, 36, 18, 0], [2, "989", 606, 225, 36, 18, 0], [2, "989", 579, 82, 36, 18, 2], [2, "989", 563, 90, 36, 18, 2], [2, "989", 547, 98, 36, 18, 2], [2, "989", 531, 106, 36, 18, 2], [2, "989", 515, 114, 36, 18, 2], [2, "989", 499, 122, 36, 18, 2], [2, "989", 483, 130, 36, 18, 2], [2, "989", 467, 138, 36, 18, 2], [2, "989", 687, 185, 36, 18, 2], [2, "989", 671, 193, 36, 18, 2], [2, "989", 655, 201, 36, 18, 2], [2, "989", 639, 209, 36, 18, 2], [2, "989", 623, 217, 36, 18, 2], [2, "796", 423, 156, 36, 47, 2], [2, "796", 453, 171, 36, 47, 2], [2, "796", 485, 187, 36, 47, 2], [2, "796", 521, 205, 36, 47, 2], [2, "796", 553, 221, 36, 47, 2], [2, "796", 589, 239, 36, 47, 2], [2, "796", 681, 210, 36, 47, 0], [2, "796", 646, 227, 36, 47, 0], [2, "796", 625, 238, 36, 47, 0], [2, "797", 422, 148, 48, 26, 2], [2, "797", 455, 165, 48, 26, 2], [2, "797", 489, 182, 48, 26, 2], [2, "797", 522, 199, 48, 26, 2], [2, "797", 438, 130, 48, 26, 0], [2, "797", 556, 216, 48, 26, 2], [2, "797", 659, 208, 48, 26, 0], [2, "797", 625, 225, 48, 26, 0], [2, "797", 589, 232, 48, 26, 2], [2, "797", 574, 62, 48, 26, 0], [2, "797", 540, 79, 48, 26, 0], [2, "797", 506, 96, 48, 26, 0], [2, "797", 472, 113, 48, 26, 0], [2, "797", 640, 38, 48, 26, 2], [2, "797", 673, 55, 48, 26, 2], [2, "797", 608, 45, 48, 26, 0], [2, "506", 470, 182, 20, 45, 2], [2, "506", 461, 186, 20, 45, 2], [2, "507", 421, 192, 48, 58, 2], [2, "508", 476, 187, 60, 37, 2], [2, "508", 511, 204, 60, 37, 2], [2, "508", 465, 197, 60, 37, 2], [2, "508", 501, 215, 60, 37, 2], [2, "508", 456, 208, 60, 37, 2], [2, "508", 492, 226, 60, 37, 2], [2, "508", 447, 219, 60, 37, 2], [2, "508", 483, 237, 60, 37, 2], [2, "508", 439, 230, 60, 37, 2], [2, "508", 473, 248, 60, 37, 2], [2, "508", 429, 240, 60, 37, 2], [2, "508", 462, 256, 60, 37, 2], [2, "506", 554, 226, 20, 45, 2], [2, "506", 545, 230, 20, 45, 2], [2, "507", 505, 236, 48, 58, 2], [2, "384", 614, 241, 24, 72, 0], [2, "381", 471, 266, 72, 48, 0], [2, "382", 543, 240, 24, 72, 0], [2, "383", 567, 239, 48, 72, 0], [2, "385", -7, 117, 72, 48, 0], [2, "381", 22, 167, 72, 48, 2], [2, "382", -1, 143, 24, 72, 2], [2, "385", 24, 137, 72, 48, 2], [2, "385", 56, 182, 72, 48, 0], [2, "385", 263, 173, 72, 48, 0], [2, "385", 339, 334, 72, 48, 2], [2, "381", 648, 631, 72, 48, 2], [2, "382", 625, 606, 24, 72, 2], [2, "383", 577, 606, 48, 72, 2], [2, "385", 597, 388, 72, 48, 2], [2, "381", 578, 249, 72, 48, 0], [2, "382", 650, 223, 24, 72, 0], [2, "383", 674, 222, 48, 72, 0], [2, "384", 721, 225, 24, 72, 0], [2, "381", 648, 288, 72, 48, 2], [2, "382", 625, 263, 24, 72, 2], [2, "383", 577, 263, 48, 72, 2], [2, "384", 553, 265, 24, 72, 2], [2, "381", 420, 307, 72, 48, 0], [2, "382", 492, 281, 24, 72, 0], [2, "383", 516, 280, 48, 72, 0], [2, "384", 563, 283, 24, 72, 0], [2, "381", 404, 353, 72, 48, 0], [2, "382", 476, 327, 24, 72, 0], [2, "383", 500, 326, 48, 72, 0], [2, "384", 547, 329, 24, 72, 0], [2, "385", 575, 228, 72, 48, 0], [2, "385", 647, 296, 72, 48, 0], [2, "385", 528, 379, 72, 48, 0], [2, "385", 506, 368, 72, 48, 0], [2, "385", 383, 410, 72, 48, 0], [2, "385", 388, 327, 72, 48, 0], [2, "385", 548, 427, 72, 48, 1], [2, "385", 85, 266, 72, 48, 3], [2, "178_1", 636, 102, 70, 37, 0], [2, "178_1", 570, 103, 70, 37, 2], [2, "178_1", 637, 139, 70, 37, 1], [2, "178_1", 571, 140, 70, 37, 3], [2, "322", 266, 443, 52, 101, 2], [2, "553", 247, 537, 14, 8, 0], [2, "552", 242, 519, 22, 15, 0], [2, "549", 222, 417, 34, 28, 2], [2, "174_2", 12, 396, 68, 33, 0], [2, "549", 212, 428, 34, 28, 2], [2, "549", 248, 434, 34, 28, 2], [2, "327", 261, 440, 30, 22, 0], [2, "324", -12, 325, 70, 35, 2], [2, "324", 68, 339, 70, 35, 2], [2, "326", 181, 394, 18, 14, 0], [2, "552", 169, 406, 22, 15, 0], [2, "553", 159, 396, 14, 8, 0], [2, "385", 392, 167, 72, 48, 2], [2, "331", -26, 264, 104, 108, 2], [2, "552", 121, 361, 22, 15, 0], [2, "552", -2, 447, 22, 15, 0], [2, "385", 668, 647, 72, 48, 0], [2, "544", 124, 464, 86, 107, 0], [2, "544", 40, 464, 86, 107, 2], [2, "550", -1, 523, 42, 28, 2], [2, "548", 209, 493, 30, 24, 0], [2, "549", 188, 468, 34, 28, 2], [2, "548", 23, 485, 30, 24, 0], [2, "551", 158, 451, 30, 21, 0], [2, "548", 131, 445, 30, 24, 0], [2, "549", 93, 436, 34, 28, 2], [2, "548", 34, 433, 30, 24, 0], [2, "550", 38, 454, 42, 28, 0], [2, "548", 200, 526, 30, 24, 0], [2, "547", 135, 532, 64, 35, 0], [2, "547", 50, 533, 64, 35, 2], [2, "550", 156, 554, 42, 28, 2], [2, "549", 190, 543, 34, 28, 2], [2, "553", 27, 477, 14, 8, 0], [2, "553", 208, 518, 14, 8, 0], [2, "549", 136, 570, 34, 28, 0], [2, "548", 97, 570, 30, 24, 0], [2, "550", 63, 560, 42, 28, 0], [2, "552", 136, 564, 22, 15, 0], [2, "553", 125, 567, 14, 8, 0], [2, "553", 134, 576, 14, 8, 0], [2, "552", 115, 591, 22, 15, 0], [2, "553", 222, 544, 14, 8, 0], [2, "549", 182, 567, 34, 28, 0], [2, "553", 168, 587, 14, 8, 0], [2, "548", 1, 499, 30, 24, 0], [2, "553", 25, 518, 14, 8, 0], [2, "549", 20, 541, 34, 28, 0], [2, "548", 42, 554, 30, 24, 0], [2, "549", 31, 566, 34, 28, 2], [2, "553", 88, 582, 14, 8, 0], [2, "552", 150, 426, 22, 15, 0], [2, "549", 8, 448, 34, 28, 0], [2, "552", 5, 487, 22, 15, 0], [2, "548", 209, 457, 30, 24, 0], [2, "552", 193, 451, 22, 15, 0], [2, "420", 170, 576, 16, 13, 2], [2, "325", 62, 432, 50, 37, 2], [2, "327", 83, 434, 30, 22, 2], [2, "422", 66, 428, 16, 14, 2], [2, "178_2", 124, 476, 70, 37, 0], [2, "178_2", 56, 476, 70, 37, 2], [2, "178_2", 56, 513, 70, 37, 3], [2, "178_2", 124, 513, 70, 37, 1], [2, "548", 550, 623, 30, 24, 0], [2, "550", -26, 470, 42, 28, 0], [2, "517", 581, 66, 16, 16, 0], [2, "523", 521, 146, 14, 33, 2], [2, "520", 502, 147, 20, 37, 0], [2, "524", 486, 153, 26, 39, 2], [2, "523", 598, 183, 14, 33, 2], [2, "520", 579, 184, 20, 37, 0], [2, "524", 561, 190, 26, 39, 2], [2, "523", 598, 183, 14, 33, 2], [2, "552", 329, 531, 22, 15, 0], [2, "85_2", 278, 502, 48, 53, 0], [2, "327", 306, 536, 30, 22, 0], [2, "552", 563, 640, 22, 15, 0], [2, "548", 135, 363, 30, 24, 0], [2, "552", 148, 380, 22, 15, 0], [2, "328", 161, 371, 32, 29, 0], [2, "327", 67, 340, 30, 22, 0], [2, "552", 96, 347, 22, 15, 0], [2, "683", 609, 205, 22, 34, 0], [2, "683", 619, 210, 22, 34, 0], [2, "683", 630, 215, 22, 34, 0], [2, "683", 600, 209, 22, 34, 0], [2, "683", 610, 214, 22, 34, 0], [2, "683", 621, 219, 22, 34, 0], [2, "683", 592, 213, 22, 34, 0], [2, "683", 602, 218, 22, 34, 0], [2, "683", 613, 223, 22, 34, 0], [2, "683", 441, 121, 22, 34, 0], [2, "683", 451, 126, 22, 34, 0], [2, "683", 462, 131, 22, 34, 0], [2, "683", 432, 125, 22, 34, 0], [2, "683", 442, 130, 22, 34, 0], [2, "683", 453, 135, 22, 34, 0], [2, "683", 424, 129, 22, 34, 0], [2, "683", 434, 134, 22, 34, 0], [2, "683", 445, 139, 22, 34, 0], [2, "986", 454, 81, 32, 61, 2], [2, "986", 422, 81, 32, 61, 0], [2, "569", 435, 92, 34, 13, 0], [2, "570", 436, 71, 34, 21, 0], [2, "571", 436, 53, 34, 18, 0], [2, "385", 616, 259, 72, 48, 0], [2, "395", 516, 256, 42, 72, 0], [2, "385", 525, 245, 72, 48, 0], [2, "395", 382, 184, 42, 72, 2], [2, "385", 372, 228, 72, 48, 0], [2, "385", 657, 472, 72, 48, 0], [2, "549", 201, 578, 34, 28, 0], [2, "553", 223, 568, 14, 8, 0], [2, "552", 154, 593, 22, 15, 0], [2, "385", 344, 233, 72, 48, 0], [2, "385", 389, 259, 72, 48, 0], [2, "381", 319, 257, 72, 48, 2], [2, "382", 296, 232, 24, 72, 2], [2, "383", 248, 232, 48, 72, 2], [2, "384", 224, 234, 24, 72, 2], [2, "381", 166, 249, 72, 48, 0], [2, "385", 243, 274, 72, 48, 0], [2, "385", 201, 239, 72, 48, 0], [2, "385", 354, 131, 72, 48, 0], [2, "385", 163, 309, 72, 48, 2], [2, "328", 528, 697, 32, 29, 0], [2, "385", 475, 228, 72, 48, 0], [2, "385", 94, 59, 72, 48, 2], [2, "174_3", 330, 560, 68, 33, 0], [2, "174_3", 459, 688, 68, 33, 2], [2, "174_3", 62, 374, 68, 33, 0], [2, "420", 493, 691, 16, 13, 0], [2, "553", 526, 672, 14, 8, 0], [2, "548", 489, 659, 30, 24, 0], [2, "552", 506, 682, 22, 15, 0], [2, "420", 517, 665, 16, 13, 0], [2, "422", 496, 670, 16, 14, 2], [2, "326", 528, 675, 18, 14, 0], [2, "420", 4, 705, 16, 13, 2], [2, "553", 37, 686, 14, 8, 2], [2, "548", 0, 673, 30, 24, 2], [2, "552", 17, 696, 22, 15, 2], [2, "420", 28, 679, 16, 13, 2], [2, "422", 7, 684, 16, 14, 0], [2, "326", 39, 689, 18, 14, 2], [2, "174_2", 55, 686, 68, 33, 0], [2, "422", 113, 347, 16, 14, 2], [2, "327", 89, 347, 30, 22, 2], [2, "328", 49, 351, 32, 29, 0], [2, "21", 118, 696, 28, 24, 0], [2, "333", 457, 483, 38, 35, 2], [2, "423", 584, 507, 4, 6, 0], [2, "334", 498, 492, 32, 40, 4], [2, "334", 529, 436, 40, 32, 0], [2, "333", 557, 435, 38, 35, 3], [2, "334", 585, 474, 40, 32, 0], [2, "333", 553, 479, 38, 35, 0], [2, "334", 517, 453, 40, 32, 0], [2, "333", 559, 453, 38, 35, 2], [2, "332", 500, 418, 36, 38, 0], [2, "332", 546, 426, 36, 38, 0], [2, "334", 571, 489, 40, 32, 0], [2, "334", 579, 506, 40, 32, 0], [2, "334", 601, 517, 40, 32, 1], [2, "332", 573, 504, 36, 38, 0], [2, "334", 520, 501, 40, 32, 2], [2, "332", 489, 450, 36, 38, 0], [2, "332", 476, 501, 36, 38, 0], [2, "333", 11, 218, 38, 35, 3], [2, "334", -16, 261, 32, 40, 4], [2, "334", 71, 243, 40, 32, 0], [2, "333", 39, 248, 38, 35, 0], [2, "334", 57, 258, 40, 32, 0], [2, "334", 65, 275, 40, 32, 0], [2, "334", 73, 289, 40, 32, 1], [2, "332", 59, 273, 36, 38, 0], [2, "334", 6, 270, 40, 32, 2], [2, "333", -56, 286, 38, 35, 2], [2, "332", -38, 270, 36, 38, 0], [2, "334", -17, 219, 40, 32, 0], [2, "334", -31, 238, 40, 32, 0], [2, "333", 12, 234, 38, 35, 2], [2, "332", 43, 233, 36, 38, 0], [2, "332", 0, 209, 36, 38, 0], [2, "525", 497, 56, 12, 26, 0], [2, "328", 160, 641, 32, 29, 0], [2, "420", 13, 570, 16, 13, 2], [2, "420", 345, 638, 16, 13, 2], [2, "326", 260, 469, 18, 14, 0], [2, "326", 258, 675, 18, 14, 0], [2, "420", 274, 684, 16, 13, 2], [2, "383", 591, 640, 48, 72, 0], [2, "384", 638, 643, 24, 72, 0], [2, "371", 583, 661, 26, 48, 2], [2, "319", 550, 685, 62, 65, 0], [2, "326", 575, 663, 18, 14, 0], [2, "328", 571, 639, 32, 29, 0], [2, "385", 624, 670, 72, 48, 0], [2, "796", 715, 194, 36, 47, 0], [2, "797", 691, 192, 48, 26, 0], [2, "989", 706, 175, 36, 18, 2], [2, "989", 654, 63, 36, 18, 0], [2, "989", 671, 72, 36, 18, 0], [2, "797", 706, 72, 48, 26, 2], [2, "989", 688, 80, 36, 18, 0], [2, "989", 705, 89, 36, 18, 0], [2, "385", 657, 1, 72, 48, 0], [2, "1437", 260, 368, 24, 27, 0], [2, "1436", 296, 388, 30, 21, 0], [2, "1436", 298, 332, 30, 21, 0], [2, "1437", 327, 310, 24, 27, 0], [2, "1437", 359, 290, 24, 27, 0], [2, "1436", 396, 311, 30, 21, 0], [2, "1437", 349, 335, 24, 27, 0], [2, "1437", 421, 296, 24, 27, 0], [2, "1438", 395, 263, 28, 30, 0], [2, "1438", 265, 402, 28, 30, 0], [2, "1438", 322, 354, 28, 30, 0], [2, "1438", 231, 385, 28, 30, 0], [2, "1438", 454, 275, 28, 30, 0], [2, "1437", 427, 256, 24, 27, 0], [2, "1438", 373, 324, 28, 30, 0], [2, "1438", 285, 352, 28, 30, 0], [2, "1438", 380, 291, 28, 30, 0]]}, {"type": 2, "data": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 105, 106, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 117, 118, 124, 123, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -127, -126, -125, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -124, -123, -122, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -107, -107, -108, -109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -119, -120, -121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -116, -117, -118, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -107, -108, -109, 105, 124, 123, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 125, 124, 123, -1, -1, 125, 124, 123, 120, 121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -127, -126, -126, -121, 117, 118, -128, -1, -1, -1, -1, -1, -1, -1, -1, -119, -120, -121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -124, -123, -123, -118, -1, -1, 125, 124, -1, -1, -1, -1, -1, -1, -1, -116, -117, -118, -1, -1, -1, -1, 70, 69, -1, -1, -1, -1, -127, -126, -123, -123, -123, -122, -121, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -108, -108, -109, -1, -1, -1, -1, 73, -127, -126, -125, -1, -1, -115, -104, -105, -105, -105, -105, -118, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -108, -1, -1, -1, -1, -1, -1, -1, -124, -123, -122, -120, -121, -1, -107, -108, -108, -108, -108, -109, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -107, -108, -108, -114, -113, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]}, {"type": 2, "data": [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 6, 10, 10, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 7, 6, 7, 6, 7, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 6, 6, 7, 7, 6, 6, 7, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 12, 6, 6, 7, 6, 7, 6, 7, 6, 7, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 6, 7, 7, 6, 7, 6, 7, 6, 7, 6, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, -1, 6, 7, 6, 7, 6, 7, 6, 6, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 17, 17, 17, 6, 7, 7, 7, 6, 6, 7, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 11, 11, 11, 11, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 11, 11, 13, 13, 13, 13, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 13, 15, 15, 15, 15, 15, 15, 91, 91, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 9, 9, 10, 10, 17, 17, 17, 17, 17, 17, 17, 17, 73, 73, 73, 73, 91, 91, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 19, 19, 19, 19, 19, 19, 19, 73, 73, 64, 73, 73, 64, 64, 64, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 64, 73, 73, 64, 64, 64, 82, 83, 73, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 73, 73, 73, 83, 79, 79, 79, 80, 78, 92, 92, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 17, 83, 79, 79, 80, 97, 98, 97, 98, 71, 70, 74, 64, 16, 16, 16, 16, 14, 14, 14, 16, 16, 16, 16, 14, 14, 14, 14, 14, 14, 14, 72, 97, 100, 101, 100, 101, 101, 60, 74, 73, 73, 64, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 65, 69, 60, 70, 69, 98, 104, 63, 64, 64, 73, 73, -1, -1, 18, -1, -1, -1, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 91, 65, 74, 73, 72, 96, 96, 78, 92, 64, 64, 73, 73, 64, 64, 64, 64, -1, -1, -1, -1, 21, 21, 21, 21, 21, 21, 21, 21, 21, 73, 64, 73, 83, 80, 99, 99, 96, 78, 79, 92, 73, 73, 73, 64, 64, 64, 64, 64, 64, 64, 64, 23, 23, 23, 23, 23, 23, 23, 23, 64, 64, 64, 72, 104, 102, 102, 99, 99, 100, 78, 79, 92, 73, 73, 73, 64, 64, 64, 64, 64, 64, 64, 22, 22, 22, 22, 22, 22, 22, 64, 64, 64, 72, 98, 100, 101, 102, 102, 103, 104, 98, 81, 73, 73, 73, 73, 73, 73, 64, 64, 64, 64, 9, 9, 9, 9, 9, 9, 9, 64, 64, 83, 75, 101, 71, 70, 69, 102, 103, 104, 98, 78, 79, 92, 73, 83, 79, 92, 64, 64, 64, 64, 64, 10, 10, 10, 10, 10, 10, 64, 64, 72, 101, 104, 63, 73, 65, 65, 70, 70, 69, 104, 99, 78, 79, 80, 97, 81, 64, 64, 64, 64, 64, 16, 11, 11, 11, 11, 11, 64, 64, 65, 61, 61, 74, 73, 73, 73, 73, 73, 65, 69, 102, 103, 104, 96, 97, 63, 64, 64, 64, 64, 64, 64, 21, 21, 21, 21, 21, 64, 64, 64, 64, 64, 73, 73, 73, 73, 73, 73, 73, 65, 61, 62, 97, 97, 71, 74, 64, 64, 64, 64, 64, 64, 20, 20, 20, 20, 20]}], "blocks": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1]}