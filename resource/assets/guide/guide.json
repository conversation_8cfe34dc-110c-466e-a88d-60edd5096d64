[{"id": 100101, "triggerType": [[5, 1101], [1, 1, 1], [7, 1111, 1100]], "completeType": [[1, 1101]], "spriteId": 1101, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  嗯？/c82ac66{1}/p你终于醒了，前些天你被卷入世界漩涡之中，大伙都担心你醒不来了。", "completeIds": [100101], "layer": 1, "groupId": 10001, "minGroupId": 1, "guideType": 5, "isReplaceName": 1}, {"id": 100102, "triggerType": [[5, 1102], [10, 100101]], "completeType": [[1, 1102]], "spriteId": 1102, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "？你说你不知道我是谁？我是/cd05959“琪琪”/p啊，看来是在世界漩涡中把脑袋撞坏了，连我这种貌美如花的美少女都忘了。哎~", "completeIds": [100102], "layer": 1, "groupId": 10001, "minGroupId": 1, "guideType": 5}, {"id": 100103, "triggerType": [[5, 1103], [10, 100102]], "completeType": [[1, 1103]], "spriteId": 1103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  好了，既然醒了就去找村长报告一下吧，他也很担心你。/c82ac66（请点击屏幕村长NPC位置，进行移动对话）/p", "completeIds": [100103], "layer": 1, "isEffect": 1, "groupId": 10001, "minGroupId": 1, "param": [[1, 0, -20, 40, 50]], "isSameSize": 1, "npcData": "世界村村长"}, {"id": 100104, "triggerType": [[5, 1104], [10, 100103]], "completeType": [[1, 1104]], "spriteId": 1104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 980, 1], "talkContent": "  ？？/c82ac66村长/p头上怎么有个/cc79b55\"！\"/p,他可能是有什么事要我们帮忙，快和他对话吧！", "completeIds": [100104], "layer": 3, "groupId": 10001, "minGroupId": 1, "guideType": 4, "mutexType": 1}, {"id": 100105, "triggerType": [[5, 1105], [10, 100104]], "completeType": [[1, 1105]], "spriteId": 1105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  请点击/c82ac66“接受任务”/p按键，开始接受村长的委托", "completeIds": [100105], "layer": 3, "isEffect": 1, "groupId": 10001, "minGroupId": 1, "effectPos": [0, 15], "mutexType": 1}, {"id": 100106, "triggerType": [[5, 1106], [10, 100105]], "completeType": [[1, 1106]], "spriteId": 1106, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  接受村长委托后，我们的任务目标头上会显示/cc79b55“？”/p提示您可提交。看~/c82ac66云裳/p头上已有问号了，快点击她寻路过去和她对话吧", "completeIds": [100106], "layer": 1, "isEffect": 1, "groupId": 10001, "minGroupId": 1, "param": [[1, -40, -95, 70, 110]], "effectPos": [10, 30], "isSameSize": 1, "mutexType": 3}, {"id": 100107, "triggerType": [[5, 1107], [10, 100106]], "completeType": [[1, 1107]], "spriteId": 1107, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  你好，云裳。看来她等我们很久了，请点击/c82ac66“完成任务”/p的按键来完成村长给我们的委托吧！", "completeIds": [100107], "layer": 3, "isEffect": 1, "groupId": 10001, "minGroupId": 1, "effectPos": [0, 15], "mutexType": 1}, {"id": 100108, "triggerType": [[5, 1108], [10, 100107]], "completeType": [[9, 0, 0, "云裳"], [1, 1108]], "spriteId": 1108, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  任务的寻路与接取已经为您介绍完毕，是否有帮助到您呢？接下来请用刚学到的技巧与村子里的各位打打招呼吧，在你昏迷期间他们可没为你少操心啊。", "completeIds": [100108], "layer": 1, "groupId": 10001, "minGroupId": 1, "guideType": 5, "isEnd": 1}, {"id": 200101, "triggerType": [[5, 2101], [13, 260]], "completeType": [[1, 2101]], "spriteId": 2101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  嗯？我怎么才走开一会，东海就叫你来教训这哈士奇，他就不能对大病初愈的人温柔点吗。没办法，来都来了，干碎它吧！/c82ac66（接下来将为您介绍战斗功能）/p", "layer": 1, "groupId": 10002, "minGroupId": 2, "guideType": 5, "isBattle": 1}, {"id": 200102, "triggerType": [[5, 2102], [10, 200101]], "completeType": [[1, 2102]], "spriteId": 2102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  我们的攻击可分为普通攻击和技能攻击，在没有任何主动技能的情况下，我们只能通过普通攻击攻击对手，但普通攻击造成的伤害很低", "layer": 1, "groupId": 10002, "minGroupId": 2, "guideType": 1, "isBattle": 1}, {"id": 200103, "triggerType": [[5, 2103], [10, 200102]], "completeType": [[1, 2103]], "spriteId": 2103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262, 1], "talkContent": "  如果你已经学习了主动技能，可以点击/cd05959“技能”/p图标，释放它们。", "layer": 1, "groupId": 10002, "minGroupId": 2, "guideType": 2, "isBattle": 1}, {"id": 200104, "triggerType": [[5, 2104], [10, 200103]], "completeType": [[1, 2104]], "spriteId": 2104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  不过你现在受伤了，用不了技能，我们只能使用普通攻击了。/c82ac66（请点击攻击按键进行攻击）/p", "layer": 3, "isEffect": 1, "groupId": 10002, "minGroupId": 2, "isBattle": 1}, {"id": 200105, "triggerType": [[5, 2105], [10, 200104]], "completeType": [[1, 2105]], "spriteId": 2105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  哼哼，攻击对象后就会自动播放战斗动画，在这释放期间，您也可以点击/cd05959“跳过”/p按键跳过该动画。", "completeIds": [200101, 200102, 200103, 200104, 200105], "layer": 3, "isEffect": 1, "groupId": 10002, "minGroupId": 2, "isBattle": 1}, {"id": 200106, "triggerType": [[5, 2106], [10, 200105], [11, 1114, 1103]], "completeType": [[1, 2106]], "spriteId": 2106, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  看来东海要我们完成的任务已经达成了，现在通过点击任务框，打开任务寻路提交任务吧。", "completeIds": [200106], "layer": 1, "isEffect": 1, "groupId": 10002, "minGroupId": 2, "mutexType": 2}, {"id": 200107, "triggerType": [[5, 2107], [10, 200106]], "completeType": [[1, 2107]], "spriteId": 2107, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  点击/cd05959“任务寻路”/p自动寻找目标提交任务吧！", "completeIds": [200107], "layer": 3, "isEffect": 1, "groupId": 10002, "minGroupId": 2, "effectPos": [0, 15], "mutexType": 1, "isEnd": 1}, {"id": 300101, "triggerType": [[5, 20001], [3, 2207, 2506], [9, 1, 201]], "completeType": [[1, 20001]], "spriteId": 20001, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  看来你帮了不少人，他们也送了你很多装备。接下来我将为您介绍装备系统功能。请点击背包界面~", "layer": 1, "groupId": 10003, "minGroupId": 3}, {"id": 300102, "triggerType": [[5, 3101], [10, 300101]], "completeType": [[1, 3101]], "spriteId": 3101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  打开背包后，聪明的你应该发现有些道具底色会不同，其实这个世界中道具会根据品质不同拥有不同底色。", "layer": 2, "groupId": 10003, "minGroupId": 3, "guideType": 2}, {"id": 300103, "triggerType": [[5, 50001], [10, 300102]], "completeType": [[1, 50001]], "spriteId": 50001, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  通常我们道具底色分为/c82ac66白、绿、蓝、橙、紫/p，品质对应底色从低到高分为/c82ac66普通、精致、稀有、史诗、传说/p。现在请您点击亮框内的装备。", "layer": 2, "groupId": 10003, "minGroupId": 3}, {"id": 300104, "triggerType": [[5, 10011], [10, 300103]], "completeType": [[1, 10011]], "spriteId": 10011, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  获得新装备后，可通过点击/cd05959“装备比较”/p与身上的装备进行比较。", "layer": 3, "groupId": 10003, "minGroupId": 3}, {"id": 300105, "triggerType": [[5, 3102], [10, 300104]], "completeType": [[1, 3102]], "spriteId": 3102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  通过比较可知，如果比较的装备属性优于身上则字体显示为/c82ac66绿色/p，劣于的话会显示/cd05959红色/p。", "layer": 3, "groupId": 10003, "minGroupId": 3, "guideType": 2}, {"id": 300106, "triggerType": [[5, 10001], [10, 300105]], "completeType": [[1, 10001]], "spriteId": 10001, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  看来是全绿呢，那装备上它吧", "completeIds": [300101, 300102, 300103, 300104, 300105, 300106], "layer": 3, "groupId": 10003, "minGroupId": 3, "isEnd": 1}, {"id": 500101, "triggerType": [[5, 20006], [9, 1, 206], [8, 20006]], "completeType": [[1, 20006]], "spriteId": 20006, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请打开'/cd05959背包/p'界面。", "layer": 1, "groupId": 10005, "minGroupId": 5}, {"id": 500102, "triggerType": [[5, 50006], [10, 500101]], "completeType": [[1, 50006]], "spriteId": 50006, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  哇呼，他好像送给了我们一个蛋！！！这莫非就是传闻中可以与主人一同战斗的宠物蛋吗！！", "layer": 2, "groupId": 10005, "minGroupId": 5}, {"id": 500103, "triggerType": [[5, 10013], [10, 500102]], "completeType": [[1, 10013]], "spriteId": 10013, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请点击'/cd05959使用/p'按键。", "layer": 3, "groupId": 10005, "minGroupId": 5}, {"id": 500104, "triggerType": [[5, 5101], [10, 500103], [9, 1, 208]], "completeType": [[1, 5101]], "spriteId": 5101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  通过背包，可直接进入宠物管理界面", "layer": 2, "groupId": 10005, "minGroupId": 5}, {"id": 500105, "triggerType": [[5, 5102], [10, 500104]], "completeType": [[12, 1], [1, 5102]], "spriteId": 5102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  宠物拥有属于自己的各个功能，通过细心的培养，可以使您的宠物变得更强大。那快来看看有什么功能吧。", "completeIds": [500101, 500102, 500103, 500104, 500105], "layer": 2, "groupId": 10005, "minGroupId": 5, "guideType": 5, "isEnd": 1}, {"id": 600101, "triggerType": [[5, 120001], [1, 9]], "completeType": [[1, 120001]], "spriteId": 120001, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请打开'/cd05959人物/p'界面。", "nextId": 600102, "layer": 1, "isEffect": 1, "groupId": 10006, "minGroupId": 6}, {"id": 600102, "triggerType": [[5, 40001], [10, 600101]], "completeType": [[1, 40001]], "spriteId": 40001, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  请点击/cd05959“属性”/p界面。", "nextId": 600103, "layer": 2, "isEffect": 1, "groupId": 10006, "minGroupId": 6}, {"id": 600103, "triggerType": [[5, 6101], [10, 600102]], "completeType": [[1, 6101]], "spriteId": 6101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 250, 1], "talkContent": "  看来你积累了不少属性点呢！要想变强，可不能冷落他们。", "nextId": 600104, "layer": 2, "groupId": 10006, "minGroupId": 6, "guideType": 1}, {"id": 600104, "triggerType": [[5, 6102], [10, 600103]], "completeType": [[1, 6102]], "spriteId": 6102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  每升级1级，人物将会获得3点可分配属性点，更多的属性点可增强你角色的实力。/c82ac66（职业推荐加点：侠客敏捷，战士力量，法师智力，修真感知）/p请点击/cd05959“加号”/p分配属性点", "nextId": 600105, "completeIds": [600101, 600102, 600103, 600104], "layer": 2, "isEffect": 1, "groupId": 10006, "minGroupId": 6, "effectPos": [5, 15]}, {"id": 600105, "triggerType": [[5, 6104], [10, 600104]], "completeType": [[1, 6104]], "spriteId": 6104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  /cd05959“点击确定”/p吧！", "nextId": 600106, "completeIds": [600105], "layer": 2, "isEffect": 1, "groupId": 10006, "minGroupId": 6, "effectPos": [0, 12]}, {"id": 600106, "triggerType": [[5, 30001], [9, 1, 1], [10, 600105]], "completeType": [[1, 30001]], "spriteId": 30001, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请点击'/cd05959返回/p'按键。", "completeIds": [600106], "layer": 2, "isEffect": 1, "groupId": 10006, "minGroupId": 6, "effectPos": [0, 15], "isEnd": 1}, {"id": 800101, "triggerType": [[5, 8101], [9, 1, 111001], [3, 42]], "completeType": [[1, 8101]], "spriteId": 8101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  看来你已经存了不少道具，背包空间都告危了。", "nextId": 800102, "layer": 1, "groupId": 10008, "minGroupId": 8, "guideType": 5, "isTeam": 1}, {"id": 800102, "triggerType": [[5, 110001], [10, 800101]], "completeType": [[9, 10000, 1, "仓库管理员"], [1, 110001]], "spriteId": 110001, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  记得当初村长赠送我们个人城市里有个仓库。我们先回去一趟把多余的东西存放起来吧。点击/cd05959“回城”/p快速跳转到个人城市", "nextId": 800103, "layer": 1, "isEffect": 1, "groupId": 10008, "minGroupId": 8, "isTeam": 1}, {"id": 800103, "triggerType": [[5, 8102], [10, 800102], [9, 1, 207]], "completeType": [[1, 8102]], "spriteId": 8102, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请打开'个人仓库/cd05959使用/p'。", "nextId": 800104, "layer": 3, "isEffect": 1, "groupId": 10008, "minGroupId": 8, "isTeam": 1, "mutexType": 1}, {"id": 800104, "triggerType": [[5, 8103], [10, 800103]], "completeType": [[1, 8103]], "spriteId": 8103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  每个玩家都有属于自己的个人仓库，当你的背包快满时，可以将你平时用不上的道具放入个人仓库", "nextId": 800105, "layer": 3, "groupId": 10008, "minGroupId": 8, "guideType": 1, "isTeam": 1}, {"id": 800105, "triggerType": [[5, 50007], [10, 800104]], "completeType": [[1, 50007]], "spriteId": 50007, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  请点击对应道具，将物品放入个人仓库", "nextId": 800106, "layer": 2, "isEffect": 1, "groupId": 10008, "minGroupId": 8, "isTeam": 1}, {"id": 800106, "triggerType": [[5, 10014], [10, 800105]], "completeType": [[1, 10014]], "spriteId": 10014, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请点击放入仓库。", "nextId": 800107, "layer": 3, "groupId": 10008, "minGroupId": 8, "isTeam": 1}, {"id": 800107, "triggerType": [[5, 8104], [10, 800106]], "completeType": [[1, 8104]], "spriteId": 8104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 380], "talkContent": "  当你的个人仓库快存放满时，你也可以通过仓库管理员-扩展仓库购买扩展仓库的道具，对你的仓库进行扩展", "nextId": 800108, "completeIds": [800101, 800102, 800103, 800104, 800105, 800106, 800107], "layer": 2, "isNotBorder": 1, "isEffect": 1, "groupId": 10008, "minGroupId": 8, "guideType": 1, "effectPos": [0, 15], "isTeam": 1}, {"id": 800108, "triggerType": [[5, 30005], [10, 800107]], "completeType": [[1, 30005]], "spriteId": 30005, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请点击'/cd05959返回/p'按键。", "completeIds": [800108], "layer": 3, "groupId": 10008, "minGroupId": 8, "isTeam": 1, "isEnd": 1}, {"id": 900101, "triggerType": [[5, 9101], [9, 1, 111002]], "completeType": [[1, 9101]], "spriteId": 9101, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  哦哦，看来经过长久的战斗，您身上的装备已经菠萝菠萝了", "nextId": 900102, "layer": 1, "groupId": 10009, "minGroupId": 9, "guideType": 5, "isTeam": 1}, {"id": 900102, "triggerType": [[5, 9102], [10, 900101]], "completeType": [[1, 9102]], "spriteId": 9102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  当装备损坏时，便会在战斗中失去作用，这时屏幕右边便会弹出/cd05959“修理”/p图标。赶快修复好装备，才能取回原有的力量。请点击/cd05959“修理”/p按键", "nextId": 900103, "layer": 2, "isEffect": 1, "groupId": 10009, "minGroupId": 9, "isTeam": 1}, {"id": 900103, "triggerType": [[5, 9103], [10, 900102]], "completeType": [[1, 9103]], "spriteId": 9103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  看来他会帮您把已经损毁的装备一一检选出来。不过修理装备我们需要前往个人城市-城市守卫处，只有她才能帮我们修理，快点击/cd05959“前往修理”/p吧", "nextId": 900104, "layer": 3, "isEffect": 1, "groupId": 10009, "minGroupId": 9, "effectPos": [0, 15], "isTeam": 1}, {"id": 900104, "triggerType": [[5, 9104], [10, 900103]], "completeType": [[1, 9104]], "spriteId": 9104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  走遍千山万水，终于找到你了，快点击/cd05959“修理装备”/p进行修理吧~", "nextId": 900105, "layer": 3, "isEffect": 1, "groupId": 10009, "minGroupId": 9, "effectPos": [0, 15], "isTeam": 1, "mutexType": 1}, {"id": 900105, "triggerType": [[5, 9105], [10, 900104]], "completeType": [[1, 9105]], "spriteId": 9105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  啊~修理装备居然还要收钱啊，不过想想也是应该的。快点击/cd05959“确定”/p进行修理吧", "nextId": 900106, "completeIds": [900101, 900102, 900103, 900104, 900105], "layer": 3, "isEffect": 1, "groupId": 10009, "minGroupId": 9, "effectPos": [0, 15], "isTeam": 1}, {"id": 900106, "triggerType": [[5, 9106], [10, 900105]], "completeType": [[1, 9106]], "spriteId": 9106, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 300], "talkContent": "  如果不想跑回个人城市修理装备，当您获得道具/cd05959“野外修理卷”/p后在背包内使用，也同样能修复装备耐久哦，怎么样？记住了吗？", "completeIds": [900106], "layer": 3, "groupId": 10009, "minGroupId": 9, "guideType": 3, "isTeam": 1, "isEnd": 1}, {"id": 1000101, "triggerType": [[5, 10101], [3, 2231, 2527]], "completeType": [[1, 10101]], "spriteId": 10101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  看来你的身体已经恢复的差不多了，但是真正的变强之路才刚刚开始", "layer": 1, "groupId": 10010, "minGroupId": 10, "guideType": 5}, {"id": 1000102, "triggerType": [[5, 20002], [10, 1000101]], "completeType": [[12, 210], [1, 20002]], "spriteId": 20002, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  接下来我们将介绍游戏的核心玩法之一：装备的强化，请点击打开背包界面", "completeIds": [1000101, 1000102], "layer": 2, "isEffect": 1, "groupId": 10010, "minGroupId": 10, "isEnd": 1}, {"id": 1100101, "triggerType": [[5, 11101], [9, 1, 111004], [14, 5]], "completeType": [[1, 11101]], "spriteId": 11101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  啊！！血量少于50%可是非常危险的行为，快点使用/cd05959“药品”/p功能恢复血量吧", "nextId": 1100102, "layer": 1, "isEffect": 1, "groupId": 10011, "minGroupId": 11}, {"id": 1100102, "triggerType": [[5, 11102], [10, 1100101]], "completeType": [[1, 11102]], "spriteId": 11102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  你可以点击一键恢复进行恢复生命值，也可以根据需求选择药品恢复", "nextId": 1100103, "completeIds": [1100101, 1100102], "layer": 3, "isEffect": 1, "groupId": 10011, "minGroupId": 11}, {"id": 1100103, "triggerType": [[5, 11103], [10, 1100102]], "completeType": [[1, 11103]], "spriteId": 11103, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "请点击'/cd05959返回/p'按键。", "nextId": 1100104, "completeIds": [1100103], "layer": 3, "isEffect": 1, "groupId": 10011, "minGroupId": 11, "autoFinish": [10, 1100102]}, {"id": 1100104, "triggerType": [[5, 11104], [10, 1100103]], "completeType": [[1, 11104]], "spriteId": 11104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280, 1], "talkContent": "  特别提醒你，通过点击人物血条可以快速使用药品回复血量", "completeIds": [1100104], "layer": 1, "isEffect": 1, "groupId": 10011, "minGroupId": 11, "autoFinish": [10, 1100103], "isEnd": 1}, {"id": 1200101, "triggerType": [[5, 12101], [9, 1500, 111003], [9, 1, 111007]], "completeType": [[1, 12101]], "spriteId": 12101, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  看来与您一同出战的宠物已经日益成长了。不过他好像还有许多技能点未被使用，这可不行哦。想要变得更强，就要去学习技能哦。", "layer": 1, "groupId": 10012, "minGroupId": 12, "guideType": 5, "isTeam": 1}, {"id": 1200102, "triggerType": [[5, 12102], [10, 1200101]], "completeType": [[13, 200001], [1, 12102]], "spriteId": 12102, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  接下来让我们去访问下宠物训练师吧", "nextId": 1200103, "layer": 3, "groupId": 10012, "minGroupId": 12, "guideType": 5, "isTeam": 1}, {"id": 1200103, "triggerType": [[5, 12103], [10, 1200102]], "completeType": [[1, 12103]], "spriteId": 12103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  请点击宠物商店", "nextId": 1200104, "layer": 3, "isEffect": 1, "groupId": 10012, "minGroupId": 12, "restoreList": [1200101, 1200102], "isTeam": 1, "mutexType": 1}, {"id": 1200104, "triggerType": [[5, 12104], [10, 1200103]], "completeType": [[1, 12104]], "spriteId": 12104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  与人物一样，当您的宠物升级时，同样也会获得技能点，通过消耗技能点，可进行学习技能。", "nextId": 1200105, "layer": 2, "groupId": 10012, "minGroupId": 12, "guideType": 1, "isTeam": 1}, {"id": 1200105, "triggerType": [[5, 12105], [10, 1200104]], "completeType": [[1, 12105]], "spriteId": 12105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  宠物技能数的学习，也与宠物品质有关，宠物品质越高，可学的技能个数越多。", "nextId": 1200106, "layer": 2, "groupId": 10012, "minGroupId": 12, "guideType": 1, "isTeam": 1}, {"id": 1200106, "triggerType": [[5, 12106], [10, 1200105]], "completeType": [[1, 12106]], "spriteId": 12106, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  其中：/c82ac66普通8个、精致9个、稀有10个、史诗12个、传说14个/p可学技能哦", "nextId": 1200107, "layer": 2, "groupId": 10012, "minGroupId": 12, "guideType": 1, "isTeam": 1}, {"id": 1200107, "triggerType": [[5, 12107], [10, 1200106]], "completeType": [[1, 12107]], "spriteId": 12107, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  另外，不同类型的宠物是可以相互学习彼此的技能的，但是跨职学习其他宠物的技能，消耗的技能点及金钱数是本职的2倍哦", "nextId": 1200108, "layer": 2, "groupId": 10012, "minGroupId": 12, "guideType": 1, "isTeam": 1}, {"id": 1200108, "triggerType": [[5, 12108], [10, 1200107]], "completeType": [[1, 12108]], "spriteId": 12108, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  如：您的宠物为迅捷宠物，学习睿智和勇猛宠物的技能时，就属于跨职哦。关于宠物类型可在主界面-宠物-点击要查看的宠物-基本中查看哦", "nextId": 1200109, "layer": 2, "groupId": 10012, "minGroupId": 12, "guideType": 5, "isTeam": 1}, {"id": 1200109, "triggerType": [[5, 12108], [10, 1200107]], "completeType": [[1, 12109]], "spriteId": 12109, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  宠物技能的学习至关重要，它可以为您在前期战斗中保驾护航。如果您想让宠物拥有更多伤害，建议学习迅捷、睿智宠物技能。而勇猛宠物技能，可以在战斗中为您抵挡伤害哦", "nextId": 1200110, "layer": 2, "groupId": 10012, "minGroupId": 12, "guideType": 5, "isTeam": 1}, {"id": 1200110, "triggerType": [[5, 12110], [10, 1200109]], "completeType": [[1, 12110]], "spriteId": 12110, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  另外，宠物还拥有/cd05959“天生技能”/p，该类技能无法学习，只能通过宠物升级自行领悟哦。作为主人，为您的宠物搭配合适的技能，也是作为主人的责任", "completeIds": [1200101, 1200102, 1200103, 1200105, 1200106, 1200107, 1200108, 1200109, 1200110], "layer": 2, "groupId": 10012, "minGroupId": 12, "guideType": 5, "isTeam": 1, "isEnd": 1}, {"id": 1500101, "triggerType": [[5, 15101], [13, 579]], "completeType": [[1, 15101]], "spriteId": 15101, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  琪琪我好像闻到了危险的味道~这些怪物，好像和以往遇到的不一样/c82ac66（接下来将进行高阶战斗说明）/p", "layer": 1, "groupId": 10015, "minGroupId": 15, "guideType": 5, "isBattle": 1}, {"id": 1500102, "triggerType": [[5, 15102], [10, 1500101]], "completeType": [[1, 15102]], "spriteId": 15102, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  在战斗中，有一种称为/cd05959“状态”/p的buff，拥有这种状态的对象可以获得额外效果。", "layer": 2, "groupId": 10015, "minGroupId": 15, "guideType": 5, "isBattle": 1}, {"id": 1500103, "triggerType": [[5, 15103], [10, 1500102]], "completeType": [[1, 15103]], "spriteId": 15103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  通常我们可以在自己回合使用/cd05959“查看”/p功能查询对方详情。请点击/cd05959“查看”/p", "layer": 2, "isEffect": 1, "groupId": 10015, "minGroupId": 15, "isBattle": 1}, {"id": 1500104, "triggerType": [[5, 15104], [10, 1500103]], "completeType": [[1, 15104]], "spriteId": 15104, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  进入查看模式后，如果对象有额外状态的话，会在其右上角显示小图标", "layer": 2, "groupId": 10015, "minGroupId": 15, "guideType": 5, "isBattle": 1}, {"id": 1500105, "triggerType": [[5, 15105], [10, 1500104]], "completeType": [[1, 15105]], "spriteId": 15105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  通过查看功能，我们知道骷髅法师有两个小图标。想要进一步了解，请点击/cd05959“骷髅法师”/p", "layer": 2, "isEffect": 1, "groupId": 10015, "minGroupId": 15, "isBattle": 1}, {"id": 1500106, "triggerType": [[5, 15106], [10, 1500105]], "completeType": [[1, 15106]], "spriteId": 15106, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  通过查看我们可知骷髅法师/c82ac66免疫定身/p和/c82ac66免疫混乱/p。如果状态名为/c82ac66绿色字体/p，则为增益状态。", "layer": 3, "groupId": 10015, "minGroupId": 15, "guideType": 1, "isBattle": 1}, {"id": 1500107, "triggerType": [[5, 15107], [10, 1500106]], "completeType": [[1, 15107]], "spriteId": 15107, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  如果状态为/cd05959红色字体/p，则为负面状态。如/cd05959“免疫定身”/p是绿色字体，即为增益状态，他不受定身技能影响。", "layer": 3, "groupId": 10015, "minGroupId": 15, "guideType": 5, "isBattle": 1}, {"id": 1500108, "triggerType": [[5, 15108], [10, 1500107]], "completeType": [[1, 15108]], "spriteId": 15108, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  在知道敌方免疫某些状态后，正确的做法是避开使用拥有这些状态的技能。", "layer": 3, "groupId": 10015, "minGroupId": 15, "guideType": 5, "isBattle": 1}, {"id": 1500109, "triggerType": [[5, 15109], [10, 1500108]], "completeType": [[1, 15109]], "spriteId": 15109, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  在战斗中，通过使用技能，为自己增加增益状态，或者清除负面状态。不过要特别提醒你：/c82ac66带有天生状态的buff是无法被清除的哦/p", "layer": 3, "groupId": 10015, "minGroupId": 15, "guideType": 5, "isBattle": 1}, {"id": 1500110, "triggerType": [[5, 15110], [10, 1500109]], "completeType": [[1, 15110]], "spriteId": 15110, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  好了，战斗的高阶已经为你介绍完毕，不妨去学习一些带状态的技能尝试一下吧", "completeIds": [1500101, 1500102, 1500103, 1500104, 1500105, 1500106, 1500107, 1500108, 1500109, 1500110], "layer": 3, "groupId": 10015, "minGroupId": 15, "guideType": 5, "param": [[5]], "isBattle": 1, "isEnd": 1}, {"id": 1700101, "triggerType": [[5, 17101], [3, 42], [3, 37]], "completeType": [[1, 17101]], "spriteId": 17101, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  嗨，我们又见面了，不知不觉已经为您讲解了许许多多的教程说明，连我已经记不清有多少了。", "nextId": 1700102, "layer": 1, "groupId": 10017, "minGroupId": 17, "guideType": 5, "isTeam": 1}, {"id": 1700102, "triggerType": [[5, 110003], [10, 1700101]], "completeType": [[9, 10000, 1, "游戏攻略师"], [1, 110003]], "spriteId": 110003, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  不过我也深思熟虑过，就算讲解再多，也不能全尽，所以今天要为您介绍一个人，他可以给您不少帮助。请点击屏幕右边的/cd05959“回城”/p按键", "nextId": 1700103, "completeIds": [1700101, 1700102], "layer": 1, "isEffect": 1, "groupId": 10017, "minGroupId": 17, "isTeam": 1}, {"id": 1700103, "triggerType": [[5, 17102], [10, 1700102]], "completeType": [[1, 17102]], "spriteId": 17102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  当您在游戏中遇到种种疑惑时，可以前往个人城市寻找游戏攻略师，在他这里可以回答您的各种问题，相信肯定可以帮到您。请点击/cd05959“游戏攻略”/p看看有什么内容吧！", "completeIds": [1700103], "layer": 3, "isEffect": 1, "groupId": 10017, "minGroupId": 17, "isTeam": 1, "mutexType": 1, "isEnd": 1}, {"id": 1800101, "triggerType": [[5, 18101], [1, 55]], "completeType": [[1, 18101]], "spriteId": 18101, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  哦呼，没想到您已经到达55级了，想必对我们游戏已经更加熟悉了吧。", "nextId": 1800102, "layer": 1, "groupId": 10018, "minGroupId": 18, "guideType": 5, "isTeam": 1}, {"id": 1800102, "triggerType": [[5, 18102], [10, 1800101]], "completeType": [[13, 200002], [1, 18102]], "spriteId": 18102, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  不过，不知道您是否对该养成哪些装备感到迷茫呢，今天就为您介绍各职业中期毕业装备的介绍。", "nextId": 1800103, "layer": 1, "groupId": 10018, "minGroupId": 18, "guideType": 5, "isTeam": 1}, {"id": 1800103, "triggerType": [[5, 18103], [10, 1800102]], "completeType": [[1, 18103]], "spriteId": 18103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  每个职业都有属于自己的可进阶装备，相同的装备，获得进阶的装备能比未进阶的装备最多多出4条鉴定属性，也就是说一件装备有8条鉴定属性。是不是很惊人呢！事不宜迟快打开进阶商店看看吧", "nextId": 1800104, "layer": 3, "groupId": 10018, "minGroupId": 18, "isTeam": 1, "mutexType": 1}, {"id": 1800104, "triggerType": [[5, 18104], [10, 1800103]], "completeType": [[1, 18104]], "spriteId": 18104, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  进入合成商店后，需要您身上携带对应的装备，通过消耗对应的材料，来合成进阶属性。当然进阶属性的合成是有几率失败的，希望您别气馁", "nextId": 1800105, "layer": 2, "groupId": 10018, "minGroupId": 18, "guideType": 5, "isTeam": 1}, {"id": 1800105, "triggerType": [[5, 18105], [10, 1800104]], "completeType": [[1, 18105]], "spriteId": 18105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 280], "talkContent": "  合成进阶所需的材料，除了日常副本会产出外，通过在背包-分解功能，将一些平时用不上的装备分解了，也是可以获得的哦。特别提醒您，如果要查看装备的获得途径，请点击对应装备的/cd05959“?”/p图标哦", "completeIds": [1800101, 1800102, 1800103, 1800104, 1800105], "layer": 2, "groupId": 10018, "minGroupId": 18, "isTeam": 1, "isEnd": 1}, {"id": 1900101, "triggerType": [[5, 19101], [3, 42], [14, 4]], "completeType": [[1, 19101]], "spriteId": 19101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  没想到长老这么大方直接送了一个城市给我们。哼哼，终于不用四处漂泊了。赶紧去看看自己的城市吧！/c82ac66（请点击主城）/p", "layer": 1, "groupId": 10019, "minGroupId": 19}, {"id": 1900102, "triggerType": [[5, 19102], [10, 1900101]], "completeType": [[1, 19102]], "spriteId": 19102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  看来城市已经有各种各样的功能了，长老真贴心呢！来看看有什么吧！看来这个叫/cd05959“成就”/p的功能，是你完成各个成就目标后，就可以领取奖励了。", "layer": 2, "groupId": 10019, "minGroupId": 19, "guideType": 2, "isRecharge": 1}, {"id": 1900103, "triggerType": [[5, 19103], [10, 1900102]], "completeType": [[1, 19103]], "spriteId": 19103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262, 1], "talkContent": "  这个叫/cd05959“社交”/p的功能，看来是可以通过申请和管理在游戏中好友关系的功能呢", "layer": 2, "groupId": 10019, "minGroupId": 19, "guideType": 2}, {"id": 1900104, "triggerType": [[5, 19104], [10, 1900103]], "completeType": [[1, 19104]], "spriteId": 19104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  让我看看还有什么，/cd05959“城市”/p~看来这个就是我们的个人城市了，快点击它进去看看吧！我已经迫不及待了", "layer": 2, "groupId": 10019, "minGroupId": 19}, {"id": 1900105, "triggerType": [[5, 19105], [10, 1900104]], "completeType": [[1, 19105]], "spriteId": 19105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  请点击进入个人城市", "layer": 2, "groupId": 10019, "minGroupId": 19}, {"id": 1900106, "triggerType": [[5, 19106], [10, 1900105]], "completeType": [[1, 19106]], "spriteId": 19106, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  看来比想象中破烂呢，不过通过完成长老的任务，可以将我们的城市升级得更加美丽，一起加油吧！", "layer": 1, "groupId": 10019, "minGroupId": 19, "guideType": 5}, {"id": 1900107, "triggerType": [[5, 19107], [10, 1900106]], "completeType": [[1, 19107]], "spriteId": 19107, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  看来也不能休息太久，世界还未和平，赶紧动身前往那些需要我们的人身边吧！/c82ac66（请点击任务继续主线）/p", "completeIds": [1900101, 1900102, 1900103, 1900104, 1900105, 1900106, 1900107], "layer": 1, "groupId": 10019, "minGroupId": 19, "mutexType": 2, "isEnd": 1}, {"id": 2000101, "triggerType": [[5, 20101], [3, 42], [1, 20, 1], [9, 1, 111006], [9, 1, 111005]], "completeType": [[1, 20101]], "spriteId": 20101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  这对手也太强了吧！看来以你现在的实力，要战胜他有些困难；也难怪，你的装备太差了", "nextId": 2000102, "layer": 1, "groupId": 10020, "minGroupId": 20, "guideType": 5, "isTeam": 1}, {"id": 2000102, "triggerType": [[5, 20102], [10, 2000101]], "completeType": [[13, 200003], [1, 20102]], "spriteId": 20102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "  俗话说工欲善其事必先利其器，琪琪我啊，刚好认识一位武器大师，快跟我一同去找他吧！", "nextId": 2000103, "layer": 1, "groupId": 10020, "minGroupId": 20, "guideType": 5, "isTeam": 1}, {"id": 2000103, "triggerType": [[5, 20103], [10, 2000102]], "completeType": [[1, 20103]], "spriteId": 20103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262, 1], "talkContent": "  就让聪明的我为你推荐商店吧！请点击打开商店", "nextId": 2000104, "layer": 3, "groupId": 10020, "minGroupId": 20, "isTeam": 1, "mutexType": 1}, {"id": 2000104, "triggerType": [[5, 20104], [10, 2000103]], "completeType": [[1, 20104]], "spriteId": 20104, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "  看来有不少东西呢，而且价格也相当实惠呢，赶紧挑选一下适合你的装备吧！", "completeIds": [2000101, 2000102, 2000103, 2000104], "layer": 2, "groupId": 10020, "minGroupId": 20, "guideType": 5, "isAlpha": 1, "isTeam": 1, "isEnd": 1}, {"id": 2100101, "triggerType": [[5, 21101], [14, 31]], "completeType": [[1, 21101]], "spriteId": 21101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "接下来将为您介绍首充功能。游戏中部分情况下会使用一种叫/cd05959黄金/p的货币。", "layer": 1, "groupId": 10021, "minGroupId": 21, "guideType": 5, "isRecharge": 1}, {"id": 2100102, "triggerType": [[5, 21102], [10, 2100101]], "completeType": [[1, 21102]], "spriteId": 21102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "当您需要这种特殊的货币时，可以点击屏幕右侧/cd05959“充值”/p按键进行获得。请点击该按键~", "layer": 1, "isEffect": 1, "groupId": 10021, "minGroupId": 21, "isRecharge": 1}, {"id": 2100103, "triggerType": [[5, 21103], [10, 2100102]], "completeType": [[1, 21103]], "spriteId": 21103, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "每个角色在充值界面，首次对特定金额商品进行充值都能获得额外的首充福利：/c82ac66黄金额外赠送1.5倍/p", "layer": 2, "groupId": 10021, "minGroupId": 21, "guideType": 1, "isRecharge": 1}, {"id": 2100104, "triggerType": [[5, 21104], [10, 2100103]], "completeType": [[1, 21104]], "spriteId": 21104, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "这里/c82ac66额外提醒您，只有充值获得黄金的面额才会赠送额外黄金哦/p。充值只有金叶是无法获得额外赠送的。", "layer": 2, "groupId": 10021, "minGroupId": 21, "guideType": 1, "isRecharge": 1}, {"id": 2100105, "triggerType": [[5, 21105], [10, 2100104]], "completeType": [[1, 21105]], "spriteId": 21105, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "部分充值选项充值成功后也会赠送/cd05959“大额充值礼包”/p，该礼包可开启到许多日常消耗品，并有几率开启到当期限定时装、坐骑或者宠物蛋哦", "layer": 2, "groupId": 10021, "minGroupId": 21, "guideType": 2, "isRecharge": 1}, {"id": 2100106, "triggerType": [[5, 21106], [10, 2100105]], "completeType": [[1, 21106]], "spriteId": 21106, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 380], "talkContent": "此外，为了感谢各位充值玩家的支持，在当前界面累积充值满30元，即可获得充值登录3天的奖励。/c82ac66PS：该奖励是发送到充值邮箱的哦/p", "layer": 2, "groupId": 10021, "minGroupId": 21, "guideType": 1, "isRecharge": 1}, {"id": 2100107, "triggerType": [[5, 30011], [10, 2100106]], "completeType": [[1, 30011]], "spriteId": 30011, "arrowDir": 1, "lock": 1, "needTxt": 1, "txt": "首充教程已经介绍完毕，请点击关闭按键", "completeIds": [2100101, 2100102, 2100103, 2100104, 2100105, 2100106, 2100107], "layer": 2, "groupId": 10021, "minGroupId": 21, "guideType": 1, "isRecharge": 1, "isEnd": 1}, {"id": 2300101, "triggerType": [[5, 23101], [1, 35]], "completeType": [[1, 23101]], "spriteId": 23101, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "看来对手格外的强悍呢，不过我们也不是完全束手无策", "nextId": 2300102, "layer": 3, "groupId": 10023, "minGroupId": 23, "guideType": 5, "isBattle": 1}, {"id": 2300102, "triggerType": [[5, 23102], [10, 2300101]], "completeType": [[1, 23102]], "spriteId": 23102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -262], "talkContent": "每次战斗失败时，不妨点击/cd05959“冒险攻略”/p，该功能记录着本场战斗胜利后其他天选之人的装备及战斗信息哦/c82ac66（请点击“冒险攻略”）/p", "nextId": 2300103, "layer": 3, "isEffect": 1, "groupId": 10023, "minGroupId": 23, "isBattle": 1}, {"id": 2300103, "triggerType": [[5, 23103], [10, 2300102]], "completeType": [[1, 23103]], "spriteId": 23103, "arrowDir": 1, "lock": 1, "needGirl": 1, "talkContent": "通过点击其他天选之人的/cd05959头像/p，即可查看他们的详细信息，浏览他们的装备、技能等其他信息，作为你通关的参考。你也可点击/cd05959“播放”/p按键，查看他的战斗回放哦", "completeIds": [2300101, 2300102, 2300103], "layer": 2, "groupId": 10023, "minGroupId": 23, "guideType": 5, "isEnd": 1}, {"id": 2070101, "triggerType": [[5, 20201], [3, 769]], "completeType": [[1, 20201]], "spriteId": 20201, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -260], "talkContent": "  嗯？看来又有新东西解锁了，点击一下去看看吧", "completeIds": [2070101], "layer": 1, "isEffect": 1, "groupId": 20001, "minGroupId": 201}, {"id": 2070102, "triggerType": [[5, 207101], [14, 7]], "completeType": [[12, 7], [1, 207101]], "spriteId": 207101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -260], "talkContent": "  请点击'/cd05959分解/p'按键。", "completeIds": [2070102], "layer": 2, "isEffect": 1, "groupId": 20001, "minGroupId": 201, "isEnd": 1}, {"id": 2080101, "triggerType": [[5, 20202], [1, 18]], "completeType": [[1, 20202]], "spriteId": 20202, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -260], "talkContent": "  嗯嗯？这次好像有不得了的东西解锁了，快点击去看看吧", "completeIds": [2080101], "layer": 1, "isEffect": 1, "groupId": 20008, "minGroupId": 208, "isEnd": 1}, {"id": 2100101, "triggerType": [[5, 203101], [14, 10]], "completeType": [[12, 10], [1, 203101]], "spriteId": 203101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -260], "talkContent": "  ！！！看来国家功能已经解锁了，快点击一下看看介绍吧", "completeIds": [2100101], "layer": 1, "isEffect": 1, "groupId": 20010, "minGroupId": 210, "isEnd": 1}, {"id": 2130101, "triggerType": [[5, 213101], [1, 58], [15, 1]], "completeType": [[1, 213101]], "spriteId": 213101, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, -260], "talkContent": "  酱酱酱！！！经过成长，看来可以为您介绍宠物剩下洗髓、潜能、封印、重生功能了。快点击过去查看吧！", "nextId": 2130102, "completeIds": [2130101], "layer": 1, "isEffect": 1, "groupId": 20013, "minGroupId": 213}, {"id": 2130102, "triggerType": [[5, 213102], [10, 2130101]], "completeType": [[1, 213102]], "spriteId": 213102, "arrowDir": 1, "lock": 1, "needGirl": 1, "girlPos": [0, 480, 1], "talkContent": "  请点击进入您的宠物界面~", "completeIds": [2130102], "layer": 2, "isEffect": 1, "groupId": 20013, "minGroupId": 213, "param": [[1, 0, 0, 0, -60]], "isSameSize": 1, "isEnd": 1}, {"1": 2, "任务接取、寻路": 2}, {"1": 3, "任务接取、寻路": 3}, {"1": 4, "任务接取、寻路": 4}, {"1": 5, "任务接取、寻路": 5}, {"1": 6, "任务接取、寻路": 6}, {"1": 7, "任务接取、寻路": 7}, {"1": 8, "任务接取、寻路": 8}, {"1": 9, "任务接取、寻路": 9}, {"1": 10, "任务接取、寻路": 10}, {"1": 11, "任务接取、寻路": 11}, {"1": 12, "任务接取、寻路": 12}, {"1": 13, "任务接取、寻路": 13}, {"1": 14, "任务接取、寻路": 14}, {"1": 15, "任务接取、寻路": 15}, {"1": 16, "任务接取、寻路": 16}, {"1": 17, "任务接取、寻路": 17}, {"1": 18, "任务接取、寻路": 18}, {"1": 19, "任务接取、寻路": 19}, {"1": 20, "任务接取、寻路": 20}, {"1": 21, "任务接取、寻路": 21}, {"1": 22, "任务接取、寻路": 22}, {"1": 23, "任务接取、寻路": 23}, {"1": 24, "任务接取、寻路": 24}]