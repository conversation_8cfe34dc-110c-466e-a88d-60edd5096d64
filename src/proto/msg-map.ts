import {Msg} from "./msg-define";
//@ts-ignore

export const SendMsgMap = {
    [Msg.C2S_BagResetMessage]: proto.C2S_BagResetMessage,
    [Msg.C2S_BagItemSellMessage]: proto.C2S_BagItemSellMessage,
    [Msg.C2S_EquipWearMessage]: proto.C2S_EquipWearMessage,
    [Msg.C2S_EquipTakeOffMessage]: proto.C2S_EquipTakeOffMessage,
    [Msg.C2S_PlayerBagUseMessage]: proto.C2S_PlayerBagUseMessage,
    [Msg.C2S_BagItemBindMessage]: proto.C2S_BagItemBindMessage,
    [Msg.C2S_BagItemStarMessage]: proto.C2S_BagItemStarMessage,
    [Msg.C2S_BagItemIdentifyMessage]: proto.C2S_BagItemIdentifyMessage,
    [Msg.C2S_BagItemIdentifyAnswerMessage]: proto.C2S_BagItemIdentifyAnswerMessage,
    [Msg.C2S_BagItemEnchaseMessage]: proto.C2S_BagItemEnchaseMessage,
    [Msg.C2S_BagItemGemReplaceMessage]: proto.C2S_BagItemGemReplaceMessage,
    [Msg.C2S_BattlePassTestMessage]: proto.C2S_BattlePassTestMessage,
    [Msg.C2S_RunLocalBattleMessage]: proto.C2S_RunLocalBattleMessage,
    [Msg.C2S_GetPlayerListMessage]: proto.C2S_GetPlayerListMessage,
    [Msg.C2S_CreateRoleMessage]: proto.C2S_CreateRoleMessage,
    [Msg.C2S_EnterGameMessage]: proto.C2S_EnterGameMessage,
    [Msg.C2S_GmExecuteMessage]: proto.C2S_GmExecuteMessage,
    [Msg.C2S_TeamJoinMessage]: proto.C2S_TeamJoinMessage,
    [Msg.C2S_TeamInviteMessage]: proto.C2S_TeamInviteMessage,
    [Msg.C2S_JumpMapMessage]: proto.C2S_JumpMapMessage,
    [Msg.C2S_PlayerMoveMessage]: proto.C2S_PlayerMoveMessage,
    [Msg.C2S_PlayerEventChooseMessage]: proto.C2S_PlayerEventChooseMessage,
    [Msg.C2S_ExitTeamMessage]: proto.C2S_ExitTeamMessage,
    [Msg.C2S_RemoveMemberMessage]: proto.C2S_RemoveMemberMessage,
    [Msg.C2S_ChangeLeaderMessage]: proto.C2S_ChangeLeaderMessage,
    [Msg.C2S_DisbandTeamMessage]: proto.C2S_DisbandTeamMessage,
    [Msg.C2S_OnMailOpenMessage]: proto.C2S_OnMailOpenMessage,
    [Msg.C2S_SendMailMessage]: proto.C2S_SendMailMessage,
    [Msg.C2S_PetAddSkillSureMessage]: proto.C2S_PetAddSkillSureMessage,
    [Msg.C2S_PetSealMessage]: proto.C2S_PetSealMessage,
    [Msg.C2S_PetSkillBookLearnMessage]: proto.C2S_PetSkillBookLearnMessage,
    [Msg.C2S_ActorAttributeMessage]: proto.C2S_ActorAttributeMessage,
    [Msg.C2S_LearnSkillByShopMessage]: proto.C2S_LearnSkillByShopMessage,
    [Msg.C2S_AutoSkillSetMessage]: proto.C2S_AutoSkillSetMessage,
    [Msg.C2S_AcceptTaskMessage]: proto.C2S_AcceptTaskMessage,
    [Msg.C2S_SubmitTaskMessage]: proto.C2S_SubmitTaskMessage,
    [Msg.C2S_DeleteTaskMessage]: proto.C2S_DeleteTaskMessage,
    [Msg.C2S_ClientCheckMessage]: proto.C2S_ClientCheckMessage,
    [Msg.C2S_ApplyLoginNoticeMessage]: proto.C2S_ApplyLoginNoticeMessage,
    [Msg.C2S_LoginMessage]: proto.C2S_LoginMessage,
    [Msg.C2S_GetAreaLinesMessage]: proto.C2S_GetAreaLinesMessage,
    [Msg.C2S_SelectAreaMessage]: proto.C2S_SelectAreaMessage,

}
export const ReceiveMsgMap = {
    [Msg.C2S_ClientCheckMessage]: proto.S2C_ClientCheckMessage,
    [Msg.C2S_ApplyLoginNoticeMessage]: proto.S2C_ApplyLoginNoticeMessage,
    [Msg.C2S_LoginMessage]: proto.S2C_LoginMessage,
    [Msg.C2S_GetAreaLinesMessage]: proto.S2C_GetAreaLinesMessage,
    [Msg.C2S_SelectAreaMessage]: proto.S2C_SelectAreaMessage,
    [Msg.C2S_BagResetMessage]: proto.S2C_BagResetMessage,
    [Msg.C2S_BagItemSellMessage]: proto.S2C_BagItemSellMessage,
    [Msg.C2S_EquipWearMessage]: proto.S2C_EquipWearMessage,
    [Msg.C2S_EquipTakeOffMessage]: proto.S2C_EquipTakeOffMessage,
    [Msg.C2S_PlayerBagUseMessage]: proto.S2C_PlayerBagUseMessage,
    [Msg.C2S_BagItemBindMessage]: proto.S2C_BagItemBindMessage,
    [Msg.C2S_BagItemStarMessage]: proto.S2C_BagItemStarMessage,
    [Msg.C2S_BagItemIdentifyMessage]: proto.S2C_BagItemIdentifyMessage,
    [Msg.C2S_BagItemIdentifyAnswerMessage]: proto.S2C_BagItemIdentifyAnswerMessage,
    [Msg.C2S_BagItemEnchaseMessage]: proto.S2C_BagItemEnchaseMessage,
    [Msg.C2S_BagItemGemReplaceMessage]: proto.S2C_BagItemGemReplaceMessage,
    [Msg.C2S_BattlePassTestMessage]: proto.S2C_BattlePassTestMessage,
    [Msg.C2S_RunLocalBattleMessage]: proto.S2C_RunLocalBattleMessage,
    [Msg.C2S_GetPlayerListMessage]: proto.S2C_GetPlayerListMessage,
    [Msg.C2S_CreateRoleMessage]: proto.S2C_CreateRoleMessage,
    [Msg.C2S_EnterGameMessage]: proto.S2C_EnterGameMessage,
    [Msg.C2S_GmExecuteMessage]: proto.S2C_GmExecuteMessage,
    [Msg.C2S_TeamJoinMessage]: proto.S2C_TeamJoinMessage,
    [Msg.C2S_TeamInviteMessage]: proto.S2C_TeamInviteMessage,
    [Msg.C2S_JumpMapMessage]: proto.S2C_JumpMapMessage,
    [Msg.C2S_PlayerMoveMessage]: proto.S2C_PlayerMoveMessage,
    [Msg.C2S_PlayerEventChooseMessage]: proto.S2C_PlayerEventChooseMessage,
    [Msg.C2S_ExitTeamMessage]: proto.S2C_ExitTeamMessage,
    [Msg.C2S_RemoveMemberMessage]: proto.S2C_RemoveMemberMessage,
    [Msg.C2S_ChangeLeaderMessage]: proto.S2C_ChangeLeaderMessage,
    [Msg.C2S_DisbandTeamMessage]: proto.S2C_DisbandTeamMessage,
    [Msg.C2S_OnMailOpenMessage]: proto.S2C_OnMailOpenMessage,
    [Msg.C2S_SendMailMessage]: proto.S2C_SendMailMessage,
    [Msg.C2S_PetAddSkillSureMessage]: proto.S2C_PetAddSkillSureMessage,
    [Msg.C2S_PetSealMessage]: proto.S2C_PetSealMessage,
    [Msg.C2S_PetSkillBookLearnMessage]: proto.S2C_PetSkillBookLearnMessage,
    [Msg.C2S_ActorAttributeMessage]: proto.S2C_ActorAttributeMessage,
    [Msg.C2S_LearnSkillByShopMessage]: proto.S2C_LearnSkillByShopMessage,
    [Msg.C2S_AutoSkillSetMessage]: proto.S2C_AutoSkillSetMessage,
    [Msg.C2S_AcceptTaskMessage]: proto.S2C_AcceptTaskMessage,
    [Msg.C2S_SubmitTaskMessage]: proto.S2C_SubmitTaskMessage,
    [Msg.C2S_DeleteTaskMessage]: proto.S2C_DeleteTaskMessage,
    [Msg.S2C_BagResetMessage]: proto.S2C_BagResetMessage,
    [Msg.S2C_BagItemSellMessage]: proto.S2C_BagItemSellMessage,
    [Msg.S2C_EquipWearMessage]: proto.S2C_EquipWearMessage,
    [Msg.S2C_EquipTakeOffMessage]: proto.S2C_EquipTakeOffMessage,
    [Msg.S2C_PlayerBagUseMessage]: proto.S2C_PlayerBagUseMessage,
    [Msg.S2C_BagItemBindMessage]: proto.S2C_BagItemBindMessage,
    [Msg.S2C_BagItemStarMessage]: proto.S2C_BagItemStarMessage,
    [Msg.S2C_BagItemIdentifyMessage]: proto.S2C_BagItemIdentifyMessage,
    [Msg.S2C_BagItemIdentifyAnswerMessage]: proto.S2C_BagItemIdentifyAnswerMessage,
    [Msg.S2C_BagItemEnchaseMessage]: proto.S2C_BagItemEnchaseMessage,
    [Msg.S2C_BagItemGemReplaceMessage]: proto.S2C_BagItemGemReplaceMessage,
    [Msg.S2C_BattlePassTestMessage]: proto.S2C_BattlePassTestMessage,
    [Msg.S2C_RunLocalBattleMessage]: proto.S2C_RunLocalBattleMessage,
    [Msg.S2C_GetPlayerListMessage]: proto.S2C_GetPlayerListMessage,
    [Msg.S2C_CreateRoleMessage]: proto.S2C_CreateRoleMessage,
    [Msg.S2C_EnterGameMessage]: proto.S2C_EnterGameMessage,
    [Msg.S2C_GmExecuteMessage]: proto.S2C_GmExecuteMessage,
    [Msg.S2C_EnterMapMessage]: proto.S2C_EnterMapMessage,
    [Msg.S2C_LeaveMapMessage]: proto.S2C_LeaveMapMessage,
    [Msg.S2C_OtherMoveMessage]: proto.S2C_OtherMoveMessage,
    [Msg.S2C_TeamJoinMessage]: proto.S2C_TeamJoinMessage,
    [Msg.S2C_TeamInviteMessage]: proto.S2C_TeamInviteMessage,
    [Msg.S2C_JumpMapMessage]: proto.S2C_JumpMapMessage,
    [Msg.S2C_PlayerMoveMessage]: proto.S2C_PlayerMoveMessage,
    [Msg.S2C_ScenePlayerEventMessage]: proto.S2C_ScenePlayerEventMessage,
    [Msg.S2C_PlayerEventChooseMessage]: proto.S2C_PlayerEventChooseMessage,
    [Msg.S2C_GetPlayerEventChooseResultMessage]: proto.S2C_GetPlayerEventChooseResultMessage,
    [Msg.S2C_BroadcastTeamJoinMessage]: proto.S2C_BroadcastTeamJoinMessage,
    [Msg.S2C_BroadcastTeamLeaveMessage]: proto.S2C_BroadcastTeamLeaveMessage,
    [Msg.S2C_ExitTeamMessage]: proto.S2C_ExitTeamMessage,
    [Msg.S2C_RemoveMemberMessage]: proto.S2C_RemoveMemberMessage,
    [Msg.S2C_ChangeLeaderMessage]: proto.S2C_ChangeLeaderMessage,
    [Msg.S2C_BroadcastChangeLeaderMessage]: proto.S2C_BroadcastChangeLeaderMessage,
    [Msg.S2C_DisbandTeamMessage]: proto.S2C_DisbandTeamMessage,
    [Msg.S2C_BroadcastDisbandTeamMessage]: proto.S2C_BroadcastDisbandTeamMessage,
    [Msg.S2C_BroadcastEnterMapTeamMessage]: proto.S2C_BroadcastEnterMapTeamMessage,
    [Msg.S2C_OnMailOpenMessage]: proto.S2C_OnMailOpenMessage,
    [Msg.S2C_SendMailMessage]: proto.S2C_SendMailMessage,
    [Msg.S2C_MapDataMessage]: proto.S2C_MapDataMessage,
    [Msg.S2C_BagDataMessage]: proto.S2C_BagDataMessage,
    [Msg.S2C_LogoutMessage]: proto.S2C_LogoutMessage,
    [Msg.S2C_SendAwardMessage]: proto.S2C_SendAwardMessage,
    [Msg.S2C_NewMailMessage]: proto.S2C_NewMailMessage,
    [Msg.S2C_PetAddSkillSureMessage]: proto.S2C_PetAddSkillSureMessage,
    [Msg.S2C_PetSealMessage]: proto.S2C_PetSealMessage,
    [Msg.S2C_PetSkillBookLearnMessage]: proto.S2C_PetSkillBookLearnMessage,
    [Msg.S2C_ActorAttributeMessage]: proto.S2C_ActorAttributeMessage,
    [Msg.S2C_LearnSkillByShopMessage]: proto.S2C_LearnSkillByShopMessage,
    [Msg.S2C_AutoSkillSetMessage]: proto.S2C_AutoSkillSetMessage,
    [Msg.S2C_AcceptTaskMessage]: proto.S2C_AcceptTaskMessage,
    [Msg.S2C_SubmitTaskMessage]: proto.S2C_SubmitTaskMessage,
    [Msg.S2C_DeleteTaskMessage]: proto.S2C_DeleteTaskMessage,
    [Msg.S2C_ClientCheckMessage]: proto.S2C_ClientCheckMessage,
    [Msg.S2C_ApplyLoginNoticeMessage]: proto.S2C_ApplyLoginNoticeMessage,
    [Msg.S2C_LoginMessage]: proto.S2C_LoginMessage,
    [Msg.S2C_GetAreaLinesMessage]: proto.S2C_GetAreaLinesMessage,
    [Msg.S2C_ErrorMessage]: proto.S2C_ErrorMessage,
    [Msg.S2C_SelectAreaMessage]: proto.S2C_SelectAreaMessage,
    [Msg.S2C_SyncServerTimeMessage]: proto.S2C_SyncServerTimeMessage,

}