import MdrBase from "../../gameCore/MdrBase";
import { Layer } from "../../Layer";
import MainView from "../view/MainView";
import LevelOpen, { LevelOpenId, LevelOpenProtoId } from "../../LevelOpen";
import { GUtil } from "../../gameCore/GUtil";
import UIHandler from "../../UIHandler";
import { ModName } from "../../viewType/ModName";
import { BuddyViewType } from "../../viewType/BuddyViewType";
import { SailingViewType } from "../../viewType/SailingViewType";
import { ActivityDataType, ActivityRwState, ActType } from "../../activity/define/ActivityDef";
import ActivityOpenData from "../../activity/data/ActivityOpenData";
import TimeMgr from "../../gameCore/TimeMgr";
import Tween from "../../gameCore/Tween";
import GameWorld from "../../GameWorld";
import { ProxyType } from "../../viewType/ProxyType";
;
import MainProxy from "../MainProxy";
import facade from "../../gameCore/Facade";
import { MainViewType } from "../../viewType/MainViewType";
import { ActivityViewType } from "../../viewType/ActivityViewType";
import { TowerViewType } from "../../viewType/TowerViewType";
import { FinalBossViewType } from "../../viewType/FinalBossViewType";
import { RichManViewType } from "../../viewType/RichManViewType";
import { LegionViewType } from "../../viewType/LegionViewType";
import { RedPacketViewType } from "../../viewType/RedPacketViewType";
import { MissionEvent } from "../../mission/define/MissionEvent";
import { SceneEvent } from "../../scene/define/SceneEvent";
import { BagEvent } from "../../bag/define/BagEvent";
import { MainEvent } from "../define/MainEvent";
import { MailEvent } from "../../mail/define/MailEvent";
import { ChatEvent } from "../../chat/define/ChatEvent";
import { RoleEvent } from "../../role/define/RoleEvent";
import { BattleEvent } from "../../battle/define/BattleEvent";
import { ShopEvent } from "../../shop/define/ShopEvent";
import { ChargeEvent } from "../../charge/define/ChargeEvent";
import { OnLineReWardEvent } from "../../activity/define/OnLineReWardEvent";
import { SystemEvent } from "../../system/define/SystemEvent";
import { BookGroupEvent } from "../../raiders/define/BookGroupEvent";
import { CampWarEvent } from "../../campwar/define/CampWarEvent";
import { SDK_EVENT } from "../../gameCore/SDK_EVENT";
import { LauncherEvent } from "../../login/define/LauncherEvent";
import { ActivityEvent } from "../../activity/define/ActivityEvent";
import { Tool } from "../../gameCore/Tool";
import { Tips } from "../../otherCmpt/common/Tips";
import City from "../../city/data/City";
import { CampWarState, Define } from "../../gameCore/Define";
import { UIAction } from "../../otherCmpt/common/UIAction";
import { ChargeType } from "../../viewType/ChargeType";
import { MainPlayerAi } from "../../MainPlayerAi";
import Notice from "../data/Notice";
import { LocalStorageKey } from "../../login/define/LoginEnums";
import PlayerBag from "../../bag/data/PlayerBag";
import { RoleGodData } from "../../role/data/RoleGodData";
import { ListChatItem } from "../../ListChatItem";
import { RedButton } from "../../otherCmpt/common/RedButton";
import ActivityModel from "../../activity/data/ActivityModel";
import TimeUtil from "../../gameCore/TimeUtil";
import { FinalBossData } from "../../finalBoss/data/FinalBossData";
import ChatMsg from "../../chat/data/ChatMsg";
import NPCVo from "../../vo/NPCVo";
import TextUtil from "../../gameCore/TextUtil";
import { SignSrc } from "../../mission/view/MissionSign";
import StringBuffer from "../../gameCore/StringBuffer";
import GameText from "../../gameCore/GameText";
import WorldMessage from "../../chat/data/WorldMessage";
import MsgHandler from "../../MsgHandler";
import { delayCall } from "../../delay";

import { HintComp } from "../../otherCmpt/common/HintComp";
import OnLineReWard from "../../activity/data/OnLineReWard";
import { CountryViewType } from "../../viewType/CountryViewType";
import { BeStrongerViewType } from "../../viewType/BeStrongerViewType";
import LoginLotteryDraw from "../../LoginLotteryDraw";
import PlayerTurnMonster from "../../PlayerTurnMonster";

import GameText2 from "../../gameCore/GameText2";
import { ChatViewType } from "../../viewType/ChatViewType";
import { SystemViewType } from "../../viewType/SystemViewType";
import WorldMap from "../../worldMap/data/WorldMap";
import { BagViewType } from "../../viewType/BagViewType";
import { MailViewType } from "../../viewType/MailViewType";
import { RelationViewType } from "../../viewType/RelationViewType";
import CostReward from "../../CostReward";
import { TeamViewType } from "../../viewType/TeamViewType";
import { RoleViewType } from "../../viewType/RoleViewType";
import { ConquerBossSt } from "../../activity/mdr/ActivityConquerBossMdr";
import { CampWarViewType } from "../../viewType/CampWarViewType";
import { AchievementEvent } from "../../achievement/define/AchievementEvent";
import GameNT from "../../gameCore/GameNT";

const MENU_BOTTOM = 15
const MENU_TOP = 30

export class MainMdr extends MdrBase<MainView, any> {

    public btnStrongerEft: number
    public nextPlayTime: number
    public nextHintTwPlayTime: number
    public autoClearEquipTime: number
    public bulkTime: number

    private _proxy: MainProxy

    public listChar: eui.ArrayCollection
    public activityTypeList: number[]
    public activityBtnList: eui.Button[]
    public payBtnList: eui.Button[]

    constructor() {
        super(Layer.main);
        this.mark("_view", MainView);
        this.btnStrongerEft = 0;
        this.nextPlayTime = 0;
        this.nextHintTwPlayTime = 0;
        this.autoClearEquipTime = 0;
        this.bulkTime = 0;
    }

    public onInit() {
        super.onInit()
        this._view.grp_bottom.cacheAsBitmap = true;
        this._view.grp_left.cacheAsBitmap = true;
        this.isNotOpenUI = true;
        this._proxy = this.getProxy(ProxyType.Main);
        this._view.percentWidth = 100
        this._view.percentHeight = 100
        this._view.comMoney_1.setType(proto.ModelConst.Type.MONEY1)
        this._view.comMoney_2.setType(proto.ModelConst.Type.MONEY2)
        this._view.comMoney_3.setType(proto.ModelConst.Type.MONEY3)
        this.listChar = new eui.ArrayCollection()
        this._view.list_chat.itemRenderer = ListChatItem
        this._view.list_chat.useVirtualLayout = false
        this._view.list_chat.dataProvider = this.listChar
        this._view.scroller_chat.viewport = this._view.list_chat
        this.updateMenu()
        this.activityTypeList = [ActivityOpenData.ACTIVITY_FULI, ActivityOpenData.ACTIVITY_OPEN, ActivityOpenData.ACTIVITY_REWARD, ActivityOpenData.ACTIVITY_SEVENDAY, ActivityOpenData.ACTIVITY_LOTTERY, ActivityOpenData.ACTIVITY_MISSION, ActivityOpenData.ACTIVITY_SEND_SHOP, ActivityOpenData.ACTIVITY_SHARE, ActivityOpenData.ACTIVITY_CHARGE, ActivityOpenData.ACTIVITY_DAILY, ActivityOpenData.ACTIVITY_OPTIONAL, ActivityOpenData.ACTIVITY_NEW, ActivityOpenData.TOWER, ActivityOpenData.RICH_MAN, ActivityOpenData.RED_PACKET, ActivityOpenData.PET_PK, ActivityOpenData.FINAL_BOSS, ActivityOpenData.GAME_CLUB, ActivityOpenData.CHARGE_GIFT]
        this.activityBtnList = [this._view.btnFuLi, this._view.btnOpen, this._view.btnReward, this._view.btnCeremony, this._view.btnLottery, this._view.btnNewTask, this._view.btnSendShop, this._view.btnShare, this._view.btnCharge, this._view.btnDaily, this._view.btnOptional, this._view.btnAct, this._view.btnTower, this._view.btnRichman, this._view.btnRedPacket, this._view.btnPetBattle, this._view.btnFinalBoss, this._view.btn_gameClub, this._view.btnChargeGift]
        this.payBtnList = [this._view.btnCharge, this._view.btnFuLi, this._view.btnSendShop, this._view.btnOptional, this._view.btnChargeGift, this._view.btnBulk]
        this._view.btnShop.visible = this._view.btnShop.includeInLayout = go.openPay
        if (this._view.btn_recorder) {
            this._view.btn_recorder.visible = true
            this._view.btn_recorder.includeInLayout = true
        }
        if (this._view.btnGotoCity) {
            this._view.btnGotoCity.visible = true
            this._view.btnGotoCity.includeInLayout = true
        }
        if (this._view.btn_help) {
            this._view.btn_help.visible = true;
            this._view.btn_help.includeInLayout = true;
        }
        this.updateChargeBtn();
        this.updateSendToDeskBtn()

        const login = facade.getProxy(ModName.Login, ProxyType.Login)
        this._view.btnMail.setRed = login.data.plrData.unreadMail
    }

    public updateChargeBtn() {
        this._view.grp_act.visible = go.openPay;
    };

    public updateSendToDeskBtn() {
        var e = this._view.btn_sendToDesk;
        e.visible = false;
        return void (e.includeInLayout = false);
    };

    public onTapSendToDesk() {
        facade.showView(ModName.Main, MainViewType.DESK);
    };

    public onTapAct() {
        UIHandler.showWin(ModName.Activity, ActivityViewType.ActivityMain2);
    };

    public onTapTower() {
        UIHandler.showWin(ModName.Tower, TowerViewType.TowerMain);
    };

    public onTapFinalBoss() {
        UIHandler.showWin(ModName.FinalBoss, FinalBossViewType.Main);
    };

    public onTapRichman() {
        UIHandler.showWin(ModName.RichMan, RichManViewType.RichManMain);
    };

    public onTapLegion() {
        UIHandler.showWin(ModName.Legion, LegionViewType.LegionMain);
    };

    public onTapRedPacket() {
        UIHandler.showWin(ModName.RedPacket, RedPacketViewType.RedPacketTask);
    };

    public updateMenu() {
        var e = this._proxy.model.menuOpen;
        this._view.switchImg.source = e ? "common_btn_caidan2" : "common_btn_caidan";
        var t = this._view.grp_bottom,
            i = this._view.grp_top,
            o = this._view.grp_left,
            n = this._view.grp_right;
        t.visible = e;
        i.visible = e;
        o.visible = e;
        n.visible = e;
        t.bottom = e ? MENU_BOTTOM : -t.height;
        i.top = e ? MENU_TOP : -i.height;
        o.left = e ? 10 : -o.width;
        n.right = e ? 10 : -n.width;
        this.updateSwitchBtnHint();
    };

    public updateSwitchBtnHint() {
        if (this._proxy.model.menuOpen) return void (this._view.hint.visible = false);
        for (var e in this._view) {
            var t = this._view[e];
            if (t instanceof RedButton && t.visible && t.hint && t.hint.visible) return void (this._view.hint.visible = true);
        }
        this._view.hint.visible = false;
    };

    public onTapView() {
        this._view.groupSelfShop.visible && (this._view.groupSelfShop.visible = false);
    };

    public levelOpenButton() {
        var e = this;
        e.addLevelOpenButton(LevelOpenId.PetView, e._view.btnPet, true);
        e.addLevelOpenButton(LevelOpenId.MainBattleActo, e._view.btnAutoFight, true);
        e.addLevelOpenButton(LevelOpenId.MailView, e._view.btnMail, true);
        e.addLevelOpenButton(LevelOpenId.CityView, e._view.btnCity, true);
        e.addLevelOpenButton(LevelOpenId.WorldMap, e._view.btnMap, true);
        e.addLevelOpenButton(LevelOpenId.DrugModel, e._view.btnUseItem, true);
        e.addLevelOpenButton(LevelOpenId.ChatView, e._view.btnChatIcon, true);
        e.addLevelOpenButton(LevelOpenId.TeamView, e._view.btnAround, true);
        e.addLevelOpenButton(LevelOpenId.PetBattle, e._view.btnPetBattle, true);
        e.addLevelOpenButton(LevelOpenId.DigHole, e._view.btnDigHole, true);
        e.addLevelOpenButton(LevelOpenId.CountryView, e._view.btnManor, true);
        e.addLevelOpenButton(LevelOpenId.DailyView, e._view.btnDaily, true);
        e.addLevelOpenButton(LevelOpenId.MainbtnAuto, e._view.btnAutoRole, true);
        e.addLevelOpenButton(LevelOpenId.MainCity, e._view.btnGotoCity, true);
        e.addLevelOpenButton(LevelOpenId.Legion, e._view.btnLegion, true);
        e.addLevelOpenButton(LevelOpenId.FinalBoss, e._view.btnFinalBoss, true);
        e.addLevelOpenButton(LevelOpenId.Hint, e._view.btnHint, true);
        e.addLevelOpenButton(LevelOpenId.NewMission, e._view.btnNewTask, true);
        e.addLevelOpenButton(LevelOpenId.Stronger, e._view.btn_beStronger, true);
        e.addLevelOpenButton(LevelOpenId.Energy, e._view.btnBlood, true);
        e.addLevelOpenButton(LevelOpenId.CrossPla, e._view.btnCrossPla, true);
        LevelOpen.ins.mandatoryAnim([LevelOpenProtoId.PetView, LevelOpenProtoId.BattleActo, LevelOpenProtoId.MailView, LevelOpenProtoId.CityView, LevelOpenProtoId.DrugModel, LevelOpenProtoId.TeamView, LevelOpenProtoId.PetBattle, LevelOpenProtoId.DigHole, LevelOpenProtoId.CountryView, LevelOpenProtoId.DailyView, LevelOpenProtoId.MainCity, LevelOpenProtoId.MainbtnAuto, LevelOpenProtoId.ChatView, LevelOpenProtoId.Legion, LevelOpenProtoId.FinalBoss, LevelOpenProtoId.Hint, LevelOpenProtoId.NewMission, LevelOpenProtoId.Stronger, LevelOpenProtoId.Energy, LevelOpenProtoId.CrossPla]);
    }

    public addListeners() {
        this.levelOpenButton();
        this.onEgret(this._view, egret.TouchEvent.TOUCH_TAP, this.onTapView, this);
        this.onEgret(this._view.btnFixed, egret.TouchEvent.TOUCH_TAP, this.topFixed, this);
        this.onEgret(this._view.btnLottery, egret.TouchEvent.TOUCH_TAP, this.topLottery, this);
        this.onEgret(this._view.btnWorldBuff, egret.TouchEvent.TOUCH_TAP, this.tapWorldBuff, this);
        this.onEgret(this._view.btnFormation, egret.TouchEvent.TOUCH_TAP, this.tapFormtion, this);
        this.onEgret(this._view.btnHint, egret.TouchEvent.TOUCH_TAP, this.tapHint, this);
        this.onEgret(this._view.btnOpen, egret.TouchEvent.TOUCH_TAP, this.tabOpen, this);
        this.listen(this._view.btnFuLi, this.tapFuLi);
        this.listen(this._view.btn_help, this.onTapHelp);
        this.listen(this._view.btnDigHole, this.onBtnDigHole);
        this.listen(this._view.btnBulk, this.onBtnBulk);
        this.onEgret(this._view.btnReward, egret.TouchEvent.TOUCH_TAP, this.tabReward, this);
        this.onEgret(this._view.btnBlood, egret.TouchEvent.TOUCH_TAP, this.onBtnblood, this);
        this.onEgret(this._view.btnOptional, egret.TouchEvent.TOUCH_TAP, this.onBtnOptional, this);
        this.onEgret(this._view.btnChargeGift, egret.TouchEvent.TOUCH_TAP, this.onBtnChargeGift, this);
        this.onEgret(this._view.btnSevenDay, egret.TouchEvent.TOUCH_TAP, this.onClickSevenDay, this);
        this.onEgret(this._view.btnBoss, egret.TouchEvent.TOUCH_TAP, this.onClickBoss, this);
        this.onEgret(this._view.scroller_chat, egret.TouchEvent.TOUCH_TAP, this.tapChat, this);
        this.onEgret(this._view.btnRole, egret.TouchEvent.TOUCH_TAP, this.tapRole, this);
        this.onEgret(this._view.btnHead, egret.TouchEvent.TOUCH_TAP, this.tapSystem, this);
        this.onEgret(this._view.btnPetHead, egret.TouchEvent.TOUCH_TAP, this.tapPetForBattle, this);
        this.onEgret(this._view.btnBag, egret.TouchEvent.TOUCH_TAP, this.tapBag, this);
        this.onEgret(this._view.btnPet, egret.TouchEvent.TOUCH_TAP, this.tapPet, this);
        this.onEgret(this._view.btnMission, egret.TouchEvent.TOUCH_TAP, this.tapMission, this);
        this.onEgret(this._view.btnUseItem, egret.TouchEvent.TOUCH_TAP, this.tapUseItem, this);
        this.onEgret(this._view.btnMap, egret.TouchEvent.TOUCH_TAP, this.tapMap, this);
        this.onEgret(this._view.btnMail, egret.TouchEvent.TOUCH_TAP, this.tapMail, this);
        this.onEgret(this._view.btnAround, egret.TouchEvent.TOUCH_TAP, this.onNear, this);
        this.onEgret(this._view.btnManor, egret.TouchEvent.TOUCH_TAP, this.tapManor, this);
        this.onEgret(this._view.btnCharge, egret.TouchEvent.TOUCH_TAP, this.onBtnCharge, this);
        this.onEgret(this._view.btnShop, egret.TouchEvent.TOUCH_TAP, this.tapShop, this);
        this.onEgret(this._view.btnNt, egret.TouchEvent.TOUCH_TAP, this.onTapNt, this);
        this.onEgret(this._view.btnCeremony, egret.TouchEvent.TOUCH_TAP, this.tabCeremony, this);
        this.onEgret(this._view.btn_beStronger, egret.TouchEvent.TOUCH_TAP, this.onTapBeStronger, this);
        this.onEgret(this._view.btnPower, egret.TouchEvent.TOUCH_TAP, this.onTapBeStronger, this);
        this.onEgret(this._view.btnNewTask, egret.TouchEvent.TOUCH_TAP, this.tapNewTask, this);
        this.onEgret(this._view.btnDaily, egret.TouchEvent.TOUCH_TAP, this.tapDaily, this);
        this.onEgret(this._view.btnPetBattle, egret.TouchEvent.TOUCH_TAP, this.tapPetBattle, this);
        this.onEgret(this._view.btnShopGoods, egret.TouchEvent.TOUCH_TAP, this.tapShopGoods, this);
        this.onEgret(this._view.btnShopRecord, egret.TouchEvent.TOUCH_TAP, this.tapShopRecord, this);
        this.onEgret(this._view.btnStopShop, egret.TouchEvent.TOUCH_TAP, this.tapStopShop, this);
        this.onEgret(this._view.btnStarShop, egret.TouchEvent.TOUCH_TAP, this.tapStarShop, this);
        this.onEgret(this._view.btnCrossPla, egret.TouchEvent.TOUCH_TAP, this.tapCrossPla, this);
        this.onEgret(this._view.lab_misContent, egret.TouchEvent.TOUCH_TAP, this.onTapMission, this);
        this.onEgret(this._view.btnSwitch, egret.TouchEvent.TOUCH_TAP, this.onSwitchMenu, this);
        this.listen(this._view.btnTeam, this.onTeam);
        this.listen(this._view.btnRevert, this.onTapRevert);
        this.listen(this._view.btnShare, this.tapShare);
        this.listen(this._view.btnAutoFight, this.onTapBtnAutoFight);
        this.listen(this._view.btnAutoRole, this.onTapBtnAutoRole);
        this.listen(this._view.btnVip, this.onTapVipBtn);
        this.listen(this._view.btn_gameClub, this.onTapGameClubBtn);
        this.listen(this._view.btn_revisitGuide, this.onTapRevisitGuideBtn);
        this.listen(this._view.btnSendShop, this.onTapSend);
        this.listen(this._view.btnCity, this.onTapCity);
        this.listen(this._view.btnFinalBoss, this.onTapFinalBoss);
        this.listen(this._view.btn_sendToDesk, this.onTapSendToDesk);
        this.listen(this._view.btnAct, this.onTapAct);
        this.listen(this._view.btnTower, this.onTapTower);
        this.listen(this._view.btnCampWar, this.onTapCampWar);
        this.listen(this._view.btnSailing, this.enterSailing);
        this.listen(this._view.btnBuddy, this.enterBuddy);
        this.listen(this._view.btnRichman, this.onTapRichman);
        this.listen(this._view.btnRedPacket, this.onTapRedPacket);
        this.listen(this._view.btnLegion, this.onTapLegion);
        this.listen(this._view.btnForHelp, this.onBtnForHelp);
        this.onNt(MissionEvent.ON_MAIN_MISSION_UPDATE, this.onMissionUpdate, this);
        this.onNt(SceneEvent.ON_NPC_SIGN_UPDATE, this.onMissionUpdate, this);
        this.onNt(MailEvent.MAIL_RP, this.updateMailRp);
        this.onNt(BagEvent.BAG_ITEM_UPDATE, this.onMissionUpdate, this);
        this.onNt(MainEvent.ON_MAIN_ICON, this.updateIcon);
        this.onNt(ChatEvent.ADD_MAIN_CHAT, this.upCharView);
        this.onNt(RoleEvent.ROLE_COMBATPOWER, this.upRoleInfo);
        this.onNt(BagEvent.BAG_CHANGE_DURABILITY, this.updateFixed);
        this.onNt(BattleEvent.ON_BATTLE_START, this.onBattleStart, this);
        this.onNt(BattleEvent.ON_BATTLE_END, this.onBattleEnd, this);
        this.onNt(MainEvent.ON_BUFF_ICON, this.updateWorldBuff, this);
        this.onNt(ShopEvent.SHOP_SELFSHOP_OPEN, this.updateSelfShop);
        this.onNt(ShopEvent.SHOP_SELFSHOP_STOP, this.updateSelfShop);
        this.onNt(SceneEvent.ON_MODE_UPDATE, this.updateSelfShop);
        this.onNt(MainEvent.ON_BAG_HINT, this.onBagHintEvent, this);
        this.onNt(MainEvent.ON_HITE_SHOW, this.onHintShowEvent, this);
        this.onNt(RoleEvent.ROLE_ATTR_UPDATE, this.onRoleHintEvent, this);
        this.onNt(RoleEvent.ROLE_GENRE_POINT, this.onRoleHintEvent, this);
        this.onNt(MainEvent.ON_PLAYER_ATTR_UPDATE, this.onRoleHintEvent, this);
        this.onNt(MainEvent.ON_UPDATE_HINT, this.onUpdateHint, this);
        this.onNt(MainEvent.PLAYER_FORMATION, this.updateWorldBuff, this);
        this.onNt(SceneEvent.UPDATE_BTN_AUTO_ROLE, this.updateBtnAutoRole, this);
        this.onNt(OnLineReWardEvent.ONLINEREWARD_RED, this.btnRewardRed, this);
        this.onNt(SystemEvent.SYSTEM_SETTING_UPDATE, this.onSystemSettingUpdate, this);
        this.onNt(MainEvent.BTN_SWITCH, this.updateMenu);
        this.onNt(ChargeEvent.TOTAL_CHARGE_UPDATE, this.updateVipBtn, this);
        this.onNt(MainEvent.ON_OPEN_LEVEL_UP, this.updateOpenLevel, this);
        this.onNt(MainEvent.ON_GAME_SPEED_UPDATE, this.updateBtnSpeed, this);
        this.onNt(MainEvent.ON_LEVEL_UP, this.upRoleLevel, this);
        this.onNt(MainEvent.ON_MAIN_UP_ICON, this.upRoleLevel, this);
        this.onNt(AchievementEvent.ACHIEVEMENT_RED, this.upCityRed);
        this.onNt(BookGroupEvent.bookGroupRed, this.upCityRed);
        this.onNt(CampWarEvent.CAMPWAR_ENTER_TOGGLE, this.enterCampWar);
        this.onNt(MainEvent.ON_PLAYER_ATTR_UPDATE, this.onPlayerAttrUpdate, this);
        this.onNt("guideTest", this.regGuide);
        this.onNt(LauncherEvent.ON_RESIZE, this.updateExp, this);
        this.onNt(MainEvent.ON_LEVEL_MAIN_BUTTON, this.levelOpenButton, this);
        this.onNt(MainEvent.MAIN_BLOOD_BOTTLE, this.onBloodBottle, this);
        this.onNt(MainEvent.MAIN_BTN_AUTO_FIGHT, this.updateBtnAutoFight, this);
        this.onNt(BattleEvent.UPDATE_AUTO_FIGHT, this.updateBtnAutoFight, this);
        this.onNt(SDK_EVENT.PAY_STATUS_CHANGE, this.updateChargeBtn, this);
        this._view.btnGotoCity && this.listen(this._view.btnGotoCity, this.onGotoCity);
        this.listen(this._view.btn_recorder, this.onTapRecorder);
        this.onNt(SDK_EVENT.ON_RECORDER_START, this.onRecorderStart, this);
        this.onNt(SDK_EVENT.ON_RECORDER_END, this.onRecorderEnd, this);
        this.onNt(SceneEvent.ON_PET_DEL, this.onPetUpdate, this);
        this.onNt(SceneEvent.ON_PET_ADD, this.onPetUpdate, this);
        //e.onNt(MainEvent.SEND_DESK, e.dealSendToDesk, e);
        this.onNt(MainEvent.HKT_KeFu_HINT, this.updateHelpHint, this);
        this.onNt(SceneEvent.SCENE_CHANGE, this.onSceneChanged, this);
        this.onNt(ActivityEvent.ACTIVIITY_BULKINFO, this.onBulkInfo, this);
        this.onNt(ActivityEvent.ACTIVIITY_SEVENDAY_REWARD_INFO, this.onSevenDay, this);
        this.onNt(ActivityEvent.ACTIVIITY_SEVENDAY_REWARD_GET, this.onSevenDayGetRw, this);
        this.onNt(ActivityEvent.ACTIVIITY_CONQUER_BOSS_INFO, this.onConquerBoss, this);
        this.onNt(ActivityEvent.ACTIVIITY_CONQUER_BOSS_REWARD, this.onConquerBossGetRw, this);
        this.onNt(MissionEvent.ON_MISSION_ICON_SHOW, this.showGuidTask, this);
        this.onNt(MissionEvent.ON_MISSION_ICON_SHOW, this.showGuidTask, this);

        this.onEgret(this._view, egret.Event.RESIZE, this.updateExp, this);
    }

    public updateHelpHint(e) {
    }

    public onPetUpdate() {
        this._view.btnPetHead.visible = null != GameWorld.myPlayer.pet;
    };

    public onRecorderStart() {
        this._view.btn_recorder.icon = "zhu_icon_luz_d";
        this._view.btn_recorder.label = "停止";
    };

    public onRecorderEnd() {
        this._view.btn_recorder.icon = "zhu_icon_luz";
        this._view.btn_recorder.label = "录屏";
    };

    public onTapRecorder() {
        //gs.startRecorder();
    };

    public onTapHelp() {
        this.showView(MainViewType.GameClubAlert);
    };

    public onGotoCity() {
        return Define.isCityMap(GameWorld.orgMapID) && GameWorld.cityMasterId == GameWorld.myPlayer.id ? Tips.show("你已经在自己的城市里了") : City.doViewCityInfo(void 0, void 0, true);
    };

    public onTapCity() {
        UIHandler.showWin(ModName.Main, MainViewType.MainCity);
    };

    public onPlayerAttrUpdate(gameNt: GameNT<proto.ModelConst.Type>) {
        const typ = gameNt.body;
        if (typ == proto.ModelConst.Type.EXP || typ == proto.ModelConst.Type.EXPMAX) {
            this.updateExp()
        }
    }

    public updateExp() {
        const plr = GameWorld.myPlayer;
        if (!plr) {
            this._view.grp_exp.visible = false
            return
        }
        this._view.grp_exp.visible = true
        const maxExp = plr.get(proto.ModelConst.Type.EXPMAX)
        const curExp = plr.get(proto.ModelConst.Type.EXP)
        const maxWidth = this._view.width;

        this._view.img_exp.width = maxExp ? curExp / maxExp * maxWidth : maxWidth
    }

    public onTapNt() {
        UIHandler.createPlayerEventUI();
    };

    public updateBtnNt() {
        var e = GameWorld.isShowPlayerEvent();
        this._view.btnNt.visible = e;
        this._view.btnNt.includeInLayout = e;
        this._view.btnNt.hint.visible = true;
    };

    public onTapSpeed() {
        UIAction.onTapSpeedBtn();
    };

    public updateBtnSpeed() {
    };

    public onTapSend() {
        UIHandler.showModal(ModName.Activity, ActivityViewType.ActivitySend);
    };

    public onTapVipBtn() {
        UIHandler.showModal(ModName.Charge, ChargeType.ChargeVip);
    };

    public updateVipBtn() {
        var e = 5e3;
        this._view.btnVip.visible = go.openPay ? LevelOpen.ins.getOpen(LevelOpenProtoId.Noble) && GameWorld.totalCharge >= e : false;
        this._view.btnVip.includeInLayout = this._view.btnVip.visible;
    };

    public upRoleLevel() {
        this.dealIcon(ActivityOpenData.ACTIVITY_LOTTERY);
        this.dealIcon(ActivityOpenData.ACTIVITY_OPTIONAL);
        this.dealIcon(ActivityOpenData.ACTIVITY_CHARGE);
        this.dealIcon(ActivityOpenData.ACTIVITY_REWARD);
        this.dealIcon(ActivityOpenData.ACTIVITY_FULI);
        this.dealIcon(ActivityOpenData.ACTIVITY_OPEN);
        this.dealIcon(ActivityOpenData.CHARGE_GIFT);
        this.dealIcon(ActivityOpenData.ACTIVITY_LOTTERY);
        this.dealIcon(ActivityOpenData.RICH_MAN);
        this.dealIcon(ActivityOpenData.TOWER);
        this.updateVipBtn();
        this.updateOpenLevel();
    };

    public upCityRed() {
        let e = false;
        if (!e) {
            const t = facade.getProxy(ModName.Achievement, ProxyType.Achievement);
            e = !!t.achievementRedPush(false);
        }
        if (!e) {
            const t = facade.getProxy(ModName.Raiders, ProxyType.Raiders);
            for (var i in t.bookGroupModel.bookGroupRed) if (t.bookGroupModel.bookGroupRed[i] && t.bookGroupModel.bookGroupRed[i].length > 0) {
                e = true;
                break;
            }
        }
        this._view.btnCity.setRed = this._view.btnCity.lock.visible ? false : e;
    };

    public onTapBtnAutoRole() {
        var e = MainPlayerAi.ins;
        e.setOpen(!e.open);
        this.updateBtnAutoRole();
        e.open && (e.updateCurMission(), e.curMission || Tips.show("当前没有可执行的挂机任务"));
    };

    public updateBtnAutoRole() {
        var e = this._view.btnAutoRole;
        e.label = MainPlayerAi.ins.open ? "关闭" : "挂机";
    };

    public onTapBtnAutoFight() {
        GameWorld.TAG_IS_AUTO_FIGHT_FLAG = !GameWorld.TAG_IS_AUTO_FIGHT_FLAG;
        this.updateBtnAutoFight();
    };

    public updateBtnAutoFight() {
        this._view.btnAutoFight.label = GameWorld.TAG_IS_AUTO_FIGHT_FLAG ? "关闭" : "自动";
    };

    public regGuide() {
        // var e = this,
        //     t = e._view;
        // e.regGuideSprs(x.GuideDefine.roleGuidelist, t.btnRole);
        // e.regGuideSpr(GuideSpriteId.bloodRevert, t.btnRevert);
        // e.regGuideSpr(GuideSpriteId.drug, t.btnUseItem);
        // e.regGuideSprs(x.GuideDefine.mainCityList, t.btnCity);
        // e.regGuideSpr(GuideSpriteId.task2, t.btnMission);
        // e.regGuideSpr(GuideSpriteId.newTaskEnter, t.btnNewTask);
        // e.regGuideSprs(x.GuideDefine.autoHangGuideList, t.btnAutoRole);
        // e.regGuideSprs([GuideSpriteId.submitTask, GuideSpriteId.cityTask], t.lab_misContent);
        // e.regGuideSpr(GuideSpriteId.euqipRepairBtn, t.btnFixed);
        // e.regGuideSprs(x.GuideDefine.mainAllGuidelist, t);
        // e.regGuideSprs(x.GuideDefine.bagGuidelist, t.btnBag);
        // e.regGuideSprs(x.GuideDefine.backCityGuidelist, t.btnGotoCity);
        // e.regGuideSpr(GuideSpriteId.countryEnter, t.btnManor);
        // e.regGuideSpr(GuideSpriteId.dailyEnter, t.btnDaily);
        // e.regGuideSpr(GuideSpriteId.petEnter, t.btnPet);
        // e.regGuideSpr(GuideSpriteId.charge, t.btnCharge);
        // e.regGuideSpr(GuideSpriteId.legionEnter, t.btnLegion);
    };

    public onUpdateFormation() {
        this._view.btnFormation.visible = false;
        this._view.btnFormation.includeInLayout = false;
    };

    public onHintShowEvent() {
        var e = egret.localStorage.getItem(LocalStorageKey.NoticeRed);
        this.setIconIncludeLayout(this._view.btnHint, Notice.list.length > 0, "true" == e);
    };

    public onUpdateHint() {
    };

    public onBagHintEvent(gameNt: GameNT<boolean>) {
        const plr = GameWorld.myPlayer
        if (!plr) return
        const bag = plr.bag
        if (!bag) return
        let is = gameNt.body
        if (this._view.btnBag.hint.visible != is) {
            if (!is) {
                for (let i = PlayerBag.BAG_START; i < plr.BagSize + PlayerBag.BAG_START; i++) {
                    const item = bag.getItem(i);
                    if (null != item && item.ShowNewSign) {
                        is = true;
                        break;
                    }
                }
            }
            this._view.btnBag.setRed = is;
        }
    }

    public onRoleHintEvent() {
        var e,
            t,
            i = GameWorld.myPlayer,
            n = i && i.get(proto.ModelConst.Type.CP) > 0;
        n || (t = facade.getProxy(ModName.Role, ProxyType.Role), !GameWorld.myPlayer.GenreIsOn() || !t.data.skillGenreData || (null === (e = t.data.skillGenreData) || void 0 === e ? void 0 : e.isUp) >= 4 ? n = false : LevelOpen.ins.getOpen(LevelOpenProtoId.RoleGenre, false) && (n = t.data.redPoint));
        n || (n = RoleGodData.getRoleGodRed());
        this._view.btnRole.setRed = n;
    };

    public onBattleStart() {
        this._view.visible = false;
    };

    public onBattleEnd() {
        this._view.visible = true;
        this.updatePlayerInfo();
    };

    public onTapRevert() {
        LevelOpen.ins.getIsOpen(LevelOpenId.DrugModel) && GameWorld.doQuickAddHP(GameWorld.myPlayer);
    };

    public tapFuLi() {
        UIHandler.createActivitInfoUI(0);
    };

    public onBtnDigHole() {
        UIHandler.createDigHoleUI();
    };

    public tapShare() {
        UIHandler.createActivityShareUI();
    };

    public updateIcon(e?) {
        var t,
            i = e ? e.body : null,
            o = this;
        if (i) {
            var n = void 0;
            switch (i) {
                case Define.ICON_TYPE_TEAM:
                    t = true;
                    break;
                case Define.IS_OPEN_TYPE_BONUS_ONLINE:
                    n = ActivityOpenData.ACTIVITY_REWARD;
                    break;
                case Define.ISO_OPEN_TYPE_LOTTERY:
                    n = ActivityOpenData.ACTIVITY_LOTTERY;
                    break;
                case Define.IS_OPEN_TYPE_ACTIVITY_ICON:
                    n = ActivityOpenData.ACTIVITY_FULI;
                    break;
                case Define.IS_OPEN_TYPE_MISSION_ICON:
                    n = ActivityOpenData.ACTIVITY_MISSION;
                    break;
                case Define.IS_OPEN_TYPE_DAILY_ICON:
                    n = ActivityOpenData.ACTIVITY_DAILY;
                    break;
                case Define.IS_OPEN_TYPE_CEREMONY_ICON:
                    n = ActivityOpenData.ACTIVITY_SEVENDAY;
                    break;
                case Define.IS_OPEN_TYPE_OPEN_ICON:
                    n = ActivityOpenData.ACTIVITY_OPEN;
                    break;
                case Define.IS_OPEN_TYPE_CHARGE_NAME:
                    n = Define.IS_OPEN_TYPE_CHARGE_NAME;
                    break;
                case Define.IS_OPEN_TYPE_SHARE_ICON:
                    n = ActivityOpenData.ACTIVITY_SHARE;
                    break;
                case ActivityOpenData.ACTIVITY_SEND_SHOP:
                    n = ActivityOpenData.ACTIVITY_SEND_SHOP;
                    break;
                case Define.IS_OPEN_TYPE_CHARGE:
                    n = ActivityOpenData.ACTIVITY_CHARGE;
                    break;
                default:
                    n = i;
            }
            null != n && o.dealIcon(n);
        } else {
            t = true;
            this.updateAllIcon();
        }
        t && this.updateTeamIcon();
        this.onShowSailing();
    };

    public updateAllIcon() {
        var e = this
        for (let t = 0; t < e.activityTypeList.length; t++) {
            e.dealIcon(e.activityTypeList[t]);
        }
    };

    public dealIcon(e) {
        var t,
            i,
            n,
            a,
            r,
            u,
            _,
            d = this,
            c = d.activityTypeList.indexOf(e);
        switch (e) {
            case ActivityOpenData.ACTIVITY_SEND_SHOP:
                if (!go.openPay) break;
                r = ActivityModel.ins.getData(ActivityDataType.SEND_SHOP, []);
                r.length > 0 && (_ = ActivityModel.ins.getData(ActivityDataType.SEND_SHOP_TIME, 0), TimeUtil.isTimeOut(_) || (n = true));
                break;
            case Define.IS_OPEN_TYPE_CHARGE_NAME:
                var p = facade.getProxy(ModName.Charge, ProxyType.Charge);
                d._view.btnCharge.labelDisplay.text = p.isMultiple.length > p.chargeItemLen ? "首充" : "充值";
                u = true;
                break;
            case ActivityOpenData.PET_PK:
                u = true;
                i = ActivityOpenData.getInstance.getType(e);
                this._view.btnPetBattle.hint.visible = i.red;
                break;
            case ActivityOpenData.GAME_CLUB:
                u = true;
                i = ActivityOpenData.getInstance.getType(e);
                this._view.btn_gameClub.hint.visible = i.red;
                break;
            case ActivityOpenData.FINAL_BOSS:
                u = true;
                var h = void 0 != FinalBossData.btnHint ? FinalBossData.btnHint : null === (t = ActivityOpenData.getInstance.getType(e)) || void 0 === t ? void 0 : t.red;
                this._view.btnFinalBoss.hint.visible = h;
                break;
            case ActivityOpenData.ACTIVITY_MISSION:
                u = true;
                i = ActivityOpenData.getInstance.getType(e);
                this._view.btnNewTask.hint.visible = i.red;
                break;
            default:
                i = ActivityOpenData.getInstance.getType(e);
                i && (n = i.isShow, a = i.red);
        }
        if (!u) {
            var f = d.activityBtnList[c];
            if (f) {
                if (-1 != d.payBtnList.indexOf(f)) {
                    d.setIconIncludeLayout(f, false, false)
                } else {
                    d.setIconIncludeLayout(f, n, a)
                    null != _ && f instanceof RedButton && (f.time = _)
                }
            }
        }
        GUtil.setLayout(d._view.btn_gameClub, true);
        GUtil.setLayout(d._view.btnShare, true);
        GUtil.setLayout(d._view.btn_revisitGuide, true);
    };

    public setIconIncludeLayout(e, t, i = false) {
        e.mandatory && (t = false);
        GUtil.setLayout(e, t);
        t && (e.setRed = e.lock.visible ? false : i);
    };

    public btnRewardRed() {
        var e = ActivityOpenData.getInstance.getType(ActivityOpenData.ACTIVITY_REWARD);
        this.setIconIncludeLayout(this._view.btnReward, e.isShow, e.red);
    };

    public upRoleInfo() {
        this._view.playerInfo.updatePlayerInfo();
    };

    public upCharView() {
        var e = ChatMsg.getChatMsgVector(Define.CHAT_TYPE_ALL),
            t = []
        for (let o = e.size() - 1; o >= 0; o--) {
            var n = e.elementAt(o),
                a = Tool.channel2Off(n.channel),
                r = GameWorld.myPlayer.isSettingBit(a);
            if (!r && (n.chatItemWidth = 600, t.unshift(e.elementAt(o)), t.length > 4)) break;
            n.name
        }
        this.listChar.source = t;
        GUtil.scrollHV(this._view.scroller_chat, this._view.list_chat, 99, 350);
    };

    public updateTeamIcon() {
        var e,
            t = GameWorld.myPlayer;
        t.isPlayerTeam() && (e = true);
        this._view.btnTeam.visible = e;
        this._view.btnTeam.includeInLayout = e;
    };

    public updatePlayerInfo() {
        this._view.playerInfo.type = UIHandler.PLAYER_INFO_WORLD;
        this._view.playerInfo.updatePlayerInfo();
    };

    public onTapMission() {
        var e = MainPlayerAi.ins.curMission;
        if (e) {
            var t = GameWorld.myPlayer;
            if (t.isCanMove()) return t.isAutoMoving ? MainPlayerAi.ins.autoMoveAsk() : NPCVo.doMissionInfoView(t, e, null, UIHandler.SUB_MISSION_VIEW);
        }
    };

    public onSceneChanged() {
        MainPlayerAi.ins.curMissionId = null;
    };

    public onMissionUpdate(e) {
        this.updateMission();
    };

    public updateMission() {
        var e = MainPlayerAi.ins.curMission;
        if (!e) {
            this._view.lab_misContent.text = "";
            this._view.img_misStatus.source = null;
            GUtil.setLayout(this._view.btnForHelp, false);
            var t = GameWorld.myPlayer.getCurMapMission(false);
            t || (t = GameWorld.myPlayer.getLocalMapMission(false, false));
            return void (t && (MainPlayerAi.ins.curMissionId = t.id));
        }
        var i = e.status;
        this._view.lab_misContent.width = void 0;
        this._view.lab_misContent.height = void 0;
        this._view.lab_misContent.textFlow = TextUtil.parseHtml(e.getMainDesc());
        this._view.img_misStatus.source = SignSrc[i + 1];
        GUtil.setLayout(this._view.btnForHelp, LevelOpen.ins.getIsOpen(LevelOpenId.ForHelp) && e.isForHelp());
        this._view.grp_mission.validateNow();
    };

    public onBtnForHelp() {
        var e = MainPlayerAi.ins.curMission;
        e && MsgHandler.askForHelp(true, e.id);
    };

    public updateFixed(e?) {
        var t = GameWorld.myPlayer
        if (!t || !t.bag) {
            return void console.log("updateFixed, bag is null !")
        }
        let hasDestroy = t.bag.isDestroyEquip(false)
        if (hasDestroy) {
            var n = new StringBuffer();
            n.append(GameText.STR_ALL_INFO_DUR_BROCKEN);
            GameWorld.myPlayer.bag.isDestroyEquip(false, n);
            WorldMessage.addPromptMsg(n.toString());
        }
        this._view.btnFixed.visible = hasDestroy;
        this._view.btnFixed.hint.visible = hasDestroy;
        this._view.btnFixed.includeInLayout = hasDestroy;
        facade.sendNt(BagEvent.BAG_FIX, hasDestroy);
    };

    public onShow() {
        super.onShow()
        UIHandler.isMainShowing = true
        this._view.visible = true
        this._view.btnBag.hint.visible = false
        this._view.groupSelfShop.visible = false
        this.updatePlayerInfo()
        this.updateMission()
        this.updateMenu()
        this.updateIcon()
        this.upCharView()
        this.updateFixed()
        this.updateWorldBuff()
        this.updateSelfShop()
        this.onRoleHintEvent()
        this.onUpdateHint()
        this.onUpdateFormation()
        this.updateBtnAutoRole()
        this.updateBtnAutoFight()
        this.onSystemSettingUpdate()
        this.updateVipBtn()
        this.onHintShowEvent()
        //this.updateExp()
        this.upCityRed()
        this.onBloodBottle()
        this.onPetUpdate()
        this.onBulkInfo()
        this.onSevenDay()
        this.onConquerBoss()
        this.onShowCampWar()
        this.onShowBuddy()
        if (!this.btnStrongerEft) {
            var i = this._view.btn_beStronger;
            i.iconDisplay.visible = false;
            i.labelDisplay.size = 20;
            this.btnStrongerEft = this.addUIAnimate("bianqiang", 27, 20, i, void 0, 3, null, 9, 1, false);
            this.nextPlayTime = TimeMgr.timer.serverTime + 1e4;
        }
        delayCall(Handler.alloc(this, this.checkLoginRecord), 500);
        TimeMgr.addUpdateItem(this, 1e3);
        this.autoClearEquipTime = TimeMgr.timer.serverTime;
        const proxy = facade.getProxy(ModName.Mission, ProxyType.Mission);
        proxy.doMissionTaskInfo();
    };

    public showGuidTask(e) {
        if (e.body && e.body.info) {
            for (var t = e.body.info, i = 0, o = t; i < o.length; i++) {
                var n = o[i];
                if (2 != n.status) return;
            }
            this.setIconIncludeLayout(this._view.btnNewTask, false, false);
        }
    };

    public checkLoginRecord() {
        var e;
        if (!go.isFight) {
            var t = facade.getProxy(ModName.Login, ProxyType.Login),
                i = null === (e = t.data.recordData) || void 0 === e ? void 0 : e.viewInfo;
            i && UIHandler.showWin(i.mName, i.vType, i.data);
        }
    };

    public playBtnStrongerEft() {
        if (this.btnStrongerEft) {
            var e = this.getUIAnimateById(this.btnStrongerEft);
            e && (e.clearTimes(), e.play(), this.nextPlayTime = TimeMgr.timer.serverTime + 1e4);
        }
    };

    public playRedBtnHintTw() {
        this.nextHintTwPlayTime = TimeMgr.timer.serverTime + 5000;
        for (const key in this._view) {
            if (this._view.hasOwnProperty(key)) {
                const prop = this._view[key];
                if (prop instanceof RedButton && prop.hint instanceof HintComp) {
                    const btn: RedButton = prop
                    btn.hint.visible && btn.hint.addTween();
                }
            }
        }
    };

    public onTapGameClubBtn() {
        this.showView(MainViewType.GameClubRew);
    };

    public onTapRevisitGuideBtn() {
        this.showView(MainViewType.GameRevisitGuide);
    };

    public autoCliearEquip() {
        this.autoClearEquipTime = TimeMgr.timer.serverTime + 18e4;
        GameWorld.automaticRepairEquip();
    };

    public onHide() {
        UIHandler.isMainShowing = false;
        this.btnStrongerEft && (this.removeUIAnimate(this.btnStrongerEft), this.btnStrongerEft = 0);
        this.nextPlayTime = 0;
        TimeMgr.removeUpdateItem(this);
        var t = this._view.grp_bottom,
            i = this._view.grp_top,
            o = this._view.grp_left,
            a = this._view.grp_right;
        Tween.remove(t);
        Tween.remove(i);
        Tween.remove(o);
        Tween.remove(a);
        this.removeLevelOpenButton();
        super.onHide()
    };

    public async checkOnlineRewardTimeCount() {
        if (OnLineReWard.totalTime > 0) {
            OnLineReWard.addTime++
            if (OnLineReWard.addTime >= OnLineReWard.totalTime) {
                OnLineReWard.onLineReWardInit.time = OnLineReWard.addTime
                OnLineReWard.onLineReWardInit.upDate()
                if (GameWorld.isAccelerate) {
                    await OnLineReWard.onLineReWardInit.doGetReward(false)
                    const e = ActivityOpenData.getInstance.getType(ActivityOpenData.ACTIVITY_REWARD);
                    this.setIconIncludeLayout(this._view.btnReward, e.isShow, e.red)
                    OnLineReWard.totalTime = 0
                }
            }
        }
    }

    public update(e) {
        this.checkOnlineRewardTimeCount();
        if (this.nextPlayTime && e.serverTime > this.nextPlayTime) {
            this.playBtnStrongerEft()
        }
        (!this.nextHintTwPlayTime || e.serverTime > this.nextHintTwPlayTime) && this.playRedBtnHintTw();
        this.bulkTime > 0 && e.serverTime > this.bulkTime && this.onBulkInfo();
        this.autoClearEquipTime && e.serverTime > this.autoClearEquipTime && this.autoCliearEquip();
        var t = GameWorld.myPlayer;
        for (var i in t.putIllusion) {
            var o = t.putIllusion[i];
            if (-1 != o) {
                var n = TimeUtil.isTimeOut(t.putIllusion[i]);
                n && (t.putIllusion[i] = 0, t.updateAndRefreshIcon(), this.sendNt(BagEvent.BAG_CHANGE), delete t.putIllusion[i]);
            }
        }
    };

    public onSwitchMenu() {
        const e = this._view.grp_bottom,
            t = this._view.grp_top,
            i = this._view.grp_left,
            o = this._view.grp_right,
            r = this._proxy.model.menuOpen;
        if (!r) {
            e.visible = true
            t.visible = true
            i.visible = true
            o.visible = true
        }
        Tween.get(e).to({ bottom: r ? -e.height : MENU_BOTTOM }, 200).exec(Handler.alloc(this, this.switchTwEnd));
        Tween.get(t).to({ top: r ? -t.height : MENU_TOP }, 200);
        Tween.get(i).to({ left: r ? -i.width : 10 }, 200);
        Tween.get(o).to({ right: r ? -o.width : 10 }, 200);
    };

    public switchTwEnd() {
        this._proxy.model.menuOpen = !this._proxy.model.menuOpen;
        this.updateMenu();
    };

    public tips() {
        Tips.show("敬请期待");
    };

    public tabReward() {
        UIHandler.createOnLineRewardUI(0);
    };

    public tabCeremony() {
    };

    public tabOpen() {
        UIHandler.createActivityOpenUI(0);
    };

    public onTapBeStronger() {
        UIHandler.showWin(ModName.BeStronger, BeStrongerViewType.BeStrongerMain, {
            subIdx: 0
        });
    };

    public tapManor() {
        UIHandler.showWin(ModName.Country, CountryViewType.CountryMain);
    };

    public topFixed() {
        var e = GameWorld.myPlayer,
            t = false;
        e && (t = e.bag.isDestroyEquip(false), t && UIAction.doMenuButton(UIHandler.EVENT_ALL_INFO_DUR_BROCKEN));
    };

    public topLottery() {
        LoginLotteryDraw.doEnter();
    };

    public topSignin() {
        UIHandler.createSigninUI();
    };

    public tapWorldBuff() {
        var e = GameWorld.myPlayer;
        if (e) {
            var t = GameWorld.doViewMyPlayerBuffInfo();
            e.formationSkill && (t += "\n" + e.formationSkill.getFormationInfo());
            return null != GameWorld.myPlayer.playerTurnMonster ? PlayerTurnMonster.doSeeTurnMonsterInfo(t) : void (t && UIHandler.alertMessage(t, "状态"));
        }
    };

    public tapHint() {
        return Notice.list.length <= 0 ? Tips.show("暂无无公告") : UIHandler.showModal(ModName.Main, MainViewType.MainHint, MainPlayerAi.ins.open);
    };

    public tapFormtion() {
        var e = GameWorld.myPlayer;
        if (e) {
            var t = e.formationSkill;
            if (t) {
                var i = GameWorld.myPlayer.isLeader(),
                    o = t.getFormationInfo();
                if (i) {
                    const n = new Vector<{ type: number, name: string }>()
                    n.addElement({ type: UIHandler.EVENT_ALL_BACK, name: GameText2.STR_FORMATION_OPTION });
                    UIHandler.createAreaMessageWin(o, n, Handler.alloc(this, function () {
                        UIHandler.openRole(e, 2);
                    }));
                } else UIHandler.alertMessage(o);
            }
        }
    };

    public tapChat() {
        UIHandler.showWin(ModName.Chat, ChatViewType.ChatAllView);
    };

    public tapRole() {
        UIHandler.openRole();
    };

    public tapBag() {
        UIHandler.openBag();
    };

    public tapSystem() {
        UIHandler.showWin(ModName.System, SystemViewType.SystemMainView);
    };

    public tapPetForBattle() {
        var e = facade.getProxy(ModName.Pet, ProxyType.Pet);
        e.openBattlePet();
    };

    public tapAddAttr() {
        UIHandler.openRole(GameWorld.myPlayer, 1);
    };

    public tapPet() {
        UIHandler.openPet();
    };

    public tapMission() {
        var e = GameWorld.myPlayer;
        if (e) return e.isAutoMoving ? MainPlayerAi.ins.autoMoveAsk() : UIHandler.createMissionMainUI(0);
    };

    public tapUseItem() {
        UIHandler.showModal(ModName.Bag, BagViewType.UseItem);
    };

    public tapShop() {
        GameWorld.doBrowseShop(Define.SHOP_TYPE_50);
    };

    public tapMap() {
        WorldMap.Instance.doEnterWorldMap();
    };

    public tapMail() {
        UIHandler.showWin(ModName.Mail, MailViewType.Mail);
    };

    public tapDeal() {
        var e = GameWorld.myPlayer;
        null == e || e.isShopMode() || UIHandler.createGoodsUI(false);
    };

    public updateMailRp(nt: GameNT<boolean>) {
        this._view.btnMail.setRed = nt.body
    };

    public onBtnCharge() {
        UIHandler.createChargeUI(0);
    };

    public onBtnChargeActivity() {
        UIHandler.showWin(ModName.Charge, ChargeType.ChargeActivity);
    };

    public onBtnAchievement() {
        UIHandler.createAchieveManageUI();
    };

    public onBtnRank() {
        UIHandler.createRankListUI();
    };

    public tapRelation() {
        UIHandler.showWin(ModName.Relation, RelationViewType.RelationMain);
    };

    public tapShopGoods() {
        UIHandler.openPlayerShop();
    };

    public tapStarShop(e) {
        var t = this._view.groupSelfShop;
        t.visible = !t.visible
        if (t.visible) {
            var i = this._view.btnStarShop,
                o = this._view.grp_right.localToGlobal(i.x, i.y),
                n = this._view.globalToLocal(o.x, o.y);
            t.x = n.x - t.width;
            t.y = n.y - 15;
            t.visible = true;
            e.stopPropagation();
        } else t.visible = false;
    };

    public tapCrossPla() {
        UIHandler.createCrossPla();
    };

    public tapNewTask() {
        UIHandler.createNewMission();
    };

    public tapDaily() {
        UIHandler.createDailyMission();
    };

    public tapPetBattle() {
        UIHandler.createPetBattle();
    };

    public tapShopRecord() {
        GameWorld.doStallRecord();
    };

    public tapStopShop() {
        var e = GameWorld.myPlayer;
        e && (e.isShopMode() ? GameWorld.doStallEnd() : Tips.show("请先开始摆摊"));
    };

    public tapCostReward() {
        CostReward.isCostRewardOPen && UIHandler.createCostRewardUI();
    };

    public onNear() {
        UIHandler.createTeamUI();
    };

    public onTeam() {
        UIHandler.showWin(ModName.Team, TeamViewType.TeamMain, {
            subIdx: 1
        });
    };

    public updateWorldBuff() {
        var e = GameWorld.myPlayer;
        if (e) {
            var t = e.showBuffIcon();
            this._view.btnWorldBuff.visible = t;
            this._view.btnWorldBuff.includeInLayout = t;
        }
    };

    public updateSelfShop() {
        var e = GameWorld.myPlayer;
        this._view.btnStarShop.visible = e.isShopMode();
        this._view.btnStarShop.includeInLayout = e.isShopMode();
        this._view.btnShopRecord.visible = e.isShopMode();
        this._view.btnShopRecord.includeInLayout = e.isShopMode();
        this._view.btnShopGoods.visible = e.isShopMode();
        this._view.btnShopGoods.includeInLayout = e.isShopMode();
        this._view.btnStopShop.visible = e.isShopMode();
        this._view.btnStopShop.includeInLayout = e.isShopMode();
    };

    public onSystemSettingUpdate() {
    };

    public onBloodBottle() {
        var e = facade.getProxy(ModName.Role, ProxyType.Role),
            t = e.energyInfo;
        return t ? void (this._view.btnBlood.icon = t.leftEnergy < t.belowTips ? "zhu_icon_nengliangw" : "zhu_icon_nengliang") : void (this._view.btnBlood.icon = "zhu_icon_nengliang");
    };

    public onBtnblood() {
        UIHandler.showModal(ModName.Role, RoleViewType.RoleBlood);
    };

    public onBtnOptional() {
        UIHandler.openOptonalOne();
    };

    public onBtnChargeGift() {
        UIHandler.showModal(ModName.Activity, ActivityViewType.ActivityGiftBag);
    };

    public onSevenDay() {
        var e = facade.getProxy(ModName.Activity, ProxyType.Activity),
            t = e.sdInfo;
        if (t) {
            for (var i = false, n = 0, a = e.sdInfo.infos; n < a.length; n++) {
                var r = a[n];
                if (r.state == ActivityRwState.GET) {
                    i = true;
                    break;
                }
            }
            this.setIconIncludeLayout(this._view.btnSevenDay, true, i);
            i && this.onClickSevenDay();
        } else this.onSevenDayRemove();
    };

    public onSevenDayGetRw() {
        var e = facade.getProxy(ModName.Activity, ProxyType.Activity),
            t = e.sdInfo,
            i = true;
        if (t) {
            for (var n = false, a = 0, r = e.sdInfo.infos; a < r.length; a++) {
                var l = r[a];
                l.state == ActivityRwState.GET && (n = true);
                l.state != ActivityRwState.HASGET && (i = false);
            }
            this.setIconIncludeLayout(this._view.btnSevenDay, true, n);
        }
        i && this.onSevenDayRemove();
    };

    public onSevenDayRemove() {
        UIHandler.hideView(ModName.Activity, ActivityViewType.ActivitySevenDay);
        this.setIconIncludeLayout(this._view.btnSevenDay, false, false);
    };

    public onClickSevenDay() {
        UIHandler.showModal(ModName.Activity, ActivityViewType.ActivitySevenDay);
    };

    public onConquerBoss() {
        var e = facade.getProxy(ModName.Activity, ProxyType.Activity),
            t = e.bsInfo;
        if (t) {
            for (var i = false, n = 0, a = t.infos; n < a.length; n++) {
                var r = a[n];
                if (r.state == ConquerBossSt.GET) {
                    i = true;
                    break;
                }
            }
            this.setIconIncludeLayout(this._view.btnBoss, true, i);
        } else this.onConquerBossRemove();
    };

    public onConquerBossGetRw() {
        var e = facade.getProxy(ModName.Activity, ProxyType.Activity),
            t = e.bsInfo,
            i = true;
        if (t) {
            for (var n = false, a = 0, r = t.infos; a < r.length; a++) {
                var l = r[a];
                l.state == ConquerBossSt.GET && (n = true);
                l.state != ConquerBossSt.HASGET && (i = false);
            }
            this.setIconIncludeLayout(this._view.btnBoss, true, n);
        }
        i && this.onConquerBossRemove();
    };

    public onConquerBossRemove() {
        UIHandler.hideView(ModName.Activity, ActivityViewType.ActivityConquerBoss);
        this.setIconIncludeLayout(this._view.btnBoss, false, false);
    };

    public onClickBoss() {
        UIHandler.showWin(ModName.Activity, ActivityViewType.ActivityConquerBoss);
    };

    public onBulkInfo() {
        for (var e = facade.getProxy(ModName.Activity, ProxyType.Activity), t = false, i = TimeMgr.timer.serverTime, n = 0; e.bulkInfo && n < e.bulkInfo.infos.length; n++) {
            var a = e.bulkInfo.infos[n],
                r = a.endTime.toNumber();
            r > i && (t = true, this.bulkTime = r);
        }
        t ? (GUtil.setLayout(this._view.btnBulk, true), this._view.btnBulk.hint.visible = e.getBulkRed()) : (this.bulkTime = 0, GUtil.setLayout(this._view.btnBulk, false));
    };

    public onBtnBulk() {
        UIHandler.showWin(ModName.Activity, ActivityViewType.ActivitySuperGroup);
    };

    public updateOpenLevel() {
        this.onShowCampWar();
        this.onShowSailing();
        this.onShowBuddy();
    };

    public onTapCampWar() {
        var e = facade.getProxy(ModName.CampWar, ProxyType.CampWar);
        e.sendFactionBattleToggle(0);
        e.status != CampWarState.War && UIHandler.showWin(ModName.CampWar, CampWarViewType.CampWarMain);
    };

    public enterCampWar() {
        UIHandler.showWin(ModName.CampWar, CampWarViewType.CampWarMain);
    };

    public onShowCampWar() {
        var e = LevelOpen.ins.getIsOpen(LevelOpenId.CAMPWAR, false);
        GUtil.setLayout(this._view.btnCampWar, e);
    };

    public onShowSailing() {
        var e = LevelOpen.ins.getIsOpen(LevelOpenId.SAILING, false),
            t = ActivityOpenData.getInstance.getTypeData(ActType.MARITIME_TRADE),
            i = t && t.isShow;
        GUtil.setLayout(this._view.btnSailing, e && i);
    };

    public enterSailing() {
        UIHandler.showWin(ModName.Sailing, SailingViewType.SailingMain);
    };

    public onShowBuddy() {
        var e = LevelOpen.ins.getIsOpen(LevelOpenId.BUDDY, false);
        GUtil.setLayout(this._view.btnBuddy, e);
    };

    public enterBuddy() {
        UIHandler.showWin(ModName.Buddy, BuddyViewType.BuddyDraw);
    };
}
