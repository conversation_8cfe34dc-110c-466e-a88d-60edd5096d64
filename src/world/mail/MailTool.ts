import facade from '../gameCore/Facade'
import { ModName } from '../viewType/ModName'
import { ProxyType } from '../viewType/ProxyType'
import ProtocolDefine from '../ProtocolDefine'
import MsgHandler from '../MsgHandler'
import { Tool } from '../gameCore/Tool'

import UIHandler from '../UIHandler'
import GameText from '../gameCore/GameText'
import GameWorld from '../GameWorld'
import { Proxy } from '../gameCore/Proxy'
import { MailViewType } from '../viewType/MailViewType'
import PlayerBag from '../bag/data/PlayerBag'
import MailDefine from './define/MailDefine'
import Alert from '../otherCmpt/alert/Alert'
import { Message } from '../Message'
import { Tips } from '../otherCmpt/common/Tips'
import { MailEvent } from './define/MailEvent'
import StringBuffer from '../gameCore/StringBuffer'
import MyPetVo from '../vo/MyPetVo'
import Item from '../vo/Item'
import ProtoUtil from '../ProtoUtil'

import { MailVo } from './data/MailVo'

import { delayCall } from '../delay'
import { GUtil } from '../gameCore/GUtil'
import PowerString from '../gameCore/PowerString'
import { Msg } from '../../proto/msg-define'
import PlayerVo from '../vo/PlayerVo'

export default class MailTool {
    public static startMoney1: number
    public static startMoney2: number
    public static startMoney3: number
    public static sb: StringBuffer
    public static count: number
    public static cur: number
    public static pList: any[]
    public static rewardList: any[]

    public static get model() {
        return facade.getProxy(ModName.Mail, ProxyType.Mail).data
    }

    public static async doMailSendMsg(mail: MailVo, type?: number) {
        if (null == mail) return false
        const body: proto.IC2S_SendMailMessage = {
            content: mail.content,
            reqMoney1: mail.reqMoney1,
            reqMoney3: mail.reqMoney3,
            money1: mail.money1,
            money3: mail.money3,
            appendix: mail.attachItem.map(item => { return { id: item.id, quantity: item.quantity, slotPos: item.slotPos } }),
            toName: mail.toName,
            toId: mail.sendId
        }

        const proxy = facade.getProxy(ModName.Mail, ProxyType.Mail)
        const r = await proxy.network.request(Msg.C2S_SendMailMessage, body)
        if (!r) return false
        MailTool.doMaildeduct(GameWorld.myPlayer, mail)
        let str = GameText.STR_MAIL_SEND_SUCCESS;
        if (mail.reqMoney1 > 0 || mail.reqMoney2 > 0 || mail.reqMoney3 > 0) {
            str += GameText.STR_MAIL_SEND_SUCCESS_INFO
        }

        UIHandler.alertMessage(str)
        UIHandler.hideView(ModName.Mail, MailViewType.MailContactPlayer)
        return true
    }

    private static doMaildeduct(plr: PlayerVo, mail: MailVo) {
        if (!plr || !mail) return
        const bag = plr.bag
        if (!bag) return
        const attachItem = mail.attachItem
        if (attachItem?.length <= 0) return
        for (let i = 0; i < attachItem.length; i++) {
            const item = attachItem[i]
            if (!item) continue
            bag.removeBagItemByPos(item.slotPos, item.quantity)
        }
    }

    public static async doMenuButton(evt: number, mail: MailVo, n) {
        if (null == mail) return false
        switch (evt) {
            case UIHandler.EVENT_ALL_BACK:
                break
            case UIHandler.EVENT_NOTE:
                MailTool.doItemSee(n, mail)
                break
            case UIHandler.EVENT_ALL_MAIL_REPLY:
                mail.sendId = mail.sender
                facade.showView(ModName.Mail, MailViewType.MailContactPlayer, mail)
                break
            case UIHandler.EVENT_ALL_MAIL_REFUSE:
            case UIHandler.EVENT_ALL_MAIL_REGAIN:
                MailTool.doBackmailMsg(mail)
                break
            case UIHandler.EVENT_ALL_MAIL_PICK:
                MailTool.doAttachItemMsg(mail, n)
                break
            case UIHandler.EVENT_ALL_MAIL_DEL:
                MailTool.doDeleteMailMsg(mail)
                break
            case UIHandler.EVENT_ALL_MAIL_SEND:
                if (await MailTool.doMailSendMsg(mail)) {
                    PlayerBag.clearBagAllItemStatus(GameWorld.myPlayer, PlayerBag.CLEAR_BAG_MAIL_SELECT)
                }
                break
            case UIHandler.EVENT_ALL_MENU_RECMAIL_REPORT:
                MailTool.doMailInformMsg(mail)
                break
            case UIHandler.EVENT_SERVICER_EMAIL_DEL:
                MailTool.doDeleteMailMsg(mail)
        }
    }

    public static doMailInformMsg(e) {
        return null == e || 0 == e.isTypeBit(MailDefine.MAIL_TYPE_PLAYER)
            ? false
            : (Alert.confirm(GameText.STR_MAIL_INFORM_ASK, Handler.alloc(this, this.sendReport, [e]), GameText.getText(GameText.TI_WARM_SHOW)), true)
    }

    public static sendReport(e) {
        var t = new Message(ProtocolDefine.CG_MAIL_REPORT)
        t.putLong(e.id)
        MsgHandler.sendRequest(t)
    }

    public static doDeleteMailMsg(e) {
        if (null == e) return false
        var t = MsgHandler.createMailDeleteMsg(e.id + '')
        Proxy.Service.onProto(ProtocolDefine.CG_MAIL_DELETE, Handler.alloc(this, this.onDel, [e]))
        MsgHandler.sendRequest(t)
        return true
    }

    public static doDeleteAllMailMsg(e) {
        var t = facade.getProxy(ModName.Mail, ProxyType.Mail),
            i = []
        for (var a in t.deleteMails) {
            var r = t.deleteMails[a]
            i.push(r.id)
        }
        var l = MsgHandler.createMailDeleteMsg(i.join(','))
        Proxy.Service.onProto(ProtocolDefine.CG_MAIL_DELETE, Handler.alloc(this, this.onDelAll, [e]))
        MsgHandler.sendRequest(l)
        return true
    }

    public static onDelAll(e, t) {
        var i = t.body,
            n = facade.getProxy(ModName.Mail, ProxyType.Mail)
        i.getInt()
        for (var a in n.deleteMails) {
            var r = n.deleteMails[a],
                s = i.getString()
            'ok' === s ? (r.setTabStatus(true, MailDefine.DEL_STATUS), Tips.show(GameText.STR_MAIL_DEL_SUCCESS)) : '' !== s && Tips.show(s)
        }
        facade.sendNt(MailEvent.MAIL_STATU)
        facade.sendNt(MailEvent.MAIL_CLOSE_RECEIVE)
    }

    public static doBackmailMsg(e) {
        if (null == e) return false
        var t = MsgHandler.createMailBackMsg(e.id)
        Proxy.Service.onProto(ProtocolDefine.CG_MAIL_TURNBACK, Handler.alloc(this, this.onTurnBack, [e]))
        MsgHandler.sendRequest(t)
        return true
    }

    public static onTurnBack(e, t) {
        var i = t.body,
            n = i.getByte()
        return 0 > n
            ? UIHandler.alertMessage(i.getString(), GameText.getText(GameText.TI_ERROR))
            : (e.setTabStatus(true, MailDefine.DEL_STATUS),
                UIHandler.alertMessage(GameText.STR_MAIL_BACK_SUCCESS),
                facade.sendNt(MailEvent.MAIL_STATU),
                void facade.sendNt(MailEvent.MAIL_CLOSE_RECEIVE))
    }

    public static doAttachItemMsg(e, t) {
        if (null == e) return false
        if (!e.isTypeBit(MailDefine.MAIL_TYPE_BACK) && !e.isTypeBit(MailDefine.MAIL_TYPE_RECEIPT)) {
            var i = new StringBuffer()
            if (1 == e.isHasReqMoney(i)) return void this.onCheck(e, t, i)
        }
        this.sendAttach(e, t)
        return true
    }

    public static sendAttach(e, t) {
        if (e.isHasSelectItem()) {
            if (null == t)
                for (var i = 0; i < e.selectItem.length; i++)
                    if (null != e.selectItem[i]) {
                        t = e.selectItem[i]
                        break
                    }
        } else t = null
        var o = null == t ? 0 : t.id
            ; (e.isTypeBit(MailDefine.MAIL_TYPE_TASK) && e.isTabStatus(MailDefine.DEL_STATUS)) || this.requestAttach(e, o)
    }

    public static onCheck(e, t, i) {
        Alert.confirm(Tool.manageString(GameText.STR_MAIL_PICK_ASK, i.toString()), Handler.alloc(this, this.onCheckMoney, [e, t]), GameText.getText(GameText.TI_MAIL_PICK_ITEM))
    }

    public static async onCheckMoney(e, t) {
        const i = await Tool.checkEnoughMoney(e.reqMoney1, e.reqMoney2, e.reqMoney3)
        i && this.sendAttach(e, t)
        return !i
    }

    public static async doItemSee(t, i) {
        if (!t) return
        if (t.isPetType) {
            const o = new MyPetVo(null)
            o.petItem = t
            await MyPetVo.doPetInfoMsg(o, MsgHandler.PET_INFO_MAIL, [i.id, t.slotPos])
        } else {
            MailTool.doMailViewItem(t, i)
        }
    }

    public static async doMailViewItem(e, t) {
        if (!t) return false
        if (0 != e.isStatusBit(Item.STATUS_GET_INFO)) return false
        let i = MsgHandler.createMailSeeItem(t.id, e.slotPos)
        await ProtoUtil.sendProtoAsync(i)
        i = MsgHandler.getReceiveMsg()
        if (!i) return false

        try {
            Item.fromBytes(i, e)
            MsgHandler.processGetSuitInfoMsg(i)
            e.setStatusBit(true, Item.STATUS_GET_INFO)
        } catch (n) {
            return false
        }
        UIHandler.checkItem(e)
        return true
    }

    public static async doMailListMsg(type: proto.MailDefine.MAIL_TYPE, i: number, n: number, a?: boolean) {
        let r = type
        if (type == MailDefine.MAIL_TYPE_BACK) {
            r = MailDefine.MAIL_TYPE_BACK | MailDefine.MAIL_TYPE_RECEIPT
        } else if (type == MailDefine.MAIL_TYPE_SYSTEM) {
            r = MailDefine.MAIL_TYPE_SYSTEM | MailDefine.MAIL_TYPE_SERVICE
        }
        let s = MsgHandler.createMailListMsg(r, i, n)
        MsgHandler.sendRequest(s)
        await ProtoUtil.sendProtoAsync(s)
        let _ = MsgHandler.getReceiveMsg()
        let c = _.getShort(),
            p = _.getShort(),
            h = new Vector<MailVo>()
        console.log('onMailList', type, c, p)
        for (let f = 0; p > f; f++) {
            const E = MailTool.fromBytesSimpleMail(_, type)
            null != E && h.addElement(E)
        }

        if (0 == h.length) {
            UIHandler.alertMessage(GameText.STR_MAIL_LIST_NOTHING)
            !a && UIHandler.hideView(ModName.Mail, MailViewType.MailList)
            return
        }
        this.model.setMailList(type, [h, c])
        a ? this.model.openWin(type) : facade.sendNt(MailEvent.MAIL_LIST)
    }

    public static isServiceEmail(e) {
        return e == MailDefine.MAIL_TYPE_SERVICE || e == MailDefine.MAIL_TYPE_SYSTEM
    }

    public static doAllAttach(t: Vector<MailVo>, i) {
        if (MailTool.isAllAttach(i) && null != t && 0 != t.size()) {
            const plr = GameWorld.myPlayer
            if (null != plr) {
                this.startMoney1 = plr.get(proto.ModelConst.Type.MONEY1)
                this.startMoney2 = plr.get(proto.ModelConst.Type.MONEY2)
                this.startMoney3 = plr.get(proto.ModelConst.Type.MONEY3)
                this.sb = new StringBuffer()
                this.count = 1
                this.cur = 0
                let n = t.size()
                this.pList = []
                this.rewardList = []
                for (let a = 0; n > a; a++) {
                    const r = t.elementAt(a)
                    if (null != r) {
                        if (r.isTabStatus(MailDefine.DEL_STATUS)) {
                            this.cur++
                            this.dealEnd(a, n)
                        } else {
                            this.pList.push([r, a, n])
                            1 == this.pList.length && this.onNext()
                        }
                    } else {
                        this.cur++
                        this.dealEnd(a, n)
                    }
                }
            }
        }
    }

    public static onNext() {
        if (this.pList.length) {
            const e = this.pList[0]
            delayCall(Handler.alloc(this, this.sendDetail, [e[0], e[1], e[2]]), 100)
        }
    }

    public static sendDetail(e, t?, i?) {
        const o = MsgHandler.createMailDetailMsg(e.id)
        Proxy.Service.onProto(ProtocolDefine.CG_MAIL_DETAIL, Handler.alloc(this, this.onBackDetail, [e, t, i]))
        MsgHandler.sendRequest(o)
    }

    public static onError() {
        UIHandler.alertMessage(GameText.STR_MAIL_PICK_ERROR)
    }

    public static dealEnd(e, t) {
        e == t - 1 && this.cur >= t && this.doAllEnd(t)
    }

    public static onBackDetail(e, t, i, o) {
        var n = null != t && null != i
        n ? this.onDetailAuto(e, t, i, o) : this.onDetail(e, o)
    }

    public static onDetailAuto(t, i, o, n) {
        this.pList.shift()
        var a = n.body
        try {
            var r = a.getByte()
            if (0 > r) {
                return this.cur++,
                    void this.onNext()
            }
            MailTool.fromBytes(a, t)
        } catch (s) {
            console.log('onDetailAttach-error', s)
            return void this.onNext()
        }
        return 0 == t.isDirectAttach() ? (this.cur++, this.dealEnd(i, o), this.onNext()) : this.requestAttach(t, 0, i, o)
    }

    public static requestAttach(e, t, i?, o?) {
        var a = MsgHandler.createMailAttachMsg(e, t)
        this.rewardList = GUtil.joinArray(this.rewardList, e.attachItem)
        Proxy.Service.onProto(ProtocolDefine.CG_MAIL_EXTRACT_ATTACHMENT, Handler.alloc(this, this.onBackAttach, [e, i, o]))
        MsgHandler.sendRequest(a)
    }

    public static onBackAttach(e, t, i, o) {
        var n = null != t && null != i
        n ? this.onAttach(e, t, i, o) : this.processMailAttachMsg(e, o)
    }

    public static onAttach(e, t, i, o) {
        let n = o.body
        try {
            if (0 > n.getByte()) {
                UIHandler.alertMessage(n.getString(), GameText.getText(GameText.TI_ERROR))
                return this.onNext()
            }
            const plr = GameWorld.myPlayer
            if (null == plr) return void this.onNext()
            const s = new StringBuffer()
            plr.setMoneyByType(proto.ModelConst.Type.MONEY1, n.getInt(), s)
            plr.setMoneyByType(proto.ModelConst.Type.MONEY2, n.getInt(), s)
            plr.setMoneyByType(proto.ModelConst.Type.MONEY3, n.getInt(), s)
            MsgHandler.processAddItemMsg(n, ProtocolDefine.ADD_ITEM_MAIL_REWARD, true)
            this.sb.append(n.getMsgInfo())
        } catch (l) {
            return void this.onNext()
        }
        e.status = MailDefine.MAIL_STATUS_READ_NO_ITEM
        this.count++
        this.cur++
        this.dealEnd(t, i)
        this.onNext()
    }

    public static doAllEnd(e) {
        if (1 == this.count) {
            1 > e && UIHandler.alertMessage(GameText.STR_MAIL_PICK_NULL)
            return
        }
        var t = GameWorld.myPlayer
        if (null != t) {
            var i = '',
                n = t.get(proto.ModelConst.Type.MONEY1) - this.startMoney1
            n > 0 && (i += PowerString.makeColorString(GameText.STR_MONEY1 + n, Tool.COLOR_YELLOW) + ' ')
            var a = t.get(proto.ModelConst.Type.MONEY2) - this.startMoney2
            a > 0 && (i += PowerString.makeColorString(GameText.STR_MONEY2 + a, Tool.COLOR_MONEY2) + ' ')
            var r = t.get(proto.ModelConst.Type.MONEY3) - this.startMoney3
            r > 0 && (i += PowerString.makeColorString(GameText.STR_MONEY3 + r, Tool.COLOR_MONEY3) + ' ')
            !Tool.isNullText(i) && (i = Tool.manageString(GameText.STR_MAIL_PICK_MONEY_INFO, i))
            i += this.sb.toString()
            this.dealReward(this.rewardList, Tool.manageString(GameText.STR_MAIL_PICK_SUCCESS_ALL_INFO, i), GameText.getText(GameText.TI_WARM_SHOW))
            facade.sendNt(MailEvent.MAIL_STATU)
        }
    }

    public static dealReward(e, t, i) {
        e && e.length ? facade.showView(ModName.Mail, MailViewType.MailReward, e) : UIHandler.alertMessage(t, i)
    }

    public static isAllAttach(e) {
        return e == MailDefine.MAIL_TYPE_TASK || e == MailDefine.MAIL_TYPE_BACK || e == MailDefine.MAIL_TYPE_RECEIPT
            ? true
            : e == MailDefine.MAIL_TYPE_SYSTEM || e == MailDefine.MAIL_TYPE_SERVICE
    }

    public static fromBytes(e, t) {
        t.toPID = e.getInt()
        t.isTypeBit(MailDefine.MAIL_TYPE_SEND) ? (e.getInt(), (t.type = MailDefine.MAIL_TYPE_SEND)) : (t.type = e.getInt())
        var i = e.getBoolean()
        i ? t.setTabStatus(true, MailDefine.DEL_STATUS) : (t.endTime = e.getLong())
        t.content = e.getString()
        t.datetime = e.getString()
        t.money1 = e.getInt()
        t.money2 = e.getInt()
        t.money3 = e.getInt()
        t.reqMoney1 = e.getInt()
        t.reqMoney2 = e.getInt()
        t.reqMoney3 = e.getInt()
        var o = e.getByte()
        if (o > 0) {
            t.attachItem = new Array(o)
            for (let n = 0; o > n; n++) {
                t.attachItem[n] = Item.fromMailBytes(e)
            }
        }
        o = e.getByte()
        if (o > 0) {
            t.selectItem = new Array(o)
            for (let n = 0; o > n; n++) {
                t.selectItem[n] = Item.fromMailBytes(e)
            }
        }
        t.type == MailDefine.MAIL_TYPE_TASK || t.type == MailDefine.MAIL_TYPE_SYSTEM ? (t.title = e.getString()) : Tool.isNullText(t.title) && (t.title = t.getTitleDesc())
        t.type == MailDefine.MAIL_TYPE_SERVICE && (t.sendcontent = e.getString())
        return t
    }

    public static fromBytesSimpleMail(e, t) {
        const i = new MailVo()
        i.type = t
        i.id = e.getLong()
        i.toName = e.getString()
        i.status = e.getByte()
        let o = e.getBoolean()
        o ? (i.title = e.getString()) : (i.title = i.getTitleDesc())
        i.needcheck = e.getBoolean()
        i.datetime = e.getString()
        i.endTime = e.getLong()
        let n = e.getByte()
        if (n > 0) {
            i.attachItem = new Array(n)
            for (let a = 0; n > a; a++) i.attachItem[a] = Item.fromMailBytes(e)
        }
        return i
    }

    public static doMailDetailMsg(e) {
        if (null == e) return false
        if (e.isTabStatus(MailDefine.DEL_STATUS)) {
            UIHandler.alertMessage(GameText.STR_NO_MAIL)
            return false
        }
        let t = e.getStatus(),
            i = t == MailDefine.MAIL_STATU_NEW
        i && this.model.removeNewMailNum(e.type)
        e.needcheck
        this.sendDetail(e)
        return true
    }

    public static onDetail(t, i) {
        var n = i.body,
            a = n.getByte()
        if (0 > a) return void UIHandler.alertMessage(n.getString(), GameText.getText(GameText.TI_ERROR))
        // t.isTypeBit(MailDefine.MAIL_TYPE_TASK);
        // t.isTypeBit(MailDefine.MAIL_TYPE_SERVICE) && 0 == t.isTypeBit(MailDefine.MAIL_TYPE_SYSTEM);
        MailTool.fromBytes(n, t)
        t.setReadStatus()
        t.isHasSelectItem() ? UIHandler.showModal(ModName.Mail, MailViewType.MailSelect, t) : UIHandler.showWin(ModName.Mail, MailViewType.MailReceive, t)
        facade.sendNt(MailEvent.MAIL_STATU)
    }

    public static onDel(e, t) {
        var i = t.body,
            n = i.getByte()
        return 0 > n
            ? UIHandler.alertMessage(i.getString(), GameText.getText(GameText.TI_ERROR))
            : (e.setTabStatus(true, MailDefine.DEL_STATUS),
                UIHandler.alertMessage(GameText.STR_MAIL_DEL_SUCCESS),
                facade.sendNt(MailEvent.MAIL_STATU),
                facade.sendNt(MailEvent.MAIL_CLOSE_RECEIVE))
    }

    public static processMailAttachMsg(e, t) {
        var i = t.body,
            n = i.getByte()
        if (0 > n) return void UIHandler.alertMessage(i.getString(), GameText.getText(GameText.TI_ERROR))
        var a = GameWorld.myPlayer
        if (null != a) {
            var r = new StringBuffer()
            a.setMoneyByType(proto.ModelConst.Type.MONEY1, i.getInt(), r)
            a.setMoneyByType(proto.ModelConst.Type.MONEY2, i.getInt(), r)
            a.setMoneyByType(proto.ModelConst.Type.MONEY3, i.getInt(), r)
            i.addTempSb(r.toString())
            var s = MsgHandler.processAddItemMsg(i, ProtocolDefine.ADD_ITEM_MAIL_REWARD, true)
            e.status = MailDefine.MAIL_STATUS_READ_NO_ITEM
            this.dealReward(s, i.getTempSb() + i.getMsgInfo(), GameText.getText(GameText.TI_MAIL_PICK_ITEM))
            facade.sendNt(MailEvent.MAIL_STATU)
            facade.sendNt(MailEvent.MAIL_CLOSE_RECEIVE)
        }
    }
}
