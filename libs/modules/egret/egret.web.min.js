var __reflect=this&&this.__reflect||function(e,t,r){e.__class__=t,r?r.push(t):r=[t],e.__types__=e.__types__?r.concat(e.__types__):r},__extends=this&&this.__extends||function(e,t){function r(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);r.prototype=t.prototype,e.prototype=new r},egret;!function(e){var t;!function(t){var r=function(t){function r(r){var i=t.call(this)||this;return i.onUpdate=function(t){var r=new e.GeolocationEvent(e.Event.CHANGE),n=t.coords;r.altitude=n.altitude,r.heading=n.heading,r.accuracy=n.accuracy,r.latitude=n.latitude,r.longitude=n.longitude,r.speed=n.speed,r.altitudeAccuracy=n.altitudeAccuracy,i.dispatchEvent(r)},i.onError=function(t){var r=e.GeolocationEvent.UNAVAILABLE;t.code==t.PERMISSION_DENIED&&(r=e.GeolocationEvent.PERMISSION_DENIED);var n=new e.GeolocationEvent(e.IOErrorEvent.IO_ERROR);n.errorType=r,n.errorMessage=t.message,i.dispatchEvent(n)},i.geolocation=navigator.geolocation,i}return __extends(r,t),r.prototype.start=function(){var t=this.geolocation;t?this.watchId=t.watchPosition(this.onUpdate,this.onError):this.onError({code:2,message:e.sys.tr(3004),PERMISSION_DENIED:1,POSITION_UNAVAILABLE:2})},r.prototype.stop=function(){var e=this.geolocation;e.clearWatch(this.watchId)},r}(e.EventDispatcher);t.WebGeolocation=r,__reflect(r.prototype,"egret.web.WebGeolocation",["egret.Geolocation"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.onChange=function(t){var i=new e.MotionEvent(e.Event.CHANGE),n={x:t.acceleration.x,y:t.acceleration.y,z:t.acceleration.z},a={x:t.accelerationIncludingGravity.x,y:t.accelerationIncludingGravity.y,z:t.accelerationIncludingGravity.z},o={alpha:t.rotationRate.alpha,beta:t.rotationRate.beta,gamma:t.rotationRate.gamma};i.acceleration=n,i.accelerationIncludingGravity=a,i.rotationRate=o,r.dispatchEvent(i)},r}return __extends(r,t),r.prototype.start=function(){window.addEventListener("devicemotion",this.onChange)},r.prototype.stop=function(){window.removeEventListener("devicemotion",this.onChange)},r}(e.EventDispatcher);t.WebMotion=r,__reflect(r.prototype,"egret.web.WebMotion",["egret.Motion"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(){function r(){this.vertSize=5,this.vertByteSize=4*this.vertSize,this.maxQuadsCount=2048,this.maxVertexCount=4*this.maxQuadsCount,this.maxIndicesCount=6*this.maxQuadsCount,this.vertices=null,this.indices=null,this.indicesForMesh=null,this.vertexIndex=0,this.indexIndex=0,this.hasMesh=!1,this._vertices=null,this._verticesFloat32View=null,this._verticesUint32View=null,this._vertices=new ArrayBuffer(this.maxVertexCount*this.vertByteSize),this._verticesFloat32View=new Float32Array(this._vertices),this._verticesUint32View=new Uint32Array(this._vertices),this.vertices=this._verticesFloat32View;var e=this.maxIndicesCount;this.indices=new Uint16Array(e),this.indicesForMesh=new Uint16Array(e);for(var t=0,r=0;e>t;t+=6,r+=4)this.indices[t+0]=r+0,this.indices[t+1]=r+1,this.indices[t+2]=r+2,this.indices[t+3]=r+0,this.indices[t+4]=r+2,this.indices[t+5]=r+3}return r.prototype.reachMaxSize=function(e,t){return void 0===e&&(e=4),void 0===t&&(t=6),this.vertexIndex>this.maxVertexCount-e||this.indexIndex>this.maxIndicesCount-t},r.prototype.getVertices=function(){var e=this.vertices.subarray(0,this.vertexIndex*this.vertSize);return e},r.prototype.getIndices=function(){return this.indices},r.prototype.getMeshIndices=function(){return this.indicesForMesh},r.prototype.changeToMeshIndices=function(){if(!this.hasMesh){for(var e=0,t=this.indexIndex;t>e;++e)this.indicesForMesh[e]=this.indices[e];this.hasMesh=!0}},r.prototype.isMesh=function(){return this.hasMesh},r.prototype.cacheArrays=function(r,i,n,a,o,s,l,h,c,u,d,f,p,v,g){var x=r.globalAlpha;x=Math.min(x,1);var y=r.globalTintColor||16777215,m=r.currentTexture;x=1>x&&m&&m[e.UNPACK_PREMULTIPLY_ALPHA_WEBGL]?e.WebGLUtils.premultiplyTint(y,x):y+(255*x<<24);var b=r.globalMatrix,w=b.a,T=b.b,E=b.c,_=b.d,C=b.tx,S=b.ty,R=r.$offsetX,L=r.$offsetY;if((0!=R||0!=L)&&(C=R*w+L*E+C,S=R*T+L*_+S),!p){(0!=s||0!=l)&&(C=s*w+l*E+C,S=s*T+l*_+S);var D=h/a;1!=D&&(w=D*w,T=D*T);var A=c/o;1!=A&&(E=A*E,_=A*_)}if(p)if(t.isIOS14Device()){var I=[],$=this.vertices,M=this._verticesUint32View,B=this.vertexIndex*this.vertSize,O=0,P=0,W=0,F=0,G=0,k=0,H=0;for(O=0,W=f.length;W>O;O+=2)P=B+5*O/2,k=p[O],H=p[O+1],F=f[O],G=f[O+1],g?I.push([w*k+E*H+C,T*k+_*H+S,(i+(1-G)*o)/u,(n+F*a)/d]):I.push([w*k+E*H+C,T*k+_*H+S,(i+F*a)/u,(n+G*o)/d]),M[P+4]=x;for(var U=0;U<v.length;U+=3){var N=I[v[U]];$[B++]=N[0],$[B++]=N[1],$[B++]=N[2],$[B++]=N[3],M[B++]=x;var X=I[v[U+1]];$[B++]=X[0],$[B++]=X[1],$[B++]=X[2],$[B++]=X[3],M[B++]=x;var z=I[v[U+2]];$[B++]=z[0],$[B++]=z[1],$[B++]=z[2],$[B++]=z[3],M[B++]=x,$[B++]=z[0],$[B++]=z[1],$[B++]=z[2],$[B++]=z[3],M[B++]=x}var Y=v.length/3;this.vertexIndex+=4*Y,this.indexIndex+=6*Y}else{var $=this.vertices,M=this._verticesUint32View,B=this.vertexIndex*this.vertSize,O=0,P=0,W=0,F=0,G=0,k=0,H=0;for(O=0,W=f.length;W>O;O+=2)P=B+5*O/2,k=p[O],H=p[O+1],F=f[O],G=f[O+1],$[P+0]=w*k+E*H+C,$[P+1]=T*k+_*H+S,g?($[P+2]=(i+(1-G)*o)/u,$[P+3]=(n+F*a)/d):($[P+2]=(i+F*a)/u,$[P+3]=(n+G*o)/d),M[P+4]=x;if(this.hasMesh)for(var V=0,j=v.length;j>V;++V)this.indicesForMesh[this.indexIndex+V]=v[V]+this.vertexIndex;this.vertexIndex+=f.length/2,this.indexIndex+=v.length}else{var q=u,K=d,Q=a,J=o;i/=q,n/=K;var $=this.vertices,M=this._verticesUint32View,B=this.vertexIndex*this.vertSize;if(g){var Z=a;a=o/q,o=Z/K,$[B++]=C,$[B++]=S,$[B++]=a+i,$[B++]=n,M[B++]=x,$[B++]=w*Q+C,$[B++]=T*Q+S,$[B++]=a+i,$[B++]=o+n,M[B++]=x,$[B++]=w*Q+E*J+C,$[B++]=_*J+T*Q+S,$[B++]=i,$[B++]=o+n,M[B++]=x,$[B++]=E*J+C,$[B++]=_*J+S,$[B++]=i,$[B++]=n,M[B++]=x}else a/=q,o/=K,$[B++]=C,$[B++]=S,$[B++]=i,$[B++]=n,M[B++]=x,$[B++]=w*Q+C,$[B++]=T*Q+S,$[B++]=a+i,$[B++]=n,M[B++]=x,$[B++]=w*Q+E*J+C,$[B++]=_*J+T*Q+S,$[B++]=a+i,$[B++]=o+n,M[B++]=x,$[B++]=E*J+C,$[B++]=_*J+S,$[B++]=i,$[B++]=o+n,M[B++]=x;if(this.hasMesh){var et=this.indicesForMesh;et[this.indexIndex+0]=0+this.vertexIndex,et[this.indexIndex+1]=1+this.vertexIndex,et[this.indexIndex+2]=2+this.vertexIndex,et[this.indexIndex+3]=0+this.vertexIndex,et[this.indexIndex+4]=2+this.vertexIndex,et[this.indexIndex+5]=3+this.vertexIndex}this.vertexIndex+=4,this.indexIndex+=6}},r.prototype.clear=function(){this.hasMesh=!1,this.vertexIndex=0,this.indexIndex=0},r}();t.WebGLVertexArrayObject=r,__reflect(r.prototype,"egret.web.WebGLVertexArrayObject"),t.isIOS14Device=function(){return!1}}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(e){if(window.location){var t=location.search;if(""==t)return"";t=t.slice(1);for(var r=t.split("&"),i=r.length,n=0;i>n;n++){var a=r[n],o=a.split("=");if(o[0]==e)return o[1]}}return""}t.getOption=r,e.getOption=r}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(){function t(){}return t.decodeAudios=function(){if(!(t.decodeArr.length<=0||t.isDecoding)){t.isDecoding=!0;var r=t.decodeArr.shift();t.ctx.decodeAudioData(r.buffer,function(e){r.self.audioBuffer=e,r.success&&r.success(),t.isDecoding=!1,t.decodeAudios()},function(){e.log("sound decode error"),r.fail&&r.fail(),t.isDecoding=!1,t.decodeAudios()})}},t.decodeArr=[],t.isDecoding=!1,t}();t.WebAudioDecode=r,__reflect(r.prototype,"egret.web.WebAudioDecode");var i=function(i){function n(){var e=i.call(this)||this;return e.loaded=!1,e}return __extends(n,i),Object.defineProperty(n.prototype,"length",{get:function(){if(this.audioBuffer)return this.audioBuffer.duration;throw new Error("sound not loaded!")},enumerable:!0,configurable:!0}),n.prototype.load=function(t){function i(){a.loaded=!0,a.dispatchEventWith(e.Event.COMPLETE)}function n(){a.dispatchEventWith(e.IOErrorEvent.IO_ERROR)}var a=this;this.url=t;var o=new XMLHttpRequest;o.open("GET",t,!0),o.responseType="arraybuffer",o.addEventListener("load",function(){var t=o.status>=400;t?a.dispatchEventWith(e.IOErrorEvent.IO_ERROR):(r.decodeArr.push({buffer:o.response,success:i,fail:n,self:a,url:a.url}),r.decodeAudios())}),o.addEventListener("error",function(){a.dispatchEventWith(e.IOErrorEvent.IO_ERROR)}),o.send()},n.prototype.play=function(r,i){r=+r||0,i=+i||0;var n=new t.WebAudioSoundChannel;return n.$url=this.url,n.$loops=i,n.$audioBuffer=this.audioBuffer,n.$startTime=r,n.$play(),e.sys.$pushSoundChannel(n),n},n.prototype.close=function(){},n.MUSIC="music",n.EFFECT="effect",n}(e.EventDispatcher);t.WebAudioSound=i,__reflect(i.prototype,"egret.web.WebAudioSound",["egret.Sound"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(r){function i(){var i=r.call(this)||this;return i.$startTime=0,i.bufferSource=null,i.context=t.WebAudioDecode.ctx,i.isStopped=!1,i._currentTime=0,i._volume=1,i.onPlayEnd=function(){return 1==i.$loops?(i.stop(),void i.dispatchEventWith(e.Event.SOUND_COMPLETE)):(i.$loops>0&&i.$loops--,void i.$play())},i._startTime=0,i.initGain(),i}return __extends(i,r),i.prototype.initGain=function(){this.gain=null,this.context.createGain?this.gain=this.context.createGain():this.gain=this.context.createGainNode()},i.prototype.$play=function(){if(this.isStopped)return void e.$error(1036);this.bufferSource&&(this.bufferSource.onended=null,this.bufferSource=null);var t=this.context,r=this.gain,i=t.createBufferSource();this.bufferSource=i,i.buffer=this.$audioBuffer,i.connect(r),r.connect(t.destination),i.onended=this.onPlayEnd,this._startTime=Date.now(),this.gain.gain.value=this._volume,i.start(0,this.$startTime),this._currentTime=0},i.prototype.stop=function(){if(this.bufferSource){var t=this.bufferSource;t.stop?t.stop(0):t.noteOff(0),t.onended=null,t.disconnect(),this.bufferSource=null,this.$audioBuffer=null}this.isStopped||e.sys.$popSoundChannel(this),this.isStopped=!0},Object.defineProperty(i.prototype,"volume",{get:function(){return this._volume},set:function(t){return this.isStopped?void e.$error(1036):(this._volume=t,void(this.gain.gain.value=t))},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"position",{get:function(){return this.bufferSource?(Date.now()-this._startTime)/1e3+this.$startTime:0},enumerable:!0,configurable:!0}),i}(e.EventDispatcher);t.WebAudioSoundChannel=r,__reflect(r.prototype,"egret.web.WebAudioSoundChannel",["egret.SoundChannel","egret.IEventDispatcher"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(t){function r(r,i){void 0===i&&(i=!0);var n=t.call(this)||this;return n.loaded=!1,n.closed=!1,n.heightSet=0/0,n.widthSet=0/0,n.waiting=!1,n.userPause=!1,n.userPlay=!1,n.isPlayed=!1,n.screenChanged=function(t){var r=document.fullscreenEnabled||document.webkitIsFullScreen;r||(n.checkFullScreen(!1),e.Capabilities.isMobile||(n._fullscreen=r))},n._fullscreen=!0,n.onVideoLoaded=function(){n.video.removeEventListener("canplay",n.onVideoLoaded);var t=n.video;n.loaded=!0,n.posterData&&(n.posterData.width=n.getPlayWidth(),n.posterData.height=n.getPlayHeight()),t.width=t.videoWidth,t.height=t.videoHeight,window.setTimeout(function(){n.dispatchEventWith(e.Event.COMPLETE)},200)},n.$renderNode=new e.sys.BitmapNode,n.src=r,n.once(e.Event.ADDED_TO_STAGE,n.loadPoster,n),r&&n.load(),n}return __extends(r,t),r.prototype.createNativeDisplayObject=function(){this.$nativeDisplayObject=new egret_native.NativeDisplayObject(1)},r.prototype.load=function(t,r){var i=this;if(void 0===r&&(r=!0),t=t||this.src,this.src=t,!this.video||this.video.src!=t){var n;!this.video||e.Capabilities.isMobile?(n=document.createElement("video"),this.video=n,n.controls=null):n=this.video,(-1!=t.indexOf("http://")||-1!=t.indexOf("HTTP://")||-1!=t.indexOf("https://")||-1!=t.indexOf("HTTPS://"))&&(n.crossOrigin="anonymous"),n.src=t,n.setAttribute("autoplay","autoplay"),n.setAttribute("webkit-playsinline","true"),n.setAttribute("playsinline","true"),n.setAttribute("x5-video-player-type","h5-page"),n.addEventListener("canplay",this.onVideoLoaded),n.addEventListener("error",function(){return i.onVideoError()}),n.addEventListener("ended",function(){return i.onVideoEnded()});var a=!1;n.addEventListener("canplay",function(){i.waiting=!1,a?i.userPause?i.pause():i.userPlay&&i.play():(a=!0,n.pause())}),n.addEventListener("waiting",function(){i.waiting=!0}),n.load(),this.videoPlay(),n.style.position="absolute",n.style.top="0px",n.style.zIndex="-88888",n.style.left="0px",n.height=1,n.width=1}},r.prototype.play=function(t,r){var i=this;if(void 0===r&&(r=!1),0==this.loaded)return this.load(this.src),void this.once(e.Event.COMPLETE,function(e){return i.play(t,r)},this);this.isPlayed=!0;var n=this.video;void 0!=t&&(n.currentTime=+t||0),n.loop=!!r,e.Capabilities.isMobile?n.style.zIndex="-88888":n.style.zIndex="9999",n.style.position="absolute",n.style.top="0px",n.style.left="0px",n.height=n.videoHeight,n.width=n.videoWidth,"Windows PC"!=e.Capabilities.os&&"Mac OS"!=e.Capabilities.os&&window.setTimeout(function(){n.width=0},1e3),this.checkFullScreen(this._fullscreen)},r.prototype.videoPlay=function(){return this.userPause=!1,this.waiting?void(this.userPlay=!0):(this.userPlay=!1,void this.video.play())},r.prototype.checkFullScreen=function(t){var r=this.video;if(t)null==r.parentElement&&(r.removeAttribute("webkit-playsinline"),r.removeAttribute("playsinline"),document.body.appendChild(r)),e.stopTick(this.markDirty,this),this.goFullscreen();else if(null!=r.parentElement&&r.parentElement.removeChild(r),r.setAttribute("webkit-playsinline","true"),r.setAttribute("playsinline","true"),this.setFullScreenMonitor(!1),e.startTick(this.markDirty,this),e.Capabilities.isMobile)return this.video.currentTime=0,void this.onVideoEnded();this.videoPlay()},r.prototype.goFullscreen=function(){var t,r=this.video;return t=e.web.getPrefixStyleName("requestFullscreen",r),r[t]||(t=e.web.getPrefixStyleName("requestFullScreen",r),r[t])?(r.removeAttribute("webkit-playsinline"),r[t](),this.setFullScreenMonitor(!0),!0):!0},r.prototype.setFullScreenMonitor=function(e){var t=this.video;e?(t.addEventListener("mozfullscreenchange",this.screenChanged),t.addEventListener("webkitfullscreenchange",this.screenChanged),t.addEventListener("mozfullscreenerror",this.screenError),t.addEventListener("webkitfullscreenerror",this.screenError)):(t.removeEventListener("mozfullscreenchange",this.screenChanged),t.removeEventListener("webkitfullscreenchange",this.screenChanged),t.removeEventListener("mozfullscreenerror",this.screenError),t.removeEventListener("webkitfullscreenerror",this.screenError))},r.prototype.screenError=function(){e.$error(3014)},r.prototype.exitFullscreen=function(){document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen?document.msExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.oCancelFullScreen?document.oCancelFullScreen():document.webkitExitFullscreen?document.webkitExitFullscreen():this.video.style.display="none",this.video&&this.video.parentElement&&this.video.parentElement.removeChild(this.video)},r.prototype.onVideoEnded=function(){this.pause(),this.isPlayed=!1,this._fullscreen&&this.exitFullscreen(),this.dispatchEventWith(e.Event.ENDED)},r.prototype.onVideoError=function(){console.error("video errorCode:",this.video.error.code),this.dispatchEventWith(e.IOErrorEvent.IO_ERROR)},r.prototype.close=function(){var e=this;this.closed=!0,this.video.removeEventListener("canplay",this.onVideoLoaded),this.video.removeEventListener("error",function(){return e.onVideoError()}),this.video.removeEventListener("ended",function(){return e.onVideoEnded()}),this.pause(),0==this.loaded&&this.video&&(this.video.src=""),this.video&&this.video.parentElement&&(this.video.parentElement.removeChild(this.video),this.video=null),this.loaded=!1},r.prototype.pause=function(){return this.userPlay=!1,this.waiting?void(this.userPause=!0):(this.userPause=!1,this.video.pause(),void e.stopTick(this.markDirty,this))},Object.defineProperty(r.prototype,"volume",{get:function(){return this.video?this.video.volume:1},set:function(e){this.video&&(this.video.volume=e)},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"position",{get:function(){return this.video?this.video.currentTime:0},set:function(e){this.video&&(this.video.currentTime=e)},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"fullscreen",{get:function(){return this._fullscreen},set:function(t){e.Capabilities.isMobile||(this._fullscreen=!!t,this.video&&0==this.video.paused&&this.checkFullScreen(this._fullscreen))},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"bitmapData",{get:function(){return this.video&&this.loaded?(this._bitmapData||(this.video.width=this.video.videoWidth,this.video.height=this.video.videoHeight,this._bitmapData=new e.BitmapData(this.video),this._bitmapData.$deleteSource=!1),this._bitmapData):null},enumerable:!0,configurable:!0}),r.prototype.loadPoster=function(){var t=this,r=this.poster;if(r){var i=new e.ImageLoader;i.once(e.Event.COMPLETE,function(r){i.data;if(t.posterData=i.data,t.$renderDirty=!0,t.posterData.width=t.getPlayWidth(),t.posterData.height=t.getPlayHeight(),e.nativeRender){var n=new e.Texture;n._setBitmapData(t.posterData),t.$nativeDisplayObject.setTexture(n)}},this),i.load(r)}},r.prototype.$measureContentBounds=function(e){var t=this.bitmapData,r=this.posterData;t?e.setTo(0,0,this.getPlayWidth(),this.getPlayHeight()):r?e.setTo(0,0,this.getPlayWidth(),this.getPlayHeight()):e.setEmpty()},r.prototype.getPlayWidth=function(){return isNaN(this.widthSet)?this.bitmapData?this.bitmapData.width:this.posterData?this.posterData.width:0/0:this.widthSet},r.prototype.getPlayHeight=function(){return isNaN(this.heightSet)?this.bitmapData?this.bitmapData.height:this.posterData?this.posterData.height:0/0:this.heightSet},r.prototype.$updateRenderNode=function(){var t=this.$renderNode,r=this.bitmapData,i=this.posterData,n=this.getPlayWidth(),a=this.getPlayHeight();this.isPlayed&&!e.Capabilities.isMobile||!i?this.isPlayed&&r&&(t.image=r,t.imageWidth=r.width,t.imageHeight=r.height,e.WebGLUtils.deleteWebGLTexture(r.webGLTexture),r.webGLTexture=null,t.drawImage(0,0,r.width,r.height,0,0,n,a)):(t.image=i,t.imageWidth=n,t.imageHeight=a,t.drawImage(0,0,i.width,i.height,0,0,n,a))},r.prototype.markDirty=function(){return this.$renderDirty=!0,!0},r.prototype.$setHeight=function(e){if(this.heightSet=e,this.paused){var r=this;this.$renderDirty=!0,window.setTimeout(function(){r.$renderDirty=!1},200)}t.prototype.$setHeight.call(this,e)},r.prototype.$setWidth=function(e){if(this.widthSet=e,this.paused){var r=this;this.$renderDirty=!0,window.setTimeout(function(){r.$renderDirty=!1},200)}t.prototype.$setWidth.call(this,e)},Object.defineProperty(r.prototype,"paused",{get:function(){return this.video?this.video.paused:!0},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"length",{get:function(){if(this.video)return this.video.duration;throw new Error("Video not loaded!")},enumerable:!0,configurable:!0}),r}(e.DisplayObject);t.WebVideo=r,__reflect(r.prototype,"egret.web.WebVideo",["egret.Video","egret.DisplayObject"]),e.Video=r}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(t){function r(){var e=t.call(this)||this;return e.timeout=0,e._url="",e._method="",e}return __extends(r,t),Object.defineProperty(r.prototype,"response",{get:function(){if(!this._xhr)return null;if(void 0!=this._xhr.response)return this._xhr.response;if("text"==this._responseType)return this._xhr.responseText;if("arraybuffer"==this._responseType&&/msie 9.0/i.test(navigator.userAgent)){var e=window;return e.convertResponseBodyToText(this._xhr.responseBody)}return"document"==this._responseType?this._xhr.responseXML:null},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"responseType",{get:function(){return this._responseType},set:function(e){this._responseType=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"withCredentials",{get:function(){return this._withCredentials},set:function(e){this._withCredentials=e},enumerable:!0,configurable:!0}),r.prototype.getXHR=function(){return window.XMLHttpRequest?new window.XMLHttpRequest:new ActiveXObject("MSXML2.XMLHTTP")},r.prototype.open=function(e,t){void 0===t&&(t="GET"),this._url=e,this._method=t,this._xhr&&(this._xhr.abort(),this._xhr=null);var r=this.getXHR();window.XMLHttpRequest?(r.addEventListener("load",this.onload.bind(this)),r.addEventListener("error",this.onerror.bind(this))):r.onreadystatechange=this.onReadyStateChange.bind(this),r.onprogress=this.updateProgress.bind(this),r.ontimeout=this.onTimeout.bind(this),r.open(this._method,this._url,!0),this._xhr=r},r.prototype.send=function(e){if(null!=this._responseType&&(this._xhr.responseType=this._responseType),null!=this._withCredentials&&(this._xhr.withCredentials=this._withCredentials),this.headerObj)for(var t in this.headerObj)this._xhr.setRequestHeader(t,this.headerObj[t]);this._xhr.timeout=this.timeout,this._xhr.send(e)},r.prototype.abort=function(){this._xhr&&this._xhr.abort()},r.prototype.getAllResponseHeaders=function(){if(!this._xhr)return null;var e=this._xhr.getAllResponseHeaders();return e?e:""},r.prototype.setRequestHeader=function(e,t){this.headerObj||(this.headerObj={}),this.headerObj[e]=t},r.prototype.getResponseHeader=function(e){if(!this._xhr)return null;var t=this._xhr.getResponseHeader(e);return t?t:""},r.prototype.onTimeout=function(){this.dispatchEventWith(e.IOErrorEvent.IO_ERROR)},r.prototype.onReadyStateChange=function(){var t=this._xhr;if(4==t.readyState){var r=t.status>=400||0==t.status,i=(this._url,this);window.setTimeout(function(){r?i.dispatchEventWith(e.IOErrorEvent.IO_ERROR):i.dispatchEventWith(e.Event.COMPLETE)},0)}},r.prototype.updateProgress=function(t){t.lengthComputable&&e.ProgressEvent.dispatchProgressEvent(this,e.ProgressEvent.PROGRESS,t.loaded,t.total)},r.prototype.onload=function(){var t=this,r=this._xhr,i=(this._url,r.status>=400);window.setTimeout(function(){i?t.dispatchEventWith(e.IOErrorEvent.IO_ERROR):t.dispatchEventWith(e.Event.COMPLETE)},0)},r.prototype.onerror=function(){var t=(this._url,this);window.setTimeout(function(){t.dispatchEventWith(e.IOErrorEvent.IO_ERROR)},0)},r}(e.EventDispatcher);t.WebHttpRequest=r,__reflect(r.prototype,"egret.web.WebHttpRequest",["egret.HttpRequest"]),e.HttpRequest=r}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=window.URL||window.webkitURL,i=function(i){function n(){var e=null!==i&&i.apply(this,arguments)||this;return e.data=null,e._crossOrigin=null,e._hasCrossOriginSet=!1,e.currentImage=null,e.request=null,e}return __extends(n,i),Object.defineProperty(n.prototype,"crossOrigin",{get:function(){return this._crossOrigin},set:function(e){this._hasCrossOriginSet=!0,this._crossOrigin=e},enumerable:!0,configurable:!0}),n.prototype.load=function(r){if(t.Html5Capatibility._canUseBlob&&0!=r.indexOf("wxLocalResource:")&&0!=r.indexOf("data:")&&0!=r.indexOf("http:")&&0!=r.indexOf("https:")){var i=this.request;i||(i=this.request=new e.web.WebHttpRequest,i.addEventListener(e.Event.COMPLETE,this.onBlobLoaded,this),i.addEventListener(e.IOErrorEvent.IO_ERROR,this.onBlobError,this),i.responseType="blob"),i.open(r),i.send()}else this.loadImage(r)},n.prototype.onBlobLoaded=function(e){var t=this.request.response;this.request=void 0,this.loadImage(r.createObjectURL(t))},n.prototype.onBlobError=function(e){this.dispatchIOError(this.currentURL),this.request=void 0},n.prototype.loadImage=function(e){var t=new Image;this.data=null,this.currentImage=t,this._hasCrossOriginSet?this._crossOrigin&&(t.crossOrigin=this._crossOrigin):n.crossOrigin&&(t.crossOrigin=n.crossOrigin),t.onload=this.onImageComplete.bind(this),t.onerror=this.onLoadError.bind(this),t.src=e},n.prototype.onImageComplete=function(t){var r=this.getImage(t);if(r){this.data=new e.BitmapData(r);var i=this;window.setTimeout(function(){i.dispatchEventWith(e.Event.COMPLETE)},0)}},n.prototype.onLoadError=function(e){var t=this.getImage(e);t&&this.dispatchIOError(t.src)},n.prototype.dispatchIOError=function(t){var r=this;window.setTimeout(function(){r.dispatchEventWith(e.IOErrorEvent.IO_ERROR)},0)},n.prototype.getImage=function(t){var i=t.target,n=i.src;if(0==n.indexOf("blob:"))try{r.revokeObjectURL(i.src)}catch(a){e.$warn(1037)}return i.onerror=null,i.onload=null,this.currentImage!==i?null:(this.currentImage=null,i)},n.crossOrigin=null,n}(e.EventDispatcher);t.WebImageLoader=i,__reflect(i.prototype,"egret.web.WebImageLoader",["egret.ImageLoader"]),e.ImageLoader=i}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(t){function r(){var e=t.call(this)||this;return e._isNeedShow=!1,e.inputElement=null,e.inputDiv=null,e._gscaleX=0,e._gscaleY=0,e.textValue="",e.colorValue=16777215,e._styleInfoes={},e}return __extends(r,t),r.prototype.$setTextField=function(e){return this.$textfield=e,!0},r.prototype.$addToStage=function(){this.htmlInput=e.web.$getTextAdapter(this.$textfield)},r.prototype._initElement=function(){var t=this.$textfield.localToGlobal(0,0),r=t.x,i=t.y,n=this.htmlInput.$scaleX,a=this.htmlInput.$scaleY;this.inputDiv.style.left=r*n+"px",this.inputDiv.style.top=i*a+"px",this.$textfield.multiline&&this.$textfield.height>this.$textfield.size?(this.inputDiv.style.top=i*a+"px",this.inputElement.style.top=-this.$textfield.lineSpacing/2*a+"px"):(this.inputDiv.style.top=i*a+"px",this.inputElement.style.top="0px");for(var o=this.$textfield,s=1,l=1,h=0;o.parent;)s*=o.scaleX,l*=o.scaleY,h+=o.rotation,o=o.parent;var c=e.web.getPrefixStyleName("transform");this.inputDiv.style[c]="rotate("+h+"deg)",this._gscaleX=n*s,this._gscaleY=a*l},r.prototype.$show=function(e){void 0===e&&(e=!0),this.htmlInput.isCurrentStageText(this)?this.inputElement.onblur=null:(this.inputElement=this.htmlInput.getInputElement(this),this.$textfield.multiline?this.inputElement.type="text":("password"==this.inputElement.type&&"password"!=this.$textfield.inputType&&(this.htmlInput.initInputElement(!1),this.inputElement=this.htmlInput.getInputElement(this)),this.inputElement.type=this.$textfield.inputType),this.inputDiv=this.htmlInput._inputDIV),this.htmlInput._needShow=!0,this._isNeedShow=!0,this._initElement(),e&&this.activeShowKeyboard()},r.prototype.activeShowKeyboard=function(){this.htmlInput._needShow?(this._isNeedShow=!1,this.dispatchEvent(new e.Event("focus")),this.executeShow(),this.htmlInput.show()):(this.htmlInput.blurInputElement(),this.htmlInput.disposeInputElement())},r.prototype.onBlurHandler=function(){this.htmlInput.clearInputElement(),window.scrollTo(0,0)},r.prototype.onFocusHandler=function(){var e=this;window.setTimeout(function(){e.inputElement&&e.inputElement.scrollIntoView()},200)},r.prototype.executeShow=function(){this.inputElement.value!==this.$getText()&&(this.inputElement.value=this.$getText()),null==this.inputElement.onblur&&(this.inputElement.onblur=this.onBlurHandler.bind(this)),null==this.inputElement.onfocus&&(this.inputElement.onfocus=this.onFocusHandler.bind(this)),this.$resetStageText(),this.$textfield.maxChars>0?this.inputElement.setAttribute("maxlength",this.$textfield.maxChars+""):this.inputElement.removeAttribute("maxlength"),this.inputElement.selectionStart=this.inputElement.value.length,this.inputElement.selectionEnd=this.inputElement.value.length,this.inputElement.focus()},r.prototype.$hide=function(){this.htmlInput&&this.htmlInput.disconnectStageText(this)},r.prototype.$getText=function(){return this.textValue||(this.textValue=""),this.textValue},r.prototype.$setText=function(e){return this.textValue=e,this.resetText(),!0},r.prototype.resetText=function(){this.inputElement&&(this.inputElement.value=this.textValue)},r.prototype.$setColor=function(e){return this.colorValue=e,this.resetColor(),!0},r.prototype.resetColor=function(){this.inputElement&&this.setElementStyle("color",e.toColorString(this.colorValue))},r.prototype.$onBlur=function(){},r.prototype._onInput=function(){var t=this;window.setTimeout(function(){t.inputElement&&t.inputElement.selectionStart==t.inputElement.selectionEnd&&(t.textValue=t.inputElement.value,e.Event.dispatchEvent(t,"updateText",!1))},0)},r.prototype.setAreaHeight=function(){var t=this.$textfield;if(t.multiline){var r=e.TextFieldUtils.$getTextHeight(t);if(t.height<=t.size)this.setElementStyle("height",t.size*this._gscaleY+"px"),this.setElementStyle("padding","0px"),this.setElementStyle("lineHeight",t.size*this._gscaleY+"px");else if(t.height<r)this.setElementStyle("height",t.height*this._gscaleY+"px"),this.setElementStyle("padding","0px"),this.setElementStyle("lineHeight",(t.size+t.lineSpacing)*this._gscaleY+"px");else{this.setElementStyle("height",(r+t.lineSpacing)*this._gscaleY+"px");var i=(t.height-r)*this._gscaleY,n=e.TextFieldUtils.$getValign(t),a=i*n,o=i-a;this.setElementStyle("padding",a+"px 0px "+o+"px 0px"),this.setElementStyle("lineHeight",(t.size+t.lineSpacing)*this._gscaleY+"px")}}},r.prototype._onClickHandler=function(t){this._isNeedShow&&(t.stopImmediatePropagation(),this._isNeedShow=!1,this.dispatchEvent(new e.Event("focus")),this.executeShow())},r.prototype._onDisconnect=function(){this.inputElement=null,this.dispatchEvent(new e.Event("blur"))},r.prototype.setElementStyle=function(e,t){this.inputElement&&this._styleInfoes[e]!=t&&(this.inputElement.style[e]=t)},r.prototype.$removeFromStage=function(){this.inputElement&&this.htmlInput.disconnectStageText(this)},r.prototype.$resetStageText=function(){if(this.inputElement){var t=this.$textfield;this.setElementStyle("fontFamily",t.fontFamily),this.setElementStyle("fontStyle",t.italic?"italic":"normal"),this.setElementStyle("fontWeight",t.bold?"bold":"normal"),this.setElementStyle("textAlign",t.textAlign),this.setElementStyle("fontSize",t.size*this._gscaleY+"px"),this.setElementStyle("color",e.toColorString(t.textColor));var r=void 0;t.stage?(r=t.localToGlobal(0,0).x,r=Math.min(t.width,t.stage.stageWidth-r)):r=t.width;var i=r*this._gscaleX,n=t.scaleX*e.sys.DisplayList.$canvasScaleX/(t.scaleY*e.sys.DisplayList.$canvasScaleY);if(this.setElementStyle("width",i/n+"px"),this.setElementStyle("transform","scale("+n+",  1)"),this.setElementStyle("left",(n-1)*i/n/2+"px"),this.setElementStyle("verticalAlign",t.verticalAlign),t.multiline)this.setAreaHeight();else if(this.setElementStyle("lineHeight",t.size*this._gscaleY+"px"),t.height<t.size){this.setElementStyle("height",t.size*this._gscaleY+"px");var a=t.size/2*this._gscaleY;this.setElementStyle("padding","0px 0px "+a+"px 0px")}else{this.setElementStyle("height",t.size*this._gscaleY+"px");var o=(t.height-t.size)*this._gscaleY,s=e.TextFieldUtils.$getValign(t),l=o*s,a=o-l;a<t.size/2*this._gscaleY&&(a=t.size/2*this._gscaleY),this.setElementStyle("padding",l+"px 0px "+a+"px 0px")}this.inputDiv.style.clip="rect(0px "+t.width*this._gscaleX+"px "+t.height*this._gscaleY+"px 0px)",this.inputDiv.style.height=t.height*this._gscaleY+"px",this.inputDiv.style.width=r*this._gscaleX+"px"}},r}(e.EventDispatcher);t.HTML5StageText=r,__reflect(r.prototype,"egret.web.HTML5StageText",["egret.StageText"]),e.StageText=r}(t=e.web||(e.web={}))}(egret||(egret={})),function(e){var t;!function(t){var r=function(){function t(){var e=this;this._needShow=!1,this.$scaleX=1,this.$scaleY=1,this.stageTextClickHandler=function(t){e._needShow?(e._needShow=!1,e._stageText._onClickHandler(t),e.show()):(e.blurInputElement(),e.disposeInputElement())}}return t.prototype.isInputOn=function(){return null!=this._stageText},t.prototype.isCurrentStageText=function(e){return this._stageText==e},t.prototype.initValue=function(e){e.style.position="absolute",e.style.left="0px",e.style.top="0px",e.style.border="none",e.style.padding="0",e.ontouchmove=function(e){e.preventDefault()}},t.prototype.$updateSize=function(){if(this.canvas){this.$scaleX=e.sys.DisplayList.$canvasScaleX,this.$scaleY=e.sys.DisplayList.$canvasScaleY,this.StageDelegateDiv.style.left=this.canvas.style.left,this.StageDelegateDiv.style.top=this.canvas.style.top;
var t=e.web.getPrefixStyleName("transform");this.StageDelegateDiv.style[t]=this.canvas.style[t],this.StageDelegateDiv.style[e.web.getPrefixStyleName("transformOrigin")]="0% 0% 0px"}},t.prototype._initStageDelegateDiv=function(t,r){this.canvas=r;var i,n=this;i||(i=document.createElement("div"),this.StageDelegateDiv=i,i.id="StageDelegateDiv",t.appendChild(i),n.initValue(i),n._inputDIV=document.createElement("div"),n.initValue(n._inputDIV),n._inputDIV.style.width="0px",n._inputDIV.style.height="0px",n._inputDIV.style.left="0px",n._inputDIV.style.top="-100px",n._inputDIV.style[e.web.getPrefixStyleName("transformOrigin")]="0% 0% 0px",i.appendChild(n._inputDIV),this.canvas.addEventListener("click",this.stageTextClickHandler),n.initInputElement(!0),n.initInputElement(!1))},t.prototype.initInputElement=function(e){var t,r=this;e?(t=document.getElementById("egretTextarea"),t&&t.parentNode&&t.parentNode.removeChild(t),t=document.createElement("textarea"),t.style.resize="none",r._multiElement=t,t.id="egretTextarea"):(t=document.getElementById("egretInput"),t&&t.parentNode&&t.parentNode.removeChild(t),t=document.createElement("input"),r._simpleElement=t,t.id="egretInput"),t.type="text",r._inputDIV.appendChild(t),t.setAttribute("tabindex","-1"),t.style.width="1px",t.style.height="12px",r.initValue(t),t.style.outline="thin",t.style.background="none",t.style.overflow="hidden",t.style.wordBreak="break-all",t.style.opacity="0";var i=!1;t.oninput=function(){r._stageText&&!i&&r._stageText._onInput()},t.addEventListener("compositionstart",function(){i=!0}),t.addEventListener("compositionend",function(){i=!1,r._stageText&&!i&&r._stageText._onInput()})},t.prototype.show=function(){var t=this,r=t._inputElement;e.$callAsync(function(){r.style.opacity="1"},t)},t.prototype.disconnectStageText=function(e){(null==this._stageText||this._stageText==e)&&(this._inputElement&&this._inputElement.blur(),this.clearInputElement(),this._inputElement&&this._inputDIV.contains(this._inputElement)&&this._inputDIV.removeChild(this._inputElement),this._needShow=!1)},t.prototype.clearInputElement=function(){var e=this;if(e._inputElement){e._inputElement.value="",e._inputElement.onblur=null,e._inputElement.onfocus=null,e._inputElement.style.width="1px",e._inputElement.style.height="12px",e._inputElement.style.left="0px",e._inputElement.style.top="0px",e._inputElement.style.opacity="0";var t=void 0;t=e._simpleElement==e._inputElement?e._multiElement:e._simpleElement,t.style.display="block",e._inputDIV.style.left="0px",e._inputDIV.style.top="-100px",e._inputDIV.style.height="0px",e._inputDIV.style.width="0px",e._inputElement.blur()}e._stageText&&(e._stageText._onDisconnect(),e._stageText=null,this.canvas.userTyping=!1,this.finishUserTyping&&this.finishUserTyping())},t.prototype.getInputElement=function(e){var t=this;t.clearInputElement(),t._stageText=e,this.canvas.userTyping=!0,t._stageText.$textfield.multiline?t._inputElement=t._multiElement:t._inputElement=t._simpleElement;var r;return r=t._simpleElement==t._inputElement?t._multiElement:t._simpleElement,r.style.display="none",this._inputElement&&!this._inputDIV.contains(this._inputElement)&&this._inputDIV.appendChild(this._inputElement),t._inputElement},t.prototype.blurInputElement=function(){this._inputElement&&(this.clearInputElement(),this._inputElement.blur())},t.prototype.disposeInputElement=function(){this._inputElement=null},t}();t.HTMLInput=r,__reflect(r.prototype,"egret.web.HTMLInput")}(t=e.web||(e.web={}))}(egret||(egret={})),function(e){var t;!function(e){function t(e){var t=e.stage?e.stage.$hashCode:0,r=i[t],o=n[t],s=a[t];return o&&s&&(delete n[t],delete a[t]),r}function r(e,t,r,o){e._initStageDelegateDiv(r,o),i[t.$hashCode]=e,n[t.$hashCode]=o,a[t.$hashCode]=r}var i={},n={},a={};e.$getTextAdapter=t,e.$cacheTextAdapter=r}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(t,r,a,o,s){n||i();var l="";return s&&(l+="italic "),o&&(l+="bold "),l+=("number"==typeof a&&a>0?a:12)+"px ",l+="string"==typeof r&&""!=r?r:"Arial",n.font=l,e.sys.measureTextWith(n,t)}function i(){n=e.sys.canvasHitTestBuffer.context,n.textAlign="left",n.textBaseline="middle"}var n=null;e.sys.measureText=r}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(t,r){var i=e.sys.createCanvas(t,r),n=i.getContext("2d");if(void 0===n.imageSmoothingEnabled){for(var a,o=["webkitImageSmoothingEnabled","mozImageSmoothingEnabled","msImageSmoothingEnabled"],s=o.length-1;s>=0&&(a=o[s],void 0===n[a]);s--);try{Object.defineProperty(n,"imageSmoothingEnabled",{get:function(){return this[a]},set:function(e){this[a]=e}})}catch(l){n.imageSmoothingEnabled=n[a]}}return i}var i=function(){function t(t,i,n){this.surface=e.sys.createCanvasRenderBufferSurface(r,t,i,n),this.context=this.surface.getContext("2d"),this.context&&(this.context.$offsetX=0,this.context.$offsetY=0),this.resize(t,i)}return Object.defineProperty(t.prototype,"width",{get:function(){return this.surface.width},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"height",{get:function(){return this.surface.height},enumerable:!0,configurable:!0}),t.prototype.resize=function(t,r,i){e.sys.resizeCanvasRenderBuffer(this,t,r,i)},t.prototype.getPixels=function(e,t,r,i){return void 0===r&&(r=1),void 0===i&&(i=1),this.context.getImageData(e,t,r,i).data},t.prototype.toDataURL=function(e,t){return this.surface.toDataURL(e,t)},t.prototype.clear=function(){this.context.setTransform(1,0,0,1,0,0),this.context.clearRect(0,0,this.surface.width,this.surface.height)},t.prototype.destroy=function(){this.surface.width=this.surface.height=0},t}();t.CanvasRenderBuffer=i,__reflect(i.prototype,"egret.web.CanvasRenderBuffer",["egret.sys.RenderBuffer"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(t){function r(r,i){var n=t.call(this)||this;return n.onTouchBegin=function(e){var t=n.getLocation(e);n.touch.onTouchBegin(t.x,t.y,e.identifier)},n.onMouseMove=function(e){0==e.buttons?n.onTouchEnd(e):n.onTouchMove(e)},n.onTouchMove=function(e){var t=n.getLocation(e);n.touch.onTouchMove(t.x,t.y,e.identifier)},n.onTouchEnd=function(e){var t=n.getLocation(e);n.touch.onTouchEnd(t.x,t.y,e.identifier)},n.scaleX=1,n.scaleY=1,n.rotation=0,n.canvas=i,n.touch=new e.sys.TouchHandler(r),n.addListeners(),n}return __extends(r,t),r.prototype.addListeners=function(){var t=this;window.navigator.msPointerEnabled?(this.canvas.addEventListener("MSPointerDown",function(e){e.identifier=e.pointerId,t.onTouchBegin(e),t.prevent(e)},!1),this.canvas.addEventListener("MSPointerMove",function(e){e.identifier=e.pointerId,t.onTouchMove(e),t.prevent(e)},!1),this.canvas.addEventListener("MSPointerUp",function(e){e.identifier=e.pointerId,t.onTouchEnd(e),t.prevent(e)},!1)):(e.Capabilities.isMobile||this.addMouseListener(),this.addTouchListener())},r.prototype.addMouseListener=function(){this.canvas.addEventListener("mousedown",this.onTouchBegin),this.canvas.addEventListener("mousemove",this.onMouseMove),this.canvas.addEventListener("mouseup",this.onTouchEnd)},r.prototype.addTouchListener=function(){var e=this;this.canvas.addEventListener("touchstart",function(t){for(var r=t.changedTouches.length,i=0;r>i;i++)e.onTouchBegin(t.changedTouches[i]);e.prevent(t)},!1),this.canvas.addEventListener("touchmove",function(t){for(var r=t.changedTouches.length,i=0;r>i;i++)e.onTouchMove(t.changedTouches[i]);e.prevent(t)},!1),this.canvas.addEventListener("touchend",function(t){for(var r=t.changedTouches.length,i=0;r>i;i++)e.onTouchEnd(t.changedTouches[i]);e.prevent(t)},!1),this.canvas.addEventListener("touchcancel",function(t){for(var r=t.changedTouches.length,i=0;r>i;i++)e.onTouchEnd(t.changedTouches[i]);e.prevent(t)},!1)},r.prototype.prevent=function(e){e.stopPropagation(),1==e.isScroll||this.canvas.userTyping||e.preventDefault()},r.prototype.getLocation=function(t){t.identifier=+t.identifier||0;var r=document.documentElement,i=this.canvas.getBoundingClientRect(),n=i.left+window.pageXOffset-r.clientLeft,a=i.top+window.pageYOffset-r.clientTop,o=t.pageX-n,s=o,l=t.pageY-a,h=l;return 90==this.rotation?(s=l,h=i.width-o):-90==this.rotation&&(s=i.height-l,h=o),s/=this.scaleX,h/=this.scaleY,e.$TempPoint.setTo(Math.round(s),Math.round(h))},r.prototype.updateScaleMode=function(e,t,r){this.scaleX=e,this.scaleY=t,this.rotation=r},r.prototype.$updateMaxTouches=function(){this.touch.$initMaxTouches()},r}(e.HashObject);t.WebTouchHandler=r,__reflect(r.prototype,"egret.web.WebTouchHandler")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(e){e.WebLifeCycleHandler=function(t){var r=function(){t.resume(),e.Html5Capatibility._audioType==e.AudioType.WEB_AUDIO&&e.WebAudioDecode.initAudioContext&&e.WebAudioDecode.initAudioContext()},i=function(){t.pause()},n=function(){document[a]?i():r()};window.addEventListener("focus",r,!1),window.addEventListener("blur",i,!1);var a,o;"undefined"!=typeof document.hidden?(a="hidden",o="visibilitychange"):"undefined"!=typeof document.mozHidden?(a="mozHidden",o="mozvisibilitychange"):"undefined"!=typeof document.msHidden?(a="msHidden",o="msvisibilitychange"):"undefined"!=typeof document.webkitHidden?(a="webkitHidden",o="webkitvisibilitychange"):"undefined"!=typeof document.oHidden&&(a="oHidden",o="ovisibilitychange"),"onpageshow"in window&&"onpagehide"in window&&(window.addEventListener("pageshow",r,!1),window.addEventListener("pagehide",i,!1)),a&&o&&document.addEventListener(o,n,!1);var s=navigator.userAgent,l=/micromessenger/gi.test(s),h=/mqq/gi.test(s),c=/mobile.*qq/gi.test(s);if((c||l)&&(h=!1),h){var u=window.browser||{};u.execWebFn=u.execWebFn||{},u.execWebFn.postX5GamePlayerMessage=function(e){var t=e.type;"app_enter_background"==t?i():"app_enter_foreground"==t&&r()},window.browser=u}}}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(e,t){var r="";if(null!=t)r=i(e,t);else{if(null==o){var n=document.createElement("div").style;o=i("transform",n)}r=o}return""==r?e:r+e.charAt(0).toUpperCase()+e.substring(1,e.length)}function i(e,t){if(e in t)return"";e=e.charAt(0).toUpperCase()+e.substring(1,e.length);for(var r=["webkit","ms","Moz","O"],i=0;i<r.length;i++){var n=r[i]+e;if(n in t)return r[i]}return""}var n=function(){function e(){}return e.WEB_AUDIO=2,e.HTML5_AUDIO=3,e}();t.AudioType=n,__reflect(n.prototype,"egret.web.AudioType");var a=function(r){function i(){return r.call(this)||this}return __extends(i,r),i.$init=function(){var r=navigator.userAgent.toLowerCase();i.ua=r,i._canUseBlob=!1;var a=window.AudioContext||window.webkitAudioContext||window.mozAudioContext,o=r.indexOf("iphone")>=0||r.indexOf("ipad")>=0||r.indexOf("ipod")>=0;if(a)try{t.WebAudioDecode.initAudioContext=function(){if(t.WebAudioDecode.ctx)try{t.WebAudioDecode.ctx.close()}catch(r){}t.WebAudioDecode.ctx=new(window.AudioContext||window.webkitAudioContext||window.mozAudioContext);for(var i=e.sys.usingChannel,n=0,a=i;n<a.length;n++){var o=a[n],s=o;s.context=t.WebAudioDecode.ctx,s.initGain(),s.$startTime=s.position,s.$play()}},t.WebAudioDecode.initAudioContext()}catch(s){a=!1}var l,h=i._audioType;h==n.WEB_AUDIO&&a||h==n.HTML5_AUDIO?(l=!1,i.setAudioType(h)):!o&&r.indexOf("safari")>=0&&-1===r.indexOf("chrome")?(l=!1,i.setAudioType(n.WEB_AUDIO)):(l=!0,i.setAudioType(n.HTML5_AUDIO)),r.indexOf("android")>=0?l&&a&&i.setAudioType(n.WEB_AUDIO):o&&i.getIOSVersion()>=7&&(i._canUseBlob=!0,l&&a&&i.setAudioType(n.WEB_AUDIO));var c=window.URL||window.webkitURL;c||(i._canUseBlob=!1),r.indexOf("egretnative")>=0&&(i.setAudioType(n.HTML5_AUDIO),i._canUseBlob=!0),e.Sound=i._AudioClass},i.setAudioType=function(t){switch(i._audioType=t,t){case n.WEB_AUDIO:i._AudioClass=e.web.WebAudioSound;break;case n.HTML5_AUDIO:i._AudioClass=e.web.HtmlSound}},i.getIOSVersion=function(){var e=i.ua.toLowerCase().match(/cpu [^\d]*\d.*like mac os x/);if(!e||0==e.length)return 0;var t=e[0];return parseInt(t.match(/\d+(_\d)*/)[0])||0},i._canUseBlob=!1,i._audioType=0,i.ua="",i}(e.HashObject);t.Html5Capatibility=a,__reflect(a.prototype,"egret.web.Html5Capatibility");var o=null;t.getPrefixStyleName=r,t.getPrefix=i}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(t,r){var n=i(t,r);return e.pro.egret2dDriveMode&&(e.pro.mainCanvas=n),n}function i(e,t){var r=document.createElement("canvas");return isNaN(e)||isNaN(t)||(r.width=e,r.height=t),r}function n(e,t,r,i){if(e){var n=e,a=n.surface;i?(a.width<t&&(a.width=t),a.height<r&&(a.height=r)):(a.width!==t&&(a.width=t),a.height!==r&&(a.height=r)),n.onResize()}}function a(r){for(var i={antialias:t.WebGLRenderContext.antialias,stencil:!0},n=null,a=["webgl","experimental-webgl"],o=0;o<a.length;++o){try{n=r.getContext(a[o],i)}catch(s){}if(n)break}return n||e.$error(1021),n}function o(e){return e?e.getContext("2d"):null}function s(t,r){var i=t,n=i.context,a=n.createTexture();return a?(a[e.glContext]=n,n.bindTexture(n.TEXTURE_2D,a),n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,1),a[e.UNPACK_PREMULTIPLY_ALPHA_WEBGL]=!0,n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,r),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.LINEAR),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.LINEAR),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE),a):void(i.contextLost=!0)}function l(t,r,i,n){var a=t,o=a.context,s=o.createTexture();return s?(s[e.glContext]=o,o.bindTexture(o.TEXTURE_2D,s),o.pixelStorei(o.UNPACK_PREMULTIPLY_ALPHA_WEBGL,1),s[e.UNPACK_PREMULTIPLY_ALPHA_WEBGL]=!0,o.texImage2D(o.TEXTURE_2D,0,o.RGBA,r,i,0,o.RGBA,o.UNSIGNED_BYTE,n),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_MAG_FILTER,o.LINEAR),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_MIN_FILTER,o.LINEAR),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_WRAP_S,o.CLAMP_TO_EDGE),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_WRAP_T,o.CLAMP_TO_EDGE),s):(a.contextLost=!0,null)}function h(e,t,r){var i=e,n=i.context;n.activeTexture(n.TEXTURE0),n.bindTexture(n.TEXTURE_2D,t.texture);var a=3*t.count;return n.drawElements(n.TRIANGLES,a,n.UNSIGNED_SHORT,2*r),a}function c(e,t){return e.measureText(t).width}function u(e,t,r,i){return e(t,r)}function d(e,t,r,i){var n=e,a=n.surface;if(i){var o=!1;a.width<t&&(a.width=t,o=!0),a.height<r&&(a.height=r,o=!0),o||(n.context.globalCompositeOperation="source-over",n.context.setTransform(1,0,0,1,0,0),n.context.globalAlpha=1)}else a.width!=t&&(a.width=t),a.height!=r&&(a.height=r);n.clear()}function f(e,t){return window.FontFace?p(e,t):v(e,t)}function p(t,r){var i=e.sys.fontResourceCache;if(!i||!i[r])return void console.warn("registerFontMapping_WARN: Can not find TTF file:"+r+", please load file first.");var n=i[r],a=new window.FontFace(t,n);document.fonts.add(a),a.load()["catch"](function(e){console.error("loadFontError:",e)})}function v(e,t){var r=document.createElement("style");r.type="text/css",r.textContent='\n            @font-face\n            {\n                font-family:"'+e+'";\n                src:url("'+t+'");\n            }',r.onerror=function(e){console.error("loadFontError:",e)},document.body.appendChild(r)}e.sys.mainCanvas=r,e.sys.createCanvas=i,t.resizeContext=n,e.sys.resizeContext=n,e.sys.getContextWebGL=a,t.getContext2d=o,e.sys.getContext2d=o,e.sys.createTexture=s,e.sys._createTexture=l,e.sys.drawTextureElements=h,e.sys.measureTextWith=c,e.sys.createCanvasRenderBufferSurface=u,e.sys.resizeCanvasRenderBuffer=d,e.Geolocation=e.web.WebGeolocation,e.Motion=e.web.WebMotion,e.sys.registerFontMapping=f,t.isIOS14Device=function(){return e.Capabilities.runtimeType==e.RuntimeType.WEB&&"iOS"==e.Capabilities.os&&e.Capabilities.isMobile&&/iPhone OS 14/.test(window.navigator.userAgent)}}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(){if(s)for(var e=document.querySelectorAll(".egret-player"),t=e.length,r=0;t>r;r++){var i=e[r],n=i["egret-player"];n.updateScreenSize()}}function i(r){if(!s){s=!0,r||(r={});var i=navigator.userAgent.toLowerCase();if(i.indexOf("egretnative")>=0&&-1==i.indexOf("egretwebview")&&(e.Capabilities.runtimeType=e.RuntimeType.RUNTIME2),r.pro){e.pro.egret2dDriveMode=!0;try{window.startup?window.startup():console.error("EgretPro.js don't has function:window.startup")}catch(h){console.error(h)}}if(i.indexOf("egretnative")>=0&&e.nativeRender)egret_native.addModuleCallback(function(){if(t.Html5Capatibility.$init(),"webgl"==r.renderMode){var i=r.antialias;t.WebGLRenderContext.antialias=!!i}e.sys.CanvasRenderBuffer=t.CanvasRenderBuffer,n(r.renderMode),egret_native.nrSetRenderMode(2);var s;s=r.canvasScaleFactor?r.canvasScaleFactor:r.calculateCanvasScaleFactor?r.calculateCanvasScaleFactor(e.sys.canvasHitTestBuffer.context):window.devicePixelRatio,e.sys.DisplayList.$canvasScaleFactor=s;var h=e.ticker;a(h),r.screenAdapter?e.sys.screenAdapter=r.screenAdapter:e.sys.screenAdapter||(e.sys.screenAdapter=new e.sys.DefaultScreenAdapter);for(var c=document.querySelectorAll(".egret-player"),u=c.length,d=0;u>d;d++){var f=c[d],p=new t.WebPlayer(f,r);f["egret-player"]=p}window.addEventListener("resize",function(){isNaN(l)&&(l=window.setTimeout(o,300))})},null),egret_native.initNativeRender();else{t.Html5Capatibility._audioType=r.audioType,t.Html5Capatibility.$init();var c=r.renderMode;if("webgl"==c){var u=r.antialias;t.WebGLRenderContext.antialias=!!u}e.sys.CanvasRenderBuffer=t.CanvasRenderBuffer,i.indexOf("egretnative")>=0&&"webgl"!=c&&(e.$warn(1051),c="webgl"),n(c);var d=void 0;if(r.canvasScaleFactor)d=r.canvasScaleFactor;else if(r.calculateCanvasScaleFactor)d=r.calculateCanvasScaleFactor(e.sys.canvasHitTestBuffer.context);else{var f=e.sys.canvasHitTestBuffer.context,p=f.backingStorePixelRatio||f.webkitBackingStorePixelRatio||f.mozBackingStorePixelRatio||f.msBackingStorePixelRatio||f.oBackingStorePixelRatio||f.backingStorePixelRatio||1;d=(window.devicePixelRatio||1)/p}e.sys.DisplayList.$canvasScaleFactor=d;var v=e.ticker;a(v),r.screenAdapter?e.sys.screenAdapter=r.screenAdapter:e.sys.screenAdapter||(e.sys.screenAdapter=new e.sys.DefaultScreenAdapter);for(var g=document.querySelectorAll(".egret-player"),x=g.length,y=0;x>y;y++){var m=g[y],b=new t.WebPlayer(m,r);m["egret-player"]=b}window.addEventListener("resize",function(){isNaN(l)&&(l=window.setTimeout(o,300))})}}}function n(r){"webgl"==r&&e.WebGLUtils.checkCanUseWebGL()?(e.sys.RenderBuffer=t.WebGLRenderBuffer,e.sys.systemRenderer=new t.WebGLRenderer,e.sys.canvasRenderer=new e.CanvasRenderer,e.sys.customHitTestBuffer=new t.WebGLRenderBuffer(3,3),e.sys.canvasHitTestBuffer=new t.CanvasRenderBuffer(3,3),e.Capabilities.renderMode="webgl"):(e.sys.RenderBuffer=t.CanvasRenderBuffer,e.sys.systemRenderer=new e.CanvasRenderer,e.sys.canvasRenderer=e.sys.systemRenderer,e.sys.customHitTestBuffer=new t.CanvasRenderBuffer(3,3),e.sys.canvasHitTestBuffer=e.sys.customHitTestBuffer,e.Capabilities.renderMode="canvas")}function a(e){function t(){r(t),e.update()}var r=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;r||(r=function(e){return window.setTimeout(e,1e3/60)}),r(t)}function o(){l=0/0,e.updateAllScreens()}var s=!1;e.sys.setRenderMode=n,window.isNaN=function(e){return e=+e,e!==e},e.runEgret=i,e.updateAllScreens=r;var l=0/0}(t=e.web||(e.web={}))}(egret||(egret={}));var language,egret;!function(e){var t;!function(t){var r=function(){function t(){}return t.detect=function(){var r=e.Capabilities,i=navigator.userAgent.toLowerCase();r.isMobile=-1!=i.indexOf("mobile")||-1!=i.indexOf("android"),r.isMobile?i.indexOf("windows")<0&&(-1!=i.indexOf("iphone")||-1!=i.indexOf("ipad")||-1!=i.indexOf("ipod"))?r.os="iOS":-1!=i.indexOf("android")&&-1!=i.indexOf("linux")?r.os="Android":-1!=i.indexOf("windows")&&(r.os="Windows Phone"):-1!=i.indexOf("windows nt")?r.os="Windows PC":"MacIntel"==navigator.platform&&navigator.maxTouchPoints>1?(r.os="iOS",r.isMobile=!0):-1!=i.indexOf("mac os")&&(r.os="Mac OS");var n=(navigator.language||navigator.browserLanguage).toLowerCase(),a=n.split("-");a.length>1&&(a[1]=a[1].toUpperCase()),r.language=a.join("-"),t.injectUIntFixOnIE9()},t.injectUIntFixOnIE9=function(){if(/msie 9.0/i.test(navigator.userAgent)&&!/opera/i.test(navigator.userAgent)){var e="<!-- IEBinaryToArray_ByteStr -->\r\n<script type='text/vbscript' language='VBScript'>\r\nFunction IEBinaryToArray_ByteStr(Binary)\r\n   IEBinaryToArray_ByteStr = CStr(Binary)\r\nEnd Function\r\nFunction IEBinaryToArray_ByteStr_Last(Binary)\r\n   Dim lastIndex\r\n   lastIndex = LenB(Binary)\r\n   if lastIndex mod 2 Then\r\n       IEBinaryToArray_ByteStr_Last = Chr( AscB( MidB( Binary, lastIndex, 1 ) ) )\r\n   Else\r\n       IEBinaryToArray_ByteStr_Last = \"\"\r\n   End If\r\nEnd Function\r\n</script>\r\n<!-- convertResponseBodyToText -->\r\n<script>\r\nlet convertResponseBodyToText = function (binary) {\r\n   let byteMapping = {};\r\n   for ( let i = 0; i < 256; i++ ) {\r\n       for ( let j = 0; j < 256; j++ ) {\r\n           byteMapping[ String.fromCharCode( i + j * 256 ) ] =\r\n           String.fromCharCode(i) + String.fromCharCode(j);\r\n       }\r\n   }\r\n   let rawBytes = IEBinaryToArray_ByteStr(binary);\r\n   let lastChr = IEBinaryToArray_ByteStr_Last(binary);\r\n   return rawBytes.replace(/[\\s\\S]/g,                           function( match ) { return byteMapping[match]; }) + lastChr;\r\n};\r\n</script>\r\n";document.write(e)}},t}();t.WebCapability=r,__reflect(r.prototype,"egret.web.WebCapability"),r.detect()}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(){function t(t,r,i,n,a){if(this.showPanle=!0,this.fpsHeight=0,this.WIDTH=101,this.HEIGHT=20,this.bgCanvasColor="#18304b",this.fpsFrontColor="#18fefe",this.WIDTH_COST=50,this.cost1Color="#18fefe",this.cost3Color="#ff0000",this.arrFps=[],this.arrCost=[],this.arrLog=[],r||i){"canvas"==e.Capabilities.renderMode?this.renderMode="Canvas":this.renderMode="WebGL",this.panelX=void 0===a.x?0:parseInt(a.x),this.panelY=void 0===a.y?0:parseInt(a.y),this.fontColor=void 0===a.textColor?"#ffffff":a.textColor.replace("0x","#"),this.fontSize=void 0===a.size?12:parseInt(a.size),e.Capabilities.isMobile&&(this.fontSize-=2);var o=document.createElement("div");o.style.position="absolute",o.style.background="rgba(0,0,0,"+a.bgAlpha+")",o.style.left=this.panelX+"px",o.style.top=this.panelY+"px",o.style.pointerEvents="none",o.id="egret-fps-panel",document.body.appendChild(o);var s=document.createElement("div");s.style.color=this.fontColor,s.style.fontSize=this.fontSize+"px",s.style.lineHeight=this.fontSize+"px",s.style.margin="4px 4px 4px 4px",this.container=s,o.appendChild(s),r&&this.addFps(),i&&this.addLog()}}return t.prototype.addFps=function(){var e=document.createElement("div");e.style.display="inline-block",this.containerFps=e,this.container.appendChild(e);var t=document.createElement("div");t.style.paddingBottom="2px",this.fps=t,this.containerFps.appendChild(t),t.innerHTML="0 FPS "+this.renderMode+"<br/>min0 max0 avg0";var r=document.createElement("canvas");this.containerFps.appendChild(r),r.width=this.WIDTH,r.height=this.HEIGHT,this.canvasFps=r;var i=r.getContext("2d");this.contextFps=i,i.fillStyle=this.bgCanvasColor,i.fillRect(0,0,this.WIDTH,this.HEIGHT);var n=document.createElement("div");this.divDatas=n,this.containerFps.appendChild(n);var a=document.createElement("div");a.style["float"]="left",a.innerHTML="Draw<br/>Cost",n.appendChild(a);var o=document.createElement("div");o.style.paddingLeft=a.offsetWidth+20+"px",n.appendChild(o);var s=document.createElement("div");this.divDraw=s,s.innerHTML="0<br/>",o.appendChild(s);var l=document.createElement("div");this.divCost=l,l.innerHTML='<font  style="color:'+this.cost1Color+'">0<font/> <font  style="color:'+this.cost3Color+'">0<font/>',o.appendChild(l),r=document.createElement("canvas"),this.canvasCost=r,this.containerFps.appendChild(r),r.width=this.WIDTH,r.height=this.HEIGHT,i=r.getContext("2d"),this.contextCost=i,i.fillStyle=this.bgCanvasColor,i.fillRect(0,0,this.WIDTH,this.HEIGHT),i.fillStyle="#000000",i.fillRect(this.WIDTH_COST,0,1,this.HEIGHT),this.fpsHeight=this.container.offsetHeight},t.prototype.addLog=function(){var e=document.createElement("div");e.style.maxWidth=document.body.clientWidth-8-this.panelX+"px",e.style.wordWrap="break-word",this.log=e,this.container.appendChild(e)},t.prototype.update=function(e,t){void 0===t&&(t=!1);var r,i,n;t?(r=this.arrFps[this.arrFps.length-1],i=this.arrCost[this.arrCost.length-1][0],n=this.arrCost[this.arrCost.length-1][1]):(r=e.fps,i=e.costTicker,n=e.costRender,this.lastNumDraw=e.draw,this.arrFps.push(r),this.arrCost.push([i,n]));var a=0,o=this.arrFps.length;o>101&&(o=101,this.arrFps.shift(),this.arrCost.shift());for(var s=this.arrFps[0],l=this.arrFps[0],h=0;o>h;h++){var c=this.arrFps[h];a+=c,s>c?s=c:c>l&&(l=c)}var u=this.WIDTH,d=this.HEIGHT,f=this.contextFps;f.drawImage(this.canvasFps,1,0,u-1,d,0,0,u-1,d),f.fillStyle=this.bgCanvasColor,f.fillRect(u-1,0,1,d);var p=Math.floor(r/60*20);1>p&&(p=1),f.fillStyle=this.fpsFrontColor,f.fillRect(u-1,20-p,1,p);var v=this.WIDTH_COST;f=this.contextCost,f.drawImage(this.canvasCost,1,0,v-1,d,0,0,v-1,d),f.drawImage(this.canvasCost,v+2,0,v-1,d,v+1,0,v-1,d);var g=Math.floor(i/2);1>g?g=1:g>20&&(g=20);var x=Math.floor(n/2);1>x?x=1:x>20&&(x=20),f.fillStyle=this.bgCanvasColor,f.fillRect(v-1,0,1,d),f.fillRect(2*v,0,1,d),f.fillRect(3*v+1,0,1,d),f.fillStyle=this.cost1Color,f.fillRect(v-1,20-g,1,g),f.fillStyle=this.cost3Color,f.fillRect(2*v,20-x,1,x);var y=Math.floor(a/o),m=r+" FPS "+this.renderMode;this.showPanle&&(m+="<br/>min"+s+" max"+l+" avg"+y,this.divDraw.innerHTML=this.lastNumDraw+"<br/>",this.divCost.innerHTML='<font  style="color:#18fefe">'+i+'<font/> <font  style="color:#ff0000">'+n+"<font/>"),this.fps.innerHTML=m},t.prototype.updateInfo=function(e){this.arrLog.push(e),this.updateLogLayout()},t.prototype.updateWarn=function(e){this.arrLog.push("[Warning]"+e),this.updateLogLayout()},t.prototype.updateError=function(e){this.arrLog.push("[Error]"+e),this.updateLogLayout()},t.prototype.updateLogLayout=function(){for(this.log.innerHTML=this.arrLog.join("<br/>");document.body.clientHeight<this.log.offsetHeight+this.fpsHeight+this.panelY+2*this.fontSize;)this.arrLog.shift(),this.log.innerHTML=this.arrLog.join("<br/>")},t}();t.WebFps=r,__reflect(r.prototype,"egret.web.WebFps",["egret.FPSDisplay"]),e.FPSDisplay=r}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r;!function(r){function i(e){return window.localStorage.getItem(e)}function n(t,r){try{return window.localStorage.setItem(t,r),!0}catch(i){return e.$warn(1047,t,r),!1}}function a(e){window.localStorage.removeItem(e)}function o(){window.localStorage.clear()}t.getItem=i,t.setItem=n,t.removeItem=a,t.clear=o}(r=t.web||(t.web={}))}(t=e.localStorage||(e.localStorage={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(r){function i(e,t){var i=r.call(this)||this;return i.updateAfterTyping=!1,i.init(e,t),i.initOrientation(),i}return __extends(i,r),i.prototype.init=function(r,i){var n=this;console.log("Egret Engine Version:",e.Capabilities.engineVersion);var a=this.readOption(r,i),o=new e.Stage;o.$screen=this,o.$scaleMode=a.scaleMode,o.$orientation=a.orientation,o.$maxTouches=a.maxTouches,o.frameRate=a.frameRate,o.textureScaleFactor=a.textureScaleFactor;var s=new e.sys.RenderBuffer(void 0,void 0,!0),l=s.surface;this.attachCanvas(r,l);var h=new t.WebTouchHandler(o,l),c=new e.sys.Player(s,o,a.entryClassName);e.lifecycle.stage=o,e.lifecycle.addLifecycleListener(t.WebLifeCycleHandler);var u=new t.HTMLInput;(a.showFPS||a.showLog)&&(e.nativeRender||c.displayFPS(a.showFPS,a.showLog,a.logFilter,a.fpsStyles)),this.playerOption=a,this.container=r,this.canvas=l,this.stage=o,this.player=c,this.webTouchHandler=h,this.webInput=u,u.finishUserTyping=function(){n.updateAfterTyping&&setTimeout(function(){n.updateScreenSize(),n.updateAfterTyping=!1},300)},e.web.$cacheTextAdapter(u,o,r,l),this.updateScreenSize(),this.updateMaxTouches(),c.start()},i.prototype.initOrientation=function(){var t=this;window.addEventListener("orientationchange",function(){window.setTimeout(function(){e.StageOrientationEvent.dispatchStageOrientationEvent(t.stage,e.StageOrientationEvent.ORIENTATION_CHANGE)},350)})},i.prototype.readOption=function(t,r){var i={};i.entryClassName=t.getAttribute("data-entry-class"),i.scaleMode=t.getAttribute("data-scale-mode")||e.StageScaleMode.NO_SCALE,i.frameRate=+t.getAttribute("data-frame-rate")||30,i.contentWidth=+t.getAttribute("data-content-width")||480,i.contentHeight=+t.getAttribute("data-content-height")||800,i.orientation=t.getAttribute("data-orientation")||e.OrientationMode.AUTO,i.maxTouches=+t.getAttribute("data-multi-fingered")||2,i.textureScaleFactor=+t.getAttribute("texture-scale-factor")||1,i.showFPS="true"==t.getAttribute("data-show-fps");for(var n=t.getAttribute("data-show-fps-style")||"",a=n.split(","),o={},s=0;s<a.length;s++){var l=a[s].split(":");o[l[0]]=l[1]}return i.fpsStyles=o,i.showLog="true"==t.getAttribute("data-show-log"),i.logFilter=t.getAttribute("data-log-filter"),i},i.prototype.attachCanvas=function(e,t){var r=t.style;r.cursor="inherit",r.position="absolute",r.top="0",r.bottom="0",r.left="0",r.right="0",e.appendChild(t),r=e.style,r.overflow="hidden",r.position="absolute"},i.prototype.updateScreenSize=function(){var t=this.canvas;if(t.userTyping)return void(this.updateAfterTyping=!0);var r=this.playerOption,i=this.container.getBoundingClientRect(),n=0,a=i.width,o=i.height;if(0!=a&&0!=o){i.top<0&&(o+=i.top,n=-i.top);var s=!1,l=this.stage.$orientation;l!=e.OrientationMode.AUTO&&(s=l!=e.OrientationMode.PORTRAIT&&o>a||l==e.OrientationMode.PORTRAIT&&a>o);var h=s?o:a,c=s?a:o;e.Capabilities.boundingClientWidth=h,e.Capabilities.boundingClientHeight=c;var u=e.sys.screenAdapter.calculateStageSize(this.stage.$scaleMode,h,c,r.contentWidth,r.contentHeight),d=u.stageWidth,f=u.stageHeight,p=u.displayWidth,v=u.displayHeight;t.style[e.web.getPrefixStyleName("transformOrigin")]="0% 0% 0px",t.width!=d&&(t.width=d),t.height!=f&&(t.height=f);var g=0;s?l==e.OrientationMode.LANDSCAPE?(g=90,t.style.top=n+(o-p)/2+"px",t.style.left=(a+v)/2+"px"):(g=-90,t.style.top=n+(o+p)/2+"px",t.style.left=(a-v)/2+"px"):(t.style.top=n+(o-v)/2+"px",t.style.left=(a-p)/2+"px");var x=p/d,y=v/f,m=x*e.sys.DisplayList.$canvasScaleFactor,b=y*e.sys.DisplayList.$canvasScaleFactor;m=Math.ceil(m),b=Math.ceil(b);var w=e.Matrix.create();w.identity(),w.scale(x/m,y/b),w.rotate(g*Math.PI/180);var T="matrix("+w.a+","+w.b+","+w.c+","+w.d+","+w.tx+","+w.ty+")";e.Matrix.release(w),t.style[e.web.getPrefixStyleName("transform")]=T,e.sys.DisplayList.$setCanvasScale(m,b),this.webTouchHandler.updateScaleMode(x,y,g),this.webInput.$updateSize(),this.player.updateStageSize(d,f),e.nativeRender&&(t.width=d*m,t.height=f*b)}},i.prototype.setContentSize=function(e,t){var r=this.playerOption;r.contentWidth=e,r.contentHeight=t,this.updateScreenSize()},i.prototype.updateMaxTouches=function(){this.webTouchHandler.$updateMaxTouches()},i}(e.HashObject);t.WebPlayer=r,__reflect(r.prototype,"egret.web.WebPlayer",["egret.sys.Screen"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(t,r){s||(s=e.sys.createCanvas(),l=s.getContext("2d"));var i=t.$getTextureWidth(),n=t.$getTextureHeight();null==r&&(r=e.$TempRectangle,r.x=0,r.y=0,r.width=i,r.height=n),r.x=Math.min(r.x,i-1),r.y=Math.min(r.y,n-1),r.width=Math.min(r.width,i-r.x),r.height=Math.min(r.height,n-r.y);var a=r.width,o=r.height,h=s;if(h.style.width=a+"px",h.style.height=o+"px",s.width=a,s.height=o,"webgl"==e.Capabilities.renderMode){var c=void 0;t.$renderBuffer?c=t:(e.sys.systemRenderer.renderClear&&e.sys.systemRenderer.renderClear(),c=new e.RenderTexture,c.drawToTexture(new e.Bitmap(t)));for(var u=c.$renderBuffer.getPixels(r.x,r.y,a,o),d=new ImageData(a,o),f=0;f<u.length;f++)d.data[f]=u[f];return l.putImageData(d,0,0),t.$renderBuffer||c.dispose(),h}var p=t,v=Math.round(p.$offsetX),g=Math.round(p.$offsetY),x=p.$bitmapWidth,y=p.$bitmapHeight;
return l.drawImage(p.$bitmapData.source,p.$bitmapX+r.x/e.$TextureScaleFactor,p.$bitmapY+r.y/e.$TextureScaleFactor,x*r.width/i,y*r.height/n,v,g,r.width,r.height),h}function i(t,i,n){try{var a=r(this,i),o=a.toDataURL(t,n);return o}catch(s){e.$error(1033)}return null}function n(e,t,r,n){var a=i.call(this,e,r,n);if(null!=a){var o=a.replace(/^data:image[^;]*/,"data:image/octet-stream"),s=document.createElement("a");s.download=t,s.href=o;var l=document.createEvent("MouseEvents");l.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),s.dispatchEvent(l)}}function a(t,r){return e.$warn(1041,"getPixel32","getPixels"),this.getPixels(t,r)}function o(t,i,n,a){if(void 0===n&&(n=1),void 0===a&&(a=1),"webgl"==e.Capabilities.renderMode){var o=void 0;this.$renderBuffer?o=this:(o=new e.RenderTexture,o.drawToTexture(new e.Bitmap(this)));var s=o.$renderBuffer.getPixels(t,i,n,a);return s}try{var h=(r(this),l.getImageData(t,i,n,a).data);return h}catch(c){e.$error(1039)}}var s,l;e.Texture.prototype.toDataURL=i,e.Texture.prototype.saveToFile=n,e.Texture.prototype.getPixel32=a,e.Texture.prototype.getPixels=o}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){function r(e){for(var t=s.parseFromString(e,"text/xml"),r=t.childNodes.length,n=0;r>n;n++){var a=t.childNodes[n];if(1==a.nodeType)return i(a,null)}return null}function i(e,t){if("parsererror"==e.localName)throw new Error(e.textContent);for(var r=new a(e.localName,t,e.prefix,e.namespaceURI,e.nodeName),n=e.attributes,s=r.attributes,l=n.length,h=0;l>h;h++){var c=n[h],u=c.name;0!=u.indexOf("xmlns:")&&(s[u]=c.value,r["$"+u]=c.value)}var d=e.childNodes;l=d.length;for(var f=r.children,h=0;l>h;h++){var p=d[h],v=p.nodeType,g=null;if(1==v)g=i(p,r);else if(3==v){var x=p.textContent.trim();x&&(g=new o(x,r))}g&&f.push(g)}return r}var n=function(){function e(e,t){this.nodeType=e,this.parent=t}return e}();t.XMLNode=n,__reflect(n.prototype,"egret.web.XMLNode");var a=function(e){function t(t,r,i,n,a){var o=e.call(this,1,r)||this;return o.attributes={},o.children=[],o.localName=t,o.prefix=i,o.namespace=n,o.name=a,o}return __extends(t,e),t}(n);t.XML=a,__reflect(a.prototype,"egret.web.XML");var o=function(e){function t(t,r){var i=e.call(this,3,r)||this;return i.text=t,i}return __extends(t,e),t}(n);t.XMLText=o,__reflect(o.prototype,"egret.web.XMLText");var s=new DOMParser;e.XML={parse:r}}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.onChange=function(t){var i=new e.OrientationEvent(e.Event.CHANGE);i.beta=t.beta,i.gamma=t.gamma,i.alpha=t.alpha,r.dispatchEvent(i)},r}return __extends(r,t),r.prototype.start=function(){window.addEventListener("deviceorientation",this.onChange)},r.prototype.stop=function(){window.removeEventListener("deviceorientation",this.onChange)},r}(e.EventDispatcher);t.WebDeviceOrientation=r,__reflect(r.prototype,"egret.web.WebDeviceOrientation",["egret.DeviceOrientation"])}(t=e.web||(e.web={}))}(egret||(egret={})),egret.DeviceOrientation=egret.web.WebDeviceOrientation;var egret;!function(e){var t;!function(t){var r=function(){function e(){}return e.call=function(e,t){},e.addCallback=function(e,t){},e}();t.WebExternalInterface=r,__reflect(r.prototype,"egret.web.WebExternalInterface",["egret.ExternalInterface"]);var i=navigator.userAgent.toLowerCase();i.indexOf("egretnative")<0&&(e.ExternalInterface=r)}(t=e.web||(e.web={}))}(egret||(egret={})),function(e){var t;!function(t){function r(t){var r=JSON.parse(t),n=r.functionName,a=i[n];if(a){var o=r.value;a.call(null,o)}else e.$warn(1050,n)}var i={},n=function(){function e(){}return e.call=function(e,t){var r={};r.functionName=e,r.value=t,egret_native.sendInfoToPlugin(JSON.stringify(r))},e.addCallback=function(e,t){i[e]=t},e}();t.NativeExternalInterface=n,__reflect(n.prototype,"egret.web.NativeExternalInterface",["egret.ExternalInterface"]);var a=navigator.userAgent.toLowerCase();a.indexOf("egretnative")>=0&&(e.ExternalInterface=n,egret_native.receivedPluginInfo=r)}(t=e.web||(e.web={}))}(egret||(egret={})),function(e){var t;!function(t){var r={},i=function(){function t(){}return t.call=function(e,t){__global.ExternalInterface.call(e,t)},t.addCallback=function(e,t){r[e]=t},t.invokeCallback=function(t,i){var n=r[t];n?n.call(null,i):e.$warn(1050,t)},t}();t.WebViewExternalInterface=i,__reflect(i.prototype,"egret.web.WebViewExternalInterface",["egret.ExternalInterface"]);var n=navigator.userAgent.toLowerCase();n.indexOf("egretwebview")>=0&&(e.ExternalInterface=i)}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(r){function i(){var e=r.call(this)||this;return e.loaded=!1,e}return __extends(i,r),Object.defineProperty(i.prototype,"length",{get:function(){if(this.originAudio)return this.originAudio.duration;throw new Error("sound not loaded!")},enumerable:!0,configurable:!0}),i.prototype.load=function(t){function r(){delete i.loadingSoundMap[t],i.$recycle(o.url,s),a(),l.indexOf("firefox")>=0&&(s.pause(),s.muted=!1),h&&document.body.appendChild(s),o.loaded=!0,o.dispatchEventWith(e.Event.COMPLETE)}function n(){a(),o.dispatchEventWith(e.IOErrorEvent.IO_ERROR)}function a(){s.removeEventListener("canplaythrough",r),s.removeEventListener("error",n),h&&document.body.removeChild(s)}var o=this;this.url=t;var s=new Audio(t);s.addEventListener("canplaythrough",r),s.addEventListener("error",n);var l=navigator.userAgent.toLowerCase();l.indexOf("firefox")>=0&&(s.autoplay=!0,s.muted=!0);var h=l.indexOf("edge")>=0||l.indexOf("trident")>=0;h&&document.body.appendChild(s),s.load(),i.loadingSoundMap[t]=s,this.originAudio=s,i.clearAudios[this.url]&&delete i.clearAudios[this.url]},i.prototype.play=function(r,n){r=+r||0,n=+n||0;var a=i.$pop(this.url);null==a&&(a=this.originAudio.cloneNode()),a.autoplay=!0;var o=new t.HtmlSoundChannel(a);return o.$url=this.url,o.$loops=n,o.$startTime=r,o.$play(),e.sys.$pushSoundChannel(o),o},i.prototype.close=function(){this.loaded&&this.originAudio&&(this.originAudio.src=""),this.originAudio&&(this.originAudio=null),i.$clear(this.url),this.loaded=!1},i.$clear=function(e){i.clearAudios[e]=!0;var t=i.audios[e];t&&(t.length=0)},i.$pop=function(e){var t=i.audios[e];return t&&t.length>0?t.pop():null},i.$recycle=function(e,t){if(!i.clearAudios[e]){var r=i.audios[e];null==i.audios[e]&&(r=i.audios[e]=[]),r.push(t)}},i.MUSIC="music",i.EFFECT="effect",i.loadingSoundMap={},i.audios={},i.clearAudios={},i}(e.EventDispatcher);t.HtmlSound=r,__reflect(r.prototype,"egret.web.HtmlSound",["egret.Sound"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(e){}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(e){var t=function(){function e(){this.drawData=[],this.drawDataLen=0}return e.prototype.pushDrawRect=function(){if(0==this.drawDataLen||1!=this.drawData[this.drawDataLen-1].type){var e=this.drawData[this.drawDataLen]||{};e.type=1,e.count=0,this.drawData[this.drawDataLen]=e,this.drawDataLen++}this.drawData[this.drawDataLen-1].count+=2},e.prototype.pushDrawTexture=function(e,t,r,i,n){if(void 0===t&&(t=2),r){var a=this.drawData[this.drawDataLen]||{};a.type=0,a.texture=e,a.filter=r,a.count=t,a.textureWidth=i,a.textureHeight=n,this.drawData[this.drawDataLen]=a,this.drawDataLen++}else{if(0==this.drawDataLen||0!=this.drawData[this.drawDataLen-1].type||e!=this.drawData[this.drawDataLen-1].texture||this.drawData[this.drawDataLen-1].filter){var a=this.drawData[this.drawDataLen]||{};a.type=0,a.texture=e,a.count=0,this.drawData[this.drawDataLen]=a,this.drawDataLen++}this.drawData[this.drawDataLen-1].count+=t}},e.prototype.pushChangeSmoothing=function(e,t){e.smoothing=t;var r=this.drawData[this.drawDataLen]||{};r.type=10,r.texture=e,r.smoothing=t,this.drawData[this.drawDataLen]=r,this.drawDataLen++},e.prototype.pushPushMask=function(e){void 0===e&&(e=1);var t=this.drawData[this.drawDataLen]||{};t.type=2,t.count=2*e,this.drawData[this.drawDataLen]=t,this.drawDataLen++},e.prototype.pushPopMask=function(e){void 0===e&&(e=1);var t=this.drawData[this.drawDataLen]||{};t.type=3,t.count=2*e,this.drawData[this.drawDataLen]=t,this.drawDataLen++},e.prototype.pushSetBlend=function(e){for(var t=this.drawDataLen,r=!1,i=t-1;i>=0;i--){var n=this.drawData[i];if(n){if((0==n.type||1==n.type)&&(r=!0),!r&&4==n.type){this.drawData.splice(i,1),this.drawDataLen--;continue}if(4==n.type){if(n.value==e)return;break}}}var a=this.drawData[this.drawDataLen]||{};a.type=4,a.value=e,this.drawData[this.drawDataLen]=a,this.drawDataLen++},e.prototype.pushResize=function(e,t,r){var i=this.drawData[this.drawDataLen]||{};i.type=5,i.buffer=e,i.width=t,i.height=r,this.drawData[this.drawDataLen]=i,this.drawDataLen++},e.prototype.pushClearColor=function(){var e=this.drawData[this.drawDataLen]||{};e.type=6,this.drawData[this.drawDataLen]=e,this.drawDataLen++},e.prototype.pushActivateBuffer=function(e){for(var t=this.drawDataLen,r=!1,i=t-1;i>=0;i--){var n=this.drawData[i];!n||(4!=n.type&&7!=n.type&&(r=!0),r||7!=n.type)||(this.drawData.splice(i,1),this.drawDataLen--)}var a=this.drawData[this.drawDataLen]||{};a.type=7,a.buffer=e,a.width=e.rootRenderTarget.width,a.height=e.rootRenderTarget.height,this.drawData[this.drawDataLen]=a,this.drawDataLen++},e.prototype.pushEnableScissor=function(e,t,r,i){var n=this.drawData[this.drawDataLen]||{};n.type=8,n.x=e,n.y=t,n.width=r,n.height=i,this.drawData[this.drawDataLen]=n,this.drawDataLen++},e.prototype.pushDisableScissor=function(){var e=this.drawData[this.drawDataLen]||{};e.type=9,this.drawData[this.drawDataLen]=e,this.drawDataLen++},e.prototype.clear=function(){for(var e=0;e<this.drawDataLen;e++){var t=this.drawData[e];t.type=0,t.count=0,t.texture=null,t.filter=null,t.value="",t.buffer=null,t.width=0,t.height=0,t.textureWidth=0,t.textureHeight=0,t.smoothing=!1,t.x=0,t.y=0}this.drawDataLen=0},e}();e.WebGLDrawCmdManager=t,__reflect(t.prototype,"egret.web.WebGLDrawCmdManager")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(r){function i(t){var i=r.call(this)||this;return i.$startTime=0,i.audio=null,i.isStopped=!1,i.canPlay=function(){i.audio.removeEventListener("canplay",i.canPlay);try{i.audio.currentTime=i.$startTime}catch(e){}finally{i.audio.play()}},i.onPlayEnd=function(){return 1==i.$loops?(i.stop(),void i.dispatchEventWith(e.Event.SOUND_COMPLETE)):(i.$loops>0&&i.$loops--,void i.$play())},i._volume=1,t.addEventListener("ended",i.onPlayEnd),i.audio=t,i}return __extends(i,r),i.prototype.$play=function(){if(this.isStopped)return void e.$error(1036);try{this.audio.volume=this._volume,this.audio.currentTime=this.$startTime}catch(t){return void this.audio.addEventListener("canplay",this.canPlay)}this.audio.play()},i.prototype.stop=function(){if(this.audio){this.isStopped||e.sys.$popSoundChannel(this),this.isStopped=!0;var r=this.audio;r.removeEventListener("ended",this.onPlayEnd),r.removeEventListener("canplay",this.canPlay),r.volume=0,this._volume=0,this.audio=null;var i=this.$url;window.setTimeout(function(){r.pause(),t.HtmlSound.$recycle(i,r)},200)}},Object.defineProperty(i.prototype,"volume",{get:function(){return this._volume},set:function(t){return this.isStopped?void e.$error(1036):(this._volume=t,void(this.audio&&(this.audio.volume=t)))},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"position",{get:function(){return this.audio?this.audio.currentTime:0},enumerable:!0,configurable:!0}),i}(e.EventDispatcher);t.HtmlSoundChannel=r,__reflect(r.prototype,"egret.web.HtmlSoundChannel",["egret.SoundChannel","egret.IEventDispatcher"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(r){function i(e,t,i){var n=r.call(this)||this;return n.clearColor=[0,0,0,0],n.useFrameBuffer=!0,n.gl=e,n._resize(t,i),n}return __extends(i,r),i.prototype._resize=function(e,t){e=e||1,t=t||1,1>e&&(e=1),1>t&&(t=1),this.width=e,this.height=t},i.prototype.resize=function(e,t){this._resize(e,t);var r=this.gl;this.frameBuffer&&(r.bindTexture(r.TEXTURE_2D,this.texture),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,this.width,this.height,0,r.RGBA,r.UNSIGNED_BYTE,null)),this.stencilBuffer&&(r.deleteRenderbuffer(this.stencilBuffer),this.stencilBuffer=null)},i.prototype.activate=function(){var e=this.gl;e.bindFramebuffer(e.FRAMEBUFFER,this.getFrameBuffer())},i.prototype.getFrameBuffer=function(){return this.useFrameBuffer?this.frameBuffer:null},i.prototype.initFrameBuffer=function(){if(!this.frameBuffer){var e=this.gl;this.texture=this.createTexture(),this.frameBuffer=e.createFramebuffer(),e.bindFramebuffer(e.FRAMEBUFFER,this.frameBuffer),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,this.texture,0)}},i.prototype.createTexture=function(){var r=t.WebGLRenderContext.getInstance(0,0);return e.sys._createTexture(r,this.width,this.height,null)},i.prototype.clear=function(e){var t=this.gl;e&&this.activate(),t.colorMask(!0,!0,!0,!0),t.clearColor(this.clearColor[0],this.clearColor[1],this.clearColor[2],this.clearColor[3]),t.clear(t.COLOR_BUFFER_BIT)},i.prototype.enabledStencil=function(){if(this.frameBuffer&&!this.stencilBuffer){var e=this.gl;e.bindFramebuffer(e.FRAMEBUFFER,this.frameBuffer),this.stencilBuffer=e.createRenderbuffer(),e.bindRenderbuffer(e.RENDERBUFFER,this.stencilBuffer),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_STENCIL,this.width,this.height),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_STENCIL_ATTACHMENT,e.RENDERBUFFER,this.stencilBuffer)}},i.prototype.dispose=function(){e.WebGLUtils.deleteWebGLTexture(this.texture)},i}(e.HashObject);t.WebGLRenderTarget=r,__reflect(r.prototype,"egret.web.WebGLRenderTarget")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r={},i=function(){function i(r,i,n){if(this._defaultEmptyTexture=null,this.glID=null,this.projectionX=0/0,this.projectionY=0/0,this.contextLost=!1,this._supportedCompressedTextureInfo=[],this.$scissorState=!1,this.vertexCountPerTriangle=3,this.triangleCountPerQuad=2,this.dataCountPerVertex=5,this.vertSize=5,this.$beforeRender=function(){var e=this.context;e.bindBuffer(e.ARRAY_BUFFER,this.vertexBuffer),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,this.indexBuffer),e.disable(e.DEPTH_TEST),e.disable(e.CULL_FACE),e.enable(e.BLEND),e.disable(e.STENCIL_TEST),e.colorMask(!0,!0,!0,!0),this.setBlendMode("source-over"),e.activeTexture(e.TEXTURE0),this.currentProgram=null},this.surface=e.sys.mainCanvas(r,i),!e.nativeRender){this.initWebGL(n),this.getSupportedCompressedTexture(),this.$bufferStack=[];var a=this.context;this.vertexBuffer=a.createBuffer(),this.indexBuffer=a.createBuffer(),a.bindBuffer(a.ARRAY_BUFFER,this.vertexBuffer),a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,this.indexBuffer),this.drawCmdManager=new t.WebGLDrawCmdManager,this.vao=new t.WebGLVertexArrayObject,this.setGlobalCompositeOperation("source-over")}}return i.getInstance=function(e,t,r){return this.instance?this.instance:(this.instance=new i(e,t,r),this.instance)},i.prototype.pushBuffer=function(e){this.$bufferStack.push(e),e!=this.currentBuffer&&(this.currentBuffer,this.drawCmdManager.pushActivateBuffer(e)),this.currentBuffer=e},i.prototype.popBuffer=function(){if(!(this.$bufferStack.length<=1)){var e=this.$bufferStack.pop(),t=this.$bufferStack[this.$bufferStack.length-1];e!=t&&this.drawCmdManager.pushActivateBuffer(t),this.currentBuffer=t}},i.prototype.activateBuffer=function(e,t,r){e.rootRenderTarget.activate(),this.bindIndices||this.uploadIndicesArray(this.vao.getIndices()),e.restoreStencil(),e.restoreScissor(),this.onResize(t,r)},i.prototype.uploadVerticesArray=function(e){var t=this.context;t.bufferData(t.ARRAY_BUFFER,e,t.STREAM_DRAW)},i.prototype.uploadIndicesArray=function(e){var t=this.context;t.bufferData(t.ELEMENT_ARRAY_BUFFER,e,t.STATIC_DRAW),this.bindIndices=!0},i.prototype.destroy=function(){this.surface.width=this.surface.height=0},i.prototype.onResize=function(e,t){e=e||this.surface.width,t=t||this.surface.height,this.projectionX=e/2,this.projectionY=-t/2,this.context&&this.context.viewport(0,0,e,t)},i.prototype.resize=function(t,r,i){e.sys.resizeContext(this,t,r,i)},i.prototype._buildSupportedCompressedTextureInfo=function(e){for(var t=[],r=0,i=e;r<i.length;r++){var n=i[r];if(n){var a={extensionName:n.name,supportedFormats:[]};for(var o in n)a.supportedFormats.push([o,n[o]]);var o;t.push(a)}}return t},i.prototype.initWebGL=function(e){this.onResize(),this.surface.addEventListener("webglcontextlost",this.handleContextLost.bind(this),!1),this.surface.addEventListener("webglcontextrestored",this.handleContextRestored.bind(this),!1),e?this.setContext(e):this.getWebGLContext();var t=this.context;this.$maxTextureSize=t.getParameter(t.MAX_TEXTURE_SIZE)},i.prototype.getSupportedCompressedTexture=function(){var t=this.context?this.context:e.sys.getContextWebGL(this.surface);this.pvrtc=t.getExtension("WEBGL_compressed_texture_pvrtc")||t.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),this.pvrtc&&(this.pvrtc.name="WEBGL_compressed_texture_pvrtc"),this.etc1=t.getExtension("WEBGL_compressed_texture_etc1")||t.getExtension("WEBKIT_WEBGL_compressed_texture_etc1"),this.etc1&&(this.etc1.name="WEBGL_compressed_texture_etc1"),e.Capabilities._supportedCompressedTexture?(e.Capabilities._supportedCompressedTexture=e.Capabilities._supportedCompressedTexture||{},e.Capabilities._supportedCompressedTexture.pvrtc=!!this.pvrtc,e.Capabilities._supportedCompressedTexture.etc1=!!this.etc1):(e.Capabilities.supportedCompressedTexture=e.Capabilities._supportedCompressedTexture||{},e.Capabilities.supportedCompressedTexture.pvrtc=!!this.pvrtc,e.Capabilities.supportedCompressedTexture.etc1=!!this.etc1),this._supportedCompressedTextureInfo=this._buildSupportedCompressedTextureInfo([this.etc1,this.pvrtc])},i.prototype.handleContextLost=function(){this.contextLost=!0},i.prototype.handleContextRestored=function(){this.initWebGL(),this.contextLost=!1},i.prototype.getWebGLContext=function(){var t=e.sys.getContextWebGL(this.surface);return this.setContext(t),t},i.prototype.setContext=function(e){this.context=e,e.id=i.glContextId++,this.glID=e.id,e.disable(e.DEPTH_TEST),e.disable(e.CULL_FACE),e.enable(e.BLEND),e.colorMask(!0,!0,!0,!0),e.activeTexture(e.TEXTURE0)},i.prototype.enableStencilTest=function(){var e=this.context;e.enable(e.STENCIL_TEST)},i.prototype.disableStencilTest=function(){var e=this.context;e.disable(e.STENCIL_TEST)},i.prototype.enableScissorTest=function(e){var t=this.context;t.enable(t.SCISSOR_TEST),t.scissor(e.x,e.y,e.width,e.height)},i.prototype.disableScissorTest=function(){var e=this.context;e.disable(e.SCISSOR_TEST)},i.prototype.getPixels=function(e,t,r,i,n){var a=this.context;a.readPixels(e,t,r,i,a.RGBA,a.UNSIGNED_BYTE,n)},i.prototype.createTexture=function(t){return e.sys.createTexture(this,t)},i.prototype.checkCompressedTextureInternalFormat=function(e,t){for(var r=0,i=e.length;i>r;++r)for(var n=e[r],a=n.supportedFormats,o=0,s=a.length;s>o;++o)if(a[o][1]===t)return!0;return!1},i.prototype.$debugLogCompressedTextureNotSupported=function(t,i){if(!r[i]){r[i]=!0,e.log("internalFormat = "+i+":0x"+i.toString(16)+", the current hardware does not support the corresponding compression format.");for(var n=0,a=t.length;a>n;++n){var o=t[n];if(o.supportedFormats.length>0){e.log("support = "+o.extensionName);for(var s=0,l=o.supportedFormats.length;l>s;++s){var h=o.supportedFormats[s];e.log(h[0]+" : "+h[1]+" : 0x"+h[1].toString(16))}}}}},i.prototype.createCompressedTexture=function(t,r,i,n,a){var o=this.checkCompressedTextureInternalFormat(this._supportedCompressedTextureInfo,a);if(!o)return this.$debugLogCompressedTextureNotSupported(this._supportedCompressedTextureInfo,a),this.defaultEmptyTexture;var s=this.context,l=s.createTexture();return l?(l[e.glContext]=s,l[e.is_compressed_texture]=!0,s.bindTexture(s.TEXTURE_2D,l),s.pixelStorei(s.UNPACK_PREMULTIPLY_ALPHA_WEBGL,1),l[e.UNPACK_PREMULTIPLY_ALPHA_WEBGL]=!0,s.compressedTexImage2D(s.TEXTURE_2D,n,a,r,i,0,t),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.bindTexture(s.TEXTURE_2D,null),l):void(this.contextLost=!0)},i.prototype.updateTexture=function(e,t){var r=this.context;r.bindTexture(r.TEXTURE_2D,e),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,1),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,r.RGBA,r.UNSIGNED_BYTE,t)},Object.defineProperty(i.prototype,"defaultEmptyTexture",{get:function(){if(!this._defaultEmptyTexture){var t=16,r=e.sys.createCanvas(t,t),i=e.sys.getContext2d(r);i.fillStyle="white",i.fillRect(0,0,t,t),this._defaultEmptyTexture=this.createTexture(r),this._defaultEmptyTexture[e.engine_default_empty_texture]=!0}return this._defaultEmptyTexture},enumerable:!0,configurable:!0}),i.prototype.getWebGLTexture=function(t){if(!t.webGLTexture){if("image"!=t.format||t.hasCompressed2d()){if(t.hasCompressed2d()){var r=t.getCompressed2dTextureData();t.webGLTexture=this.createCompressedTexture(r.byteArray,r.width,r.height,r.level,r.glInternalFormat);var i=t.etcAlphaMask;if(i){var n=this.getWebGLTexture(i);n&&(t.webGLTexture[e.etc_alpha_mask]=n)}}}else t.webGLTexture=this.createTexture(t.source);t.$deleteSource&&t.webGLTexture&&(t.source&&(t.source.src="",t.source=null),t.clearCompressedTextureData()),t.webGLTexture&&(t.webGLTexture.smoothing=!0)}return t.webGLTexture},i.prototype.clearRect=function(e,t,r,i){if(0!=e||0!=t||r!=this.surface.width||i!=this.surface.height){var n=this.currentBuffer;if(n.$hasScissor)this.setGlobalCompositeOperation("destination-out"),this.drawRect(e,t,r,i),this.setGlobalCompositeOperation("source-over");else{var a=n.globalMatrix;0==a.b&&0==a.c?(e=e*a.a+a.tx,t=t*a.d+a.ty,r*=a.a,i*=a.d,this.enableScissor(e,-t-i+n.height,r,i),this.clear(),this.disableScissor()):(this.setGlobalCompositeOperation("destination-out"),this.drawRect(e,t,r,i),this.setGlobalCompositeOperation("source-over"))}}else this.clear()},i.prototype.setGlobalCompositeOperation=function(e){this.drawCmdManager.pushSetBlend(e)},i.prototype.drawImage=function(e,t,r,i,n,a,o,s,l,h,c,u,d){var f=this.currentBuffer;if(!this.contextLost&&e&&f){var p,v,g;if(e.texture||e.source&&e.source.texture)p=e.texture||e.source.texture,f.saveTransform(),v=f.$offsetX,g=f.$offsetY,f.useOffset(),f.transform(1,0,0,-1,0,l+2*o);else{if(!e.source&&!e.webGLTexture)return;p=this.getWebGLTexture(e)}p&&(this.drawTexture(p,t,r,i,n,a,o,s,l,h,c,void 0,void 0,void 0,void 0,u,d),e.source&&e.source.texture&&(f.$offsetX=v,f.$offsetY=g,f.restoreTransform()))}},i.prototype.drawMesh=function(e,t,r,i,n,a,o,s,l,h,c,u,d,f,p,v,g){var x=this.currentBuffer;if(!this.contextLost&&e&&x){var y,m,b;if(e.texture||e.source&&e.source.texture)y=e.texture||e.source.texture,x.saveTransform(),m=x.$offsetX,b=x.$offsetY,x.useOffset(),x.transform(1,0,0,-1,0,l+2*o);else{if(!e.source&&!e.webGLTexture)return;y=this.getWebGLTexture(e)}y&&(this.drawTexture(y,t,r,i,n,a,o,s,l,h,c,u,d,f,p,v,g),(e.texture||e.source&&e.source.texture)&&(x.$offsetX=m,x.$offsetY=b,x.restoreTransform()))}},i.prototype.drawTexture=function(e,r,i,n,a,o,s,l,h,c,u,d,f,p,v,g,x){var y=this.currentBuffer;if(!this.contextLost&&e&&y){var m;if(t.isIOS14Device()){var b=p&&p.length/3||0;p?this.vao.reachMaxSize(4*b,6*b)&&this.$drawWebGL():this.vao.reachMaxSize()&&this.$drawWebGL(),void 0!=x&&e.smoothing!=x&&this.drawCmdManager.pushChangeSmoothing(e,x),m=p?2*b:2}else f&&p?this.vao.reachMaxSize(f.length/2,p.length)&&this.$drawWebGL():this.vao.reachMaxSize()&&this.$drawWebGL(),void 0!=x&&e.smoothing!=x&&this.drawCmdManager.pushChangeSmoothing(e,x),d&&this.vao.changeToMeshIndices(),m=p?p.length/3:2;this.drawCmdManager.pushDrawTexture(e,m,this.$filter,c,u),y.currentTexture=e,this.vao.cacheArrays(y,r,i,n,a,o,s,l,h,c,u,d,f,p,g)}},i.prototype.drawRect=function(e,t,r,i){var n=this.currentBuffer;!this.contextLost&&n&&(this.vao.reachMaxSize()&&this.$drawWebGL(),this.drawCmdManager.pushDrawRect(),n.currentTexture=null,this.vao.cacheArrays(n,0,0,r,i,e,t,r,i,r,i))},i.prototype.pushMask=function(e,t,r,i){var n=this.currentBuffer;!this.contextLost&&n&&(n.$stencilList.push({x:e,y:t,width:r,height:i}),this.vao.reachMaxSize()&&this.$drawWebGL(),this.drawCmdManager.pushPushMask(),n.currentTexture=null,this.vao.cacheArrays(n,0,0,r,i,e,t,r,i,r,i))},i.prototype.popMask=function(){var e=this.currentBuffer;if(!this.contextLost&&e){var t=e.$stencilList.pop();this.vao.reachMaxSize()&&this.$drawWebGL(),this.drawCmdManager.pushPopMask(),e.currentTexture=null,this.vao.cacheArrays(e,0,0,t.width,t.height,t.x,t.y,t.width,t.height,t.width,t.height)}},i.prototype.clear=function(){this.drawCmdManager.pushClearColor()},i.prototype.enableScissor=function(e,t,r,i){var n=this.currentBuffer;this.drawCmdManager.pushEnableScissor(e,t,r,i),n.$hasScissor=!0},i.prototype.disableScissor=function(){var e=this.currentBuffer;this.drawCmdManager.pushDisableScissor(),e.$hasScissor=!1},i.prototype.$drawWebGL=function(){if(0!=this.drawCmdManager.drawDataLen&&!this.contextLost){var e=this.vao.getIndices(),r=this.vao.getVertices();t.isIOS14Device()||this.uploadVerticesArray(r),this.vao.isMesh()&&this.uploadIndicesArray(this.vao.getMeshIndices());for(var i=this.drawCmdManager.drawDataLen,n=0,a=0;i>a;a++){var o=this.drawCmdManager.drawData[a],s=0==o.type||1==o.type||2==o.type||3==o.type;t.isIOS14Device()&&!this.vao.isMesh()&&s?(this.uploadIndicesArray(e.subarray(0,o.count*this.vertexCountPerTriangle)),this.uploadVerticesArray(this.vao.vertices.subarray(n/this.vertexCountPerTriangle*this.triangleCountPerQuad*this.dataCountPerVertex,(n+o.count*this.vertexCountPerTriangle)/this.vertexCountPerTriangle*this.triangleCountPerQuad*this.dataCountPerVertex)),this.drawData(o,0),n+=o.count*this.vertexCountPerTriangle):n=this.drawData(o,n),7==o.type&&(this.activatedBuffer=o.buffer),s&&this.activatedBuffer&&this.activatedBuffer.$computeDrawCall&&this.activatedBuffer.$drawCalls++}this.vao.isMesh()&&this.uploadIndicesArray(this.vao.getIndices()),this.drawCmdManager.clear(),this.vao.clear()}},i.prototype.drawData=function(r,i){if(r){var n,a=this.context,o=r.filter;switch(r.type){case 0:o?"custom"===o.type?n=t.EgretWebGLProgram.getProgram(a,o.$vertexSrc,o.$fragmentSrc,o.$shaderKey):"colorTransform"===o.type?r.texture[e.etc_alpha_mask]?(a.activeTexture(a.TEXTURE1),a.bindTexture(a.TEXTURE_2D,r.texture[e.etc_alpha_mask]),n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.colorTransform_frag_etc_alphamask_frag,"colorTransform_frag_etc_alphamask_frag")):n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.colorTransform_frag,"colorTransform"):"blurX"===o.type?n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.blur_frag,"blur"):"blurY"===o.type?n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.blur_frag,"blur"):"glow"===o.type&&(n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.glow_frag,"glow")):r.texture[e.etc_alpha_mask]?(n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.texture_etc_alphamask_frag,e.etc_alpha_mask),a.activeTexture(a.TEXTURE1),a.bindTexture(a.TEXTURE_2D,r.texture[e.etc_alpha_mask])):n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.texture_frag,"texture"),this.activeProgram(a,n),this.syncUniforms(n,o,r.textureWidth,r.textureHeight),i+=this.drawTextureElements(r,i);break;case 1:n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.primitive_frag,"primitive"),this.activeProgram(a,n),this.syncUniforms(n,o,r.textureWidth,r.textureHeight),i+=this.drawRectElements(r,i);break;case 2:n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.primitive_frag,"primitive"),this.activeProgram(a,n),this.syncUniforms(n,o,r.textureWidth,r.textureHeight),i+=this.drawPushMaskElements(r,i);break;case 3:n=t.EgretWebGLProgram.getProgram(a,t.EgretShaderLib.default_vert,t.EgretShaderLib.primitive_frag,"primitive"),this.activeProgram(a,n),this.syncUniforms(n,o,r.textureWidth,r.textureHeight),i+=this.drawPopMaskElements(r,i);break;case 4:this.setBlendMode(r.value);break;case 5:r.buffer.rootRenderTarget.resize(r.width,r.height),this.onResize(r.width,r.height);break;case 6:if(this.activatedBuffer){var s=this.activatedBuffer.rootRenderTarget;(0!=s.width||0!=s.height)&&s.clear(!0)}break;case 7:this.activateBuffer(r.buffer,r.width,r.height);break;case 8:var l=this.activatedBuffer;l&&(l.rootRenderTarget&&l.rootRenderTarget.enabledStencil(),l.enableScissor(r.x,r.y,r.width,r.height));break;case 9:l=this.activatedBuffer,l&&l.disableScissor();break;case 10:a.bindTexture(a.TEXTURE_2D,r.texture),r.smoothing?(a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MAG_FILTER,a.LINEAR),a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MIN_FILTER,a.LINEAR)):(a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MAG_FILTER,a.NEAREST),a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MIN_FILTER,a.NEAREST))}return i}},i.prototype.activeProgram=function(t,r){if(e.pro.egret2dDriveMode||r!=this.currentProgram){t.useProgram(r.id);var i=r.attributes;for(var n in i)"aVertexPosition"===n?(t.vertexAttribPointer(i.aVertexPosition.location,2,t.FLOAT,!1,20,0),t.enableVertexAttribArray(i.aVertexPosition.location)):"aTextureCoord"===n?(t.vertexAttribPointer(i.aTextureCoord.location,2,t.FLOAT,!1,20,8),t.enableVertexAttribArray(i.aTextureCoord.location)):"aColor"===n&&(t.vertexAttribPointer(i.aColor.location,4,t.UNSIGNED_BYTE,!0,20,16),t.enableVertexAttribArray(i.aColor.location));this.currentProgram=r}},i.prototype.syncUniforms=function(e,t,r,i){var n=e.uniforms;t&&"custom"===t.type;for(var a in n)if("$filterScale"!=a)if("projectionVector"===a)n[a].setValue({x:this.projectionX,y:this.projectionY});else if("uTextureSize"===a)n[a].setValue({x:r,y:i});else if("uSampler"===a)n[a].setValue(0);else if("uSamplerAlphaMask"===a)n[a].setValue(1);else{var o=t.$uniforms[a];if(void 0!==o){if("glow"==t.type||0==t.type.indexOf("blur"))if("blurX"==a||"blurY"==a||"dist"==a)o*=t.$uniforms.$filterScale||1;else if("blur"==a&&void 0!=o.x&&void 0!=o.y){var s={x:0,y:0};s.x=o.x*(void 0!=t.$uniforms.$filterScale?t.$uniforms.$filterScale:1),s.y=o.y*(void 0!=t.$uniforms.$filterScale?t.$uniforms.$filterScale:1),n[a].setValue(s);continue}n[a].setValue(o)}}},i.prototype.drawTextureElements=function(t,r){return e.sys.drawTextureElements(this,t,r)},i.prototype.drawRectElements=function(e,t){var r=this.context,i=3*e.count;return r.drawElements(r.TRIANGLES,i,r.UNSIGNED_SHORT,2*t),i},i.prototype.drawPushMaskElements=function(e,t){var r=this.context,i=3*e.count,n=this.activatedBuffer;if(n){n.rootRenderTarget&&n.rootRenderTarget.enabledStencil(),0==n.stencilHandleCount&&(n.enableStencil(),r.clear(r.STENCIL_BUFFER_BIT));var a=n.stencilHandleCount;n.stencilHandleCount++,r.colorMask(!1,!1,!1,!1),r.stencilFunc(r.EQUAL,a,255),r.stencilOp(r.KEEP,r.KEEP,r.INCR),r.drawElements(r.TRIANGLES,i,r.UNSIGNED_SHORT,2*t),r.stencilFunc(r.EQUAL,a+1,255),r.colorMask(!0,!0,!0,!0),r.stencilOp(r.KEEP,r.KEEP,r.KEEP)}return i},i.prototype.drawPopMaskElements=function(e,t){var r=this.context,i=3*e.count,n=this.activatedBuffer;if(n)if(n.stencilHandleCount--,0==n.stencilHandleCount)n.disableStencil();else{var a=n.stencilHandleCount;r.colorMask(!1,!1,!1,!1),r.stencilFunc(r.EQUAL,a+1,255),r.stencilOp(r.KEEP,r.KEEP,r.DECR),r.drawElements(r.TRIANGLES,i,r.UNSIGNED_SHORT,2*t),r.stencilFunc(r.EQUAL,a,255),r.colorMask(!0,!0,!0,!0),r.stencilOp(r.KEEP,r.KEEP,r.KEEP)}return i},i.prototype.setBlendMode=function(e){var t=this.context,r=i.blendModesForGL[e];r&&t.blendFunc(r[0],r[1])},i.prototype.drawTargetWidthFilters=function(r,i){var n,a=i,o=r.length;if(o>1)for(var s=0;o-1>s;s++){var l=r[s],h=i.rootRenderTarget.width,c=i.rootRenderTarget.height;n=t.WebGLRenderBuffer.create(h,c);var u=Math.max(e.sys.DisplayList.$canvasScaleFactor,2);n.setTransform(u,0,0,u,0,0),n.globalAlpha=1,this.drawToRenderTarget(l,i,n),i!=a&&t.WebGLRenderBuffer.release(i),i=n
}var d=r[o-1];this.drawToRenderTarget(d,i,this.currentBuffer),i!=a&&t.WebGLRenderBuffer.release(i)},i.prototype.drawToRenderTarget=function(r,i,n){if(!this.contextLost){this.vao.reachMaxSize()&&this.$drawWebGL(),this.pushBuffer(n);var a,o=i,s=i.rootRenderTarget.width,l=i.rootRenderTarget.height;if("blur"==r.type){var h=r.blurXFilter,c=r.blurYFilter;if(0!=h.blurX&&0!=c.blurY){a=t.WebGLRenderBuffer.create(s,l);var u=Math.max(e.sys.DisplayList.$canvasScaleFactor,2);a.setTransform(1,0,0,1,0,0),a.transform(u,0,0,u,0,0),a.globalAlpha=1,this.drawToRenderTarget(r.blurXFilter,i,a),i!=o&&t.WebGLRenderBuffer.release(i),i=a,r=c}else r=0===h.blurX?c:h}n.saveTransform();var d=Math.max(e.sys.DisplayList.$canvasScaleFactor,2);n.transform(1/d,0,0,1/d,0,0),n.transform(1,0,0,-1,0,l),n.currentTexture=i.rootRenderTarget.texture,this.vao.cacheArrays(n,0,0,s,l,0,0,s,l,s,l),n.restoreTransform(),this.drawCmdManager.pushDrawTexture(i.rootRenderTarget.texture,2,r,s,l),i!=o&&t.WebGLRenderBuffer.release(i),this.popBuffer()}},i.initBlendMode=function(){i.blendModesForGL={},i.blendModesForGL["source-over"]=[1,771],i.blendModesForGL.lighter=[1,1],i.blendModesForGL["lighter-in"]=[770,771],i.blendModesForGL["destination-out"]=[0,771],i.blendModesForGL["destination-in"]=[0,770]},i.glContextId=0,i.blendModesForGL=null,i}();t.WebGLRenderContext=i,__reflect(i.prototype,"egret.web.WebGLRenderContext",["egret.sys.RenderContext"]),i.initBlendMode(),e.sys.WebGLRenderContext=i}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(r){function n(i,n,a){var o=r.call(this)||this;if(o.currentTexture=null,o.globalAlpha=1,o.globalTintColor=16777215,o.stencilState=!1,o.$stencilList=[],o.stencilHandleCount=0,o.$scissorState=!1,o.scissorRect=new e.Rectangle,o.$hasScissor=!1,o.$drawCalls=0,o.$computeDrawCall=!1,o.globalMatrix=new e.Matrix,o.savedGlobalMatrix=new e.Matrix,o.$offsetX=0,o.$offsetY=0,o.context=t.WebGLRenderContext.getInstance(i,n),e.nativeRender)return a?o.surface=o.context.surface:o.surface=new egret_native.NativeRenderSurface(o,i,n,a),o.rootRenderTarget=null,o;if(o.rootRenderTarget=new t.WebGLRenderTarget(o.context.context,3,3),i&&n&&o.resize(i,n),o.root=a,o.root)o.context.pushBuffer(o),o.surface=o.context.surface,o.$computeDrawCall=!0;else{var s=o.context.activatedBuffer;s&&s.rootRenderTarget.activate(),o.rootRenderTarget.initFrameBuffer(),o.surface=o.rootRenderTarget}return o}return __extends(n,r),n.prototype.enableStencil=function(){this.stencilState||(this.context.enableStencilTest(),this.stencilState=!0)},n.prototype.disableStencil=function(){this.stencilState&&(this.context.disableStencilTest(),this.stencilState=!1)},n.prototype.restoreStencil=function(){this.stencilState?this.context.enableStencilTest():this.context.disableStencilTest()},n.prototype.enableScissor=function(e,t,r,i){this.$scissorState||(this.$scissorState=!0,this.scissorRect.setTo(e,t,r,i),this.context.enableScissorTest(this.scissorRect))},n.prototype.disableScissor=function(){this.$scissorState&&(this.$scissorState=!1,this.scissorRect.setEmpty(),this.context.disableScissorTest())},n.prototype.restoreScissor=function(){this.$scissorState?this.context.enableScissorTest(this.scissorRect):this.context.disableScissorTest()},Object.defineProperty(n.prototype,"width",{get:function(){return e.nativeRender?this.surface.width:this.rootRenderTarget.width},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"height",{get:function(){return e.nativeRender?this.surface.height:this.rootRenderTarget.height},enumerable:!0,configurable:!0}),n.prototype.resize=function(t,r,i){return t=t||1,r=r||1,e.nativeRender?void this.surface.resize(t,r):(this.context.pushBuffer(this),(t!=this.rootRenderTarget.width||r!=this.rootRenderTarget.height)&&(this.context.drawCmdManager.pushResize(this,t,r),this.rootRenderTarget.width=t,this.rootRenderTarget.height=r),this.root&&this.context.resize(t,r,i),this.context.clear(),void this.context.popBuffer())},n.prototype.getPixels=function(t,r,i,n){void 0===i&&(i=1),void 0===n&&(n=1);var a=new Uint8Array(4*i*n);if(e.nativeRender)egret_native.activateBuffer(this),egret_native.nrGetPixels(t,r,i,n,a),egret_native.activateBuffer(null);else{var o=this.rootRenderTarget.useFrameBuffer;this.rootRenderTarget.useFrameBuffer=!0,this.rootRenderTarget.activate(),this.context.getPixels(t,this.height-r-n,i,n,a),this.rootRenderTarget.useFrameBuffer=o,this.rootRenderTarget.activate()}for(var s=new Uint8Array(4*i*n),l=0;n>l;l++)for(var h=0;i>h;h++){var c=4*(i*(n-l-1)+h),u=4*(i*l+h),d=a[u+3];s[c]=Math.round(a[u]/d*255),s[c+1]=Math.round(a[u+1]/d*255),s[c+2]=Math.round(a[u+2]/d*255),s[c+3]=a[u+3]}return s},n.prototype.toDataURL=function(e,t){return this.context.surface.toDataURL(e,t)},n.prototype.destroy=function(){this.context.destroy()},n.prototype.onRenderFinish=function(){this.$drawCalls=0},n.prototype.drawFrameBufferToSurface=function(e,t,r,i,n,a,o,s,l){void 0===l&&(l=!1),this.rootRenderTarget.useFrameBuffer=!1,this.rootRenderTarget.activate(),this.context.disableStencilTest(),this.context.disableScissorTest(),this.setTransform(1,0,0,1,0,0),this.globalAlpha=1,this.context.setGlobalCompositeOperation("source-over"),l&&this.context.clear(),this.context.drawImage(this.rootRenderTarget,e,t,r,i,n,a,o,s,r,i,!1),this.context.$drawWebGL(),this.rootRenderTarget.useFrameBuffer=!0,this.rootRenderTarget.activate(),this.restoreStencil(),this.restoreScissor()},n.prototype.drawSurfaceToFrameBuffer=function(e,t,r,i,n,a,o,s,l){void 0===l&&(l=!1),this.rootRenderTarget.useFrameBuffer=!0,this.rootRenderTarget.activate(),this.context.disableStencilTest(),this.context.disableScissorTest(),this.setTransform(1,0,0,1,0,0),this.globalAlpha=1,this.context.setGlobalCompositeOperation("source-over"),l&&this.context.clear(),this.context.drawImage(this.context.surface,e,t,r,i,n,a,o,s,r,i,!1),this.context.$drawWebGL(),this.rootRenderTarget.useFrameBuffer=!1,this.rootRenderTarget.activate(),this.restoreStencil(),this.restoreScissor()},n.prototype.clear=function(){this.context.pushBuffer(this),this.context.clear(),this.context.popBuffer()},n.prototype.setTransform=function(e,t,r,i,n,a){var o=this.globalMatrix;o.a=e,o.b=t,o.c=r,o.d=i,o.tx=n,o.ty=a},n.prototype.transform=function(e,t,r,i,n,a){var o=this.globalMatrix,s=o.a,l=o.b,h=o.c,c=o.d;(1!=e||0!=t||0!=r||1!=i)&&(o.a=e*s+t*h,o.b=e*l+t*c,o.c=r*s+i*h,o.d=r*l+i*c),o.tx=n*s+a*h+o.tx,o.ty=n*l+a*c+o.ty},n.prototype.useOffset=function(){var e=this;(0!=e.$offsetX||0!=e.$offsetY)&&(e.globalMatrix.append(1,0,0,1,e.$offsetX,e.$offsetY),e.$offsetX=e.$offsetY=0)},n.prototype.saveTransform=function(){var e=this.globalMatrix,t=this.savedGlobalMatrix;t.a=e.a,t.b=e.b,t.c=e.c,t.d=e.d,t.tx=e.tx,t.ty=e.ty},n.prototype.restoreTransform=function(){var e=this.globalMatrix,t=this.savedGlobalMatrix;e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e.tx=t.tx,e.ty=t.ty},n.create=function(e,t){var r=i.pop();if(r){r.resize(e,t);var a=r.globalMatrix;a.a=1,a.b=0,a.c=0,a.d=1,a.tx=0,a.ty=0,r.globalAlpha=1,r.$offsetX=0,r.$offsetY=0}else r=new n(e,t),r.$computeDrawCall=!1;return r},n.release=function(e){i.push(e)},n.autoClear=!0,n}(e.HashObject);t.WebGLRenderBuffer=r,__reflect(r.prototype,"egret.web.WebGLRenderBuffer",["egret.sys.RenderBuffer"]);var i=[]}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=["source-over","lighter","destination-out"],i="source-over",n=[],a=function(){function a(){this.wxiOS10=!1,this.nestLevel=0}return a.prototype.render=function(t,r,i,a){this.nestLevel++;var o=r,s=o.context;s.pushBuffer(o),o.transform(i.a,i.b,i.c,i.d,0,0),this.drawDisplayObject(t,o,i.tx,i.ty,!0),s.$drawWebGL();var l=o.$drawCalls;o.onRenderFinish(),s.popBuffer();var h=e.Matrix.create();if(i.$invertInto(h),o.transform(h.a,h.b,h.c,h.d,0,0),e.Matrix.release(h),this.nestLevel--,0===this.nestLevel){n.length>6&&(n.length=6);for(var c=n.length,u=0;c>u;u++)n[u].resize(0,0)}return l},a.prototype.drawDisplayObject=function(t,r,i,n,a){var o,s=0,l=t.$displayList;if(l&&!a?((t.$cacheDirty||t.$renderDirty||l.$canvasScaleX!=e.sys.DisplayList.$canvasScaleX||l.$canvasScaleY!=e.sys.DisplayList.$canvasScaleY)&&(s+=l.drawToSurface()),o=l.$renderNode):o=t.$renderDirty?t.$getRenderNode():t.$renderNode,t.$cacheDirty=!1,o){switch(s++,r.$offsetX=i,r.$offsetY=n,o.type){case 1:this.renderBitmap(o,r);break;case 2:this.renderText(o,r);break;case 3:this.renderGraphics(o,r);break;case 4:this.renderGroup(o,r);break;case 5:this.renderMesh(o,r);break;case 6:this.renderNormalBitmap(o,r)}r.$offsetX=0,r.$offsetY=0}if(l&&!a)return s;var h=t.$children;if(h){t.sortableChildren&&t.$sortDirty&&t.sortChildren();for(var c=h.length,u=0;c>u;u++){var d=h[u],f=void 0,p=void 0,v=void 0,g=void 0;1!=d.$alpha&&(v=r.globalAlpha,r.globalAlpha*=d.$alpha),16777215!==d.tint&&(g=r.globalTintColor,r.globalTintColor=d.$tintRGB);var x=void 0;if(d.$useTranslate){var y=d.$getMatrix();f=i+d.$x,p=n+d.$y;var m=r.globalMatrix;x=e.Matrix.create(),x.a=m.a,x.b=m.b,x.c=m.c,x.d=m.d,x.tx=m.tx,x.ty=m.ty,r.transform(y.a,y.b,y.c,y.d,f,p),f=-d.$anchorOffsetX,p=-d.$anchorOffsetY}else f=i+d.$x-d.$anchorOffsetX,p=n+d.$y-d.$anchorOffsetY;switch(d.$renderMode){case 1:break;case 2:s+=this.drawWithFilter(d,r,f,p);break;case 3:s+=this.drawWithClip(d,r,f,p);break;case 4:s+=this.drawWithScrollRect(d,r,f,p);break;default:s+=this.drawDisplayObject(d,r,f,p)}if(v&&(r.globalAlpha=v),g&&(r.globalTintColor=g),x){var y=r.globalMatrix;y.a=x.a,y.b=x.b,y.c=x.c,y.d=x.d,y.tx=x.tx,y.ty=x.ty,e.Matrix.release(x)}}}return s},a.prototype.drawWithFilter=function(t,a,o,s){var l=0;if(t.$children&&0==t.$children.length&&(!t.$renderNode||0==t.$renderNode.$getRenderCount()))return l;var h,c=t.$filters,u=0!==t.$blendMode;u&&(h=r[t.$blendMode],h||(h=i));var d=t.$getOriginalBounds(),f=d.x,p=d.y,v=d.width,g=d.height;if(0>=v||0>=g)return l;if(!t.mask&&1==c.length&&("colorTransform"==c[0].type||"custom"===c[0].type&&0===c[0].padding)){var x=this.getRenderCount(t);if(!t.$children||1==x)return u&&a.context.setGlobalCompositeOperation(h),a.context.$filter=c[0],l+=t.$mask?this.drawWithClip(t,a,o,s):t.$scrollRect||t.$maskRect?this.drawWithScrollRect(t,a,o,s):this.drawDisplayObject(t,a,o,s),a.context.$filter=null,u&&a.context.setGlobalCompositeOperation(i),l}var y=Math.max(e.sys.DisplayList.$canvasScaleFactor,2);c.forEach(function(t){if((t instanceof e.GlowFilter||t instanceof e.BlurFilter)&&(t.$uniforms.$filterScale=y,"blur"==t.type)){var r=t;r.blurXFilter.$uniforms.$filterScale=y,r.blurYFilter.$uniforms.$filterScale=y}});var m=this.createRenderBuffer(y*v,y*g);if(m.saveTransform(),m.transform(y,0,0,y,0,0),m.context.pushBuffer(m),l+=t.$mask?this.drawWithClip(t,m,-f,-p):t.$scrollRect||t.$maskRect?this.drawWithScrollRect(t,m,-f,-p):this.drawDisplayObject(t,m,-f,-p),m.context.popBuffer(),m.restoreTransform(),l>0){u&&a.context.setGlobalCompositeOperation(h),l++,a.$offsetX=o+f,a.$offsetY=s+p;var b=e.Matrix.create(),w=a.globalMatrix;b.a=w.a,b.b=w.b,b.c=w.c,b.d=w.d,b.tx=w.tx,b.ty=w.ty,a.useOffset(),a.context.drawTargetWidthFilters(c,m),w.a=b.a,w.b=b.b,w.c=b.c,w.d=b.d,w.tx=b.tx,w.ty=b.ty,e.Matrix.release(b),u&&a.context.setGlobalCompositeOperation(i)}return n.push(m),l},a.prototype.getRenderCount=function(e){var t=0,r=e.$getRenderNode();if(r&&(t+=r.$getRenderCount()),e.$children)for(var i=0,n=e.$children;i<n.length;i++){var a=n[i],o=a.$filters;if(o&&o.length>0)return 2;if(a.$children)t+=this.getRenderCount(a);else{var s=a.$getRenderNode();s&&(t+=s.$getRenderCount())}}return t},a.prototype.drawWithClip=function(t,a,o,s){var l,h=0,c=0!==t.$blendMode;c&&(l=r[t.$blendMode],l||(l=i));var u=t.$scrollRect?t.$scrollRect:t.$maskRect,d=t.$mask;if(d){var f=d.$getMatrix();if(0==f.a&&0==f.b||0==f.c&&0==f.d)return h}if(d||t.$children&&0!=t.$children.length){var p=t.$getOriginalBounds(),v=p.x,g=p.y,x=p.width,y=p.height;if(0>=x||0>=y)return h;var m=this.createRenderBuffer(x,y);if(m.context.pushBuffer(m),h+=this.drawDisplayObject(t,m,-v,-g),d){var b=this.createRenderBuffer(x,y);b.context.pushBuffer(b);var w=e.Matrix.create();w.copyFrom(d.$getConcatenatedMatrix()),d.$getConcatenatedMatrixAt(t,w),w.translate(-v,-g),b.setTransform(w.a,w.b,w.c,w.d,w.tx,w.ty),e.Matrix.release(w),h+=this.drawDisplayObject(d,b,0,0),b.context.popBuffer(),m.context.setGlobalCompositeOperation("destination-in"),m.setTransform(1,0,0,-1,0,b.height);var T=b.rootRenderTarget.width,E=b.rootRenderTarget.height;m.context.drawTexture(b.rootRenderTarget.texture,0,0,T,E,0,0,T,E,T,E),m.setTransform(1,0,0,1,0,0),m.context.setGlobalCompositeOperation("source-over"),b.setTransform(1,0,0,1,0,0),n.push(b)}if(m.context.setGlobalCompositeOperation(i),m.context.popBuffer(),h>0){h++,c&&a.context.setGlobalCompositeOperation(l),u&&a.context.pushMask(u.x+o,u.y+s,u.width,u.height);var _=e.Matrix.create(),C=a.globalMatrix;_.a=C.a,_.b=C.b,_.c=C.c,_.d=C.d,_.tx=C.tx,_.ty=C.ty,C.append(1,0,0,-1,o+v,s+g+m.height);var S=m.rootRenderTarget.width,R=m.rootRenderTarget.height;a.context.drawTexture(m.rootRenderTarget.texture,0,0,S,R,0,0,S,R,S,R),u&&m.context.popMask(),c&&a.context.setGlobalCompositeOperation(i);var L=a.globalMatrix;L.a=_.a,L.b=_.b,L.c=_.c,L.d=_.d,L.tx=_.tx,L.ty=_.ty,e.Matrix.release(_)}return n.push(m),h}return u&&a.context.pushMask(u.x+o,u.y+s,u.width,u.height),c&&a.context.setGlobalCompositeOperation(l),h+=this.drawDisplayObject(t,a,o,s),c&&a.context.setGlobalCompositeOperation(i),u&&a.context.popMask(),h},a.prototype.drawWithScrollRect=function(e,t,r,i){var n=0,a=e.$scrollRect?e.$scrollRect:e.$maskRect;if(a.isEmpty())return n;e.$scrollRect&&(r-=a.x,i-=a.y);var o=t.globalMatrix,s=t.context,l=!1;if(t.$hasScissor||0!=o.b||0!=o.c)t.context.pushMask(a.x+r,a.y+i,a.width,a.height);else{var h=o.a,c=o.d,u=o.tx,d=o.ty,f=a.x+r,p=a.y+i,v=f+a.width,g=p+a.height,x=void 0,y=void 0,m=void 0,b=void 0;if(1==h&&1==c)x=f+u,y=p+d,m=v+u,b=g+d;else{var w=h*f+u,T=c*p+d,E=h*v+u,_=c*p+d,C=h*v+u,S=c*g+d,R=h*f+u,L=c*g+d,D=0;w>E&&(D=w,w=E,E=D),C>R&&(D=C,C=R,R=D),x=C>w?w:C,m=E>R?E:R,T>_&&(D=T,T=_,_=D),S>L&&(D=S,S=L,L=D),y=S>T?T:S,b=_>L?_:L}s.enableScissor(x,-b+t.height,m-x,b-y),l=!0}return n+=this.drawDisplayObject(e,t,r,i),l?s.disableScissor():s.popMask(),n},a.prototype.drawNodeToBuffer=function(e,t,r,i){var n=t;n.context.pushBuffer(n),n.setTransform(r.a,r.b,r.c,r.d,r.tx,r.ty),this.renderNode(e,t,0,0,i),n.context.$drawWebGL(),n.onRenderFinish(),n.context.popBuffer()},a.prototype.drawDisplayToBuffer=function(e,t,r){t.context.pushBuffer(t),r&&t.setTransform(r.a,r.b,r.c,r.d,r.tx,r.ty);var i;i=e.$renderDirty?e.$getRenderNode():e.$renderNode;var n=0;if(i)switch(n++,i.type){case 1:this.renderBitmap(i,t);break;case 2:this.renderText(i,t);break;case 3:this.renderGraphics(i,t);break;case 4:this.renderGroup(i,t);break;case 5:this.renderMesh(i,t);break;case 6:this.renderNormalBitmap(i,t)}var a=e.$children;if(a)for(var o=a.length,s=0;o>s;s++){var l=a[s];switch(l.$renderMode){case 1:break;case 2:n+=this.drawWithFilter(l,t,0,0);break;case 3:n+=this.drawWithClip(l,t,0,0);break;case 4:n+=this.drawWithScrollRect(l,t,0,0);break;default:n+=this.drawDisplayObject(l,t,0,0)}}return t.context.$drawWebGL(),t.onRenderFinish(),t.context.popBuffer(),n},a.prototype.renderNode=function(e,t,r,i,n){switch(t.$offsetX=r,t.$offsetY=i,e.type){case 1:this.renderBitmap(e,t);break;case 2:this.renderText(e,t);break;case 3:this.renderGraphics(e,t,n);break;case 4:this.renderGroup(e,t);break;case 5:this.renderMesh(e,t);break;case 6:this.renderNormalBitmap(e,t)}},a.prototype.renderNormalBitmap=function(e,t){var r=e.image;r&&t.context.drawImage(r,e.sourceX,e.sourceY,e.sourceW,e.sourceH,e.drawX,e.drawY,e.drawW,e.drawH,e.imageWidth,e.imageHeight,e.rotated,e.smoothing)},a.prototype.renderBitmap=function(t,n){var a=t.image;if(a){var o,s,l,h=t.drawData,c=h.length,u=0,d=t.matrix,f=t.blendMode,p=t.alpha;if(d){o=e.Matrix.create();var v=n.globalMatrix;o.a=v.a,o.b=v.b,o.c=v.c,o.d=v.d,o.tx=v.tx,o.ty=v.ty,s=n.$offsetX,l=n.$offsetY,n.useOffset(),n.transform(d.a,d.b,d.c,d.d,d.tx,d.ty)}f&&n.context.setGlobalCompositeOperation(r[f]);var g;if(p==p&&(g=n.globalAlpha,n.globalAlpha*=p),t.filter){for(n.context.$filter=t.filter;c>u;)n.context.drawImage(a,h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],t.imageWidth,t.imageHeight,t.rotated,t.smoothing);n.context.$filter=null}else for(;c>u;)n.context.drawImage(a,h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],t.imageWidth,t.imageHeight,t.rotated,t.smoothing);if(f&&n.context.setGlobalCompositeOperation(i),p==p&&(n.globalAlpha=g),d){var x=n.globalMatrix;x.a=o.a,x.b=o.b,x.c=o.c,x.d=o.d,x.tx=o.tx,x.ty=o.ty,n.$offsetX=s,n.$offsetY=l,e.Matrix.release(o)}}},a.prototype.renderMesh=function(t,n){var a,o,s,l=t.image,h=t.drawData,c=h.length,u=0,d=t.matrix,f=t.blendMode,p=t.alpha;if(d){a=e.Matrix.create();var v=n.globalMatrix;a.a=v.a,a.b=v.b,a.c=v.c,a.d=v.d,a.tx=v.tx,a.ty=v.ty,o=n.$offsetX,s=n.$offsetY,n.useOffset(),n.transform(d.a,d.b,d.c,d.d,d.tx,d.ty)}f&&n.context.setGlobalCompositeOperation(r[f]);var g;if(p==p&&(g=n.globalAlpha,n.globalAlpha*=p),t.filter){for(n.context.$filter=t.filter;c>u;)n.context.drawMesh(l,h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],t.imageWidth,t.imageHeight,t.uvs,t.vertices,t.indices,t.bounds,t.rotated,t.smoothing);n.context.$filter=null}else for(;c>u;)n.context.drawMesh(l,h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],h[u++],t.imageWidth,t.imageHeight,t.uvs,t.vertices,t.indices,t.bounds,t.rotated,t.smoothing);if(f&&n.context.setGlobalCompositeOperation(i),p==p&&(n.globalAlpha=g),d){var x=n.globalMatrix;x.a=a.a,x.b=a.b,x.c=a.c,x.d=a.d,x.tx=a.tx,x.ty=a.ty,n.$offsetX=o,n.$offsetY=s,e.Matrix.release(a)}},a.prototype.___renderText____=function(r,i){var n=r.width-r.x,a=r.height-r.y;if(!(0>=n||0>=a)&&n&&a&&0!==r.drawData.length){var o=e.sys.DisplayList.$canvasScaleX,s=e.sys.DisplayList.$canvasScaleY,l=i.context.$maxTextureSize;n*o>l&&(o*=l/(n*o)),a*s>l&&(s*=l/(a*s)),n*=o,a*=s;var h=r.x*o,c=r.y*s;(r.$canvasScaleX!==o||r.$canvasScaleY!==s)&&(r.$canvasScaleX=o,r.$canvasScaleY=s,r.dirtyRender=!0),(h||c)&&i.transform(1,0,0,1,h/o,c/s),r.dirtyRender&&t.TextAtlasRender.analysisTextNodeAndFlushDrawLabel(r);var u=r[t.property_drawLabel];if(u&&u.length>0){for(var d=i.$offsetX,f=i.$offsetY,p=null,v=0,g=0,x=null,y=null,m=null,b=0,w=u.length;w>b;++b){p=u[b],v=p.anchorX,g=p.anchorY,x=p.textBlocks,i.$offsetX=d+v;for(var T=0,E=x.length;E>T;++T)y=x[T],T>0&&(i.$offsetX-=y.canvasWidthOffset),i.$offsetY=f+g-(y.measureHeight+(y.stroke2?y.canvasHeightOffset:0))/2,m=y.line.page,i.context.drawTexture(m.webGLTexture,y.u,y.v,y.contentWidth,y.contentHeight,0,0,y.contentWidth,y.contentHeight,m.pageWidth,m.pageHeight),i.$offsetX+=y.contentWidth-y.canvasWidthOffset}i.$offsetX=d,i.$offsetY=f}(h||c)&&i.transform(1,0,0,1,-h/o,-c/s),r.dirtyRender=!1}},a.prototype.renderText=function(r,i){if(t.textAtlasRenderEnable)return void this.___renderText____(r,i);var n=r.width-r.x,a=r.height-r.y;if(!(0>=n||0>=a)&&n&&a&&0!=r.drawData.length){var o=e.sys.DisplayList.$canvasScaleX,s=e.sys.DisplayList.$canvasScaleY,l=i.context.$maxTextureSize;n*o>l&&(o*=l/(n*o)),a*s>l&&(s*=l/(a*s)),n*=o,a*=s;var h=r.x*o,c=r.y*s;if((r.$canvasScaleX!=o||r.$canvasScaleY!=s)&&(r.$canvasScaleX=o,r.$canvasScaleY=s,r.dirtyRender=!0),this.wxiOS10?(this.canvasRenderer||(this.canvasRenderer=new e.CanvasRenderer),r.dirtyRender&&(this.canvasRenderBuffer=new t.CanvasRenderBuffer(n,a))):this.canvasRenderBuffer&&this.canvasRenderBuffer.context?r.dirtyRender&&this.canvasRenderBuffer.resize(n,a):(this.canvasRenderer=new e.CanvasRenderer,this.canvasRenderBuffer=new t.CanvasRenderBuffer(n,a)),this.canvasRenderBuffer.context){if((1!=o||1!=s)&&this.canvasRenderBuffer.context.setTransform(o,0,0,s,0,0),h||c?(r.dirtyRender&&this.canvasRenderBuffer.context.setTransform(o,0,0,s,-h,-c),i.transform(1,0,0,1,h/o,c/s)):(1!=o||1!=s)&&this.canvasRenderBuffer.context.setTransform(o,0,0,s,0,0),r.dirtyRender){var u=this.canvasRenderBuffer.surface;if(this.canvasRenderer.renderText(r,this.canvasRenderBuffer.context),this.wxiOS10)u.isCanvas=!0,r.$texture=u;else{var d=r.$texture;d?i.context.updateTexture(d,u):(d=i.context.createTexture(u),r.$texture=d)}r.$textureWidth=u.width,r.$textureHeight=u.height}var f=r.$textureWidth,p=r.$textureHeight;i.context.drawTexture(r.$texture,0,0,f,p,0,0,f/o,p/s,f,p),(h||c)&&(r.dirtyRender&&this.canvasRenderBuffer.context.setTransform(o,0,0,s,0,0),i.transform(1,0,0,1,-h/o,-c/s)),r.dirtyRender=!1}}},a.prototype.renderGraphics=function(r,i,n){var a=r.width,o=r.height;if(!(0>=a||0>=o)&&a&&o&&0!=r.drawData.length){var s=e.sys.DisplayList.$canvasScaleX,l=e.sys.DisplayList.$canvasScaleY;(1>a*s||1>o*l)&&(s=l=1),(r.$canvasScaleX!=s||r.$canvasScaleY!=l)&&(r.$canvasScaleX=s,r.$canvasScaleY=l,r.dirtyRender=!0),a*=s,o*=l;var h=Math.ceil(a),c=Math.ceil(o);if(s*=h/a,l*=c/o,a=h,o=c,this.wxiOS10?(this.canvasRenderer||(this.canvasRenderer=new e.CanvasRenderer),r.dirtyRender&&(this.canvasRenderBuffer=new t.CanvasRenderBuffer(a,o))):this.canvasRenderBuffer&&this.canvasRenderBuffer.context?r.dirtyRender&&this.canvasRenderBuffer.resize(a,o):(this.canvasRenderer=new e.CanvasRenderer,this.canvasRenderBuffer=new t.CanvasRenderBuffer(a,o)),this.canvasRenderBuffer.context){(1!=s||1!=l)&&this.canvasRenderBuffer.context.setTransform(s,0,0,l,0,0),(r.x||r.y)&&((r.dirtyRender||n)&&this.canvasRenderBuffer.context.translate(-r.x,-r.y),i.transform(1,0,0,1,r.x,r.y));var u=this.canvasRenderBuffer.surface;if(n){this.canvasRenderer.renderGraphics(r,this.canvasRenderBuffer.context,!0);var d=void 0;this.wxiOS10?(u.isCanvas=!0,d=u):(e.WebGLUtils.deleteWebGLTexture(u),d=i.context.getWebGLTexture(u)),i.context.drawTexture(d,0,0,a,o,0,0,a,o,u.width,u.height)}else{if(r.dirtyRender){if(this.canvasRenderer.renderGraphics(r,this.canvasRenderBuffer.context),this.wxiOS10)u.isCanvas=!0,r.$texture=u;else{var d=r.$texture;d?i.context.updateTexture(d,u):(d=i.context.createTexture(u),r.$texture=d)}r.$textureWidth=u.width,r.$textureHeight=u.height}var f=r.$textureWidth,p=r.$textureHeight;i.context.drawTexture(r.$texture,0,0,f,p,0,0,f/s,p/l,f,p)}(r.x||r.y)&&((r.dirtyRender||n)&&this.canvasRenderBuffer.context.translate(r.x,r.y),i.transform(1,0,0,1,-r.x,-r.y)),n||(r.dirtyRender=!1)}}},a.prototype.renderGroup=function(t,r){var i,n,a,o=t.matrix;if(o){i=e.Matrix.create();var s=r.globalMatrix;i.a=s.a,i.b=s.b,i.c=s.c,i.d=s.d,i.tx=s.tx,i.ty=s.ty,n=r.$offsetX,a=r.$offsetY,r.useOffset(),r.transform(o.a,o.b,o.c,o.d,o.tx,o.ty)}for(var l=t.drawData,h=l.length,c=0;h>c;c++){var u=l[c];this.renderNode(u,r,r.$offsetX,r.$offsetY)}if(o){var d=r.globalMatrix;d.a=i.a,d.b=i.b,d.c=i.c,d.d=i.d,d.tx=i.tx,d.ty=i.ty,r.$offsetX=n,r.$offsetY=a,e.Matrix.release(i)}},a.prototype.createRenderBuffer=function(e,r){var i=n.pop();return i?(i.resize(e,r),i.setTransform(1,0,0,1,0,0)):(i=new t.WebGLRenderBuffer(e,r),i.$computeDrawCall=!1),i},a.prototype.renderClear=function(){var e=t.WebGLRenderContext.getInstance(),r=e.context;e.$beforeRender();var i=e.surface.width,n=e.surface.height;r.viewport(0,0,i,n)},a}();t.WebGLRenderer=a,__reflect(a.prototype,"egret.web.WebGLRenderer",["egret.sys.SystemRenderer"])}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){var r=function(e){function t(t,r,i,n,a,o,s,l){var h=e.call(this)||this;return h._width=0,h._height=0,h._border=0,h.line=null,h.x=0,h.y=0,h.u=0,h.v=0,h.tag="",h.measureWidth=0,h.measureHeight=0,h.canvasWidthOffset=0,h.canvasHeightOffset=0,h.stroke2=0,h._width=t,h._height=r,h._border=l,h.measureWidth=i,h.measureHeight=n,h.canvasWidthOffset=a,h.canvasHeightOffset=o,h.stroke2=s,h}return __extends(t,e),Object.defineProperty(t.prototype,"border",{get:function(){return this._border},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"width",{get:function(){return this._width+2*this.border},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"height",{get:function(){return this._height+2*this.border},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"contentWidth",{get:function(){return this._width},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"contentHeight",{get:function(){return this._height},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"page",{get:function(){return this.line?this.line.page:null},enumerable:!0,configurable:!0}),t.prototype.updateUV=function(){var e=this.line;return e?(this.u=e.x+this.x+1*this.border,this.v=e.y+this.y+1*this.border,!0):!1},Object.defineProperty(t.prototype,"subImageOffsetX",{get:function(){var e=this.line;return e?e.x+this.x+this.border:0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"subImageOffsetY",{get:function(){var e=this.line;return e?e.y+this.y+this.border:0},enumerable:!0,configurable:!0}),t}(e.HashObject);t.TextBlock=r,__reflect(r.prototype,"egret.web.TextBlock");var i=function(e){function t(t){var r=e.call(this)||this;return r.page=null,r.textBlocks=[],r.dynamicMaxHeight=0,r.maxWidth=0,r.x=0,r.y=0,r.maxWidth=t,r}return __extends(t,e),t.prototype.isCapacityOf=function(e){if(!e)return!1;var t=0,r=0,i=this.lastTextBlock();return i&&(t=i.x+i.width,r=i.y),t+e.width>this.maxWidth?!1:this.dynamicMaxHeight>0&&(e.height>this.dynamicMaxHeight||e.height/this.dynamicMaxHeight<.5)?!1:!0},t.prototype.lastTextBlock=function(){var e=this.textBlocks;return e.length>0?e[e.length-1]:null},t.prototype.addTextBlock=function(e,t){if(!e)return!1;if(t&&!this.isCapacityOf(e))return!1;var r=0,i=0,n=this.lastTextBlock();return n&&(r=n.x+n.width,i=n.y),e.x=r,e.y=i,e.line=this,this.textBlocks.push(e),this.dynamicMaxHeight=Math.max(this.dynamicMaxHeight,e.height),!0},t}(e.HashObject);t.Line=i,__reflect(i.prototype,"egret.web.Line");var n=function(e){function t(t,r){var i=e.call(this)||this;return i.lines=[],i.pageWidth=0,i.pageHeight=0,i.webGLTexture=null,i.pageWidth=t,i.pageHeight=r,i}return __extends(t,e),t.prototype.addLine=function(e){if(!e)return!1;var t=0,r=0,i=this.lines;if(i.length>0){var n=i[i.length-1];t=n.x,r=n.y+n.dynamicMaxHeight}return e.maxWidth>this.pageWidth?(console.error("line.maxWidth = "+e.maxWidth+", this.pageWidth = "+this.pageWidth),!1):r+e.dynamicMaxHeight>this.pageHeight?!1:(e.x=t,e.y=r,e.page=this,this.lines.push(e),!0)},t}(e.HashObject);t.Page=n,__reflect(n.prototype,"egret.web.Page");var a=function(e){function t(t,r){var i=e.call(this)||this;return i._pages=[],i._sortLines=[],i._maxSize=1024,i._border=1,i._maxSize=t,i._border=r,i}return __extends(t,e),t.prototype.addTextBlock=function(e){var t=this._addTextBlock(e);if(!t)return!1;e.updateUV();for(var r=!1,i=t,n=this._sortLines,a=0,o=n;a<o.length;a++){var s=o[a];if(s===i[1]){r=!0;break}}return r||n.push(i[1]),this.sort(),!0},t.prototype._addTextBlock=function(e){if(!e)return null;if(e.width>this._maxSize||e.height>this._maxSize)return null;for(var t=this._sortLines,r=0,n=t.length;n>r;++r){var a=t[r];if(a.isCapacityOf(e)&&a.addTextBlock(e,!1))return[a.page,a]}var o=new i(this._maxSize);if(!o.addTextBlock(e,!0))return console.error("_addTextBlock !newLine.addTextBlock(textBlock, true)"),null;for(var s=this._pages,r=0,l=s.length;l>r;++r){var h=s[r];if(h.addLine(o))return[h,o]}var c=this.createPage(this._maxSize,this._maxSize);return c.addLine(o)?[c,o]:(console.error("_addText newPage.addLine failed"),null)},t.prototype.createPage=function(e,t){var r=new n(e,t);return this._pages.push(r),r},t.prototype.sort=function(){if(!(this._sortLines.length<=1)){var e=function(e,t){return e.dynamicMaxHeight<t.dynamicMaxHeight?-1:1};this._sortLines=this._sortLines.sort(e)}},t.prototype.createTextBlock=function(e,t,i,n,a,o,s,l){var h=new r(t,i,n,a,o,s,l,this._border);return this.addTextBlock(h)?(h.tag=e,h):null},t}(e.HashObject);t.Book=a,__reflect(a.prototype,"egret.web.Book")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(t){t.textAtlasRenderEnable=!1,t.__textAtlasRender__=null,t.property_drawLabel="DrawLabel";var r=!1,i=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.anchorX=0,t.anchorY=0,t.textBlocks=[],t}return __extends(t,e),t.prototype.clear=function(){this.anchorX=0,this.anchorY=0,this.textBlocks.length=0},t.create=function(){var e=t.pool;return 0===e.length&&e.push(new t),e.pop()},t.back=function(e,r){if(e){var i=t.pool;if(r&&i.indexOf(e)>=0)return void console.error("DrawLabel.back repeat");e.clear(),i.push(e)}},t.pool=[],t}(e.HashObject);t.DrawLabel=i,__reflect(i.prototype,"egret.web.DrawLabel");var n=function(t){function i(i,n){var a=t.call(this)||this;a.format=null;var o=0;r&&(o=i.textColor,i.textColor=16711680),a.textColor=i.textColor,a.strokeColor=i.strokeColor,a.size=i.size,a.stroke=i.stroke,a.bold=i.bold,a.italic=i.italic,a.fontFamily=i.fontFamily,a.format=n,a.font=e.getFontString(i,a.format);var s=n.textColor?n.textColor:i.textColor,l=n.strokeColor?n.strokeColor:i.strokeColor,h=n.stroke?n.stroke:i.stroke,c=n.size?n.size:i.size;return a.description=""+a.font+"-"+c,a.description+="-"+e.toColorString(s),a.description+="-"+e.toColorString(l),h&&(a.description+="-"+2*h),r&&(i.textColor=o),a}return __extends(i,t),i}(e.HashObject);__reflect(n.prototype,"StyleInfo");var a=function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return e["char"]="",e.styleInfo=null,e.hashCodeString="",e.charWithStyleHashCode=0,e.measureWidth=0,e.measureHeight=0,e.canvasWidthOffset=0,e.canvasHeightOffset=0,e.stroke2=0,e}return __extends(r,t),r.prototype.reset=function(t,r){return this["char"]=t,this.styleInfo=r,this.hashCodeString=t+":"+r.description,this.charWithStyleHashCode=e.NumberUtils.convertStringToHashCode(this.hashCodeString),this.canvasWidthOffset=0,this.canvasHeightOffset=0,this.stroke2=0,this},r.prototype.measureAndDraw=function(t){var r=t;if(r){var i=this["char"],n=this.styleInfo.format,a=n.textColor?n.textColor:this.styleInfo.textColor,o=n.strokeColor?n.strokeColor:this.styleInfo.strokeColor,s=n.stroke?n.stroke:this.styleInfo.stroke,l=n.size?n.size:this.styleInfo.size;this.measureWidth=this.measure(i,this.styleInfo,l),this.measureHeight=l;var h=this.measureWidth,c=this.measureHeight,u=2*s;u>0&&(h+=2*u,c+=2*u),this.stroke2=u,r.width=h=Math.ceil(h)+4,r.height=c=Math.ceil(c)+4,this.canvasWidthOffset=(r.width-this.measureWidth)/2,this.canvasHeightOffset=(r.height-this.measureHeight)/2;var d=3,f=Math.pow(10,d);this.canvasWidthOffset=Math.floor(this.canvasWidthOffset*f)/f,this.canvasHeightOffset=Math.floor(this.canvasHeightOffset*f)/f;var p=e.sys.getContext2d(r);p.save(),p.textAlign="center",p.textBaseline="middle",p.lineJoin="round",p.font=this.styleInfo.font,p.fillStyle=e.toColorString(a),p.strokeStyle=e.toColorString(o),p.clearRect(0,0,r.width,r.height),s&&(p.lineWidth=2*s,p.strokeText(i,r.width/2,r.height/2)),p.fillText(i,r.width/2,r.height/2),p.restore()}},r.prototype.measure=function(t,i,n){var a=r.chineseCharactersRegExp.test(t);if(a&&r.chineseCharacterMeasureFastMap[i.font])return r.chineseCharacterMeasureFastMap[i.font];var o=e.sys.measureText(t,i.fontFamily,n||i.size,i.bold,i.italic);return a&&(r.chineseCharacterMeasureFastMap[i.font]=o),o},r.chineseCharactersRegExp=new RegExp("^[一-龥]$"),r.chineseCharacterMeasureFastMap={},r}(e.HashObject);__reflect(a.prototype,"CharImageRender");var o=function(o){function s(e,r,i){var n=o.call(this)||this;return n.book=null,n.charImageRender=new a,n.textBlockMap={},n._canvas=null,n.textAtlasTextureCache=[],n.webglRenderContext=null,n.webglRenderContext=e,n.book=new t.Book(r,i),n}return __extends(s,o),s.analysisTextNodeAndFlushDrawLabel=function(a){if(a){if(!t.__textAtlasRender__){var o=e.web.WebGLRenderContext.getInstance(0,0);t.__textAtlasRender__=new s(o,512,r?12:1)}a[t.property_drawLabel]=a[t.property_drawLabel]||[];for(var l=a[t.property_drawLabel],h=0,c=l;h<c.length;h++){var u=c[h];i.back(u,!1)}l.length=0;for(var d=4,f=a.drawData,p=0,v=0,g="",x={},y=[],m=0,b=f.length;b>m;m+=d){p=f[m+0],v=f[m+1],g=f[m+2],x=f[m+3]||{},y.length=0,t.__textAtlasRender__.convertLabelStringToTextAtlas(g,new n(a,x),y);var u=i.create();u.anchorX=p,u.anchorY=v,u.textBlocks=[].concat(y),l.push(u)}}},s.prototype.convertLabelStringToTextAtlas=function(t,i,n){for(var a=this.canvas,o=this.charImageRender,s=this.textBlockMap,l=0,h=t;l<h.length;l++){var c=h[l];
if(o.reset(c,i),s[o.charWithStyleHashCode])n.push(s[o.charWithStyleHashCode]);else{o.measureAndDraw(a);var u=this.book.createTextBlock(c,a.width,a.height,o.measureWidth,o.measureHeight,o.canvasWidthOffset,o.canvasHeightOffset,o.stroke2);if(u){n.push(u),s[o.charWithStyleHashCode]=u;var d=u.page;d.webGLTexture||(d.webGLTexture=this.createTextTextureAtlas(d.pageWidth,d.pageHeight,r));var f=this.webglRenderContext.context;d.webGLTexture[e.glContext]=f,f.bindTexture(f.TEXTURE_2D,d.webGLTexture),f.pixelStorei(f.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),d.webGLTexture[e.UNPACK_PREMULTIPLY_ALPHA_WEBGL]=!0,f.texSubImage2D(f.TEXTURE_2D,0,u.subImageOffsetX,u.subImageOffsetY,f.RGBA,f.UNSIGNED_BYTE,a),f.pixelStorei(f.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1)}}}},s.prototype.createTextTextureAtlas=function(t,r,i){var n=null;if(i){var a=e.sys.createCanvas(t,t),o=e.sys.getContext2d(a);o.fillStyle="black",o.fillRect(0,0,t,t),n=e.sys.createTexture(this.webglRenderContext,a)}else n=e.sys._createTexture(this.webglRenderContext,t,r,null);return n&&this.textAtlasTextureCache.push(n),n},Object.defineProperty(s.prototype,"canvas",{get:function(){return this._canvas||(this._canvas=e.sys.createCanvas(24,24)),this._canvas},enumerable:!0,configurable:!0}),s}(e.HashObject);t.TextAtlasRender=o,__reflect(o.prototype,"egret.web.TextAtlasRender")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(e){var t=function(){function e(e,t,r){this.gl=e,this.name=r.name,this.type=r.type,this.size=r.size,this.location=e.getAttribLocation(t,this.name),this.count=0,this.initCount(e),this.format=e.FLOAT,this.initFormat(e)}return e.prototype.initCount=function(e){var t=this.type;switch(t){case 5126:case 5120:case 5121:case 5123:this.count=1;break;case 35664:this.count=2;break;case 35665:this.count=3;break;case 35666:this.count=4}},e.prototype.initFormat=function(e){var t=this.type;switch(t){case 5126:case 35664:case 35665:case 35666:this.format=e.FLOAT;break;case 5121:this.format=e.UNSIGNED_BYTE;break;case 5123:this.format=e.UNSIGNED_SHORT;break;case 5120:this.format=e.BYTE}},e}();e.EgretWebGLAttribute=t,__reflect(t.prototype,"egret.web.EgretWebGLAttribute")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(e){function t(e,t,r){var i=e.createShader(t);e.shaderSource(i,r),e.compileShader(i);var n=e.getShaderParameter(i,e.COMPILE_STATUS);return n||(console.log("shader not compiled!"),console.log(e.getShaderInfoLog(i))),i}function r(e,t,r){var i=e.createProgram();return e.attachShader(i,t),e.attachShader(i,r),e.linkProgram(i),i}function i(t,r){for(var i={},n=t.getProgramParameter(r,t.ACTIVE_ATTRIBUTES),a=0;n>a;a++){var o=t.getActiveAttrib(r,a),s=o.name,l=new e.EgretWebGLAttribute(t,r,o);i[s]=l}return i}function n(t,r){for(var i={},n=t.getProgramParameter(r,t.ACTIVE_UNIFORMS),a=0;n>a;a++){var o=t.getActiveUniform(r,a),s=o.name,l=new e.EgretWebGLUniform(t,r,o);i[s]=l}return i}var a=function(){function e(e,a,o){this.vshaderSource=a,this.fshaderSource=o,this.vertexShader=t(e,e.VERTEX_SHADER,this.vshaderSource),this.fragmentShader=t(e,e.FRAGMENT_SHADER,this.fshaderSource),this.id=r(e,this.vertexShader,this.fragmentShader),this.uniforms=n(e,this.id),this.attributes=i(e,this.id)}return e.getProgram=function(t,r,i,n){return this.programCache[n]||(this.programCache[n]=new e(t,r,i)),this.programCache[n]},e.deleteProgram=function(e,t,r,i){},e.programCache={},e}();e.EgretWebGLProgram=a,__reflect(a.prototype,"egret.web.EgretWebGLProgram")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(e){var t=function(){function e(e,t,r){this.gl=e,this.name=r.name,this.type=r.type,this.size=r.size,this.location=e.getUniformLocation(t,this.name),this.setDefaultValue(),this.generateSetValue(),this.generateUpload()}return e.prototype.setDefaultValue=function(){var e=this.type;switch(e){case 5126:case 35678:case 35680:case 35670:case 5124:this.value=0;break;case 35664:case 35671:case 35667:this.value=[0,0];break;case 35665:case 35672:case 35668:this.value=[0,0,0];break;case 35666:case 35673:case 35669:this.value=[0,0,0,0];break;case 35674:this.value=new Float32Array([1,0,0,1]);break;case 35675:this.value=new Float32Array([1,0,0,0,1,0,0,0,1]);break;case 35676:this.value=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}},e.prototype.generateSetValue=function(){var e=this.type;switch(e){case 5126:case 35678:case 35680:case 35670:case 5124:this.setValue=function(e){var t=this.value!==e;this.value=e,t&&this.upload()};break;case 35664:case 35671:case 35667:this.setValue=function(e){var t=this.value[0]!==e.x||this.value[1]!==e.y;this.value[0]=e.x,this.value[1]=e.y,t&&this.upload()};break;case 35665:case 35672:case 35668:this.setValue=function(e){this.value[0]=e.x,this.value[1]=e.y,this.value[2]=e.z,this.upload()};break;case 35666:case 35673:case 35669:this.setValue=function(e){this.value[0]=e.x,this.value[1]=e.y,this.value[2]=e.z,this.value[3]=e.w,this.upload()};break;case 35674:case 35675:case 35676:this.setValue=function(e){this.value.set(e),this.upload()}}},e.prototype.generateUpload=function(){var e=this.gl,t=this.type,r=this.location;switch(t){case 5126:this.upload=function(){var t=this.value;e.uniform1f(r,t)};break;case 35664:this.upload=function(){var t=this.value;e.uniform2f(r,t[0],t[1])};break;case 35665:this.upload=function(){var t=this.value;e.uniform3f(r,t[0],t[1],t[2])};break;case 35666:this.upload=function(){var t=this.value;e.uniform4f(r,t[0],t[1],t[2],t[3])};break;case 35678:case 35680:case 35670:case 5124:this.upload=function(){var t=this.value;e.uniform1i(r,t)};break;case 35671:case 35667:this.upload=function(){var t=this.value;e.uniform2i(r,t[0],t[1])};break;case 35672:case 35668:this.upload=function(){var t=this.value;e.uniform3i(r,t[0],t[1],t[2])};break;case 35673:case 35669:this.upload=function(){var t=this.value;e.uniform4i(r,t[0],t[1],t[2],t[3])};break;case 35674:this.upload=function(){var t=this.value;e.uniformMatrix2fv(r,!1,t)};break;case 35675:this.upload=function(){var t=this.value;e.uniformMatrix3fv(r,!1,t)};break;case 35676:this.upload=function(){var t=this.value;e.uniformMatrix4fv(r,!1,t)}}},e}();e.EgretWebGLUniform=t,__reflect(t.prototype,"egret.web.EgretWebGLUniform")}(t=e.web||(e.web={}))}(egret||(egret={}));var egret;!function(e){var t;!function(e){var t=function(){function e(){}return e.blur_frag="precision mediump float;\r\nuniform vec2 blur;\r\nuniform sampler2D uSampler;\r\nvarying vec2 vTextureCoord;\r\nuniform vec2 uTextureSize;\r\nvoid main()\r\n{\r\n    const int sampleRadius = 5;\r\n    const int samples = sampleRadius * 2 + 1;\r\n    vec2 blurUv = blur / uTextureSize;\r\n    vec4 color = vec4(0, 0, 0, 0);\r\n    vec2 uv = vec2(0.0, 0.0);\r\n    blurUv /= float(sampleRadius);\r\n\r\n    for (int i = -sampleRadius; i <= sampleRadius; i++) {\r\n        uv.x = vTextureCoord.x + float(i) * blurUv.x;\r\n        uv.y = vTextureCoord.y + float(i) * blurUv.y;\r\n        color += texture2D(uSampler, uv);\r\n    }\r\n\r\n    color /= float(samples);\r\n    gl_FragColor = color;\r\n}",e.colorTransform_frag="precision mediump float;\r\nvarying vec2 vTextureCoord;\r\nvarying vec4 vColor;\r\nuniform mat4 matrix;\r\nuniform vec4 colorAdd;\r\nuniform sampler2D uSampler;\r\n\r\nvoid main(void) {\r\n    vec4 texColor = texture2D(uSampler, vTextureCoord);\r\n    if(texColor.a > 0.) {\r\n        // 抵消预乘的alpha通道\r\n        texColor = vec4(texColor.rgb / texColor.a, texColor.a);\r\n    }\r\n    vec4 locColor = clamp(texColor * matrix + colorAdd, 0., 1.);\r\n    gl_FragColor = vColor * vec4(locColor.rgb * locColor.a, locColor.a);\r\n}",e.default_vert="attribute vec2 aVertexPosition;\r\nattribute vec2 aTextureCoord;\r\nattribute vec4 aColor;\r\n\r\nuniform vec2 projectionVector;\r\n// uniform vec2 offsetVector;\r\n\r\nvarying vec2 vTextureCoord;\r\nvarying vec4 vColor;\r\n\r\nconst vec2 center = vec2(-1.0, 1.0);\r\n\r\nvoid main(void) {\r\n   gl_Position = vec4( (aVertexPosition / projectionVector) + center , 0.0, 1.0);\r\n   vTextureCoord = aTextureCoord;\r\n   vColor = aColor;\r\n}",e.glow_frag="precision highp float;\r\nvarying vec2 vTextureCoord;\r\n\r\nuniform sampler2D uSampler;\r\n\r\nuniform float dist;\r\nuniform float angle;\r\nuniform vec4 color;\r\nuniform float alpha;\r\nuniform float blurX;\r\nuniform float blurY;\r\n// uniform vec4 quality;\r\nuniform float strength;\r\nuniform float inner;\r\nuniform float knockout;\r\nuniform float hideObject;\r\n\r\nuniform vec2 uTextureSize;\r\n\r\nfloat random(vec2 scale)\r\n{\r\n    return fract(sin(dot(gl_FragCoord.xy, scale)) * 43758.5453);\r\n}\r\n\r\nvoid main(void) {\r\n    vec2 px = vec2(1.0 / uTextureSize.x, 1.0 / uTextureSize.y);\r\n    // TODO 自动调节采样次数？\r\n    const float linearSamplingTimes = 7.0;\r\n    const float circleSamplingTimes = 12.0;\r\n    vec4 ownColor = texture2D(uSampler, vTextureCoord);\r\n    vec4 curColor;\r\n    float totalAlpha = 0.0;\r\n    float maxTotalAlpha = 0.0;\r\n    float curDistanceX = 0.0;\r\n    float curDistanceY = 0.0;\r\n    float offsetX = dist * cos(angle) * px.x;\r\n    float offsetY = dist * sin(angle) * px.y;\r\n\r\n    const float PI = 3.14159265358979323846264;\r\n    float cosAngle;\r\n    float sinAngle;\r\n    float offset = PI * 2.0 / circleSamplingTimes * random(vec2(12.9898, 78.233));\r\n    float stepX = blurX * px.x / linearSamplingTimes;\r\n    float stepY = blurY * px.y / linearSamplingTimes;\r\n    for (float a = 0.0; a <= PI * 2.0; a += PI * 2.0 / circleSamplingTimes) {\r\n        cosAngle = cos(a + offset);\r\n        sinAngle = sin(a + offset);\r\n        for (float i = 1.0; i <= linearSamplingTimes; i++) {\r\n            curDistanceX = i * stepX * cosAngle;\r\n            curDistanceY = i * stepY * sinAngle;\r\n            if (vTextureCoord.x + curDistanceX - offsetX >= 0.0 && vTextureCoord.y + curDistanceY + offsetY <= 1.0){\r\n                curColor = texture2D(uSampler, vec2(vTextureCoord.x + curDistanceX - offsetX, vTextureCoord.y + curDistanceY + offsetY));\r\n                totalAlpha += (linearSamplingTimes - i) * curColor.a;\r\n            }\r\n            maxTotalAlpha += (linearSamplingTimes - i);\r\n        }\r\n    }\r\n\r\n    ownColor.a = max(ownColor.a, 0.0001);\r\n    ownColor.rgb = ownColor.rgb / ownColor.a;\r\n\r\n    float outerGlowAlpha = (totalAlpha / maxTotalAlpha) * strength * alpha * (1. - inner) * max(min(hideObject, knockout), 1. - ownColor.a);\r\n    float innerGlowAlpha = ((maxTotalAlpha - totalAlpha) / maxTotalAlpha) * strength * alpha * inner * ownColor.a;\r\n\r\n    ownColor.a = max(ownColor.a * knockout * (1. - hideObject), 0.0001);\r\n    vec3 mix1 = mix(ownColor.rgb, color.rgb, innerGlowAlpha / (innerGlowAlpha + ownColor.a));\r\n    vec3 mix2 = mix(mix1, color.rgb, outerGlowAlpha / (innerGlowAlpha + ownColor.a + outerGlowAlpha));\r\n    float resultAlpha = min(ownColor.a + outerGlowAlpha + innerGlowAlpha, 1.);\r\n    gl_FragColor = vec4(mix2 * resultAlpha, resultAlpha);\r\n}",e.primitive_frag="precision lowp float;\r\nvarying vec2 vTextureCoord;\r\nvarying vec4 vColor;\r\n\r\nvoid main(void) {\r\n    gl_FragColor = vColor;\r\n}",e.texture_frag="precision lowp float;\r\nvarying vec2 vTextureCoord;\r\nvarying vec4 vColor;\r\nuniform sampler2D uSampler;\r\n\r\nvoid main(void) {\r\n    gl_FragColor = texture2D(uSampler, vTextureCoord) * vColor;\r\n}",e.texture_etc_alphamask_frag="precision lowp float;\r\nvarying vec2 vTextureCoord;\r\nvarying vec4 vColor;\r\nuniform sampler2D uSampler;\r\nuniform sampler2D uSamplerAlphaMask;\r\nvoid main(void) {\r\nfloat alpha = texture2D(uSamplerAlphaMask, vTextureCoord).r;\r\nif (alpha < 0.0039) { discard; }\r\nvec4 v4Color = texture2D(uSampler, vTextureCoord);\r\nv4Color.rgb = v4Color.rgb * alpha;\r\nv4Color.a = alpha;\r\ngl_FragColor = v4Color * vColor;\r\n}",e.colorTransform_frag_etc_alphamask_frag="precision mediump float;\r\nvarying vec2 vTextureCoord;\r\nvarying vec4 vColor;\r\nuniform mat4 matrix;\r\nuniform vec4 colorAdd;\r\nuniform sampler2D uSampler;\r\nuniform sampler2D uSamplerAlphaMask;\r\n\r\nvoid main(void){\r\nfloat alpha = texture2D(uSamplerAlphaMask, vTextureCoord).r;\r\nif (alpha < 0.0039) { discard; }\r\nvec4 texColor = texture2D(uSampler, vTextureCoord);\r\nif(texColor.a > 0.0) {\r\n // 抵消预乘的alpha通道\r\ntexColor = vec4(texColor.rgb / texColor.a, texColor.a);\r\n}\r\nvec4 v4Color = clamp(texColor * matrix + colorAdd, 0.0, 1.0);\r\nv4Color.rgb = v4Color.rgb * alpha;\r\nv4Color.a = alpha;\r\ngl_FragColor = v4Color * vColor;\r\n}",e}();e.EgretShaderLib=t,__reflect(t.prototype,"egret.web.EgretShaderLib")}(t=e.web||(e.web={}))}(egret||(egret={}));